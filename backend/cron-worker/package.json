{"name": "linktrackpro-cron-worker", "version": "1.0.0", "description": "Cron worker for LinkTrackPro to collect SEO metrics", "main": "src/index.js", "scripts": {"dev": "wrangler dev --config wrangler.dev.toml", "deploy": "wrangler deploy", "test": "node tests/test-basic.js", "test:all": "node tests/run-all-tests.js", "test:full": "node tests/full.js", "test:manual": "node tests/manual.js", "test:db": "node tests/db-connection.js", "test:verbose": "node tests/full.js --verbose"}, "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.4"}, "devDependencies": {"wrangler": "^4.20.0"}, "type": "module"}