-- D1 Database Schema for API Call Logging and Usage Tracking
-- This file contains the database schema for tracking API calls, usage, and results

-- Drop tables if they exist (for development)
DROP TABLE IF EXISTS api_call_logs;
DROP TABLE IF EXISTS api_usage_stats;
DROP TABLE IF EXISTS api_endpoints;

-- API Endpoints table - Define all available endpoints
CREATE TABLE api_endpoints (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    endpoint TEXT NOT NULL UNIQUE,
    method TEXT NOT NULL DEFAULT 'GET',
    description TEXT,
    category TEXT NOT NULL,
    requires_auth BOOLEAN DEFAULT false,
    rate_limit_per_hour INTEGER DEFAULT 100,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- API Call Logs table - Record every API call
CREATE TABLE api_call_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    endpoint_id INTEGER NOT NULL,
    endpoint_path TEXT NOT NULL,
    method TEXT NOT NULL,
    
    -- Request details
    query_params TEXT, -- JSON string of query parameters
    request_body TEXT, -- JSON string of request body (if applicable)
    user_agent TEXT,
    ip_address TEXT,
    referer TEXT,
    
    -- Authentication details
    auth_type TEXT, -- 'bearer', 'basic', 'none'
    auth_provided BOOLEAN DEFAULT false,
    auth_valid BOOLEAN DEFAULT false,
    
    -- Response details
    status_code INTEGER NOT NULL,
    response_size_bytes INTEGER DEFAULT 0,
    response_time_ms INTEGER NOT NULL,
    response_body TEXT, -- JSON string of response content
    success BOOLEAN NOT NULL,
    error_message TEXT,
    error_code TEXT,
    
    -- Processing details
    cache_hit BOOLEAN DEFAULT false,
    external_api_calls INTEGER DEFAULT 0, -- Number of external API calls made
    external_api_cost REAL DEFAULT 0.0, -- Estimated cost of external API calls
    
    -- Results summary
    items_processed INTEGER DEFAULT 0,
    items_successful INTEGER DEFAULT 0,
    items_failed INTEGER DEFAULT 0,
    data_source TEXT, -- 'google', 'ahrefs', 'cache', etc.
    
    -- Timestamps
    started_at DATETIME NOT NULL,
    completed_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (endpoint_id) REFERENCES api_endpoints(id)
);

-- API Usage Statistics table - Daily/hourly aggregated stats
CREATE TABLE api_usage_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    endpoint_id INTEGER NOT NULL,
    
    -- Time period
    date DATE NOT NULL,
    hour INTEGER NOT NULL, -- 0-23 for hourly stats
    
    -- Usage metrics
    total_calls INTEGER DEFAULT 0,
    successful_calls INTEGER DEFAULT 0,
    failed_calls INTEGER DEFAULT 0,
    cache_hits INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_response_time_ms REAL DEFAULT 0,
    total_response_time_ms INTEGER DEFAULT 0,
    min_response_time_ms INTEGER DEFAULT 0,
    max_response_time_ms INTEGER DEFAULT 0,
    
    -- Data transfer
    total_response_bytes INTEGER DEFAULT 0,
    avg_response_bytes REAL DEFAULT 0,
    
    -- External API usage
    total_external_api_calls INTEGER DEFAULT 0,
    total_external_api_cost REAL DEFAULT 0.0,
    
    -- Processing metrics
    total_items_processed INTEGER DEFAULT 0,
    total_items_successful INTEGER DEFAULT 0,
    total_items_failed INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (endpoint_id) REFERENCES api_endpoints(id),
    UNIQUE(endpoint_id, date, hour)
);

-- Create indexes for better performance
CREATE INDEX idx_api_call_logs_endpoint_id ON api_call_logs(endpoint_id);
CREATE INDEX idx_api_call_logs_started_at ON api_call_logs(started_at);
CREATE INDEX idx_api_call_logs_status_code ON api_call_logs(status_code);
CREATE INDEX idx_api_call_logs_success ON api_call_logs(success);
CREATE INDEX idx_api_call_logs_ip_address ON api_call_logs(ip_address);
CREATE INDEX idx_api_call_logs_data_source ON api_call_logs(data_source);

CREATE INDEX idx_api_usage_stats_endpoint_id ON api_usage_stats(endpoint_id);
CREATE INDEX idx_api_usage_stats_date ON api_usage_stats(date);
CREATE INDEX idx_api_usage_stats_date_hour ON api_usage_stats(date, hour);

CREATE INDEX idx_api_endpoints_endpoint ON api_endpoints(endpoint);
CREATE INDEX idx_api_endpoints_category ON api_endpoints(category);

-- Insert default API endpoints
INSERT INTO api_endpoints (endpoint, method, description, category, requires_auth, rate_limit_per_hour) VALUES
('/api/external-links', 'GET', 'Get external links for a specific domain', 'external-links', false, 60),
('/trigger/links', 'GET', 'Trigger external links collection for all projects', 'batch-processing', true, 10),
('/trigger/traffic', 'GET', 'Trigger traffic data collection', 'batch-processing', true, 10),
('/trigger/domain-rating', 'GET', 'Trigger domain rating collection', 'batch-processing', true, 10),
('/trigger/indexing', 'GET', 'Trigger indexing status checks', 'batch-processing', true, 10),
('/trigger/all', 'GET', 'Trigger all data collection processes', 'batch-processing', true, 5),
('/health', 'GET', 'Health check endpoint', 'system', false, 1000),
('/api/stats', 'GET', 'Get API usage statistics', 'reporting', false, 100);

-- Create triggers to automatically update timestamps
CREATE TRIGGER update_api_endpoints_updated_at 
    AFTER UPDATE ON api_endpoints
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
    BEGIN
        UPDATE api_endpoints SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_api_usage_stats_updated_at 
    AFTER UPDATE ON api_usage_stats
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
    BEGIN
        UPDATE api_usage_stats SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;