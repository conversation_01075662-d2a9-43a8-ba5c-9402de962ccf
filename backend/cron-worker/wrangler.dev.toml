name = "linktrackpro-cron-worker-dev"
main = "src/index.js"
compatibility_date = "2023-10-30"
nodejs_compat = true

# Development environment configuration
[vars]
ENVIRONMENT = "development"
NODE_ENV = "development"
BATCH_SIZE = "5"
MAX_RETRIES = "1"
RETRY_DELAY_MS = "500"
SUPABASE_URL = "https://towzzcheowkqpfrbkipc.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvd3p6Y2hlb3drcXBmcmJraXBjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxMTQzMzExMCwiZXhwIjoyMDI3MDA5MTEwfQ.zdAM34ipH0WS0fIKRBbBtN2xn1JHb-GAHB7-cdv92yc"
SUPABASE_SCHEMA = "link_track"
FEISHU_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659"
AUTH_TOKEN = "8636be4e-24d9-11f0-8829-ebb795c2affb"
SIMILARWEB_API_KEY = ""
AHREFS_API_KEY = ""
GOOGLE_API_KEY = "AIzaSyCO8i49eEBD1qkH6zewa9hEhimJHUzorFc"
GOOGLE_SEARCH_ENGINE_ID = "210e0f75ee3984e2e"
EXA_API_KEY = "9246d222-3666-40a5-9a86-6738aea99c73"
RAPIDAPI_KEY = "**************************************************"
API_LAYER_KEY = "HtRYFKqZwkzFxuO6fj0wCQwojqUmX5a1"


[[d1_databases]]
binding = "DB"
database_name = "linktrackpro-api-logs"
database_id = "5740c3f4-dccd-44a7-bbb6-287c2efe332d"

# KV Database for RapidAPI quota management
[[kv_namespaces]]
binding = "QUOTA_KV"
id = "557d465e0281485eac57ecc8e5ff960a"
preview_id = "0a973bced9504116abffebe3a9f749ba"