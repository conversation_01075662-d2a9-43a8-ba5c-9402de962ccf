# SEO Traffic Authority API Migration

## 概述

由于原有的 Ahrefs API 已经废弃，我们已将 `external_links` 和 `domain_rating` API 迁移到新的 SEO Traffic Authority API。

## 变更详情

### 1. API 配置更新

**原有配置 (已废弃):**
- External Links: `ahrefs2.p.rapidapi.com` (月限额: 25次)
- Domain Rating: `ahrefs-api.p.rapidapi.com` (月限额: 300次)

**新配置:**
- External Links: `seo-traffic-authority.p.rapidapi.com/backlink-checker` (月限额: 500次)
- Domain Rating: `seo-traffic-authority.p.rapidapi.com/check` (月限额: 500次)

### 2. API 端点变更

#### External Links API
```bash
# 新的 API 调用
curl --request GET \
  --url 'https://seo-traffic-authority.p.rapidapi.com/backlink-checker?url=oceana.org&mode=subdomains' \
  --header 'x-rapidapi-host: seo-traffic-authority.p.rapidapi.com' \
  --header 'x-rapidapi-key: YOUR_API_KEY'
```

#### Domain Rating API
```bash
# 新的 API 调用
curl --request POST \
  --url https://seo-traffic-authority.p.rapidapi.com/check \
  --header 'Content-Type: application/json' \
  --header 'x-rapidapi-host: seo-traffic-authority.p.rapidapi.com' \
  --header 'x-rapidapi-key: YOUR_API_KEY' \
  --data '{"url":"google.com"}'
```

### 3. 响应格式适配

新 API 的响应格式已经通过响应适配器自动转换为系统内部格式，保持向后兼容性。

#### External Links 响应格式
```json
{
  "external_links": 478625,
  "refdomains": 16998,
  "dofollow_backlinks": 411618,
  "domain_rating": 80,
  "url_rating": 43,
  "discovered_links": [
    {
      "title": "Shark - Wikipedia",
      "url": "https://en.wikipedia.org/wiki/Shark",
      "target_url": "https://oceana.org/sharks/threats/",
      "domain_rating": 91,
      "anchor": "the original",
      "is_dofollow": true,
      "source": "seo_traffic_authority"
    }
  ]
}
```

#### Domain Rating 响应格式
```json
{
  "dr_score": 98,
  "domain_rank": 6
}
```

### 4. 代码变更

#### 文件修改列表
1. `backend/cron-worker/src/lib/api-adapter.js`
   - 更新 API_CONFIGS 中的 external_links 和 domain_rating 配置
   - 添加新的 SEO Traffic Authority API 适配器
   - 添加便捷方法: `getSEOTrafficAuthorityBacklinks()` 和 `getSEOTrafficAuthorityDomainRating()`

2. `backend/cron-worker/src/lib/quota-manager.js`
   - 更新方法映射，添加新的 API 方法映射

3. `backend/cron-worker/src/services/external-links.js`
   - 更新 `normalizeExternalLinksData()` 函数，支持新 API 源标识

4. `backend/cron-worker/src/services/domain-rating.js`
   - 更新 `normalizeDomainRatingData()` 函数，添加 SEO Traffic Authority 格式支持

### 5. 优势

1. **更高的配额限制**: 从 25/300 次/月提升到 500 次/月
2. **无日限制**: 新 API 没有每日调用限制
3. **统一的 API 源**: 两个功能使用同一个 API 提供商
4. **向后兼容**: 保持现有的数据格式和接口

### 6. 测试

运行测试脚本验证新 API 集成:
```bash
cd backend/cron-worker
node test-seo-traffic-authority.js
```

### 7. 监控

- 新 API 的配额使用情况会自动记录在配额管理系统中
- 可以通过 `/api/quota-status` 端点查看使用情况
- 系统会自动在配额不足时应用回退策略

### 8. 回退策略

如果新 API 出现问题，系统会自动应用以下回退策略:
- External Links: 返回空结果 (`empty_results`)
- Domain Rating: 返回默认评级 (`default_rating`)

### 9. 注意事项

1. 确保 `RAPIDAPI_KEY` 环境变量已正确配置
2. 新 API 使用相同的 RapidAPI 密钥
3. 旧的 Ahrefs API 配置保留作为备用选项
4. 监控新 API 的响应时间和成功率

## 迁移完成

✅ API 配置已更新
✅ 响应适配器已实现
✅ 数据标准化已适配
✅ 配额管理已集成
✅ 测试脚本已创建
✅ 向后兼容性已保持
