# 🗃️ Cloudflare KV Setup Guide for RapidAPI Quota Management

## 📋 Prerequisites

1. **Node.js >= 20.0.0** (current version: 18.20.4 - needs upgrade)
2. **Wrangler CLI** installed globally
3. **Cloudflare account** with Workers access

## 🚀 Step 1: Upgrade Node.js

### Using nvm (recommended):
```bash
# Install nvm if not already installed
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart terminal or source profile
source ~/.bashrc  # or ~/.zshrc

# Install and use Node.js 20
nvm install 20
nvm use 20
```

### Using volta:
```bash
# Install volta if not already installed
curl https://get.volta.sh | bash

# Install Node.js 20
volta install node@20
```

## 🗄️ Step 2: Create KV Namespaces

### Automatic Setup (Recommended):
```bash
cd /Users/<USER>/Code/LinkTrackPro/backend/cron-worker
./setup-kv.sh
```

### Manual Setup:
```bash
# 1. Create production namespace
npx wrangler kv namespace create "QUOTA_KV"
# Copy the ID from output

# 2. Create preview namespace  
npx wrangler kv namespace create "QUOTA_KV" --preview
# Copy the preview_id from output

# 3. Update wrangler.toml manually
# Replace YOUR_KV_NAMESPACE_ID_HERE with production ID
# Replace YOUR_KV_PREVIEW_ID_HERE with preview ID
```

## ⚙️ Step 3: Current wrangler.toml Configuration

The configuration has been prepared in your `wrangler.toml`:

```toml
# KV Database for RapidAPI quota management
[[kv_namespaces]]
binding = "QUOTA_KV"
id = "YOUR_KV_NAMESPACE_ID_HERE"        # Replace with actual ID
preview_id = "YOUR_KV_PREVIEW_ID_HERE"  # Replace with actual preview ID
```

## 🚀 Step 4: Deploy Worker

After setting up KV:
```bash
npx wrangler deploy
```

## 🌐 Step 5: Access Quota Dashboard

Once deployed, you can access:

### Web Interface:
- **Quota Dashboard**: `https://your-worker-url.com/quota?token=8636be4e-24d9-11f0-8829-ebb795c2affb`
- **API Logs**: `https://your-worker-url.com/logs?token=8636be4e-24d9-11f0-8829-ebb795c2affb`

### API Endpoints:
- **GET** `/api/quota/status` - View quota status
- **POST** `/api/quota/reset` - Reset quotas
- **POST** `/api/quota/update` - Update quota configs

## 📊 Pre-configured APIs

The system comes with these RapidAPI quotas:

| API | Monthly Limit | Daily Limit | Allow Pay |
|-----|---------------|-------------|-----------|
| SimilarWeb Insights | 30 | - | No |
| Google Search Results | 100 | 5 | Yes |
| SEO Analysis API | 50 | 3 | No |

## 🔧 Troubleshooting

### Common Issues:

1. **Node.js version error**: Upgrade to Node.js 20+
2. **Wrangler not found**: Install with `npm install -g wrangler`
3. **Authentication error**: Run `npx wrangler login`
4. **KV binding error**: Verify namespace IDs in wrangler.toml

### Verify Setup:
```bash
# Check current configuration
npx wrangler kv:namespace list

# Test deployment
npx wrangler dev
```

## ✅ Next Steps

After successful setup:

1. ✅ KV namespaces created and bound
2. ✅ Worker deployed with quota management
3. ✅ Access web dashboard for quota monitoring
4. ✅ APIs automatically respect quota limits
5. ✅ Monthly/daily resets work automatically

Your RapidAPI quota management system is ready! 🎉