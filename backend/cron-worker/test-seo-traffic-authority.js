/**
 * Test script for SEO Traffic Authority API integration
 * This script tests the new API endpoints to ensure they work correctly
 */

import { apiClient } from './src/lib/api-adapter.js';
import { loadConfig } from './src/lib/config.js';

// Mock environment for testing
const mockEnv = {
  RAPIDAPI_KEY: process.env.RAPIDAPI_KEY || 'your_rapidapi_key_here'
};

async function testSEOTrafficAuthorityAPI() {
  console.log('🧪 Testing SEO Traffic Authority API Integration');
  console.log('=' .repeat(50));
  
  // Load configuration
  loadConfig(mockEnv);
  
  const testDomain = 'oceana.org';
  
  try {
    // Test 1: Domain Rating Check
    console.log('\n📊 Testing Domain Rating API...');
    const domainRatingResult = await apiClient.getSEOTrafficAuthorityDomainRating(testDomain);
    
    console.log('Domain Rating Result:', JSON.stringify(domainRatingResult, null, 2));
    
    if (domainRatingResult.success) {
      console.log('✅ Domain Rating API test passed');
      console.log(`   DR Score: ${domainRatingResult.data.dr_score}`);
      console.log(`   Domain Rank: ${domainRatingResult.data.domain_rank}`);
    } else {
      console.log('❌ Domain Rating API test failed:', domainRatingResult.error);
    }
    
    // Test 2: Backlinks Check
    console.log('\n🔗 Testing Backlinks API...');
    // Try with a simpler domain first
    const simpleTestDomain = 'google.com';
    console.log(`   Testing with domain: ${simpleTestDomain}`);
    const backlinksResult = await apiClient.getSEOTrafficAuthorityBacklinks(simpleTestDomain, 'subdomains');
    
    console.log('Backlinks Result:', JSON.stringify(backlinksResult, null, 2));
    
    if (backlinksResult.success) {
      console.log('✅ Backlinks API test passed');
      console.log(`   External Links: ${backlinksResult.data.external_links}`);
      console.log(`   Referring Domains: ${backlinksResult.data.refdomains}`);
      console.log(`   Domain Rating: ${backlinksResult.data.domain_rating}`);
      console.log(`   Discovered Links: ${backlinksResult.data.discovered_links.length}`);
    } else {
      console.log('❌ Backlinks API test failed:', backlinksResult.error);
    }
    
    // Test 3: API Configuration Check
    console.log('\n⚙️ Testing API Configuration...');
    const adapter = apiClient.getAdapter('seo-traffic-authority.p.rapidapi.com');
    
    if (adapter) {
      console.log('✅ API adapter configuration found');
      console.log(`   Adapter Name: ${adapter.name}`);
      console.log(`   Base URL: ${adapter.baseUrl}`);
      console.log(`   Endpoints: ${Object.keys(adapter.endpoints).join(', ')}`);
    } else {
      console.log('❌ API adapter configuration not found');
    }
    
    // Test 4: Quota Status Check
    console.log('\n📈 Testing Quota Status...');
    const quotaStatus = await apiClient.getQuotaStatus();
    
    const seoTrafficAuthorityQuota = quotaStatus['seo-traffic-authority.p.rapidapi.com'];
    if (seoTrafficAuthorityQuota) {
      console.log('✅ Quota status available');
      console.log(`   Monthly Usage: ${seoTrafficAuthorityQuota.monthly.used}/${seoTrafficAuthorityQuota.monthly.limit}`);
      console.log(`   Daily Usage: ${seoTrafficAuthorityQuota.daily.used}/${seoTrafficAuthorityQuota.daily.limit || 'unlimited'}`);
    } else {
      console.log('⚠️ Quota status not available for SEO Traffic Authority API');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Test completed');
}

// Test API response adapters
async function testResponseAdapters() {
  console.log('\n🔧 Testing Response Adapters...');
  
  // Mock response data based on the API documentation
  const mockBacklinksResponse = {
    success: true,
    data: {
      overview: {
        domainRating: 80,
        urlRating: 43,
        backlinks: 478625,
        referringDomains: 16998,
        dofollowBacklinks: {
          percentage: 86,
          count: 411618
        }
      },
      backlinks: [
        {
          anchor: "the original",
          domainRating: 91,
          title: "Shark - Wikipedia",
          urlFrom: "https://en.wikipedia.org/wiki/Shark",
          urlTo: "https://oceana.org/sharks/threats/",
          httpCode: 200
        }
      ]
    }
  };
  
  const mockDomainRatingResponse = {
    success: true,
    data: {
      domainRating: 98,
      domainRank: 6
    }
  };
  
  // Test backlinks adapter
  const adapter = apiClient.getAdapter('seo-traffic-authority.p.rapidapi.com');
  const backlinksAdapter = adapter.endpoints['backlink-checker'].responseAdapter;
  const adaptedBacklinks = backlinksAdapter(mockBacklinksResponse);
  
  console.log('Adapted Backlinks Response:', JSON.stringify(adaptedBacklinks, null, 2));
  
  // Test domain rating adapter
  const domainRatingAdapter = adapter.endpoints.check.responseAdapter;
  const adaptedDomainRating = domainRatingAdapter(mockDomainRatingResponse);
  
  console.log('Adapted Domain Rating Response:', JSON.stringify(adaptedDomainRating, null, 2));
}

// Run tests
async function runTests() {
  await testSEOTrafficAuthorityAPI();
  await testResponseAdapters();
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { testSEOTrafficAuthorityAPI, testResponseAdapters };
