#!/bin/bash

echo "🚀 Setting up Cloudflare KV for RapidAPI Quota Management"
echo "=================================================="

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI not found. Please install it first:"
    echo "npm install -g wrangler"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="20.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
    echo "✅ Node.js version: $NODE_VERSION (>= $REQUIRED_VERSION)"
else
    echo "❌ Node.js version $NODE_VERSION is too old. Required: >= $REQUIRED_VERSION"
    echo "Please update Node.js using nvm or volta:"
    echo "  nvm install 20"
    echo "  nvm use 20"
    exit 1
fi

echo ""
echo "📦 Creating KV namespace for quota management..."

# Create production KV namespace
echo "Creating production namespace..."
PROD_OUTPUT=$(npx wrangler kv namespace create "QUOTA_KV" 2>&1)
echo "$PROD_OUTPUT"

# Extract namespace ID from output (new format)
PROD_ID=$(echo "$PROD_OUTPUT" | grep -o 'id: "[^"]*"' | cut -d'"' -f2)
if [ -z "$PROD_ID" ]; then
    # Try alternative format
    PROD_ID=$(echo "$PROD_OUTPUT" | grep -o 'id = "[^"]*"' | cut -d'"' -f2)
fi
if [ -z "$PROD_ID" ]; then
    # Try another format
    PROD_ID=$(echo "$PROD_OUTPUT" | grep -oE '[0-9a-f]{32}')
fi

if [ -z "$PROD_ID" ]; then
    echo "❌ Failed to create production namespace. Please check the output above."
    exit 1
fi

echo ""
echo "📦 Creating preview KV namespace..."

# Create preview KV namespace  
PREVIEW_OUTPUT=$(npx wrangler kv namespace create "QUOTA_KV" --preview 2>&1)
echo "$PREVIEW_OUTPUT"

# Extract preview namespace ID (new format)
PREVIEW_ID=$(echo "$PREVIEW_OUTPUT" | grep -o 'id: "[^"]*"' | cut -d'"' -f2)
if [ -z "$PREVIEW_ID" ]; then
    # Try alternative format
    PREVIEW_ID=$(echo "$PREVIEW_OUTPUT" | grep -o 'preview_id = "[^"]*"' | cut -d'"' -f2)
fi
if [ -z "$PREVIEW_ID" ]; then
    # Try another format
    PREVIEW_ID=$(echo "$PREVIEW_OUTPUT" | grep -oE '[0-9a-f]{32}')
fi

if [ -z "$PREVIEW_ID" ]; then
    echo "❌ Failed to create preview namespace. Please check the output above."
    exit 1
fi

echo ""
echo "📝 Updating wrangler.toml configuration..."

# Update wrangler.toml with the actual IDs
sed -i.bak "s/YOUR_KV_NAMESPACE_ID_HERE/$PROD_ID/" wrangler.toml
sed -i.bak "s/YOUR_KV_PREVIEW_ID_HERE/$PREVIEW_ID/" wrangler.toml

# Remove backup file
rm -f wrangler.toml.bak

echo ""
echo "✅ KV setup completed successfully!"
echo ""
echo "📋 Configuration added to wrangler.toml:"
echo "  Production ID: $PROD_ID"
echo "  Preview ID: $PREVIEW_ID"
echo ""
echo "🚀 You can now deploy your worker with:"
echo "  npx wrangler deploy"
echo ""
echo "🌐 After deployment, access the quota dashboard at:"
echo "  https://your-worker-url.com/quota?token=YOUR_AUTH_TOKEN"