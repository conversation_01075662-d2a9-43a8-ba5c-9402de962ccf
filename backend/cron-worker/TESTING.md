# LinkTrackPro Cron Worker 测试指南

## 测试脚本概览

项目提供了三个层次的测试脚本：

### 1. 基础测试 (`npm test`)
```bash
npm test
# 或
node tests/test-basic.js
```

**用途**: 验证核心功能，不需要数据库连接
**测试内容**:
- ✅ 配置加载
- ✅ 身份验证逻辑
- ✅ 模块导入
- ✅ Worker结构
- ✅ 环境变量检查
- ✅ 日期/调度逻辑

### 2. 完整测试 (`npm run test:full`)
```bash
npm run test:full
# 或
node tests/full.js
```

**用途**: 全面的自动化测试套件
**测试内容**:
- 🔧 配置验证测试
- 🗄️ 数据库连接测试
- 🔐 身份验证测试
- 🌐 服务可用性测试
- ⚙️ 处理器结构测试
- 🔗 Worker端点测试

### 3. 手动测试 (`npm run test:manual`)
```bash
npm run test:manual <command>
# 或
node tests/manual.js <command>
```

### 4. 完整测试套件 (`npm run test:all`)
```bash
npm run test:all
# 或
node tests/run-all-tests.js
```

**用途**: 交互式测试和调试工具

## 手动测试命令

### 配置和健康检查
```bash
# 查看当前配置
node test-manual.js config

# 运行健康检查
node test-manual.js health-check
```

### 单个组件测试
```bash
# 测试飞书通知
node test-manual.js test-feishu

# 测试流量数据收集
node test-manual.js test-traffic

# 测试域名评级收集
node test-manual.js test-dr

# 测试外链收集
node test-manual.js test-links

# 测试索引状态检查
node test-manual.js test-indexing
```

### 模拟执行测试
```bash
# 模拟定时任务执行
node test-manual.js simulate-cron

# 模拟周日执行（会运行周任务）
node test-manual.js simulate-cron --day=sunday

# 测试手动触发端点
node test-manual.js worker-fetch --endpoint=/trigger/traffic
node test-manual.js worker-fetch --endpoint=/trigger/dr
node test-manual.js worker-fetch --endpoint=/trigger/links
node test-manual.js worker-fetch --endpoint=/trigger/indexing
```

## 环境变量配置

### 必需变量
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-service-role-key
AUTH_TOKEN=your-auth-token-for-manual-triggers
```

### API服务变量
```bash
SIMILARWEB_API_KEY=your-similarweb-api-key
AHREFS_API_KEY=your-ahrefs-api-key
GOOGLE_API_KEY=your-google-api-key
GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id
FEISHU_WEBHOOK_URL=https://open.feishu.cn/your-webhook
```

### 可选配置
```bash
BATCH_SIZE=50              # API调用批次大小
MAX_RETRIES=3              # 最大重试次数
RETRY_DELAY_MS=1000        # 重试延迟(毫秒)
```

## 测试场景

### 开发环境测试
1. **基础功能验证**:
   ```bash
   npm test
   ```

2. **配置检查**:
   ```bash
   node test-manual.js config
   node test-manual.js health-check
   ```

### 生产部署前测试
1. **完整功能测试**:
   ```bash
   npm run test:full
   ```

2. **API连接测试**:
   ```bash
   node test-manual.js test-feishu
   node test-manual.js test-traffic
   ```

3. **模拟执行测试**:
   ```bash
   node test-manual.js simulate-cron
   node test-manual.js simulate-cron --day=sunday
   ```

### 问题排查
1. **配置问题**:
   ```bash
   node test-manual.js config
   ```

2. **数据库连接问题**:
   ```bash
   node test-manual.js test-traffic  # 会显示数据库初始化状态
   ```

3. **API服务问题**:
   ```bash
   node test-manual.js test-feishu   # 测试通知服务
   node test-manual.js test-traffic  # 测试SimilarWeb API
   node test-manual.js test-dr       # 测试Ahrefs API
   ```

4. **Worker端点问题**:
   ```bash
   node test-manual.js worker-fetch --endpoint=/trigger/traffic
   ```

## 测试结果解读

### 成功指标
- ✅ 所有基础测试通过
- ✅ 配置正确加载
- ✅ API密钥正确设置
- ✅ 数据库连接成功
- ✅ 通知服务正常

### 警告指标
- ⚠️ 使用测试配置值
- ⚠️ 某些API密钥未设置
- ⚠️ 数据库未初始化（在无数据库环境下正常）

### 错误指标
- ❌ 模块导入失败
- ❌ 函数结构错误
- ❌ 配置加载失败
- ❌ 网络连接错误

## 常见问题

### Q: 测试时出现"Supabase client not initialized"错误
**A**: 这是正常的，表示没有配置数据库连接。在开发/测试环境中，这个错误是预期的。如需完整测试，请设置 `SUPABASE_URL` 和 `SUPABASE_KEY` 环境变量。

### Q: API测试失败
**A**: 检查对应的API密钥是否正确设置：
- SimilarWeb: `SIMILARWEB_API_KEY`
- Ahrefs: `AHREFS_API_KEY`
- Google: `GOOGLE_API_KEY` 和 `GOOGLE_SEARCH_ENGINE_ID`

### Q: 飞书通知测试失败
**A**: 确保 `FEISHU_WEBHOOK_URL` 设置正确，并且webhook地址有效。

### Q: 如何在CI/CD中运行测试
**A**: 使用基础测试即可：
```bash
npm test
```
这个测试不需要外部依赖，适合CI环境。

## 下一步

完成测试后，可以：
1. 部署到Cloudflare Workers: `npm run deploy`
2. 设置生产环境的环境变量
3. 配置cron触发器
4. 监控执行日志和飞书通知