/**
 * Traffic data processor for SimilarWeb data (Updated for new schema)
 * Now focuses on domain-level statistics rather than individual links
 */

import { fetchAllDomains, updateDomainStats, updateProjectsStats, fetchProjects } from '../db.js';
import { fetchTrafficData, initTrafficService } from '../services/traffic.js';
import { sendMessage } from '../services/notification.js';
import { processBatches } from '../lib/utils.js';

/**
 * Fetch traffic data from SimilarWeb API for all domains
 */
export async function processTrafficData(kvNamespace = null) {
  console.log('🚀 Starting quota-managed daily traffic data collection from SimilarWeb (new schema)');
  
  // Initialize traffic service with quota management
  if (kvNamespace) {
    initTrafficService(kvNamespace);
  }
  
  try {
    // 1. Get all unique domains from projects, discovered_links, and link_resources
    const domains = await fetchAllDomains();
    console.log(`Found ${domains.length} unique domains to process`);
    
    if (domains.length === 0) {
      console.log('No domains found to process');
      return;
    }
    
    // 2. Process domains in batches to get traffic data
    const domainResults = await processBatches(domains, async (domainObj) => {
      try {
        const trafficData = await fetchTrafficData(domainObj.domain);
        return {
          domain: domainObj.domain,
          traffic: trafficData.traffic || 0,
          timestamp: trafficData.timestamp || new Date().toISOString(),
          error: trafficData.error
        };
      } catch (error) {
        console.error(`Error fetching traffic for ${domainObj.domain}:`, error);
        return {
          domain: domainObj.domain,
          traffic: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }, {
      batchSize: 5, // Smaller batches for external API calls
      delayBetweenBatches: 1000 // 1 second delay to respect rate limits
    });
    
    // 3. Update domain statistics in all_links table
    await updateDomainStats(domainResults, 'traffic');
    
    // 4. Update project statistics
    const projects = await fetchProjects();
    const projectIds = projects.map(p => p.id);
    if (projectIds.length > 0) {
      await updateProjectsStats(projectIds);
    }
    
    // 5. Send report via Feishu webhook
    const summary = generateTrafficSummary(domainResults);
    await sendMessage('📈 Daily Traffic Report (New Schema)', summary);
    
    console.log('✅ Traffic data collection completed successfully');
  } catch (error) {
    console.error('❌ Error in traffic data processing:', error);
    throw error;
  }
}

/**
 * Generate traffic summary report
 */
function generateTrafficSummary(domainResults) {
  const totalDomains = domainResults.length;
  const successfulDomains = domainResults.filter(d => !d.error).length;
  const failedDomains = domainResults.filter(d => d.error).length;
  
  let summary = `🔄 **Traffic Data Collection Summary**\n\n`;
  summary += `📊 **Statistics:**\n`;
  summary += `• Total domains processed: ${totalDomains}\n`;
  summary += `• Successful: ${successfulDomains}\n`;
  summary += `• Failed: ${failedDomains}\n\n`;
  
  // Add top 5 domains by traffic
  const topDomains = [...domainResults]
    .filter(d => !d.error && d.traffic > 0)
    .sort((a, b) => b.traffic - a.traffic)
    .slice(0, 5);
  
  if (topDomains.length > 0) {
    summary += `🏆 **Top domains by traffic:**\n`;
    topDomains.forEach((domain, i) => {
      summary += `${i + 1}. ${domain.domain}: ${domain.traffic.toLocaleString()} visits\n`;
    });
    summary += `\n`;
  }
  
  // Add failed domains if any
  const failedDomainsWithErrors = domainResults.filter(d => d.error).slice(0, 3);
  if (failedDomainsWithErrors.length > 0) {
    summary += `⚠️ **Failed domains (showing first 3):**\n`;
    failedDomainsWithErrors.forEach(domain => {
      summary += `• ${domain.domain}: ${domain.error}\n`;
    });
    summary += `\n`;
  }
  
  // Add total traffic
  const totalTraffic = domainResults
    .filter(d => !d.error)
    .reduce((sum, d) => sum + d.traffic, 0);
  
  summary += `📈 **Total traffic across all domains:** ${totalTraffic.toLocaleString()} visits\n`;
  summary += `🔗 **SimilarWeb API calls made:** ${totalDomains}\n`;
  summary += `⏰ **Processed at:** ${new Date().toISOString()}`;
  
  return summary;
}

/**
 * Manual traffic processing for specific domains (for testing/debugging)
 * @param {Array} domains - Array of domain strings to process
 */
export async function processTrafficForDomains(domains) {
  console.log(`🔧 Manual traffic processing for ${domains.length} domains`);
  
  const domainObjects = domains.map(domain => ({ domain }));
  
  const domainResults = await processBatches(domainObjects, async (domainObj) => {
    try {
      const trafficData = await fetchTrafficData(domainObj.domain);
      return {
        domain: domainObj.domain,
        traffic: trafficData.traffic || 0,
        timestamp: trafficData.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error fetching traffic for ${domainObj.domain}:`, error);
      return {
        domain: domainObj.domain,
        traffic: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  });
  
  await updateDomainStats(domainResults, 'traffic');
  
  return domainResults;
}

/**
 * Get traffic data for a single domain (API endpoint)
 * @param {string} domain - Domain to process
 * @param {Object} kvNamespace - Optional KV namespace for quota management
 */
export async function getTrafficData(domain, kvNamespace = null) {
  console.log(`🔍 Getting traffic data for single domain: ${domain}`);
  
  // Initialize traffic service with quota management
  if (kvNamespace) {
    initTrafficService(kvNamespace);
  }
  
  try {
    const trafficData = await fetchTrafficData(domain);
    const result = {
      domain: domain,
      traffic: trafficData.traffic || 0,
      timestamp: trafficData.timestamp || new Date().toISOString(),
      error: trafficData.error
    };
    
    // Update domain stats in database
    await updateDomainStats([result], 'traffic');
    
    return result;
  } catch (error) {
    console.error(`Error getting traffic for ${domain}:`, error);
    return {
      domain: domain,
      traffic: 0,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}