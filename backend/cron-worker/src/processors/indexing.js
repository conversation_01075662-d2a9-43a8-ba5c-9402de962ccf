/**
 * Indexing status processor for Google Search API data (Updated for new schema)
 * Now focuses on domain-level indexing statistics
 */

import { fetchAllDomains, updateDomainStats, updateProjectsStats, fetchProjects, updateDiscoveredLinksIndexingStatus } from '../db.js';
import { checkIndexingStatus, getIndexedUrls, initIndexingService } from '../services/indexing.js';
import { sendMessage } from '../services/notification.js';
import { processBatches } from '../lib/utils.js';

/**
 * Check indexing status for all domains using Google Search API
 */
export async function processIndexingStatus(kvNamespace = null) {
  console.log('🚀 Starting quota-managed weekly indexing status check via Google Search API (new schema)');
  
  // Initialize indexing service with quota management
  if (kvNamespace) {
    initIndexingService(kvNamespace);
  }
  
  try {
    // 1. Get all unique domains from projects, discovered_links, and link_resources
    const domains = await fetchAllDomains();
    console.log(`Found ${domains.length} unique domains to check indexing status`);
    
    if (domains.length === 0) {
      console.log('No domains found to process');
      return;
    }
    
    // 2. Process domains in batches to check indexing status
    const domainResults = await processBatches(domains, async (domainObj) => {
      try {
        const indexingData = await checkIndexingStatus(domainObj.domain);
        
        // Get indexed URLs for this domain to update discovered_links
        const indexedUrls = await getIndexedUrls(domainObj.domain);
        
        // Update discovered_links with indexing status
        await updateDiscoveredLinksIndexingStatus(domainObj.domain, indexedUrls);
        
        return {
          domain: domainObj.domain,
          is_indexed: indexingData.is_indexed || false,
          indexed_pages: indexingData.indexed_pages || 0,
          total_pages: indexingData.total_pages || 0,
          indexing_issues: indexingData.issues || [],
          indexed_urls: indexedUrls.length,
          timestamp: indexingData.timestamp || new Date().toISOString(),
          error: indexingData.error
        };
      } catch (error) {
        console.error(`Error checking indexing for ${domainObj.domain}:`, error);
        return {
          domain: domainObj.domain,
          is_indexed: false,
          indexed_pages: 0,
          total_pages: 0,
          indexing_issues: [],
          indexed_urls: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }, {
      batchSize: 3, // Small batches for Google API (rate limits)
      delayBetweenBatches: 2000 // 2 second delay to respect rate limits
    });
    
    // 3. Update domain statistics in all_links table
    await updateDomainStats(domainResults, 'indexing');
    
    // 4. Update project statistics
    const projects = await fetchProjects();
    const projectIds = projects.map(p => p.id);
    if (projectIds.length > 0) {
      await updateProjectsStats(projectIds);
    }
    
    // 5. Send report via Feishu webhook
    const summary = generateIndexingSummary(domainResults);
    await sendMessage('🔍 Weekly Indexing Status Report (New Schema)', summary);
    
    console.log('✅ Indexing status check completed successfully');
  } catch (error) {
    console.error('❌ Error in indexing status processing:', error);
    throw error;
  }
}

/**
 * Generate indexing status summary report
 */
function generateIndexingSummary(domainResults) {
  const totalDomains = domainResults.length;
  const successfulDomains = domainResults.filter(d => !d.error).length;
  const failedDomains = domainResults.filter(d => d.error).length;
  
  // Calculate indexing statistics
  const indexedDomains = domainResults.filter(d => !d.error && d.is_indexed).length;
  const notIndexedDomains = domainResults.filter(d => !d.error && !d.is_indexed).length;
  const domainsWithIssues = domainResults.filter(d => !d.error && d.indexing_issues && d.indexing_issues.length > 0).length;
  
  let summary = `🔄 **Indexing Status Check Summary**\n\n`;
  summary += `📊 **Statistics:**\n`;
  summary += `• Total domains checked: ${totalDomains}\n`;
  summary += `• Successfully checked: ${successfulDomains}\n`;
  summary += `• Failed checks: ${failedDomains}\n\n`;
  
  summary += `🔍 **Indexing Status:**\n`;
  summary += `• Indexed domains: ${indexedDomains} (${Math.round(indexedDomains/successfulDomains*100)||0}%)\n`;
  summary += `• Not indexed domains: ${notIndexedDomains} (${Math.round(notIndexedDomains/successfulDomains*100)||0}%)\n`;
  summary += `• Domains with issues: ${domainsWithIssues}\n\n`;
  
  // Add top 5 domains by indexed pages
  const topIndexedDomains = [...domainResults]
    .filter(d => !d.error && d.indexed_pages > 0)
    .sort((a, b) => b.indexed_pages - a.indexed_pages)
    .slice(0, 5);
  
  if (topIndexedDomains.length > 0) {
    summary += `🏆 **Top domains by indexed pages:**\n`;
    topIndexedDomains.forEach((domain, i) => {
      const percentage = domain.total_pages > 0 ? 
        Math.round((domain.indexed_pages / domain.total_pages) * 100) : 0;
      summary += `${i + 1}. ${domain.domain}: ${domain.indexed_pages}/${domain.total_pages} pages (${percentage}%)\n`;
    });
    summary += `\n`;
  }
  
  // Add domains with indexing issues
  const domainsWithIssuesList = domainResults
    .filter(d => !d.error && d.indexing_issues && d.indexing_issues.length > 0)
    .slice(0, 5);
  
  if (domainsWithIssuesList.length > 0) {
    summary += `⚠️ **Domains with indexing issues (showing first 5):**\n`;
    domainsWithIssuesList.forEach(domain => {
      const issuesText = domain.indexing_issues.slice(0, 2).join(', ');
      summary += `• ${domain.domain}: ${issuesText}\n`;
    });
    summary += `\n`;
  }
  
  // Add newly indexed domains
  const newlyIndexedDomains = domainResults
    .filter(d => !d.error && d.is_indexed)
    .slice(0, 5);
  
  if (newlyIndexedDomains.length > 0) {
    summary += `✅ **Recently indexed domains:**\n`;
    newlyIndexedDomains.forEach(domain => {
      summary += `• ${domain.domain}: ${domain.indexed_pages} pages indexed\n`;
    });
    summary += `\n`;
  }
  
  // Add failed domains if any
  const failedDomainsWithErrors = domainResults.filter(d => d.error).slice(0, 3);
  if (failedDomainsWithErrors.length > 0) {
    summary += `❌ **Failed domains (showing first 3):**\n`;
    failedDomainsWithErrors.forEach(domain => {
      summary += `• ${domain.domain}: ${domain.error}\n`;
    });
    summary += `\n`;
  }
  
  // Calculate totals
  const totalIndexedPages = domainResults
    .filter(d => !d.error)
    .reduce((sum, d) => sum + d.indexed_pages, 0);
  
  const totalPages = domainResults
    .filter(d => !d.error)
    .reduce((sum, d) => sum + d.total_pages, 0);
  
  const overallIndexingRate = totalPages > 0 ? Math.round((totalIndexedPages / totalPages) * 100) : 0;
  
  // Calculate discovered links updates
  const totalIndexedUrls = domainResults
    .filter(d => !d.error)
    .reduce((sum, d) => sum + (d.indexed_urls || 0), 0);

  summary += `📈 **Overall Statistics:**\n`;
  summary += `• Total indexed pages: ${totalIndexedPages.toLocaleString()}\n`;
  summary += `• Total pages: ${totalPages.toLocaleString()}\n`;
  summary += `• Overall indexing rate: ${overallIndexingRate}%\n`;
  summary += `• Discovered links updated: ${totalIndexedUrls}\n`;
  summary += `• Google Search API calls: ${totalDomains}\n`;
  summary += `⏰ **Processed at:** ${new Date().toISOString()}`;
  
  return summary;
}

/**
 * Manual indexing status check for specific domains (for testing/debugging)
 * @param {Array} domains - Array of domain strings to check
 */
export async function processIndexingForDomains(domains) {
  console.log(`🔧 Manual indexing status check for ${domains.length} domains`);
  
  const domainObjects = domains.map(domain => ({ domain }));
  
  const domainResults = await processBatches(domainObjects, async (domainObj) => {
    try {
      const indexingData = await checkIndexingStatus(domainObj.domain);
      return {
        domain: domainObj.domain,
        is_indexed: indexingData.is_indexed || false,
        indexed_pages: indexingData.indexed_pages || 0,
        total_pages: indexingData.total_pages || 0,
        indexing_issues: indexingData.issues || [],
        timestamp: indexingData.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error checking indexing for ${domainObj.domain}:`, error);
      return {
        domain: domainObj.domain,
        is_indexed: false,
        indexed_pages: 0,
        total_pages: 0,
        indexing_issues: [],
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  });
  
  await updateDomainStats(domainResults, 'indexing');
  
  return domainResults;
}

/**
 * Check indexing status for a single domain (quick check)
 * @param {string} domain - Domain to check
 */
export async function quickIndexingCheck(domain) {
  console.log(`🔍 Quick indexing check for ${domain}`);
  
  try {
    const indexingData = await checkIndexingStatus(domain);
    const result = {
      domain: domain,
      is_indexed: indexingData.is_indexed || false,
      indexed_pages: indexingData.indexed_pages || 0,
      total_pages: indexingData.total_pages || 0,
      indexing_issues: indexingData.issues || [],
      timestamp: indexingData.timestamp || new Date().toISOString()
    };
    
    // Update domain stats
    await updateDomainStats([result], 'indexing');
    
    return result;
  } catch (error) {
    console.error(`Error in quick indexing check for ${domain}:`, error);
    throw error;
  }
}

/**
 * Get indexing status for a single domain (API endpoint)
 * @param {string} domain - Domain to process
 * @param {string} sourceDomain - Optional source domain to search within
 * @param {Object} kvNamespace - Optional KV namespace for quota management
 */
export async function getIndexingStatus(domain, sourceDomain = null, kvNamespace = null) {
  console.log(`🔍 Getting indexing status for single domain: ${domain}${sourceDomain ? ` within ${sourceDomain}` : ''}`);
  
  // Initialize indexing service with quota management
  if (kvNamespace) {
    initIndexingService(kvNamespace);
  }
  
  try {
    // If sourceDomain is provided, search for domain within sourceDomain
    // Otherwise, search for domain's own indexing status
    const targetUrl = domain;
    const searchDomain = sourceDomain || domain;
    
    const indexingData = await checkIndexingStatus(targetUrl, searchDomain);
    const result = {
      domain: searchDomain,
      target_url: targetUrl,
      is_indexed: indexingData.is_indexed || false,
      indexed_pages: indexingData.indexed_pages || 0,
      total_pages: indexingData.total_pages || 0,
      indexing_issues: indexingData.indexing_issues || [],
      timestamp: indexingData.timestamp || new Date().toISOString(),
      source: indexingData.source || 'unknown',
      error: indexingData.error
    };
    
    // Only update domain stats if we're checking the domain's own indexing
    if (!sourceDomain) {
      await updateDomainStats([result], 'indexing');
    }
    
    return result;
  } catch (error) {
    console.error(`Error getting indexing status for ${domain}:`, error);
    return {
      domain: sourceDomain || domain,
      target_url: domain,
      is_indexed: false,
      indexed_pages: 0,
      total_pages: 0,
      indexing_issues: [],
      error: error.message,
      timestamp: new Date().toISOString(),
      source: 'error'
    };
  }
}