/**
 * WHOIS processor for domain registration information
 * Focuses on domain-level WHOIS data collection and storage
 */

import { fetchAllDomains, storeWhoisInfo, updateProjectsStats, fetchProjects } from '../db.js';
import { fetchWhoisInfo, initWhoisService } from '../services/whois.js';
import { sendMessage } from '../services/notification.js';
import { processBatches } from '../lib/utils.js';

/**
 * Fetch WHOIS data from API Layer for all domains
 */
export async function processWhoisInfo(kvNamespace = null) {
  console.log('🚀 Starting quota-managed WHOIS information collection from API Layer');
  
  // Initialize WHOIS service with quota management
  if (kvNamespace) {
    initWhoisService(kvNamespace);
  }
  
  try {
    // 1. Get all unique domains from projects, discovered_links, and link_resources
    const domains = await fetchAllDomains();
    console.log(`Found ${domains.length} unique domains to process for WHOIS`);
    
    if (domains.length === 0) {
      console.log('No domains found to process');
      return;
    }
    
    // 2. Process domains in batches to get WHOIS data
    const domainResults = await processBatches(domains, async (domainObj) => {
      try {
        const whoisData = await fetchWhoisInfo(domainObj.domain);
        
        // Store WHOIS data in database if successful
        if (whoisData && !whoisData.error) {
          await storeWhoisInfo(domainObj.domain, whoisData);
        }
        
        return {
          domain: domainObj.domain,
          creation_date: whoisData.creation_date,
          expiration_date: whoisData.expiration_date,
          registrar: whoisData.registrar,
          timestamp: whoisData.timestamp || new Date().toISOString(),
          error: whoisData.error,
          cached: whoisData.cached || false
        };
      } catch (error) {
        console.error(`Error fetching WHOIS for ${domainObj.domain}:`, error);
        return {
          domain: domainObj.domain,
          creation_date: null,
          expiration_date: null,
          registrar: '',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }, {
      batchSize: 5, // API Layer has good rate limits (3000/month)
      delayBetweenBatches: 1000 // 1 second delay to be respectful
    });
    
    // 3. Update project statistics
    const projects = await fetchProjects();
    const projectIds = projects.map(p => p.id);
    if (projectIds.length > 0) {
      await updateProjectsStats(projectIds);
    }
    
    // 4. Send report via Feishu webhook
    const summary = generateWhoisSummary(domainResults);
    await sendMessage('🔍 Weekly WHOIS Information Report', summary);
    
    console.log('✅ WHOIS information collection completed successfully');
  } catch (error) {
    console.error('❌ Error in WHOIS processing:', error);
    throw error;
  }
}

/**
 * Generate WHOIS collection summary report
 */
function generateWhoisSummary(domainResults) {
  const totalDomains = domainResults.length;
  const successfulDomains = domainResults.filter(d => !d.error).length;
  const failedDomains = domainResults.filter(d => d.error).length;
  const cachedDomains = domainResults.filter(d => d.cached).length;
  
  let summary = `🔄 **WHOIS Information Collection Summary**\n\n`;
  summary += `📊 **Statistics:**\n`;
  summary += `• Total domains processed: ${totalDomains}\n`;
  summary += `• Successful: ${successfulDomains}\n`;
  summary += `• Failed: ${failedDomains}\n`;
  summary += `• From cache: ${cachedDomains}\n\n`;
  
  // Add domain age analysis
  const domainsWithCreationDate = domainResults.filter(d => !d.error && d.creation_date);
  if (domainsWithCreationDate.length > 0) {
    const now = new Date();
    const ageRanges = {
      'Very Old (15+ years)': 0,
      'Old (10-15 years)': 0,
      'Mature (5-10 years)': 0,
      'Medium (2-5 years)': 0,
      'Young (1-2 years)': 0,
      'New (< 1 year)': 0
    };
    
    domainsWithCreationDate.forEach(domain => {
      try {
        const created = new Date(domain.creation_date);
        const ageYears = (now - created) / (1000 * 60 * 60 * 24 * 365);
        
        if (ageYears >= 15) ageRanges['Very Old (15+ years)']++;
        else if (ageYears >= 10) ageRanges['Old (10-15 years)']++;
        else if (ageYears >= 5) ageRanges['Mature (5-10 years)']++;
        else if (ageYears >= 2) ageRanges['Medium (2-5 years)']++;
        else if (ageYears >= 1) ageRanges['Young (1-2 years)']++;
        else ageRanges['New (< 1 year)']++;
      } catch (e) {
        // Skip invalid dates
      }
    });
    
    summary += `📅 **Domain Age Distribution:**\n`;
    Object.entries(ageRanges).forEach(([range, count]) => {
      if (count > 0) {
        summary += `• ${range}: ${count} domains\n`;
      }
    });
    summary += `\n`;
  }
  
  // Add expiration analysis
  const domainsWithExpirationDate = domainResults.filter(d => !d.error && d.expiration_date);
  if (domainsWithExpirationDate.length > 0) {
    const now = new Date();
    const expiringDomains = domainsWithExpirationDate.filter(domain => {
      try {
        const expires = new Date(domain.expiration_date);
        const daysUntilExpiration = (expires - now) / (1000 * 60 * 60 * 24);
        return daysUntilExpiration <= 90 && daysUntilExpiration > 0;
      } catch (e) {
        return false;
      }
    });
    
    if (expiringDomains.length > 0) {
      summary += `⚠️ **Domains expiring within 90 days:**\n`;
      expiringDomains.slice(0, 5).forEach(domain => {
        try {
          const expires = new Date(domain.expiration_date);
          const daysUntilExpiration = Math.ceil((expires - now) / (1000 * 60 * 60 * 24));
          summary += `• ${domain.domain}: ${daysUntilExpiration} days\n`;
        } catch (e) {
          summary += `• ${domain.domain}: Invalid expiration date\n`;
        }
      });
      if (expiringDomains.length > 5) {
        summary += `• ... and ${expiringDomains.length - 5} more\n`;
      }
      summary += `\n`;
    }
  }
  
  // Add registrar distribution (top 5)
  const registrarCounts = {};
  domainResults.filter(d => !d.error && d.registrar).forEach(domain => {
    const registrar = domain.registrar.trim();
    if (registrar) {
      registrarCounts[registrar] = (registrarCounts[registrar] || 0) + 1;
    }
  });
  
  const topRegistrars = Object.entries(registrarCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);
  
  if (topRegistrars.length > 0) {
    summary += `🏢 **Top Registrars:**\n`;
    topRegistrars.forEach(([registrar, count]) => {
      summary += `• ${registrar}: ${count} domains\n`;
    });
    summary += `\n`;
  }
  
  // Add failed domains if any
  const failedDomainsWithErrors = domainResults.filter(d => d.error).slice(0, 3);
  if (failedDomainsWithErrors.length > 0) {
    summary += `⚠️ **Failed domains (showing first 3):**\n`;
    failedDomainsWithErrors.forEach(domain => {
      summary += `• ${domain.domain}: ${domain.error}\n`;
    });
    summary += `\n`;
  }
  
  summary += `🔗 **API Layer calls made:** ${totalDomains - cachedDomains}\n`;
  summary += `⏰ **Processed at:** ${new Date().toISOString()}`;
  
  return summary;
}

/**
 * Manual WHOIS processing for specific domains (for testing/debugging)
 * @param {Array} domains - Array of domain strings to process
 */
export async function processWhoisForDomains(domains) {
  console.log(`🔧 Manual WHOIS processing for ${domains.length} domains`);
  
  const domainObjects = domains.map(domain => ({ domain }));
  
  const domainResults = await processBatches(domainObjects, async (domainObj) => {
    try {
      const whoisData = await fetchWhoisInfo(domainObj.domain);
      
      // Store WHOIS data in database if successful
      if (whoisData && !whoisData.error) {
        await storeWhoisInfo(domainObj.domain, whoisData);
      }
      
      return {
        domain: domainObj.domain,
        creation_date: whoisData.creation_date,
        expiration_date: whoisData.expiration_date,
        registrar: whoisData.registrar,
        timestamp: whoisData.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error fetching WHOIS for ${domainObj.domain}:`, error);
      return {
        domain: domainObj.domain,
        creation_date: null,
        expiration_date: null,
        registrar: '',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  });
  
  return domainResults;
}

/**
 * Get WHOIS info for a single domain (API endpoint)
 * @param {string} domain - Domain to process
 * @param {Object} kvNamespace - Optional KV namespace for quota management
 */
export async function getWhoisInfo(domain, kvNamespace = null) {
  console.log(`🔍 Getting WHOIS info for single domain: ${domain}`);
  
  // Initialize WHOIS service with quota management
  if (kvNamespace) {
    initWhoisService(kvNamespace);
  }
  
  try {
    const whoisData = await fetchWhoisInfo(domain);
    
    // Store WHOIS data in database if successful
    if (whoisData && !whoisData.error) {
      await storeWhoisInfo(domain, whoisData);
    }
    
    return whoisData;
  } catch (error) {
    console.error(`Error getting WHOIS for ${domain}:`, error);
    throw error;
  }
} 