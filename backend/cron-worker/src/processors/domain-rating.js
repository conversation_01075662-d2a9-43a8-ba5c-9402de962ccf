/**
 * Domain Rating processor for Ahrefs data (Updated for new schema)
 * Now focuses on domain-level statistics rather than individual links
 */

import { fetchAllDomains, updateDomainStats, updateProjectsStats, fetchProjects } from '../db.js';
import { fetchDomainRating, initDomainRatingService } from '../services/domain-rating.js';
import { sendMessage } from '../services/notification.js';
import { processBatches } from '../lib/utils.js';

/**
 * Fetch domain rating data from Ahrefs API for all domains
 */
export async function processDomainRatings(kvNamespace = null) {
  console.log('🚀 Starting quota-managed weekly domain rating collection from Ahrefs (new schema)');
  
  // Initialize domain rating service with quota management
  if (kvNamespace) {
    initDomainRatingService(kvNamespace);
  }
  
  try {
    // 1. Get all unique domains from projects, discovered_links, and link_resources
    const domains = await fetchAllDomains();
    console.log(`Found ${domains.length} unique domains to process for DR`);
    
    if (domains.length === 0) {
      console.log('No domains found to process');
      return;
    }
    
    // 2. Process domains in batches to get DR data
    const domainResults = await processBatches(domains, async (domainObj) => {
      try {
        const drData = await fetchDomainRating(domainObj.domain);
        return {
          domain: domainObj.domain,
          dr_score: drData.dr_score || 0,
          timestamp: drData.timestamp || new Date().toISOString(),
          error: drData.error
        };
      } catch (error) {
        console.error(`Error fetching DR for ${domainObj.domain}:`, error);
        return {
          domain: domainObj.domain,
          dr_score: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }, {
      batchSize: 3, // Smaller batches for Ahrefs API (more restrictive rate limits)
      delayBetweenBatches: 2000 // 2 second delay to respect rate limits
    });
    
    // 3. Update domain statistics in all_links table
    await updateDomainStats(domainResults, 'domain_rating');
    
    // 4. Update project statistics
    const projects = await fetchProjects();
    const projectIds = projects.map(p => p.id);
    if (projectIds.length > 0) {
      await updateProjectsStats(projectIds);
    }
    
    // 5. Send report via Feishu webhook
    const summary = generateDRSummary(domainResults);
    await sendMessage('🎯 Weekly Domain Rating Report (New Schema)', summary);
    
    console.log('✅ Domain rating collection completed successfully');
  } catch (error) {
    console.error('❌ Error in domain rating processing:', error);
    throw error;
  }
}

/**
 * Generate domain rating summary report
 */
function generateDRSummary(domainResults) {
  const totalDomains = domainResults.length;
  const successfulDomains = domainResults.filter(d => !d.error).length;
  const failedDomains = domainResults.filter(d => d.error).length;
  
  let summary = `🔄 **Domain Rating Collection Summary**\n\n`;
  summary += `📊 **Statistics:**\n`;
  summary += `• Total domains processed: ${totalDomains}\n`;
  summary += `• Successful: ${successfulDomains}\n`;
  summary += `• Failed: ${failedDomains}\n\n`;
  
  // Add top 5 domains by DR score
  const topDomains = [...domainResults]
    .filter(d => !d.error && d.dr_score > 0)
    .sort((a, b) => b.dr_score - a.dr_score)
    .slice(0, 5);
  
  if (topDomains.length > 0) {
    summary += `🏆 **Top domains by DR score:**\n`;
    topDomains.forEach((domain, i) => {
      summary += `${i + 1}. ${domain.domain}: DR ${domain.dr_score}\n`;
    });
    summary += `\n`;
  }
  
  // Add DR distribution
  const drRanges = {
    'High (DR 70+)': 0,
    'Good (DR 50-69)': 0,
    'Medium (DR 30-49)': 0,
    'Low (DR 10-29)': 0,
    'Very Low (DR 1-9)': 0,
    'No DR (0)': 0
  };
  
  domainResults.filter(d => !d.error).forEach(domain => {
    const dr = domain.dr_score;
    if (dr >= 70) drRanges['High (DR 70+)']++;
    else if (dr >= 50) drRanges['Good (DR 50-69)']++;
    else if (dr >= 30) drRanges['Medium (DR 30-49)']++;
    else if (dr >= 10) drRanges['Low (DR 10-29)']++;
    else if (dr >= 1) drRanges['Very Low (DR 1-9)']++;
    else drRanges['No DR (0)']++;
  });
  
  summary += `📈 **DR Distribution:**\n`;
  Object.entries(drRanges).forEach(([range, count]) => {
    if (count > 0) {
      summary += `• ${range}: ${count} domains\n`;
    }
  });
  summary += `\n`;
  
  // Add failed domains if any
  const failedDomainsWithErrors = domainResults.filter(d => d.error).slice(0, 3);
  if (failedDomainsWithErrors.length > 0) {
    summary += `⚠️ **Failed domains (showing first 3):**\n`;
    failedDomainsWithErrors.forEach(domain => {
      summary += `• ${domain.domain}: ${domain.error}\n`;
    });
    summary += `\n`;
  }
  
  // Add average DR
  const validDRs = domainResults
    .filter(d => !d.error && d.dr_score > 0)
    .map(d => d.dr_score);
  
  if (validDRs.length > 0) {
    const avgDR = Math.round(validDRs.reduce((sum, dr) => sum + dr, 0) / validDRs.length);
    summary += `📊 **Average DR score:** ${avgDR}\n`;
  }
  
  summary += `🔗 **Ahrefs API calls made:** ${totalDomains}\n`;
  summary += `⏰ **Processed at:** ${new Date().toISOString()}`;
  
  return summary;
}

/**
 * Manual DR processing for specific domains (for testing/debugging)
 * @param {Array} domains - Array of domain strings to process
 */
export async function processDRForDomains(domains) {
  console.log(`🔧 Manual DR processing for ${domains.length} domains`);
  
  const domainObjects = domains.map(domain => ({ domain }));
  
  const domainResults = await processBatches(domainObjects, async (domainObj) => {
    try {
      const drData = await fetchDomainRating(domainObj.domain);
      return {
        domain: domainObj.domain,
        dr_score: drData.dr_score || 0,
        timestamp: drData.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error fetching DR for ${domainObj.domain}:`, error);
      return {
        domain: domainObj.domain,
        dr_score: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  });
  
  await updateDomainStats(domainResults, 'domain_rating');
  
  return domainResults;
}

/**
 * Get domain rating for a single domain (API endpoint)
 * @param {string} domain - Domain to process
 * @param {Object} kvNamespace - Optional KV namespace for quota management
 */
export async function getDomainRating(domain, kvNamespace = null) {
  console.log(`🔍 Getting DR for single domain: ${domain}`);
  
  // Initialize domain rating service with quota management
  if (kvNamespace) {
    initDomainRatingService(kvNamespace);
  }
  
  try {
    const drData = await fetchDomainRating(domain);
    const result = {
      domain: domain,
      dr_score: drData.dr_score || 0,
      timestamp: drData.timestamp || new Date().toISOString(),
      error: drData.error
    };
    
    // Update domain stats in database
    await updateDomainStats([result], 'domain_rating');
    
    return result;
  } catch (error) {
    console.error(`Error getting DR for ${domain}:`, error);
    return {
      domain: domain,
      dr_score: 0,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}