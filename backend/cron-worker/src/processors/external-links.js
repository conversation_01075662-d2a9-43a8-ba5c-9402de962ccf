/**
 * External links processor for Google Search Console data (Updated for new schema)
 * Now stores discovered links per project and updates domain stats
 */

import { 
  fetchProjects, 
  updateDomainStats, 
  updateProjectsStats, 
  storeDiscoveredLinks,
  getProjectByDomain 
} from '../db.js';
import { fetchExternalLinks, initExternalLinksService } from '../services/external-links.js';
import { sendMessage } from '../services/notification.js';
import { processBatches } from '../lib/utils.js';

/**
 * Fetch external links data from Google Search Console for all project domains
 */
export async function processExternalLinks(kvNamespace = null) {
  console.log('🚀 Starting quota-managed weekly external links collection from Google Search Console (new schema)');
  
  // Initialize external links service with quota management
  if (kvNamespace) {
    initExternalLinksService(kvNamespace);
  }
  
  try {
    // 1. Get all projects (since we need to associate discovered links with projects)
    const projects = await fetchProjects();
    console.log(`Found ${projects.length} projects to process for external links`);
    
    if (projects.length === 0) {
      console.log('No projects found to process');
      return;
    }
    
    // 2. Process projects in batches to get external links data
    const projectResults = await processBatches(projects, async (project) => {
      try {
        const linksData = await fetchExternalLinks(project.domain);
        return {
          project_id: project.id,
          user_id: project.user_id,
          domain: project.domain,
          discovered_links: linksData.discovered_links || [],
          total_links: linksData.external_links || 0,
          timestamp: linksData.timestamp || new Date().toISOString(),
          error: linksData.error
        };
      } catch (error) {
        console.error(`Error fetching external links for ${project.domain}:`, error);
        return {
          project_id: project.id,
          user_id: project.user_id,
          domain: project.domain,
          discovered_links: [],
          total_links: 0,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }, {
      batchSize: 2, // Smaller batches for Google API (rate limits)
      delayBetweenBatches: 3000 // 3 second delay to respect rate limits
    });
    
    // 3. Store discovered links for each project
    const discoveredLinksResults = await Promise.all(
      projectResults.map(async (result) => {
        if (result.error || !result.discovered_links || result.discovered_links.length === 0) {
          return { project_id: result.project_id, stored: 0, error: result.error };
        }
        
        const success = await storeDiscoveredLinks(
          result.project_id,
          result.user_id,
          result.discovered_links,
          result.domain
        );
        
        return {
          project_id: result.project_id,
          domain: result.domain,
          stored: success ? result.discovered_links.length : 0,
          error: success ? null : 'Failed to store discovered links'
        };
      })
    );
    
    // 4. Update project statistics (total_links and indexed_links counts)
    const projectIds = projects.map(p => p.id);
    await updateProjectsStats(projectIds);
    
    // 5. Extract unique domains from discovered links and update domain stats
    const uniqueDomains = new Set();
    projectResults.forEach(result => {
      if (!result.error && result.discovered_links) {
        result.discovered_links.forEach(link => {
          try {
            const url = new URL(link.url);
            uniqueDomains.add(url.hostname);
          } catch (e) {
            // Skip invalid URLs
          }
        });
      }
    });
    
    // Update domain stats for newly discovered domains (set basic stats)
    if (uniqueDomains.size > 0) {
      const domainResults = Array.from(uniqueDomains).map(domain => ({
        domain,
        traffic: 0, // Will be updated by traffic processor
        dr_score: null, // Will be updated by DR processor
        is_indexed: false, // Will be updated by indexing processor
        timestamp: new Date().toISOString()
      }));
      
      await updateDomainStats(domainResults, 'external_links');
    }
    
    // 6. Send report via Feishu webhook
    const summary = generateExternalLinksSummary(projectResults, discoveredLinksResults);
    await sendMessage('🔗 Weekly External Links Report (New Schema)', summary);
    
    console.log('✅ External links collection completed successfully');
  } catch (error) {
    console.error('❌ Error in external links processing:', error);
    throw error;
  }
}

/**
 * Generate external links summary report
 */
function generateExternalLinksSummary(projectResults, discoveredLinksResults) {
  const totalProjects = projectResults.length;
  const successfulProjects = projectResults.filter(p => !p.error).length;
  const failedProjects = projectResults.filter(p => p.error).length;
  
  let summary = `🔄 **External Links Collection Summary**\n\n`;
  summary += `📊 **Statistics:**\n`;
  summary += `• Total projects processed: ${totalProjects}\n`;
  summary += `• Successful: ${successfulProjects}\n`;
  summary += `• Failed: ${failedProjects}\n\n`;
  
  // Calculate total discovered links
  const totalDiscoveredLinks = discoveredLinksResults.reduce((sum, result) => sum + result.stored, 0);
  const projectsWithLinks = discoveredLinksResults.filter(result => result.stored > 0).length;
  
  summary += `🔗 **Discovered Links:**\n`;
  summary += `• Total new links discovered: ${totalDiscoveredLinks}\n`;
  summary += `• Projects with new links: ${projectsWithLinks}\n\n`;
  
  // Add top 5 projects by discovered links
  const topProjects = [...discoveredLinksResults]
    .filter(p => p.stored > 0)
    .sort((a, b) => b.stored - a.stored)
    .slice(0, 5);
  
  if (topProjects.length > 0) {
    summary += `🏆 **Top projects by new links discovered:**\n`;
    topProjects.forEach((project, i) => {
      summary += `${i + 1}. ${project.domain}: ${project.stored} links\n`;
    });
    summary += `\n`;
  }
  
  // Add project external links summary
  const projectsWithTotalLinks = projectResults
    .filter(p => !p.error && p.total_links > 0)
    .sort((a, b) => b.total_links - a.total_links)
    .slice(0, 5);
  
  if (projectsWithTotalLinks.length > 0) {
    summary += `📈 **Top projects by total external links:**\n`;
    projectsWithTotalLinks.forEach((project, i) => {
      summary += `${i + 1}. ${project.domain}: ${project.total_links} total links\n`;
    });
    summary += `\n`;
  }
  
  // Add failed projects if any
  const failedProjectsWithErrors = projectResults.filter(p => p.error).slice(0, 3);
  if (failedProjectsWithErrors.length > 0) {
    summary += `⚠️ **Failed projects (showing first 3):**\n`;
    failedProjectsWithErrors.forEach(project => {
      summary += `• ${project.domain}: ${project.error}\n`;
    });
    summary += `\n`;
  }
  
  // Calculate totals
  const totalExternalLinks = projectResults
    .filter(p => !p.error)
    .reduce((sum, p) => sum + p.total_links, 0);
  
  summary += `📊 **Totals:**\n`;
  summary += `• Total external links across all projects: ${totalExternalLinks.toLocaleString()}\n`;
  summary += `• New links stored in database: ${totalDiscoveredLinks.toLocaleString()}\n`;
  summary += `• Google Search Console API calls: ${totalProjects}\n`;
  summary += `⏰ **Processed at:** ${new Date().toISOString()}`;
  
  return summary;
}

/**
 * Manual external links processing for specific projects (for testing/debugging)
 * @param {Array} projectIds - Array of project IDs to process
 */
export async function processExternalLinksForProjects(projectIds) {
  console.log(`🔧 Manual external links processing for ${projectIds.length} projects`);
  
  // Get project details
  const projects = await fetchProjects();
  const selectedProjects = projects.filter(p => projectIds.includes(p.id));
  
  if (selectedProjects.length === 0) {
    throw new Error('No matching projects found');
  }
  
  const projectResults = await processBatches(selectedProjects, async (project) => {
    try {
      const linksData = await fetchExternalLinks(project.domain);
      return {
        project_id: project.id,
        user_id: project.user_id,
        domain: project.domain,
        discovered_links: linksData.discovered_links || [],
        total_links: linksData.external_links || 0,
        timestamp: linksData.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error fetching external links for ${project.domain}:`, error);
      return {
        project_id: project.id,
        user_id: project.user_id,
        domain: project.domain,
        discovered_links: [],
        total_links: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  });
  
  // Store discovered links
  const discoveredLinksResults = await Promise.all(
    projectResults.map(async (result) => {
      if (result.error || !result.discovered_links || result.discovered_links.length === 0) {
        return { project_id: result.project_id, stored: 0, error: result.error };
      }
      
      const success = await storeDiscoveredLinks(
        result.project_id,
        result.user_id,
        result.discovered_links,
        result.domain
      );
      
      return {
        project_id: result.project_id,
        stored: success ? result.discovered_links.length : 0,
        error: success ? null : 'Failed to store discovered links'
      };
    })
  );
  
  // Update project stats
  await updateProjectsStats(projectIds);
  
  return { projectResults, discoveredLinksResults };
}

/**
 * Get external links for a single domain (API endpoint)
 * @param {string} domain - Domain to process
 * @param {Object} kvNamespace - Optional KV namespace for quota management
 */
export async function getExternalLinks(domain, kvNamespace = null) {
  console.log(`🔍 Getting external links for single domain: ${domain}`);
  
  // Initialize external links service with quota management
  if (kvNamespace) {
    initExternalLinksService(kvNamespace);
  }
  
  try {
    const linksData = await fetchExternalLinks(domain);
    const result = {
      domain: domain,
      discovered_links: linksData.discovered_links || [],
      total_links: linksData.external_links || 0,
      timestamp: linksData.timestamp || new Date().toISOString(),
      error: linksData.error
    };
    
    // Get or create a temporary project for this domain (for API usage)
    const project = await getProjectByDomain(domain);
    if (project && result.discovered_links && result.discovered_links.length > 0) {
      // Store discovered links if we have a project
      await storeDiscoveredLinks(
        project.id,
        project.user_id,
        result.discovered_links,
        domain
      );
      
      // Update project stats
      await updateProjectsStats([project.id]);
    }
    
    return result;
  } catch (error) {
    console.error(`Error getting external links for ${domain}:`, error);
    return {
      domain: domain,
      discovered_links: [],
      total_links: 0,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}