/**
 * LinkTrackPro Cron Worker (Updated for New Schema)
 * 
 * This worker handles scheduled tasks with the new database schema:
 * - Daily: Traffic information (SimilarWeb API) → all_links table
 * - Weekly: Domain Rating (Ahrefs API) → all_links table  
 * - Weekly: External link discovery (Google Search Console) → discovered_links table
 * - Weekly: Indexing status (Google Search API) → all_links table
 * 
 * Each task sends results via Feishu webhook upon completion
 * Schema changes:
 * - Centralized domain stats in all_links table
 * - Historical tracking in all_links_history table
 * - Link resources decoupled from projects
 * - Project stats calculated dynamically
 */

import { initSupabase, cleanupOldHistoryData } from './db.js';
import { loadConfig } from './lib/config.js';
import { isAuthenticated } from './lib/auth.js';
import { sendAlert } from './services/notification.js';
import { processTrafficData, getTrafficData } from './processors/traffic.js';
import { processDomainRatings, getDomainRating } from './processors/domain-rating.js';
import { processExternalLinks, getExternalLinks } from './processors/external-links.js';
import { processIndexingStatus, getIndexingStatus } from './processors/indexing.js';
import { processWhoisInfo, getWhoisInfo } from './processors/whois.js';
import { withApiLogging, extractProcessingMetrics, trackExternalApiCall } from './lib/api-middleware.js';
import { initializeD1Database, getUsageStats, getRecentLogs } from './lib/d1-logger.js';
import {
  healthHandler,
  triggerTrafficHandler,
  triggerDomainRatingHandler,
  triggerLinksHandler,
  triggerIndexingHandler,
  triggerWhoisHandler,
  triggerCleanupHandler,
  triggerAllHandler,
  apiDomainRatingHandler,
  apiExternalLinksHandler,
  apiTrafficHandler,
  apiIndexingHandler,
  apiWhoisHandler,
  apiRdapTestHandler,
  apiRdapStatsHandler,
  apiWhoisTestHandler,
  apiForceRdapHandler,
  apiForceWhoisHandler,
  apiStatsHandler,
  apiLogsWebHandler,
  apiQuotaStatusHandler,
  apiQuotaUpdateHandler,
  apiQuotaResetHandler,
  apiQuotaWebHandler
} from './handlers/api-handlers.js';

// Scheduled task handler
export default {
  // Daily and weekly scheduled tasks
  async scheduled(event, env, ctx) {
    // Load configuration from environment variables
    loadConfig(env);
    
    // Initialize database client
    initSupabase(env.SUPABASE_URL, env.SUPABASE_KEY, env.SUPABASE_SCHEMA);
    
    const date = new Date(event.scheduledTime);
    const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, ...
    
    console.log(`🕐 Running scheduled task at ${date.toISOString()}`);
    console.log(`📅 Day of week: ${dayOfWeek} (0=Sunday, 6=Saturday)`);
    
    try {
      // Daily task - Traffic data collection with quota management
      console.log('🚀 Starting daily tasks...');
      await processTrafficData(env.QUOTA_KV);
      console.log('✅ Daily traffic data collection completed');
      
      // Weekly tasks - Run on Sunday (day 0)
      if (dayOfWeek === 0) {
        console.log('🚀 Starting weekly tasks (Sunday)...');
        
        // Domain Rating collection with quota management
        await processDomainRatings(env.QUOTA_KV);
        console.log('✅ Weekly domain rating collection completed');
        
        // External links discovery with quota management
        await processExternalLinks(env.QUOTA_KV);
        console.log('✅ Weekly external links discovery completed');
        
        // Indexing status check with quota management
        await processIndexingStatus(env.QUOTA_KV);
        console.log('✅ Weekly indexing status check completed');
        
        // WHOIS information collection with quota management
        await processWhoisInfo(env.QUOTA_KV);
        console.log('✅ Weekly WHOIS information collection completed');
        
        // Cleanup old historical data (keep last 90 days)
        await cleanupOldHistoryData();
        console.log('✅ Historical data cleanup completed');
        
        console.log('🎉 All weekly tasks completed successfully');
      } else {
        console.log(`⏭️ Skipping weekly tasks (runs on Sunday, today is day ${dayOfWeek})`);
      }
      
      console.log('🎉 All scheduled tasks completed successfully');
    } catch (error) {
      console.error('❌ Error in scheduled tasks:', error);
      await sendAlert(`❌ **Scheduled Task Error**\n\nError: ${error.message}\nTime: ${date.toISOString()}\nDay: ${dayOfWeek}`);
      throw error; // Re-throw to mark the scheduled event as failed
    }
  },
  
  // Handle fetch requests (for manual triggering or API endpoints)
  async fetch(request, env, ctx) {
    // Load configuration from environment variables
    loadConfig(env);
    
    // Initialize database client
    initSupabase(env.SUPABASE_URL, env.SUPABASE_KEY, env.SUPABASE_SCHEMA);
    
    // Initialize D1 database for API logging
    if (env.DB && !env._d1_initialized) {
      await initializeD1Database(env);
      env._d1_initialized = true;
    }
    
    const url = new URL(request.url);
    const path = url.pathname;
    
    // Basic authentication for manual triggers (skip in development)
    const isDevelopment = env.ENVIRONMENT === 'development' || 
                         env.NODE_ENV === 'development' ||
                         url.hostname === 'localhost' ||
                         url.hostname.includes('127.0.0.1') ||
                         url.hostname.includes('.local');
    
    if (!isDevelopment && !isAuthenticated(request)) {
      return new Response('🔒 Unauthorized', { 
        status: 401,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }
    
    const startTime = Date.now();
    
    try {
      // Health check endpoint
      if (path === '/' || path === '/health') {
        return await healthHandler(request, env, ctx);
      }
      
      // Manual trigger endpoints  
      if (path === '/trigger/traffic') {
        return await triggerTrafficHandler(request, env, ctx);
        
      } else if (path === '/trigger/dr') {
        return await triggerDomainRatingHandler(request, env, ctx);
        
      } else if (path === '/trigger/links') {
        return await triggerLinksHandler(request, env, ctx);
        
      } else if (path === '/trigger/indexing') {
        return await triggerIndexingHandler(request, env, ctx);
        
      } else if (path === '/trigger/whois') {
        return await triggerWhoisHandler(request, env, ctx);
        
      } else if (path === '/trigger/cleanup') {
        return await triggerCleanupHandler(request, env, ctx);
        
      } else if (path === '/trigger/all') {
        return await triggerAllHandler(request, env, ctx);
        
      } 
      
      // API endpoints for single domain queries
      else if (path === '/api/domain-rating') {
        return await apiDomainRatingHandler(request, env, ctx);
        
      } else if (path === '/api/external-links') {
        return await apiExternalLinksHandler(request, env, ctx);
        
      } else if (path === '/api/traffic') {
        return await apiTrafficHandler(request, env, ctx);
        
      } else if (path === '/api/indexing') {
        return await apiIndexingHandler(request, env, ctx);
        
      } else if (path === '/api/whois') {
        return await apiWhoisHandler(request, env, ctx);
        
      } else if (path === '/api/stats') {
        return await apiStatsHandler(request, env, ctx);
        
      } else if (path === '/logs') {
        return await apiLogsWebHandler(request, env, ctx);
        
      } 
      
      // RapidAPI quota management endpoints
      else if (path === '/api/quota/status') {
        return await apiQuotaStatusHandler(request, env, ctx);
        
      } else if (path === '/api/quota/update') {
        return await apiQuotaUpdateHandler(request, env, ctx);
        
      } else if (path === '/api/quota/reset') {
        return await apiQuotaResetHandler(request, env, ctx);
        
      } else if (path === '/quota') {
        return await apiQuotaWebHandler(request, env, ctx);
        
      } 
      
      // RDAP (Registration Data Access Protocol) endpoints
      else if (path === '/api/rdap/test') {
        return await apiRdapTestHandler(request, env, ctx);
        
      } else if (path === '/api/rdap/stats') {
        return await apiRdapStatsHandler(request, env, ctx);
        
      } else if (path === '/api/whois/test') {
        return await apiWhoisTestHandler(request, env, ctx);
        
      } else if (path === '/api/rdap/force') {
        return await apiForceRdapHandler(request, env, ctx);
        
      } else if (path === '/api/whois/force') {
        return await apiForceWhoisHandler(request, env, ctx);
        
      } else {
        // Return available endpoints
        const endpoints = [
          '🏠 / or /health - Health check',
          '',
          '📋 **Batch Processing Endpoints:**',
          '📈 /trigger/traffic - Manual traffic data collection',
          '🎯 /trigger/dr - Manual domain rating collection', 
          '🔗 /trigger/links - Manual external links collection',
          '🔍 /trigger/indexing - Manual indexing status check',
          '🔍 /trigger/whois - Manual WHOIS information collection',
          '🧹 /trigger/cleanup - Manual historical data cleanup',
          '🚀 /trigger/all - Run all processors',
          '',
          '🔌 **API Endpoints:**',
          '🎯 /api/domain-rating?domain=example.com - Get domain rating for specific domain',
          '🔗 /api/external-links?domain=example.com - Get external links for specific domain',
          '📈 /api/traffic?domain=example.com - Get traffic data for specific domain',
          '🔍 /api/indexing?domain=example.com - Get indexing status for specific domain',
          '🔍 /api/whois?domain=example.com - Get WHOIS/RDAP information for specific domain',
          '📊 /api/stats?endpoint=&startDate=&endDate=&granularity=hour - Get API usage statistics and logs',
          '',
          '🌐 **RDAP (Registration Data Access Protocol) Endpoints:**',
          '🧪 /api/rdap/test?domain=example.com - Test RDAP connectivity for specific domain',
          '📊 /api/rdap/stats - Get RDAP server statistics and bootstrap information',
          '🔬 /api/whois/test?domain=example.com - Test both RDAP and traditional WHOIS connectivity',
          '⚡ /api/rdap/force?domain=example.com - Force RDAP query (skip cache and fallback)',
          '⚡ /api/whois/force?domain=example.com&mode=cost_first - Force traditional WHOIS API query',
          '',
          '📊 **RapidAPI Quota Management:**',
          '📈 /api/quota/status - Get quota status for all RapidAPI endpoints',
          '⚙️ /api/quota/update - Update quota configuration (POST)',
          '🔄 /api/quota/reset - Reset quota usage (POST)',
          '',
          '🌐 **Web Interface:**',
          '📋 /logs - API Logs Dashboard (requires AUTH_TOKEN)',
          '📊 /quota - RapidAPI Quota Management Dashboard (requires AUTH_TOKEN)'
        ];
        
        return new Response(`🤖 LinkTrackPro Cron Worker (New Schema) with D1 API Logging & RDAP Support\n\nAvailable endpoints:\n${endpoints.join('\n')}`, {
          status: 404,
          headers: { 'Content-Type': 'text/plain; charset=utf-8' }
        });
      }
    } catch (error) {
      console.error(`❌ Error handling request to ${path}:`, error);
      const duration = Date.now() - startTime;
      
      await sendAlert(`❌ **Manual Trigger Error**\n\nEndpoint: ${path}\nError: ${error.message}\nDuration: ${duration}ms\nTime: ${new Date().toISOString()}`);
      
      return new Response(`❌ Error: ${error.message}\nDuration: ${duration}ms`, { 
        status: 500,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }
  }
};