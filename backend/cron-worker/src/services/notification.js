/**
 * Notification service - 通知服务
 * 整合不同平台的通知功能
 */

import { config } from '../lib/config.js';

/**
 * 发送消息到Feishu webhook (基础版本)
 */
export async function sendMessage(title, content) {
  if (!config.FEISHU_WEBHOOK_URL) {
    console.warn('Feishu webhook URL not configured, skipping notification');
    return;
  }
  
  const message = {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title: title,
          content: [
            [
              {
                tag: 'text',
                text: content
              }
            ]
          ]
        }
      }
    }
  };
  
  try {
    const response = await fetch(config.FEISHU_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(message)
    });
    
    if (!response.ok) {
      throw new Error(`Feishu webhook error: ${response.status} ${response.statusText}`);
    }
    
    console.log('Feishu webhook notification sent successfully');
  } catch (error) {
    console.error('Failed to send Feishu webhook notification:', error);
  }
}

/**
 * 发送警报消息到Feishu webhook
 */
export async function sendAlert(message) {
  await sendMessage('⚠️ LinkTrackPro Alert', message);
}

/**
 * 发送飞书消息 (支持交互式卡片)
 */
export async function sendFeishuMessage(webhookUrl, payload) {
  if (!webhookUrl) {
    console.warn('Feishu webhook URL not provided, skipping notification');
    return;
  }
  
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`Feishu webhook error: ${response.status} ${response.statusText}`);
    }
    
    console.log('Feishu interactive message sent successfully');
  } catch (error) {
    console.error('Failed to send Feishu interactive message:', error);
    throw error;
  }
}

/**
 * 创建通知处理器
 */
function createNotificationHandler(site, result) {
  return {
    getStatusText() {
      const statusIcon = result.status === 'success' ? '✅' : 
                        result.status === 'warning' ? '⚠️' : '❌';
      const responseTime = result.responseTime ? `(${result.responseTime}ms)` : '';
      const errorInfo = result.error ? ` - ${result.error}` : '';
      
      return `${statusIcon} **${site.name || site.url}** ${responseTime}${errorInfo}`;
    },
    
    async getSpecialAlertContent() {
      if (result.status === 'error' && result.error) {
        return `🚨 **${site.name || site.url}**: ${result.error}`;
      }
      
      if (result.status === 'warning' && result.warning) {
        return `⚠️ **${site.name || site.url}**: ${result.warning}`;
      }
      
      return null;
    }
  };
}

/**
 * 发送统一监控报告
 */
export async function sendUnifiedMonitoringReport(webhookUrl, results, hasNewFailures = [], failedSites = []) {
  const successCount = results.filter(r => r.status === 'success').length;
  const warningCount = results.filter(r => r.status === 'warning').length;
  const errorCount = results.filter(r => r.status === 'error').length;
  const totalCount = results.length;
  
  // 计算平均响应时间
  const avgResponseTime = Math.round(
    results.reduce((sum, r) => sum + (r.responseTime || 0), 0) / totalCount
  );
  
  // 确定总体状态和颜色
  let overallStatus = '✅ 全部正常';
  let headerColor = 'green';
  let headerTitle = '🟢 系统监控报告';
  
  if (errorCount > 0) {
    overallStatus = '❌ 有异常';
    headerColor = 'red';
    headerTitle = hasNewFailures.length > 0 ? '🚨 紧急监控告警' : '🔴 系统异常报告';
  } else if (warningCount > 0) {
    overallStatus = '⚠️ 有警告';
    headerColor = 'orange';
    headerTitle = '🟡 系统警告报告';
  }
  
  // 构建消息内容
  const elements = [];
  
  // 如果有新失败，添加紧急告警部分
  if (hasNewFailures.length > 0) {
    const urgentFailures = hasNewFailures.map(failure => {
      const statusIcon = failure.status === 'warning' ? '⚠️' : '🚨';
      return `${statusIcon} **${failure.site.name || failure.site.url}** - ${failure.error || '状态异常'}`;
    }).join('\n');
    
    elements.push({
      tag: "div",
      text: {
        content: `🚨 **紧急告警**\n\n${urgentFailures}`,
        tag: "lark_md"
      }
    });
    
    elements.push({ tag: "hr" });
  }
  
  // 添加汇总报告部分
  elements.push({
    tag: "div",
    text: {
      content: `**LinkTrackPro 系统检查报告**\n\n📊 **总体状态**: ${overallStatus}\n📈 **统计信息**: ${successCount}正常 / ${warningCount}警告 / ${errorCount}异常 (共${totalCount}个)\n⏱️ **平均响应**: ${avgResponseTime}ms`,
      tag: "lark_md"
    }
  });
  
  // 添加详细检查结果
  if (results.length > 0) {
    const siteStatusList = results.map(result => {
      const site = result.site || { name: result.name, url: result.url };
      const handler = createNotificationHandler(site, result);
      return handler.getStatusText();
    }).join('\n');
    
    elements.push({
      tag: "div",
      text: {
        content: `\n**检查结果**:\n${siteStatusList}`,
        tag: "lark_md"
      }
    });
  }
  
  // 收集所有特殊警报内容
  const specialAlerts = [];
  for (const result of failedSites) {
    const site = result.site || { name: result.name, url: result.url };
    const handler = createNotificationHandler(site, result);
    const specialAlert = await handler.getSpecialAlertContent();
    if (specialAlert) {
      specialAlerts.push(specialAlert);
    }
  }
  
  // 如果有特殊警报，添加到消息中
  if (specialAlerts.length > 0) {
    elements.push({ tag: "hr" });
    elements.push({
      tag: "div",
      text: {
        content: `**特殊提醒**:\n\n${specialAlerts.join('\n\n')}`,
        tag: "lark_md"
      }
    });
  }
  
  // 添加时间信息
  elements.push({ tag: "hr" });
  elements.push({
    tag: "note",
    elements: [
      {
        tag: "plain_text",
        content: `检查时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })} | LinkTrackPro 系统监控`
      }
    ]
  });
  
  const payload = {
    msg_type: "interactive",
    card: {
      elements: elements,
      header: {
        template: headerColor,
        title: {
          content: headerTitle,
          tag: "plain_text"
        }
      }
    }
  };

  await sendFeishuMessage(webhookUrl, payload);
}

/**
 * 发送简单状态报告
 */
export async function sendStatusReport(title, status, details = []) {
  const webhookUrl = config.FEISHU_WEBHOOK_URL;
  if (!webhookUrl) {
    console.warn('Feishu webhook URL not configured, skipping status report');
    return;
  }

  const statusIcon = status === 'success' ? '✅' : 
                    status === 'warning' ? '⚠️' : '❌';
  const headerColor = status === 'success' ? 'green' : 
                     status === 'warning' ? 'orange' : 'red';

  const elements = [
    {
      tag: "div",
      text: {
        content: `${statusIcon} **${title}**`,
        tag: "lark_md"
      }
    }
  ];

  if (details.length > 0) {
    elements.push({
      tag: "div",
      text: {
        content: `\n**详情**:\n${details.join('\n')}`,
        tag: "lark_md"
      }
    });
  }

  elements.push({
    tag: "note",
    elements: [
      {
        tag: "plain_text",
        content: `时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`
      }
    ]
  });

  const payload = {
    msg_type: "interactive",
    card: {
      elements: elements,
      header: {
        template: headerColor,
        title: {
          content: `LinkTrackPro ${title}`,
          tag: "plain_text"
        }
      }
    }
  };

  await sendFeishuMessage(webhookUrl, payload);
} 