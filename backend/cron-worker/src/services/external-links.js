/**
 * External Links service - 外部链接服务
 * 专注于业务逻辑，API选择交给智能quota-manager处理
 */

import { getCachedDiscoveredLinks } from '../db.js';
import { quotaManager } from '../lib/quota-manager.js';

/**
 * Initialize external links service with KV namespace for quota management
 */
export function initExternalLinksService(kvNamespace) {
  if (kvNamespace) {
    quotaManager.init(kvNamespace);
  }
}

/**
 * 获取域名的外部链接 - 使用智能API选择
 * quota-manager根据配额、优先级和优化模式自动选择最佳API
 */
export async function fetchExternalLinks(domain, optimizationMode = 'cost_first') {
  console.log(`🔗 Fetching external links for domain: ${domain} (mode: ${optimizationMode})`);
  
  // Step 1: 检查缓存
  const cachedData = await getCachedDiscoveredLinks(domain);
  if (cachedData) {
    console.log(`✅ Using cached external links data for ${domain}`);
    return {
      ...cachedData,
      cached: true
    };
  }

  console.log(`🤖 No cache found, using intelligent API selection for ${domain}`);
  
  try {
    // 使用智能API选择系统
    const result = await quotaManager.executeWithIntelligentSelection(
      'external_links',
      [domain],
      optimizationMode
    );

    if (result.success) {
      console.log(`✅ External links retrieved via ${result.api_selection.selected_api}`);
      
      // 标准化返回格式，适配不同API的数据结构
      const linksData = normalizeExternalLinksData(result.data, domain, result.api_selection.selected_api);
      
      return {
        ...linksData,
        timestamp: result.timestamp,
        source: result.api_selection.selected_api,
        quota_used: result.api_info?.quota_used || true,
        api_selection: result.api_selection
      };
    } else {
      console.log(`⚠️ All external links APIs failed, using fallback strategy: ${result.fallback_strategy}`);
      return handleExternalLinksFallback(domain, result);
    }
  } catch (error) {
    console.error(`❌ Error in intelligent external links fetch for ${domain}:`, error.message);
    return handleExternalLinksFallback(domain, {
      fallback_strategy: 'empty_results',
      error: error.message
    });
  }
}

/**
 * 标准化不同API返回的外部链接数据格式
 */
function normalizeExternalLinksData(apiData, domain, apiSource = 'unknown') {
  const baseData = {
    domain,
    external_links: 0,
    discovered_links: []
  };

  // 适配Google Custom Search格式
  if (apiData.total_results !== undefined && apiData.links) {
    return {
      ...baseData,
      external_links: apiData.total_results || 0,
      discovered_links: (apiData.links || []).map(link => ({
        title: link.title || 'External Link',
        url: link.url || link.link,
        snippet: link.snippet || '',
        source: 'google_search'
      }))
    };
  }

  // 适配Ahrefs/SEO Traffic Authority RapidAPI格式
  if (apiData.external_links !== undefined || apiData.refdomains !== undefined) {
    // 确定API源
    const sourceLabel = apiSource.includes('seo-traffic-authority') ? 'seo_traffic_authority' : 'ahrefs_api';

    return {
      ...baseData,
      external_links: apiData.external_links || 0,
      refdomains: apiData.refdomains || 0,
      dofollow_backlinks: apiData.dofollow_backlinks || 0,
      domain_rating: apiData.domain_rating || 0,
      url_rating: apiData.url_rating || 0,
      discovered_links: (apiData.discovered_links || []).map(link => ({
        title: link.title || link.anchor || 'Backlink',
        url: link.url || link.urlFrom,
        target_url: link.target_url || link.urlTo,
        domain_rating: link.domain_rating || 0,
        anchor: link.anchor || '',
        is_dofollow: link.is_dofollow !== false,
        source: sourceLabel
      }))
    };
  }

  // 默认格式
  return baseData;
}

/**
 * 处理外部链接获取失败的回退策略
 */
function handleExternalLinksFallback(domain, failureInfo) {
  console.log(`🔄 Applying external links fallback strategy: ${failureInfo.fallback_strategy}`);
  
  const baseResult = {
    domain,
    timestamp: new Date().toISOString(),
    quota_used: false,
    fallback_reason: failureInfo.reason || 'api_unavailable'
  };

  switch (failureInfo.fallback_strategy) {
    case 'empty_results':
      return {
        ...baseResult,
        external_links: 0,
        discovered_links: [],
        refdomains: 0,
        source: 'fallback_empty'
      };
      
    default:
      return {
        ...baseResult,
        external_links: 0,
        discovered_links: [],
        source: 'fallback_unknown',
        error: failureInfo.error
      };
  }
}

/**
 * 批量获取多个域名的外部链接（优化配额使用）
 */
export async function fetchBatchExternalLinks(domains, optimizationMode = 'quota_preserve') {
  console.log(`🔗 Batch fetching external links for ${domains.length} domains`);
  
  const results = [];
  
  for (const domain of domains) {
    try {
      const result = await fetchExternalLinks(domain, optimizationMode);
      results.push(result);
      
      // 批量处理时添加小延迟避免频率限制
      if (domains.length > 5) {
        await new Promise(resolve => setTimeout(resolve, 400));
      }
    } catch (error) {
      console.error(`Error fetching external links for ${domain}:`, error.message);
      results.push(handleExternalLinksFallback(domain, {
        fallback_strategy: 'empty_results',
        error: error.message
      }));
    }
  }
  
  return results;
}


/**
 * 获取外部链接数据收集的API状态分析
 */
export async function getExternalLinksApiAnalytics() {
  try {
    const analytics = await quotaManager.getApiAnalytics('external_links');
    return {
      success: true,
      data: analytics,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 从URL提取规范化域名 - 与前端normalizeUrl函数保持一致
 */
function extractDomainFromUrl(url) {
  try {
    if (!url || typeof url !== 'string') {
      return 'unknown';
    }
    
    // Handle URLs without protocol
    let fullUrl = url.trim();
    if (!fullUrl.match(/^https?:\/\//)) {
      fullUrl = `https://${fullUrl}`;
    }
    
    const urlObj = new URL(fullUrl);
    
    // Normalize hostname (remove www prefix and convert to lowercase)
    let hostname = urlObj.hostname.toLowerCase();
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    return hostname;
  } catch (e) {
    return 'unknown';
  }
}

/**
 * 智能外部链接优化模式说明：
 * - 'balanced': 平衡模式，综合考虑成本、质量、可靠性
 * - 'cost_first': 成本优先，优先使用免费或低成本API
 * - 'quality_first': 质量优先，优先使用数据质量最高的API
 * - 'quota_preserve': 配额保护，优先使用配额影响最小的API
 */