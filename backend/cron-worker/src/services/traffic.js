/**
 * Traffic service - 流量数据服务
 * 整合多个平台的流量数据获取功能
 */

import { config } from '../lib/config.js';
import { extractDomain } from '../lib/utils.js';
import { apiClient } from '../lib/api-adapter.js';

/**
 * Initialize traffic service with KV namespace for quota management
 */
export function initTrafficService(kvNamespace) {
  if (kvNamespace) {
    apiClient.init(kvNamespace);
  }
}

/**
 * 获取域名流量数据，支持多个数据源的回退机制
 * 优先级: SimilarWeb Direct -> SimilarWeb Traffic (RapidAPI) -> SimilarWeb Insights (RapidAPI) -> 模拟数据
 */
export async function fetchTrafficData(domain) {
  console.log(`Fetching traffic data for domain: ${domain}`);

  try {
    // Step 1: 尝试SimilarWeb Direct API（使用统一api-adapter）
    const directResult = await apiClient.getSimilarWebDirectTraffic(domain);
    if (directResult && directResult.success) {
      console.log(`✅ Using SimilarWeb Direct API traffic data for ${domain}: ${directResult.data.traffic}`);
      return {
        domain,
        traffic: directResult.data.traffic || 0,
        timestamp: directResult.timestamp,
        source: 'similarweb_direct',
        quota_used: directResult.api_info.quota_used
      };
    } else {
      console.log(`⚠️ SimilarWeb Direct API failed: ${directResult?.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`Error fetching SimilarWeb Direct data for ${domain}:`, error.message);
  }

  try {
    // Step 2: 回退到RapidAPI SimilarWeb Traffic（使用新的traffic API）
    const trafficApiResult = await apiClient.getSimilarWebTrafficDirect(domain);
    if (trafficApiResult && trafficApiResult.success) {
      console.log(`✅ Using RapidAPI SimilarWeb Traffic API data for ${domain}: ${trafficApiResult.data.visits}`);
      return {
        domain,
        traffic: trafficApiResult.data.visits || trafficApiResult.data.estimated_monthly_visits || 0,
        unique_visitors: trafficApiResult.data.unique_visitors || 0,
        bounce_rate: trafficApiResult.data.bounce_rate || 0,
        pages_per_visit: trafficApiResult.data.pages_per_visit || 0,
        avg_visit_duration: trafficApiResult.data.avg_visit_duration || 0,
        traffic_sources: trafficApiResult.data.traffic_sources || {},
        global_rank: trafficApiResult.data.global_rank || null,
        country_rank: trafficApiResult.data.country_rank || null,
        category: trafficApiResult.data.category || 'Unknown',
        timestamp: trafficApiResult.timestamp,
        source: 'rapidapi_similarweb_traffic',
        quota_used: trafficApiResult.api_info.quota_used
      };
    } else {
      console.log(`⚠️ RapidAPI SimilarWeb Traffic API failed: ${trafficApiResult?.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`Error fetching RapidAPI SimilarWeb Traffic data for ${domain}:`, error.message);
  }

  try {
    // Step 3: 回退到RapidAPI SimilarWeb Insights（使用insights API）
    const insightsResult = await apiClient.getSimilarWebTraffic(domain);
    if (insightsResult && insightsResult.success) {
      console.log(`✅ Using RapidAPI SimilarWeb Insights API data for ${domain}: ${insightsResult.data.visits}`);
      return {
        domain,
        traffic: insightsResult.data.visits || 0,
        unique_visitors: insightsResult.data.unique_visitors || 0,
        bounce_rate: insightsResult.data.bounce_rate || 0,
        pages_per_visit: insightsResult.data.pages_per_visit || 0,
        avg_visit_duration: insightsResult.data.avg_visit_duration || 0,
        traffic_sources: insightsResult.data.traffic_sources || {},
        global_rank: insightsResult.data.global_rank || null,
        country_rank: insightsResult.data.country_rank || null,
        category: insightsResult.data.category || 'Unknown',
        timestamp: insightsResult.timestamp,
        source: 'rapidapi_similarweb_insights',
        quota_used: insightsResult.api_info.quota_used
      };
    } else {
      console.log(`⚠️ RapidAPI SimilarWeb Insights API failed: ${insightsResult?.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`Error fetching RapidAPI SimilarWeb Insights data for ${domain}:`, error.message);
  }

  // 如果所有SimilarWeb API都失败，返回默认值
  console.log(`⚠️ Using fallback traffic data for ${domain}`);
  return {
    domain,
    traffic: 0,
    timestamp: new Date().toISOString(),
    source: 'fallback'
  };
}

/**
 * 为URL获取流量数据
 */
export async function fetchTrafficForUrl(url) {
  const domain = extractDomain(url);
  if (!domain) {
    return {
      url,
      traffic: 0,
      error: 'Invalid URL',
      timestamp: new Date().toISOString()
    };
  }
  
  const result = await fetchTrafficData(domain);
  return { ...result, url };
}

/**
 * 获取流量数据收集的API状态分析
 */
export async function getTrafficApiAnalytics() {
  try {
    const analytics = await quotaManager.getApiAnalytics('traffic');
    return {
      success: true,
      data: analytics,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 智能流量数据优化模式说明：
 * - 'balanced': 平衡模式，综合考虑成本、质量、可靠性
 * - 'cost_first': 成本优先，优先使用免费或低成本API
 * - 'quality_first': 质量优先，优先使用数据质量最高的API
 * - 'quota_preserve': 配额保护，优先使用配额影响最小的API
 */