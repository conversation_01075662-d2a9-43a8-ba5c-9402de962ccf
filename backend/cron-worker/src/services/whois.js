/**
 * WHOIS service - 域名WHOIS信息服务
 * 现在默认使用RDAP (Registration Data Access Protocol)，失败时回退到传统WHOIS API
 * 
 * RDAP优势：
 * - 结构化JSON数据，更易解析
 * - HTTPS安全传输
 * - 更好的国际化支持
 * - 标准化的访问控制
 * - 官方IANA引导服务支持
 */

import { extractDomain } from '../lib/utils.js';
import { getCachedWhoisInfo } from '../db.js';
import { quotaManager } from '../lib/quota-manager.js';
import { fetchRdapInfo, testRdapConnectivity, getRdapStats, clearRdapCache } from './rdap.js';

/**
 * Initialize WHOIS service with KV namespace for quota management
 */
export function initWhoisService(kvNamespace) {
  if (kvNamespace) {
    quotaManager.init(kvNamespace);
  }
}

/**
 * 获取域名WHOIS信息 - 优先使用RDAP，失败时回退到传统WHOIS API
 * 
 * 查询顺序：
 * 1. 检查缓存 (30天有效期)
 * 2. 尝试RDAP查询 (推荐方式)
 * 3. 回退到传统WHOIS API (quota-manager智能选择)
 */
export async function fetchWhoisInfo(domain, optimizationMode = 'cost_first') {
  console.log(`🔍 Fetching WHOIS info for domain: ${domain} (mode: ${optimizationMode})`);
  
  // Normalize domain (remove protocol, www, etc.)
  const cleanDomain = extractDomain(domain);
  
  // Step 1: 检查数据库缓存 (30天，WHOIS/RDAP信息变化较少)
  const cachedData = await getCachedWhoisInfo(cleanDomain);
  if (cachedData) {
    console.log(`✅ Using cached ${cachedData.source || 'WHOIS'} data for ${cleanDomain}`);
    return {
      ...cachedData,
      cached: true
    };
  }
  
  console.log(`🤖 No cache found, trying RDAP first for ${cleanDomain}`);
  
  // Step 2: 尝试RDAP查询 (现代化方式)
  try {
    const rdapData = await fetchRdapInfo(cleanDomain);
    
    if (rdapData && !rdapData.error) {
      console.log(`✅ RDAP lookup successful for ${cleanDomain}`);
      return {
        ...rdapData,
        method: 'rdap',
        fallback_used: false
      };
    }
  } catch (rdapError) {
    console.warn(`⚠️ RDAP lookup failed for ${cleanDomain}:`, rdapError.message);
    
    // Log RDAP failure for monitoring
    console.log(`🔄 Falling back to traditional WHOIS API for ${cleanDomain}`);
  }
  
  // Step 3: 回退到传统WHOIS API (使用智能API选择)
  try {
    console.log(`🤖 Using traditional WHOIS API with intelligent selection for ${cleanDomain}`);
    
    const result = await quotaManager.executeWithIntelligentSelection(
      'whois',
      [cleanDomain],
      optimizationMode
    );

    if (result.success) {
      console.log(`✅ Traditional WHOIS info retrieved via ${result.api_selection.selected_api}`);
      
      // 标准化返回格式，适配不同API的数据结构
      const whoisData = normalizeWhoisData(result.data, cleanDomain, result.api_selection.selected_api);
      
      return {
        ...whoisData,
        timestamp: result.timestamp,
        source: result.api_selection.selected_api,
        method: 'whois_api',
        fallback_used: true,
        quota_used: result.api_info?.quota_used || true,
        api_selection: result.api_selection
      };
    } else {
      // Check if this is a structured API error (not a complete failure)
      if (result.errorType === 'api_error' && result.error) {
        console.log(`⚠️ Traditional WHOIS API returned structured error: ${result.error}`);
        return handleWhoisFallback(cleanDomain, {
          fallback_strategy: 'return_error',
          error: result.error,
          reason: 'api_error',
          method: 'whois_api'
        });
      }
      
      console.log(`⚠️ All traditional WHOIS APIs failed, using fallback strategy: ${result.fallback_strategy}`);
      return handleWhoisFallback(cleanDomain, {
        ...result,
        method: 'whois_api'
      });
    }
  } catch (error) {
    console.error(`❌ Error in traditional WHOIS fetch for ${cleanDomain}:`, error.message);
    return handleWhoisFallback(cleanDomain, {
      fallback_strategy: 'empty_whois',
      error: error.message,
      method: 'both_failed'
    });
  }
}

/**
 * 标准化不同API返回的WHOIS数据格式
 */
function normalizeWhoisData(apiData, domain, apiSource) {
  const baseData = {
    domain,
    creation_date: null,
    expiration_date: null,
    registrar: '',
    name_servers: [],
    emails: '',
    dnssec: 'unknown'
  };

  // 适配API Layer WHOIS格式
  if (apiSource === 'api_layer_whois' && apiData) {
    return {
      ...baseData,
      domain: apiData.domain || domain,
      creation_date: apiData.creation_date,
      expiration_date: apiData.expiration_date,
      registrar: apiData.registrar || '',
      name_servers: Array.isArray(apiData.name_servers) ? apiData.name_servers : [],
      emails: apiData.emails || '',
      dnssec: apiData.dnssec || 'unknown',
      source: apiSource
    };
  }
  
  // 直接格式
  if (apiData.domain || apiData.creation_date) {
    return {
      ...baseData,
      domain: apiData.domain || domain,
      creation_date: apiData.creation_date,
      expiration_date: apiData.expiration_date,
      registrar: apiData.registrar || '',
      name_servers: Array.isArray(apiData.name_servers) ? apiData.name_servers : [],
      emails: apiData.emails || '',
      dnssec: apiData.dnssec || 'unknown',
      source: apiSource
    };
  }
  
  // 默认格式
  return {
    ...baseData,
    source: apiSource
  };
}

/**
 * 处理WHOIS信息获取失败的回退策略
 */
function handleWhoisFallback(domain, failureInfo) {
  console.log(`🔄 Applying WHOIS fallback strategy: ${failureInfo.fallback_strategy}`);
  
  const baseResult = {
    domain,
    timestamp: new Date().toISOString(),
    quota_used: false,
    fallback_reason: failureInfo.reason || 'api_unavailable',
    method: failureInfo.method || 'unknown',
    fallback_used: true
  };

  switch (failureInfo.fallback_strategy) {
    case 'return_error':
      // Return clear error instead of empty/fake data
      return {
        ...baseResult,
        error: failureInfo.error || 'WHOIS/RDAP APIs unavailable',
        source: 'error'
      };
      
    case 'empty_whois':
      return {
        ...baseResult,
        creation_date: null,
        expiration_date: null,
        registrar: '',
        name_servers: [],
        emails: '',
        dnssec: 'unknown',
        source: 'fallback_empty',
        error: failureInfo.error
      };
      
    default:
      // Default to error for transparency
      return {
        ...baseResult,
        error: failureInfo.error || 'WHOIS/RDAP APIs unavailable',
        source: 'error'
      };
  }
}

/**
 * 批量获取WHOIS信息
 */
export async function fetchBatchWhoisInfo(domains, optimizationMode = 'quota_preserve') {
  console.log(`📦 Fetching batch WHOIS info for ${domains.length} domains`);
  
  const results = [];
  
  for (const domain of domains) {
    try {
      const whoisInfo = await fetchWhoisInfo(domain, optimizationMode);
      results.push(whoisInfo);
      
      // 批量处理时添加小延迟避免rate limiting
      if (domains.length > 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error(`❌ Failed to fetch WHOIS for ${domain}:`, error.message);
      results.push({
        domain: extractDomain(domain),
        error: error.message,
        timestamp: new Date().toISOString(),
        method: 'error'
      });
    }
  }
  
  return results;
}

/**
 * 获取URL的WHOIS信息（自动提取域名）
 */
export async function fetchWhoisInfoForUrl(url) {
  const domain = extractDomain(url);
  return fetchWhoisInfo(domain);
}

/**
 * 获取WHOIS API使用分析
 */
export async function getWhoisApiAnalytics() {
  try {
    const quotaStatus = await quotaManager.getQuotaStatus();
    const whoisApis = Object.entries(quotaStatus).filter(([key, data]) => 
      key.includes('whois') || key.includes('apilayer')
    );
    
    // 获取RDAP统计信息
    const rdapStats = await getRdapStats();
    
    return {
      rdap_available: !rdapStats.error,
      rdap_stats: rdapStats,
      traditional_apis: {
        available_apis: whoisApis.length,
        api_details: whoisApis.map(([key, data]) => ({
          api: key,
          name: data.adapter_name || key,
          monthly_usage: data.monthly,
          daily_usage: data.daily,
          status: data.monthly.remaining > 0 ? 'available' : 'exhausted'
        }))
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('❌ Error getting WHOIS API analytics:', error.message);
    return {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 测试WHOIS/RDAP连通性
 */
export async function testWhoisConnectivity(domain) {
  const cleanDomain = extractDomain(domain);
  const results = {
    domain: cleanDomain,
    timestamp: new Date().toISOString(),
    rdap_test: null,
    whois_api_test: null,
    recommended_method: null
  };
  
  try {
    // Test RDAP connectivity
    console.log(`🧪 Testing RDAP connectivity for ${cleanDomain}`);
    results.rdap_test = await testRdapConnectivity(cleanDomain);
    
    // Test traditional WHOIS API
    console.log(`🧪 Testing traditional WHOIS API for ${cleanDomain}`);
    try {
      const whoisResult = await quotaManager.executeWithIntelligentSelection('whois', [cleanDomain], 'cost_first');
      results.whois_api_test = {
        available: whoisResult.success,
        api_used: whoisResult.api_selection?.selected_api,
        error: whoisResult.success ? null : whoisResult.error
      };
    } catch (error) {
      results.whois_api_test = {
        available: false,
        error: error.message
      };
    }
    
    // Determine recommended method
    const rdapWorking = results.rdap_test && results.rdap_test.server_tests.some(test => test.accessible);
    const whoisWorking = results.whois_api_test && results.whois_api_test.available;
    
    if (rdapWorking && whoisWorking) {
      results.recommended_method = 'rdap_preferred';
    } else if (rdapWorking) {
      results.recommended_method = 'rdap_only';
    } else if (whoisWorking) {
      results.recommended_method = 'whois_api_only';
    } else {
      results.recommended_method = 'both_failed';
    }
    
  } catch (error) {
    results.error = error.message;
  }
  
  return results;
}

/**
 * 计算域名年龄（从创建日期到现在）
 */
export function calculateDomainAge(creationDate) {
  if (!creationDate) return null;
  
  try {
    const created = new Date(creationDate);
    const now = new Date();
    const diffTime = Math.abs(now - created);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffYears = Math.floor(diffDays / 365);
    const remainingDays = diffDays % 365;
    
    return {
      total_days: diffDays,
      years: diffYears,
      remaining_days: remainingDays,
      formatted: diffYears > 0 ? `${diffYears} years, ${remainingDays} days` : `${diffDays} days`
    };
  } catch (error) {
    console.error('Error calculating domain age:', error.message);
    return null;
  }
}

/**
 * 检查域名是否即将过期（默认90天内）
 */
export function checkDomainExpiration(expirationDate, warningDays = 90) {
  if (!expirationDate) return null;
  
  try {
    const expires = new Date(expirationDate);
    const now = new Date();
    const diffTime = expires - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return {
      days_until_expiration: diffDays,
      is_expired: diffDays < 0,
      is_expiring_soon: diffDays > 0 && diffDays <= warningDays,
      warning_threshold: warningDays,
      status: diffDays < 0 ? 'expired' : 
              diffDays <= warningDays ? 'expiring_soon' : 'active'
    };
  } catch (error) {
    console.error('Error checking domain expiration:', error.message);
    return null;
  }
}

/**
 * 强制使用RDAP查询（跳过缓存，用于测试）
 */
export async function forceRdapQuery(domain) {
  const cleanDomain = extractDomain(domain);
  console.log(`🔬 Force RDAP query for ${cleanDomain}`);
  
  try {
    return await fetchRdapInfo(cleanDomain);
  } catch (error) {
    console.error(`❌ Force RDAP query failed:`, error.message);
    throw error;
  }
}

/**
 * 强制使用传统WHOIS API查询（跳过缓存和RDAP，用于测试）
 */
export async function forceWhoisApiQuery(domain, optimizationMode = 'cost_first') {
  const cleanDomain = extractDomain(domain);
  console.log(`🔬 Force traditional WHOIS API query for ${cleanDomain}`);
  
  try {
    const result = await quotaManager.executeWithIntelligentSelection(
      'whois',
      [cleanDomain],
      optimizationMode
    );

    if (result.success) {
      const whoisData = normalizeWhoisData(result.data, cleanDomain, result.api_selection.selected_api);
      return {
        ...whoisData,
        timestamp: result.timestamp,
        source: result.api_selection.selected_api,
        method: 'whois_api_forced',
        quota_used: result.api_info?.quota_used || true,
        api_selection: result.api_selection
      };
    } else {
      throw new Error(result.error || 'Traditional WHOIS API failed');
    }
  } catch (error) {
    console.error(`❌ Force WHOIS API query failed:`, error.message);
    throw error;
  }
}

/**
 * 清除RDAP缓存（用于测试）
 */
export function clearWhoisCache() {
  clearRdapCache();
  console.log('🧹 WHOIS service cache cleared');
}

// Re-export RDAP utilities for direct access
export { 
  testRdapConnectivity, 
  getRdapStats, 
  clearRdapCache,
  fetchRdapInfo 
} from './rdap.js'; 