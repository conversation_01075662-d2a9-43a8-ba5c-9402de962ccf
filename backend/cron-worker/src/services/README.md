# Services 重构说明

## 重构概述

服务层已从按平台组织重构为按功能需求组织，提高了代码的可维护性和可扩展性。

## 重构前后对比

### 重构前（按平台）
```
services/
├── ahrefs.js      # Ahrefs API 调用
├── google.js      # Google API 调用
├── similarweb.js  # SimilarWeb API 调用
├── rapidapi.js    # RapidAPI 调用
└── feishu.js      # 飞书通知
```

### 重构后（按功能）
```
services/
├── domain-rating.js    # 域名评级服务
├── external-links.js   # 外部链接服务
├── indexing.js         # 索引状态服务
├── traffic.js          # 流量数据服务
└── notification.js     # 通知服务
```

## 各服务详细说明

### 1. domain-rating.js - 域名评级服务
**功能**: 获取域名评级（DR）数据
**数据源优先级**: 数据库缓存 → SimilarWeb → Ahrefs → RapidAPI → 模拟数据
**主要方法**:
- `fetchDomainRating(domain)` - 获取域名评级
- `fetchDomainRatingForUrl(url)` - 为URL获取域名评级

### 2. external-links.js - 外部链接服务
**功能**: 获取域名的外部链接数据
**数据源优先级**: 数据库缓存 → SimilarWeb → Google Custom Search → RapidAPI → 模拟数据
**主要方法**:
- `fetchExternalLinks(domain)` - 获取域名的外部链接

### 3. indexing.js - 索引状态服务
**功能**: 检查URL在搜索引擎中的索引状态
**数据源优先级**: 数据库缓存 → Google link:site 搜索 → Google Custom Search API
**主要方法**:
- `checkIndexingStatus(targetUrl, website)` - 检查URL索引状态
- `getIndexedUrls(domain)` - 获取域名下已索引的URL

### 4. traffic.js - 流量数据服务
**功能**: 获取域名流量数据
**数据源优先级**: SimilarWeb → RapidAPI → 模拟数据
**主要方法**:
- `fetchTrafficData(domain)` - 获取域名流量数据
- `fetchTrafficForUrl(url)` - 为URL获取流量数据

### 5. notification.js - 通知服务
**功能**: 发送各种通知消息
**平台**: 飞书 Webhook
**主要方法**:
- `sendMessage(title, content)` - 发送消息
- `sendAlert(message)` - 发送警报消息

## 重构优势

1. **功能聚合**: 相同功能的代码集中在一个文件中，便于维护
2. **回退机制**: 每个服务都实现了多数据源的回退机制，提高可靠性
3. **统一接口**: 每个服务提供统一的接口，隐藏内部实现细节
4. **易于扩展**: 添加新的数据源或功能更加简单
5. **代码复用**: 减少了重复代码，提高了代码质量

## 处理器更新

所有处理器（processors）中的导入和调用都已更新：

### domain-rating.js 处理器
- 导入: `fetchDomainRatingForDomain` → `fetchDomainRating`
- 来源: `../services/ahrefs.js` → `../services/domain-rating.js`

### external-links.js 处理器
- 导入: `fetchExternalLinksForDomain` → `fetchExternalLinks`
- 来源: `../services/google.js` → `../services/external-links.js`
- 数据字段: `links` → `discovered_links`, `total_links` → `external_links`

### indexing.js 处理器
- 导入: `getIndexedUrlsForDomain` → `getIndexedUrls`
- 来源: `../services/google.js` → `../services/indexing.js`

### traffic.js 处理器
- 导入: `fetchTrafficForDomain` → `fetchTrafficData`
- 来源: `../services/similarweb.js` → `../services/traffic.js`

### 通知更新
- 所有处理器: `../services/feishu.js` → `../services/notification.js`

## 配置要求

各服务需要的环境变量：

- **domain-rating.js**: `SIMILARWEB_API_KEY`, `AHREFS_API_KEY`, `RAPIDAPI_KEY`
- **external-links.js**: `SIMILARWEB_API_KEY`, `GOOGLE_API_KEY`, `GOOGLE_SEARCH_ENGINE_ID`, `RAPIDAPI_KEY`
- **indexing.js**: `GOOGLE_API_KEY`, `GOOGLE_SEARCH_ENGINE_ID`
- **traffic.js**: `SIMILARWEB_API_KEY`, `RAPIDAPI_KEY`
- **notification.js**: `FEISHU_WEBHOOK_URL`

## 测试建议

在没有真实API密钥的情况下，各服务都会返回模拟数据，确保系统正常运行。建议在生产环境中配置真实的API密钥以获得准确数据。 