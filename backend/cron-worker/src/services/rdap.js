/**
 * RDAP (Registration Data Access Protocol) service
 * 
 * RDAP is the modern successor to WHOIS, providing structured JSON data
 * over HTTPS with better internationalization and access control.
 * 
 * This service uses the IANA bootstrap system to discover authoritative
 * RDAP servers for each TLD, with fallback to traditional WHOIS.
 */

import { extractDomain } from '../lib/utils.js';
import { getCachedWhoisInfo } from '../db.js';

// IANA RDAP Bootstrap URLs
const IANA_BOOTSTRAP_URLS = {
  dns: 'https://data.iana.org/rdap/dns.json',
  ipv4: 'https://data.iana.org/rdap/ipv4.json',
  ipv6: 'https://data.iana.org/rdap/ipv6.json',
  asn: 'https://data.iana.org/rdap/asn.json'
};

// Common RDAP server endpoints (fallback when bootstrap fails)
const COMMON_RDAP_SERVERS = {
  // Generic TLDs
  'com': ['https://rdap.verisign.com/com/v1/', 'https://rdap.markmonitor.com/rdap/'],
  'net': ['https://rdap.verisign.com/net/v1/'],
  'org': ['https://rdap.pir.org/'],
  'info': ['https://rdap.afilias.net/rdap/afilias/'],
  'biz': ['https://rdap.nic.biz/'],
  'name': ['https://rdap.nic.name/'],
  'pro': ['https://rdap.nic.pro/'],
  
  // Country code TLDs
  'uk': ['https://rdap.nominet.uk/uk/'],
  'de': ['https://rdap.denic.de/'],
  'fr': ['https://rdap.nic.fr/'],
  'nl': ['https://rdap.sidn.nl/'],
  'au': ['https://rdap.auda.org.au/'],
  'ca': ['https://rdap.cira.ca/'],
  'jp': ['https://rdap.jprs.jp/'],
  'cn': ['https://rdap.cnnic.cn/'],
  'in': ['https://rdap.registry.in/'],
  'br': ['https://rdap.registro.br/'],
  'mx': ['https://rdap.mx/'],
  'ru': ['https://rdap.tcinet.ru/'],
  'it': ['https://rdap.nic.it/'],
  'es': ['https://rdap.nic.es/'],
  'pl': ['https://rdap.dns.pl/'],
  'tw': ['https://rdap.twnic.tw/'],
  'kr': ['https://rdap.kr/'],
  'sg': ['https://rdap.sgnic.sg/'],
  'hk': ['https://rdap.hkirc.hk/'],
  'th': ['https://rdap.thnic.co.th/'],
  'my': ['https://rdap.mynic.my/'],
  'id': ['https://rdap.pandi.id/'],
  'ph': ['https://rdap.dot.ph/'],
  'vn': ['https://rdap.vnnic.vn/'],
  
  // New gTLDs (examples)
  'app': ['https://rdap.nic.google/'],
  'dev': ['https://rdap.nic.google/'],
  'tech': ['https://rdap.nic.tech/'],
  'online': ['https://rdap.centralnic.com/online/'],
  'site': ['https://rdap.centralnic.com/site/'],
  'store': ['https://rdap.nic.store/'],
  'cloud': ['https://rdap.nic.cloud/'],
  'io': ['https://rdap.nic.io/'],
  'ai': ['https://rdap.nic.ai/'],
  'co': ['https://rdap.nic.co/'],
  'me': ['https://rdap.nic.me/'],
  'ly': ['https://rdap.nic.ly/'],
  'tv': ['https://rdap.nic.tv/'],
  'cc': ['https://rdap.nic.cc/'],
  'ws': ['https://rdap.nic.ws/']
};

// Cache for bootstrap data (1 hour TTL)
let bootstrapCache = {
  dns: { data: null, expires: 0 },
  ipv4: { data: null, expires: 0 },
  ipv6: { data: null, expires: 0 },
  asn: { data: null, expires: 0 }
};

/**
 * Fetch and cache IANA bootstrap data
 */
async function fetchBootstrapData(type = 'dns') {
  const now = Date.now();
  const cache = bootstrapCache[type];
  
  // Return cached data if still valid
  if (cache.data && now < cache.expires) {
    return cache.data;
  }
  
  try {
    console.log(`📡 Fetching IANA ${type.toUpperCase()} bootstrap data...`);
    const response = await fetch(IANA_BOOTSTRAP_URLS[type], {
      headers: {
        'User-Agent': 'LinkTrackPro-RDAP-Client/1.0',
        'Accept': 'application/json'
      },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Cache for 1 hour
    cache.data = data;
    cache.expires = now + (60 * 60 * 1000);
    
    console.log(`✅ Bootstrap data cached for ${type.toUpperCase()}`);
    return data;
  } catch (error) {
    console.warn(`⚠️ Failed to fetch ${type} bootstrap data:`, error.message);
    
    // Return stale cache if available
    if (cache.data) {
      console.log(`🔄 Using stale ${type} bootstrap cache`);
      return cache.data;
    }
    
    return null;
  }
}

/**
 * Find RDAP server for a domain using IANA bootstrap
 */
async function findRdapServer(domain) {
  const tld = domain.split('.').pop().toLowerCase();
  
  try {
    const bootstrapData = await fetchBootstrapData('dns');
    
    if (bootstrapData && bootstrapData.services) {
      // Find matching service in bootstrap data
      for (const service of bootstrapData.services) {
        const [tlds, servers] = service;
        
        if (tlds.includes(tld)) {
          console.log(`🎯 Found RDAP servers for .${tld} via IANA bootstrap`);
          return servers.filter(url => url.startsWith('https://'));
        }
      }
    }
  } catch (error) {
    console.warn(`⚠️ Error using IANA bootstrap for ${tld}:`, error.message);
  }
  
  // Fallback to common servers
  if (COMMON_RDAP_SERVERS[tld]) {
    console.log(`🔄 Using fallback RDAP servers for .${tld}`);
    return COMMON_RDAP_SERVERS[tld];
  }
  
  console.warn(`❌ No RDAP servers found for .${tld}`);
  return [];
}

/**
 * Query RDAP server for domain information
 */
async function queryRdapServer(serverUrl, domain) {
  const rdapUrl = `${serverUrl.replace(/\/$/, '')}/domain/${domain}`;
  
  try {
    console.log(`🔍 Querying RDAP: ${rdapUrl}`);
    
    const response = await fetch(rdapUrl, {
      headers: {
        'User-Agent': 'LinkTrackPro-RDAP-Client/1.0',
        'Accept': 'application/rdap+json, application/json'
      },
      signal: AbortSignal.timeout(15000) // 15 second timeout
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Validate RDAP response
    if (!data.objectClassName || data.objectClassName !== 'domain') {
      throw new Error('Invalid RDAP response: not a domain object');
    }
    
    console.log(`✅ RDAP query successful for ${domain}`);
    return data;
  } catch (error) {
    console.warn(`⚠️ RDAP query failed for ${rdapUrl}:`, error.message);
    throw error;
  }
}

/**
 * Convert RDAP response to normalized WHOIS format
 */
function normalizeRdapResponse(rdapData, domain) {
  const normalized = {
    domain: domain,
    creation_date: null,
    expiration_date: null,
    registrar: '',
    name_servers: [],
    emails: '',
    dnssec: 'unknown',
    source: 'rdap'
  };
  
  try {
    // Extract domain name
    if (rdapData.ldhName) {
      normalized.domain = rdapData.ldhName.toLowerCase();
    }
    
    // Extract dates from events
    if (rdapData.events && Array.isArray(rdapData.events)) {
      for (const event of rdapData.events) {
        if (event.eventAction === 'registration' && event.eventDate) {
          normalized.creation_date = event.eventDate;
        } else if (event.eventAction === 'expiration' && event.eventDate) {
          normalized.expiration_date = event.eventDate;
        }
      }
    }
    
    // Extract registrar information
    if (rdapData.entities && Array.isArray(rdapData.entities)) {
      for (const entity of rdapData.entities) {
        if (entity.roles && entity.roles.includes('registrar')) {
          // Try to get registrar name from vCard
          if (entity.vcardArray && Array.isArray(entity.vcardArray)) {
            const vcard = entity.vcardArray[1]; // vCard data is in second element
            if (Array.isArray(vcard)) {
              for (const field of vcard) {
                if (Array.isArray(field) && field[0] === 'fn' && field[3]) {
                  normalized.registrar = field[3];
                  break;
                }
              }
            }
          }
          
          // Fallback to entity handle or publicIds
          if (!normalized.registrar) {
            if (entity.handle) {
              normalized.registrar = entity.handle;
            } else if (entity.publicIds && entity.publicIds[0] && entity.publicIds[0].identifier) {
              normalized.registrar = entity.publicIds[0].identifier;
            }
          }
          break;
        }
      }
    }
    
    // Extract name servers
    if (rdapData.nameservers && Array.isArray(rdapData.nameservers)) {
      normalized.name_servers = rdapData.nameservers
        .map(ns => ns.ldhName || ns.objectClassName)
        .filter(Boolean);
    }
    
    // Extract DNSSEC information
    if (rdapData.secureDNS) {
      if (rdapData.secureDNS.delegationSigned === true) {
        normalized.dnssec = 'signed';
      } else if (rdapData.secureDNS.delegationSigned === false) {
        normalized.dnssec = 'unsigned';
      }
    }
    
    // Extract contact emails (for compatibility)
    const emails = [];
    if (rdapData.entities && Array.isArray(rdapData.entities)) {
      for (const entity of rdapData.entities) {
        if (entity.vcardArray && Array.isArray(entity.vcardArray)) {
          const vcard = entity.vcardArray[1];
          if (Array.isArray(vcard)) {
            for (const field of vcard) {
              if (Array.isArray(field) && field[0] === 'email' && field[3]) {
                emails.push(field[3]);
              }
            }
          }
        }
      }
    }
    normalized.emails = emails.join(', ');
    
  } catch (error) {
    console.warn(`⚠️ Error normalizing RDAP response:`, error.message);
  }
  
  return normalized;
}

/**
 * Fetch domain information using RDAP
 */
export async function fetchRdapInfo(domain) {
  console.log(`🚀 Starting RDAP lookup for: ${domain}`);
  
  // Normalize domain
  const cleanDomain = extractDomain(domain);
  
  // Check cache first (30 days for RDAP data)
  const cachedData = await getCachedWhoisInfo(cleanDomain);
  if (cachedData && cachedData.source === 'rdap') {
    console.log(`✅ Using cached RDAP data for ${cleanDomain}`);
    return {
      ...cachedData,
      cached: true
    };
  }
  
  try {
    // Find RDAP servers for this domain
    const rdapServers = await findRdapServer(cleanDomain);
    
    if (rdapServers.length === 0) {
      throw new Error(`No RDAP servers found for domain ${cleanDomain}`);
    }
    
    // Try each RDAP server until one succeeds
    let lastError = null;
    for (const server of rdapServers) {
      try {
        const rdapData = await queryRdapServer(server, cleanDomain);
        const normalizedData = normalizeRdapResponse(rdapData, cleanDomain);
        
        return {
          ...normalizedData,
          timestamp: new Date().toISOString(),
          rdap_server: server,
          raw_rdap_data: rdapData // Include raw data for debugging
        };
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ RDAP server ${server} failed:`, error.message);
        continue;
      }
    }
    
    // All RDAP servers failed
    throw new Error(`All RDAP servers failed. Last error: ${lastError?.message}`);
    
  } catch (error) {
    console.error(`❌ RDAP lookup failed for ${cleanDomain}:`, error.message);
    throw error;
  }
}

/**
 * Test RDAP connectivity for a domain
 */
export async function testRdapConnectivity(domain) {
  const cleanDomain = extractDomain(domain);
  const results = {
    domain: cleanDomain,
    timestamp: new Date().toISOString(),
    bootstrap_available: false,
    servers_found: [],
    server_tests: []
  };
  
  try {
    // Test bootstrap availability
    const bootstrapData = await fetchBootstrapData('dns');
    results.bootstrap_available = !!bootstrapData;
    
    // Find servers
    const rdapServers = await findRdapServer(cleanDomain);
    results.servers_found = rdapServers;
    
    // Test each server
    for (const server of rdapServers.slice(0, 3)) { // Test max 3 servers
      const serverTest = {
        server,
        accessible: false,
        response_time: null,
        error: null
      };
      
      try {
        const startTime = Date.now();
        await queryRdapServer(server, cleanDomain);
        serverTest.accessible = true;
        serverTest.response_time = Date.now() - startTime;
      } catch (error) {
        serverTest.error = error.message;
      }
      
      results.server_tests.push(serverTest);
    }
    
  } catch (error) {
    results.error = error.message;
  }
  
  return results;
}

/**
 * Get RDAP server statistics
 */
export async function getRdapStats() {
  try {
    const bootstrapData = await fetchBootstrapData('dns');
    
    if (!bootstrapData || !bootstrapData.services) {
      return {
        error: 'Bootstrap data unavailable',
        timestamp: new Date().toISOString()
      };
    }
    
    const stats = {
      bootstrap_date: bootstrapData.publication,
      total_services: bootstrapData.services.length,
      total_tlds: 0,
      https_servers: 0,
      http_servers: 0,
      server_distribution: {},
      timestamp: new Date().toISOString()
    };
    
    // Analyze bootstrap data
    for (const service of bootstrapData.services) {
      const [tlds, servers] = service;
      stats.total_tlds += tlds.length;
      
      for (const server of servers) {
        if (server.startsWith('https://')) {
          stats.https_servers++;
        } else if (server.startsWith('http://')) {
          stats.http_servers++;
        }
        
        // Extract server domain for distribution analysis
        try {
          const serverDomain = new URL(server).hostname;
          stats.server_distribution[serverDomain] = (stats.server_distribution[serverDomain] || 0) + 1;
        } catch (e) {
          // Skip invalid URLs
        }
      }
    }
    
    return stats;
  } catch (error) {
    return {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Clear RDAP bootstrap cache (for testing/debugging)
 */
export function clearRdapCache() {
  bootstrapCache = {
    dns: { data: null, expires: 0 },
    ipv4: { data: null, expires: 0 },
    ipv6: { data: null, expires: 0 },
    asn: { data: null, expires: 0 }
  };
  console.log('🧹 RDAP bootstrap cache cleared');
} 