/**
 * Indexing service - 索引状态服务
 * 专注于业务逻辑，API选择交给智能quota-manager处理
 */

import { getCachedIndexingStatus } from '../db.js';
import { quotaManager } from '../lib/quota-manager.js';

/**
 * Initialize indexing service with KV namespace for quota management
 */
export function initIndexingService(kvNamespace) {
  if (kvNamespace) {
    quotaManager.init(kvNamespace);
  }
}

/**
 * 检查URL的索引状态 - 使用智能API选择
 * quota-manager根据配额、优先级和优化模式自动选择最佳API
 * @param {string} targetUrl - 目标URL
 * @param {string} website - 网站域名 (可选)
 * @param {string} optimizationMode - 优化模式
 */
export async function checkIndexingStatus(targetUrl, website = null, optimizationMode = 'cost_first') {
  console.log(`🔍 Checking indexing status for URL: ${targetUrl}${website ? ` within ${website}` : ''} (mode: ${optimizationMode})`);
  
  // 从targetUrl提取域名
  if (!website) {
    try {
      website = new URL(targetUrl).hostname;
    } catch (e) {
      website = targetUrl;
    }
  }
  
  // Step 1: 检查数据库缓存
  const cachedData = await getCachedIndexingStatus(targetUrl, website);
  if (cachedData) {
    console.log(`✅ Using cached indexing data for ${targetUrl}`);
    return {
      ...cachedData,
      cached: true
    };
  }

  console.log(`🤖 No cache found, using intelligent API selection for ${targetUrl}`);
  
  try {
    // 使用智能API选择系统
    const result = await quotaManager.executeWithIntelligentSelection(
      'indexing',
      [targetUrl, website],
      optimizationMode
    );

    if (result.success) {
      console.log(`✅ Indexing status retrieved via ${result.api_selection.selected_api}`);
      
      // 标准化返回格式，适配不同API的数据结构
      const indexingData = normalizeIndexingData(result.data, targetUrl, website);
      
      return {
        ...indexingData,
        timestamp: result.timestamp,
        source: result.api_selection.selected_api,
        quota_used: result.api_info?.quota_used || true,
        api_selection: result.api_selection
      };
    } else {
      console.log(`⚠️ All indexing APIs failed, using fallback strategy: ${result.fallback_strategy}`);
      return handleIndexingFallback(targetUrl, website, result);
    }
  } catch (error) {
    console.error(`❌ Error in intelligent indexing check for ${targetUrl}:`, error.message);
    return handleIndexingFallback(targetUrl, website, { 
      fallback_strategy: 'not_indexed', 
      error: error.message 
    });
  }
}

/**
 * 标准化不同API返回的索引数据格式
 */
function normalizeIndexingData(apiData, targetUrl, website) {
  const baseData = {
    domain: website,
    target_url: targetUrl,
    is_indexed: false,
    indexed_pages: 0,
    total_pages: 1,
    indexing_issues: []
  };

  // 适配Google API格式
  if (apiData.is_indexed !== undefined) {
    return {
      ...baseData,
      is_indexed: apiData.is_indexed,
      indexed_pages: apiData.indexed_pages || 0,
      total_results: apiData.total_results || 0,
      indexing_issues: apiData.is_indexed ? [] : ['URL not found in search index']
    };
  }
  
  // 适配Exa API格式
  if (apiData.results_count !== undefined) {
    const isIndexed = apiData.is_indexed || apiData.domain_matches > 0;
    return {
      ...baseData,
      is_indexed: isIndexed,
      indexed_pages: apiData.domain_matches || 0,
      exa_results_count: apiData.results_count,
      exa_domain_matches: apiData.domain_matches,
      indexing_issues: isIndexed ? [] : ['URL not found via search API']
    };
  }
  
  // 默认格式
  return {
    ...baseData,
    indexing_issues: ['Unable to determine indexing status']
  };
}

/**
 * 处理索引状态检查失败的回退策略
 */
function handleIndexingFallback(targetUrl, website, failureInfo) {
  console.log(`🔄 Applying indexing fallback strategy: ${failureInfo.fallback_strategy}`);
  
  const baseResult = {
    domain: website,
    target_url: targetUrl,
    timestamp: new Date().toISOString(),
    quota_used: false,
    fallback_reason: failureInfo.reason || 'api_unavailable'
  };

  switch (failureInfo.fallback_strategy) {
    case 'not_indexed':
      return {
        ...baseResult,
        is_indexed: false,
        indexed_pages: 0,
        total_pages: 1,
        indexing_issues: ['Unable to verify indexing status - assuming not indexed'],
        source: 'fallback_not_indexed'
      };
      
    default:
      return {
        ...baseResult,
        is_indexed: false,
        indexed_pages: 0,
        total_pages: 1,
        indexing_issues: ['Indexing verification failed'],
        source: 'fallback_unknown',
        error: failureInfo.error
      };
  }
}

/**
 * 获取域名下所有已索引的URL - 使用智能API选择
 * @param {string} domain - 域名
 * @param {string} optimizationMode - 优化模式
 * @param {number} maxResults - 最大返回结果数
 * @returns {Array} - 已索引URL列表
 */
export async function getIndexedUrls(domain, optimizationMode = 'cost_first', maxResults = 20) {
  console.log(`🔍 Getting indexed URLs for domain: ${domain} (mode: ${optimizationMode})`);
  
  try {
    // 使用智能API选择 - 这里复用indexing策略，因为都是检索索引内容
    const result = await quotaManager.executeWithIntelligentSelection(
      'indexing',
      [`site:${domain}`, domain],
      optimizationMode
    );

    if (result.success) {
      console.log(`✅ Indexed URLs retrieved via ${result.api_selection.selected_api}`);
      
      // 提取URL列表
      const indexedUrls = extractUrlsFromSearchResult(result.data, domain);
      const limitedUrls = indexedUrls.slice(0, maxResults);
      
      console.log(`📄 Found ${limitedUrls.length} indexed URLs for ${domain}`);
      return {
        success: true,
        domain,
        urls: limitedUrls,
        total_found: indexedUrls.length,
        source: result.api_selection.selected_api,
        timestamp: result.timestamp,
        api_selection: result.api_selection
      };
    } else {
      console.log(`⚠️ Failed to get indexed URLs: ${result.error}`);
      return {
        success: false,
        domain,
        urls: [],
        total_found: 0,
        source: 'fallback',
        error: result.error,
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    console.error(`❌ Error getting indexed URLs for ${domain}:`, error.message);
    return {
      success: false,
      domain,
      urls: [],
      total_found: 0,
      source: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 从搜索结果中提取URL列表
 */
function extractUrlsFromSearchResult(searchData, domain) {
  let urls = [];
  
  // 适配Google Custom Search结果
  if (searchData.results && Array.isArray(searchData.results)) {
    urls = searchData.results
      .map(item => item.link || item.url)
      .filter(url => url && url.includes(domain));
  }
  
  // 适配Exa AI搜索结果
  if (searchData.results && searchData.results.length && searchData.results[0].url) {
    urls = searchData.results
      .map(item => item.url)
      .filter(url => url && url.includes(domain));
  }
  
  // 去重并验证URL格式
  return [...new Set(urls)].filter(url => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  });
}

/**
 * 批量检查多个URL的索引状态（优化配额使用）
 */
export async function batchCheckIndexingStatus(urlList, optimizationMode = 'quota_preserve') {
  console.log(`📋 Batch checking indexing status for ${urlList.length} URLs`);
  
  const results = [];
  
  for (const url of urlList) {
    try {
      const result = await checkIndexingStatus(url, null, optimizationMode);
      results.push(result);
      
      // 批量处理时添加小延迟避免频率限制
      if (urlList.length > 5) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    } catch (error) {
      console.error(`Error checking indexing for ${url}:`, error.message);
      results.push(handleIndexingFallback(url, null, {
        fallback_strategy: 'not_indexed',
        error: error.message
      }));
    }
  }
  
  return results;
}

/**
 * 获取索引状态检查的API状态分析
 */
export async function getIndexingApiAnalytics() {
  try {
    const analytics = await quotaManager.getApiAnalytics('indexing');
    return {
      success: true,
      data: analytics,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}



/**
 * 智能索引检查优化模式说明：
 * - 'balanced': 平衡模式，综合考虑成本、质量、可靠性
 * - 'cost_first': 成本优先，优先使用免费或低成本API
 * - 'quality_first': 质量优先，优先使用数据质量最高的API  
 * - 'quota_preserve': 配额保护，优先使用配额影响最小的API
 */