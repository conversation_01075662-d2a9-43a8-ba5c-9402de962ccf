/**
 * Core API Route Handlers
 * Contains basic health check and main API endpoints
 */

import { processTrafficData, getTrafficData } from '../processors/traffic.js';
import { processDomainRatings, getDomainRating } from '../processors/domain-rating.js';
import { processExternalLinks, getExternalLinks } from '../processors/external-links.js';
import { processIndexingStatus, getIndexingStatus } from '../processors/indexing.js';
import { processWhoisInfo, getWhoisInfo } from '../processors/whois.js';
import { cleanupOldHistoryData } from '../db.js';
import { withApiLogging, extractProcessingMetrics } from '../lib/api-middleware.js';

/**
 * Health check endpoint
 */
export const healthHandler = withApiLogging(async (request, env, ctx) => {
  // Set cache hit since this is a simple response
  env.apiLogger?.setCacheHit(true);
  
  return new Response('🟢 LinkTrackPro Cron Worker (New Schema) - Healthy', {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual traffic data collection trigger
 */
export const triggerTrafficHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual traffic data collection triggered');
  
  const result = await processTrafficData(env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource('traffic-api');
  
  return new Response(`✅ Traffic data collection completed. Processed: ${metrics.processed}, Successful: ${metrics.successful}`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual domain rating collection trigger
 */
export const triggerDomainRatingHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual domain rating collection triggered');
  
  const result = await processDomainRatings(env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource('ahrefs-api');
  
  return new Response(`✅ Domain rating collection completed. Processed: ${metrics.processed}, Successful: ${metrics.successful}`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual external links collection trigger
 */
export const triggerLinksHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual external links collection triggered');
  
  const result = await processExternalLinks(env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource('google-search-console');
  
  return new Response(`✅ External links collection completed. Processed: ${metrics.processed}, Successful: ${metrics.successful}`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual indexing status check trigger
 */
export const triggerIndexingHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual indexing status check triggered');
  
  const result = await processIndexingStatus(env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource('google-search-api');
  
  return new Response(`✅ Indexing status check completed. Processed: ${metrics.processed}, Successful: ${metrics.successful}`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual WHOIS information collection trigger
 */
export const triggerWhoisHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual WHOIS information collection triggered');
  
  const result = await processWhoisInfo(env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource('api-layer-whois');
  
  return new Response(`✅ WHOIS information collection completed. Processed: ${metrics.processed}, Successful: ${metrics.successful}`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual historical data cleanup trigger
 */
export const triggerCleanupHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual historical data cleanup triggered');
  
  const result = await cleanupOldHistoryData();
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(1, result ? 1 : 0, result ? 0 : 1);
  env.apiLogger?.setDataSource('database');
  
  return new Response(`✅ Historical data cleanup completed`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Manual full processing trigger
 */
export const triggerAllHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔧 Manual full processing triggered');
  
  let totalProcessed = 0;
  let totalSuccessful = 0;
  let totalFailed = 0;
  
  // Process traffic data
  const trafficResult = await processTrafficData(env.QUOTA_KV);
  const trafficMetrics = extractProcessingMetrics(trafficResult);
  totalProcessed += trafficMetrics.processed;
  totalSuccessful += trafficMetrics.successful;
  totalFailed += trafficMetrics.failed;
  console.log('✅ Traffic data collection completed');
  
  // Process domain ratings
  const drResult = await processDomainRatings(env.QUOTA_KV);
  const drMetrics = extractProcessingMetrics(drResult);
  totalProcessed += drMetrics.processed;
  totalSuccessful += drMetrics.successful;
  totalFailed += drMetrics.failed;
  console.log('✅ Domain rating collection completed');
  
  // Process external links
  const linksResult = await processExternalLinks(env.QUOTA_KV);
  const linksMetrics = extractProcessingMetrics(linksResult);
  totalProcessed += linksMetrics.processed;
  totalSuccessful += linksMetrics.successful;
  totalFailed += linksMetrics.failed;
  console.log('✅ External links collection completed');
  
  // Process indexing status
  const indexingResult = await processIndexingStatus(env.QUOTA_KV);
  const indexingMetrics = extractProcessingMetrics(indexingResult);
  totalProcessed += indexingMetrics.processed;
  totalSuccessful += indexingMetrics.successful;
  totalFailed += indexingMetrics.failed;
  console.log('✅ Indexing status check completed');
  
  // Process WHOIS information
  const whoisResult = await processWhoisInfo(env.QUOTA_KV);
  const whoisMetrics = extractProcessingMetrics(whoisResult);
  totalProcessed += whoisMetrics.processed;
  totalSuccessful += whoisMetrics.successful;
  totalFailed += whoisMetrics.failed;
  console.log('✅ WHOIS information collection completed');
  
  // Cleanup historical data
  const cleanupResult = await cleanupOldHistoryData();
  totalProcessed += 1;
  totalSuccessful += cleanupResult ? 1 : 0;
  totalFailed += cleanupResult ? 0 : 1;
  console.log('✅ Historical data cleanup completed');
  
  // Log combined processing metrics
  env.apiLogger?.setProcessingMetrics(totalProcessed, totalSuccessful, totalFailed);
  env.apiLogger?.setDataSource('batch-processing');
  
  return new Response(`✅ All processing completed successfully. Total processed: ${totalProcessed}, Successful: ${totalSuccessful}, Failed: ${totalFailed}`, {
    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
  });
});

/**
 * Domain rating API endpoint
 */
export const apiDomainRatingHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🔍 API: Getting domain rating for ${domain}`);
  
  const result = await getDomainRating(domain, env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource(result.cached ? 'cache' : 'ahrefs-api');
  env.apiLogger?.setCacheHit(!!result.cached);
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'DOMAIN_RATING_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * External links API endpoint
 */
export const apiExternalLinksHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🔍 API: Getting external links for ${domain}`);
  
  const result = await getExternalLinks(domain, env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource(result.cached ? 'cache' : result.source || 'google-api');
  env.apiLogger?.setCacheHit(!!result.cached);
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'EXTERNAL_LINKS_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * Traffic data API endpoint
 */
export const apiTrafficHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🔍 API: Getting traffic data for ${domain}`);
  
  const result = await getTrafficData(domain, env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource(result.cached ? 'cache' : 'traffic-api');
  env.apiLogger?.setCacheHit(!!result.cached);
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'TRAFFIC_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * Indexing status API endpoint
 */
export const apiIndexingHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const projectUrl = url.searchParams.get('projectUrl');
  const sourceDomain = url.searchParams.get('sourceDomain');
  
  if (!projectUrl || !sourceDomain) {
    throw new Error('Missing required parameters: projectUrl and sourceDomain');
  }
  
  console.log(`🔍 API: Checking indexing status for ${projectUrl} on ${sourceDomain}`);
  
  const result = await getIndexingStatus(projectUrl, sourceDomain, env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource(result.cached ? 'cache' : 'google-search-api');
  env.apiLogger?.setCacheHit(!!result.cached);
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'INDEXING_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * WHOIS information API endpoint
 */
export const apiWhoisHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🔍 API: Getting WHOIS information for ${domain}`);
  
  const result = await getWhoisInfo(domain, env.QUOTA_KV);
  const metrics = extractProcessingMetrics(result);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(metrics.processed, metrics.successful, metrics.failed);
  env.apiLogger?.setDataSource(result.cached ? 'cache' : (result.method || 'unknown'));
  env.apiLogger?.setCacheHit(!!result.cached);
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'WHOIS_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    method: result.method || 'unknown',
    fallback_used: result.fallback_used || false,
    cached: result.cached || false,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * RDAP connectivity test API endpoint
 */
export const apiRdapTestHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🧪 API: Testing RDAP connectivity for ${domain}`);
  
  const { testRdapConnectivity } = await import('../services/rdap.js');
  const result = await testRdapConnectivity(domain);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(1, result.error ? 0 : 1, result.error ? 1 : 0);
  env.apiLogger?.setDataSource('rdap-test');
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'RDAP_TEST_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * RDAP statistics API endpoint
 */
export const apiRdapStatsHandler = withApiLogging(async (request, env, ctx) => {
  console.log(`📊 API: Getting RDAP statistics`);
  
  const { getRdapStats } = await import('../services/rdap.js');
  const result = await getRdapStats();
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(1, result.error ? 0 : 1, result.error ? 1 : 0);
  env.apiLogger?.setDataSource('rdap-stats');
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'RDAP_STATS_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * WHOIS/RDAP connectivity test API endpoint
 */
export const apiWhoisTestHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🧪 API: Testing WHOIS/RDAP connectivity for ${domain}`);
  
  const { testWhoisConnectivity } = await import('../services/whois.js');
  const result = await testWhoisConnectivity(domain);
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(1, result.error ? 0 : 1, result.error ? 1 : 0);
  env.apiLogger?.setDataSource('whois-rdap-test');
  
  if (result.error) {
    env.apiLogger?.setError(result.error, 'WHOIS_TEST_ERROR');
  }
  
  return new Response(JSON.stringify({
    success: !result.error,
    data: result,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * Force RDAP query API endpoint (for testing)
 */
export const apiForceRdapHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🔬 API: Force RDAP query for ${domain}`);
  
  const { forceRdapQuery } = await import('../services/whois.js');
  
  try {
    const result = await forceRdapQuery(domain);
    
    // Log processing metrics
    env.apiLogger?.setProcessingMetrics(1, 1, 0);
    env.apiLogger?.setDataSource('rdap-forced');
    
    return new Response(JSON.stringify({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  } catch (error) {
    // Log processing metrics
    env.apiLogger?.setProcessingMetrics(1, 0, 1);
    env.apiLogger?.setError(error.message, 'FORCE_RDAP_ERROR');
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  }
});

/**
 * Force traditional WHOIS API query endpoint (for testing)
 */
export const apiForceWhoisHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const domain = url.searchParams.get('domain');
  const mode = url.searchParams.get('mode') || 'cost_first';
  
  if (!domain) {
    throw new Error('Missing required parameter: domain');
  }
  
  console.log(`🔬 API: Force traditional WHOIS API query for ${domain}`);
  
  const { forceWhoisApiQuery } = await import('../services/whois.js');
  
  try {
    const result = await forceWhoisApiQuery(domain, mode);
    
    // Log processing metrics
    env.apiLogger?.setProcessingMetrics(1, 1, 0);
    env.apiLogger?.setDataSource('whois-api-forced');
    
    return new Response(JSON.stringify({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  } catch (error) {
    // Log processing metrics
    env.apiLogger?.setProcessingMetrics(1, 0, 1);
    env.apiLogger?.setError(error.message, 'FORCE_WHOIS_ERROR');
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  }
});