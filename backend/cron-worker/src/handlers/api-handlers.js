/**
 * API Route Handlers Index
 * Re-exports all handlers from different modules organized by categories
 */

// =============================================================================
// SYSTEM & MONITORING HANDLERS
// =============================================================================

// Health check
export {
  healthHandler
} from './core-handlers.js';

// Logging and statistics
export {
  apiStatsHandler,
  apiLogsWebHandler
} from './logs-handlers.js';

// =============================================================================
// QUOTA MANAGEMENT HANDLERS  
// =============================================================================

export {
  apiQuotaStatusHandler,
  apiQuotaUpdateHandler,
  apiQuotaResetHandler,
  apiQuotaWebHandler
} from './quota-handlers.js';

// =============================================================================
// TRAFFIC DATA HANDLERS
// =============================================================================

// Manual triggers
export {
  triggerTrafficHandler
} from './core-handlers.js';

// API endpoints
export {
  apiTrafficHandler
} from './core-handlers.js';

// =============================================================================
// DOMAIN RATING HANDLERS
// =============================================================================

// Manual triggers
export {
  triggerDomainRatingHandler
} from './core-handlers.js';

// API endpoints
export {
  apiDomainRatingHandler
} from './core-handlers.js';

// =============================================================================
// EXTERNAL LINKS HANDLERS
// =============================================================================

// Manual triggers
export {
  triggerLinksHandler
} from './core-handlers.js';

// API endpoints
export {
  apiExternalLinksHandler
} from './core-handlers.js';

// =============================================================================
// INDEXING STATUS HANDLERS
// =============================================================================

// Manual triggers
export {
  triggerIndexingHandler
} from './core-handlers.js';

// API endpoints
export {
  apiIndexingHandler
} from './core-handlers.js';

// =============================================================================
// WHOIS INFORMATION HANDLERS
// =============================================================================

// Manual triggers
export {
  triggerWhoisHandler
} from './core-handlers.js';

// API endpoints
export {
  apiWhoisHandler
} from './core-handlers.js';

// =============================================================================
// RDAP (Registration Data Access Protocol) HANDLERS
// =============================================================================

// RDAP testing and statistics
export {
  apiRdapTestHandler,
  apiRdapStatsHandler,
  apiWhoisTestHandler,
  apiForceRdapHandler,
  apiForceWhoisHandler
} from './core-handlers.js';

// =============================================================================
// BATCH PROCESSING HANDLERS
// =============================================================================

// Manual triggers for multiple operations
export {
  triggerAllHandler,
  triggerCleanupHandler
} from './core-handlers.js';