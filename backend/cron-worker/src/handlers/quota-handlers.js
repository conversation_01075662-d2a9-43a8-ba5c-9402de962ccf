/**
 * API Quota Management Handlers
 * Organized by functionality: Status, Configuration, Reset, Web Interface
 */

import { withApiLogging } from '../lib/api-middleware.js';
import { quotaManager } from '../lib/quota-manager.js';
import { kvStore } from '../lib/kv-store.js';
import { API_CONFIGS } from '../lib/api-adapter.js';

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Get all API hosts with their configurations for quota management
 */
function getAllApiHosts() {
  const hosts = new Map();
  
  // Extract from API_CONFIGS
  for (const [category, config] of Object.entries(API_CONFIGS)) {
    if (config.sources) {
      for (const source of config.sources) {
        hosts.set(source.host, {
          name: `${source.id} (${config.name})`,
          monthly_limit: source.monthly_limit,
          daily_limit: source.daily_limit,
          allow_pay: source.allow_pay,
          description: `${config.name} - ${source.id}`,
          category: category
        });
      }
    }
  }
  
  return hosts;
}

/**
 * Get quota configuration for API host
 */
export function getQuotaConfig(apiHost) {
  const allHosts = getAllApiHosts();
  return allHosts.get(apiHost) || {
    name: 'Unknown API',
    monthly_limit: 100,
    daily_limit: 10,
    allow_pay: true,
    description: 'Default quota for unspecified APIs',
    category: 'unknown'
  };
}

// =============================================================================
// QUOTA STATUS HANDLERS
// =============================================================================

/**
 * API quota status endpoint
 */
export const apiQuotaStatusHandler = withApiLogging(async (request, env, ctx) => {
  console.log('🔍 API: Getting API quota status');

  // Initialize KV store with environment
  if (env.QUOTA_KV) {
    kvStore.setNamespace(env.QUOTA_KV);
    quotaManager.init(env.QUOTA_KV);
  }

  const quotaStatus = {};
  const allHosts = getAllApiHosts();
  
  for (const [apiHost, config] of allHosts) {
    const monthlyUsage = await kvStore.getMonthlyUsage(apiHost);
    const dailyUsage = await kvStore.getDailyUsage(apiHost);
    
    quotaStatus[apiHost] = {
      name: config.name,
      description: config.description,
      category: config.category,
      monthly: {
        used: monthlyUsage,
        limit: config.monthly_limit,
        remaining: config.monthly_limit ? config.monthly_limit - monthlyUsage : null,
        percentage: config.monthly_limit ? Math.round((monthlyUsage / config.monthly_limit) * 100) : 0
      },
      daily: {
        used: dailyUsage,
        limit: config.daily_limit,
        remaining: config.daily_limit ? config.daily_limit - dailyUsage : null,
        percentage: config.daily_limit ? Math.round((dailyUsage / config.daily_limit) * 100) : 0
      },
      allow_pay: config.allow_pay,
      can_make_request: await quotaManager.canMakeRequest(`https://${apiHost}/test`)
    };
  }

  env.apiLogger?.setProcessingMetrics(Object.keys(quotaStatus).length, Object.keys(quotaStatus).length, 0);
  env.apiLogger?.setDataSource('cloudflare-kv');

  return new Response(JSON.stringify({
    success: true,
    data: quotaStatus,
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

// =============================================================================
// QUOTA CONFIGURATION HANDLERS
// =============================================================================

/**
 * Update API quota configuration (read-only - configurations are now centralized)
 */
export const apiQuotaUpdateHandler = withApiLogging(async (request, env, ctx) => {
  if (request.method !== 'POST') {
    throw new Error('Method not allowed');
  }

  return new Response(JSON.stringify({
    success: false,
    error: 'Quota configuration updates are disabled. Please update API_CONFIGS in api-adapter.js',
    message: 'API configurations are now centralized in api-adapter.js for better maintainability',
    timestamp: new Date().toISOString()
  }), {
    status: 403,
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

// =============================================================================
// QUOTA RESET HANDLERS
// =============================================================================

/**
 * Reset quota usage for specific API
 */
export const apiQuotaResetHandler = withApiLogging(async (request, env, ctx) => {
  if (request.method !== 'POST') {
    throw new Error('Method not allowed');
  }

  // Initialize KV store with environment
  if (env.QUOTA_KV) {
    kvStore.setNamespace(env.QUOTA_KV);
    quotaManager.init(env.QUOTA_KV);
  }

  const data = await request.json();
  const { apiHost, resetType } = data; // resetType: 'monthly', 'daily', or 'both'

  if (!apiHost) {
    throw new Error('Missing apiHost parameter');
  }

  let resetCount = 0;
  
  if (resetType === 'monthly' || resetType === 'both') {
    const monthlyKey = kvStore.getMonthlyKey(apiHost);
    await kvStore.delete(monthlyKey);
    resetCount++;
  }
  
  if (resetType === 'daily' || resetType === 'both') {
    const dailyKey = kvStore.getDailyKey(apiHost);
    await kvStore.delete(dailyKey);
    resetCount++;
  }

  console.log(`🔧 Reset ${resetType} quota for ${apiHost}`);

  env.apiLogger?.setProcessingMetrics(1, 1, 0);
  env.apiLogger?.setDataSource('cloudflare-kv');

  return new Response(JSON.stringify({
    success: true,
    data: {
      apiHost,
      resetType,
      resetCount
    },
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

// =============================================================================
// WEB INTERFACE HANDLERS
// =============================================================================

/**
 * API quota management web interface
 */
export const apiQuotaWebHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  
  // Handle authentication via query parameter or form submission
  let authToken = url.searchParams.get('token');
  
  // If no token in query params and this is a POST request, try to get from form data
  if (!authToken && request.method === 'POST') {
    try {
      const contentType = request.headers.get('content-type') || '';
      if (contentType.includes('application/x-www-form-urlencoded') || contentType.includes('multipart/form-data')) {
        const formData = await request.formData();
        authToken = formData.get('token');
      }
    } catch (error) {
      console.warn('Error parsing form data:', error);
      // Continue without token, will show login form
    }
  }
                   
  const validToken = env.AUTH_TOKEN || env.BACKEND_WORKER_API_KEY;
  
  // Check if auth token is provided and valid
  if (!authToken || !validToken || authToken !== validToken) {
    console.log('Authentication failed:', { 
      hasAuthToken: !!authToken, 
      hasValidToken: !!validToken,
      tokensMatch: authToken === validToken
    });
    return new Response(generateQuotaLoginForm(), {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  }

  // Authentication successful, proceed with quota management
  // Initialize KV store with environment
  if (env.QUOTA_KV) {
    kvStore.setNamespace(env.QUOTA_KV);
    quotaManager.init(env.QUOTA_KV);
  }

  // Get quota status for all APIs
  const quotaStatus = {};
  const allHosts = getAllApiHosts();
  
  for (const [apiHost, config] of allHosts) {
    const monthlyUsage = await kvStore.getMonthlyUsage(apiHost);
    const dailyUsage = await kvStore.getDailyUsage(apiHost);
    
    quotaStatus[apiHost] = {
      ...config,
      monthly: {
        used: monthlyUsage,
        limit: config.monthly_limit,
        remaining: config.monthly_limit ? config.monthly_limit - monthlyUsage : null,
        percentage: config.monthly_limit ? Math.round((monthlyUsage / config.monthly_limit) * 100) : 0
      },
      daily: {
        used: dailyUsage,
        limit: config.daily_limit,
        remaining: config.daily_limit ? config.daily_limit - dailyUsage : null,
        percentage: config.daily_limit ? Math.round((dailyUsage / config.daily_limit) * 100) : 0
      },
      can_make_request: await quotaManager.canMakeRequest(`https://${apiHost}/test`)
    };
  }

  env.apiLogger?.setProcessingMetrics(Object.keys(quotaStatus).length, Object.keys(quotaStatus).length, 0);
  env.apiLogger?.setDataSource('cloudflare-kv');

  return new Response(generateQuotaManagementHTML(quotaStatus, authToken), {
    headers: { 'Content-Type': 'text/html; charset=utf-8' }
  });
});

// =============================================================================
// HTML GENERATION UTILITIES
// =============================================================================

/**
 * Generate quota management login form
 */
function generateQuotaLoginForm() {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Quota Management</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        .logo p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-1px);
        }
        .debug-info {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 11px;
            color: #666;
            word-break: break-all;
        }
        .error-message {
            margin-top: 10px;
            padding: 10px;
            background: #fed7d7;
            color: #742a2a;
            border-radius: 4px;
            font-size: 14px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>📊 API Quota</h1>
            <p>Management Dashboard</p>
        </div>
        <form method="POST" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="token">AUTH_TOKEN:</label>
                <input type="password" id="token" name="token" required 
                       placeholder="Enter your authentication token">
            </div>
            <button type="submit">Access Dashboard</button>
        </form>
        <div class="error-message" id="error-message">
            Please enter a valid authentication token.
        </div>
        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Current URL: <span id="current-url">${typeof window !== 'undefined' ? window.location.href : 'N/A'}</span><br>
            Timestamp: ${new Date().toISOString()}<br>
            <small>If you're having trouble, check that your AUTH_TOKEN environment variable is set correctly.</small>
        </div>
    </div>
    
    <script>
        // Update current URL in debug info
        if (typeof window !== 'undefined') {
            document.getElementById('current-url').textContent = window.location.href;
        }
        
        function validateForm() {
            const token = document.getElementById('token').value.trim();
            const errorDiv = document.getElementById('error-message');
            
            if (!token) {
                errorDiv.style.display = 'block';
                errorDiv.textContent = 'Please enter an authentication token.';
                return false;
            }
            
            if (token.length < 10) {
                errorDiv.style.display = 'block';
                errorDiv.textContent = 'Authentication token seems too short. Please check your token.';
                return false;
            }
            
            errorDiv.style.display = 'none';
            return true;
        }
        
        // Show any URL-based error messages
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        if (error) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.style.display = 'block';
            errorDiv.textContent = decodeURIComponent(error);
        }
    </script>
</body>
</html>`;
}

/**
 * Generate grouped quota display HTML
 */
function generateGroupedQuotaDisplay(quotaStatus, authToken) {
  // Group quotas by category
  const groupedQuotas = {};
  const categoryNames = {
    'traffic': '📊 Traffic Data APIs',
    'domain_rating': '⭐ Domain Rating APIs',
    'external_links': '🔗 External Links APIs',
    'indexing': '🔍 Indexing Status APIs',
    'unknown': '❓ Other APIs'
  };

  // Group APIs by category
  for (const [apiHost, quota] of Object.entries(quotaStatus)) {
    const category = quota.category || 'unknown';
    if (!groupedQuotas[category]) {
      groupedQuotas[category] = [];
    }
    groupedQuotas[category].push({ apiHost, ...quota });
  }

  // Generate HTML for each group
  return Object.entries(groupedQuotas).map(([category, quotas]) => `
    <div class="category-section">
      <div class="category-header">
        <h2>${categoryNames[category] || category}</h2>
        <div class="category-summary">
          ${quotas.length} API${quotas.length > 1 ? 's' : ''} | 
          ${quotas.filter(q => q.can_make_request).length} Available | 
          ${quotas.filter(q => !q.can_make_request).length} Limited
        </div>
      </div>
      <div class="quota-grid">
        ${quotas.map(quota => `
        <div class="quota-card">
          <div class="quota-header">
            <h3>${quota.name}</h3>
            <p>${quota.description}</p>
            <span class="status-badge ${quota.can_make_request ? 'status-available' : 'status-limited'}">
              ${quota.can_make_request ? 'Available' : 'Quota Exceeded'}
            </span>
          </div>
          <div class="quota-body">
            <div class="usage-stats">
              <div class="stat">
                <div class="stat-value">${quota.monthly.used}/${quota.monthly.limit || '∞'}</div>
                <div class="stat-label">Monthly Usage</div>
                <div class="usage-bar">
                  <div class="usage-progress ${quota.monthly.percentage > 80 ? 'progress-high' : quota.monthly.percentage > 60 ? 'progress-medium' : 'progress-low'}" 
                       style="width: ${Math.min(quota.monthly.percentage || 0, 100)}%"></div>
                </div>
              </div>
              <div class="stat">
                <div class="stat-value">${quota.daily.used}/${quota.daily.limit || '∞'}</div>
                <div class="stat-label">Daily Usage</div>
                <div class="usage-bar">
                  <div class="usage-progress ${quota.daily.percentage > 80 ? 'progress-high' : quota.daily.percentage > 60 ? 'progress-medium' : 'progress-low'}" 
                       style="width: ${Math.min(quota.daily.percentage || 0, 100)}%"></div>
                </div>
              </div>
            </div>
            
            <div class="controls">
              <button class="btn btn-secondary" onclick="resetQuota('${quota.apiHost}', 'monthly')">
                Reset Monthly
              </button>
              <button class="btn btn-secondary" onclick="resetQuota('${quota.apiHost}', 'daily')">
                Reset Daily
              </button>
              <button class="btn btn-danger" onclick="resetQuota('${quota.apiHost}', 'both')">
                Reset Both
              </button>
            </div>
            
            <p style="margin-top: 15px; font-size: 12px; color: #666;">
              Allow Pay: <strong>${quota.allow_pay ? 'Yes' : 'No'}</strong> | 
              Host: <code>${quota.apiHost}</code>
            </p>
          </div>
        </div>
        `).join('')}
      </div>
    </div>
  `).join('');
}

/**
 * Generate quota management dashboard HTML
 */
function generateQuotaManagementHTML(quotaStatus, authToken) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Quota Management</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
        }
        .quota-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .quota-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .quota-header {
            background: #f8fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        .quota-header h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 18px;
        }
        .quota-header p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .quota-body {
            padding: 20px;
        }
        .usage-bar {
            background: #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
            height: 8px;
        }
        .usage-progress {
            height: 100%;
            transition: width 0.3s ease;
        }
        .progress-low { background: #48bb78; }
        .progress-medium { background: #ed8936; }
        .progress-high { background: #f56565; }
        .usage-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .stat {
            text-align: center;
            padding: 10px;
            background: #f7fafc;
            border-radius: 6px;
        }
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-available {
            background: #c6f6d5;
            color: #22543d;
        }
        .status-limited {
            background: #fed7d7;
            color: #742a2a;
        }
        .controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        .btn-danger {
            background: #f56565;
            color: white;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .refresh-info {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
        .category-section {
            margin-bottom: 40px;
        }
        .category-header {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .category-header h2 {
            margin: 0 0 8px 0;
            color: #2d3748;
            font-size: 20px;
            font-weight: 600;
        }
        .category-summary {
            color: #4a5568;
            font-size: 14px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 API Quota Management</h1>
        <p>Monitor and manage API usage limits in real-time</p>
    </div>
    
    ${generateGroupedQuotaDisplay(quotaStatus, authToken)}
    
    <div class="refresh-info">
        <button class="btn btn-primary" onclick="location.reload()">🔄 Refresh Data</button>
        <p>Auto-refresh every 30 seconds</p>
    </div>
    
    <script>
        async function resetQuota(apiHost, resetType) {
            if (!confirm(\`Are you sure you want to reset \${resetType} quota for \${apiHost}?\`)) {
                return;
            }
            
            try {
                const response = await fetch('/api/quota/reset?token=${authToken}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        apiHost: apiHost,
                        resetType: resetType
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(\`Successfully reset \${resetType} quota for \${apiHost}\`);
                    location.reload();
                } else {
                    alert('Error: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }
        
        // Auto-refresh every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>`;
}