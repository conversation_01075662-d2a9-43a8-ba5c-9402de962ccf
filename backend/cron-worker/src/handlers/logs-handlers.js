/**
 * API Logs Handlers
 * Handles API usage statistics and logs web interface
 */

import { withApiLogging } from '../lib/api-middleware.js';
import { getUsageStats, getRecentLogs } from '../lib/d1-logger.js';

/**
 * API usage statistics endpoint
 */
export const apiStatsHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  const endpoint = url.searchParams.get('endpoint');
  const startDate = url.searchParams.get('startDate');
  const endDate = url.searchParams.get('endDate');
  const granularity = url.searchParams.get('granularity') || 'hour';
  
  console.log('🔍 API: Getting usage statistics');
  
  if (!env.DB) {
    throw new Error('D1 database not available');
  }
  
  const stats = await getUsageStats(env.DB, {
    endpoint,
    startDate,
    endDate,
    granularity
  });
  
  const recentLogs = await getRecentLogs(env.DB, {
    limit: 50,
    endpoint
  });
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(1, 1, 0);
  env.apiLogger?.setDataSource('d1-database');
  env.apiLogger?.setCacheHit(false);
  
  return new Response(JSON.stringify({
    success: true,
    data: {
      statistics: stats,
      recent_logs: recentLogs,
      query_params: { endpoint, startDate, endDate, granularity }
    },
    timestamp: new Date().toISOString()
  }), {
    headers: { 
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
});

/**
 * API Logs Web Interface
 * Shows API logs in a simple HTML table with authentication
 */
export const apiLogsWebHandler = withApiLogging(async (request, env, ctx) => {
  const url = new URL(request.url);
  
  // Handle authentication via query parameter or form submission
  let authToken = url.searchParams.get('token');
  
  // If no token in query params and this is a POST request, try to get from form data
  if (!authToken && request.method === 'POST') {
    try {
      const contentType = request.headers.get('content-type') || '';
      if (contentType.includes('application/x-www-form-urlencoded') || contentType.includes('multipart/form-data')) {
        const formData = await request.formData();
        authToken = formData.get('token');
      }
    } catch (error) {
      console.warn('Error parsing form data:', error);
      // Continue without token, will show login form
    }
  }
                   
  const validToken = env.AUTH_TOKEN || env.BACKEND_WORKER_API_KEY;
  
  // Check if auth token is provided and valid
  if (!authToken || !validToken || authToken !== validToken) {
    console.log('Authentication failed:', { 
      hasAuthToken: !!authToken, 
      hasValidToken: !!validToken,
      tokensMatch: authToken === validToken
    });
    return new Response(generateLoginForm(), {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  }
  
  // Authentication successful, proceed with logs display
  if (!env.DB) {
    throw new Error('D1 database not available');
  }
  
  // Get recent logs and stats
  const recentLogs = await getRecentLogs(env.DB, { limit: 100 });
  const stats = await getUsageStats(env.DB, { granularity: 'hour' });
  
  // Log processing metrics
  env.apiLogger?.setProcessingMetrics(recentLogs.length, recentLogs.length, 0);
  env.apiLogger?.setDataSource('d1-database');
  env.apiLogger?.setCacheHit(false);
  
  return new Response(generateLogsHTML(recentLogs, stats), {
    headers: { 'Content-Type': 'text/html; charset=utf-8' }
  });
});

/**
 * Generate login form HTML
 */
function generateLoginForm() {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkTrackPro API Logs</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        .logo p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-1px);
        }
        .note {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        .debug-info {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 11px;
            color: #666;
            word-break: break-all;
        }
        .error-message {
            margin-top: 10px;
            padding: 10px;
            background: #fed7d7;
            color: #742a2a;
            border-radius: 4px;
            font-size: 14px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🔒 LinkTrackPro</h1>
            <p>API Logs Dashboard</p>
        </div>
        <form method="POST" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="token">AUTH_TOKEN:</label>
                <input type="password" id="token" name="token" required 
                       placeholder="Enter your authentication token">
            </div>
            <button type="submit">Access Logs</button>
        </form>
        <div class="error-message" id="error-message">
            Please enter a valid authentication token.
        </div>
        <div class="note">
            Enter your AUTH_TOKEN to view API logs and statistics.
        </div>
        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Current URL: <span id="current-url">${typeof window !== 'undefined' ? window.location.href : 'N/A'}</span><br>
            Timestamp: ${new Date().toISOString()}<br>
            <small>If you're having trouble, check that your AUTH_TOKEN environment variable is set correctly.</small>
        </div>
    </div>
    
    <script>
        // Update current URL in debug info
        if (typeof window !== 'undefined') {
            document.getElementById('current-url').textContent = window.location.href;
        }
        
        function validateForm() {
            const token = document.getElementById('token').value.trim();
            const errorDiv = document.getElementById('error-message');
            
            if (!token) {
                errorDiv.style.display = 'block';
                errorDiv.textContent = 'Please enter an authentication token.';
                return false;
            }
            
            if (token.length < 10) {
                errorDiv.style.display = 'block';
                errorDiv.textContent = 'Authentication token seems too short. Please check your token.';
                return false;
            }
            
            errorDiv.style.display = 'none';
            return true;
        }
        
        // Show any URL-based error messages
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        if (error) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.style.display = 'block';
            errorDiv.textContent = decodeURIComponent(error);
        }
    </script>
</body>
</html>`;
}

/**
 * Generate logs dashboard HTML
 */
function generateLogsHTML(logs, statsData) {
  const stats = statsData?.stats || [];
  
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkTrackPro API Logs Dashboard</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .stat-card .value {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-card .label {
            color: #666;
            font-size: 12px;
        }
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background: #f8fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        .table-header h2 {
            margin: 0;
            color: #333;
            font-size: 20px;
        }
        .table-controls {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 15px;
        }
        .filter-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        .filter-controls label {
            font-size: 12px;
            font-weight: 600;
            color: #4a5568;
            margin-right: 5px;
        }
        .filter-controls select {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 13px;
            background: white;
            cursor: pointer;
        }
        .filter-controls select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .action-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .refresh-btn, .clear-filter-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .refresh-btn:hover, .clear-filter-btn:hover {
            background: #5a67d8;
        }
        .clear-filter-btn {
            background: #e53e3e;
        }
        .clear-filter-btn:hover {
            background: #c53030;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        tbody tr:hover {
            background-color: #f7fafc;
        }
        .status-success {
            color: #38a169;
            font-weight: 600;
        }
        .status-error {
            color: #e53e3e;
            font-weight: 600;
        }
        .method {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        .endpoint {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #4a5568;
        }
        .response-time {
            font-weight: 600;
            color: #667eea;
        }
        .timestamp {
            color: #718096;
            font-size: 12px;
        }
        .response-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 11px;
            color: #4a5568;
            background: #f7fafc;
            padding: 4px 6px;
            border-radius: 3px;
            cursor: pointer;
        }
        .no-data {
            text-align: center;
            color: #666;
            padding: 40px;
            font-style: italic;
        }
        .auto-refresh {
            display: inline-block;
            margin-left: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 LinkTrackPro API Dashboard</h1>
        <p>Real-time API logs and performance monitoring</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Total Requests</h3>
            <div class="value">${logs.length}</div>
            <div class="label">Last 100 requests</div>
        </div>
        <div class="stat-card">
            <h3>Success Rate</h3>
            <div class="value">${Math.round((logs.filter(l => l.success).length / Math.max(logs.length, 1)) * 100)}%</div>
            <div class="label">Successful responses</div>
        </div>
        <div class="stat-card">
            <h3>Avg Response Time</h3>
            <div class="value">${logs.length ? Math.round(logs.reduce((sum, l) => sum + l.response_time_ms, 0) / logs.length) : 0}ms</div>
            <div class="label">Average latency</div>
        </div>
        <div class="stat-card">
            <h3>Active Endpoints</h3>
            <div class="value">${new Set(logs.map(l => l.endpoint_path)).size}</div>
            <div class="label">Unique endpoints</div>
        </div>
    </div>
    
    <div class="table-container">
        <div class="table-header">
            <h2>📊 Recent API Calls</h2>
            <div class="table-controls">
                <div class="filter-controls">
                    <label for="endpoint-filter">Filter by Endpoint:</label>
                    <select id="endpoint-filter" onchange="filterLogs()">
                        <option value="">All Endpoints</option>
                        ${[...new Set(logs.map(log => log.endpoint_path))].sort().map(endpoint => 
                            `<option value="${endpoint}">${endpoint}</option>`
                        ).join('')}
                    </select>
                    <label for="status-filter">Filter by Status:</label>
                    <select id="status-filter" onchange="filterLogs()">
                        <option value="">All Status</option>
                        <option value="success">Success Only</option>
                        <option value="error">Error Only</option>
                    </select>
                    <button class="clear-filter-btn" onclick="clearFilters()">Clear Filters</button>
                </div>
                <div class="action-controls">
                    <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
                    <span class="auto-refresh">Auto-refresh: 30s</span>
                </div>
            </div>
        </div>
        
        ${logs.length === 0 ? '<div class="no-data">No API logs found</div>' : `
        <table>
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Method</th>
                    <th>Endpoint</th>
                    <th>Status</th>
                    <th>Response Time</th>
                    <th>Data Source</th>
                    <th>Items</th>
                    <th>Response Preview</th>
                </tr>
            </thead>
            <tbody>
                ${logs.map(log => `
                <tr>
                    <td class="timestamp">${new Date(log.started_at).toLocaleString()}</td>
                    <td><span class="method">${log.method}</span></td>
                    <td class="endpoint">${log.endpoint_path}</td>
                    <td class="${log.success ? 'status-success' : 'status-error'}">
                        ${log.status_code} ${log.success ? '✅' : '❌'}
                    </td>
                    <td class="response-time">${log.response_time_ms}ms</td>
                    <td>${log.data_source || 'N/A'}</td>
                    <td>${log.items_processed || 0}</td>
                    <td class="response-preview" title="${(log.response_body || '').replace(/"/g, '&quot;')}">
                        ${log.response_body ? (log.response_body.length > 100 ? log.response_body.substring(0, 100) + '...' : log.response_body) : 'No response body'}
                    </td>
                </tr>
                `).join('')}
            </tbody>
        </table>
        `}
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
        
        // Add click handler for response preview tooltips
        document.querySelectorAll('.response-preview').forEach(cell => {
            cell.addEventListener('click', function() {
                const fullResponse = this.getAttribute('title');
                if (fullResponse && fullResponse !== 'No response body') {
                    try {
                        const formatted = JSON.stringify(JSON.parse(fullResponse), null, 2);
                        alert(formatted);
                    } catch {
                        alert(fullResponse);
                    }
                }
            });
        });
        
        // Filter logs functionality
        function filterLogs() {
            const endpointFilter = document.getElementById('endpoint-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            const rows = document.querySelectorAll('tbody tr');
            
            let visibleCount = 0;
            
            rows.forEach(row => {
                const endpoint = row.querySelector('.endpoint').textContent;
                const statusCell = row.querySelector('[class*="status-"]');
                const isSuccess = statusCell.classList.contains('status-success');
                
                let showRow = true;
                
                // Apply endpoint filter
                if (endpointFilter && endpoint !== endpointFilter) {
                    showRow = false;
                }
                
                // Apply status filter
                if (statusFilter === 'success' && !isSuccess) {
                    showRow = false;
                } else if (statusFilter === 'error' && isSuccess) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });
            
            // Update visible count indicator
            updateFilterCount(visibleCount, rows.length);
        }
        
        function clearFilters() {
            document.getElementById('endpoint-filter').value = '';
            document.getElementById('status-filter').value = '';
            filterLogs();
        }
        
        function updateFilterCount(visible, total) {
            let indicator = document.getElementById('filter-indicator');
            if (!indicator) {
                indicator = document.createElement('span');
                indicator.id = 'filter-indicator';
                indicator.style.cssText = 'font-size: 12px; color: #666; margin-left: 10px;';
                document.querySelector('.table-header h2').appendChild(indicator);
            }
            
            if (visible < total) {
                indicator.textContent = \` (showing \${visible} of \${total})\`;
            } else {
                indicator.textContent = '';
            }
        }
        
        // Initialize filter count on page load
        document.addEventListener('DOMContentLoaded', function() {
            const totalRows = document.querySelectorAll('tbody tr').length;
            updateFilterCount(totalRows, totalRows);
        });
    </script>
</body>
</html>`;
}