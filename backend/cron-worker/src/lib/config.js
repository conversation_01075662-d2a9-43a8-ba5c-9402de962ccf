/**
 * Configuration management for LinkTrackPro Cron Worker
 */

export const config = {
  // API Keys
  SIMILARWEB_API_KEY: '',
  AHREFS_API_KEY: '',
  GOOGLE_API_KEY: '',
  GOOGLE_SEARCH_ENGINE_ID: '',
  RAPIDAPI_KEY: '',
  EXA_API_KEY: '',
  API_LAYER_KEY: '',
  
  // Feishu webhook URL
  FEISHU_WEBHOOK_URL: '',
  
  // Database connection
  SUPABASE_URL: '',
  SUPABASE_KEY: '',
  
  // Batch sizes for API calls (to optimize costs)
  BATCH_SIZE: 50,
  
  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY_MS: 1000,

  // API auth token (can be set via AUTH_TOKEN or BACKEND_WORKER_API_KEY)
  AUTH_TOKEN: ''
};

/**
 * Load configuration from environment variables
 */
export function loadConfig(env) {
  config.SIMILARWEB_API_KEY = env.SIMILARWEB_API_KEY || config.SIMILARWEB_API_KEY;
  config.AHREFS_API_KEY = env.AHREFS_API_KEY || config.AHREFS_API_KEY;
  config.GOOGLE_API_KEY = env.GOOGLE_API_KEY || config.GOOGLE_API_KEY;
  config.GOOGLE_SEARCH_ENGINE_ID = env.GOOGLE_SEARCH_ENGINE_ID || config.GOOGLE_SEARCH_ENGINE_ID;
  config.RAPIDAPI_KEY = env.RAPIDAPI_KEY || config.RAPIDAPI_KEY;
  config.EXA_API_KEY = env.EXA_API_KEY || config.EXA_API_KEY;
  config.API_LAYER_KEY = env.API_LAYER_KEY || config.API_LAYER_KEY;
  config.FEISHU_WEBHOOK_URL = env.FEISHU_WEBHOOK_URL || config.FEISHU_WEBHOOK_URL;
  config.SUPABASE_URL = env.SUPABASE_URL || config.SUPABASE_URL;
  config.SUPABASE_KEY = env.SUPABASE_KEY || config.SUPABASE_KEY;
  config.BATCH_SIZE = parseInt(env.BATCH_SIZE || config.BATCH_SIZE);
  config.MAX_RETRIES = parseInt(env.MAX_RETRIES || config.MAX_RETRIES);
  config.RETRY_DELAY_MS = parseInt(env.RETRY_DELAY_MS || config.RETRY_DELAY_MS);
  // Support both AUTH_TOKEN and BACKEND_WORKER_API_KEY for compatibility
  config.AUTH_TOKEN = env.AUTH_TOKEN || env.BACKEND_WORKER_API_KEY || config.AUTH_TOKEN;
} 
