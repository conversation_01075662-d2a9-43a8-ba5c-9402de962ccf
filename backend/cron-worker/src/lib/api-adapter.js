/**
 * Universal API Adapter Layer
 * Handles different API endpoints (RapidAPI, Direct APIs, Google APIs, etc.) 
 * with unified interface and quota management
 */

import { config } from './config.js';

/**
 * Unified API configuration with quota limits and selection strategies
 */
const API_CONFIGS = {
  // Traffic data collection APIs
  'traffic': {
    name: 'Traffic Data Collection',
    sources: [
      {
        id: 'similarweb_direct',
        host: 'data.similarweb.com',
        endpoint: 'traffic',
        priority: 1,
        monthly_limit: null, 
        daily_limit: null, 
        allow_pay: false,
        search_type: null
      },
      {
        id: 'similarweb_traffic',
        host: 'similarweb-traffic.p.rapidapi.com',
        endpoint: 'traffic',
        priority: 2,
        monthly_limit: 100,
        daily_limit: null,
        allow_pay: false,
        search_type: null
      },
      {
        id: 'similarweb_rapidapi',
        host: 'similarweb-insights.p.rapidapi.com',
        endpoint: 'traffic',
        priority: 3,
        monthly_limit: 100,
        daily_limit: null,
        allow_pay: false,
        search_type: null
      }
    ],
    fallback_strategy: 'empty_results'
  },

  // Indexing status check APIs
  'indexing': {
    name: 'Indexing Status Check',
    sources: [
      {
        id: 'google_link_site',
        host: 'www.googleapis.com',
        endpoint: 'indexing',
        priority: 1,
        monthly_limit: 10000, // Google free tier
        daily_limit: 100,
        allow_pay: true,
        search_type: 'link_site'
      },
      {
        id: 'google_site_search',
        host: 'www.googleapis.com',
        endpoint: 'indexing',
        priority: 2,
        monthly_limit: 10000, // Google free tier
        daily_limit: 100,
        allow_pay: true,
        search_type: 'site'
      },
      {
        id: 'exa_ai',
        host: 'api.exa.ai',
        endpoint: 'indexing',
        priority: 3,
        monthly_limit: 1000, // Exa free tier
        daily_limit: 50,
        allow_pay: true,
        search_type: null
      }
    ],
    fallback_strategy: 'not_indexed'
  },

  // External links discovery APIs
  'external_links': {
    name: 'External Links Discovery',
    sources: [
      {
        id: 'seo_traffic_authority_backlinks',
        host: 'seo-traffic-authority.p.rapidapi.com',
        endpoint: 'backlink-checker',
        priority: 1,
        monthly_limit: 500,
        daily_limit: null,
        allow_pay: false,
        search_type: null
      }
    ],
    fallback_strategy: 'empty_results'
  },

  // Domain rating analysis APIs
  'domain_rating': {
    name: 'Domain Rating Analysis',
    sources: [
      {
        id: 'seo_traffic_authority_check',
        host: 'seo-traffic-authority.p.rapidapi.com',
        endpoint: 'check',
        priority: 1,
        monthly_limit: 500,
        daily_limit: null,
        allow_pay: false,
        search_type: null
      }
    ],
    fallback_strategy: 'default_rating'
  },

  // Domain WHOIS information APIs
  'whois': {
    name: 'Domain WHOIS Information',
    sources: [
      {
        id: 'api_layer_whois',
        host: 'api.apilayer.com',
        endpoint: 'whois',
        priority: 1,
        monthly_limit: 3000, // API Layer free tier
        daily_limit: 100,
        allow_pay: false,
        search_type: null
      }
    ],
    fallback_strategy: 'return_error'
  }
};

/**
 * API endpoint configurations with request/response adapters
 * Supports RapidAPI, Direct APIs, Google APIs, Exa API, etc.
 */
const API_ADAPTERS = {
  'similarweb-insights.p.rapidapi.com': {
    name: 'SimilarWeb Insights',
    baseUrl: 'https://similarweb-insights.p.rapidapi.com',
    endpoints: {
      traffic: {
        path: '/traffic',
        method: 'GET',
        params: (domain) => ({ domain }),
        headers: (apiKey) => ({
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': 'similarweb-insights.p.rapidapi.com'
        }),
        responseAdapter: (data) => {
          // Parse the new API response structure
          const latestVisits = data.Traffic?.Visits ? 
            Object.values(data.Traffic.Visits).pop() : 0;
          
          return {
            visits: latestVisits || 0,
            unique_visitors: latestVisits || 0,
            bounce_rate: data.Traffic?.Engagement?.BounceRate || 0,
            pages_per_visit: data.Traffic?.Engagement?.PagesPerVisit || 0,
            avg_visit_duration: data.Traffic?.Engagement?.TimeOnSite || 0,
            traffic_sources: data.Traffic?.Sources || {},
            global_rank: data.Rank?.GlobalRank || null,
            country_rank: data.Rank?.CountryRank?.Rank || null,
            category: data.WebsiteDetails?.Category || 'Unknown',
            timestamp: new Date().toISOString()
          };
        }
      },
      overview: {
        path: '/overview',
        method: 'GET', 
        params: (domain) => ({ domain }),
        headers: (apiKey) => ({
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': 'similarweb-insights.p.rapidapi.com'
        }),
        responseAdapter: (data) => ({
          global_rank: data.Rank?.GlobalRank || null,
          country_rank: data.Rank?.CountryRank?.Rank || null,
          category_rank: data.Rank?.CategoryRank?.Rank || null,
          category: data.WebsiteDetails?.Category || 'Unknown',
          estimated_monthly_visits: data.Traffic?.Visits ? 
            Object.values(data.Traffic.Visits).pop() : 0,
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  'similarweb-traffic.p.rapidapi.com': {
    name: 'SimilarWeb Traffic',
    baseUrl: 'https://similarweb-traffic.p.rapidapi.com',
    apiType: 'rapidapi',
    endpoints: {
      traffic: {
        path: '/traffic',
        method: 'GET',
        params: (domain) => ({ domain }),
        headers: (apiKey) => ({
          'x-rapidapi-key': apiKey,
          'x-rapidapi-host': 'similarweb-traffic.p.rapidapi.com'
        }),
        responseAdapter: (data) => {
          // Parse the SimilarWeb Traffic API response structure
          const visits = parseInt(data.Engagments?.Visits) || 0;
          const latestMonthlyVisits = data.EstimatedMonthlyVisits ? 
            Object.values(data.EstimatedMonthlyVisits).pop() : 0;
          
          return {
            visits: visits || latestMonthlyVisits || 0,
            estimated_monthly_visits: latestMonthlyVisits || visits || 0,
            unique_visitors: visits || latestMonthlyVisits || 0,
            bounce_rate: parseFloat(data.Engagments?.BounceRate) || 0,
            pages_per_visit: parseFloat(data.Engagments?.PagePerVisit) || 0,
            avg_visit_duration: parseFloat(data.Engagments?.TimeOnSite) || 0,
            traffic_sources: data.TrafficSources || {},
            global_rank: data.GlobalRank?.Rank || null,
            country_rank: data.CountryRank?.Rank || null,
            category_rank: parseInt(data.CategoryRank?.Rank) || null,
            category: data.Category || 'Unknown',
            top_country_shares: data.TopCountryShares || [],
            is_small: data.IsSmall || false,
            snapshot_date: data.SnapshotDate || new Date().toISOString(),
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  },

  'google-search-results.p.rapidapi.com': {
    name: 'Google Search Results',
    baseUrl: 'https://google-search-results.p.rapidapi.com',
    endpoints: {
      search: {
        path: '/search',
        method: 'GET',
        params: (query, options = {}) => ({
          q: query,
          num: options.num || 10,
          start: options.start || 0,
          gl: options.country || 'us',
          hl: options.language || 'en'
        }),
        headers: (apiKey) => ({
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': 'google-search-results.p.rapidapi.com'
        }),
        responseAdapter: (data) => ({
          results: (data.organic_results || []).map(result => ({
            title: result.title,
            link: result.link,
            snippet: result.snippet,
            position: result.position
          })),
          total_results: data.search_information?.total_results || 0,
          search_time: data.search_information?.time_taken || 0,
          timestamp: new Date().toISOString()
        })
      },
      related: {
        path: '/related',
        method: 'GET',
        params: (query) => ({ q: query }),
        headers: (apiKey) => ({
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': 'google-search-results.p.rapidapi.com'
        }),
        responseAdapter: (data) => ({
          related_queries: data.related_searches || [],
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  'seo-api.p.rapidapi.com': {
    name: 'SEO Analysis API',
    baseUrl: 'https://seo-api.p.rapidapi.com',
    endpoints: {
      analyze: {
        path: '/analyze',
        method: 'POST',
        params: (url, options = {}) => ({
          url: url,
          include_meta: options.include_meta !== false,
          include_links: options.include_links !== false,
          include_images: options.include_images !== false
        }),
        headers: (apiKey) => ({
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': 'seo-api.p.rapidapi.com',
          'Content-Type': 'application/json'
        }),
        responseAdapter: (data) => ({
          title: data.meta?.title || '',
          description: data.meta?.description || '',
          keywords: data.meta?.keywords || [],
          h1_tags: data.headings?.h1 || [],
          h2_tags: data.headings?.h2 || [],
          internal_links: data.links?.internal?.length || 0,
          external_links: data.links?.external?.length || 0,
          images_count: data.images?.length || 0,
          word_count: data.content?.word_count || 0,
          timestamp: new Date().toISOString()
        })
      },
      keywords: {
        path: '/keywords',
        method: 'GET',
        params: (domain, options = {}) => ({
          domain: domain,
          country: options.country || 'us',
          limit: options.limit || 100
        }),
        headers: (apiKey) => ({
          'X-RapidAPI-Key': apiKey,
          'X-RapidAPI-Host': 'seo-api.p.rapidapi.com'
        }),
        responseAdapter: (data) => ({
          keywords: (data.keywords || []).map(kw => ({
            keyword: kw.keyword,
            position: kw.position,
            search_volume: kw.search_volume,
            cpc: kw.cpc,
            competition: kw.competition
          })),
          total_keywords: data.total || 0,
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  // Direct API endpoints
  'data.similarweb.com': {
    name: 'SimilarWeb Direct API',
    baseUrl: 'https://data.similarweb.com',
    apiType: 'direct',
    endpoints: {
      traffic: {
        path: '/api/v1/data',
        method: 'GET',
        params: (domain) => ({ domain }),
        headers: () => ({
          'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'cache-control': 'no-cache',
          'cookie': '_ga=GA1.1.1636869952.1733582771; _pk_id.1.fd33=c6316898802aab8f.1733582771.; FPID=FPID2.2.7Qa9UrKO3v0v0rYdceaWkt5T62V5PyiXC82zNMHkcz8%3D.1733582771; _abck=A0B44D3E6FF657215604712B1A95B2A6~0~YAAQrR4euPhWCbaTAQAA6xjqYQ2m6VRpBtuyjJMEfWdV7+9V9qlC908ELX8z7/6rwLKLkZF+KR/FRHRSxzmJwpHWaz/gbVxNmrXspMW86E9m5MJp0bPyEjsPBQgVlOl+vvsyU+1POenvTFgLCm5UxpVLWkSxHSvwsUb4MixitWo2rODLCz5hzlSmWxrzHickfs9f7eZ7iG55NOXggNMf6OR6WNaE3zDae/fRUNXEcp4eYyxduWHy2Fs4P8G5R48Rtxofm9yjF1ZhCs2ROwMywI7O6hkvq7ebKbvfOlFODjif91J9/r+VWqMVLdDaVRSRyEdSQEGc6IBZjQJSSFBz7a8ciQq2Rtz4JbxAlDtk+RLsztMKHKmQ/UJh78bVEt6uJpu/XI4nSh58JRt7Ofg7DLg7m7TOi/TE2XdvqUWXDBozp23SfSaeXWZ/lQYPXuX/S2w6yl5CdjjHerg2+PE28Ax+LEzQqh2fkz44+lfEVkunVi4GQCRsrow6bYYSzUZwyCWizx/ODkD7E5jdlfjHsq3yEnPpQrPASaEPwEsxhooYUfmSTI7DfGwc731iDrBtEMhjfXM/FrFkt9sCcxrzoQV8JCa5Bpn5BqXM6OvksbzHO6hyugv+P7jjhr5cnBUlrKY1lRER0tft3Iwj0y+8aHV4EKB3rCJNOqATL3jN2vo2kW7YuvxrzZCpZKo32/XDqIRVLeSQHzMY9PISLJYH5/6QxnySgPAli+ePO4xKBLToaMX1LLdWuhwS0g==~-1~||0||~-1; _ga_V5DSP51YD0=GS1.1.1736809519.5.0.1736809525.0.0.2084998713',
          'dnt': '1',
          'pragma': 'no-cache',
          'priority': 'u=0, i',
          'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'document',
          'sec-fetch-mode': 'navigate',
          'sec-fetch-site': 'none',
          'sec-fetch-user': '?1',
          'upgrade-insecure-requests': '1',
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }),
        responseAdapter: (data) => {
          let traffic = 0;
          if (data.visits) traffic = parseInt(data.visits) || 0;
          else if (data.totalVisits) traffic = parseInt(data.totalVisits) || 0;
          else if (data.monthly_visits) traffic = parseInt(data.monthly_visits) || 0;
          else if (data.estimated_monthly_visits) traffic = parseInt(data.estimated_monthly_visits) || 0;
          else if (data.traffic?.visits) traffic = parseInt(data.traffic.visits) || 0;
          else if (data.GlobalRank?.Visits) traffic = parseInt(data.GlobalRank.Visits) || 0;

          return {
            traffic,
            domain: data.domain,
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  },

  'www.googleapis.com': {
    name: 'Google Custom Search API',
    baseUrl: 'https://www.googleapis.com',
    apiType: 'google',
    endpoints: {
      customsearch: {
        path: '/customsearch/v1',
        method: 'GET',
        params: (query, options = {}) => ({
          key: config.GOOGLE_API_KEY,
          cx: config.GOOGLE_SEARCH_ENGINE_ID,
          q: query,
          num: options.num || 10,
          start: options.start || 0
        }),
        headers: () => ({}),
        responseAdapter: (data) => ({
          results: (data.items || []).map(item => ({
            title: item.title,
            link: item.link,
            snippet: item.snippet
          })),
          total_results: parseInt(data.searchInformation?.totalResults || 0),
          search_time: parseFloat(data.searchInformation?.searchTime || 0),
          timestamp: new Date().toISOString()
        })
      },
      indexing: {
        path: '/customsearch/v1',
        method: 'GET',
        params: (targetUrl, website, searchType = 'site') => ({
          key: config.GOOGLE_API_KEY,
          cx: config.GOOGLE_SEARCH_ENGINE_ID,
          q: searchType === 'link_site' ? `link:${targetUrl} site:${website}` : `site:${targetUrl}`
        }),
        headers: () => ({}),
        responseAdapter: (data) => {
          const totalResults = parseInt(data.searchInformation?.totalResults || 0);
          const isIndexed = totalResults > 0;
          return {
            is_indexed: isIndexed,
            indexed_pages: isIndexed ? Math.min(totalResults, 1) : 0,
            total_results: totalResults,
            timestamp: new Date().toISOString()
          };
        }
      },
      backlinks: {
        path: '/customsearch/v1',
        method: 'GET',
        params: (domain) => ({
          key: config.GOOGLE_API_KEY,
          cx: config.GOOGLE_SEARCH_ENGINE_ID,
          q: `link:${domain}`
        }),
        headers: () => ({}),
        responseAdapter: (data) => ({
          total_results: parseInt(data.searchInformation?.totalResults || 0),
          links: (data.items || []).map(item => ({
            title: item.title,
            url: item.link,
            snippet: item.snippet
          })),
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  'api.exa.ai': {
    name: 'Exa AI Search API',
    baseUrl: 'https://api.exa.ai',
    apiType: 'exa',
    endpoints: {
      search: {
        path: '/search',
        method: 'POST',
        params: (query, options = {}) => ({
          query: query,
          type: 'search',
          useAutoprompt: false,
          numResults: options.numResults || 10,
          contents: options.contents || { text: true },
          includeDomains: options.includeDomains || []
        }),
        headers: () => ({
          'Authorization': `Bearer ${config.EXA_API_KEY}`,
          'Content-Type': 'application/json'
        }),
        responseAdapter: (data) => ({
          results: (data.results || []).map(result => ({
            url: result.url,
            title: result.title,
            text: result.text,
            score: result.score
          })),
          total_results: data.results?.length || 0,
          timestamp: new Date().toISOString()
        })
      },
      indexing: {
        path: '/search',
        method: 'POST',
        params: (targetUrl, website) => ({
          query: `site:${website} ${targetUrl}`,
          type: 'search',
          useAutoprompt: false,
          numResults: 10,
          contents: { text: true },
          includeDomains: [website]
        }),
        headers: () => ({
          'Authorization': `Bearer ${config.EXA_API_KEY}`,
          'Content-Type': 'application/json'
        }),
        responseAdapter: (data) => {
          const results = data.results || [];
          const foundExactMatch = results.some(result => 
            result.url === arguments[0] || 
            result.url.includes(arguments[0]) ||
            arguments[0].includes(result.url)
          );
          const domainMatches = results.filter(result => {
            try {
              const resultDomain = new URL(result.url).hostname;
              return resultDomain === arguments[1] || resultDomain.includes(arguments[1]);
            } catch (e) {
              return false;
            }
          });
          
          return {
            is_indexed: foundExactMatch || domainMatches.length > 0,
            indexed_pages: domainMatches.length,
            results_count: results.length,
            domain_matches: domainMatches.length,
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  },

  // SEO Traffic Authority API
  'seo-traffic-authority.p.rapidapi.com': {
    name: 'SEO Traffic Authority API',
    baseUrl: 'https://seo-traffic-authority.p.rapidapi.com',
    apiType: 'rapidapi',
    endpoints: {
      'backlink-checker': {
        path: '/backlink-checker',
        method: 'GET',
        params: (domain, mode = 'subdomains') => ({ url: domain, mode }),
        headers: (apiKey) => ({
          'x-rapidapi-host': 'seo-traffic-authority.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }),
        responseAdapter: (data) => {
          if (!data.success || !data.data) {
            return {
              external_links: 0,
              refdomains: 0,
              dofollow_backlinks: 0,
              domain_rating: 0,
              url_rating: 0,
              discovered_links: [],
              timestamp: new Date().toISOString()
            };
          }

          const overview = data.data.overview || {};
          const backlinks = data.data.backlinks || [];

          const discoveredLinks = backlinks.map(link => ({
            title: link.title || 'Backlink',
            url: link.urlFrom,
            target_url: link.urlTo,
            domain_rating: link.domainRating || 0,
            anchor: link.anchor || '',
            is_dofollow: !link.nofollow,
            http_code: link.httpCode || 200
          }));

          return {
            external_links: overview.backlinks || 0,
            refdomains: overview.referringDomains || 0,
            dofollow_backlinks: overview.dofollowBacklinks?.count || 0,
            domain_rating: overview.domainRating || 0,
            url_rating: overview.urlRating || 0,
            discovered_links: discoveredLinks,
            timestamp: new Date().toISOString()
          };
        }
      },
      check: {
        path: '/check',
        method: 'POST',
        params: (domain) => ({ url: domain }),
        headers: (apiKey) => ({
          'Content-Type': 'application/json',
          'x-rapidapi-host': 'seo-traffic-authority.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }),
        responseAdapter: (data) => {
          if (!data.success || !data.data) {
            return {
              dr_score: 0,
              domain_rank: null,
              timestamp: new Date().toISOString()
            };
          }

          return {
            dr_score: data.data.domainRating || 0,
            domain_rank: data.data.domainRank || null,
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  },

  // Additional RapidAPI endpoints for legacy support
  'ahrefs-api.p.rapidapi.com': {
    name: 'Ahrefs API (RapidAPI)',
    baseUrl: 'https://ahrefs-api.p.rapidapi.com',
    apiType: 'rapidapi',
    endpoints: {
      check_dr_ar: {
        path: '/check-dr-ar',
        method: 'GET',
        params: (domain) => ({ domain }),
        headers: (apiKey) => ({
          'x-rapidapi-host': 'ahrefs-api.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }),
        responseAdapter: (data) => ({
          dr_score: data.domain_rating || 0,
          ahrefs_rank: data.ahrefs_rank || null,
          domain: data.domain || '',
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  'ahrefs2.p.rapidapi.com': {
    name: 'Ahrefs2 Backlink Checker (RapidAPI)',
    baseUrl: 'https://ahrefs2.p.rapidapi.com',
    apiType: 'rapidapi',
    endpoints: {
      backlinks: {
        path: '/backlinks',
        method: 'GET',
        params: (domain, mode = 'subdomains') => ({ url: domain, mode }),
        headers: (apiKey) => ({
          'x-rapidapi-host': 'ahrefs2.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }),
        responseAdapter: (data) => {
          const discoveredLinks = (data.backlinksList || []).map(link => ({
            title: link.title || 'Backlink',
            url: link.url,
            target_url: link.target_url,
            domain_rating: link.domain_rating || 0,
            anchor: link.anchor || '',
            is_dofollow: true,
            http_code: 200
          }));
          
          return {
            external_links: data.backlinks || 0,
            refdomains: data.refdomains || 0,
            dofollow_backlinks: data.dofollowBacklinks || 0,
            domain_rating: data.domainRating || 0,
            url_rating: data.urlRating || 0,
            discovered_links: discoveredLinks,
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  },

  'ahrefs1.p.rapidapi.com': {
    name: 'Ahrefs Backlink Checker (RapidAPI)',
    baseUrl: 'https://ahrefs1.p.rapidapi.com',
    apiType: 'rapidapi',
    endpoints: {
      backlinks: {
        path: '/v1/backlink-checker',
        method: 'GET',
        params: (domain, mode = 'subdomains') => ({ url: domain, mode }),
        headers: (apiKey) => ({
          'x-rapidapi-host': 'ahrefs1.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }),
        responseAdapter: (data) => {
          const discoveredLinks = (data.topBacklinks?.backlinks || []).map(link => ({
            title: link.title || link.anchor || 'Backlink',
            url: link.urlFrom,
            target_url: link.urlTo,
            domain_rating: link.domainRating || 0,
            anchor: link.anchor || '',
            is_dofollow: !link.nofollow,
            http_code: link.httpCode || 0
          }));
          
          return {
            external_links: data.overview?.backlinks || 0,
            refdomains: data.overview?.refdomains || 0,
            dofollow_backlinks: data.overview?.dofollowBacklinks || 0,
            domain_rating: data.overview?.domainRating || 0,
            url_rating: data.overview?.urlRating || 0,
            discovered_links: discoveredLinks,
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  },

  'ahrefs-dr-rank-checker.p.rapidapi.com': {
    name: 'Ahrefs DR Checker (RapidAPI)',
    baseUrl: 'https://ahrefs-dr-rank-checker.p.rapidapi.com',
    apiType: 'rapidapi',
    endpoints: {
      check: {
        path: '/check',
        method: 'POST',
        params: (domain) => ({ url: domain }),
        headers: (apiKey) => ({
          'Content-Type': 'application/json',
          'x-rapidapi-host': 'ahrefs-dr-rank-checker.p.rapidapi.com',
          'x-rapidapi-key': apiKey
        }),
        responseAdapter: (data) => ({
          dr_score: data.data?.domainRating || 0,
          ahrefs_rank: data.data?.ahrefsRank || null,
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  // Ahrefs Official API
  'api.ahrefs.com': {
    name: 'Ahrefs Official API',
    baseUrl: 'https://api.ahrefs.com',
    apiType: 'ahrefs',
    endpoints: {
      domain_rating: {
        path: '/v3/site-explorer/domain-rating',
        method: 'GET',
        params: (domain) => ({
          target: domain,
          mode: 'domain'
        }),
        headers: () => ({
          'Authorization': `Bearer ${config.AHREFS_API_KEY}`,
          'Accept': 'application/json'
        }),
        responseAdapter: (data) => ({
          dr_score: data.domain_rating?.value || 0,
          url_rating: data.url_rating?.value || 0,
          backlinks: data.metrics?.backlinks || 0,
          referring_domains: data.metrics?.refdomains || 0,
          timestamp: new Date().toISOString()
        })
      }
    }
  },

  // API Layer WHOIS API
  'api.apilayer.com': {
    name: 'API Layer WHOIS API',
    baseUrl: 'https://api.apilayer.com',
    apiType: 'apilayer',
    endpoints: {
      whois: {
        path: '/whois/query',
        method: 'GET',
        params: (domain) => ({ domain }),
        headers: () => ({
          'apikey': config.API_LAYER_KEY,
          'User-Agent': 'LinkTrackPro/1.0 (https://mybacklinks.app)',
          'Accept': 'application/json'
        }),
        responseAdapter: (data) => {
          const result = data.result || {};
          
          // Parse creation date
          let creationDate = null;
          if (result.creation_date) {
            try {
              creationDate = new Date(result.creation_date).toISOString();
            } catch (e) {
              console.warn('Failed to parse creation date:', result.creation_date);
            }
          }
          
          // Parse expiration date
          let expirationDate = null;
          if (result.expiration_date) {
            try {
              expirationDate = new Date(result.expiration_date).toISOString();
            } catch (e) {
              console.warn('Failed to parse expiration date:', result.expiration_date);
            }
          }
          
          return {
            domain: result.domain_name || '',
            creation_date: creationDate,
            expiration_date: expirationDate,
            registrar: result.registrar || '',
            name_servers: result.name_servers || [],
            emails: result.emails || '',
            dnssec: result.dnssec || 'unknown',
            timestamp: new Date().toISOString()
          };
        }
      }
    }
  }
};

/**
 * Universal API Client Class
 * Handles RapidAPI, Direct APIs, Google APIs, Exa API, etc.
 */
export class UniversalAPIClient {
  constructor() {
    this.quotaManager = null; // Will be initialized when needed
  }

  /**
   * Get quota manager (lazy loading to avoid circular dependency)
   */
  async getQuotaManager() {
    if (!this.quotaManager) {
      const { quotaManager } = await import('./quota-manager.js');
      this.quotaManager = quotaManager;
    }
    return this.quotaManager;
  }

  /**
   * Initialize with Cloudflare KV namespace
   */
  async init(kvNamespace) {
    const quotaManager = await this.getQuotaManager();
    quotaManager.init(kvNamespace);
  }

  /**
   * Get API adapter configuration
   */
  getAdapter(apiHost) {
    return API_ADAPTERS[apiHost];
  }

  /**
   * Get all available adapters
   */
  getAvailableAdapters() {
    return Object.keys(API_ADAPTERS);
  }

  /**
   * Get endpoints for a specific API
   */
  getEndpoints(apiHost) {
    const adapter = this.getAdapter(apiHost);
    return adapter ? Object.keys(adapter.endpoints) : [];
  }

  /**
   * Build request URL with parameters
   */
  buildRequestUrl(adapter, endpoint, params) {
    const endpointConfig = adapter.endpoints[endpoint];
    if (!endpointConfig) {
      throw new Error(`Endpoint '${endpoint}' not found for adapter`);
    }

    const url = new URL(endpointConfig.path, adapter.baseUrl);
    
    if (endpointConfig.method === 'GET' && params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    return url.toString();
  }

  /**
   * Make API request with quota checking and response adaptation
   */
  async makeRequest(apiHost, endpoint, requestParams = {}, options = {}) {
    const adapter = this.getAdapter(apiHost);
    if (!adapter) {
      throw new Error(`No adapter found for API host: ${apiHost}`);
    }

    const endpointConfig = adapter.endpoints[endpoint];
    if (!endpointConfig) {
      throw new Error(`Endpoint '${endpoint}' not found for ${adapter.name}`);
    }

    // Prepare request parameters
    const params = typeof endpointConfig.params === 'function' 
      ? endpointConfig.params(...(Array.isArray(requestParams) ? requestParams : [requestParams]))
      : endpointConfig.params || {};

    // Build request URL
    const requestUrl = this.buildRequestUrl(adapter, endpoint, 
      endpointConfig.method === 'GET' ? params : null);

    // Check quota before making request
    const quotaManager = await this.getQuotaManager();
    const canMakeRequest = await quotaManager.canMakeRequest(requestUrl);
    if (!canMakeRequest) {
      const stats = await quotaManager.getUsageStats(requestUrl);
      throw new Error(`Quota exceeded for ${adapter.name}. Monthly: ${stats.monthly.used}/${stats.monthly.limit}, Daily: ${stats.daily.used}/${stats.daily.limit}`);
    }

    // Prepare headers based on API type
    let headers;
    switch (adapter.apiType) {
      case 'rapidapi':
        headers = endpointConfig.headers(config.RAPIDAPI_KEY);
        break;
      case 'ahrefs':
        headers = endpointConfig.headers();
        break;
      case 'apilayer':
        headers = endpointConfig.headers();
        break;
      default:
        headers = endpointConfig.headers();
    }

    // Prepare request options
    const requestOptions = {
      method: endpointConfig.method,
      headers,
      ...options
    };

    // Add body for POST requests
    if (endpointConfig.method === 'POST' && params) {
      requestOptions.body = JSON.stringify(params);
    }

    console.log(`🚀 Making ${endpointConfig.method} request to ${adapter.name}:${endpoint}`);
    console.log(`📍 URL: ${requestUrl}`);
    console.log(`📍 Params: ${JSON.stringify(params)}`);
    console.log(`📍 Request Headers: ${JSON.stringify(requestOptions.headers)}`);
    console.log(`📍 Request Options: ${JSON.stringify(requestOptions)}`);

    try {
      // Make the actual request
      const response = await fetch(requestUrl, requestOptions);
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unable to read error response');
        console.error(`❌ Request failed:`, {
          status: response.status,
          statusText: response.statusText,
          url: requestUrl,
          headers: Object.fromEntries(response.headers.entries()),
          body: errorText
        });
        
        // For WHOIS APIs, try to parse error response to provide better error handling
        if (endpoint === 'whois' && errorText) {
          try {
            const errorData = JSON.parse(errorText);
            if (errorData.result === 'error' && errorData.message) {
              // Return a structured error response instead of throwing
              return {
                success: false,
                error: `WHOIS API Error: ${errorData.message}`,
                errorType: 'api_error',
                statusCode: response.status,
                api_info: {
                  host: apiHost,
                  endpoint: endpoint,
                  adapter_name: adapter.name,
                  quota_used: false
                },
                timestamp: new Date().toISOString()
              };
            }
          } catch (parseError) {
            // If we can't parse the error, fall through to generic error handling
          }
        }
        
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const rawData = await response.json();
      
      // Record usage on successful request
      await quotaManager.recordUsage(requestUrl);

      // Adapt response using the adapter's response transformer
      const adaptedData = endpointConfig.responseAdapter 
        ? endpointConfig.responseAdapter(rawData)
        : rawData;

      console.log(`✅ Request successful. Response adapted.`);

      return {
        success: true,
        data: adaptedData,
        raw_data: rawData,
        api_info: {
          host: apiHost,
          endpoint: endpoint,
          adapter_name: adapter.name,
          quota_used: true
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Request failed:`, error.message);
      
      return {
        success: false,
        error: error.message,
        api_info: {
          host: apiHost,
          endpoint: endpoint,
          adapter_name: adapter.name,
          quota_used: false
        },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Convenience methods for common operations
   */
  
  // SimilarWeb traffic data (Insights API)
  async getSimilarWebTraffic(domain) {
    return this.makeRequest('similarweb-insights.p.rapidapi.com', 'traffic', domain);
  }

  // SimilarWeb traffic data (Traffic API)
  async getSimilarWebTrafficDirect(domain) {
    return this.makeRequest('similarweb-traffic.p.rapidapi.com', 'traffic', domain);
  }

  // SimilarWeb overview data
  async getSimilarWebOverview(domain) {
    return this.makeRequest('similarweb-insights.p.rapidapi.com', 'overview', domain);
  }

  // Google search results
  async getGoogleSearchResults(query, options = {}) {
    return this.makeRequest('google-search-results.p.rapidapi.com', 'search', [query, options]);
  }

  // Google related searches
  async getGoogleRelatedSearches(query) {
    return this.makeRequest('google-search-results.p.rapidapi.com', 'related', query);
  }

  // SEO analysis
  async getSEOAnalysis(url, options = {}) {
    return this.makeRequest('seo-api.p.rapidapi.com', 'analyze', [url, options]);
  }

  // SEO keywords
  async getSEOKeywords(domain, options = {}) {
    return this.makeRequest('seo-api.p.rapidapi.com', 'keywords', [domain, options]);
  }

  // Direct API methods
  
  // SimilarWeb Direct traffic data
  async getSimilarWebDirectTraffic(domain) {
    return this.makeRequest('data.similarweb.com', 'traffic', domain);
  }

  // Google Custom Search methods
  async getGoogleCustomSearch(query, options = {}) {
    return this.makeRequest('www.googleapis.com', 'customsearch', [query, options]);
  }

  async getGoogleIndexingStatus(targetUrl, website, searchType = 'site') {
    return this.makeRequest('www.googleapis.com', 'indexing', [targetUrl, website, searchType]);
  }

  async getGoogleBacklinks(domain) {
    return this.makeRequest('www.googleapis.com', 'backlinks', domain);
  }

  // Exa AI methods
  async getExaSearch(query, options = {}) {
    return this.makeRequest('api.exa.ai', 'search', [query, options]);
  }

  async getExaIndexingStatus(targetUrl, website) {
    return this.makeRequest('api.exa.ai', 'indexing', [targetUrl, website]);
  }

  // Ahrefs Official API methods
  async getAhrefsDomainRating(domain) {
    return this.makeRequest('api.ahrefs.com', 'domain_rating', domain);
  }

  // RapidAPI Legacy methods
  async getAhrefsAPIDomainRating(domain) {
    return this.makeRequest('ahrefs-api.p.rapidapi.com', 'check_dr_ar', domain);
  }

  async getAhrefs2Backlinks(domain, mode = 'subdomains') {
    return this.makeRequest('ahrefs2.p.rapidapi.com', 'backlinks', [domain, mode]);
  }

  async getAhrefsBacklinks(domain, mode = 'subdomains') {
    return this.makeRequest('ahrefs1.p.rapidapi.com', 'backlinks', [domain, mode]);
  }

  async getAhrefsDRChecker(domain) {
    return this.makeRequest('ahrefs-dr-rank-checker.p.rapidapi.com', 'check', domain);
  }

  // SEO Traffic Authority API methods
  async getSEOTrafficAuthorityBacklinks(domain, mode = 'subdomains') {
    return this.makeRequest('seo-traffic-authority.p.rapidapi.com', 'backlink-checker', [domain, mode]);
  }

  async getSEOTrafficAuthorityDomainRating(domain) {
    return this.makeRequest('seo-traffic-authority.p.rapidapi.com', 'check', domain);
  }

  // WHOIS information methods (with proper error handling)
  async getWhoisInfo(domain) {
    // Try API Layer first (primary)
    try {
      const result = await this.makeRequest('api.apilayer.com', 'whois', domain);
      if (result.success && result.data) {
        return result;
      }
    } catch (error) {
      console.warn('API Layer WHOIS failed:', error.message);
    }

    // API Layer failed, return proper error instead of fake data
    console.log(`WHOIS API unavailable for ${domain}, returning error`);
    return {
      success: false,
      error: 'WHOIS API unavailable - API Layer key missing or service down',
      data: {
        domain,
        creation_date: null,
        expiration_date: null,
        registrar: null,
        name_servers: [],
        emails: null,
        dnssec: 'unknown',
        timestamp: new Date().toISOString()
      },
      api_info: {
        host: 'api.apilayer.com',
        endpoint: 'whois',
        adapter_name: 'API Layer WHOIS API',
        quota_used: false
      },
      timestamp: new Date().toISOString()
    };
  }

  // Individual WHOIS API methods for testing
  async getWhoisInfoFromAPILayer(domain) {
    return this.makeRequest('api.apilayer.com', 'whois', domain);
  }

  /**
   * Get quota status for all APIs
   */
  async getQuotaStatus() {
    const status = {};
    const quotaManager = await this.getQuotaManager();
    
    for (const apiHost of this.getAvailableAdapters()) {
      const stats = await quotaManager.getUsageStats(`https://${apiHost}/test`);
      status[apiHost] = {
        adapter_name: API_ADAPTERS[apiHost].name,
        ...stats
      };
    }

    return status;
  }

  /**
   * Test connection to all configured APIs
   */
  async testConnections() {
    const results = {};
    const quotaManager = await this.getQuotaManager();
    
    for (const apiHost of this.getAvailableAdapters()) {
      const adapter = this.getAdapter(apiHost);
      
      try {
        // Test with first available endpoint
        const firstEndpoint = Object.keys(adapter.endpoints)[0];
        const testUrl = `${adapter.baseUrl}${adapter.endpoints[firstEndpoint].path}`;
        
        // Just test quota checking, not actual API call
        const canMake = await quotaManager.canMakeRequest(testUrl);
        
        results[apiHost] = {
          name: adapter.name,
          status: 'available',
          can_make_request: canMake,
          endpoints: Object.keys(adapter.endpoints)
        };
      } catch (error) {
        results[apiHost] = {
          name: adapter.name,
          status: 'error',
          error: error.message,
          can_make_request: false
        };
      }
    }

    return results;
  }
}

// Export singleton instance
export const apiClient = new UniversalAPIClient();

// Export configurations
export { API_CONFIGS };

// Backward compatibility
export const rapidApiClient = apiClient;