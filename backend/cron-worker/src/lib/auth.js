/**
 * Authentication utilities for LinkTrackPro Cron Worker
 */

import { config } from './config.js';

/**
 * Bearer token authentication for API endpoints
 * 
 * @param {Request} request - The incoming request
 * @returns {boolean} - Whether the request is authenticated
 */
export function isAuthenticated(request) {
  let authHeader;
  
  // Handle different request formats
  if (request.headers && typeof request.headers.get === 'function') {
    authHeader = request.headers.get('Authorization') || request.headers.get('authorization');
  } else if (request.headers && request.headers.Authorization) {
    authHeader = request.headers.Authorization;
  } else if (request.headers && request.headers.authorization) {
    authHeader = request.headers.authorization;
  } else {
    console.warn('No headers found in request');
    return false;
  }
  
  // Check if Authorization header exists and starts with 'Bearer '
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.warn('Missing or invalid Authorization header format');
    return false;
  }
  
  // Extract token from 'Bearer <token>'
  const token = authHeader.substring(7).trim(); // Remove 'Bearer ' prefix
  
  // Validate token is not empty
  if (!token) {
    console.warn('Empty bearer token provided');
    return false;
  }
  
  // Check if AUTH_TOKEN is configured
  if (!config.AUTH_TOKEN) {
    console.error('AUTH_TOKEN not configured - authentication disabled');
    return false;
  }
  
  // Secure token comparison using constant-time comparison to prevent timing attacks
  const isValid = secureCompare(token, config.AUTH_TOKEN);
  
  if (!isValid) {
    console.warn('Invalid bearer token provided');
  }
  
  return isValid;
}

/**
 * Constant-time string comparison to prevent timing attacks
 * 
 * @param {string} a - First string to compare
 * @param {string} b - Second string to compare
 * @returns {boolean} - Whether strings are equal
 */
function secureCompare(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * Generate a secure random token for AUTH_TOKEN configuration
 * This is a utility function for generating secure tokens
 * 
 * @param {number} length - Token length (default: 32)
 * @returns {string} - Random hex token
 */
export function generateSecureToken(length = 32) {
  const chars = '0123456789abcdef';
  let result = '';
  
  // Use crypto.getRandomValues for secure random generation
  const randomArray = new Uint8Array(length);
  crypto.getRandomValues(randomArray);
  
  for (let i = 0; i < length; i++) {
    result += chars[randomArray[i] % chars.length];
  }
  
  return result;
}