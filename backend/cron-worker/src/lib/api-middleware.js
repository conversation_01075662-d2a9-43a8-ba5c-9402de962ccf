/**
 * API Middleware for Request/Response Logging and Tracking
 * 
 * This middleware wraps API endpoints to automatically log requests,
 * responses, performance metrics, and usage statistics.
 */

import { logApiCall, initializeD1Database } from './d1-logger.js';

/**
 * API Request/Response Logger Class
 */
export class ApiLogger {
  constructor(env, request) {
    this.env = env;
    this.request = request;
    this.startTime = Date.now();
    this.logData = {
      started_at: new Date().toISOString(),
      endpoint_path: new URL(request.url).pathname,
      method: request.method,
      query_params: null,
      request_body: null,
      user_agent: null,
      ip_address: null,
      referer: null,
      auth_type: 'none',
      auth_provided: false,
      auth_valid: false,
      status_code: 200,
      response_size_bytes: 0,
      response_time_ms: 0,
      success: true,
      error_message: null,
      error_code: null,
      cache_hit: false,
      external_api_calls: 0,
      external_api_cost: 0.0,
      items_processed: 0,
      items_successful: 0,
      items_failed: 0,
      data_source: null,
      response_body: null
    };

    this.initializeRequestData();
  }

  /**
   * Initialize request data from headers and URL
   */
  initializeRequestData() {
    try {
      const url = new URL(this.request.url);
      
      // Extract query parameters
      if (url.search) {
        const params = {};
        url.searchParams.forEach((value, key) => {
          params[key] = value;
        });
        this.logData.query_params = JSON.stringify(params);
      }

      // Extract headers
      this.logData.user_agent = this.request.headers.get('user-agent') || null;
      this.logData.referer = this.request.headers.get('referer') || null;
      
      // Extract IP address (Cloudflare headers)
      this.logData.ip_address = 
        this.request.headers.get('cf-connecting-ip') ||
        this.request.headers.get('x-forwarded-for') ||
        this.request.headers.get('x-real-ip') ||
        null;

      // Check authentication
      const authHeader = this.request.headers.get('authorization');
      if (authHeader) {
        this.logData.auth_provided = true;
        if (authHeader.toLowerCase().startsWith('bearer ')) {
          this.logData.auth_type = 'bearer';
        } else if (authHeader.toLowerCase().startsWith('basic ')) {
          this.logData.auth_type = 'basic';
        }
      }
    } catch (error) {
      console.warn('Error initializing request data:', error);
    }
  }

  /**
   * Set authentication validation result
   * @param {boolean} isValid - Whether authentication is valid
   */
  setAuthValid(isValid) {
    this.logData.auth_valid = isValid;
  }

  /**
   * Set cache hit status
   * @param {boolean} cacheHit - Whether response came from cache
   */
  setCacheHit(cacheHit) {
    this.logData.cache_hit = cacheHit;
  }

  /**
   * Set external API usage metrics
   * @param {number} apiCalls - Number of external API calls made
   * @param {number} cost - Estimated cost of external API calls
   */
  setExternalApiUsage(apiCalls, cost = 0.0) {
    this.logData.external_api_calls = apiCalls;
    this.logData.external_api_cost = cost;
  }

  /**
   * Set processing metrics
   * @param {number} processed - Total items processed
   * @param {number} successful - Successfully processed items
   * @param {number} failed - Failed items
   */
  setProcessingMetrics(processed, successful, failed) {
    this.logData.items_processed = processed;
    this.logData.items_successful = successful;
    this.logData.items_failed = failed;
  }

  /**
   * Set data source information
   * @param {string} source - Data source (e.g., 'google', 'ahrefs', 'cache')
   */
  setDataSource(source) {
    this.logData.data_source = source;
  }

  /**
   * Set error information
   * @param {string} message - Error message
   * @param {string} code - Error code
   */
  setError(message, code = null) {
    this.logData.success = false;
    this.logData.error_message = message;
    this.logData.error_code = code;
  }

  /**
   * Set response body content
   * @param {string} body - Response body content (will be truncated if too large)
   */
  setResponseBody(body) {
    if (body) {
      // Truncate response body if it's too large (keep first 10KB for storage efficiency)
      const maxLength = 10240; // 10KB limit
      this.logData.response_body = body.length > maxLength ? 
        body.substring(0, maxLength) + '... [TRUNCATED]' : 
        body;
    }
  }

  /**
   * Complete the API call logging
   * @param {Response} response - The response object
   * @param {boolean} skipDatabase - Whether to skip database logging
   * @returns {Promise<number|null>} - Log record ID
   */
  async complete(response, skipDatabase = false) {
    try {
      // Calculate response time
      this.logData.response_time_ms = Date.now() - this.startTime;
      this.logData.completed_at = new Date().toISOString();
      
      // Set response data
      if (response) {
        this.logData.status_code = response.status;
        this.logData.success = response.status >= 200 && response.status < 400;
        
        // Get response body and calculate size
        const responseText = await response.clone().text();
        this.logData.response_size_bytes = new Blob([responseText]).size;
        
        // Store response body (with size limit)
        this.setResponseBody(responseText);
      }

      // Log to D1 database if available and not skipped
      if (this.env?.DB && !skipDatabase) {
        return await logApiCall(this.env.DB, this.logData);
      }

      // Fallback: log to console if D1 not available
      console.log('API Call Log:', JSON.stringify({
        endpoint: this.logData.endpoint_path,
        method: this.logData.method,
        status: this.logData.status_code,
        response_time: this.logData.response_time_ms,
        success: this.logData.success,
        auth_provided: this.logData.auth_provided,
        cache_hit: this.logData.cache_hit,
        external_api_calls: this.logData.external_api_calls,
        items_processed: this.logData.items_processed
      }));

      return null;
    } catch (error) {
      console.error('Error completing API call log:', error);
      return null;
    }
  }

  /**
   * Get current log data (for debugging)
   * @returns {Object} - Current log data
   */
  getLogData() {
    return { ...this.logData };
  }
}

/**
 * Middleware wrapper for API endpoints
 * @param {Function} handler - The API endpoint handler function
 * @param {Object} options - Middleware options
 * @returns {Function} - Wrapped handler function
 */
export function withApiLogging(handler, options = {}) {
  return async function(request, env, ctx) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    // Skip logging for certain paths to avoid infinite loops
    const skipLoggingPaths = [
      '/logs',
      '/api/logs', 
      '/api/stats',
      '/quota',
      '/api/quota'
    ];
    
    const shouldSkipLogging = skipLoggingPaths.some(path => 
      pathname === path || pathname.startsWith(path + '/')
    );

    // Initialize D1 database if not already done
    if (env?.DB && !env._d1_initialized) {
      await initializeD1Database(env);
      env._d1_initialized = true;
    }

    // Create API logger (but may skip actual logging)
    const logger = new ApiLogger(env, request);
    
    // Add logger to environment for handler access
    env.apiLogger = logger;

    let response;
    try {
      // Call the original handler
      response = await handler(request, env, ctx);
      
      // Ensure response is a Response object
      if (!(response instanceof Response)) {
        response = new Response(JSON.stringify(response), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return response;
    } catch (error) {
      // Log error
      logger.setError(error.message, error.name);
      
      // Create error response
      response = new Response(JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });

      return response;
    } finally {
      // Complete logging (skip database logging for certain paths)
      await logger.complete(response, shouldSkipLogging);
      
      if (shouldSkipLogging) {
        console.log(`Skipped DB logging for path: ${pathname}`);
      }
    }
  };
}

/**
 * Wrapper for external API calls to track usage
 * @param {Function} apiCall - The external API call function
 * @param {Object} logger - The ApiLogger instance
 * @param {Object} options - Options for tracking
 * @returns {Function} - Wrapped API call function
 */
export function trackExternalApiCall(apiCall, logger, options = {}) {
  return async function(...args) {
    const { cost = 0.0, source = 'unknown' } = options;
    
    try {
      // Increment external API call count
      const currentCalls = logger.logData.external_api_calls || 0;
      const currentCost = logger.logData.external_api_cost || 0.0;
      
      logger.setExternalApiUsage(currentCalls + 1, currentCost + cost);
      logger.setDataSource(source);

      // Make the API call
      const result = await apiCall(...args);
      
      return result;
    } catch (error) {
      // Log the external API error but don't track it as a call
      console.error(`External API call to ${source} failed:`, error);
      throw error;
    }
  };
}

/**
 * Helper function to extract processing metrics from result
 * @param {Object} result - Processing result
 * @returns {Object} - Metrics object
 */
export function extractProcessingMetrics(result) {
  const metrics = {
    processed: 0,
    successful: 0,
    failed: 0
  };

  if (result && typeof result === 'object') {
    // Handle different result formats
    if ('discovered_links' in result) {
      metrics.processed = result.discovered_links?.length || 0;
      metrics.successful = metrics.processed;
    }
    
    if ('success' in result && 'failed' in result) {
      metrics.successful = result.success || 0;
      metrics.failed = result.failed || 0;
      metrics.processed = metrics.successful + metrics.failed;
    }
    
    if ('total_links' in result) {
      metrics.processed = result.total_links || 0;
      metrics.successful = metrics.processed;
    }
    
    if (result.error) {
      metrics.failed = Math.max(metrics.failed, 1);
    }
  }

  return metrics;
}

/**
 * Rate limiting middleware (simple implementation)
 * @param {Object} options - Rate limiting options
 * @returns {Function} - Middleware function
 */
export function withRateLimit(options = {}) {
  const { 
    requestsPerHour = 100,
    keyGenerator = (request) => request.headers.get('cf-connecting-ip') || 'unknown'
  } = options;

  return function(handler) {
    return async function(request, env, ctx) {
      const key = keyGenerator(request);
      const currentHour = Math.floor(Date.now() / (1000 * 60 * 60));
      const rateLimitKey = `rate_limit:${key}:${currentHour}`;

      // This is a simplified rate limiting - in production you'd use KV storage
      // For now, we'll just log the rate limit attempt
      if (env?.apiLogger) {
        env.apiLogger.logData.rate_limit_key = rateLimitKey;
      }

      return handler(request, env, ctx);
    };
  };
}