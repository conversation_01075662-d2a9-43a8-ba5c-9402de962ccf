/**
 * Utility functions for LinkTrackPro Cron Worker
 */

import { config } from './config.js';

/**
 * Process items in batches to avoid rate limits and optimize API costs
 */
export async function processBatches(items, processFn) {
  const results = [];
  const batchSize = config.BATCH_SIZE;
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchPromises = batch.map(item => processFn(item));
    
    // Process batch with retries
    const batchResults = await Promise.all(
      batchPromises.map(promise => withRetry(promise))
    );
    
    results.push(...batchResults);
    
    // Add a small delay between batches to avoid rate limits
    if (i + batchSize < items.length) {
      await delay(1000);
    }
  }
  
  return results;
}

/**
 * Retry a promise-based operation with exponential backoff
 */
export async function withRetry(promiseFn, retries = config.MAX_RETRIES, delayMs = config.RETRY_DELAY_MS) {
  try {
    return await promiseFn;
  } catch (error) {
    if (retries <= 0) throw error;
    
    console.log(`Retrying after error: ${error.message}. Retries left: ${retries}`);
    await delay(delayMs);
    
    return withRetry(promiseFn, retries - 1, delayMs * 2);
  }
}

/**
 * Helper function to introduce delay
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Extract normalized domain from URL - consistent with frontend normalizeUrl function
 * Removes www prefix and converts to lowercase for consistency
 */
export function extractDomain(url) {
  try {
    if (!url || typeof url !== 'string') {
      return null;
    }
    
    // Handle URLs without protocol
    let fullUrl = url.trim();
    if (!fullUrl.match(/^https?:\/\//)) {
      fullUrl = `https://${fullUrl}`;
    }
    
    const urlObj = new URL(fullUrl);
    
    // Normalize hostname (remove www prefix and convert to lowercase)
    let hostname = urlObj.hostname.toLowerCase();
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    return hostname;
  } catch (error) {
    console.error(`Invalid URL: ${url}`, error);
    return null;
  }
}

/**
 * Normalize URL to match frontend normalizeUrl function behavior
 * Returns normalized URL for consistent storage
 */
export function normalizeUrl(url) {
  try {
    if (!url || typeof url !== 'string') {
      return '';
    }

    // Handle URLs without protocol
    let fullUrl = url.trim();
    if (!fullUrl.match(/^https?:\/\//)) {
      fullUrl = `https://${fullUrl}`;
    }

    const urlObj = new URL(fullUrl);
    
    // Normalize hostname (remove www prefix and convert to lowercase)
    let hostname = urlObj.hostname.toLowerCase();
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    // Always use https
    urlObj.protocol = 'https:';
    urlObj.hostname = hostname;
    
    // Remove trailing slash from pathname unless it's just "/"
    if (urlObj.pathname !== '/' && urlObj.pathname.endsWith('/')) {
      urlObj.pathname = urlObj.pathname.slice(0, -1);
    }
    
    // Remove fragment identifier
    urlObj.hash = '';
    
    return urlObj.toString();
  } catch (error) {
    console.error(`Error normalizing URL: ${url}`, error);
    return url.trim().toLowerCase();
  }
} 