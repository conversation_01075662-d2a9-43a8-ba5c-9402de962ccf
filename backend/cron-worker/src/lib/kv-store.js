/**
 * Cloudflare KV store for tracking RapidAPI usage quotas
 * Uses Cloudflare KV database for persistent storage in Worker environment
 */

class CloudflareKVStore {
  constructor(kvNamespace = null) {
    this.kv = kvNamespace;
  }

  /**
   * Set the KV namespace (called from Worker environment)
   */
  setNamespace(kvNamespace) {
    this.kv = kvNamespace;
  }

  /**
   * Get value from KV store
   */
  async get(key) {
    if (!this.kv) {
      console.warn('KV namespace not available, returning null');
      return null;
    }
    
    try {
      const value = await this.kv.get(key);
      return value ? parseInt(value) : null;
    } catch (error) {
      console.error('Failed to get from KV store:', error.message);
      return null;
    }
  }

  /**
   * Set value in KV store
   */
  async set(key, value) {
    if (!this.kv) {
      console.warn('KV namespace not available, skipping set operation');
      return;
    }
    
    try {
      await this.kv.put(key, value.toString());
    } catch (error) {
      console.error('Failed to set in KV store:', error.message);
    }
  }

  /**
   * Increment value in KV store atomically
   */
  async increment(key, amount = 1) {
    if (!this.kv) {
      console.warn('KV namespace not available, returning 0');
      return 0;
    }
    
    try {
      // Get current value
      const currentValue = await this.get(key) || 0;
      const newValue = currentValue + amount;
      
      // Set new value
      await this.set(key, newValue);
      return newValue;
    } catch (error) {
      console.error('Failed to increment in KV store:', error.message);
      return 0;
    }
  }

  /**
   * Delete key from KV store
   */
  async delete(key) {
    if (!this.kv) {
      console.warn('KV namespace not available, skipping delete operation');
      return false;
    }
    
    try {
      await this.kv.delete(key);
      return true;
    } catch (error) {
      console.error('Failed to delete from KV store:', error.message);
      return false;
    }
  }

  /**
   * Clear all quota-related keys (be careful with this!)
   */
  async clear() {
    if (!this.kv) {
      console.warn('KV namespace not available, skipping clear operation');
      return;
    }
    
    try {
      // List all keys with quota prefix
      const quotaKeys = await this.kv.list({ prefix: 'quota:' });
      
      // Delete all quota keys
      for (const key of quotaKeys.keys) {
        await this.kv.delete(key.name);
      }
      
      console.log(`Cleared ${quotaKeys.keys.length} quota keys`);
    } catch (error) {
      console.error('Failed to clear KV store:', error.message);
    }
  }

  /**
   * Get all quota keys for debugging
   */
  async listQuotaKeys() {
    if (!this.kv) {
      console.warn('KV namespace not available, returning empty array');
      return [];
    }
    
    try {
      const result = await this.kv.list({ prefix: 'quota:' });
      return result.keys.map(key => ({
        name: key.name,
        metadata: key.metadata
      }));
    } catch (error) {
      console.error('Failed to list quota keys:', error.message);
      return [];
    }
  }

  // Helper methods for quota tracking
  getMonthlyKey(apiHost) {
    const now = new Date();
    const month = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
    return `quota:monthly:${apiHost}:${month}`;
  }

  getDailyKey(apiHost) {
    const now = new Date();
    const date = now.toISOString().split('T')[0]; // YYYY-MM-DD
    return `quota:daily:${apiHost}:${date}`;
  }

  async getMonthlyUsage(apiHost) {
    const key = this.getMonthlyKey(apiHost);
    return await this.get(key) || 0;
  }

  async getDailyUsage(apiHost) {
    const key = this.getDailyKey(apiHost);
    return await this.get(key) || 0;
  }

  async incrementMonthlyUsage(apiHost) {
    const key = this.getMonthlyKey(apiHost);
    return await this.increment(key);
  }

  async incrementDailyUsage(apiHost) {
    const key = this.getDailyKey(apiHost);
    return await this.increment(key);
  }

  // Clean up old quota data (older than 2 months)
  async cleanupOldQuotas() {
    if (!this.kv) {
      console.warn('KV namespace not available, skipping cleanup');
      return;
    }
    
    try {
      const now = new Date();
      const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1);
      const cutoffMonth = twoMonthsAgo.getFullYear() + '-' + String(twoMonthsAgo.getMonth() + 1).padStart(2, '0');
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      // Get all quota keys
      const quotaKeys = await this.kv.list({ prefix: 'quota:' });
      let cleaned = 0;
      
      for (const keyData of quotaKeys.keys) {
        const key = keyData.name;
        
        // Clean monthly quotas older than 2 months
        if (key.startsWith('quota:monthly:')) {
          const monthMatch = key.match(/quota:monthly:[^:]+:(\d{4}-\d{2})/);
          if (monthMatch && monthMatch[1] < cutoffMonth) {
            await this.kv.delete(key);
            cleaned++;
          }
        }
        
        // Clean daily quotas older than 30 days
        if (key.startsWith('quota:daily:')) {
          const dateMatch = key.match(/quota:daily:[^:]+:(\d{4}-\d{2}-\d{2})/);
          if (dateMatch) {
            const keyDate = new Date(dateMatch[1]);
            if (keyDate < thirtyDaysAgo) {
              await this.kv.delete(key);
              cleaned++;
            }
          }
        }
      }
      
      if (cleaned > 0) {
        console.log(`Cleaned up ${cleaned} old quota records from Cloudflare KV`);
      }
    } catch (error) {
      console.error('Failed to cleanup old quotas:', error.message);
    }
  }
}

// Export class and singleton instance
export { CloudflareKVStore };
export const kvStore = new CloudflareKVStore();