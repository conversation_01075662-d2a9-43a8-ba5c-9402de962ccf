/**
 * D1 Database Logger for API Call Tracking and Usage Statistics
 * 
 * This module provides functionality to log API calls, track usage,
 * and generate statistics using Cloudflare D1 database.
 */

/**
 * Initialize D1 database with required tables
 * @param {Object} env - Environment variables containing D1 binding
 * @returns {Promise<boolean>} - Success status
 */
export async function initializeD1Database(env) {
  if (!env.DB) {
    console.warn('⚠️ D1 database not available in environment');
    return false;
  }

  try {
    // Check if tables exist by trying to query api_endpoints
    const testQuery = await env.DB.prepare('SELECT COUNT(*) as count FROM api_endpoints').first();
    
    if (testQuery) {
      console.log('✅ D1 database already initialized');
      return true;
    }
  } catch (error) {
    console.log('🔧 Initializing D1 database tables...');
    
    try {
      // Read and execute schema - in production, this should be done via wrangler d1 migrations
      // For now, we'll create tables programmatically
      await createTables(env.DB);
      console.log('✅ D1 database initialized successfully');
      return true;
    } catch (initError) {
      console.error('❌ Failed to initialize D1 database:', initError);
      return false;
    }
  }

  return true;
}

/**
 * Create database tables programmatically
 * @param {Object} db - D1 database instance
 */
async function createTables(db) {
  // API Endpoints table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS api_endpoints (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      endpoint TEXT NOT NULL UNIQUE,
      method TEXT NOT NULL DEFAULT 'GET',
      description TEXT,
      category TEXT NOT NULL,
      requires_auth BOOLEAN DEFAULT false,
      rate_limit_per_hour INTEGER DEFAULT 100,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // API Call Logs table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS api_call_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      endpoint_id INTEGER NOT NULL,
      endpoint_path TEXT NOT NULL,
      method TEXT NOT NULL,
      query_params TEXT,
      request_body TEXT,
      user_agent TEXT,
      ip_address TEXT,
      referer TEXT,
      auth_type TEXT,
      auth_provided BOOLEAN DEFAULT false,
      auth_valid BOOLEAN DEFAULT false,
      status_code INTEGER NOT NULL,
      response_size_bytes INTEGER DEFAULT 0,
      response_time_ms INTEGER NOT NULL,
      response_body TEXT,
      success BOOLEAN NOT NULL,
      error_message TEXT,
      error_code TEXT,
      cache_hit BOOLEAN DEFAULT false,
      external_api_calls INTEGER DEFAULT 0,
      external_api_cost REAL DEFAULT 0.0,
      items_processed INTEGER DEFAULT 0,
      items_successful INTEGER DEFAULT 0,
      items_failed INTEGER DEFAULT 0,
      data_source TEXT,
      started_at DATETIME NOT NULL,
      completed_at DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (endpoint_id) REFERENCES api_endpoints(id)
    )
  `);

  // API Usage Statistics table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS api_usage_stats (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      endpoint_id INTEGER NOT NULL,
      date DATE NOT NULL,
      hour INTEGER NOT NULL,
      total_calls INTEGER DEFAULT 0,
      successful_calls INTEGER DEFAULT 0,
      failed_calls INTEGER DEFAULT 0,
      cache_hits INTEGER DEFAULT 0,
      avg_response_time_ms REAL DEFAULT 0,
      total_response_time_ms INTEGER DEFAULT 0,
      min_response_time_ms INTEGER DEFAULT 0,
      max_response_time_ms INTEGER DEFAULT 0,
      total_response_bytes INTEGER DEFAULT 0,
      avg_response_bytes REAL DEFAULT 0,
      total_external_api_calls INTEGER DEFAULT 0,
      total_external_api_cost REAL DEFAULT 0.0,
      total_items_processed INTEGER DEFAULT 0,
      total_items_successful INTEGER DEFAULT 0,
      total_items_failed INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (endpoint_id) REFERENCES api_endpoints(id),
      UNIQUE(endpoint_id, date, hour)
    )
  `);

  // Create indexes
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_api_call_logs_endpoint_id ON api_call_logs(endpoint_id)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_api_call_logs_started_at ON api_call_logs(started_at)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_api_call_logs_status_code ON api_call_logs(status_code)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_api_usage_stats_endpoint_id ON api_usage_stats(endpoint_id)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_api_usage_stats_date ON api_usage_stats(date)`);

  // Insert fallback endpoint for failed endpoint creations
  try {
    await db.prepare(`
      INSERT OR IGNORE INTO api_endpoints (endpoint, method, category, description, requires_auth, rate_limit_per_hour)
      VALUES ('unknown', 'UNKNOWN', 'system', 'Fallback endpoint for failed endpoint creation', false, 1000)
    `).run();
  } catch (error) {
    console.warn('Failed to create fallback endpoint:', error);
  }

  // Insert default endpoints
  const endpoints = [
    ['/api/external-links', 'GET', 'Get external links for a specific domain', 'external-links', false, 60],
    ['/trigger/links', 'GET', 'Trigger external links collection for all projects', 'batch-processing', true, 10],
    ['/trigger/traffic', 'GET', 'Trigger traffic data collection', 'batch-processing', true, 10],
    ['/trigger/domain-rating', 'GET', 'Trigger domain rating collection', 'batch-processing', true, 10],
    ['/trigger/indexing', 'GET', 'Trigger indexing status checks', 'batch-processing', true, 10],
    ['/trigger/all', 'GET', 'Trigger all data collection processes', 'batch-processing', true, 5],
    ['/health', 'GET', 'Health check endpoint', 'system', false, 1000],
    ['/api/stats', 'GET', 'Get API usage statistics', 'reporting', false, 100],
    ['/logs', 'GET', 'API logs web interface', 'reporting', false, 100],
    ['/logs', 'POST', 'API logs web interface with auth', 'reporting', false, 100]
  ];

  const insertEndpoint = db.prepare(`
    INSERT OR IGNORE INTO api_endpoints (endpoint, method, description, category, requires_auth, rate_limit_per_hour)
    VALUES (?, ?, ?, ?, ?, ?)
  `);

  for (const endpoint of endpoints) {
    await insertEndpoint.bind(...endpoint).run();
  }
}

/**
 * Get or create endpoint ID
 * @param {Object} db - D1 database instance
 * @param {string} endpoint - Endpoint path
 * @param {string} method - HTTP method
 * @returns {Promise<number>} - Endpoint ID
 */
export async function getEndpointId(db, endpoint, method = 'GET') {
  if (!db) return null;

  try {
    let endpointRecord = await db.prepare(
      'SELECT id FROM api_endpoints WHERE endpoint = ? AND method = ?'
    ).bind(endpoint, method).first();

    if (!endpointRecord) {
      try {
        // Create new endpoint record using INSERT OR IGNORE to handle race conditions
        const result = await db.prepare(`
          INSERT OR IGNORE INTO api_endpoints (endpoint, method, category, requires_auth)
          VALUES (?, ?, 'unknown', false)
        `).bind(endpoint, method).run();
        
        if (result.meta.last_row_id) {
          return result.meta.last_row_id;
        }
        
        // If INSERT OR IGNORE didn't insert (duplicate), try to find the existing record
        endpointRecord = await db.prepare(
          'SELECT id FROM api_endpoints WHERE endpoint = ? AND method = ?'
        ).bind(endpoint, method).first();
        
        if (endpointRecord) {
          return endpointRecord.id;
        }
      } catch (insertError) {
        // If there's still an error, try to find existing record
        console.warn('Insert failed, trying to find existing endpoint:', insertError.message);
        endpointRecord = await db.prepare(
          'SELECT id FROM api_endpoints WHERE endpoint = ? AND method = ?'
        ).bind(endpoint, method).first();
        
        if (endpointRecord) {
          return endpointRecord.id;
        }
      }
    } else {
      return endpointRecord.id;
    }
    
    console.error('Failed to get or create endpoint ID for:', endpoint, method);
    return null;
  } catch (error) {
    console.error('Error getting endpoint ID:', error);
    return null;
  }
}

/**
 * Log API call details
 * @param {Object} db - D1 database instance
 * @param {Object} logData - API call log data
 * @returns {Promise<number|null>} - Log record ID or null if failed
 */
export async function logApiCall(db, logData) {
  if (!db) return null;

  try {
    let endpointId = await getEndpointId(db, logData.endpoint_path, logData.method);
    if (!endpointId) {
      console.warn('Failed to get endpoint ID for logging, using fallback endpoint');
      // Get the fallback endpoint ID
      const fallbackEndpoint = await db.prepare(
        'SELECT id FROM api_endpoints WHERE endpoint = ? AND method = ?'
      ).bind('unknown', 'UNKNOWN').first();
      
      if (fallbackEndpoint) {
        endpointId = fallbackEndpoint.id;
      } else {
        console.error('Fallback endpoint not found, cannot log API call');
        return null;
      }
    }

    const result = await db.prepare(`
      INSERT INTO api_call_logs (
        endpoint_id, endpoint_path, method, query_params, request_body,
        user_agent, ip_address, referer, auth_type, auth_provided, auth_valid,
        status_code, response_size_bytes, response_time_ms, response_body, success,
        error_message, error_code, cache_hit, external_api_calls, external_api_cost,
        items_processed, items_successful, items_failed, data_source,
        started_at, completed_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      endpointId,
      logData.endpoint_path,
      logData.method,
      logData.query_params || null,
      logData.request_body || null,
      logData.user_agent || null,
      logData.ip_address || null,
      logData.referer || null,
      logData.auth_type || 'none',
      logData.auth_provided || false,
      logData.auth_valid || false,
      logData.status_code,
      logData.response_size_bytes || 0,
      logData.response_time_ms,
      logData.response_body || null,
      logData.success,
      logData.error_message || null,
      logData.error_code || null,
      logData.cache_hit || false,
      logData.external_api_calls || 0,
      logData.external_api_cost || 0.0,
      logData.items_processed || 0,
      logData.items_successful || 0,
      logData.items_failed || 0,
      logData.data_source || null,
      logData.started_at,
      logData.completed_at
    ).run();

    // Update usage statistics
    await updateUsageStats(db, endpointId, logData);

    return result.meta.last_row_id;
  } catch (error) {
    console.error('Error logging API call:', error);
    return null;
  }
}

/**
 * Update hourly usage statistics
 * @param {Object} db - D1 database instance
 * @param {number} endpointId - Endpoint ID
 * @param {Object} logData - API call log data
 */
async function updateUsageStats(db, endpointId, logData) {
  if (!db) return;

  try {
    const startedAt = new Date(logData.started_at);
    const date = startedAt.toISOString().split('T')[0]; // YYYY-MM-DD
    const hour = startedAt.getHours();

    // Get current stats for this hour
    const currentStats = await db.prepare(`
      SELECT * FROM api_usage_stats 
      WHERE endpoint_id = ? AND date = ? AND hour = ?
    `).bind(endpointId, date, hour).first();

    if (currentStats) {
      // Update existing stats
      await db.prepare(`
        UPDATE api_usage_stats SET
          total_calls = total_calls + 1,
          successful_calls = successful_calls + ?,
          failed_calls = failed_calls + ?,
          cache_hits = cache_hits + ?,
          total_response_time_ms = total_response_time_ms + ?,
          avg_response_time_ms = (total_response_time_ms + ?) / (total_calls + 1),
          min_response_time_ms = MIN(min_response_time_ms, ?),
          max_response_time_ms = MAX(max_response_time_ms, ?),
          total_response_bytes = total_response_bytes + ?,
          avg_response_bytes = (total_response_bytes + ?) / (total_calls + 1),
          total_external_api_calls = total_external_api_calls + ?,
          total_external_api_cost = total_external_api_cost + ?,
          total_items_processed = total_items_processed + ?,
          total_items_successful = total_items_successful + ?,
          total_items_failed = total_items_failed + ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE endpoint_id = ? AND date = ? AND hour = ?
      `).bind(
        logData.success ? 1 : 0,
        logData.success ? 0 : 1,
        logData.cache_hit ? 1 : 0,
        logData.response_time_ms,
        logData.response_time_ms,
        logData.response_time_ms,
        logData.response_time_ms,
        logData.response_size_bytes || 0,
        logData.response_size_bytes || 0,
        logData.external_api_calls || 0,
        logData.external_api_cost || 0.0,
        logData.items_processed || 0,
        logData.items_successful || 0,
        logData.items_failed || 0,
        endpointId,
        date,
        hour
      ).run();
    } else {
      // Create new stats record
      await db.prepare(`
        INSERT INTO api_usage_stats (
          endpoint_id, date, hour, total_calls, successful_calls, failed_calls,
          cache_hits, avg_response_time_ms, total_response_time_ms,
          min_response_time_ms, max_response_time_ms, total_response_bytes,
          avg_response_bytes, total_external_api_calls, total_external_api_cost,
          total_items_processed, total_items_successful, total_items_failed
        ) VALUES (?, ?, ?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        endpointId,
        date,
        hour,
        logData.success ? 1 : 0,
        logData.success ? 0 : 1,
        logData.cache_hit ? 1 : 0,
        logData.response_time_ms,
        logData.response_time_ms,
        logData.response_time_ms,
        logData.response_time_ms,
        logData.response_size_bytes || 0,
        logData.response_size_bytes || 0,
        logData.external_api_calls || 0,
        logData.external_api_cost || 0.0,
        logData.items_processed || 0,
        logData.items_successful || 0,
        logData.items_failed || 0
      ).run();
    }
  } catch (error) {
    console.error('Error updating usage stats:', error);
  }
}

/**
 * Get API usage statistics
 * @param {Object} db - D1 database instance
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Usage statistics
 */
export async function getUsageStats(db, options = {}) {
  if (!db) return null;

  try {
    const {
      endpoint = null,
      startDate = null,
      endDate = null,
      granularity = 'hour' // 'hour' or 'day'
    } = options;

    let query = `
      SELECT 
        e.endpoint,
        e.category,
        s.date,
        ${granularity === 'hour' ? 's.hour,' : ''}
        SUM(s.total_calls) as total_calls,
        SUM(s.successful_calls) as successful_calls,
        SUM(s.failed_calls) as failed_calls,
        SUM(s.cache_hits) as cache_hits,
        AVG(s.avg_response_time_ms) as avg_response_time_ms,
        SUM(s.total_response_bytes) as total_response_bytes,
        SUM(s.total_external_api_calls) as total_external_api_calls,
        SUM(s.total_external_api_cost) as total_external_api_cost,
        SUM(s.total_items_processed) as total_items_processed
      FROM api_usage_stats s
      JOIN api_endpoints e ON s.endpoint_id = e.id
      WHERE 1=1
    `;

    const bindings = [];

    if (endpoint) {
      query += ` AND e.endpoint = ?`;
      bindings.push(endpoint);
    }

    if (startDate) {
      query += ` AND s.date >= ?`;
      bindings.push(startDate);
    }

    if (endDate) {
      query += ` AND s.date <= ?`;
      bindings.push(endDate);
    }

    if (granularity === 'hour') {
      query += ` GROUP BY e.endpoint, s.date, s.hour ORDER BY s.date DESC, s.hour DESC`;
    } else {
      query += ` GROUP BY e.endpoint, s.date ORDER BY s.date DESC`;
    }

    query += ` LIMIT 1000`;

    const stmt = db.prepare(query);
    const results = await stmt.bind(...bindings).all();

    return {
      stats: results.results || [],
      total_records: results.results ? results.results.length : 0,
      granularity,
      period: { startDate, endDate }
    };
  } catch (error) {
    console.error('Error getting usage stats:', error);
    return null;
  }
}

/**
 * Get recent API call logs
 * @param {Object} db - D1 database instance
 * @param {Object} options - Query options
 * @returns {Promise<Array>} - Recent API call logs
 */
export async function getRecentLogs(db, options = {}) {
  if (!db) return [];

  try {
    const {
      limit = 100,
      endpoint = null,
      success = null,
      startDate = null
    } = options;

    let query = `
      SELECT 
        l.*,
        e.endpoint,
        e.category
      FROM api_call_logs l
      JOIN api_endpoints e ON l.endpoint_id = e.id
      WHERE 1=1
    `;

    const bindings = [];

    if (endpoint) {
      query += ` AND e.endpoint = ?`;
      bindings.push(endpoint);
    }

    if (success !== null) {
      query += ` AND l.success = ?`;
      bindings.push(success);
    }

    if (startDate) {
      query += ` AND l.started_at >= ?`;
      bindings.push(startDate);
    }

    query += ` ORDER BY l.started_at DESC LIMIT ?`;
    bindings.push(limit);

    const stmt = db.prepare(query);
    const results = await stmt.bind(...bindings).all();

    return results.results || [];
  } catch (error) {
    console.error('Error getting recent logs:', error);
    return [];
  }
}