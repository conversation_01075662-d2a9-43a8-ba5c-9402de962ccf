/**
 * Test script for quota management system
 */

import { quotaManager } from '../lib/quota-manager.js';
import { config } from '../lib/config.js';

async function testQuotaSystem() {
  console.log('Testing RapidAPI Quota Management System\n');

  const testUrl = 'https://similarweb-insights.p.rapidapi.com/test';
  
  try {
    // Test 1: Check initial quota status
    console.log('1. Initial quota status:');
    const initialStats = await quotaManager.getUsageStats(testUrl);
    console.log(JSON.stringify(initialStats, null, 2));

    // Test 2: Check if request is allowed
    console.log('\n2. Testing quota check:');
    const canMake = await quotaManager.canMakeRequest(testUrl);
    console.log(`Can make request: ${canMake}`);

    // Test 3: Simulate API usage
    console.log('\n3. Simulating API usage:');
    for (let i = 0; i < 5; i++) {
      await quotaManager.recordUsage(testUrl);
      const stats = await quotaManager.getUsageStats(testUrl);
      console.log(`Usage ${i + 1}: Monthly ${stats.monthly.used}/${stats.monthly.limit}, Daily ${stats.daily.used}/${stats.daily.limit || 'unlimited'}`);
    }

    // Test 4: Test quota limits
    console.log('\n4. Testing quota limits:');
    const quotaConfig = config.RAPIDAPI_QUOTAS['similarweb-insights.p.rapidapi.com'];
    
    // Simulate reaching monthly limit
    for (let i = 5; i < quotaConfig.monthly_limit; i++) {
      await quotaManager.recordUsage(testUrl);
    }

    const statsAtLimit = await quotaManager.getUsageStats(testUrl);
    console.log('At limit:', JSON.stringify(statsAtLimit, null, 2));

    // Test 5: Test quota exceeded
    console.log('\n5. Testing quota exceeded:');
    const canMakeAtLimit = await quotaManager.canMakeRequest(testUrl);
    console.log(`Can make request at limit: ${canMakeAtLimit}`);

    // Try to record one more usage
    await quotaManager.recordUsage(testUrl);
    const canMakeOverLimit = await quotaManager.canMakeRequest(testUrl);
    console.log(`Can make request over limit: ${canMakeOverLimit}`);

    // Test 6: Reset quota
    console.log('\n6. Resetting quota:');
    await quotaManager.resetQuota(testUrl);
    const statsAfterReset = await quotaManager.getUsageStats(testUrl);
    console.log('After reset:', JSON.stringify(statsAfterReset, null, 2));

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run tests
testQuotaSystem();