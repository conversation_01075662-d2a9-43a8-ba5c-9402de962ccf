/**
 * Test script for API adapter layer and quota management integration
 */

import { rapidApiClient } from '../lib/api-adapter.js';
import { quotaManager } from '../lib/quota-manager.js';

async function testApiAdapterSystem() {
  console.log('Testing RapidAPI Adapter Layer and Quota Integration\n');

  try {
    // Test 1: Test connection to all APIs
    console.log('1. Testing API connections:');
    const connections = await rapidApiClient.testConnections();
    console.log(JSON.stringify(connections, null, 2));

    // Test 2: Get quota status for all APIs
    console.log('\n2. Current quota status:');
    const quotaStatus = await rapidApiClient.getQuotaStatus();
    console.log(JSON.stringify(quotaStatus, null, 2));

    // Test 3: Test SimilarWeb traffic API (if quota allows)
    console.log('\n3. Testing SimilarWeb traffic API:');
    try {
      const trafficResult = await rapidApiClient.getSimilarWebTraffic('example.com');
      console.log('Traffic Result:', JSON.stringify(trafficResult, null, 2));
    } catch (error) {
      console.log('Traffic API Error:', error.message);
    }

    // Test 4: Test SimilarWeb overview API (if quota allows)
    console.log('\n4. Testing SimilarWeb overview API:');
    try {
      const overviewResult = await rapidApiClient.getSimilarWebOverview('example.com');
      console.log('Overview Result:', JSON.stringify(overviewResult, null, 2));
    } catch (error) {
      console.log('Overview API Error:', error.message);
    }

    // Test 5: Test Google search API (if quota allows)
    console.log('\n5. Testing Google search API:');
    try {
      const searchResult = await rapidApiClient.getGoogleSearchResults('test query', { num: 5 });
      console.log('Search Result:', JSON.stringify(searchResult, null, 2));
    } catch (error) {
      console.log('Search API Error:', error.message);
    }

    // Test 6: Test SEO analysis API (if quota allows)
    console.log('\n6. Testing SEO analysis API:');
    try {
      const seoResult = await rapidApiClient.getSEOAnalysis('https://example.com');
      console.log('SEO Result:', JSON.stringify(seoResult, null, 2));
    } catch (error) {
      console.log('SEO API Error:', error.message);
    }

    // Test 7: Test quota enforcement by making multiple requests
    console.log('\n7. Testing quota enforcement:');
    for (let i = 0; i < 3; i++) {
      try {
        console.log(`Making request ${i + 1}...`);
        const testResult = await rapidApiClient.getSimilarWebTraffic(`test${i}.com`);
        console.log(`Request ${i + 1} result:`, testResult.success ? 'Success' : `Failed: ${testResult.error}`);
      } catch (error) {
        console.log(`Request ${i + 1} error:`, error.message);
      }
    }

    // Test 8: Final quota status after tests
    console.log('\n8. Final quota status:');
    const finalQuotaStatus = await rapidApiClient.getQuotaStatus();
    console.log(JSON.stringify(finalQuotaStatus, null, 2));

    console.log('\n✅ All adapter tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test quota management functions separately
async function testQuotaManagement() {
  console.log('\n=== Testing Quota Management Functions ===\n');

  try {
    const testApiUrl = 'https://similarweb-insights.p.rapidapi.com/test';

    // Test quota status
    console.log('1. Testing quota status:');
    const stats = await quotaManager.getUsageStats(testApiUrl);
    console.log(JSON.stringify(stats, null, 2));

    // Test can make request
    console.log('\n2. Testing request permission:');
    const canMake = await quotaManager.canMakeRequest(testApiUrl);
    console.log(`Can make request: ${canMake}`);

    // Test record usage
    console.log('\n3. Testing usage recording:');
    await quotaManager.recordUsage(testApiUrl);
    const updatedStats = await quotaManager.getUsageStats(testApiUrl);
    console.log('After recording usage:', JSON.stringify(updatedStats, null, 2));

    console.log('\n✅ Quota management tests completed!');

  } catch (error) {
    console.error('❌ Quota management test failed:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  await testApiAdapterSystem();
  await testQuotaManagement();
}

runAllTests();