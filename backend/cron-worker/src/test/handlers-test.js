/**
 * Test refactored handlers modules
 */

async function testHandlers() {
  try {
    // Test api-handlers.js (index)
    const handlers = await import('../handlers/api-handlers.js');
    console.log('✅ api-handlers.js loaded successfully');
    console.log('Available handlers:', Object.keys(handlers).join(', '));

    // Test core handlers
    const coreHandlers = await import('../handlers/core-handlers.js');
    console.log('✅ core-handlers.js loaded successfully');
    console.log('Core handlers:', Object.keys(coreHandlers).join(', '));

    // Test quota handlers
    const quotaHandlers = await import('../handlers/quota-handlers.js');
    console.log('✅ quota-handlers.js loaded successfully');
    console.log('Quota handlers:', Object.keys(quotaHandlers).join(', '));

    // Test logs handlers
    const logsHandlers = await import('../handlers/logs-handlers.js');
    console.log('✅ logs-handlers.js loaded successfully');
    console.log('Logs handlers:', Object.keys(logsHandlers).join(', '));

    console.log('\n✅ All handler modules loaded successfully!');
    console.log('Refactoring completed without breaking functionality.');

  } catch (error) {
    console.error('❌ Error loading handlers:', error.message);
  }
}

testHandlers();