/**
 * <PERSON><PERSON> test for Cloudflare KV quota management
 * Since we can't test real Cloudflare KV locally, this simulates the behavior
 */

import { CloudflareKVStore } from '../lib/kv-store.js';
import { QuotaManager } from '../lib/quota-manager.js';

// Mock Cloudflare KV namespace
class MockKVNamespace {
  constructor() {
    this.data = new Map();
  }

  async get(key) {
    return this.data.get(key) || null;
  }

  async put(key, value) {
    this.data.set(key, value);
  }

  async delete(key) {
    return this.data.delete(key);
  }

  async list(options = {}) {
    const keys = Array.from(this.data.keys());
    const filteredKeys = options.prefix 
      ? keys.filter(key => key.startsWith(options.prefix))
      : keys;
    
    return {
      keys: filteredKeys.map(name => ({ name })),
      list_complete: true
    };
  }
}

async function testCloudflareKVQuotaSystem() {
  console.log('Testing Cloudflare KV Quota Management System\n');

  // Create mock KV namespace
  const mockKV = new MockKVNamespace();
  
  // Create KV store and quota manager
  const kvStore = new CloudflareKVStore();
  const quotaManager = new QuotaManager();
  
  // Initialize with mock KV
  kvStore.setNamespace(mockKV);
  quotaManager.init(mockKV);

  try {
    const testApiHost = 'similarweb-insights.p.rapidapi.com';
    const testUrl = `https://${testApiHost}/test`;

    // Test 1: Initial quota status
    console.log('1. Initial quota status:');
    const initialStats = await quotaManager.getUsageStats(testUrl);
    console.log(JSON.stringify(initialStats, null, 2));

    // Test 2: Test KV operations
    console.log('\n2. Testing KV operations:');
    await kvStore.set('test-key', 42);
    const value = await kvStore.get('test-key');
    console.log(`Set and get test: ${value}`);

    // Test 3: Test usage tracking
    console.log('\n3. Testing usage tracking:');
    for (let i = 0; i < 5; i++) {
      await quotaManager.recordUsage(testUrl);
      const monthlyUsage = await kvStore.getMonthlyUsage(testApiHost);
      console.log(`Usage ${i + 1}: Monthly ${monthlyUsage}`);
    }

    // Test 4: Test quota checking
    console.log('\n4. Testing quota limits:');
    const canMake = await quotaManager.canMakeRequest(testUrl);
    console.log(`Can make request: ${canMake}`);

    // Test 5: Test quota reset
    console.log('\n5. Testing quota reset:');
    const monthlyKey = kvStore.getMonthlyKey(testApiHost);
    await kvStore.delete(monthlyKey);
    const afterReset = await kvStore.getMonthlyUsage(testApiHost);
    console.log(`After reset: ${afterReset}`);

    // Test 6: Test cleanup
    console.log('\n6. Testing cleanup:');
    await kvStore.cleanupOldQuotas();
    const quotaKeys = await kvStore.listQuotaKeys();
    console.log(`Quota keys after cleanup: ${quotaKeys.length}`);

    // Test 7: Test batch operations
    console.log('\n7. Testing batch operations:');
    await kvStore.set('quota:monthly:test1.com:2025-01', 10);
    await kvStore.set('quota:monthly:test2.com:2025-01', 20);
    await kvStore.set('quota:daily:test3.com:2025-06-29', 5);
    
    const allKeys = await kvStore.listQuotaKeys();
    console.log('All quota keys:', allKeys.map(k => k.name));

    // Test 8: Test environment simulation
    console.log('\n8. Testing environment simulation:');
    const mockEnv = {
      QUOTA_KV: mockKV,
      AUTH_TOKEN: 'test-token'
    };
    
    // Simulate API call with environment
    const quotaStatus = {};
    const apiHosts = ['similarweb-insights.p.rapidapi.com', 'google-search-results.p.rapidapi.com'];
    
    for (const apiHost of apiHosts) {
      const monthlyUsage = await kvStore.getMonthlyUsage(apiHost);
      const dailyUsage = await kvStore.getDailyUsage(apiHost);
      
      quotaStatus[apiHost] = {
        monthly: { used: monthlyUsage },
        daily: { used: dailyUsage }
      };
    }
    
    console.log('Simulated quota status:', JSON.stringify(quotaStatus, null, 2));

    console.log('\n✅ All Cloudflare KV tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Show KV namespace structure
function showKVStructure() {
  console.log('\n=== Cloudflare KV Structure ===');
  console.log('Key patterns:');
  console.log('- Monthly quota: quota:monthly:{api-host}:{YYYY-MM}');
  console.log('- Daily quota:   quota:daily:{api-host}:{YYYY-MM-DD}');
  console.log('');
  console.log('Example keys:');
  console.log('- quota:monthly:similarweb-insights.p.rapidapi.com:2025-06');
  console.log('- quota:daily:google-search-results.p.rapidapi.com:2025-06-29');
  console.log('');
  console.log('KV features used:');
  console.log('- get(key) - Get quota count');
  console.log('- put(key, value) - Set quota count');
  console.log('- delete(key) - Reset quota');
  console.log('- list({prefix: "quota:"}) - List all quotas');
}

// Run tests
showKVStructure();
testCloudflareKVQuotaSystem();