# 智能API选择架构文档

## 架构概述

新的智能API选择架构将cron-worker从手动API管理重构为智能化的API选择和调度系统。核心思想是让Service层专注于业务逻辑，而将API endpoint的选择决策权转移到quota-manager中。

## 架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        LinkTrackPro Cron Worker                │
│                     智能API选择架构 v2.0                          │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Core Handlers │    │   Processors    │    │   API Routes    │
│                 │    │                 │    │                 │
│ triggerTraffic  │    │ processTraffic  │    │ /api/traffic    │
│ triggerDR       │    │ processDR       │    │ /api/dr         │
│ triggerLinks    │    │ processLinks    │    │ /api/links      │
│ triggerIndexing │    │ processIndexing │    │ /api/indexing   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │     Service Layer       │
                    │   (Business Logic)      │
                    │                         │
                    │ ┌─────────────────────┐ │
                    │ │  traffic.js         │ │
                    │ │  indexing.js        │ │
                    │ │  external-links.js  │ │
                    │ │  domain-rating.js   │ │
                    │ └─────────────────────┘ │
                    └────────────┬────────────┘
                                 │
              ┌──────────────────▼──────────────────┐
              │        智能API管理层                  │
              │      (quota-manager.js)             │
              │                                     │
              │ ┌─────────────────────────────────┐ │
              │ │    智能API选择引擎                │ │
              │ │                                 │ │
              │ │ • 业务类型策略配置                │ │
              │ │ • 多维度评分算法                  │ │
              │ │ • 优化模式 (balanced/cost/...)   │ │
              │ │ • 自动回退机制                    │ │
              │ └─────────────────────────────────┘ │
              │                                     │
              │ ┌─────────────────────────────────┐ │
              │ │    配额管理与监控                │ │
              │ │                                 │ │
              │ │ • 实时配额检查                    │ │
              │ │ • 使用统计跟踪                    │ │
              │ │ • 限制预警机制                    │ │
              │ └─────────────────────────────────┘ │
              └──────────────────┬──────────────────┘
                                 │
               ┌─────────────────▼─────────────────┐
               │      统一API适配层                  │
               │     (api-adapter.js)              │
               │                                   │
               │ ┌─────────────────────────────┐   │
               │ │   API配置与适配器             │   │
               │ │                             │   │
               │ │ • 请求参数标准化              │   │
               │ │ • 响应数据格式化              │   │
               │ │ • 错误处理统一化              │   │
               │ └─────────────────────────────┘   │
               └─────────────────┬─────────────────┘
                                 │
    ┌────────────────────────────▼────────────────────────────┐
    │                     外部API服务                           │
    └─────────────────────────────────────────────────────────┘
    │
    ├─ RapidAPI 服务
    │  ├─ SimilarWeb Insights
    │  ├─ Google Search Results  
    │  ├─ SEO Analysis API
    │  ├─ Ahrefs Backlinks
    │  └─ Ahrefs DR Checker
    │
    ├─ 直接API服务
    │  ├─ SimilarWeb Direct
    │  ├─ Google Custom Search
    │  ├─ Exa AI Search
    │  └─ Ahrefs Official
    │
    └─ 数据存储
       ├─ Cloudflare KV (配额数据)
       └─ PostgreSQL (缓存与结果)
```

## 核心组件

### 1. 智能API选择引擎 (quota-manager.js)

智能API选择引擎是整个架构的核心，负责：

#### 业务类型策略配置
```javascript
const strategies = {
  'traffic': {
    primary_sources: [
      { id: 'similarweb_direct', priority: 1, cost: 0, reliability: 0.7 },
      { id: 'similarweb_rapidapi', priority: 2, cost: 1, reliability: 0.9 }
    ]
  },
  'indexing': { /* ... */ },
  'external_links': { /* ... */ },
  'domain_rating': { /* ... */ }
}
```

#### 多维度评分算法
- **优先级分数**: 基于API配置的优先级排序
- **配额可用性分数**: 基于当前配额使用情况
- **可靠性分数**: 基于历史成功率
- **成本效益分数**: 基于API调用成本

#### 优化模式
- `balanced`: 平衡模式，综合考虑各项指标
- `cost_first`: 成本优先，优先使用免费/低成本API
- `quality_first`: 质量优先，优先使用数据质量最高的API  
- `quota_preserve`: 配额保护，优先使用配额影响最小的API

### 2. Service层重构

Service层现在专注于业务逻辑，不再关心具体使用哪个API：

```javascript
// 之前的方式
export async function fetchTrafficData(domain) {
  // Step 1: 尝试SimilarWeb Direct API
  // Step 2: 回退到RapidAPI SimilarWeb  
  // Step 3: 返回默认数据
}

// 新的方式  
export async function fetchTrafficData(domain, optimizationMode = 'balanced') {
  const result = await quotaManager.executeWithIntelligentSelection(
    'traffic', 
    [domain], 
    optimizationMode
  );
  return normalizeTrafficData(result.data, domain);
}
```

### 3. 统一API适配层 (api-adapter.js)

提供统一的API调用接口，支持多种API类型：
- RapidAPI服务
- 直接API服务 (Google, Exa, SimilarWeb)
- Ahrefs官方API

## 数据流

### 1. API请求流程
```
Service Layer Request
        ↓
智能API选择引擎
        ↓  
配额检查 + API评分
        ↓
选择最优API endpoint
        ↓
统一API适配层
        ↓
外部API调用
        ↓
响应数据标准化
        ↓
返回给Service Layer
```

### 2. 回退机制
```
主选API失败
        ↓
尝试备选API (按评分排序)
        ↓
所有API都失败
        ↓
执行回退策略
        ↓ 
返回默认/模拟数据
```

## 配额管理策略

### 1. 实时配额监控
- 月度配额跟踪
- 日配额跟踪  
- 配额预警机制
- 超限保护

### 2. 智能配额分配
- 根据业务优先级分配配额
- 动态调整API使用策略
- 配额使用优化建议

### 3. 配额数据存储
```javascript
// Cloudflare KV 存储结构
{
  "quota:monthly:api.host:2024-12": { used: 45, limit: 100 },
  "quota:daily:api.host:2024-12-29": { used: 5, limit: 10 }
}
```

## API策略配置

### Traffic数据收集策略
```
1. SimilarWeb Direct (免费，可靠性中等)
2. SimilarWeb RapidAPI (付费，可靠性高)
```

### 索引状态检查策略  
```
1. Google Link:Site Search (精确度最高)
2. Google Site Search (通用搜索)
3. Exa AI Search (AI增强搜索)
```

### 外部链接发现策略
```
1. Google Custom Search (免费配额充足)
2. Ahrefs RapidAPI (付费，数据质量高)
```

### 域名评级策略
```
1. Ahrefs Official API (最高质量，高成本)
2. SimilarWeb Overview (中等质量，中等成本)
3. SEO Analysis API (基础分析)
4. Ahrefs DR Checker (快速检查)
```

## 性能优化

### 1. 批量处理优化
- 智能延迟控制
- 配额感知的批量大小调整
- 失败重试机制

### 2. 缓存策略
- 数据库缓存优先
- 智能缓存失效
- 缓存命中率监控

### 3. 监控与分析
```javascript
// API使用分析
{
  business_type: 'traffic',
  available_apis: 2,
  api_status: [
    { id: 'similarweb_direct', available: true, quota_remaining: 155 },
    { id: 'similarweb_rapidapi', available: true, quota_remaining: 25 }
  ]
}
```

## 优势总结

### 1. 架构优势
- **关注点分离**: Service层专注业务逻辑
- **智能决策**: 自动选择最优API
- **高可用性**: 多重回退机制
- **成本优化**: 智能配额管理

### 2. 运维优势  
- **实时监控**: 配额使用状态可视化
- **预警机制**: 提前发现配额问题
- **灵活配置**: 支持多种优化策略
- **易于扩展**: 新增API仅需配置

### 3. 业务优势
- **数据质量**: 智能选择最优数据源
- **成本控制**: 避免不必要的API调用
- **稳定性**: 减少单点故障风险
- **响应速度**: 优化API调用路径

## 使用示例

### 1. 基础使用
```javascript
// 获取流量数据 - 平衡模式
const traffic = await fetchTrafficData('example.com');

// 获取域名评级 - 质量优先模式  
const rating = await fetchDomainRating('example.com', 'quality_first');

// 检查索引状态 - 成本优先模式
const indexing = await checkIndexingStatus('https://example.com/page', null, 'cost_first');
```

### 2. 批量处理
```javascript
// 批量获取流量数据 - 配额保护模式
const domains = ['site1.com', 'site2.com', 'site3.com'];
const results = await fetchBatchTrafficData(domains, 'quota_preserve');
```

### 3. API状态监控
```javascript
// 获取API使用分析
const analytics = await getTrafficApiAnalytics();
console.log(`可用API数量: ${analytics.data.available_apis}`);
```

这个新架构实现了您要求的核心目标：
1. **Service层专注业务逻辑**
2. **quota-manager负责API选择**  
3. **智能化的配额和优先级管理**
4. **支持所有业务类型** (traffic、indexing、external_links、domain_rating)

通过这种设计，系统具备了更强的可维护性、可扩展性和智能化水平。