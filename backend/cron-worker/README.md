# LinkTrackPro Cron Worker

这是LinkTrackPro的定时任务工作器，使用Cloudflare Worker处理SEO指标的定时收集，并更新数据库。

## 功能特性

- **每日任务**:
  - 从SimilarWeb API获取所有项目和链接的流量信息
  
- **每周任务** (每周日执行):
  - 从Ahrefs API获取所有项目和链接的域名评级 (DR)
  - 从Google Search API获取外链数量和详细信息
  - 通过Google API检查所有链接的索引状态
  
- **附加功能**:
  - 批处理优化API使用和成本
  - 带重试机制的错误处理
  - 飞书webhook集成用于通知和报告
  - 历史数据跟踪以进行趋势分析
  - Supabase数据库集成

## 自测脚本

本项目提供了完整的测试体系，包括三个层次的测试脚本，所有配置自动从 `wrangler.toml` 加载：

### 快速开始

```bash
# 基础功能测试（推荐首次使用）
npm test

# 运行所有测试套件
npm run test:all

# 查看配置状态
npm run test:manual config

# 检查系统健康状况
npm run test:manual health-check
```

### 1. 基础测试 (`npm test`)

**用途**: 核心功能验证，无需外部依赖
**包含测试**:
- ✅ 配置加载（从wrangler.toml）
- ✅ 身份验证逻辑
- ✅ 模块导入和结构
- ✅ Worker端点验证
- ✅ 环境变量检查
- ✅ 调度逻辑验证

### 2. 完整测试套件 (`npm run test:full`)

**用途**: 全面的自动化测试
**包含测试**:
- 🔧 配置验证和API密钥检查
- 🗄️ 数据库连接测试
- 🔐 身份验证机制测试
- 🌐 服务可用性测试
- ⚙️ 数据处理器结构测试
- 🔗 Worker端点和定时任务测试
- 🌐 HTTP端点路由和触发器测试

### 3. 手动测试工具 (`npm run test:manual`)

**用途**: 交互式测试和调试

#### 配置和状态检查
```bash
npm run test:manual config        # 显示完整配置状态
npm run test:manual health-check  # 系统健康检查
```

#### 单个组件测试
```bash
npm run test:manual test-feishu      # 测试飞书通知
npm run test:manual test-traffic     # 测试流量数据收集
npm run test:manual test-dr          # 测试域名评级收集
npm run test:manual test-links       # 测试外链收集
npm run test:manual test-indexing    # 测试索引状态检查
```

#### 模拟执行测试
```bash
npm run test:manual simulate-cron                    # 模拟日常执行
npm run test:manual simulate-cron --day=sunday       # 模拟周日执行
npm run test:manual worker-fetch --endpoint=/trigger/traffic
```

### 4. 数据库连接测试

```bash
npm run test:db  # 专门的数据库连接和表结构检查
```

### 5. 完整测试套件

```bash
npm run test:all  # 运行所有测试并生成汇总报告
```

### 配置状态

测试脚本自动从 `wrangler.toml` 读取配置，当前状态：

**✅ 已配置的服务**:
- 数据库连接 (Supabase)
- 身份验证令牌
- 飞书通知webhook
- 基础配置参数

**⚠️ 需要配置的API密钥**:
- SIMILARWEB_API_KEY (当前为placeholder)
- AHREFS_API_KEY (当前为placeholder)
- GOOGLE_API_KEY (当前为placeholder)
- GOOGLE_SEARCH_ENGINE_ID (当前为placeholder)

**🗄️ 数据库状态**:
- 连接正常 ✅
- Schema: link_track ✅
- 基础表存在 (projects, links) ✅
- 统计表需要创建 (project_stats, link_stats) ⚠️

### 测试结果解读

#### 成功状态
- ✅ 绿色勾号：功能正常
- 📋 信息符号：配置详情
- 🎉 庆祝符号：系统就绪

#### 警告状态  
- ⚠️ 警告符号：需要注意但不影响基本功能
- 💡 灯泡符号：提示和建议

#### 错误状态
- ❌ 错误符号：功能异常，需要修复
- 🚨 警报符号：严重问题

## 代码架构

### 分层设计

代码采用分层架构设计，职责分离，便于维护和扩展：

```
src/
├── index.js                 # 主入口文件，调度逻辑和路由
├── lib/                     # 通用工具库
│   ├── config.js           # 配置管理
│   ├── auth.js             # 认证相关
│   └── utils.js            # 通用工具函数
├── services/               # 第三方API服务
│   ├── similarweb.js       # SimilarWeb API 服务
│   ├── ahrefs.js           # Ahrefs API 服务
│   ├── google.js           # Google APIs 服务
│   └── feishu.js           # 飞书 webhook 服务
├── processors/             # 数据处理器
│   ├── traffic.js          # 流量数据处理
│   ├── domain-rating.js    # 域名评级处理
│   ├── external-links.js   # 外链数据处理
│   └── indexing.js         # 索引状态处理
└── db.js                   # 数据库操作
```

### 模块说明

#### 📁 lib/ - 通用工具库
- **config.js**: 统一管理所有配置项，从环境变量加载配置
- **auth.js**: 处理API请求的身份验证
- **utils.js**: 提供批处理、重试机制等通用工具函数

#### 📁 services/ - 第三方API服务
- **similarweb.js**: SimilarWeb API集成，获取流量数据
- **ahrefs.js**: Ahrefs API集成，获取域名评级
- **google.js**: Google Search和Indexing API集成
- **feishu.js**: 飞书webhook集成，发送通知消息

#### 📁 processors/ - 数据处理器
- **traffic.js**: 处理SimilarWeb流量数据的收集、更新和报告生成
- **domain-rating.js**: 处理Ahrefs域名评级数据
- **external-links.js**: 处理Google外链数据
- **indexing.js**: 处理Google索引状态数据

## 设置和部署

### 前置要求

- [Node.js](https://nodejs.org/) (v16+)
- [Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/) 用于Cloudflare Workers部署
- 拥有Workers订阅的Cloudflare账户
- SimilarWeb、Ahrefs和Google服务的API密钥
- 拥有service role key的Supabase账户
- 飞书webhook URL用于通知

### 安装

1. 克隆仓库并导航到cron-worker目录:
   ```bash
   cd backend/cron-worker
   ```

2. 安装依赖:
   ```bash
   npm install
   ```

3. 复制环境变量示例文件并填入API密钥:
   ```bash
   cp env.example .env
   ```

4. 使用实际的API密钥和配置编辑`.env`文件。

### 本地开发

1. 启动开发服务器:
   ```bash
   npm run dev
   ```

2. 测试特定端点:
   - `/trigger/traffic` - 手动触发流量数据收集
   - `/trigger/dr` - 手动触发域名评级收集
   - `/trigger/links` - 手动触发外链收集
   - `/trigger/indexing` - 手动触发索引状态检查

### 部署

1. 登录Cloudflare:
   ```bash
   npx wrangler login
   ```

2. 设置密钥 (API keys):
   ```bash
   npx wrangler secret put SIMILARWEB_API_KEY
   npx wrangler secret put AHREFS_API_KEY
   npx wrangler secret put GOOGLE_API_KEY
   npx wrangler secret put GOOGLE_SEARCH_ENGINE_ID
   npx wrangler secret put RAPIDAPI_KEY
   npx wrangler secret put EXA_API_KEY
   npx wrangler secret put API_LAYER_KEY
   npx wrangler secret put FEISHU_WEBHOOK_URL
   npx wrangler secret put SUPABASE_URL
   npx wrangler secret put SUPABASE_KEY
   ```

3. 部署到Cloudflare Workers:
   ```bash
   npm run deploy
   ```

## API集成详情

### SimilarWeb API

工作器使用SimilarWeb API收集域名和URL的流量数据。处理这些数据以更新数据库中的流量指标。

### Ahrefs API

Ahrefs API用于收集所有域名和URL的域名评级(DR)信息。这些数据有助于跟踪网站的权威度和SEO表现。

### Google Search API

Google Search API用于收集指向数据库中域名的外链信息。这有助于跟踪反向链接配置文件和SEO表现。

### Google Indexing API

Google Indexing API用于检查URL是否在Google搜索结果中被索引。这有助于跟踪链接在搜索引擎中的可见性。

## 数据库集成

工作器连接到Supabase数据库以:

1. 获取所有需要处理的项目和链接
2. 更新流量数据、域名评级、外链数量和索引状态
3. 存储历史数据用于趋势分析

## Webhook通知

每个任务完成后，工作器向飞书webhook发送通知，包含结果摘要和遇到的任何错误。这有助于跟踪工作器的性能并识别需要注意的问题。

## 优化特性

- **批处理**: 批量处理API调用以优化成本和性能
- **重试机制**: 使用指数退避的自动重试机制
- **错误处理**: 全面的错误处理和日志记录
- **速率限制**: 内置延迟以遵守API速率限制
- **配置管理**: 集中式配置管理便于维护 