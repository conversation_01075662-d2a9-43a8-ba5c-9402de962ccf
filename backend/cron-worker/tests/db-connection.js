#!/usr/bin/env node
/**
 * Database Connection Test for LinkTrackPro Cron Worker
 * 
 * This script tests the database connection without depending on table structure
 */

import { loadConfig, config } from '../src/lib/config.js';
import { createClient } from '@supabase/supabase-js';
import { getTestConfig } from './load-wrangler-config.js';

async function testDatabaseConnection() {
  console.log('🗄️ Testing Database Connection\n');
  
  // Load configuration
  const testConfig = getTestConfig();
  loadConfig(testConfig);
  
  if (!config.SUPABASE_URL || !config.SUPABASE_KEY) {
    console.log('❌ Database credentials not configured');
    return;
  }
  
  console.log(`🔗 Connecting to: ${new URL(config.SUPABASE_URL).hostname}`);
  
  try {
    // Create Supabase client with configured schema
    const schema = config.SUPABASE_SCHEMA || 'link_track';
    console.log(`📊 Using database schema: ${schema}`);
    
    const supabase = createClient(config.SUPABASE_URL, config.SUPABASE_KEY, {
      db: {
        schema: schema
      }
    });
    
    // Test basic connection with a simple query
    console.log('📡 Testing connection...');
    
    // Try to test connection with common Supabase system tables
    const testQueries = [
      // Try projects table (our main table)
      () => supabase.from('projects').select('count').limit(0),
      // Try links table
      () => supabase.from('links').select('count').limit(0),
      // Try a simple RPC call
      () => supabase.rpc('version'),
      // Generic error to test connectivity
      () => supabase.from('non_existent_table_test').select('*').limit(1)
    ];
    
    let connected = false;
    let tables = [];
    
    for (const [index, query] of testQueries.entries()) {
      try {
        const result = await query();
        if (result.error) {
          if (result.error.code === 'PGRST116') {
            // Table doesn't exist but we're connected
            connected = true;
            if (index === 0) console.log('⚠️  projects table not found');
            if (index === 1) console.log('⚠️  links table not found');
          } else if (result.error.code === '42P01') {
            // PostgreSQL relation does not exist
            connected = true;
            if (index === 0) console.log('⚠️  projects table not found');
            if (index === 1) console.log('⚠️  links table not found');
          } else {
            console.log(`📡 Query ${index + 1} error: ${result.error.message}`);
          }
        } else {
          connected = true;
          if (index === 0) {
            console.log('✅ projects table exists');
            tables.push('projects');
          }
          if (index === 1) {
            console.log('✅ links table exists');
            tables.push('links');
          }
          if (index === 2) {
            console.log('✅ RPC functionality working');
          }
        }
      } catch (error) {
        console.log(`📡 Query ${index + 1} exception: ${error.message}`);
      }
    }
    
    if (connected) {
      console.log('\n✅ Database connection successful!');
      
      if (tables.length > 0) {
        console.log(`📋 Found ${tables.length} required tables:`);
        tables.forEach(table => console.log(`   - ${table}`));
      }
      
      const expectedTables = ['projects', 'links', 'project_stats', 'link_stats'];
      const missingTables = expectedTables.filter(table => !tables.includes(table));
      
      if (missingTables.length > 0) {
        console.log('\n⚠️  Missing tables for full functionality:');
        missingTables.forEach(table => console.log(`   - ${table}`));
        console.log('\n💡 These tables need to be created in the database');
      }
      
      if (tables.length === expectedTables.length) {
        console.log('\n🎉 All required tables exist! Ready for production.');
      } else if (tables.length > 0) {
        console.log('\n⚠️  Partial setup detected. Some functionality may be limited.');
      } else {
        console.log('\n⚠️  No LinkTrackPro tables found. Database schema setup required.');
      }
    } else {
      console.log('\n❌ Could not establish database connection');
      console.log('💡 Check your SUPABASE_URL and SUPABASE_KEY configuration');
    }
    
  } catch (error) {
    console.log(`❌ Database test failed: ${error.message}`);
  }
}

// Run the test
testDatabaseConnection()
  .then(() => {
    console.log('\n✅ Database connection test completed');
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });