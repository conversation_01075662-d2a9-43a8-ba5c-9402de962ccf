/**
 * Load configuration from wrangler.toml for testing
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Parse wrangler.toml file and extract environment variables
 */
export function loadWranglerConfig() {
  try {
    // Try to find wrangler.toml in various locations
    const possiblePaths = [
      path.join(process.cwd(), 'wrangler.toml'),           // Current directory
      path.join(process.cwd(), '..', 'wrangler.toml'),     // Parent directory
      path.join(process.cwd(), '..', '..', 'wrangler.toml'), // Two levels up
      path.join(__dirname, '..', 'wrangler.toml'),         // Relative to this file
      path.join(__dirname, '..', '..', 'wrangler.toml')    // Two levels up from this file
    ];
    
    let wranglerPath = null;
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        wranglerPath = testPath;
        break;
      }
    }
    
    if (!wranglerPath) {
      throw new Error('wrangler.toml not found in any expected location');
    }
    const content = fs.readFileSync(wranglerPath, 'utf-8');
    
    // Simple TOML parser for [vars] section
    const lines = content.split('\n');
    const vars = {};
    let inVarsSection = false;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // Check if we're entering the [vars] section
      if (trimmed === '[vars]') {
        inVarsSection = true;
        continue;
      }
      
      // Check if we're leaving the [vars] section
      if (trimmed.startsWith('[') && trimmed !== '[vars]') {
        inVarsSection = false;
        continue;
      }
      
      // Parse variables in [vars] section
      if (inVarsSection && trimmed && !trimmed.startsWith('#')) {
        const equalIndex = trimmed.indexOf('=');
        if (equalIndex > 0) {
          const key = trimmed.substring(0, equalIndex).trim();
          let value = trimmed.substring(equalIndex + 1).trim();
          
          // Remove quotes if present
          if ((value.startsWith('"') && value.endsWith('"')) || 
              (value.startsWith("'") && value.endsWith("'"))) {
            value = value.slice(1, -1);
          }
          
          vars[key] = value;
        }
      }
    }
    
    return vars;
  } catch (error) {
    console.warn('Failed to load wrangler.toml:', error.message);
    return {};
  }
}

/**
 * Merge wrangler config with process.env, giving priority to process.env
 */
export function getTestConfig() {
  const wranglerVars = loadWranglerConfig();
  
  // Merge with process.env, process.env takes priority
  const config = {
    ...wranglerVars,
    ...process.env
  };
  
  return config;
}