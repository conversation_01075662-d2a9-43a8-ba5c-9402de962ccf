/**
 * WHOIS API Test Suite
 * Tests the WHOIS functionality with different APIs and fallback mechanisms
 */

import { config, loadConfig } from '../src/lib/config.js';
import { UniversalAPIClient } from '../src/lib/api-adapter.js';

// Mock environment for testing
const mockEnv = {
  ENVIRONMENT: 'development',
  API_LAYER_KEY: '', // Intentionally empty to test fallback
  RAPIDAPI_KEY: '**************************************************',
  // Add other required env vars
  SUPABASE_URL: 'https://towzzcheowkqpfrbkipc.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvd3p6Y2hlb3drcXBmcmJraXBjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxMTQzMzExMCwiZXhwIjoyMDI3MDA5MTEwfQ.zdAM34ipH0WS0fIKRBbBtN2xn1JHb-GAHB7-cdv92yc',
  SUPABASE_SCHEMA: 'link_track'
};

async function testWhoisAPI() {
  console.log('🧪 Starting WHOIS API Tests...\n');
  
  // Load configuration
  loadConfig(mockEnv);
  
  // Create API client
  const apiClient = new UniversalAPIClient();
  
  // Test domains
  const testDomains = [
    'example.com',
    'google.com',
    'github.com'
  ];
  
  console.log('📊 Testing individual WHOIS APIs...\n');
  
  for (const domain of testDomains) {
    console.log(`\n🔍 Testing domain: ${domain}`);
    console.log('='.repeat(50));
    
    // Test API Layer (expected to fail due to missing key)
    console.log('\n📡 Testing API Layer WHOIS (api.apilayer.com)...');
    try {
      const layerResult = await apiClient.getWhoisInfoFromAPILayer(domain);
      if (layerResult.success) {
        console.log('✅ API Layer Success (unexpected!)');
        console.log(`   Domain: ${layerResult.data.domain}`);
        console.log(`   Creation Date: ${layerResult.data.creation_date}`);
        console.log(`   Expiration Date: ${layerResult.data.expiration_date}`);
        console.log(`   Registrar: ${layerResult.data.registrar}`);
      } else {
        console.log('❌ API Layer Failed (expected):', layerResult.error);
      }
    } catch (error) {
      console.log('❌ API Layer Error (expected):', error.message);
    }
    
    // Test combined method with proper error handling
    console.log('\n🔄 Testing combined WHOIS method with proper error handling...');
    try {
      const combinedResult = await apiClient.getWhoisInfo(domain);
      if (combinedResult.success) {
        console.log('✅ Combined WHOIS Success');
        console.log(`   Domain: ${combinedResult.data.domain}`);
        console.log(`   Creation Date: ${combinedResult.data.creation_date}`);
        console.log(`   Expiration Date: ${combinedResult.data.expiration_date}`);
        console.log(`   Registrar: ${combinedResult.data.registrar}`);
        console.log(`   Source: ${combinedResult.api_info?.adapter_name || 'Unknown'}`);
      } else {
        console.log('❌ Combined WHOIS Failed (expected when API unavailable):', combinedResult.error);
        console.log(`   Domain: ${combinedResult.data?.domain || domain}`);
        console.log(`   Error Source: ${combinedResult.api_info?.adapter_name || 'Unknown'}`);
        console.log('   ✅ This is the correct behavior - no fake data provided');
      }
    } catch (error) {
      console.log('❌ Combined WHOIS Error:', error.message);
    }
    
    // Add delay between domain tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🎉 WHOIS API Tests Completed!');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testWhoisAPI().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

export { testWhoisAPI }; 