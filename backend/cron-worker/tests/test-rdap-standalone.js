/**
 * Standalone RDAP Test (No Database Dependencies)
 * 
 * Tests the core RDAP functionality without requiring Supabase initialization
 */

// Mock the database functions to avoid initialization errors
const mockDb = {
  getCachedWhoisInfo: async () => null // Always return null to skip cache
};

// Create a standalone RDAP service that doesn't use database
async function createStandaloneRdapService() {
  // Read the RDAP service file and create a version without DB dependencies
  const fs = await import('fs');
  const path = await import('path');
  
  // Import required utilities
  const { extractDomain } = await import('../src/lib/utils.js');
  
  // IANA RDAP Bootstrap URLs
  const IANA_BOOTSTRAP_URLS = {
    dns: 'https://data.iana.org/rdap/dns.json'
  };
  
  // Common RDAP server endpoints (fallback when bootstrap fails)
  const COMMON_RDAP_SERVERS = {
    'com': ['https://rdap.verisign.com/com/v1/'],
    'org': ['https://rdap.publicinterestregistry.org/rdap/'],
    'dev': ['https://pubapi.registry.google/rdap/'],
    'co': ['https://rdap.nic.co/'],
    'io': ['https://rdap.nic.io/']
  };
  
  // Cache for bootstrap data
  let bootstrapCache = { data: null, expires: 0 };
  
  // Fetch bootstrap data
  async function fetchBootstrapData() {
    const now = Date.now();
    
    if (bootstrapCache.data && now < bootstrapCache.expires) {
      return bootstrapCache.data;
    }
    
    try {
      console.log('📡 Fetching IANA DNS bootstrap data...');
      const response = await fetch(IANA_BOOTSTRAP_URLS.dns, {
        headers: {
          'User-Agent': 'LinkTrackPro-RDAP-Client/1.0',
          'Accept': 'application/json'
        },
        signal: AbortSignal.timeout(10000)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Cache for 1 hour
      bootstrapCache.data = data;
      bootstrapCache.expires = now + (60 * 60 * 1000);
      
      console.log('✅ Bootstrap data cached');
      return data;
    } catch (error) {
      console.warn('⚠️ Failed to fetch bootstrap data:', error.message);
      return null;
    }
  }
  
  // Find RDAP server for domain
  async function findRdapServer(domain) {
    const tld = domain.split('.').pop().toLowerCase();
    
    try {
      const bootstrapData = await fetchBootstrapData();
      
      if (bootstrapData && bootstrapData.services) {
        for (const service of bootstrapData.services) {
          const [tlds, servers] = service;
          
          if (tlds.includes(tld)) {
            console.log(`🎯 Found RDAP servers for .${tld} via IANA bootstrap`);
            return servers.filter(url => url.startsWith('https://'));
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ Error using IANA bootstrap for ${tld}:`, error.message);
    }
    
    if (COMMON_RDAP_SERVERS[tld]) {
      console.log(`🔄 Using fallback RDAP servers for .${tld}`);
      return COMMON_RDAP_SERVERS[tld];
    }
    
    console.warn(`❌ No RDAP servers found for .${tld}`);
    return [];
  }
  
  // Query RDAP server
  async function queryRdapServer(serverUrl, domain) {
    const rdapUrl = `${serverUrl.replace(/\/$/, '')}/domain/${domain}`;
    
    try {
      console.log(`🔍 Querying RDAP: ${rdapUrl}`);
      
      const response = await fetch(rdapUrl, {
        headers: {
          'User-Agent': 'LinkTrackPro-RDAP-Client/1.0',
          'Accept': 'application/rdap+json, application/json'
        },
        signal: AbortSignal.timeout(15000)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.objectClassName || data.objectClassName !== 'domain') {
        throw new Error('Invalid RDAP response: not a domain object');
      }
      
      console.log(`✅ RDAP query successful for ${domain}`);
      return data;
    } catch (error) {
      console.warn(`⚠️ RDAP query failed for ${rdapUrl}:`, error.message);
      throw error;
    }
  }
  
  // Normalize RDAP response
  function normalizeRdapResponse(rdapData, domain) {
    const normalized = {
      domain: domain,
      creation_date: null,
      expiration_date: null,
      registrar: '',
      name_servers: [],
      emails: '',
      dnssec: 'unknown',
      source: 'rdap'
    };
    
    try {
      if (rdapData.ldhName) {
        normalized.domain = rdapData.ldhName.toLowerCase();
      }
      
      if (rdapData.events && Array.isArray(rdapData.events)) {
        for (const event of rdapData.events) {
          if (event.eventAction === 'registration' && event.eventDate) {
            normalized.creation_date = event.eventDate;
          } else if (event.eventAction === 'expiration' && event.eventDate) {
            normalized.expiration_date = event.eventDate;
          }
        }
      }
      
      if (rdapData.entities && Array.isArray(rdapData.entities)) {
        for (const entity of rdapData.entities) {
          if (entity.roles && entity.roles.includes('registrar')) {
            if (entity.vcardArray && Array.isArray(entity.vcardArray)) {
              const vcard = entity.vcardArray[1];
              if (Array.isArray(vcard)) {
                for (const field of vcard) {
                  if (Array.isArray(field) && field[0] === 'fn' && field[3]) {
                    normalized.registrar = field[3];
                    break;
                  }
                }
              }
            }
            
            if (!normalized.registrar) {
              if (entity.handle) {
                normalized.registrar = entity.handle;
              } else if (entity.publicIds && entity.publicIds[0] && entity.publicIds[0].identifier) {
                normalized.registrar = entity.publicIds[0].identifier;
              }
            }
            break;
          }
        }
      }
      
      if (rdapData.nameservers && Array.isArray(rdapData.nameservers)) {
        normalized.name_servers = rdapData.nameservers
          .map(ns => ns.ldhName || ns.objectClassName)
          .filter(Boolean);
      }
      
      if (rdapData.secureDNS) {
        if (rdapData.secureDNS.delegationSigned === true) {
          normalized.dnssec = 'signed';
        } else if (rdapData.secureDNS.delegationSigned === false) {
          normalized.dnssec = 'unsigned';
        }
      }
      
      const emails = [];
      if (rdapData.entities && Array.isArray(rdapData.entities)) {
        for (const entity of rdapData.entities) {
          if (entity.vcardArray && Array.isArray(entity.vcardArray)) {
            const vcard = entity.vcardArray[1];
            if (Array.isArray(vcard)) {
              for (const field of vcard) {
                if (Array.isArray(field) && field[0] === 'email' && field[3]) {
                  emails.push(field[3]);
                }
              }
            }
          }
        }
      }
      normalized.emails = emails.join(', ');
      
    } catch (error) {
      console.warn(`⚠️ Error normalizing RDAP response:`, error.message);
    }
    
    return normalized;
  }
  
  // Main fetch function
  async function fetchRdapInfo(domain) {
    console.log(`🚀 Starting RDAP lookup for: ${domain}`);
    
    const cleanDomain = extractDomain(domain);
    
    try {
      const rdapServers = await findRdapServer(cleanDomain);
      
      if (rdapServers.length === 0) {
        throw new Error(`No RDAP servers found for domain ${cleanDomain}`);
      }
      
      let lastError = null;
      for (const server of rdapServers) {
        try {
          const rdapData = await queryRdapServer(server, cleanDomain);
          const normalizedData = normalizeRdapResponse(rdapData, cleanDomain);
          
          return {
            ...normalizedData,
            timestamp: new Date().toISOString(),
            rdap_server: server,
            raw_rdap_data: rdapData
          };
        } catch (error) {
          lastError = error;
          console.warn(`⚠️ RDAP server ${server} failed:`, error.message);
          continue;
        }
      }
      
      throw new Error(`All RDAP servers failed. Last error: ${lastError?.message}`);
      
    } catch (error) {
      console.error(`❌ RDAP lookup failed for ${cleanDomain}:`, error.message);
      throw error;
    }
  }
  
  return { fetchRdapInfo, fetchBootstrapData };
}

// Test function
async function testStandaloneRdap() {
  console.log('🧪 Standalone RDAP Test Suite');
  console.log('=' .repeat(50));
  
  const { fetchRdapInfo } = await createStandaloneRdapService();
  
  const testDomains = ['google.com', 'example.org', 'google.dev'];
  let successCount = 0;
  
  for (const domain of testDomains) {
    console.log(`\n🔍 Testing ${domain}:`);
    
    try {
      const result = await fetchRdapInfo(domain);
      
      console.log(`   ✅ RDAP query successful`);
      console.log(`   📝 Parsed data:`);
      console.log(`     • Domain: ${result.domain}`);
      console.log(`     • Creation date: ${result.creation_date || 'N/A'}`);
      console.log(`     • Expiration date: ${result.expiration_date || 'N/A'}`);
      console.log(`     • Registrar: ${result.registrar || 'N/A'}`);
      console.log(`     • Name servers: ${result.name_servers?.length || 0} servers`);
      console.log(`     • DNSSEC: ${result.dnssec || 'unknown'}`);
      console.log(`     • RDAP server: ${result.rdap_server || 'N/A'}`);
      
      if (result.name_servers && result.name_servers.length > 0) {
        console.log(`     • Name servers:`);
        result.name_servers.slice(0, 3).forEach(ns => {
          console.log(`       - ${ns}`);
        });
        if (result.name_servers.length > 3) {
          console.log(`       ... and ${result.name_servers.length - 3} more`);
        }
      }
      
      successCount++;
      
    } catch (error) {
      console.error(`   ❌ ${domain}: RDAP lookup failed -`, error.message);
    }
  }
  
  console.log(`\n📊 Test Summary:`);
  console.log(`   • Total domains tested: ${testDomains.length}`);
  console.log(`   • Successful lookups: ${successCount}`);
  console.log(`   • Success rate: ${Math.round((successCount / testDomains.length) * 100)}%`);
  
  return successCount > 0;
}

// Run the test
testStandaloneRdap()
  .then(success => {
    console.log(`\n${success ? '🎉' : '❌'} Test ${success ? 'completed successfully' : 'failed'}`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  }); 