# LinkTrackPro Cron Worker Tests

本目录包含 LinkTrackPro Cron Worker 的所有测试脚本。

## 📁 测试文件结构

```
tests/
├── test-basic.js           # 基础功能测试 (npm test)
├── full.js                 # 完整测试套件 (npm run test:full)
├── manual.js               # 手动测试工具 (npm run test:manual)
├── db-connection.js        # 数据库连接测试 (npm run test:db)
├── endpoint-tests.js       # URL端点和触发器测试
├── load-wrangler-config.js # 配置加载工具
├── run-all-tests.js        # 完整测试运行器
└── README.md               # 本文件
```

## 🚀 快速开始

### 运行所有测试
```bash
# 从项目根目录运行
node tests/run-all-tests.js
```

### 单独运行测试
```bash
# 基础测试 (推荐首次使用)
npm test

# 数据库连接测试
npm run test:db

# 查看配置
npm run test:manual config

# 系统健康检查
npm run test:manual health-check
```

## 📋 测试类型详解

### 1. 基础测试 (`test-basic.js`)
- **目的**: 验证核心功能，无需外部依赖
- **包含**: 配置加载、模块导入、Worker结构、身份验证逻辑
- **运行**: `npm test`

### 2. 完整测试 (`full.js`)
- **目的**: 全面的自动化测试套件
- **包含**: 所有组件的深度测试
- **运行**: `npm run test:full`

### 3. 手动测试 (`manual.js`)
- **目的**: 交互式测试和调试
- **包含**: 单个组件测试、模拟执行、配置检查
- **运行**: `npm run test:manual <command>`

### 4. 数据库测试 (`db-connection.js`)
- **目的**: 数据库连接和表结构验证
- **包含**: 连接测试、表存在性检查
- **运行**: `npm run test:db`

### 5. 端点测试 (`endpoint-tests.js`)
- **目的**: Worker URL端点和触发器测试
- **包含**: HTTP fetch处理、身份验证、路由测试
- **运行**: 作为 `npm run test:all` 的一部分运行

## 🔧 配置管理

### 自动配置加载
- 测试脚本自动从 `wrangler.toml` 加载配置
- 支持环境变量覆盖
- 智能路径查找

### 开发模式配置
- 使用 `wrangler.dev.toml` 进行开发测试
- 跳过身份验证 (ENVIRONMENT=development)
- 优化的批处理参数

## 🛠️ 手动测试命令

### 配置相关
```bash
npm run test:manual config        # 显示当前配置
npm run test:manual health-check  # 系统健康检查
```

### 组件测试
```bash
npm run test:manual test-feishu      # 飞书通知测试
npm run test:manual test-traffic     # 流量数据收集测试
npm run test:manual test-dr          # 域名评级测试
npm run test:manual test-links       # 外链数据测试
npm run test:manual test-indexing    # 索引状态测试
```

### 模拟执行
```bash
npm run test:manual simulate-cron                    # 模拟日常执行
npm run test:manual simulate-cron --day=sunday       # 模拟周日执行
npm run test:manual worker-fetch --endpoint=/trigger/traffic
```

## 🔍 开发模式特性

### Auth 校验跳过
开发模式下自动跳过身份验证，满足以下条件之一：
- `ENVIRONMENT=development`
- `NODE_ENV=development`
- 主机名包含 `localhost`, `127.0.0.1`, 或 `.local`

### 开发配置
使用 `wrangler.dev.toml` 可以：
- 设置开发环境变量
- 使用较小的批处理大小
- 使用测试API密钥
- 跳过身份验证

## 📊 测试结果解读

### 状态符号
- ✅ 成功 - 功能正常工作
- ⚠️ 警告 - 需要注意但不影响基本功能  
- ❌ 错误 - 功能异常，需要修复
- 💡 提示 - 建议和改进建议
- 📋 信息 - 配置详情和状态信息

### 常见警告
- API密钥为placeholder值 - 需要配置真实API密钥
- 数据库表不存在 - 需要创建表结构
- 使用测试配置值 - 在开发环境中正常

## 🚨 故障排除

### 配置问题
```bash
npm run test:manual config  # 查看配置状态
```

### 数据库问题
```bash
npm run test:db  # 测试数据库连接
```

### 模块导入问题
```bash
npm test  # 基础测试会检查模块结构
```

### 路径问题
测试脚本会自动查找 `wrangler.toml` 文件，支持：
- 当前目录
- 父级目录  
- 相对于测试文件的路径

## 📈 CI/CD 集成

### 基础CI测试
```bash
npm test  # 只运行基础测试，不依赖外部服务
```

### 完整CI测试
```bash
node tests/run-all-tests.js  # 运行所有测试套件
```

### 环境变量
CI环境中可以通过环境变量覆盖配置：
```bash
export SUPABASE_URL="your_ci_database_url"
export SUPABASE_KEY="your_ci_database_key"
npm test
```