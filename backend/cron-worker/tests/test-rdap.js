/**
 * RDAP Implementation Test Script
 * 
 * Tests the new RDAP (Registration Data Access Protocol) implementation
 * including bootstrap discovery, server connectivity, and data parsing.
 */

import { fetchRdapInfo, testRdapConnectivity as testRdapConnectivityFn, getRdapStats, clearRdapCache } from '../src/services/rdap.js';
import { fetchWhoisInfo, testWhoisConnectivity, forceRdapQuery, forceWhoisApiQuery } from '../src/services/whois.js';

// Test domains for different TLDs
const TEST_DOMAINS = [
  'google.com',        // .com - Verisign
  'example.org',       // .org - PIR
  'github.io',         // .io - NIC.io
  'google.dev',        // .dev - Google Registry
  'stackoverflow.co',  // .co - .CO Internet
  'example.uk',        // .uk - Nominet
  'example.de',        // .de - DENIC
  'cloudflare.com',    // .com - Another test
  'reddit.com'         // .com - Third test
];

/**
 * Test RDAP bootstrap system
 */
async function testRdapBootstrap() {
  console.log('\n🧪 Testing RDAP Bootstrap System...');
  console.log('=' .repeat(50));
  
  try {
    const stats = await getRdapStats();
    
    if (stats.error) {
      console.error('❌ Bootstrap test failed:', stats.error);
      return false;
    }
    
    console.log('✅ Bootstrap data loaded successfully');
    console.log(`📊 Bootstrap Statistics:`);
    console.log(`   • Publication date: ${stats.bootstrap_date}`);
    console.log(`   • Total services: ${stats.total_services}`);
    console.log(`   • Total TLDs: ${stats.total_tlds}`);
    console.log(`   • HTTPS servers: ${stats.https_servers}`);
    console.log(`   • HTTP servers: ${stats.http_servers}`);
    
    // Show top server providers
    const topServers = Object.entries(stats.server_distribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
    
    console.log(`   • Top server providers:`);
    topServers.forEach(([domain, count]) => {
      console.log(`     - ${domain}: ${count} TLDs`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ Bootstrap test error:', error.message);
    return false;
  }
}

/**
 * Test RDAP connectivity for various domains
 */
async function testRdapConnectivitySuite() {
  console.log('\n🌐 Testing RDAP Connectivity...');
  console.log('=' .repeat(50));
  
  let successCount = 0;
  let totalTests = 0;
  
  for (const domain of TEST_DOMAINS.slice(0, 5)) { // Test first 5 domains
    console.log(`\n🔍 Testing ${domain}:`);
    
    try {
             const result = await testRdapConnectivityFn(domain);
      totalTests++;
      
      console.log(`   • Bootstrap available: ${result.bootstrap_available ? '✅' : '❌'}`);
      console.log(`   • Servers found: ${result.servers_found.length}`);
      
      if (result.servers_found.length > 0) {
        console.log(`   • Server list: ${result.servers_found.join(', ')}`);
      }
      
      console.log(`   • Server tests:`);
      let serverSuccess = false;
      for (const test of result.server_tests) {
        const status = test.accessible ? '✅' : '❌';
        const time = test.response_time ? `${test.response_time}ms` : 'N/A';
        console.log(`     - ${test.server}: ${status} (${time})`);
        if (test.accessible) serverSuccess = true;
        if (test.error) console.log(`       Error: ${test.error}`);
      }
      
      if (serverSuccess) {
        successCount++;
        console.log(`   🎉 ${domain}: RDAP connectivity successful`);
      } else {
        console.log(`   ⚠️ ${domain}: No accessible RDAP servers`);
      }
      
    } catch (error) {
      console.error(`   ❌ ${domain}: Test failed -`, error.message);
    }
  }
  
  console.log(`\n📊 RDAP Connectivity Summary:`);
  console.log(`   • Total domains tested: ${totalTests}`);
  console.log(`   • Successful connections: ${successCount}`);
  console.log(`   • Success rate: ${totalTests > 0 ? Math.round((successCount / totalTests) * 100) : 0}%`);
  
  return successCount > 0;
}

/**
 * Test RDAP data fetching and parsing
 */
async function testRdapDataFetching() {
  console.log('\n📊 Testing RDAP Data Fetching and Parsing...');
  console.log('=' .repeat(50));
  
  let successCount = 0;
  const testDomains = ['google.com', 'github.io', 'example.org'];
  
  for (const domain of testDomains) {
    console.log(`\n🔍 Testing data fetch for ${domain}:`);
    
    try {
      const result = await forceRdapQuery(domain);
      
      if (result.error) {
        console.log(`   ❌ RDAP query failed: ${result.error}`);
        continue;
      }
      
      console.log(`   ✅ RDAP query successful`);
      console.log(`   📝 Parsed data:`);
      console.log(`     • Domain: ${result.domain}`);
      console.log(`     • Creation date: ${result.creation_date || 'N/A'}`);
      console.log(`     • Expiration date: ${result.expiration_date || 'N/A'}`);
      console.log(`     • Registrar: ${result.registrar || 'N/A'}`);
      console.log(`     • Name servers: ${result.name_servers?.length || 0} servers`);
      console.log(`     • DNSSEC: ${result.dnssec || 'unknown'}`);
      console.log(`     • RDAP server: ${result.rdap_server || 'N/A'}`);
      
      // Show name servers if available
      if (result.name_servers && result.name_servers.length > 0) {
        console.log(`     • Name servers list:`);
        result.name_servers.slice(0, 3).forEach(ns => {
          console.log(`       - ${ns}`);
        });
        if (result.name_servers.length > 3) {
          console.log(`       ... and ${result.name_servers.length - 3} more`);
        }
      }
      
      successCount++;
      
    } catch (error) {
      console.error(`   ❌ ${domain}: Data fetch failed -`, error.message);
    }
  }
  
  console.log(`\n📊 Data Fetching Summary:`);
  console.log(`   • Total domains tested: ${testDomains.length}`);
  console.log(`   • Successful data fetches: ${successCount}`);
  console.log(`   • Success rate: ${Math.round((successCount / testDomains.length) * 100)}%`);
  
  return successCount > 0;
}

/**
 * Test WHOIS service with RDAP integration
 */
async function testWhoisIntegration() {
  console.log('\n🔄 Testing WHOIS Service with RDAP Integration...');
  console.log('=' .repeat(50));
  
  const testDomain = 'google.com';
  
  try {
    // Clear cache to ensure fresh test
    console.log('🧹 Clearing cache for fresh test...');
    
    // Test the integrated WHOIS service (should try RDAP first)
    console.log(`\n🔍 Testing integrated WHOIS service for ${testDomain}:`);
    const whoisResult = await fetchWhoisInfo(testDomain);
    
    console.log(`   • Method used: ${whoisResult.method || 'unknown'}`);
    console.log(`   • Source: ${whoisResult.source || 'unknown'}`);
    console.log(`   • Fallback used: ${whoisResult.fallback_used ? 'Yes' : 'No'}`);
    console.log(`   • Cached: ${whoisResult.cached ? 'Yes' : 'No'}`);
    
    if (whoisResult.error) {
      console.log(`   ❌ Error: ${whoisResult.error}`);
    } else {
      console.log(`   ✅ Data retrieved successfully`);
      console.log(`   📝 Data summary:`);
      console.log(`     • Domain: ${whoisResult.domain}`);
      console.log(`     • Creation date: ${whoisResult.creation_date || 'N/A'}`);
      console.log(`     • Registrar: ${whoisResult.registrar || 'N/A'}`);
    }
    
    // Test connectivity test
    console.log(`\n🧪 Testing connectivity test for ${testDomain}:`);
    const connectivityResult = await testWhoisConnectivity(testDomain);
    
    console.log(`   • RDAP available: ${connectivityResult.rdap_test ? 'Yes' : 'No'}`);
    console.log(`   • Traditional WHOIS available: ${connectivityResult.whois_api_test?.available ? 'Yes' : 'No'}`);
    console.log(`   • Recommended method: ${connectivityResult.recommended_method || 'unknown'}`);
    
    if (connectivityResult.rdap_test) {
      const rdapWorking = connectivityResult.rdap_test.server_tests.filter(test => test.accessible).length;
      console.log(`   • RDAP servers working: ${rdapWorking}/${connectivityResult.rdap_test.server_tests.length}`);
    }
    
    return true;
    
  } catch (error) {
    console.error(`❌ WHOIS integration test failed:`, error.message);
    return false;
  }
}

/**
 * Performance comparison test
 */
async function testPerformanceComparison() {
  console.log('\n⚡ Performance Comparison: RDAP vs Traditional WHOIS...');
  console.log('=' .repeat(50));
  
  const testDomain = 'google.com';
  
  try {
    // Test RDAP performance
    console.log(`🚀 Testing RDAP performance for ${testDomain}:`);
    const rdapStart = Date.now();
    const rdapResult = await forceRdapQuery(testDomain);
    const rdapTime = Date.now() - rdapStart;
    
    console.log(`   • RDAP query time: ${rdapTime}ms`);
    console.log(`   • RDAP success: ${rdapResult.error ? 'No' : 'Yes'}`);
    
    // Test traditional WHOIS performance (if available)
    console.log(`🔄 Testing traditional WHOIS performance for ${testDomain}:`);
    const whoisStart = Date.now();
    let whoisTime = 0;
    let whoisSuccess = false;
    
    try {
      const whoisResult = await forceWhoisApiQuery(testDomain);
      whoisTime = Date.now() - whoisStart;
      whoisSuccess = !whoisResult.error;
    } catch (error) {
      whoisTime = Date.now() - whoisStart;
      console.log(`   • Traditional WHOIS failed: ${error.message}`);
    }
    
    console.log(`   • Traditional WHOIS query time: ${whoisTime}ms`);
    console.log(`   • Traditional WHOIS success: ${whoisSuccess ? 'Yes' : 'No'}`);
    
    // Performance comparison
    console.log(`\n📊 Performance Comparison:`);
    if (rdapResult.error && !whoisSuccess) {
      console.log(`   • Both methods failed`);
    } else if (!rdapResult.error && whoisSuccess) {
      const faster = rdapTime < whoisTime ? 'RDAP' : 'Traditional WHOIS';
      const difference = Math.abs(rdapTime - whoisTime);
      console.log(`   • Faster method: ${faster} (${difference}ms faster)`);
      console.log(`   • RDAP: ${rdapTime}ms, Traditional: ${whoisTime}ms`);
    } else if (!rdapResult.error) {
      console.log(`   • Only RDAP succeeded (${rdapTime}ms)`);
    } else if (whoisSuccess) {
      console.log(`   • Only Traditional WHOIS succeeded (${whoisTime}ms)`);
    }
    
    return true;
    
  } catch (error) {
    console.error(`❌ Performance comparison failed:`, error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🧪 RDAP Implementation Test Suite');
  console.log('=' .repeat(60));
  console.log(`Started at: ${new Date().toISOString()}`);
  
  const tests = [
    { name: 'RDAP Bootstrap System', fn: testRdapBootstrap },
    { name: 'RDAP Connectivity', fn: testRdapConnectivitySuite },
    { name: 'RDAP Data Fetching', fn: testRdapDataFetching },
    { name: 'WHOIS Integration', fn: testWhoisIntegration },
    { name: 'Performance Comparison', fn: testPerformanceComparison }
  ];
  
  let passedTests = 0;
  const results = [];
  
  for (const test of tests) {
    console.log(`\n🏃 Running: ${test.name}`);
    const startTime = Date.now();
    
    try {
      const success = await test.fn();
      const duration = Date.now() - startTime;
      
      if (success) {
        console.log(`✅ ${test.name}: PASSED (${duration}ms)`);
        passedTests++;
        results.push({ name: test.name, status: 'PASSED', duration });
      } else {
        console.log(`❌ ${test.name}: FAILED (${duration}ms)`);
        results.push({ name: test.name, status: 'FAILED', duration });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`💥 ${test.name}: ERROR (${duration}ms) - ${error.message}`);
      results.push({ name: test.name, status: 'ERROR', duration, error: error.message });
    }
  }
  
  // Final summary
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Test Results Summary');
  console.log('=' .repeat(60));
  
  results.forEach(result => {
    const status = result.status === 'PASSED' ? '✅' : 
                   result.status === 'FAILED' ? '❌' : '💥';
    console.log(`${status} ${result.name}: ${result.status} (${result.duration}ms)`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log(`\n📊 Overall Results:`);
  console.log(`   • Total tests: ${tests.length}`);
  console.log(`   • Passed: ${passedTests}`);
  console.log(`   • Failed: ${tests.length - passedTests}`);
  console.log(`   • Success rate: ${Math.round((passedTests / tests.length) * 100)}%`);
  
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
  console.log(`   • Total duration: ${totalDuration}ms`);
  console.log(`   • Average per test: ${Math.round(totalDuration / tests.length)}ms`);
  
  console.log(`\nCompleted at: ${new Date().toISOString()}`);
  
  return passedTests === tests.length;
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test suite crashed:', error);
      process.exit(1);
    });
}

export { runAllTests }; 