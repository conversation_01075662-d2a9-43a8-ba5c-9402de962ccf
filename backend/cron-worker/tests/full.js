#!/usr/bin/env node
/**
 * LinkTrackPro Cron Worker Self-Test Script
 * 
 * This script tests all major components of the cron worker:
 * - Configuration loading
 * - Database connections
 * - API service connections
 * - Data processors
 * - Manual trigger endpoints
 */

import { loadConfig, config } from '../src/lib/config.js';
import { initSupabase } from '../src/db.js';
import { isAuthenticated } from '../src/lib/auth.js';
import { sendAlert, sendMessage } from '../src/services/feishu.js';
import { fetchTrafficForDomain } from '../src/services/similarweb.js';
import { processTrafficData } from '../src/processors/traffic.js';
import { processDomainRatings } from '../src/processors/domain-rating.js';
import { processExternalLinks } from '../src/processors/external-links.js';
import { processIndexingStatus } from '../src/processors/indexing.js';
import { getTestConfig } from './load-wrangler-config.js';

// Load configuration from wrangler.toml and environment
const testConfig = getTestConfig();

class TestSuite {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.warnings = 0;
  }

  async run() {
    console.log('🚀 Starting LinkTrackPro Cron Worker Self-Test Suite\n');
    console.log('=' .repeat(60));

    // Load test configuration from wrangler.toml
    loadConfig(testConfig);
    console.log('📋 Configuration loaded from wrangler.toml and environment variables');

    // Run all tests
    await this.testConfiguration();
    await this.testDatabaseConnection();
    await this.testAuthentication();
    await this.testServices();
    await this.testProcessors();
    await this.testWorkerEndpoints();

    // Print summary
    this.printSummary();

    // Exit with appropriate code
    process.exit(this.failed > 0 ? 1 : 0);
  }

  async test(name, testFunction) {
    process.stdout.write(`Testing ${name}... `);
    try {
      const result = await testFunction();
      if (result === 'warning') {
        console.log('⚠️  WARNING');
        this.warnings++;
      } else {
        console.log('✅ PASS');
        this.passed++;
      }
    } catch (error) {
      console.log('❌ FAIL');
      console.log(`   Error: ${error.message}`);
      this.failed++;
    }
  }

  async testConfiguration() {
    console.log('\n📋 Configuration Tests');
    console.log('-'.repeat(30));

    await this.test('Config loading', () => {
      if (!config.SUPABASE_URL || !config.SUPABASE_KEY) {
        throw new Error('Missing required database configuration');
      }
      return true;
    });

    await this.test('API keys presence', () => {
      const requiredKeys = ['SIMILARWEB_API_KEY', 'AHREFS_API_KEY', 'GOOGLE_API_KEY'];
      const placeholderKeys = requiredKeys.filter(key => 
        !config[key] || config[key].startsWith('your_')
      );
      
      if (placeholderKeys.length > 0) {
        console.log(`   Placeholder API keys detected: ${placeholderKeys.join(', ')}`);
        console.log(`   Update these in wrangler.toml with real API keys`);
        return 'warning';
      }
      return true;
    });

    await this.test('Batch configuration', () => {
      if (config.BATCH_SIZE < 1 || config.BATCH_SIZE > 100) {
        throw new Error(`Invalid batch size: ${config.BATCH_SIZE}`);
      }
      if (config.MAX_RETRIES < 1 || config.MAX_RETRIES > 10) {
        throw new Error(`Invalid max retries: ${config.MAX_RETRIES}`);
      }
      return true;
    });
  }

  async testDatabaseConnection() {
    console.log('\n🗄️  Database Tests');
    console.log('-'.repeat(30));

    await this.test('Database initialization', () => {
      try {
        initSupabase(config.SUPABASE_URL, config.SUPABASE_KEY);
        return true;
      } catch (error) {
        throw new Error(`Database init failed: ${error.message}`);
      }
    });

    // Note: Actual database connectivity test would require real credentials
    await this.test('Database connectivity', () => {
      // We have real database credentials from wrangler.toml
      if (config.SUPABASE_URL && config.SUPABASE_KEY) {
        console.log('   Real database credentials detected from wrangler.toml');
        return true;
      } else {
        console.log('   Database credentials missing');
        return 'warning';
      }
    });
  }

  async testAuthentication() {
    console.log('\n🔐 Authentication Tests');
    console.log('-'.repeat(30));

    await this.test('Auth token validation', () => {
      if (!config.AUTH_TOKEN) {
        console.log('   No auth token configured');
        return 'warning';
      }
      console.log('   Auth token configured from wrangler.toml');
      return true;
    });

    await this.test('Request authentication', () => {
      // Mock request with correct auth header
      const mockRequest = {
        headers: {
          get: (name) => name === 'authorization' ? `Bearer ${config.AUTH_TOKEN}` : null
        }
      };

      if (!isAuthenticated(mockRequest)) {
        throw new Error('Authentication check failed for valid token');
      }

      // Mock request with wrong auth header
      const mockInvalidRequest = {
        headers: {
          get: (name) => name === 'authorization' ? 'Bearer wrong-token' : null
        }
      };

      if (isAuthenticated(mockInvalidRequest)) {
        throw new Error('Authentication check passed for invalid token');
      }

      return true;
    });
  }

  async testServices() {
    console.log('\n🌐 Service Tests');
    console.log('-'.repeat(30));

    await this.test('Feishu webhook configuration', () => {
      if (!config.FEISHU_WEBHOOK_URL || config.FEISHU_WEBHOOK_URL.includes('test')) {
        console.log('   No production Feishu webhook configured');
        return 'warning';
      }
      return true;
    });

    await this.test('SimilarWeb API configuration', () => {
      if (!config.SIMILARWEB_API_KEY || config.SIMILARWEB_API_KEY === 'test-api-key') {
        console.log('   No production SimilarWeb API key configured');
        return 'warning';
      }
      return true;
    });

    await this.test('Service error handling', () => {
      // Test that services handle errors gracefully
      try {
        // This should not throw an error, but return an error result
        const result = fetchTrafficForDomain('invalid-domain-test-12345.com');
        return true;
      } catch (error) {
        // If it throws, that's still acceptable for some services
        return true;
      }
    });
  }

  async testProcessors() {
    console.log('\n⚙️  Processor Tests');
    console.log('-'.repeat(30));

    await this.test('Traffic processor structure', () => {
      if (typeof processTrafficData !== 'function') {
        throw new Error('processTrafficData is not a function');
      }
      return true;
    });

    await this.test('Domain rating processor structure', () => {
      if (typeof processDomainRatings !== 'function') {
        throw new Error('processDomainRatings is not a function');
      }
      return true;
    });

    await this.test('External links processor structure', () => {
      if (typeof processExternalLinks !== 'function') {
        throw new Error('processExternalLinks is not a function');
      }
      return true;
    });

    await this.test('Indexing processor structure', () => {
      if (typeof processIndexingStatus !== 'function') {
        throw new Error('processIndexingStatus is not a function');
      }
      return true;
    });
  }

  async testWorkerEndpoints() {
    console.log('\n🔗 Worker Endpoint Tests');
    console.log('-'.repeat(30));

    await this.test('Trigger endpoints structure', async () => {
      // Test the main worker structure
      const worker = await import('../src/index.js');
      const defaultExport = worker.default;
      
      if (!defaultExport || typeof defaultExport !== 'object') {
        throw new Error('Worker default export is not an object');
      }
      
      if (typeof defaultExport.scheduled !== 'function') {
        throw new Error('Worker scheduled function is missing');
      }
      
      if (typeof defaultExport.fetch !== 'function') {
        throw new Error('Worker fetch function is missing');
      }
      
      return true;
    });

    await this.test('Scheduled task timing logic', () => {
      // Test that weekly tasks run on Sunday (day 0)
      const sundayDate = new Date('2024-01-07T10:00:00Z'); // A Sunday
      const mondayDate = new Date('2024-01-08T10:00:00Z'); // A Monday
      
      if (sundayDate.getDay() !== 0) {
        throw new Error('Sunday date calculation is incorrect');
      }
      
      if (mondayDate.getDay() !== 1) {
        throw new Error('Monday date calculation is incorrect');
      }
      
      return true;
    });
  }

  async testIntegration() {
    console.log('\n🔄 Integration Tests');
    console.log('-'.repeat(30));

    await this.test('Full workflow simulation', async () => {
      // This would test the full workflow with mock data
      // Skip if no production credentials are available
      if (config.SUPABASE_URL.includes('test')) {
        console.log('   Skipping integration test with test credentials');
        return 'warning';
      }
      
      // In a real scenario, you might test with a single domain
      // return await processTrafficData();
      return 'warning'; // Skip for now
    });
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 Test Summary');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${this.passed}`);
    console.log(`❌ Failed: ${this.failed}`);
    console.log(`⚠️  Warnings: ${this.warnings}`);
    console.log(`📋 Total: ${this.passed + this.failed + this.warnings}`);
    
    if (this.failed === 0) {
      console.log('\n🎉 All tests passed! Cron worker is ready for deployment.');
    } else {
      console.log('\n🚨 Some tests failed. Please fix the issues before deployment.');
    }
    
    if (this.warnings > 0) {
      console.log('\n💡 Warnings indicate missing production configuration or optional features.');
    }
  }
}

// Run the test suite
const testSuite = new TestSuite();
testSuite.run().catch(error => {
  console.error('Test suite failed to run:', error);
  process.exit(1);
});