#!/usr/bin/env node
/**
 * Test Runner for LinkTrackPro Cron Worker
 * 
 * Runs all test suites in sequence and provides a summary
 */

import { execSync } from 'child_process';
import path from 'path';

const testSuites = [
  {
    name: 'Basic Tests',
    command: 'node tests/test-basic.js',
    description: 'Core functionality validation'
  },
  {
    name: 'Database Connection',
    command: 'node tests/db-connection.js',
    description: 'Database connectivity and schema check'
  },
  {
    name: 'Worker Endpoints',
    command: 'node tests/endpoint-tests.js',
    description: 'URL endpoint and trigger testing'
  },
  {
    name: 'Manual Test Suite',
    command: 'node tests/manual.js health-check',
    description: 'System health and configuration check'
  }
];

async function runAllTests() {
  console.log('🚀 LinkTrackPro Cron Worker - Complete Test Suite\n');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const [index, test] of testSuites.entries()) {
    console.log(`\n${index + 1}. ${test.name}`);
    console.log(`   ${test.description}`);
    console.log('-'.repeat(40));
    
    try {
      const output = execSync(test.command, { 
        encoding: 'utf-8',
        cwd: process.cwd(), // Run from current directory
        timeout: 30000 // 30 second timeout
      });
      
      console.log(output);
      results.push({ ...test, status: 'PASS' });
      
    } catch (error) {
      console.log(error.stdout || error.message);
      results.push({ ...test, status: 'FAIL', error: error.message });
    }
  }
  
  // Print summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 Test Suite Summary');
  console.log('='.repeat(60));
  
  let passed = 0;
  let failed = 0;
  
  results.forEach((result, index) => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.name}: ${result.status}`);
    
    if (result.status === 'PASS') {
      passed++;
    } else {
      failed++;
      if (result.error) {
        console.log(`   Error: ${result.error.split('\n')[0]}`);
      }
    }
  });
  
  console.log(`\n📈 Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! System is ready for deployment.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
  }
  
  // Quick setup guide
  console.log('\n💡 Quick Commands:');
  console.log('   npm test              - Run basic tests');
  console.log('   npm run test:db       - Test database connection');
  console.log('   npm run test:manual config - Show configuration');
  console.log('   npm run test:manual health-check - Health check');
  
  process.exit(failed > 0 ? 1 : 0);
}

runAllTests().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});