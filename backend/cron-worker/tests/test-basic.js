#!/usr/bin/env node
/**
 * Basic Test Script for LinkTrackPro Cron Worker
 * 
 * This is a simplified test that doesn't require database connection
 * and focuses on module loading and basic functionality.
 */

import { loadConfig, config } from '../src/lib/config.js';
import { isAuthenticated } from '../src/lib/auth.js';
import { getTestConfig } from './load-wrangler-config.js';

// Load configuration from wrangler.toml and environment
const testConfig = getTestConfig();

async function runBasicTests() {
  console.log('🧪 Running Basic LinkTrackPro Cron Worker Tests\n');
  console.log('📋 Loading configuration from wrangler.toml...');
  
  let passed = 0;
  let failed = 0;
  
  // Test 1: Configuration Loading
  console.log('1. Testing configuration loading...');
  try {
    loadConfig(testConfig);
    console.log('✅ Configuration loaded successfully');
    console.log(`   - Batch size: ${config.BATCH_SIZE}`);
    console.log(`   - Max retries: ${config.MAX_RETRIES}`);
    passed++;
  } catch (error) {
    console.log(`❌ Configuration loading failed: ${error.message}`);
    failed++;
  }
  
  // Test 2: Authentication Function
  console.log('\n2. Testing authentication function...');
  try {
    const mockValidRequest = {
      headers: {
        get: (name) => name === 'authorization' ? `Bearer ${config.AUTH_TOKEN}` : null
      }
    };
    
    const mockInvalidRequest = {
      headers: {
        get: (name) => name === 'authorization' ? 'Bearer wrong-token' : null
      }
    };
    
    if (isAuthenticated(mockValidRequest) && !isAuthenticated(mockInvalidRequest)) {
      console.log('✅ Authentication function working correctly');
      passed++;
    } else {
      console.log('❌ Authentication function not working correctly');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Authentication test failed: ${error.message}`);
    failed++;
  }
  
  // Test 3: Module Imports
  console.log('\n3. Testing module imports...');
  try {
    const { processBatches } = await import('../src/lib/utils.js');
    const { processTrafficData } = await import('../src/processors/traffic.js');
    const { sendMessage } = await import('../src/services/feishu.js');
    
    if (typeof processBatches === 'function' && 
        typeof processTrafficData === 'function' && 
        typeof sendMessage === 'function') {
      console.log('✅ All required modules imported successfully');
      passed++;
    } else {
      console.log('❌ Some modules are not functions');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Module import failed: ${error.message}`);
    failed++;
  }
  
  // Test 4: Worker Structure
  console.log('\n4. Testing worker structure...');
  try {
    const worker = await import('../src/index.js');
    const defaultExport = worker.default;
    
    if (defaultExport && 
        typeof defaultExport === 'object' &&
        typeof defaultExport.scheduled === 'function' &&
        typeof defaultExport.fetch === 'function') {
      console.log('✅ Worker structure is correct');
      console.log('   - Has scheduled function: ✓');
      console.log('   - Has fetch function: ✓');
      passed++;
    } else {
      console.log('❌ Worker structure is incorrect');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Worker structure test failed: ${error.message}`);
    failed++;
  }
  
  // Test 5: Environment Variables Check
  console.log('\n5. Testing environment variables...');
  try {
    const requiredVars = ['SUPABASE_URL', 'SUPABASE_KEY', 'AUTH_TOKEN'];
    const missing = requiredVars.filter(key => !config[key]);
    
    console.log('📋 Configuration status:');
    console.log(`   - SUPABASE_URL: ${config.SUPABASE_URL ? '✓ Set' : '✗ Missing'}`);
    console.log(`   - SUPABASE_KEY: ${config.SUPABASE_KEY ? '✓ Set' : '✗ Missing'}`);
    console.log(`   - AUTH_TOKEN: ${config.AUTH_TOKEN ? '✓ Set' : '✗ Missing'}`);
    console.log(`   - FEISHU_WEBHOOK_URL: ${config.FEISHU_WEBHOOK_URL ? '✓ Set' : '✗ Missing'}`);
    
    // Check API keys status
    const apiKeys = ['SIMILARWEB_API_KEY', 'AHREFS_API_KEY', 'GOOGLE_API_KEY'];
    const placeholderKeys = apiKeys.filter(key => 
      !config[key] || config[key].startsWith('your_')
    );
    
    if (placeholderKeys.length > 0) {
      console.log(`⚠️  API keys need configuration: ${placeholderKeys.join(', ')}`);
      console.log('💡 These are placeholder values from wrangler.toml');
    }
    
    if (missing.length === 0) {
      console.log('✅ All required environment variables are set');
      passed++;
    } else {
      console.log(`⚠️  Missing environment variables: ${missing.join(', ')}`);
      console.log('✅ Test passed with warnings');
      passed++;
    }
  } catch (error) {
    console.log(`❌ Environment variables test failed: ${error.message}`);
    failed++;
  }
  
  // Test 6: Date Logic (for cron scheduling)
  console.log('\n6. Testing date/scheduling logic...');
  try {
    const testDate = new Date('2024-01-07T10:00:00Z'); // A Sunday
    const dayOfWeek = testDate.getDay();
    
    if (dayOfWeek === 0) {
      console.log('✅ Date calculation works correctly');
      console.log('   - Sunday detection: ✓');
      passed++;
    } else {
      console.log('❌ Date calculation is incorrect');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Date logic test failed: ${error.message}`);
    failed++;
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 Basic Test Results');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📋 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All basic tests passed! Core functionality is working.');
    console.log('\n💡 To test with real APIs, configure environment variables:');
    console.log('   - SUPABASE_URL and SUPABASE_KEY for database');
    console.log('   - API keys for SimilarWeb, Ahrefs, and Google');
    console.log('   - FEISHU_WEBHOOK_URL for notifications');
  } else {
    console.log('\n🚨 Some tests failed. Check the errors above.');
  }
  
  return failed === 0 ? 0 : 1;
}

// Run the tests
runBasicTests()
  .then(exitCode => process.exit(exitCode))
  .catch(error => {
    console.error('Test suite failed to run:', error);
    process.exit(1);
  });