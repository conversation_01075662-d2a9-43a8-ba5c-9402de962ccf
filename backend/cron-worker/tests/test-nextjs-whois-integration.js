/**
 * NextJS WHOIS API Integration Test
 * Tests the integration between NextJS frontend API and cron-worker backend
 */

async function testNextJSWhoisIntegration() {
  console.log('🧪 Testing NextJS WHOIS API Integration...\n');

  const testDomains = ['example.com', 'google.com', 'test.ai'];
  const nextjsUrl = 'http://localhost:3000';

  for (const domain of testDomains) {
    console.log(`\n🔍 Testing domain: ${domain}`);
    console.log('='.repeat(50));

    try {
      const response = await fetch(`${nextjsUrl}/api/domain/whois?domain=${encodeURIComponent(domain)}`);
      
      if (response.ok) {
        const data = await response.json();
        
        console.log('✅ NextJS API Response Success');
        console.log(`   Status: ${response.status}`);
        console.log(`   Domain: ${data.domain}`);
        console.log(`   Service Available: ${!data.serviceUnavailable}`);
        
        if (data.serviceUnavailable) {
          console.log(`   Service Error: ${data.serviceError}`);
          console.log(`   ✅ Graceful error handling - no exception thrown`);
        } else {
          console.log(`   Registrar: ${data.registrar || 'N/A'}`);
          console.log(`   Created: ${data.createdDate || 'N/A'}`);
          console.log(`   Expires: ${data.expiryDate || 'N/A'}`);
        }
        
        console.log(`   Source: ${data.source}`);
        console.log(`   Timestamp: ${data.timestamp}`);
        
      } else {
        console.log('❌ NextJS API Response Failed');
        console.log(`   Status: ${response.status}`);
        const errorData = await response.json();
        console.log(`   Error: ${errorData.error}`);
      }
      
    } catch (error) {
      console.log('❌ Integration Test Error:', error.message);
    }

    // Add delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n🎉 NextJS WHOIS Integration Tests Completed!');
}

// Run test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testNextJSWhoisIntegration().catch(error => {
    console.error('❌ Integration test failed:', error);
    process.exit(1);
  });
}

export { testNextJSWhoisIntegration }; 