# 🔧 手动KV数据库设置指南

由于Wrangler CLI命令格式变更，请按以下步骤手动创建KV namespace：

## 📋 步骤 1: 确认Node.js版本

确保使用Node.js 20+：
```bash
node -v  # 应该显示 20.x.x
```

如果不是，请升级：
```bash
nvm use 20  # 或 nvm install 20 && nvm use 20
```

## 🗂️ 步骤 2: 创建KV Namespace

### 创建生产环境namespace：
```bash
npx wrangler kv namespace create "QUOTA_KV"
```

预期输出类似：
```
🌀 Creating namespace with title "linktrackpro-cron-worker-QUOTA_KV"
✨ Success! Created KV namespace "linktrackpro-cron-worker-QUOTA_KV"
Add the following to your configuration file in your kv_namespaces array:
{ binding = "QUOTA_KV", id = "abcd1234567890abcd1234567890abcd" }
```

**记录生产环境ID**: `abcd1234567890abcd1234567890abcd`

### 创建预览环境namespace：
```bash
npx wrangler kv namespace create "QUOTA_KV" --preview
```

预期输出类似：
```
🌀 Creating namespace with title "linktrackpro-cron-worker-QUOTA_KV_preview"
✨ Success! Created KV namespace "linktrackpro-cron-worker-QUOTA_KV_preview"  
Add the following to your configuration file in your kv_namespaces array:
{ binding = "QUOTA_KV", preview_id = "efgh5678901234efgh5678901234efgh" }
```

**记录预览环境ID**: `efgh5678901234efgh5678901234efgh`

## ⚙️ 步骤 3: 更新wrangler.toml

替换 `/Users/<USER>/Code/LinkTrackPro/backend/cron-worker/wrangler.toml` 中的占位符：

```toml
# KV Database for RapidAPI quota management
[[kv_namespaces]]
binding = "QUOTA_KV"
id = "abcd1234567890abcd1234567890abcd"      # 替换为实际生产环境ID
preview_id = "efgh5678901234efgh5678901234efgh"  # 替换为实际预览环境ID
```

## 🚀 步骤 4: 部署Worker

```bash
npx wrangler deploy
```

## 🧪 步骤 5: 测试KV配置

部署后访问：
```
https://your-worker-url.com/api/quota/status
```

应该返回JSON格式的配额状态。

## 📊 步骤 6: 访问Web仪表板

```
https://your-worker-url.com/quota?token=8636be4e-24d9-11f0-8829-ebb795c2affb
```

## ✅ 验证清单

- [ ] Node.js 20+ 已安装
- [ ] 生产环境KV namespace已创建
- [ ] 预览环境KV namespace已创建  
- [ ] wrangler.toml已更新实际ID
- [ ] Worker已部署成功
- [ ] API endpoints响应正常
- [ ] Web仪表板可访问

## 🔧 故障排除

### 常见错误：

**1. "Unknown arguments" 错误**
- 解决：使用 `wrangler kv namespace create` 而不是 `wrangler kv:namespace create`

**2. "Authentication required" 错误**  
- 解决：运行 `npx wrangler login`

**3. "KV binding not found" 错误**
- 检查wrangler.toml中的ID是否正确
- 确保binding名称为 "QUOTA_KV"

**4. Web界面401错误**
- 确保URL包含正确的token参数
- 检查AUTH_TOKEN环境变量

成功设置后，您的RapidAPI配额管理系统将完全可用！🎉