# D1 API Logging System

## Overview

The LinkTrackPro Cron Worker now includes comprehensive API logging and usage tracking using Cloudflare D1 database. This system records detailed information about every API call, including:

- Request/response details
- Performance metrics
- Authentication status
- External API usage
- Processing results
- Error tracking
- Usage statistics

## Database Schema

The D1 database contains three main tables:

### `api_endpoints`
Defines all available API endpoints with their configurations:
- `endpoint` - API endpoint path
- `method` - HTTP method
- `category` - Endpoint category (e.g., 'external-links', 'batch-processing')
- `requires_auth` - Whether authentication is required
- `rate_limit_per_hour` - Rate limit configuration

### `api_call_logs`
Records every API call with detailed information:
- Request details (URL, headers, query params)
- Authentication information
- Response details (status, size, duration)
- Processing metrics (items processed/successful/failed)
- External API usage and costs
- Error information

### `api_usage_stats`
Hourly aggregated statistics for performance monitoring:
- Call counts (total, successful, failed, cache hits)
- Performance metrics (avg/min/max response times)
- Data transfer statistics
- External API usage summaries

## Setup Instructions

### 1. Create D1 Database

```bash
# Create the D1 database
wrangler d1 create linktrackpro-api-logs

# Note the database_id from the output and update wrangler.toml
```

### 2. Initialize Database Schema

```bash
# Apply the database schema
wrangler d1 execute linktrackpro-api-logs --local --file=schema.sql

# For production
wrangler d1 execute linktrackpro-api-logs --file=schema.sql
```

### 3. Update Configuration

The database binding is already configured in `wrangler.toml`:

```toml
[[d1_databases]]
binding = "DB"
database_name = "linktrackpro-api-logs"
database_id = "YOUR_DATABASE_ID"  # Update with actual ID
```

### 4. Deploy

```bash
# Deploy the worker with D1 database
wrangler deploy
```

## API Endpoints for Monitoring

### Get Usage Statistics

```bash
curl "https://your-worker.example.workers.dev/api/stats?granularity=hour&startDate=2024-01-01"
```

Query parameters:
- `endpoint` - Filter by specific endpoint (optional)
- `startDate` - Start date for statistics (YYYY-MM-DD)
- `endDate` - End date for statistics (YYYY-MM-DD)
- `granularity` - 'hour' or 'day' (default: 'hour')

Response:
```json
{
  "success": true,
  "data": {
    "statistics": {
      "stats": [
        {
          "endpoint": "/api/external-links",
          "category": "external-links",
          "date": "2024-01-01",
          "hour": 14,
          "total_calls": 25,
          "successful_calls": 23,
          "failed_calls": 2,
          "cache_hits": 5,
          "avg_response_time_ms": 1250.5,
          "total_external_api_calls": 18,
          "total_external_api_cost": 0.045
        }
      ],
      "total_records": 1,
      "granularity": "hour"
    },
    "recent_logs": [
      {
        "id": 123,
        "endpoint_path": "/api/external-links",
        "method": "GET",
        "status_code": 200,
        "response_time_ms": 1200,
        "success": true,
        "cache_hit": false,
        "external_api_calls": 1,
        "items_processed": 15,
        "data_source": "google-api",
        "started_at": "2024-01-01T14:30:00Z"
      }
    ]
  }
}
```

## Logged Information

### Request Information
- Endpoint path and HTTP method
- Query parameters and request body
- User agent and referer headers
- IP address (from Cloudflare headers)
- Authentication details

### Response Information
- HTTP status code
- Response size in bytes
- Response time in milliseconds
- Success/failure status
- Error messages and codes

### Processing Metrics
- Number of items processed
- Number of successful operations
- Number of failed operations
- Data source used (cache, google-api, ahrefs-api, etc.)

### External API Usage
- Number of external API calls made
- Estimated cost of external API calls
- API rate limiting information

## Automatic Features

### Middleware Integration
All API endpoints are automatically wrapped with logging middleware that:
- Records request start time
- Extracts authentication information
- Tracks external API calls
- Measures response time
- Logs processing results
- Updates usage statistics

### Cache Detection
The system automatically detects when responses come from cache vs. fresh API calls:
```javascript
env.apiLogger?.setCacheHit(true);  // Mark as cache hit
```

### External API Tracking
External API calls are automatically tracked:
```javascript
env.apiLogger?.setExternalApiUsage(1, 0.001);  // 1 call, $0.001 cost
```

### Processing Metrics
Processing results are automatically logged:
```javascript
env.apiLogger?.setProcessingMetrics(100, 95, 5);  // 100 processed, 95 success, 5 failed
```

## Monitoring and Alerts

### Performance Monitoring
Track API performance trends:
- Average response times
- Error rates
- Cache hit ratios
- External API usage costs

### Usage Analytics
Monitor API usage patterns:
- Most popular endpoints
- Peak usage hours
- User behavior patterns
- Rate limiting effectiveness

### Error Tracking
Identify and resolve issues:
- Failed request details
- Error message patterns
- Problematic domains or parameters
- External API failures

## Data Retention

- **API Call Logs**: Retained indefinitely (consider implementing cleanup for very old logs)
- **Usage Statistics**: Retained indefinitely (hourly aggregations are compact)
- **Cache Duration**: 24 hours for discovered links, 7 days for domain ratings

## Security Considerations

- IP addresses are logged for rate limiting and abuse detection
- Authentication details are logged (but not tokens/passwords)
- All sensitive data in query parameters is logged (be cautious with API keys in URLs)
- D1 database has built-in security through Cloudflare Workers environment

## Performance Impact

The logging system is designed for minimal performance impact:
- Non-blocking logging operations
- Efficient database operations with proper indexing
- Automatic batching of statistics updates
- Graceful fallback if D1 is unavailable

## Development and Testing

For local development:
```bash
# Run with local D1 database
wrangler dev --local

# Execute SQL queries locally
wrangler d1 execute linktrackpro-api-logs --local --command="SELECT COUNT(*) FROM api_call_logs"
```

## Troubleshooting

### Common Issues

1. **D1 Database Not Available**
   - Check wrangler.toml configuration
   - Verify database_id is correct
   - Ensure database exists in Cloudflare dashboard

2. **Schema Errors**
   - Re-run schema.sql file
   - Check for table creation errors
   - Verify all indexes are created

3. **Missing Logs**
   - Check if D1 binding is working
   - Verify middleware is properly integrated
   - Look for JavaScript errors in worker logs

4. **Performance Issues**
   - Monitor D1 query performance
   - Check for database locks
   - Consider query optimization

### Debugging Commands

```bash
# Check recent logs
wrangler d1 execute linktrackpro-api-logs --command="SELECT * FROM api_call_logs ORDER BY started_at DESC LIMIT 10"

# Check usage statistics
wrangler d1 execute linktrackpro-api-logs --command="SELECT * FROM api_usage_stats ORDER BY date DESC, hour DESC LIMIT 10"

# Check endpoint configuration
wrangler d1 execute linktrackpro-api-logs --command="SELECT * FROM api_endpoints"
```