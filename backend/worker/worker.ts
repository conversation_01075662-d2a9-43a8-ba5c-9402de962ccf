import { PrismaClient, OrderStatus } from '@prisma/client'
import * as dotenv from 'dotenv'
import OpenAI from 'openai'
import { GoogleGenerativeAI } from '@google/generative-ai';
import { uploadFileToStorage, uploadBufferToStorage } from './cloudflareR2' 
import fetch from 'node-fetch'
import { sendLarkWebhook } from './webhook'
import { styleConfig } from './config'

// Initialize environment variables
dotenv.config()

// Initialize Prisma client
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

// Generate a unique worker ID for logging
const WORKER_ID = `worker-${Math.random().toString(36).substring(2, 8)}`;

// Helper function to generate consistent log messages with timestamp and worker ID
function logMessage(level: 'INFO' | 'ERROR' | 'WARN', message: string): void {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}][${level}][${WORKER_ID}] ${message}`);
}

// Configuration for worker threads and APIs
interface ApiConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  provider?: 'openai' | 'gemini';
  maxConcurrency?: number; // Maximum concurrent requests per API
  currentConcurrency?: number; // Current number of requests being processed
}

interface WorkerConfig {
  paidApis: ApiConfig[];
  freeApis: ApiConfig[];
}

// Load configuration from environment variables
const workerConfig: WorkerConfig = {
  paidApis: [],
  freeApis: []
};

// Initialize API configs
// Load API configurations from environment variables
const loadApiConfigs = () => {
  // Count how many API configurations are defined
  const apiCount = parseInt(process.env.API_COUNT || '1', 10);
  
  for (let i = 0; i < apiCount; i++) {
    const apiKey = process.env[`OPENAI_API_KEY_${i}`] || process.env.OPENAI_API_KEY || '';
    const baseURL = process.env[`OPENAI_BASE_URL_${i}`] || process.env.OPENAI_BASE_URL || '';
    const model = process.env[`OPENAI_MODEL_${i}`] || process.env.OPENAI_MODEL || 'gpt-4o';
    const isPriority = process.env[`API_PRIORITY_${i}`] === 'high';
    const provider = process.env[`API_PROVIDER_${i}`] || 'openai';
    const maxConcurrency = parseInt(process.env[`API_CONCURRENCY_${i}`] || '1', 10);
    
    const config: ApiConfig = { 
      apiKey, 
      baseURL, 
      model,
      provider: provider as 'openai' | 'gemini',
      maxConcurrency,
      currentConcurrency: 0
    };
    
    if (isPriority) {
      workerConfig.paidApis.push(config);
    } else {
      workerConfig.freeApis.push(config);
    }
  }
  
  // If no configs were loaded for either category, use the default
  if (workerConfig.paidApis.length === 0) {
    const defaultMaxConcurrency = parseInt(process.env.DEFAULT_PAID_CONCURRENCY || '1', 10);
    workerConfig.paidApis.push({
      apiKey: process.env.OPENAI_API_KEY || '',
      baseURL: process.env.OPENAI_BASE_URL || '',
      model: process.env.OPENAI_MODEL || 'gpt-4o',
      provider: 'openai',
      maxConcurrency: defaultMaxConcurrency,
      currentConcurrency: 0
    });
  }
  
  if (workerConfig.freeApis.length === 0) {
    const defaultMaxConcurrency = parseInt(process.env.DEFAULT_FREE_CONCURRENCY || '1', 10);
    workerConfig.freeApis.push({
      apiKey: process.env.OPENAI_API_KEY || '',
      baseURL: process.env.OPENAI_BASE_URL || '',
      model: process.env.OPENAI_MODEL || 'gpt-4o',
      provider: 'openai',
      maxConcurrency: defaultMaxConcurrency,
      currentConcurrency: 0
    });
  }
  
  // Add Gemini API config if environment variables are set
  if (process.env.GEMINI_API_KEY) {
    const maxConcurrency = parseInt(process.env.GEMINI_CONCURRENCY || '1', 10);
    const geminiConfig: ApiConfig = {
      apiKey: process.env.GEMINI_API_KEY,
      baseURL: process.env.GEMINI_BASE_URL || '',
      model: process.env.GEMINI_MODEL || 'gemini-pro-vision',
      provider: 'gemini',
      maxConcurrency,
      currentConcurrency: 0
    };
    
    if (process.env.GEMINI_PRIORITY === 'high') {
      workerConfig.paidApis.push(geminiConfig);
    } else {
      workerConfig.freeApis.push(geminiConfig);
    }
  }
};

loadApiConfigs();

// Create OpenAI clients for each API configuration
const createOpenAIClient = (config: ApiConfig) => {
  logMessage('INFO', `Creating OpenAI client with API key: ${config.apiKey} baseURL: ${config.baseURL} model: ${config.model}`);
  return new OpenAI({
    apiKey: config.apiKey,
    baseURL: config.baseURL || undefined,
  });
};

// Create Gemini client
const createGeminiClient = (config: ApiConfig) => {
  const genAI = new GoogleGenerativeAI(config.apiKey);
  return genAI.getGenerativeModel({ model: config.model });
};

// Gemini API types
interface GeminiResponse {
  response: {
    text: () => string;
  };
}

// Function to generate images using the appropriate API client
async function generateImage(config: ApiConfig, contentArray: any[], timeout: number): Promise<string> {
  if (config.provider === 'gemini') {
    // Use Gemini API
    logMessage('INFO', `Using Gemini API with model: ${config.model}`);
    const geminiModel = createGeminiClient(config);
    
    // Transform contentArray to Gemini format
    const geminiContent: any[] = [];
    
    // Extract text prompt
    const textPrompt = contentArray.find(item => item.type === 'text')?.text || '';
    
    // Process images
    const images = contentArray
      .filter(item => item.type === 'image_url')
      .map(item => item.image_url.url);
    
    // Fetch images and convert to base64
    const imagesParts = await Promise.all(images.map(async (imageUrl: string) => {
      const response = await fetch(imageUrl);
      const arrayBuffer = await response.arrayBuffer();
      return {
        inlineData: {
          data: Buffer.from(arrayBuffer).toString('base64'),
          mimeType: response.headers.get('content-type') || 'image/jpeg'
        }
      };
    }));
    
    // Create Gemini content format
    const geminiPrompt = {
      contents: [{
        role: 'user',
        parts: [
          { text: textPrompt },
          ...imagesParts
        ]
      }]
    };
    
    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timed out after ${timeout}ms`));
      }, timeout);
    });
    
    try {
      // Race between Gemini API call and timeout
      const result = await Promise.race([
        geminiModel.generateContent(geminiPrompt),
        timeoutPromise
      ]);
      
      // Return the response text
      if ('response' in result) {
        return result.response.text();
      } else {
        throw new Error('Invalid Gemini response format');
      }
    } catch (error: unknown) {
      logMessage('ERROR', 'Error with Gemini API:');
      logMessage('ERROR', error instanceof Error ? error.message : String(error));
      throw error;
    }
  } else {
    // Use OpenAI API
    logMessage('INFO', `Using OpenAI API with model: ${config.model}`);
    const openai = createOpenAIClient(config);
    
    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timed out after ${timeout}ms`));
      }, timeout);
    });
    
    // Race between OpenAI API call and timeout
    try {
      const result = await Promise.race([
        openai.chat.completions.create({
          model: config.model,
          messages: [
            {
              role: "user",
              content: contentArray
            }
          ],
          max_tokens: 1000
        }),
        timeoutPromise
      ]);
      
      // Extract content from the response
      if ('choices' in result && result.choices.length > 0) {
        return result.choices[0].message.content || '';
      } else {
        throw new Error('Invalid OpenAI response format');
      }
    } catch (error: unknown) {
      logMessage('ERROR', 'Error with OpenAI API:');
      logMessage('ERROR', error instanceof Error ? error.message : String(error));
      throw error;
    }
  }
}

const timeoutMs = parseInt(process.env.TIMEOUT_INTERVAL || '100000', 10);
const updateUrl = process.env.WEB_API_URL + "/api/order/update";

// Processing state tracking
let isPolling = false;

// Find an available API for the given order
function findAvailableApi(isPaid: boolean, requestedModel?: string): ApiConfig | null {
  const apiConfigs = isPaid ? workerConfig.paidApis : workerConfig.freeApis;
  
  // Try to find an API with the requested model first
  if (requestedModel) {
    // For Gemini model
    if (requestedModel.toLowerCase().includes('gemini')) {
      const availableGeminiApi = apiConfigs.find(config => 
        config.provider === 'gemini' && 
        (config.currentConcurrency || 0) < (config.maxConcurrency || 1)
      );
      if (availableGeminiApi) return availableGeminiApi;
    } 
    // For specific OpenAI model
    else {
      const availableOpenAIApi = apiConfigs.find(config => 
        config.provider === 'openai' && 
        config.model.toLowerCase().includes(requestedModel.toLowerCase()) && 
        (config.currentConcurrency || 0) < (config.maxConcurrency || 1)
      );
      if (availableOpenAIApi) return availableOpenAIApi;
    }
  }
  
  // If no matching model found or no model specified, 
  // find an available API with capacity (prioritizing those with most available capacity)
  const availableApis = apiConfigs.filter(config => 
    (config.currentConcurrency || 0) < (config.maxConcurrency || 1)
  );
  
  if (availableApis.length === 0) {
    return null;
  }
  
  // Sort by most available capacity (maxConcurrency - currentConcurrency)
  availableApis.sort((a, b) => {
    const aAvailable = (a.maxConcurrency || 1) - (a.currentConcurrency || 0);
    const bAvailable = (b.maxConcurrency || 1) - (b.currentConcurrency || 0);
    return bAvailable - aAvailable; // higher availability first
  });
  
  return availableApis[0];
}

// Process a single task from the queue
async function processTask(order: any, selectedApi: ApiConfig) {
  try {
    // Increment API concurrency counter
    selectedApi.currentConcurrency = (selectedApi.currentConcurrency || 0) + 1;
    logMessage('INFO', `Processing order ${order.orderNo} with API ${selectedApi.provider} (${selectedApi.model}), concurrency: ${selectedApi.currentConcurrency}/${selectedApi.maxConcurrency}`);
    
    // Parse the order options from metadata
    let outputOptions;
    try {
      outputOptions = typeof order.outputOptions === 'string' ? JSON.parse(order.outputOptions) : order.outputOptions || {};
    } catch (error) {
      logMessage('WARN', `Failed to parse outputOptions for order ${order.orderNo}, using empty object`);
      outputOptions = {};
    }
    
    // Ensure options is always an object
    const options = outputOptions || {};
    logMessage('INFO', `Options: ${JSON.stringify(options)}`);

    // Update order status to PROCESSING
    await prisma.order.update({
      where: { id: order.id },
      data: { status: OrderStatus.PROCESSING }
    });
    
    // Prepare the prompt for image generation
    let fileUrl = order.inputFilePath;
    
    // Verify that fileUrl is valid
    if (!fileUrl) {
      throw new Error('Invalid or missing input file URL');
    }
    
    let promptText = "";
    let contentArray: any[] = [];

    // Set default style if not provided
    if (!options.style) {
      options.style = 'Ghibli'; // Default to Ghibli style if none provided
    }

    const styleConfigItem = styleConfig.find(item => item.style.toLowerCase() === options.style.toLowerCase());
    if (styleConfigItem) {
      promptText = styleConfigItem.prompt || "";
      if (styleConfigItem.ignore_user_prompt) {
        promptText = styleConfigItem.prompt || "";
      }
      else {
        promptText = options.promptText || "";
      }
      contentArray.push({
        type: "text",
        text: promptText
      });
      if (styleConfigItem.system_reference_image_required && styleConfigItem.system_reference_image_url && styleConfigItem.system_reference_image_url.trim() !== '') {
        contentArray.push({
          type: "image_url",
          image_url: { url: styleConfigItem.system_reference_image_url }
        });
      }
      if (styleConfigItem.user_reference_image_required && options.referenceImageUrl) {
        contentArray.push({
          type: "image_url",
          image_url: { url: options.referenceImageUrl }
        });
      }
      
      // Always add the user's input image to the content array
      contentArray.push({
        type: "image_url",
        image_url: { url: fileUrl }
      });
    }
    else {
      // Fallback to default content array if no matching style found
      promptText = options.promptText || "Transform this image in a beautiful style";
      contentArray = [
        { type: "text", text: promptText },
        {
          type: "image_url",
          image_url: { url: fileUrl }
        }
      ];
    }

    logMessage('INFO', `Generating image with prompt: ${promptText} using ${selectedApi.baseURL || selectedApi.provider || 'default'} model: ${selectedApi.model} keys: ${selectedApi.apiKey}`);
    logMessage('INFO', `Content array: ${JSON.stringify(contentArray)}`);
    
    const startTime = Date.now();
    
    let aiResponse;
    try {
      aiResponse = await generateImage(
        selectedApi,
        contentArray,
        timeoutMs
      );
    } catch (apiError: unknown) {
      logMessage('ERROR', `Error calling ${selectedApi.provider || 'API'}:`);
      logMessage('ERROR', apiError instanceof Error ? apiError.message : String(apiError));
      throw new Error(`${(selectedApi.provider || 'API').toUpperCase()} error: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`);
    }
    
    const endTime = Date.now();
    logMessage('INFO', `${(selectedApi.provider || 'API').toUpperCase()} response received after ${endTime - startTime} ms`);
    
    // Extract image URLs from the AI response
    const imageUrls = extractImageUrls(aiResponse);
    logMessage('INFO', `Extracted ${imageUrls.length} image URLs from AI response`);
    
    if (imageUrls.length === 0) {
      await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 没有生成图片，AI返回内容为：${aiResponse}`);
      throw new Error('No images found in the AI response');
    }
    
    // Download and upload images to Cloudflare R2
    const uploadedImageUrls = await Promise.all(
      imageUrls.map(async (imageUrl, index) => {
        try {
          logMessage('INFO', `Downloading image ${index + 1} from ${imageUrl}`);
          // Download the image
          const response = await fetch(imageUrl);
          if (!response.ok) {
            await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 下载图片失败，图片URL为：${imageUrl}`);
            throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
          }
          
          // Convert to buffer for Node.js environment
          const arrayBuffer = await response.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          const contentType = response.headers.get('content-type') || 'image/png';
          const fileName = `gen-image-${order.orderNo}-${index + 1}.png`;
          
          // Upload to Cloudflare R2 using the Buffer version
          logMessage('INFO', `Uploading image ${index + 1} to Cloudflare R2`);
          const uploadedUrl = await uploadBufferToStorage(buffer, fileName, contentType);
          return uploadedUrl;
        } catch (downloadError: unknown) {
          logMessage('ERROR', `Error processing image ${index + 1}:`);
          logMessage('ERROR', downloadError instanceof Error ? downloadError.message : String(downloadError));
          await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 上传图片失败，错误信息为：${downloadError instanceof Error ? downloadError.message : String(downloadError)}`);
          return null;
        }
      })
    );
    
    // Filter out any failed uploads
    const successfulUploads = uploadedImageUrls.filter(url => url !== null);
    
    if (successfulUploads.length === 0) {
      throw new Error('Failed to upload any images to storage');
    }
    
    // Update order with generated images
    try {
      logMessage('INFO', `Updating order ${order.orderNo} with ${successfulUploads.length} images`);
      
      // Update the web API
      try {
        const response = await fetch(updateUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.orderNo,
            status: OrderStatus.SUCCEED,
            outputText: aiResponse,
            outputImagePath: successfulUploads[0],
            outputOptions: successfulUploads.join(','),
            reason: 'Success',
            costTime: endTime - startTime
          }),
          timeout: 10000 // 10 seconds timeout
        });
        
        if (!response.ok) {
          await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 更新订单失败，错误信息为：${response.status} ${response.statusText}`);
          logMessage('ERROR', `Failed to update order via web API: ${response.status} ${response.statusText}`);
        }
      } catch (apiError: unknown) {
        await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 更新订单失败，错误信息为：${apiError instanceof Error ? apiError.message : String(apiError)}`);
        logMessage('ERROR', 'Failed to call update API:');
        logMessage('ERROR', apiError instanceof Error ? apiError.message : String(apiError));
      }

      await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][SUCCESS] 图片生成成功，图片URL为：${successfulUploads[0]}`);
      logMessage('INFO', `Order ${order.orderNo} processed successfully`);
    } catch (dbError: unknown) {
      logMessage('ERROR', 'Failed to update order in database:');
      logMessage('ERROR', dbError instanceof Error ? dbError.message : String(dbError));
      throw new Error(`Database update error: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`);
    }
  } catch (error: unknown) {
    logMessage('ERROR', `Error processing order ${order.orderNo}:`);
    logMessage('ERROR', error instanceof Error ? error.message : String(error));
    
    try {
      // Notify the web API of failure
      try {
        const response = await fetch(updateUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.orderNo,
            status: OrderStatus.FAILED,
            reason: error instanceof Error ? error.message : String(error),
            outputText: null,
            outputImagePath: null,
            costTime: 0
          }),
          timeout: 10000 // 10 seconds timeout
        });
        
        if (!response.ok) {
          logMessage('ERROR', 'Failed to update order failure via web API:');
          logMessage('ERROR', `${response.status} ${response.statusText}`);
          await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 更新失败状态失败，错误信息为：${response.status} ${response.statusText}`);
        }
      } catch (apiError: unknown) {
        logMessage('ERROR', 'Failed to call update API for failure:');
        logMessage('ERROR', apiError instanceof Error ? apiError.message : String(apiError));
      }
    } catch (dbError: unknown) {
      logMessage('ERROR', 'Failed to update order status due to database error:');
      logMessage('ERROR', dbError instanceof Error ? dbError.message : String(dbError));
      await sendLarkWebhook(`[FREE-AI][${order.isPaid ? 'PAID' : 'FREE'}][${order.orderNo}][ERROR] 更新订单失败，错误信息为：${dbError instanceof Error ? dbError.message : String(dbError)}`);
    }
  } finally {
    // Release API concurrency counter
    selectedApi.currentConcurrency = (selectedApi.currentConcurrency || 1) - 1;
    logMessage('INFO', `Decreased concurrency for API ${selectedApi.provider} to ${selectedApi.currentConcurrency}/${selectedApi.maxConcurrency}`);
  }
}

// Helper function to extract image URLs from AI response
export function extractImageUrls(aiResponse: string): string[] {
  const urls: string[] = [];
  
  // Regex to find Markdown image syntax ![...](URL)
  const markdownImageRegex = /!\[.*?\]\((https?:\/\/[^\s)]+)\)/g;
  let match;
  
  while ((match = markdownImageRegex.exec(aiResponse)) !== null) {
    urls.push(match[1]);
  }
  
  // If no markdown images, try finding plain URLs (common in some API responses)
  if (urls.length === 0) {
    const urlRegex = /(https?:\/\/\S+\.(?:png|jpg|jpeg|webp|gif))/gi;
    while ((match = urlRegex.exec(aiResponse)) !== null) {
      urls.push(match[1]);
    }
  }
  
  // Look for filesystem.site URLs which are used in the test example
  const filesystemSiteRegex = /(https?:\/\/filesystem\.site\/cdn\/[^\s)]+)/g;
  while ((match = filesystemSiteRegex.exec(aiResponse)) !== null) {
    urls.push(match[1]);
  }

  const uniqueUrls = [...new Set(urls)];
  
  return uniqueUrls;
}

// Main polling function that follows the required logic
async function pollForTasks() {
  if (isPolling) return;
  isPolling = true;
  
  try {
    logMessage('INFO', 'Polling for tasks...');
    
    // 1. Query for pending orders
    const pendingOrders = await prisma.order.findMany({
      where: {
        status: OrderStatus.PENDING
      },
      orderBy: [
        { isPaid: 'desc' },
        { createTime: 'asc' }
      ]
    });
    
    if (pendingOrders.length === 0) {
      logMessage('INFO', 'No pending orders found.');
      return;
    }
    
    logMessage('INFO', `Found ${pendingOrders.length} pending orders`);
    
    // Get batch size from environment variable
    const batchSize = parseInt(process.env.BATCH_SIZE || '3', 10);
    let processedCount = 0;
    
    // Process orders in batches based on priority and API availability
    for (const order of pendingOrders) {
      // Check if we've reached the batch size limit
      if (processedCount >= batchSize) {
        logMessage('INFO', `Reached batch size limit (${batchSize}). Will process remaining orders in next poll.`);
        break;
      }
      
      // Parse the options to get model info if available
      let requestedModel;
      try {
        const options = typeof order.outputOptions === 'string' 
          ? JSON.parse(order.outputOptions) 
          : order.outputOptions || {};
        requestedModel = options.model;
      } catch (e) {
        // If parsing fails, just continue without requested model
      }
      
      // Find an available API with capacity
      const availableApi = findAvailableApi(order.isPaid, requestedModel);
      
      if (!availableApi) {
        // If no APIs are available for this order type, skip to next
        logMessage('INFO', `No available APIs for order ${order.orderNo} (isPaid: ${order.isPaid})`);
        continue;
      }
      
      // Process the order with the selected API
      logMessage('INFO', `Starting to process order ${order.orderNo} (isPaid: ${order.isPaid}) with ${availableApi.provider} API (concurrency: ${availableApi.currentConcurrency}/${availableApi.maxConcurrency})`);
      
      // Process this order without awaiting
      processTask(order, availableApi).catch(error => {
        logMessage('ERROR', `Unhandled error processing order ${order.orderNo}:`);
        logMessage('ERROR', error instanceof Error ? error.message : String(error));
      });
      
      // Increment the processed count
      processedCount++;
    }
    
    logMessage('INFO', `Started processing ${processedCount} orders in this batch`);
  } catch (error: unknown) {
    logMessage('ERROR', 'Error in main polling function:');
    logMessage('ERROR', error instanceof Error ? error.message : String(error));
  } finally {
    isPolling = false;
    
    // Schedule next poll
    const pollingInterval = parseInt(process.env.POLLING_INTERVAL || '5000', 10);
    setTimeout(pollForTasks, pollingInterval);
  }
}

// Start the worker
logMessage('INFO', 'Starting worker...');
logMessage('INFO', `Using database URL: ${process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':****@')}`);
logMessage('INFO', `Worker configuration - Paid APIs: ${workerConfig.paidApis.length}, Free APIs: ${workerConfig.freeApis.length}`);

// Log details of each API
logMessage('INFO', 'Paid APIs:');
workerConfig.paidApis.forEach((api, index) => {
  logMessage('INFO', `  [${index}] Provider: ${api.provider}, Model: ${api.model}, Max Concurrency: ${api.maxConcurrency}`);
});

logMessage('INFO', 'Free APIs:');
workerConfig.freeApis.forEach((api, index) => {
  logMessage('INFO', `  [${index}] Provider: ${api.provider}, Model: ${api.model}, Max Concurrency: ${api.maxConcurrency}`);
});

// Reset any stuck PROCESSING orders back to PENDING
async function resetStuckOrders() {
  try {
    logMessage('INFO', 'Checking for orders stuck in PROCESSING state...');
    
    // Find orders stuck in processing state
    const stuckOrders = await prisma.order.findMany({
      where: {
        status: OrderStatus.PROCESSING
      }
    });
    
    if (stuckOrders.length > 0) {
      logMessage('INFO', `Found ${stuckOrders.length} orders stuck in PROCESSING state. Resetting to PENDING...`);
      
      // Reset these orders to PENDING
      const result = await prisma.order.updateMany({
        where: {
          status: OrderStatus.PROCESSING
        },
        data: {
          status: OrderStatus.PENDING,
          updateTime: new Date()
        }
      });
      
      logMessage('INFO', `Reset ${result.count} orders from PROCESSING to PENDING status.`);
    } else {
      logMessage('INFO', 'No orders stuck in PROCESSING state found.');
    }
  } catch (error: unknown) {
    logMessage('ERROR', 'Error resetting stuck orders:');
    logMessage('ERROR', error instanceof Error ? error.message : String(error));
  }
}

// Reset any stuck orders before starting polling
resetStuckOrders().then(() => {
  // Start the regular polling
  pollForTasks();
}).catch(error => {
  logMessage('ERROR', 'Failed to reset stuck orders:');
  logMessage('ERROR', error instanceof Error ? error.message : String(error));
  // Start polling anyway even if reset failed
  pollForTasks();
});

// Handle graceful shutdown
process.on('SIGINT', async () => {
  logMessage('INFO', 'Worker shutting down...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logMessage('INFO', 'Worker shutting down...');
  await prisma.$disconnect();
  process.exit(0);
});