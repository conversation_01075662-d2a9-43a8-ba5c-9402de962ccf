# Worker Configuration
POLLING_INTERVAL=5000
TIMEOUT_INTERVAL=30000
BATCH_SIZE=5

# Thread Configuration
PAID_THREADS=2
FREE_THREADS=1

# Default Concurrency Configuration
DEFAULT_PAID_CONCURRENCY=3
DEFAULT_FREE_CONCURRENCY=1

# API Configuration
API_COUNT=3

# Default API (used if specific ones aren't configured)
OPENAI_API_KEY=your_default_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o

# High Priority API for paid users (index 0)
OPENAI_API_KEY_0=your_priority_api_key_1
OPENAI_BASE_URL_0=https://api.highpriority.ai/v1
OPENAI_MODEL_0=gpt-4o
API_PRIORITY_0=high
API_CONCURRENCY_0=3

# High Priority API for paid users (index 1)
OPENAI_API_KEY_1=your_priority_api_key_2
OPENAI_BASE_URL_1=https://api.highpriority2.ai/v1
OPENAI_MODEL_1=gpt-4o
API_PRIORITY_1=high
API_CONCURRENCY_1=2

# Low Priority API for free users (index 2)
OPENAI_API_KEY_2=
OPENAI_BASE_URL_2=https://api.standard.ai/v1
OPENAI_MODEL_2=gpt-4o
API_PRIORITY_2=low
API_CONCURRENCY_2=1

# Web API URL for updating orders
WEB_API_URL=https://your-website.com

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/database?schema=public
DIRECT_URL=postgresql://user:password@localhost:5432/database?schema=public

# Storage Configuration (Cloudflare R2)
UE_S3_ACCESS_KEY=your_access_key
UE_S3_SECRET_KEY=your_secret_key
UE_S3_SESSION_TOKEN=
UE_S3_ENDPOINT=https://your-endpoint.r2.cloudflarestorage.com/bucket
UE_S3_ACCOUNT_ID=
UE_S3_REGION=auto
UE_S3_BUCKET=your-bucket
UE_S3_PUBLIC_PATH=https://public-file-access.com 