version: '3.8'

services:
  img-gen-worker:
    build:
      context: .
      dockerfile: Dockerfile
    image: img-gen-worker:latest
    container_name: img-gen-worker
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "3000:3000"
    networks:
      - img-gen-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

networks:
  img-gen-network:
    driver: bridge 