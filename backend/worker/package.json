{"name": "img-gen-worker", "version": "1.0.0", "description": "Worker for processing image generation tasks", "main": "dist/worker.js", "scripts": {"build": "tsc", "start": "node dist/worker.js", "dev": "ts-node worker.ts", "test": "ts-node worker.test.ts", "pg:gen": "prisma generate", "pg:gen:binary": "PRISMA_CLIENT_ENGINE_TYPE=binary PRISMA_SCHEMA_ENGINE_TYPE=binary PRISMA_QUERY_ENGINE_TYPE=binary prisma generate", "prisma:generate": "prisma generate", "docker:build": "docker build -t img-gen-worker:latest .", "docker:deploy": "bash scripts/quick-update.sh", "docker:deploy:skip-build": "bash scripts/quick-update.sh --skip-build", "docker:deploy:no-logs": "bash scripts/quick-update.sh --no-logs", "docker:deploy:custom": "bash scripts/quick-update.sh", "docker:logs": "docker logs -f img-gen-worker", "docker:start": "docker start img-gen-worker", "docker:stop": "docker stop img-gen-worker", "docker:restart": "docker restart img-gen-worker", "docker:status": "docker ps -a | grep img-gen-worker", "docker:shell": "docker exec -it img-gen-worker /bin/bash", "docker:prune": "docker image prune -f", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "docker:compose:restart": "docker-compose restart", "macos:deploy": "bash deploy-from-macos-build.sh", "macos:deploy:help": "bash deploy-from-macos-build.sh --help", "macos:deploy:skip-build": "bash deploy-from-macos-build.sh --skip-build", "macos:deploy:no-logs": "bash deploy-from-macos-build.sh --skip-logs", "py:build": "cd ../img_gen_worker_py && docker build -t img-gen-worker-py:latest .", "py:deploy": "cd ../img_gen_worker_py && ./deploy.sh", "py:deploy:skip-build": "cd ../img_gen_worker_py && ./deploy.sh --skip-build", "py:deploy:no-logs": "cd ../img_gen_worker_py && ./deploy.sh --no-logs", "py:logs": "docker logs -f img-gen-worker-py", "py:stop": "docker stop img-gen-worker-py", "py:start": "docker start img-gen-worker-py", "py:restart": "docker restart img-gen-worker-py", "py:status": "docker ps -a -f name=img-gen-worker-py", "py:sync-env:to-py": "cd ../img_gen_worker_py && python scripts/sync_env.py", "py:sync-env:to-node": "cd ../img_gen_worker_py && python scripts/sync_env.py to_node"}, "dependencies": {"@ai-sdk/openai": "^1.3.3", "@ai-sdk/react": "^1.2.2", "@aws-sdk/client-s3": "^3.777.0", "@aws-sdk/s3-request-presigner": "^3.777.0", "@google/generative-ai": "^0.24.0", "@prisma/client": "5.22.0", "ai": "^4.2.6", "axios": "^1.8.4", "dotenv": "^16.0.3", "node-fetch": "^2.6.7", "openai": "^4.90.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^18.15.11", "@types/node-fetch": "^2.6.3", "prisma": "5.22.0", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}