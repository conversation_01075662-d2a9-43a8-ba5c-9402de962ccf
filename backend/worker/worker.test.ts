import * as dotenv from 'dotenv'
import OpenAI from 'openai'
import { GoogleGenerativeAI } from '@google/generative-ai'
import fetch from 'node-fetch'
import { extractImageUrls } from './worker'
import { v4 as uuidv4 } from 'uuid'
import { uploadFileToStorage } from './cloudflareR2'

// Initialize environment variables
dotenv.config()

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: "sk-UpndhK8R7LeGN8pqM1kgDoaQV4zZPJzVlyp2s2csxhLeAWvn",// process.env.OPENAI_API_KEY,
  baseURL: "https://yunwu.ai/v1"
});

// Initialize Gemini client if API key is available
const geminiApiKey = process.env.GEMINI_API_KEY;
const gemini = geminiApiKey ? new GoogleGenerativeAI(geminiApiKey) : null;

// Helper function to simulate file download and upload
async function downloadAndUploadImage(imageUrl: string): Promise<string | null> {
  try {
    console.log(`Downloading image from ${imageUrl}`);
    // Download the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }
    
    // Convert to blob/file
    const arrayBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/png';
    const fileName = `test-image-${uuidv4()}.png`;
    const file = new File([arrayBuffer], fileName, { type: contentType });
    
    // Upload to Cloudflare R2
    console.log(`Uploading test image to storage`);
    const uploadedUrl = await uploadFileToStorage(file);
    return uploadedUrl;
  } catch (error) {
    console.error(`Error processing test image:`, error);
    return null;
  }
}

// Process a task with OpenAI
async function processTaskWithOpenAI(fileUrl: string, options: any = {}) {
  console.log(`Processing task with OpenAI for file: ${fileUrl}`)
  
  try {
    let promptText = options.prompt || "请根据如下图片生成一组表情包的图片。采用动漫风格，表情包的图片数量为4张，直接返回图片给我即可";
    
    // Add style if provided and not reference style
    if (options.style && options.style !== 'reference') {
      promptText += `\n风格: ${options.style}`;
    }

    console.log(`Generating image with OpenAI, prompt: ${promptText}`)
    console.log(`Using model: ${options.model || "gpt-4o-image-vip"}`)

    const startTime = Date.now()
    
    // Prepare content array
    const contentArray: any[] = [
      { type: "text", text: promptText },
      {
        type: "image_url",
        image_url: {
          url: fileUrl
        }
      }
    ];
    
    // Add reference image only if style is 'reference'
    if (options.style === 'reference' && options.referenceImageUrl) {
      console.log(`Using reference image for style reference: ${options.referenceImageUrl}`);
      contentArray.push({
        type: "image_url",
        image_url: { url: options.referenceImageUrl }
      });
    }
    
    const response = await openai.chat.completions.create({
      model: options.model || "gpt-4o-image-vip",
      messages: [
        {
          role: "user",
          content: contentArray
        }
      ],
      max_tokens: 1000
    });

    const endTime = Date.now()
    console.log('OpenAI response received after ', endTime - startTime)
    console.log("Raw OpenAI response result: ", response.choices[0].message.content)
    
    // Extract image URLs from the response
    const responseText = response.choices[0].message.content || '';
    const imageUrls = extractImageUrls(responseText);
    console.log(`Extracted ${imageUrls.length} image URLs:`, imageUrls);
    
    // Download and upload images
    if (imageUrls.length > 0) {
      console.log("Downloading and uploading extracted images...");
      const uploadedUrls = await Promise.all(
        imageUrls.map((url: string) => downloadAndUploadImage(url))
      );
      
      console.log("Uploaded image URLs:", uploadedUrls.filter(Boolean));
    }

  } catch (error){
    console.error(`Error processing order with OpenAI:`, error)
  }
}

// Process a task with Gemini if available
async function processTaskWithGemini(fileUrl: string, options: any = {}) {
  if (!gemini) {
    console.log('Gemini API key not configured, skipping Gemini test');
    return;
  }
  
  console.log(`Processing task with Gemini for file: ${fileUrl}`);
  
  try {
    let promptText = options.prompt || "请根据如下图片生成一组表情包的图片。采用动漫风格，表情包的图片数量为4张，直接返回图片给我即可";
    
    // Add style if provided and not reference style
    if (options.style && options.style !== 'reference') {
      promptText += `\n风格: ${options.style}`;
    }

    console.log(`Generating image with Gemini, prompt: ${promptText}`);
    console.log(`Using model: ${options.model || "gemini-pro-vision"}`);

    const startTime = Date.now();
    
    // For Gemini, we need to fetch the images and convert to base64
    async function fetchImageAsBase64(imageUrl: string) {
      const response = await fetch(imageUrl);
      const arrayBuffer = await response.arrayBuffer();
      const contentType = response.headers.get('content-type') || 'image/jpeg';
      return {
        inlineData: {
          data: Buffer.from(arrayBuffer).toString('base64'),
          mimeType: contentType
        }
      };
    }
    
    // Fetch main image
    const imageData = await fetchImageAsBase64(fileUrl);
    
    // Prepare parts array
    const parts = [
      { text: promptText },
      imageData
    ];
    
    // Add reference image if style is reference
    if (options.style === 'reference' && options.referenceImageUrl) {
      console.log(`Using reference image for style reference: ${options.referenceImageUrl}`);
      const refImageData = await fetchImageAsBase64(options.referenceImageUrl);
      parts.push(refImageData);
    }
    
    // Get Gemini model
    const model = gemini.getGenerativeModel({ 
      model: options.model || "gemini-pro-vision" 
    });
    
    // Generate content
    const result = await model.generateContent({
      contents: [{ role: 'user', parts }]
    });
    
    const response = result.response;
    const endTime = Date.now();
    console.log('Gemini response received after ', endTime - startTime);
    console.log("Raw Gemini response: ", response.text());
    
    // Extract image URLs from the response
    const responseText = response.text();
    const imageUrls = extractImageUrls(responseText);
    console.log(`Extracted ${imageUrls.length} image URLs:`, imageUrls);
    
    // Download and upload images
    if (imageUrls.length > 0) {
      console.log("Downloading and uploading extracted images...");
      const uploadedUrls = await Promise.all(
        imageUrls.map((url: string) => downloadAndUploadImage(url))
      );
      
      console.log("Uploaded image URLs:", uploadedUrls.filter(Boolean));
    }

  } catch (error){
    console.error(`Error processing order with Gemini:`, error)
  }
}

// Start the worker
console.log('Starting worker test...')

let fileUrl = "https://img.aimcp.info/26052a0e687c6d94_s.jpg"
console.log(`Processing file: ${fileUrl}`);

// Test different option combinations
const testOptions = [
  { 
    prompt: "请根据如下图片生成一组表情包的图片。采用动漫风格，表情包的图片数量为4张，直接返回图片给我即可",
    model: "gpt-4o"
  },
  { 
    prompt: "将这张图片转换为宫崎骏风格的图像", 
    style: "动漫",
    model: "gpt-4o-vision"
  },
  { 
    prompt: "根据这张图片和参考图片的风格创建一张新图片", 
    style: "reference",
    referenceImageUrl: "https://img.aimcp.info/reference_image.jpg"
  },
  {
    prompt: "将这张图片转换为水彩画风格",
    model: "gemini"
  }
];

// Run test with OpenAI
processTaskWithOpenAI(fileUrl, testOptions[0]);

// Run test with Gemini if available
if (geminiApiKey) {
  processTaskWithGemini(fileUrl, testOptions[3]);
}