import axios from 'axios';

// 发送图片生成结果到飞书
async function sendLarkWebhook(message: string) {
  const larkWebhookUrl = process.env.LARK_WEBHOOK_URL;
  if (!larkWebhookUrl) {
    console.error('LARK_WEBHOOK_URL is not set');
    return;
  }

  try {
    await axios.post(larkWebhookUrl, {
      msg_type: 'markdown',
      content: {
        markdown: message
      }
    });
  } catch (error) {
    console.error('Failed to send Lark webhook:', error);
  }
}

export { sendLarkWebhook };
