#!/bin/bash
# Image Generation Worker macOS本地构建部署脚本
# 使用方法: ./deploy-from-macos-build.sh [选项]

# =====================================================
# 颜色定义
# =====================================================
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# =====================================================
# 工具函数
# =====================================================
print_message() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

print_help() {
  echo "用法: $0 [选项]"
  echo ""
  echo "选项:"
  echo "  -h, --help                显示此帮助信息"
  echo "  -n, --name NAME           设置容器名称 (默认: img-gen-worker)"
  echo "  -i, --image NAME          设置镜像名称 (默认: img-gen-worker:latest)"
  echo "  -p, --port PORT           设置映射端口 (默认: 3000)"
  echo "  --network NETWORK         设置容器网络 (默认: bridge)"
  echo "  --skip-build              跳过构建镜像步骤"
  echo "  --skip-logs               部署后不显示容器日志"
  echo ""
  echo "示例:"
  echo "  $0 --name my-worker --port 8080"
  echo "  $0 --skip-build --network host"
}

# =====================================================
# 解析命令行参数
# =====================================================
CONTAINER_NAME="img-gen-worker"
IMAGE_NAME="img-gen-worker:latest"
PORT="2345"
NETWORK="bridge"
SKIP_BUILD=false
SKIP_LOGS=false

while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      print_help
      exit 0
      ;;
    -n|--name)
      CONTAINER_NAME="$2"
      shift
      shift
      ;;
    -i|--image)
      IMAGE_NAME="$2"
      shift
      shift
      ;;
    -p|--port)
      PORT="$2"
      shift
      shift
      ;;
    --network)
      NETWORK="$2"
      shift
      shift
      ;;
    --skip-build)
      SKIP_BUILD=true
      shift
      ;;
    --skip-logs)
      SKIP_LOGS=true
      shift
      ;;
    *)
      print_error "未知选项: $1"
      print_help
      exit 1
      ;;
  esac
done

# =====================================================
# 检查依赖
# =====================================================
check_docker() {
  if ! command -v docker &> /dev/null; then
    print_error "Docker未安装，请先安装Docker Desktop: https://docs.docker.com/desktop/mac/install/"
    exit 1
  fi

  if ! docker info &>/dev/null; then
    print_warning "Docker似乎未运行，尝试启动Docker Desktop..."
    open -a Docker
    print_message "正在等待Docker Desktop启动..."
    
    # 等待Docker启动
    TIMEOUT=60
    for i in $(seq 1 $TIMEOUT); do
      if docker info &>/dev/null; then
        print_message "Docker Desktop已成功启动"
        break
      fi
      if [ $i -eq $TIMEOUT ]; then
        print_error "Docker Desktop未能在规定时间内启动，请手动启动Docker Desktop后重试"
        exit 1
      fi
      sleep 1
    done
  fi
}

# =====================================================
# 主函数
# =====================================================
main() {
  print_message "开始部署 Image Generation Worker..."
  
  # 检查Docker是否可用
  check_docker
  
  # 创建必要的目录
  print_message "创建数据目录..."
  mkdir -p ./data ./logs
  
  # 检查.env文件是否存在
  if [ ! -f ./.env ]; then
    if [ -f ./.env.example ]; then
      print_warning ".env文件不存在，正在从.env.example创建..."
      cp ./.env.example ./.env
      print_warning "请编辑.env文件，填写必要的配置信息"
    else
      print_error ".env文件和.env.example文件都不存在，请先创建.env文件"
      exit 1
    fi
  fi
  
  # 修改Dockerfile中的--frozen-lockfile为--no-frozen-lockfile
  print_message "调整Dockerfile以避免锁文件问题..."
  if [ -f ./Dockerfile ]; then
    # 创建临时文件
    temp_file=$(mktemp)
    # 替换frozen-lockfile为no-frozen-lockfile
    sed 's/pnpm install --frozen-lockfile/pnpm install --no-frozen-lockfile/g' ./Dockerfile > "$temp_file"
    # 检查是否成功替换
    if grep -q "pnpm install --no-frozen-lockfile" "$temp_file"; then
      # 将修改后的内容移回原文件
      mv "$temp_file" ./Dockerfile
      print_message "成功修改Dockerfile，使用--no-frozen-lockfile替代--frozen-lockfile"
    else
      rm "$temp_file"
      print_warning "未能在Dockerfile中找到'pnpm install --frozen-lockfile'，跳过此步骤"
    fi
  else
    print_warning "未找到Dockerfile，跳过修改步骤"
  fi
  
  # 构建镜像
  if [ "$SKIP_BUILD" = false ]; then
    print_message "构建Docker镜像: $IMAGE_NAME..."
    docker build -t "$IMAGE_NAME" .
    
    if [ $? -ne 0 ]; then
      print_error "构建Docker镜像失败"
      exit 1
    fi
    
    print_message "Docker镜像构建成功"
  else
    print_message "跳过构建镜像步骤"
  fi
  
  # 检查是否已存在同名容器
  if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_warning "发现已存在的容器: $CONTAINER_NAME，正在停止并移除..."
    docker stop "$CONTAINER_NAME" >/dev/null 2>&1
    docker rm "$CONTAINER_NAME" >/dev/null 2>&1
  fi
  
  # 启动容器
  print_message "启动容器: $CONTAINER_NAME..."
  docker run -d \
    --name "$CONTAINER_NAME" \
    --restart unless-stopped \
    -p "$PORT:3000" \
    --network "$NETWORK" \
    -v "$(pwd)/data:/app/data" \
    -v "$(pwd)/logs:/app/logs" \
    --env-file ./.env \
    -e PRISMA_CLIENT_ENGINE_TYPE=library \
    -e PRISMA_SCHEMA_ENGINE_TYPE=library \
    -e PRISMA_QUERY_ENGINE_TYPE=library \
    "$IMAGE_NAME"
  
  if [ $? -ne 0 ]; then
    print_error "启动容器失败"
    exit 1
  fi
  
  print_message "容器启动成功!"
  print_message "容器名称: $CONTAINER_NAME"
  print_message "镜像名称: $IMAGE_NAME"
  print_message "端口映射: $PORT:3000"
  print_message "网络模式: $NETWORK"
  
  # 显示容器日志
  if [ "$SKIP_LOGS" = false ]; then
    print_message "正在显示容器日志，按 Ctrl+C 退出日志查看..."
    docker logs -f "$CONTAINER_NAME"
  else
    print_message "容器已在后台运行，可使用 'docker logs $CONTAINER_NAME' 查看日志"
  fi
}

# 执行主函数
main 