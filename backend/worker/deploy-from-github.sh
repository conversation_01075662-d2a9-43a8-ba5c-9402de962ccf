#!/bin/bash
# Image Generation Worker一键部署脚本
# 使用方法: macOS上使用: ./deploy.sh 或 Linux上使用: sudo ./deploy.sh

# =====================================================
# 配置参数 - 请在此处修改为您的实际值
# =====================================================

# 域名和邮箱配置（如果需要通过Web访问Worker）
DOMAIN=""        # 您的域名，如果不需要Web访问可留空
ADMIN_EMAIL="<EMAIL>"        # 管理员邮箱，用于Let's Encrypt通知

# Docker镜像配置
DOCKER_IMAGE="ghcr.io/hekmon8/ai-img-gen/img-gen-worker:latest"  # Docker镜像地址

# GitHub认证信息（用于访问私有Docker镜像）
GITHUB_USERNAME="hekmon8"               # GitHub用户名
GITHUB_TOKEN="****************************************"   # GitHub个人访问令牌，需要有read:packages权限

# 配置参数
POLLING_INTERVAL=5000
TIMEOUT_INTERVAL=300000
BATCH_SIZE=3

# Thread Configuration
PAID_THREADS=1
FREE_THREADS=2

# API Configuration
API_COUNT=3

# 一次注册账户，余额110$
OPENAI_API_KEY_0=sk-UpndhK8R7LeGN8pqM1kgDoaQV4zZPJzVlyp2s2csxhLeAWvn
OPENAI_BASE_URL_0=https://yunwu.ai/v1
OPENAI_MODEL_0=gpt-4o-image-vip
API_PRIORITY_0=high
API_PROVIDER_0=openai

# 二次注册账户，余额100$
OPENAI_API_KEY_1=sk-KkPMS4y26T8OAeBEBUVhndUPoAjg5k4UXKZY8x71MCJZkuCG
OPENAI_BASE_URL_1=https://yunwu.ai/v1
OPENAI_MODEL_1=gpt-4o-image-vip
API_PRIORITY_1=low
API_PROVIDER_1=openai

# linux.do找到的，$1000额度，不确定有没有用完
OPENAI_API_KEY_2=sk-vvrb0iGIIitUHthtpQkKK3Cb2sfXKKTiN0O8ud6TT7mxn6D6
OPENAI_BASE_URL_2=https://instcopilot-api.com/v1
OPENAI_MODEL_2=gpt-4o-image
API_PRIORITY_2=low
API_PROVIDER_2=openai

# 5$ 额度
OPENAI_API_KEY_3=sk-89nGdlw7hCUobfnKxPIK4uXc5E4BK8Q9PZlAjPe7TxNXTMR9
OPENAI_BASE_URL_3=https://api.tu-zi.com/v1
OPENAI_MODEL_3=gpt-4o-image
API_PRIORITY_3=low
API_PROVIDER_3=openai

WEB_API_URL=https://www.free-ai-img.com

DATABASE_URL=postgresql://postgres.towzzcheowkqpfrbkipc:<EMAIL>:6543/postgres?schema=ghibli_img&pgbouncer=true&connect_timeout=30&connection_limit=300
DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres?schema=ghibli_img

UE_S3_ACCESS_KEY=7be28070bf653bc11a8e3b78fb5ddf7a
UE_S3_SECRET_KEY=7739e26ecf538ba6de6721b6980c87cd526da9961237590a0b9b02ad9865d533
UE_S3_SESSION_TOKEN=
UE_S3_ENDPOINT=https://65d778bd01d280395c661484c5e2b681.r2.cloudflarestorage.com/ai-img
UE_S3_ACCOUNT_ID=
UE_S3_REGION=auto
UE_S3_BUCKET=ai-img
UE_S3_PUBLIC_PATH=https://file.hekmon.com

LARK_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/8b3a45cd-2d0e-4269-8edf-9fb1009ce659

# =====================================================
# 脚本逻辑 - 以下内容通常不需要修改
# =====================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统类型
OS_TYPE=$(uname)
if [ "$OS_TYPE" = "Darwin" ]; then
  IS_MACOS=true
  print_message "检测到macOS系统"
else
  IS_MACOS=false
  print_message "检测到Linux系统"
  
  # 在Linux上检查是否为root用户
  if [ "$EUID" -ne 0 ]; then
    print_error "在Linux系统上，请使用root用户运行此脚本"
    exit 1
  fi
fi

# 检查必要的工具
check_command() {
  if ! command -v $1 &> /dev/null; then
    print_error "$1 未安装，正在安装..."
    
    if [ "$IS_MACOS" = true ]; then
      # macOS使用Homebrew安装
      if ! command -v brew &> /dev/null; then
        print_error "Homebrew未安装，请先安装Homebrew: https://brew.sh/"
        exit 1
      fi
      
      if [ "$1" = "docker" ] || [ "$1" = "docker-compose" ]; then
        brew install --cask docker
        print_warning "Docker已安装，请确保Docker Desktop已运行"
        print_warning "您可能需要手动启动Docker Desktop"
        open -a Docker
        print_message "正在等待Docker Desktop启动..."
        
        # 等待Docker启动
        TIMEOUT=60
        for i in $(seq 1 $TIMEOUT); do
          if docker info &>/dev/null; then
            print_message "Docker Desktop已成功启动"
            break
          fi
          if [ $i -eq $TIMEOUT ]; then
            print_error "Docker Desktop未能在规定时间内启动，请手动启动Docker Desktop后重试"
            exit 1
          fi
          sleep 1
        done
      else
        brew install $1
      fi
    else
      # Linux安装
      if [ "$1" = "docker" ]; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
      elif [ "$1" = "docker-compose" ]; then
        curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
      else
        apt-get update
        apt-get install -y $1
      fi
    fi
    
    print_message "$1 安装完成"
  fi
  
  # 检查Docker是否运行（特别是在macOS上）
  if [ "$1" = "docker" ] && [ "$IS_MACOS" = true ]; then
    if ! docker info &>/dev/null; then
      print_warning "Docker似乎未运行，尝试启动Docker Desktop..."
      open -a Docker
      print_message "正在等待Docker Desktop启动..."
      
      # 等待Docker启动
      TIMEOUT=60
      for i in $(seq 1 $TIMEOUT); do
        if docker info &>/dev/null; then
          print_message "Docker Desktop已成功启动"
          break
        fi
        if [ $i -eq $TIMEOUT ]; then
          print_error "Docker Desktop未能在规定时间内启动，请手动启动Docker Desktop后重试"
          exit 1
        fi
        sleep 1
      done
    fi
  fi
}

# 检查并安装必要的工具
check_command docker
check_command curl

# 在macOS上，docker-compose通常随Docker Desktop一起安装
if [ "$IS_MACOS" = false ]; then
  check_command docker-compose
fi

# 欢迎信息
echo "================================================"
echo "      Image Generation Worker一键部署脚本       "
echo "================================================"

# 创建部署目录
if [ "$IS_MACOS" = true ]; then
  DEPLOY_DIR="$HOME/img_gen_worker"
else
  DEPLOY_DIR="/opt/img_gen_worker"
fi

if [ ! -d "$DEPLOY_DIR" ]; then
  print_message "创建部署目录 $DEPLOY_DIR..."
  mkdir -p $DEPLOY_DIR
else
  print_message "已存在部署目录 $DEPLOY_DIR"
fi

cd $DEPLOY_DIR

# 配置Docker登录GitHub Container Registry
print_message "配置Docker登录GitHub Container Registry..."
if [ "$IS_MACOS" = true ]; then
  # macOS处理
  print_message "在macOS上配置Docker凭据..."
  # 创建临时Docker配置文件
  DOCKER_CONFIG_DIR="$HOME/.docker-tmp"
  mkdir -p "$DOCKER_CONFIG_DIR"
  
  # 使用配置文件登录，避免使用macOS keychain
  DOCKER_CONFIG="$DOCKER_CONFIG_DIR" docker login ghcr.io -u "$GITHUB_USERNAME" --password-stdin <<< "$GITHUB_TOKEN"
  if [ $? -ne 0 ]; then
    print_warning "自动登录失败，请手动执行以下命令登录Docker："
    print_warning "docker login ghcr.io -u $GITHUB_USERNAME -p $GITHUB_TOKEN"
    print_warning "如果上述命令仍然失败，请尝试手动进入Docker Desktop设置登录。"
    
    # 询问用户是否已成功登录
    read -p "您是否已成功登录Docker? (y/n): " docker_login_success
    if [ "$docker_login_success" != "y" ]; then
      print_error "Docker登录失败，部署中止"
      exit 1
    fi
  else
    print_message "Docker登录成功"
  fi
else
  # Linux处理
  echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_USERNAME --password-stdin
  if [ $? -ne 0 ]; then
    print_error "Docker登录失败，请检查GitHub用户名和个人访问令牌"
    exit 1
  fi
  print_message "Docker登录成功"
fi

# 创建docker-compose.yml文件
print_message "创建docker-compose.yml文件..."
cat > docker-compose.yml << EOF
version: '3'

services:
  img-gen-worker:
    image: ${DOCKER_IMAGE}
    container_name: img-gen-worker
    restart: always
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
EOF

# 创建.env文件
print_message "创建.env文件..."
cat > .env << EOF
# 多API配置
EOF

# 添加OpenAI API配置
for i in $(seq 0 $((API_COUNT-1))); do
  API_KEY_VAR="OPENAI_API_KEY_$i"
  BASE_URL_VAR="OPENAI_BASE_URL_$i"
  MODEL_VAR="OPENAI_MODEL_$i"
  PRIORITY_VAR="API_PRIORITY_$i"
  PROVIDER_VAR="API_PROVIDER_$i"
  
  if [ -n "${!API_KEY_VAR}" ]; then
    echo "OPENAI_API_KEY_$i=${!API_KEY_VAR}" >> .env
    echo "OPENAI_BASE_URL_$i=${!BASE_URL_VAR}" >> .env
    echo "OPENAI_MODEL_$i=${!MODEL_VAR}" >> .env
    echo "API_PRIORITY_$i=${!PRIORITY_VAR}" >> .env
    echo "API_PROVIDER_$i=${!PROVIDER_VAR}" >> .env
    echo "" >> .env
  fi
done

# 添加其他配置
cat >> .env << EOF
# Database配置
DATABASE_URL=${DATABASE_URL}
DIRECT_URL=${DIRECT_URL}

# 线程配置
PAID_THREADS=${PAID_THREADS}
FREE_THREADS=${FREE_THREADS}

# Worker配置
POLLING_INTERVAL=${POLLING_INTERVAL}
TIMEOUT_INTERVAL=${TIMEOUT_INTERVAL}
BATCH_SIZE=${BATCH_SIZE}

# UE S3配置
UE_S3_ACCESS_KEY=${UE_S3_ACCESS_KEY}
UE_S3_SECRET_KEY=${UE_S3_SECRET_KEY}
UE_S3_SESSION_TOKEN=${UE_S3_SESSION_TOKEN}
UE_S3_ENDPOINT=${UE_S3_ENDPOINT}
UE_S3_ACCOUNT_ID=${UE_S3_ACCOUNT_ID}
UE_S3_REGION=${UE_S3_REGION}
UE_S3_BUCKET=${UE_S3_BUCKET}
UE_S3_PUBLIC_PATH=${UE_S3_PUBLIC_PATH}

# API URL配置
WEB_API_URL=${WEB_API_URL}

# Lark Webhook配置
LARK_WEBHOOK_URL=${LARK_WEBHOOK_URL}

# GitHub认证信息
GITHUB_USERNAME=${GITHUB_USERNAME}
GITHUB_TOKEN=${GITHUB_TOKEN}
EOF

# 创建日志目录
mkdir -p logs

# 如果配置了域名且非macOS，则设置Caddy
if [ ! -z "$DOMAIN" ] && [ "$IS_MACOS" = false ]; then
  # 检查Caddy是否已安装
  if ! command -v caddy &> /dev/null; then
    print_warning "Caddy未安装，正在安装..."
    
    # 添加Caddy官方仓库
    apt-get update
    apt-get install -y debian-keyring debian-archive-keyring apt-transport-https
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list
    
    # 安装Caddy
    apt-get update
    apt-get install -y caddy
    
    print_message "Caddy安装完成"
  fi

  # 配置Caddy
  print_message "配置Caddy..."
  CADDY_CONFIG_PATH="/etc/caddy/Caddyfile"
  
  # 创建Caddy配置
  CADDY_CONFIG="
$DOMAIN {
    reverse_proxy localhost:8000
    tls $ADMIN_EMAIL
}
"

  # 检查是否已存在相同的配置
  if grep -q "$DOMAIN" "$CADDY_CONFIG_PATH" 2>/dev/null; then
    print_warning "检测到已存在域名 $DOMAIN 的Caddy配置，正在更新..."
    # 使用sed删除已存在的配置块
    sed -i "/^$DOMAIN {/,/^}/d" "$CADDY_CONFIG_PATH"
  fi

  # 添加新的配置
  echo "$CADDY_CONFIG" >> "$CADDY_CONFIG_PATH"
  print_message "Caddy配置已更新"

  # 验证Caddy配置
  print_message "验证Caddy配置..."
  if caddy validate --config "$CADDY_CONFIG_PATH" 2>/dev/null; then
    print_message "Caddy配置验证通过"
  else
    print_error "Caddy配置验证失败，请手动检查配置"
    print_error "配置路径: $CADDY_CONFIG_PATH"
    exit 1
  fi

  # 重启Caddy服务
  print_message "重启Caddy服务..."
  if systemctl is-active --quiet caddy; then
    systemctl reload caddy
    print_message "Caddy服务已重新加载"
  else
    systemctl start caddy
    print_message "Caddy服务已启动"
  fi
elif [ ! -z "$DOMAIN" ] && [ "$IS_MACOS" = true ]; then
  print_warning "macOS上不支持Caddy自动配置，您需要手动设置反向代理"
  print_warning "推荐使用Caddy、Nginx或本地开发工具如Ngrok"
fi

# 拉取Docker镜像
print_message "拉取Docker镜像..."
docker pull $DOCKER_IMAGE

# 启动服务
print_message "启动服务..."
if [ "$IS_MACOS" = true ]; then
  # macOS上使用docker compose命令
  docker compose down || true
  docker compose up -d
else
  # Linux上使用docker-compose命令
  docker-compose down || true
  docker-compose up -d
fi

# 检查服务状态
print_message "检查服务状态..."
sleep 5
if [ "$IS_MACOS" = true ]; then
  if docker compose ps | grep -q "Up"; then
    print_message "Image Generation Worker已成功启动!"
  else
    print_error "服务启动失败，请检查日志"
    docker compose logs
    exit 1
  fi
else
  if docker-compose ps | grep -q "Up"; then
    print_message "Image Generation Worker已成功启动!"
  else
    print_error "服务启动失败，请检查日志"
    docker-compose logs
    exit 1
  fi
fi

# 设置自动更新
print_message "设置自动更新..."
cat > update.sh << EOF
#!/bin/bash
cd \$(dirname \$0)
source .env

# 检测系统类型
if [ "\$(uname)" = "Darwin" ]; then
  # macOS处理
  # 创建临时Docker配置文件
  DOCKER_CONFIG_DIR="\$HOME/.docker-tmp"
  mkdir -p "\$DOCKER_CONFIG_DIR"
  
  # 使用配置文件登录，避免使用macOS keychain
  echo "\$GITHUB_TOKEN" | DOCKER_CONFIG="\$DOCKER_CONFIG_DIR" docker login ghcr.io -u "\$GITHUB_USERNAME" --password-stdin
  if [ \$? -ne 0 ]; then
    echo "警告: Docker登录失败，尝试使用直接方式登录"
    echo "\$GITHUB_TOKEN" | docker login ghcr.io -u "\$GITHUB_USERNAME" --password-stdin
  fi
  
  # 拉取更新的镜像
  docker pull $DOCKER_IMAGE
  
  # 重启服务
  docker compose down
  docker compose up -d
else
  # Linux处理
  echo "\$GITHUB_TOKEN" | docker login ghcr.io -u "\$GITHUB_USERNAME" --password-stdin
  docker pull $DOCKER_IMAGE
  docker-compose down
  docker-compose up -d
fi
EOF

chmod +x update.sh

# 设置定时任务自动更新
if [ "$IS_MACOS" = false ]; then
  print_message "设置定时任务每天凌晨2点自动更新..."
  (crontab -l 2>/dev/null || echo "") | grep -v "$DEPLOY_DIR/update.sh" | { cat; echo "0 2 * * * $DEPLOY_DIR/update.sh >> $DEPLOY_DIR/logs/update.log 2>&1"; } | crontab -
else
  print_message "在macOS上，请使用launchd设置自动更新，或手动运行update.sh脚本"
  print_message "您可以运行以下命令创建一个定时任务："
  print_message "1. 创建launchd plist文件:"
  print_message "mkdir -p ~/Library/LaunchAgents"
  
  # 创建plist文件示例
  cat > ~/Library/LaunchAgents/com.img-gen-worker-update.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.img-gen-worker-update</string>
    <key>ProgramArguments</key>
    <array>
        <string>${DEPLOY_DIR}/update.sh</string>
    </array>
    <key>StartCalendarInterval</key>
    <dict>
        <key>Hour</key>
        <integer>2</integer>
        <key>Minute</key>
        <integer>0</integer>
    </dict>
    <key>StandardOutPath</key>
    <string>${DEPLOY_DIR}/logs/update.log</string>
    <key>StandardErrorPath</key>
    <string>${DEPLOY_DIR}/logs/update.log</string>
</dict>
</plist>
EOF
  
  print_message "2. 加载启动项:"
  print_message "launchctl load ~/Library/LaunchAgents/com.img-gen-worker-update.plist"
fi

print_message "部署完成! Image Generation Worker已配置完成"
if [ ! -z "$DOMAIN" ]; then
  print_message "您可以通过以下地址访问: https://$DOMAIN"
else
  print_message "Worker已在后台运行，无需Web访问"
fi
echo "================================================" 