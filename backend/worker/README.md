# Image Generation Worker

This worker service polls the database for pending image generation tasks and processes them asynchronously.

## Features

- Polls database for pending image generation tasks
- Processes tasks in batches
- Updates database with task status and results
- Integrates with the image generation service to process images
- Supports multiple API configurations with different priorities
- Allows multiple concurrent requests to each API with configurable limits
- Prioritizes APIs with most available capacity for efficient request distribution

## Getting Started

### Prerequisites

- Node.js 16+ 
- Access to a PostgreSQL database with the appropriate schema
- Access to the image generation service

### Environment Variables

- `DATABASE_URL`: The connection URL for the PostgreSQL database
- `POLLING_INTERVAL`: Time in milliseconds between database polls (default: 5000)
- `BATCH_SIZE`: Number of tasks to process in each batch (default: 10)
- `PAID_THREADS`: Number of simultaneous threads for paid users (default: 2)
- `FREE_THREADS`: Number of simultaneous threads for free users (default: 1)
- `DEFAULT_PAID_CONCURRENCY`: Default number of concurrent requests for paid APIs (default: 3)
- `DEFAULT_FREE_CONCURRENCY`: Default number of concurrent requests for free APIs (default: 1)
- `API_COUNT`: Number of API configurations (default: 1)
- `API_CONCURRENCY_X`: Concurrency limit for API index X (default: 1)

### API Configuration

Multiple APIs can be configured with different concurrency limits:

```
# API Configuration Example
API_COUNT=3

# High priority API with high concurrency
OPENAI_API_KEY_0=your_key_here
OPENAI_BASE_URL_0=https://api.example.com/v1
OPENAI_MODEL_0=gpt-4o-image
API_PRIORITY_0=high
API_PROVIDER_0=openai
API_CONCURRENCY_0=3  # Allow 3 concurrent requests to this API

# Medium priority API 
OPENAI_API_KEY_1=your_key_here
OPENAI_BASE_URL_1=https://api.example2.com/v1
OPENAI_MODEL_1=gpt-4o-image
API_PRIORITY_1=high
API_PROVIDER_1=openai
API_CONCURRENCY_1=2  # Allow 2 concurrent requests

# Low priority API for free users
OPENAI_API_KEY_2=your_key_here
OPENAI_BASE_URL_2=https://api.example3.com/v1
OPENAI_MODEL_2=gpt-4o-image
API_PRIORITY_2=low
API_PROVIDER_2=openai
API_CONCURRENCY_2=1  # Only 1 concurrent request
```

### Running Locally

```bash
# Install dependencies
pnpm install

# Generate Prisma client
pnpm run pg:gen

# Start the worker in development mode
pnpm run dev

# Or build and start the worker
pnpm run build
pnpm run start
```

### Running with Docker

This project includes npm/pnpm scripts for Docker operations. Use the following commands:

#### Basic Docker Commands

```bash
# Build Docker image
pnpm run docker:build

# Deploy/Update Docker container (standard deploy)
pnpm run docker:deploy

# Deploy with specific options
pnpm run docker:deploy:skip-build  # Skip image building
pnpm run docker:deploy:no-logs     # Don't show logs after deploy

# Custom deployment with arguments (pass -- before arguments)
pnpm run docker:deploy:custom -- --port=4000 --env=.env.production --skip-build

# View container logs
pnpm run docker:logs

# Start/Stop/Restart container
pnpm run docker:start
pnpm run docker:stop
pnpm run docker:restart

# Check container status
pnpm run docker:status

# Open shell in the container
pnpm run docker:shell

# Prune unused Docker images
pnpm run docker:prune
```

#### MacOS Local Deployment

The project includes a macOS-specific deployment script `deploy-from-macos-build.sh` for local development and testing:

```bash
# Run with default settings
./deploy-from-macos-build.sh

# Show help information
./deploy-from-macos-build.sh --help

# Run with custom settings
./deploy-from-macos-build.sh --name my-worker --port 8080 --skip-logs

# Skip image build (use existing image)
./deploy-from-macos-build.sh --skip-build
```

The macOS deployment script supports the following options:

| Option | Description | Default |
|--------|-------------|---------|
| `-n, --name NAME` | Container name | `img-gen-worker` |
| `-i, --image NAME` | Image name | `img-gen-worker:latest` |
| `-p, --port PORT` | Port to expose | `3000` |
| `--network NETWORK` | Docker network | `bridge` |
| `--skip-build` | Skip image building | `false` |
| `--skip-logs` | Don't show container logs | `false` |
| `-h, --help` | Show help information | |

The script includes automatic fixes for common issues:

- Automatically modifies Dockerfile to use `--no-frozen-lockfile` instead of `--frozen-lockfile` to prevent pnpm lockfile version mismatch errors
- Sets required Prisma engine environment variables (`PRISMA_CLIENT_ENGINE_TYPE=binary`, etc.) to prevent Prisma initialization errors
- Automatically creates necessary data directories
- Validates environment file existence and creates from template if needed
- Detects and starts Docker Desktop if it's not running

#### Docker Compose Commands

```bash
# Start services with Docker Compose
pnpm run docker:compose:up

# Stop services
pnpm run docker:compose:down

# View logs
pnpm run docker:compose:logs

# Restart services
pnpm run docker:compose:restart
```

#### Deployment Script

The project includes a deployment script (`scripts/quick-update.sh`) which is used by `pnpm run docker:deploy`. This script:

1. Builds a new Docker image
2. Stops the existing container (if any)
3. Runs a new container
4. Removes the old container

The script handles error checking and persistence through volume mapping.

## Available Deployment Options

The deployment script supports the following options:

| Option | Description | Default |
|--------|-------------|---------|
| `--container=NAME` | Container name | `img-gen-worker` |
| `--image=NAME` | Image name | `img-gen-worker:latest` |
| `--env=FILE` | Environment file | `.env` |
| `--port=PORT` | Port to expose | `3000` |
| `--network=NAME` | Docker network name | `img-gen-network` |
| `--skip-build` | Skip image building | `false` |
| `--no-logs` | Don't show container logs | `false` |
| `--data-dir=DIR` | Data directory path | `./data` |
| `--logs-dir=DIR` | Logs directory path | `./logs` |
| `--help` | Show help information | |

Example of custom deployment:
```bash
# Use specific environment file and custom port
pnpm run docker:deploy:custom -- --env=.env.staging --port=4001

# Use production settings with custom container name
pnpm run docker:deploy:custom -- --env=.env.production --container=img-gen-prod
```

## How It Works

1. The worker polls the database at regular intervals to find pending tasks of type 'IMG_GEN'
2. For each pending task, it:
   - Updates the task status to 'PROCESSING'
   - Extracts the source image and model from the task metadata
   - Selects the most available API based on concurrency limits
   - Sends the image to the image generation service for processing
   - Updates the task with the generated image and marks it as 'SUCCEED'
   - If any step fails, marks the task as 'FAILED' with an error message

## Integration with NextJS

This worker is designed to work with the NextJS application's database. The NextJS app:

1. Creates tasks in the database with status 'PENDING'
2. Polls the database for task status updates
3. Displays the generated image to the user when the task is complete 