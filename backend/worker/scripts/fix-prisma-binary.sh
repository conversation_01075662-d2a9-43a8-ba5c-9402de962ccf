#!/bin/bash

# <PERSON><PERSON>t to install and configure OpenSSL for Prisma in Docker
echo "Installing OpenSSL for Prisma Binary Engines..."

# Check if we're on Debian-based system
if [ -f /etc/debian_version ]; then
    echo "Debian-based system detected, installing dependencies..."
    apt-get update
    apt-get install -y openssl ca-certificates

    # Create necessary links
    if [ ! -e /lib/libssl.so.1.1 ] && [ -e /usr/lib/x86_64-linux-gnu/libssl.so.1.1 ]; then
        ln -s /usr/lib/x86_64-linux-gnu/libssl.so.1.1 /lib/libssl.so.1.1
    fi
    
    if [ ! -e /lib/libcrypto.so.1.1 ] && [ -e /usr/lib/x86_64-linux-gnu/libcrypto.so.1.1 ]; then
        ln -s /usr/lib/x86_64-linux-gnu/libcrypto.so.1.1 /lib/libcrypto.so.1.1
    fi
fi

# Check if we're on Alpine
if [ -f /etc/alpine-release ]; then
    echo "Alpine-based system detected, installing dependencies..."
    apk add --no-cache openssl libc6-compat
fi

# Set environment variables for Prisma
export PRISMA_CLIENT_ENGINE_TYPE=binary
export PRISMA_SCHEMA_ENGINE_TYPE=binary
export PRISMA_QUERY_ENGINE_TYPE=binary

echo "OpenSSL setup for Prisma completed!"