#!/bin/bash

# 快速更新脚本 - img_gen_worker
# 按顺序执行：1.编译docker镜像，2.停止旧的container，3.运行新的container，4.删除旧的container

# 默认配置
CONTAINER_NAME="img-gen-worker"
IMAGE_NAME="img-gen-worker:latest"
ENV_FILE=".env"
NETWORK_NAME="img-gen-network"
PORT=3000
SKIP_BUILD=false
SHOW_LOGS=true
DATA_DIR="./data"
LOGS_DIR="./logs"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case "$1" in
    --container=*)
      CONTAINER_NAME="${1#*=}"
      shift
      ;;
    --image=*)
      IMAGE_NAME="${1#*=}"
      shift
      ;;
    --env=*)
      ENV_FILE="${1#*=}"
      shift
      ;;
    --port=*)
      PORT="${1#*=}"
      shift
      ;;
    --network=*)
      NETWORK_NAME="${1#*=}"
      shift
      ;;
    --skip-build)
      SKIP_BUILD=true
      shift
      ;;
    --no-logs)
      SHOW_LOGS=false
      shift
      ;;
    --data-dir=*)
      DATA_DIR="${1#*=}"
      shift
      ;;
    --logs-dir=*)
      LOGS_DIR="${1#*=}"
      shift
      ;;
    --help)
      echo "用法: $0 [选项]"
      echo ""
      echo "选项:"
      echo "  --container=NAME    设置容器名称 (默认: $CONTAINER_NAME)"
      echo "  --image=NAME        设置镜像名称 (默认: $IMAGE_NAME)"
      echo "  --env=FILE          设置环境文件 (默认: $ENV_FILE)"
      echo "  --port=PORT         设置端口 (默认: $PORT)"
      echo "  --network=NAME      设置网络名称 (默认: $NETWORK_NAME)"
      echo "  --skip-build        跳过镜像构建"
      echo "  --no-logs           不显示容器日志"
      echo "  --data-dir=DIR      设置数据目录 (默认: $DATA_DIR)"
      echo "  --logs-dir=DIR      设置日志目录 (默认: $LOGS_DIR)"
      echo "  --help              显示此帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 --help 查看帮助"
      exit 1
      ;;
  esac
done

# 显示头部信息
echo "========================================"
echo "   img_gen_worker 快速更新脚本"
echo "========================================"
echo "容器名称: $CONTAINER_NAME"
echo "镜像名称: $IMAGE_NAME"
echo "端口映射: $PORT:$PORT"
echo "环境文件: $ENV_FILE"
echo "网络名称: $NETWORK_NAME"
echo "数据目录: $DATA_DIR"
echo "日志目录: $LOGS_DIR"
echo "跳过构建: $SKIP_BUILD"
echo "显示日志: $SHOW_LOGS"
echo "========================================"

# 检查 Docker 是否已安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker。"
    exit 1
fi

# 检查环境文件是否存在
if [ ! -f "$ENV_FILE" ]; then
    echo "错误: 环境文件 '$ENV_FILE' 不存在。"
    exit 1
fi

# 1. 编译 Docker 镜像
if [ "$SKIP_BUILD" = false ]; then
    echo "步骤 1: 编译 Docker 镜像 '$IMAGE_NAME'..."
    docker build -t $IMAGE_NAME .
    if [ $? -ne 0 ]; then
        echo "错误: Docker 镜像编译失败。"
        exit 1
    fi
    echo "镜像编译成功。"
else
    echo "步骤 1: 跳过 Docker 镜像构建。"
    
    # 检查镜像是否存在
    if ! docker image inspect $IMAGE_NAME &> /dev/null; then
        echo "警告: 镜像 '$IMAGE_NAME' 不存在，将强制构建。"
        docker build -t $IMAGE_NAME .
        if [ $? -ne 0 ]; then
            echo "错误: Docker 镜像编译失败。"
            exit 1
        fi
    fi
fi

# 检查容器是否存在
CONTAINER_EXISTS=$(docker ps -a -q -f name="^/${CONTAINER_NAME}$")
if [ -n "$CONTAINER_EXISTS" ]; then
    # 2. 停止旧的容器
    echo "步骤 2: 停止旧的容器 '$CONTAINER_NAME'..."
    docker stop $CONTAINER_NAME
    if [ $? -ne 0 ]; then
        echo "警告: 停止容器失败，可能容器已经停止。"
    else
        echo "容器已停止。"
    fi
else
    echo "容器 '$CONTAINER_NAME' 不存在，跳过停止步骤。"
    
    # 创建 Docker 网络（如果不存在）
    if ! docker network inspect $NETWORK_NAME &> /dev/null; then
        echo "创建 Docker 网络 '$NETWORK_NAME'..."
        docker network create $NETWORK_NAME
        echo "网络已创建。"
    fi
fi

# 创建数据目录（如果不存在）
mkdir -p "$DATA_DIR" "$LOGS_DIR"
if [ $? -ne 0 ]; then
    echo "警告: 无法创建数据目录。"
fi

# 3. 运行新的容器
echo "步骤 3: 运行新的容器..."
docker run -d \
    --name $CONTAINER_NAME \
    --network $NETWORK_NAME \
    -p $PORT:$PORT \
    --env-file $ENV_FILE \
    -v "$DATA_DIR":/app/data \
    -v "$LOGS_DIR":/app/logs \
    --restart unless-stopped \
    $IMAGE_NAME

if [ $? -ne 0 ]; then
    echo "错误: 启动新容器失败。"
    exit 1
fi
echo "新容器已启动。"

# 4. 删除旧的容器（如果存在且不是刚刚创建的）
if [ -n "$CONTAINER_EXISTS" ]; then
    echo "步骤 4: 删除旧的容器..."
    docker rm $CONTAINER_NAME
    if [ $? -ne 0 ]; then
        echo "警告: 删除旧容器失败。可能需要手动删除。"
    else
        echo "旧容器已删除。"
    fi
else
    echo "没有旧容器需要删除。"
fi

# 显示容器日志
echo "========================================"
echo "容器 '$CONTAINER_NAME' 已成功更新！"
echo "镜像: $IMAGE_NAME"
echo "端口映射: $PORT:$PORT"
echo ""
echo "查看日志:"
echo "  docker logs -f $CONTAINER_NAME"
echo ""
echo "停止容器:"
echo "  docker stop $CONTAINER_NAME"
echo ""
echo "重启容器:"
echo "  docker restart $CONTAINER_NAME"
echo ""
echo "或使用npm/pnpm脚本:"
echo "  pnpm run docker:logs"
echo "  pnpm run docker:stop"
echo "  pnpm run docker:restart"
echo "========================================"

# 显示容器日志
if [ "$SHOW_LOGS" = true ]; then
    echo "容器日志（前10行）:"
    sleep 2
    docker logs $CONTAINER_NAME | head -n 10
    echo "..."
    echo "查看完整日志，请运行: docker logs -f $CONTAINER_NAME"
    echo "或使用: pnpm run docker:logs"
fi 