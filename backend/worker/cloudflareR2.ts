import { v4 as uuidv4 } from 'uuid';
import { createPutSingedUrl } from './s34r2';

export async function uploadFileToStorage(file: File): Promise<string> {
    console.log(`[uploadFileToStorage] Starting upload for file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);
    
    try {
      // Generate a unique filename
      const filename = `${uuidv4()}-${file.name.replace(/\s+/g, '-')}`;
      
      // Add retries for pre-signed URL generation
      let retries = 3;
      let uploadUrl: string;
      while (retries > 0) {
        try {
          uploadUrl = await createPutSingedUrl(filename);
          break;
        } catch (urlError) {
          retries--;
          if (retries === 0) throw urlError;
          console.log(`[uploadFileToStorage] Retrying pre-signed URL generation. Attempts left: ${retries}`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      // Convert File to ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      
      // Add timeout and retry logic for upload
      const uploadWithRetry = async (retries: number, delay: number): Promise<Response> => {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout
          
          const response = await fetch(uploadUrl, {
            method: 'PUT',
            body: arrayBuffer,
            headers: {
              'Content-Type': file.type,
            },
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          return response;
        } catch (error) {
          if (retries === 0) throw error;
          console.log(`[uploadFileToStorage] Upload failed, retrying... (${retries} attempts left)`);
          await new Promise(resolve => setTimeout(resolve, delay));
          return uploadWithRetry(retries - 1, delay * 1.5);
        }
      };
      
      const uploadResponse = await uploadWithRetry(2, 1000);
      
      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload file: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }
      
      // Return the public URL
      const fileUrl = `${process.env.UE_S3_PUBLIC_PATH}/${process.env.UE_S3_BUCKET}/${filename}`;
      console.log(`[uploadFileToStorage] File uploaded successfully. URL: ${fileUrl}`);
      return fileUrl;
    } catch (error) {
      console.error(`[uploadFileToStorage] Error uploading file:`, error);
      throw new Error(`Storage upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

export async function uploadBufferToStorage(buffer: Buffer, fileName: string, contentType: string): Promise<string> {
    console.log(`[uploadBufferToStorage] Starting upload for file: ${fileName}, size: ${buffer.length} bytes, type: ${contentType}`);
    
    try {
      // Generate a unique filename
      const filename = `${uuidv4()}-${fileName.replace(/\s+/g, '-')}`;
      
      // Add retries for pre-signed URL generation
      let retries = 3;
      let uploadUrl: string;
      while (retries > 0) {
        try {
          uploadUrl = await createPutSingedUrl(filename);
          break;
        } catch (urlError) {
          retries--;
          if (retries === 0) throw urlError;
          console.log(`[uploadBufferToStorage] Retrying pre-signed URL generation. Attempts left: ${retries}`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      // Add timeout and retry logic for upload
      const uploadWithRetry = async (retries: number, delay: number): Promise<Response> => {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout
          
          const response = await fetch(uploadUrl, {
            method: 'PUT',
            body: buffer,
            headers: {
              'Content-Type': contentType,
            },
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          return response;
        } catch (error) {
          if (retries === 0) throw error;
          console.log(`[uploadBufferToStorage] Upload failed, retrying... (${retries} attempts left)`);
          await new Promise(resolve => setTimeout(resolve, delay));
          return uploadWithRetry(retries - 1, delay * 1.5);
        }
      };
      
      const uploadResponse = await uploadWithRetry(2, 1000);
      
      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload file: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }
      
      // Return the public URL
      const fileUrl = `${process.env.UE_S3_PUBLIC_PATH}/${process.env.UE_S3_BUCKET}/${filename}`;
      console.log(`[uploadBufferToStorage] File uploaded successfully. URL: ${fileUrl}`);
      return fileUrl;
    } catch (error) {
      console.error(`[uploadBufferToStorage] Error uploading file:`, error);
      throw new Error(`Storage upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}