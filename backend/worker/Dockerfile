FROM node:18-bullseye as builder

WORKDIR /app

# Install dependencies
RUN apt-get update && \
    apt-get install -y \
    openssl \
    ca-certificates \
    python3 \
    make \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm
RUN npm install -g pnpm

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --no-frozen-lockfile

# Copy Prisma schema and scripts
COPY prisma ./prisma/
COPY scripts ./scripts/

# Make script executable
RUN chmod +x ./scripts/fix-prisma-binary.sh

# Run OpenSSL fix script for Prisma
RUN ./scripts/fix-prisma-binary.sh

# Generate Prisma client with binary engine
ENV PRISMA_CLIENT_ENGINE_TYPE=library
ENV PRISMA_SCHEMA_ENGINE_TYPE=library
ENV PRISMA_QUERY_ENGINE_TYPE=library
RUN npx prisma generate

# Copy source code
COPY . .

# Build TypeScript
RUN pnpm run build

# Second stage for a smaller image
FROM node:18-bullseye-slim

WORKDIR /app

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    openssl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm
RUN npm install -g pnpm

# Copy OpenSSL fix script
COPY --from=builder /app/scripts/fix-prisma-binary.sh ./scripts/
RUN chmod +x ./scripts/fix-prisma-binary.sh && ./scripts/fix-prisma-binary.sh

# Copy built app
COPY --from=builder /app/package.json /app/pnpm-lock.yaml ./
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma

# Set environment variables
ENV NODE_ENV=production
ENV PRISMA_CLIENT_ENGINE_TYPE=library
ENV PRISMA_SCHEMA_ENGINE_TYPE=library
ENV PRISMA_QUERY_ENGINE_TYPE=library

# Run the worker
CMD ["node", "dist/worker.js"]