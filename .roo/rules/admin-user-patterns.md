---
description: 
globs: 
alwaysApply: false
---
# Admin & User Management Patterns

## Admin Panel Structure

### Admin Routes
The admin interface is located in [nextjs/app/[locale]/(admin)/admin/](mdc:nextjs/app/[locale]/(admin)/admin/) with the following key sections:

- **[affiliates/](mdc:nextjs/app/[locale]/(admin)/admin/affiliates/)** - Affiliate program management
- **[ai-tasks/](mdc:nextjs/app/[locale]/(admin)/admin/ai-tasks/)** - AI task monitoring and management
- **[api-keys/](mdc:nextjs/app/[locale]/(admin)/admin/api-keys/)** - API key management for users
- **[emails/](mdc:nextjs/app/[locale]/(admin)/admin/emails/)** - Email template and log management
- **[items/](mdc:nextjs/app/[locale]/(admin)/admin/items/)** - Content item administration
- **[paid-orders/](mdc:nextjs/app/[locale]/(admin)/admin/paid-orders/)** - Payment and order management
- **[posts/](mdc:nextjs/app/[locale]/(admin)/admin/posts/)** - Blog/content post management
- **[users/](mdc:nextjs/app/[locale]/(admin)/admin/users/)** - User account management
- **[settings/](mdc:nextjs/app/[locale]/(admin)/admin/settings/)** - System configuration

### Admin API Endpoints
Admin APIs are protected and located in [nextjs/app/api/admin/](mdc:mcp:nextjs/app/api/admin):

```typescript
// Admin API pattern with role checking
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session || !isAdmin(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Admin-only logic here
}
```

## User Console Structure

### User Dashboard Routes
User-facing console in [nextjs/app/[locale]/(default)/(console)/](mcp:nextjs/app/[locale]/(default)/(console)/):

- **[api-keys/](mdc:nextjs/app/[locale]/(default)/(console)/api-keys/)** - User's API key management
- **[invite/](mdc:nextjs/app/[locale]/(default)/(console)/invite/)** - Invitation system
- **[my-credits/](mdc:nextjs/app/[locale]/(default)/(console)/my-credits/)** - Credit balance management
- **[my-favorites/](mdc:nextjs/app/[locale]/(default)/(console)/my-favorites/)** - User favorites
- **[my-orders/](mdc:nextjs/app/[locale]/(default)/(console)/my-orders/)** - Order history
- **[my-submit/](mdc:nextjs/app/[locale]/(default)/(console)/my-submit/)** - User submissions
- **[my-tasks/](mdc:nextjs/app/[locale]/(default)/(console)/my-tasks/)** - User's AI tasks

## Authentication Patterns

### NextAuth Configuration
Authentication setup in [nextjs/auth/](mdc:nextjs/auth) with:

```typescript
// Auth configuration pattern
export const authOptions: NextAuthOptions = {
  providers: [
    // OAuth providers
  ],
  callbacks: {
    async session({ session, token }) {
      // Enhance session with user data
      return session;
    },
    async jwt({ token, user }) {
      // JWT token handling
      return token;
    }
  },
  pages: {
    signIn: '/auth/signin',
    // Custom auth pages
  }
};
```

### Protected Route Patterns
```typescript
// Page-level protection
export default async function ProtectedPage() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect('/auth/signin');
  }
  
  return <PageContent />;
}

// API route protection
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Protected logic
}
```

## Component Patterns

### Admin Components
Admin-specific components in [nextjs/components/admin/](mdc:mcp:nextjs/components/admin):

```typescript
// Admin table component pattern
interface AdminTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
}

export function AdminTable<T>({ data, columns, onEdit, onDelete }: AdminTableProps<T>) {
  // Table implementation with admin actions
}
```

### Console Components
User console components in [nextjs/components/console/](mdc:mcp:nextjs/components/console):

- **[sidebar/](mdc:nextjs/components/console/sidebar)** - Console navigation
- **[slots/](mdc:nextjs/components/console/slots)** - Reusable console UI slots

### Dashboard Components
Dashboard-specific components in [nextjs/components/dashboard/](mdc:mcp:nextjs/components/dashboard):

- **[header/](mdc:nextjs/components/dashboard/header)** - Dashboard header with user info
- **[sidebar/](mdc:nextjs/components/dashboard/sidebar)** - Dashboard navigation

## User Management Patterns

### User Roles & Permissions
```typescript
// User role enum
enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

// Permission checking
function hasPermission(user: User, permission: string): boolean {
  return user.role === UserRole.ADMIN || user.permissions.includes(permission);
}
```

### User Context Pattern
```typescript
// User context for client-side state
interface UserContextType {
  user: User | null;
  loading: boolean;
  credits: number;
  orders: Order[];
  favorites: string[];
  updateCredits: (amount: number) => void;
}

export const UserContext = createContext<UserContextType | undefined>(undefined);
```

## API Key Management

### API Key Generation
```typescript
// API key creation pattern
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  const { name, permissions } = await request.json();
  
  const api_key = await createAPIKey({
    userId: session.user.id,
    name,
    permissions,
    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
  });
  
  return NextResponse.json({ api_key: api_key.key });
}
```

### API Key Validation
```typescript
// Middleware for API key validation
export async function validateAPIKey(request: Request): Promise<User | null> {
  const api_key = request.headers.get('X-API-Key');
  
  if (!api_key) return null;
  
  const key = await findAPIKey(api_key);
  return key && !key.revoked && key.expiresAt > new Date() ? key.user : null;
}
```

## Email Management

### Email Templates
Admin email management in [nextjs/app/[locale]/(admin)/admin/emails/](mcp:nextjs/app/[locale]/(admin)/admin/emails/):

```typescript
// Email template pattern
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables: string[];
}

// Email sending pattern
async function sendTemplatedEmail(templateId: string, to: string, variables: Record<string, string>) {
  const template = await getEmailTemplate(templateId);
  const rendered = renderTemplate(template, variables);
  
  return await sendEmail({
    to,
    subject: rendered.subject,
    html: rendered.body
  });
}
```

## Submission Management

### Content Submission Flow
User submission handling in [nextjs/app/[locale]/(default)/submit/](mcp:nextjs/app/[locale]/(default)/submit/):

```typescript
// Submission status enum
enum SubmissionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PROCESSED = 'processed'
}

// Admin submission review
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const { action, reason } = await request.json();
  
  await updateSubmissionStatus(params.id, action, reason);
  
  return NextResponse.json({ success: true });
}
```
