# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LinkTrackPro is a comprehensive link tracking and analytics platform with a three-tier architecture:

1. **Next.js Frontend** (`nextjs/`): Full-stack web application with admin panel and user dashboard
2. **Cloudflare Worker Backend** (`backend/worker/`): Serverless API with PostgreSQL database
3. **Cron Worker** (`backend/cron-worker/`): Background job processing for analytics data collection

## Development Commands

### Frontend (from `nextjs/` directory)
- `pnpm dev` - Start development server
- `pnpm build` - Build for production  
- `pnpm lint` - Run ESLint
- `pnpm type-check` - Run TypeScript type checking
- `pnpm cf:deploy` - Deploy to Cloudflare Pages

### Backend Worker (from `backend/worker/` directory)
- `pnpm dev` - Start worker development server
- `pnpm deploy` - Deploy to Cloudflare Workers
- `pnpm test` - Run tests

### Cron Worker (from `backend/cron-worker/` directory)
- `npm start` - Start cron worker
- `npm run dev` - Start in development mode with watch

### Data Processing Tools (from `tools/` directory)
- `npm run update-embeddings` - Generate vector embeddings for items
- `npm run process-submissions` - Process pending submissions
- `npm run approve-mcps` - Approve MCP submissions
- `npm run translate-locale` - Translate locale files

## Architecture

### Frontend Architecture
- **Next.js 14+ App Router**: Modern routing with server components
- **Component Organization**:
  - `components/blocks/`: Feature-specific components (link-dashboard, admin panel, gallery)
  - `components/ui/`: shadcn/ui base components  
  - `components/admin/`: Admin-specific components
  - `components/console/` & `components/dashboard/`: Layout components
- **Internationalization**: Multi-language support with next-intl in `i18n/`
- **Authentication**: NextAuth.js with role-based access control

### Backend Architecture  
- **Cloudflare Workers**: Serverless API with edge computing
- **PostgreSQL + Prisma**: Database with type-safe ORM
- **Multi-service Integration**: Google Search Console, Ahrefs, SEMrush, analytics providers

### Database Design
- **Core Entities**: Users → Projects → Links → Analytics
- **Vector Search**: pgvector extension for semantic search on MCP items
- **User Configurations**: Per-user analytics and integration settings
- **Audit Trails**: Comprehensive logging and submission tracking

## Key Features & Data Flow

### Link Management Pipeline
1. **Project Creation**: Users create projects for organizing links
2. **Link Discovery**: Manual addition or automated crawling
3. **Analytics Collection**: Cron worker fetches data from external APIs
4. **Data Processing**: Aggregation and storage in analytics tables
5. **Visualization**: Real-time dashboards and reports

### Analytics Integration Architecture
- **Google Search Console**: Organic search performance data
- **Ahrefs API**: Backlink analysis and domain authority
- **SEMrush API**: Keyword research and competitive analysis  
- **Custom Analytics**: Umami, Google Analytics traffic data
- **Configuration Management**: User-specific API credentials and settings

### Vector Search System
- **Embedding Generation**: OpenAI text-embedding-3-small model
- **Storage**: PostgreSQL pgvector extension with HNSW indexes
- **Search Pipeline**: Query → Embedding → Similarity Search → Results
- **Multi-language Support**: Separate embeddings for localized content

## Development Guidelines

### API Development Patterns
- **Route Organization**: Feature-based grouping in `app/api/`
- **Validation**: Zod schemas for request/response validation
- **Authentication**: Middleware for protected routes
- **Error Handling**: Consistent error responses with proper HTTP status codes

### Database Operations
- **Prisma ORM**: Type-safe database operations
- **Migrations**: Use `prisma migrate` for schema changes
- **Query Optimization**: Proper indexing for analytics time-series data
- **Transaction Handling**: Critical operations wrapped in database transactions

### External Service Integration
- **Rate Limiting**: Respect API limits for external services
- **Error Recovery**: Retry logic with exponential backoff
- **Data Validation**: Sanitize and validate external API responses
- **Configuration Management**: Secure storage of API credentials

### Admin Panel Architecture
- **Role-based Access**: Admin vs user permissions
- **CRUD Operations**: Comprehensive management interfaces
- **Audit Logging**: Track administrative actions
- **Email System**: Template-based notifications

## Performance Considerations

### Frontend Optimization
- **Server Components**: Minimize client-side JavaScript
- **Code Splitting**: Route-based and component-based splitting
- **Image Optimization**: Next.js Image component with proper sizing
- **Caching Strategies**: Static generation where possible

### Backend Performance
- **Database Indexing**: Optimized indexes for analytics queries
- **Connection Pooling**: Efficient database connection management
- **Batch Processing**: Cron worker processes data in batches
- **Edge Computing**: Cloudflare Workers for global distribution

### Analytics Data Handling
- **Time-series Optimization**: Proper partitioning for large datasets
- **Aggregation Strategies**: Pre-computed metrics for dashboard performance
- **Data Retention**: Automated cleanup of old analytics data
- **Caching Layer**: Redis-compatible caching for frequently accessed data

## Testing Strategy

### Test Commands
- Frontend: `pnpm test` (from nextjs/)
- Worker: `pnpm test` (from backend/worker/)
- Type Check: `pnpm type-check` (from nextjs/)

### Testing Approach
- **Unit Tests**: Business logic and utility functions
- **Integration Tests**: API endpoints with database operations
- **Analytics Testing**: Mock external API responses for consistent testing
- **Admin Features**: Role-based access and permission testing

## Security Implementation

### Authentication & Authorization
- **NextAuth.js**: Secure session management
- **Role-based Access**: Admin panel restricted to authorized users
- **API Key Management**: Secure generation and storage of API keys
- **Input Validation**: Comprehensive validation using Zod schemas

### Data Protection
- **Environment Variables**: Secure credential storage
- **Database Encryption**: Sensitive configuration data encryption
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Rate Limiting**: Protection against abuse on public endpoints

## Environment Setup

### Required Environment Variables
- `DATABASE_URL`: PostgreSQL connection with pgvector support
- `NEXTAUTH_SECRET`: Authentication secret key
- `AI_API_KEY`: OpenAI API key for embeddings
- Analytics API keys for Google Search Console, Ahrefs, SEMrush
- Email service credentials for notifications

### Local Development Setup
1. Copy environment files: `cp nextjs/.env.example nextjs/.env.local`
2. Install dependencies in all three projects
3. Set up PostgreSQL with pgvector extension
4. Run database migrations: `npx prisma migrate dev`
5. Start development servers for frontend, worker, and cron

### Vector Search Setup
- Install PostgreSQL with pgvector extension: `CREATE EXTENSION IF NOT EXISTS vector;`
- Configure embedding model settings in environment
- Run initial embedding generation: `cd tools && npm run update-embeddings`