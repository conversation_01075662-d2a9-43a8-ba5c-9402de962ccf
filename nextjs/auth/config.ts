import CredentialsProvider from "next-auth/providers/credentials";
import GitHubProvider from "next-auth/providers/github";
import GoogleProvider from "next-auth/providers/google";
import { NextAuthConfig } from "next-auth";
import { Provider } from "next-auth/providers/index";
import { User } from "@/types/user";
import { getClientIp } from "@/lib/ip";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import { saveUser } from "@/services/user";

let providers: Provider[] = [];

// Google One Tap Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true" &&
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
) {
  providers.push(
    CredentialsProvider({
      id: "google-one-tap",
      name: "google-one-tap",

      credentials: {
        credential: { type: "text" },
      },

      async authorize(credentials, req) {
        const googleClientId = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID;
        if (!googleClientId) {
          console.log("invalid google auth config");
          return null;
        }

        const token = credentials!.credential;

        const response = await fetch(
          "https://oauth2.googleapis.com/tokeninfo?id_token=" + token
        );
        if (!response.ok) {
          console.log("Failed to verify token");
          return null;
        }

        const payload = await response.json();
        if (!payload) {
          console.log("invalid payload from token");
          return null;
        }

        const {
          email,
          sub,
          given_name,
          family_name,
          email_verified,
          picture: image,
        } = payload;
        if (!email) {
          console.log("invalid email in payload");
          return null;
        }

        const user = {
          id: sub,
          name: [given_name, family_name].join(" "),
          email,
          image,
          emailVerified: email_verified ? new Date() : null,
        };

        return user;
      },
    })
  );
}

// Google Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" &&
  process.env.AUTH_GOOGLE_ID &&
  process.env.AUTH_GOOGLE_SECRET
) {
  providers.push(
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
      authorization: {
        params: {
          scope: "openid email profile",
          prompt: "consent",
          access_type: "offline",
        }
      },
      // Add profile callback for better error handling
      profile(profile) {
        console.log("Google OAuth profile received:", { 
          id: profile.sub, 
          email: profile.email,
          verified: profile.email_verified 
        });
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
          emailVerified: profile.email_verified ? new Date() : null,
        };
      },
    })
  );
}

// Github Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" &&
  process.env.AUTH_GITHUB_ID &&
  process.env.AUTH_GITHUB_SECRET
) {
  providers.push(
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
      // Add profile callback for better error handling
      profile(profile) {
        console.log("GitHub OAuth profile received:", { 
          id: profile.id, 
          login: profile.login,
          email: profile.email 
        });
        return {
          id: profile.id.toString(),
          name: profile.name || profile.login,
          email: profile.email,
          image: profile.avatar_url,
        };
      },
    })
  );
}

export const providerMap = providers
  .map((provider) => {
    if (typeof provider === "function") {
      const providerData = provider();
      return { id: providerData.id, name: providerData.name };
    } else {
      return { id: provider.id, name: provider.name };
    }
  })
  .filter((provider) => provider.id !== "google-one-tap");

export const authOptions: NextAuthConfig = {
  providers,
  secret: process.env.NEXTAUTH_SECRET,
  trustHost: true,
  pages: {
    signIn: "/en/auth/signin",
    error: "/en/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  // Add timeout configuration to handle network issues
  experimental: {
    enableWebAuthn: false,
  },
  // Add custom fetch with timeout handling
  events: {
    async signIn(message) {
      console.log("NextAuth signIn event:", message);
    },
    async signOut(message) {
      console.log("NextAuth signOut event:", message);
    },
    async createUser(message) {
      console.log("NextAuth createUser event:", message);
    },
    async linkAccount(message) {
      console.log("NextAuth linkAccount event:", message);
    },
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      try {
        console.log("SignIn callback:", { 
          userEmail: user?.email, 
          accountProvider: account?.provider,
          hasProfile: !!profile 
        });
        
        const isAllowedToSignIn = true;
        if (isAllowedToSignIn) {
          return true;
        } else {
          return false;
        }
      } catch (error) {
        console.error("SignIn callback error:", error);
        return false;
      }
    },
    async redirect({ url, baseUrl }) {
      console.log("Redirect callback:", { url, baseUrl });
      
      try {
        // Handle relative URLs
        if (url.startsWith("/")) {
          // Check if it's already a complete path or needs locale prefix
          if (url.startsWith("/en/") || url.startsWith("/zh/") || url.startsWith("/ja/") || 
              url === "/en" || url === "/zh" || url === "/ja") {
            return `${baseUrl}${url}`;
          }
          // Add default locale prefix for relative URLs
          return `${baseUrl}/en${url}`;
        }
        
        // Handle absolute URLs on same origin
        if (url.startsWith(baseUrl)) {
          // Parse the URL to check if it's a signin page to prevent loops
          const urlObj = new URL(url);
          if (urlObj.pathname.includes('/auth/signin')) {
            console.log("Preventing signin redirect loop, redirecting to home");
            return `${baseUrl}/en`;
          }
          return url;
        }
        
        // For external URLs, redirect to home
        console.log("Redirecting to default home:", `${baseUrl}/en`);
        return `${baseUrl}/en`;
      } catch (error) {
        console.error("Redirect callback error:", error);
        return `${baseUrl}/en`;
      }
    },
    async session({ session, token, user }) {
      console.log("Session callback called with:", { 
        hasToken: !!token, 
        hasTokenUser: !!token.user,
        sessionUserEmail: session.user?.email 
      });

      try {
        // Always use token data if available (this is the correct approach)
        if (token && token.user) {
          console.log("Session callback - using token.user:", token.user);
          const tokenUser = token.user as any; // Type assertion for token.user
          session.user = {
            ...session.user,
            ...tokenUser,
            // Ensure essential fields are preserved from token.user
            uuid: tokenUser.uuid,
            email: tokenUser.email || session.user?.email,
            nickname: tokenUser.nickname,
            avatar_url: tokenUser.avatar_url,
            created_at: tokenUser.created_at,
          };
          
          // Also copy Google OAuth tokens
          if (token.googleAccessToken) {
            session.googleAccessToken = token.googleAccessToken as string;
            session.googleRefreshToken = token.googleRefreshToken as string;
            session.googleTokenExpiry = token.googleTokenExpiry as number;
            session.googleScope = token.googleScope as string;
          }
        } else if (token && token.email && !token.user) {
          // Fallback: if token.user is missing but we have email, ensure session has email
          console.log("Session callback - token.user missing, preserving email from token:", token.email);
          session.user = {
            ...session.user,
            email: token.email,
            // uuid will be undefined, but getUserInfo will handle this by looking up by email
          };
        }

        console.log("Session callback - final session.user:", session.user);
        return session;
      } catch (error) {
        console.error("Session callback error:", error);
        return session;
      }
    },
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      console.log("JWT callback called with:", { 
        hasToken: !!token, 
        hasUser: !!user, 
        userEmail: user?.email, 
        hasAccount: !!account,
        accountProvider: account?.provider 
      });
      
      try {
        // Initial login: user and account are provided
        if (user && user.email && account) {
          console.log("JWT callback - processing new user authentication");
          
          // Ensure token.email is set from the user
          token.email = user.email;
          
          const dbUser: User = {
            uuid: getUuid(),
            email: user.email,
            nickname: user.name || "",
            avatar_url: user.image || "",
            signin_type: account.type,
            signin_provider: account.provider,
            signin_openid: account.providerAccountId,
            created_at: getIsoTimestr(),
            signin_ip: await getClientIp(),
          };

          try {
            const savedUser = await saveUser(dbUser);
            console.log("JWT callback - saved user:", savedUser);

            // Only set token.user if we have a valid saved user with UUID
            if (savedUser && savedUser.uuid) {
              token.user = {
                uuid: savedUser.uuid,
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatar_url: savedUser.avatar_url,
                created_at: savedUser.created_at,
              };

              // Save Google OAuth tokens for Analytics access
              if (account.provider === "google" && account.access_token) {
                token.googleAccessToken = account.access_token;
                token.googleRefreshToken = account.refresh_token;
                token.googleTokenExpiry = account.expires_at;
                token.googleScope = account.scope;
              }
              
              console.log("JWT callback - token.user set:", token.user);
            } else {
              console.error("JWT callback - savedUser is invalid or missing UUID:", savedUser);
            }
          } catch (e) {
            console.error("save user failed:", e);
            // Do not set token.user if save fails - let authentication fail properly
          }
        } 
        // Subsequent calls: no user/account, just token maintenance
        else {
          console.log("JWT callback - subsequent call, maintaining existing token:", { hasTokenUser: !!token.user });
          
          // Ensure token.email is preserved if not already set
          if (!token.email && token.sub) {
            console.log("JWT callback - attempting to preserve email from previous session");
            // token.sub usually contains email in OAuth scenarios
          }
          
          // If we don't have token.user but we have email in token, try to get user from database
          if (!token.user && token.email) {
            console.log("JWT callback - missing token.user, trying to recover from database");
            try {
              const { findUserByEmail } = await import("@/models/user");
              const dbUser = await findUserByEmail(token.email);
              if (dbUser && dbUser.uuid) {
                console.log("JWT callback - recovered user from database:", dbUser);
                token.user = {
                  uuid: dbUser.uuid,
                  email: dbUser.email,
                  nickname: dbUser.nickname,
                  avatar_url: dbUser.avatar_url,
                  created_at: dbUser.created_at,
                };
              } else {
                console.log("JWT callback - no user found in database for recovery");
              }
            } catch (error) {
              console.error("JWT callback - failed to recover user from database:", error);
            }
          }
        }
        
        return token;
      } catch (e) {
        console.error("jwt callback error:", e);
        return token;
      }
    },
  },
  debug: process.env.NODE_ENV === "development",
  logger: {
    error(error) {
      console.error("NextAuth Error:", error);
    },
    warn(code) {
      console.warn("NextAuth Warning:", code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.debug("NextAuth Debug:", code, metadata);
      }
    },
  },
};
