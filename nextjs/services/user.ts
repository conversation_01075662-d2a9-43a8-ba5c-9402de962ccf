import { CreditsAmount, CreditsTransType } from "./credit";
import { 
  findUserByEmail, 
  findUserByUuid, 
  insertUser, 
  findUserByInviteCode, 
  incrementInvitesCount,
  updateUserInvitedBy
} from "@/models/user";

import { User } from "@/types/user";
import { auth } from "@/auth";
import { getOneYearLaterTimestr } from "@/lib/time";
import { getUserUuidByApiKey } from "@/models/apikey";
import { headers } from "next/headers";
import { increaseCredits } from "./credit";
import { cookies } from "next/headers";
import { getInvitationCredits } from "@/models/settings";
import { getAuthenticatedUser } from "@/lib/auth";

export async function saveUser(user: User) {
  try {
    console.log('saveUser: Starting user save process for:', user.email);
    
    const existUser = await findUserByEmail(user.email);
    
    // Check for invite code in cookies
    const cookieStore = await cookies();
    const inviteCode = cookieStore.get('invite_code')?.value;
    
    if (!existUser) {
      console.log('saveUser: New user detected, inserting...');
      
      // If new user and has invite code, set invited_by
      if (inviteCode) {
        const inviter = await findUserByInviteCode(inviteCode);
        if (inviter && inviter.uuid) {
          user.invited_by = inviter.uuid;
        }
      }
      
      await insertUser(user);

      // increase credits for new user, expire in one year
      await increaseCredits({
        user_uuid: user.uuid || "",
        trans_type: CreditsTransType.NewUser,
        credits: CreditsAmount.NewUserGet,
        expired_at: getOneYearLaterTimestr(),
      });
      
      // If user was invited, process invitation rewards
      if (user.invited_by) {
        await processInvitationRewards(user);
      }
    } else {
      user.id = existUser.id;
      user.uuid = existUser.uuid;
      user.created_at = existUser.created_at;
      user.invite_code = existUser.invite_code;
      user.invites_count = existUser.invites_count;
      
      // If existing user doesn't have invited_by but we have an invite code,
      // update the user record
      if (!existUser.invited_by && inviteCode) {
        const inviter = await findUserByInviteCode(inviteCode);
        if (inviter && inviter.uuid && inviter.uuid !== user.uuid) {
          await updateUserInvitedBy(user.uuid || "", inviter.uuid);
          user.invited_by = inviter.uuid;
          
          // Process invitation rewards
          await processInvitationRewards(user);
        }
      }
    }

    return user;
  } catch (e) {
    console.log("save user failed: ", e);
    throw e;
  }
}

// Process rewards for successful invitation
async function processInvitationRewards(user: User) {
  if (!user.invited_by) return;
  
  try {
    // Get the invitation credit amount from settings
    const invitationCredits = await getInvitationCredits();
    
    // Increment the inviter's invitation count
    await incrementInvitesCount(user.invited_by);
    
    // Add invitation credits to the inviter
    await increaseCredits({
      user_uuid: user.invited_by,
      trans_type: CreditsTransType.Invitation,
      credits: invitationCredits,
      expired_at: getOneYearLaterTimestr(),
    });
    
  } catch (error) {
    console.error("Failed to process invitation rewards:", error);
  }
}

// Get invite link for current user
export async function getUserInviteLink() {
  const user = await getUserInfo();
  if (!user || !user.invite_code) return null;
  
  return `${process.env.NEXT_PUBLIC_WEB_URL}/auth/signin?invite=${user.invite_code}`;
}

export async function getUserUuid() {
  let user_uuid = "";

  const token = await getBearerToken();

  if (token) {
    // api key
    if (token.startsWith("sk-")) {
      const user_uuid = await getUserUuidByApiKey(token);

      return user_uuid || "";
    }
  }

  // Use the enhanced auth function that supports development mode
  const user = await getAuthenticatedUser();
  if (user && user.uuid) {
    user_uuid = user.uuid;
  }

  return user_uuid;
}

export async function getBearerToken() {
  try {
    // Import the safeHeadersAccess utility
    const { safeHeadersAccess } = await import('@/lib/utils/retry');
    
    // Safely access headers using the utility function
    const authHeader = await safeHeadersAccess(async () => {
      const h = headers();
      return h.get("Authorization");
    });
    
    if (!authHeader) {
      return "";
    }

    return authHeader.replace("Bearer ", "");
  } catch (error) {
    console.error("Error getting bearer token:", error);
    return "";
  }
}

export async function getUserEmail() {
  let user_email = "";

  // Use the enhanced auth function that supports development mode
  const user = await getAuthenticatedUser();
  if (user && user.email) {
    user_email = user.email;
  }

  console.log("user_email: ", user_email);

  return user_email;
}

export async function getUserInfo() {
  // Use the enhanced auth function that supports development mode
  const authenticatedUser = await getAuthenticatedUser();
  
  if (!authenticatedUser) {
    console.log("getUserInfo: No authenticated user");
    return;
  }

  // Use standard database lookup for user info

  // First try to get user by UUID if available
  if (authenticatedUser.uuid) {
    console.log("getUserInfo: Looking up user by UUID:", authenticatedUser.uuid);
    const user = await findUserByUuid(authenticatedUser.uuid);
    if (user) {
      console.log("getUserInfo: User found by UUID:", user.uuid);
      return user;
    }
  }

  // If no UUID or user not found by UUID, try to find by email
  if (authenticatedUser.email) {
    console.log("getUserInfo: UUID not available, looking up user by email:", authenticatedUser.email);
    const user = await findUserByEmail(authenticatedUser.email);
    if (user) {
      console.log("getUserInfo: User found by email:", user.uuid);
      return user;
    } else {
      console.log("getUserInfo: No user found by email, user may need to be created");
    }
  }

  console.log("getUserInfo: No user found using available identifiers");
  return;
}
