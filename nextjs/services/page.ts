import { LandingPage } from "@/types/pages/landing";

// Import all landing page locale files statically to avoid dynamic import issues
import enLanding from "@/i18n/pages/landing/en.json";
import zhLanding from "@/i18n/pages/landing/zh.json";
import esLanding from "@/i18n/pages/landing/es.json";
import frLanding from "@/i18n/pages/landing/fr.json";
import deLanding from "@/i18n/pages/landing/de.json";
import jaLanding from "@/i18n/pages/landing/ja.json";
import koLanding from "@/i18n/pages/landing/ko.json";
import ruLanding from "@/i18n/pages/landing/ru.json";

const landingPageMap = {
  en: enLanding,
  zh: zhLanding,
  es: esLanding,
  fr: frLanding,
  de: deLanding,
  ja: jaLanding,
  ko: koLanding,
  ru: ruLanding,
} as const;

export async function getLandingPage(locale: string): Promise<LandingPage> {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    const landingPage = landingPageMap[locale.toLowerCase() as keyof typeof landingPageMap] || landingPageMap.en;
    return landingPage as LandingPage;
  } catch (error) {
    console.warn(`Failed to load ${locale}.json, falling back to en.json`);
    return landingPageMap.en as LandingPage;
  }
}
