import { google } from 'googleapis';
import { oauthTokenManager, OAuthTokenSet } from './oauth-token-manager';
import { OAuthErrorHandler } from './oauth-error-handler';
import { getCanonicalDomain } from '@/utils/url-normalization';

interface SearchConsoleProperty {
  siteUrl: string;
  permissionLevel: string;
  verified: boolean;
}

interface SearchAnalyticsData {
  keys: string[];
  clicks: number;
  impressions: number;
  ctr: number;
  position: number;
}

interface BacklinkData {
  url: string;
  title: string;
  domain: string;
  anchorText: string;
  link_type: 'dofollow' | 'nofollow';
  discoveredAt: string;
  sourceUrl: string;
  isActive: boolean;
  dr_score?: number;
  traffic?: number;
}

class EnhancedSearchConsoleService {
  private createOAuthClient(tokenSet: OAuthTokenSet) {
    const oauth2Client = new google.auth.OAuth2();
    
    oauth2Client.setCredentials({
      access_token: tokenSet.accessToken,
      refresh_token: tokenSet.refreshToken,
      expiry_date: tokenSet.expiresAt
    });

    return oauth2Client;
  }

  async getSearchConsoleProperties(
    userId: string, 
    projectId: string
  ): Promise<SearchConsoleProperty[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'search_console'
        );
        
        const auth = this.createOAuthClient(tokenSet);
        const searchconsole = google.searchconsole({ version: 'v1', auth });
        
        const response = await searchconsole.sites.list();
        const sites = response.data.siteEntry || [];
        
        return sites.map(site => ({
          siteUrl: site.siteUrl || '',
          permissionLevel: site.permissionLevel || '',
          verified: site.permissionLevel === 'siteOwner' || site.permissionLevel === 'siteFullUser'
        }));
      },
      userId,
      projectId,
      'search_console',
      { maxRetries: 2 }
    );
  }

  async getSearchAnalyticsData(
    userId: string,
    projectId: string,
    siteUrl: string,
    startDate: string,
    endDate: string,
    dimensions: string[] = ['page'],
    rowLimit: number = 1000
  ): Promise<SearchAnalyticsData[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'search_console'
        );

        const auth = this.createOAuthClient(tokenSet);
        const searchconsole = google.searchconsole({ version: 'v1', auth });
        
        const response = await searchconsole.searchanalytics.query({
          siteUrl,
          requestBody: {
            startDate,
            endDate,
            dimensions,
            rowLimit
          }
        });

        const rows = response.data.rows || [];
        
        return rows.map(row => ({
          keys: row.keys || [],
          clicks: row.clicks || 0,
          impressions: row.impressions || 0,
          ctr: row.ctr || 0,
          position: row.position || 0
        }));
      },
      userId,
      projectId,
      'search_console',
      { maxRetries: 2 }
    );
  }

  async isDomainVerified(
    domain: string, 
    userId: string, 
    projectId: string
  ): Promise<boolean> {
    try {
      const properties = await this.getSearchConsoleProperties(userId, projectId);
      
      // Normalize domain for comparison
      const normalizedDomain = getCanonicalDomain(domain) || domain;
      const siteUrl = `https://${normalizedDomain}/`;
      
      const verifiedSite = properties.find(site => 
        site.siteUrl === siteUrl && 
        (site.permissionLevel === 'siteOwner' || site.permissionLevel === 'siteFullUser')
      );
      
      return !!verifiedSite;
    } catch (error) {
      console.error('Error checking domain verification:', error);
      return false;
    }
  }

  async discoverBacklinks(
    domain: string, 
    userId: string, 
    projectId: string
  ): Promise<BacklinkData[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'search_console'
        );

        const auth = this.createOAuthClient(tokenSet);
        const searchconsole = google.searchconsole({ version: 'v1', auth });
        
        // Normalize domain for consistent API calls
        const normalizedDomain = getCanonicalDomain(domain) || domain;
        const siteUrl = `https://${normalizedDomain}/`;
        
        // Get external links data from last 90 days
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 90);
        
        const response = await searchconsole.searchanalytics.query({
          siteUrl,
          requestBody: {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
            dimensions: ['page', 'query'],
            dimensionFilterGroups: [{
              filters: [{
                dimension: 'country',
                operator: 'equals',
                expression: 'usa'
              }]
            }],
            rowLimit: 1000
          }
        });

        const rows = response.data.rows || [];
        const backlinks: BacklinkData[] = [];

        for (const row of rows) {
          const [pageUrl, query] = row.keys || [];
          if (pageUrl && query) {
            try {
              const url = new URL(pageUrl);
              const linkDomain = url.hostname;
              
              // Skip internal links
              if (linkDomain === normalizedDomain) continue;
              
              backlinks.push({
                url: pageUrl,
                title: query, // Using query as title approximation
                domain: linkDomain,
                anchorText: query,
                link_type: 'dofollow', // GSC doesn't provide this info
                discoveredAt: new Date().toISOString(),
                sourceUrl: pageUrl,
                isActive: true,
                traffic: row.impressions || 0
              });
            } catch (urlError) {
              // Skip invalid URLs
              continue;
            }
          }
        }

        return backlinks;
      },
      userId,
      projectId,
      'search_console',
      { maxRetries: 2 }
    );
  }

  async getTopPages(
    userId: string,
    projectId: string,
    siteUrl: string,
    startDate: string,
    endDate: string,
    limit: number = 100
  ): Promise<SearchAnalyticsData[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'search_console'
        );

        const auth = this.createOAuthClient(tokenSet);
        const searchconsole = google.searchconsole({ version: 'v1', auth });
        
        const response = await searchconsole.searchanalytics.query({
          siteUrl,
          requestBody: {
            startDate,
            endDate,
            dimensions: ['page'],
            rowLimit: limit
          }
        });

        const rows = response.data.rows || [];
        
        return rows.map(row => ({
          keys: row.keys || [],
          clicks: row.clicks || 0,
          impressions: row.impressions || 0,
          ctr: row.ctr || 0,
          position: row.position || 0
        }));
      },
      userId,
      projectId,
      'search_console',
      { maxRetries: 2 }
    );
  }

  async getTopQueries(
    userId: string,
    projectId: string,
    siteUrl: string,
    startDate: string,
    endDate: string,
    limit: number = 100
  ): Promise<SearchAnalyticsData[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'search_console'
        );

        const auth = this.createOAuthClient(tokenSet);
        const searchconsole = google.searchconsole({ version: 'v1', auth });
        
        const response = await searchconsole.searchanalytics.query({
          siteUrl,
          requestBody: {
            startDate,
            endDate,
            dimensions: ['query'],
            rowLimit: limit
          }
        });

        const rows = response.data.rows || [];
        
        return rows.map(row => ({
          keys: row.keys || [],
          clicks: row.clicks || 0,
          impressions: row.impressions || 0,
          ctr: row.ctr || 0,
          position: row.position || 0
        }));
      },
      userId,
      projectId,
      'search_console',
      { maxRetries: 2 }
    );
  }

  async testConnection(
    userId: string, 
    projectId: string
  ): Promise<{ 
    isValid: boolean; 
    error?: string; 
    hasSearchConsole: boolean; 
    properties?: SearchConsoleProperty[];
  }> {
    try {
      const tokenSet = await oauthTokenManager.getValidTokenSet(
        userId, 
        projectId, 
        'search_console'
      );

      const auth = this.createOAuthClient(tokenSet);
      
      // Test Search Console access by listing sites
      const searchconsole = google.searchconsole({ version: 'v1', auth });
      await searchconsole.sites.list();

      // If successful, get properties for additional verification
      const properties = await this.getSearchConsoleProperties(userId, projectId);

      return {
        isValid: true,
        hasSearchConsole: true,
        properties
      };
    } catch (error: any) {
      console.error('Search Console connection test failed:', error);
      
      const userMessage = OAuthErrorHandler.getUserMessage(error, 'Google Search Console');
      
      return {
        isValid: false,
        hasSearchConsole: false,
        error: userMessage
      };
    }
  }

  // Get indexing status for URLs
  async getIndexingStatus(
    userId: string,
    projectId: string,
    urls: string[]
  ): Promise<any[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'search_console'
        );

        const auth = this.createOAuthClient(tokenSet);
        const searchconsole = google.searchconsole({ version: 'v1', auth });

        const results = [];
        
        // Note: This would require the URL Inspection API which may need additional setup
        // For now, we'll return a placeholder response
        for (const url of urls) {
          try {
            // This is a placeholder - actual implementation would use URL Inspection API
            results.push({
              url,
              indexingStatus: 'INDEXED', // This would come from actual API
              lastCrawlTime: new Date().toISOString(),
              verdict: 'PASS'
            });
          } catch (error) {
            results.push({
              url,
              indexingStatus: 'ERROR',
              error: 'Failed to check indexing status'
            });
          }
        }

        return results;
      },
      userId,
      projectId,
      'search_console',
      { maxRetries: 1 }
    );
  }

  // Check OAuth status for Search Console
  async checkOAuthStatus(
    userId: string,
    projectId: string
  ): Promise<{
    authorized: boolean;
    scopes: string[];
    expiresAt?: number;
    error?: string;
  }> {
    try {
      const status = await oauthTokenManager.getOAuthStatus(userId, projectId);
      return status.searchConsole;
    } catch (error) {
      console.error('Failed to check OAuth status:', error);
      return {
        authorized: false,
        scopes: [],
        error: 'Failed to check authorization status'
      };
    }
  }

  // Revoke OAuth access
  async revokeAccess(
    userId: string,
    projectId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await oauthTokenManager.revokeTokenSet(userId, projectId, 'search_console');
      return { success: true };
    } catch (error: any) {
      console.error('Failed to revoke Search Console access:', error);
      return { 
        success: false, 
        error: error.message || 'Failed to revoke access' 
      };
    }
  }
}

export const enhancedSearchConsoleService = new EnhancedSearchConsoleService();