/**
 * Newsletter service for handling newsletter subscriptions via Beehiiv
 */

/**
 * Subscribe an email to the newsletter using Beehiiv API
 * @param email Email address to subscribe
 * @param source Source of the subscription (default: 'website')
 * @returns Object with success status and message
 */
export async function subscribeToNewsletter(email: string, source: string = 'website') {
  try {
    const BEEHIIV_API_KEY = process.env.BEEHIIV_API_KEY;
    const BEEHIIV_PUBLICATION_ID = process.env.BEEHIIV_PUBLICATION_ID;
    const BEEHIIV_ENABLED = process.env.BEEHIIV_ENABLED;

    if (!BEEHIIV_ENABLED || BEEHIIV_ENABLED === 'false') {
      console.log('Beehi<PERSON> is disabled');
      return {
        success: true,
        message: '<PERSON><PERSON><PERSON> is disabled'
      };
    }

    if (!BEEHIIV_API_KEY || !BEEHIIV_PUBLICATION_ID) {
      console.error('Beehiiv API key or Publication ID is missing');
      throw new Error('Newsletter configuration error');
    }

    // Subscribe to Beehiiv
    const response = await fetch(
      `https://api.beehiiv.com/v2/publications/${BEEHIIV_PUBLICATION_ID}/subscriptions`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${BEEHIIV_API_KEY}`,
        },
        body: JSON.stringify({
          email: email,
          reactivate_existing: false,
          send_welcome_email: true,
          utm_source: source,
          utm_medium: 'website',
          utm_campaign: 'newsletter_signup'
        }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      console.error('Beehiiv subscription error:', data);
      throw new Error(data.message || 'Failed to subscribe to newsletter');
    }

    return {
      success: true,
      message: 'Successfully subscribed to newsletter'
    };
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    throw error;
  }
} 