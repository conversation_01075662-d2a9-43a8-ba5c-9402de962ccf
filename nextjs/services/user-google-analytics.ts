import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { google } from 'googleapis';

interface UserGoogleTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
  scope?: string;
}

interface GoogleAnalyticsProperty {
  propertyId: string;
  displayName: string;
  websiteUrl: string;
  timeZone: string;
  currencyCode: string;
  accountId: string;
  accountDisplayName: string;
}

interface AnalyticsData {
  pageviews: number;
  sessions: number;
  users: number;
  bounceRate: number;
  avgSessionDuration: number;
  date: string;
  source: string;
  medium: string;
  campaign?: string;
  page?: string;
}

interface DateRange {
  startDate: string;
  endDate: string;
}

class UserGoogleAnalyticsService {
  private createOAuthClient(tokens: UserGoogleTokens) {
    const oauth2Client = new google.auth.OAuth2();
    
    oauth2Client.setCredentials({
      access_token: tokens.accessToken,
      refresh_token: tokens.refreshToken,
      expiry_date: tokens.expiresAt
    });

    return oauth2Client;
  }

  private async getAnalyticsDataClient(tokens: UserGoogleTokens): Promise<BetaAnalyticsDataClient> {
    // For now, we'll use the traditional googleapis approach for Analytics
    // The BetaAnalyticsDataClient will be used via googleapis instead
    throw new Error('Use googleapis approach instead');
  }

  async getUserGoogleProperties(tokens: UserGoogleTokens): Promise<GoogleAnalyticsProperty[]> {
    try {
      // 检查token是否过期
      if (tokens.expiresAt && Date.now() >= tokens.expiresAt * 1000) {
        throw new Error('Google access token has expired. Please re-login.');
      }

      const auth = this.createOAuthClient(tokens);
      const analyticsAdmin = google.analyticsadmin({ version: 'v1alpha', auth });

      const accountsResponse = await analyticsAdmin.accounts.list();
      const properties: GoogleAnalyticsProperty[] = [];

      if (accountsResponse.data.accounts) {
        for (const account of accountsResponse.data.accounts) {
          try {
            const propertiesResponse = await analyticsAdmin.properties.list({
              filter: `parent:${account.name}`
            });

            if (propertiesResponse.data.properties) {
              for (const property of propertiesResponse.data.properties) {
                properties.push({
                  propertyId: property.name?.split('/').pop() || '',
                  displayName: property.displayName || '',
                  websiteUrl: '', // GA4 Admin API doesn't provide websiteUrl
                  timeZone: property.timeZone || '',
                  currencyCode: property.currencyCode || '',
                  accountId: account.name?.split('/').pop() || '',
                  accountDisplayName: account.displayName || ''
                });
              }
            }
          } catch (propertyError) {
            console.warn('Failed to fetch properties for account:', account.name, propertyError);
          }
        }
      }

      return properties;
    } catch (error) {
      console.error('Error fetching user Google Analytics properties:', error);
      throw error;
    }
  }

  async getUserAnalyticsData(
    tokens: UserGoogleTokens,
    propertyId: string,
    dateRange: DateRange,
    dimensions: string[] = ['date'],
    metrics: string[] = ['activeUsers', 'sessions', 'screenPageViews']
  ): Promise<AnalyticsData[]> {
    try {
      // 检查token是否过期
      if (tokens.expiresAt && Date.now() >= tokens.expiresAt * 1000) {
        throw new Error('Google access token has expired. Please re-login.');
      }

      // Use googleapis for Analytics Data API since BetaAnalyticsDataClient has auth issues
      const auth = this.createOAuthClient(tokens);
      const analyticsDataApi = google.analyticsdata({ version: 'v1beta', auth });

      const response = await analyticsDataApi.properties.runReport({
        property: `properties/${propertyId}`,
        requestBody: {
          dateRanges: [
            {
              startDate: dateRange.startDate,
              endDate: dateRange.endDate
            }
          ],
          dimensions: dimensions.map(name => ({ name })),
          metrics: metrics.map(name => ({ name }))
        }
      });

      const analyticsData: AnalyticsData[] = [];

      if (response.data.rows) {
        for (const row of response.data.rows) {
          const dimensionValues = row.dimensionValues || [];
          const metricValues = row.metricValues || [];

          // Extract dimension values
          const dateValue = dimensionValues[0]?.value || '';
          const sourceValue = dimensionValues[1]?.value || 'direct';
          const mediumValue = dimensionValues[2]?.value || 'none';
          const campaignValue = dimensionValues[3]?.value || '';
          const pageValue = dimensionValues[4]?.value || '';

          // Extract metric values
          const users = parseInt(metricValues[0]?.value || '0');
          const sessions = parseInt(metricValues[1]?.value || '0');
          const pageviews = parseInt(metricValues[2]?.value || '0');
          const bounceRate = parseFloat(metricValues[3]?.value || '0');
          const avgSessionDuration = parseFloat(metricValues[4]?.value || '0');

          analyticsData.push({
            date: dateValue,
            users,
            sessions,
            pageviews,
            bounceRate,
            avgSessionDuration,
            source: sourceValue,
            medium: mediumValue,
            campaign: campaignValue || undefined,
            page: pageValue || undefined
          });
        }
      }

      return analyticsData;
    } catch (error) {
      console.error('Error fetching user Google Analytics data:', error);
      throw error;
    }
  }

  async getUserSearchConsoleProperties(tokens: UserGoogleTokens): Promise<any[]> {
    try {
      // 检查token是否过期
      if (tokens.expiresAt && Date.now() >= tokens.expiresAt * 1000) {
        throw new Error('Google access token has expired. Please re-login.');
      }

      const auth = this.createOAuthClient(tokens);
      const searchconsole = google.searchconsole({ version: 'v1', auth });
      
      const response = await searchconsole.sites.list();
      const sites = response.data.siteEntry || [];
      
      return sites.map(site => ({
        siteUrl: site.siteUrl,
        permissionLevel: site.permissionLevel,
        verified: site.permissionLevel === 'siteOwner' || site.permissionLevel === 'siteFullUser'
      }));
    } catch (error) {
      console.error('Error fetching user Search Console properties:', error);
      throw error;
    }
  }

  async getUserSearchConsoleData(
    tokens: UserGoogleTokens,
    siteUrl: string,
    startDate: string,
    endDate: string,
    dimensions: string[] = ['page'],
    rowLimit: number = 1000
  ): Promise<any> {
    try {
      // 检查token是否过期
      if (tokens.expiresAt && Date.now() >= tokens.expiresAt * 1000) {
        throw new Error('Google access token has expired. Please re-login.');
      }

      const auth = this.createOAuthClient(tokens);
      const searchconsole = google.searchconsole({ version: 'v1', auth });
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions,
          rowLimit
        }
      });

      return {
        rows: response.data.rows || [],
        responseAggregationType: response.data.responseAggregationType
      };
    } catch (error) {
      console.error('Error fetching user search console data:', error);
      throw error;
    }
  }

  async testUserConnection(tokens: UserGoogleTokens): Promise<{ isValid: boolean; error?: string; hasAnalytics: boolean; hasSearchConsole: boolean }> {
    try {
      // 检查token是否过期
      if (tokens.expiresAt && Date.now() >= tokens.expiresAt * 1000) {
        return { 
          isValid: false, 
          error: 'Google access token has expired. Please re-login.',
          hasAnalytics: false,
          hasSearchConsole: false
        };
      }

      const auth = this.createOAuthClient(tokens);
      
      // 测试Analytics权限
      let hasAnalytics = false;
      try {
        const analyticsAdmin = google.analyticsadmin({ version: 'v1alpha', auth });
        await analyticsAdmin.accounts.list();
        hasAnalytics = true;
      } catch (analyticsError) {
        console.warn('No Google Analytics access:', analyticsError);
      }

      // 测试Search Console权限
      let hasSearchConsole = false;
      try {
        const searchconsole = google.searchconsole({ version: 'v1', auth });
        await searchconsole.sites.list();
        hasSearchConsole = true;
      } catch (searchError) {
        console.warn('No Google Search Console access:', searchError);
      }

      return {
        isValid: hasAnalytics || hasSearchConsole,
        hasAnalytics,
        hasSearchConsole
      };
    } catch (error: any) {
      return {
        isValid: false,
        error: error.message || 'Failed to test Google connection',
        hasAnalytics: false,
        hasSearchConsole: false
      };
    }
  }
}

export const userGoogleAnalyticsService = new UserGoogleAnalyticsService();