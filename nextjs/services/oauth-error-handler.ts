import { OAuthError, OAuthErrorType, oauthTokenManager } from './oauth-token-manager';

interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
}

export class OAuthErrorHandler {
  static async handleWithRetry<T>(
    operation: () => Promise<T>,
    userId: string,
    projectId: string,
    serviceType: 'analytics' | 'search_console',
    options: RetryOptions = {}
  ): Promise<T> {
    const { 
      maxRetries = 3, 
      retryDelay = 1000, 
      exponentialBackoff = true 
    } = options;
    
    let lastError: OAuthError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.processError(error);
        
        console.warn(`OAuth operation failed (attempt ${attempt}/${maxRetries}):`, {
          error: lastError.type,
          message: lastError.message,
          serviceType,
          userId: userId.substring(0, 8) + '...'
        });
        
        // If error requires reauth, don't retry
        if (lastError.requiresReauth) {
          throw lastError;
        }
        
        // If token expired, retry once (getValidTokenSet handles refresh automatically)
        if (lastError.type === OAuthErrorType.TOKEN_EXPIRED && attempt === 1) {
          console.log(`Token expired for ${serviceType}, retrying operation...`);
          continue; // Retry operation, getValidTokenSet will handle refresh
        }
        
        // If rate limited, wait before retry
        if (lastError.type === OAuthErrorType.RATE_LIMITED && attempt < maxRetries) {
          const delay = exponentialBackoff 
            ? retryDelay * Math.pow(2, attempt - 1)
            : retryDelay;
          
          console.log(`Rate limited, waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // If not retryable or max attempts reached, throw error
        if (!lastError.retryable || attempt === maxRetries) {
          throw lastError;
        }
        
        // Wait before next attempt
        if (attempt < maxRetries) {
          const delay = exponentialBackoff 
            ? retryDelay * Math.pow(2, attempt - 1)
            : retryDelay;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError!;
  }

  static processError(error: any): OAuthError {
    // If it's already an OAuthError, return as-is
    if (error instanceof OAuthError) {
      return error;
    }

    // Handle different types of errors
    if (error?.response) {
      // HTTP response errors
      return this.handleHttpError(error);
    }

    if (error?.code) {
      // Handle specific error codes
      return this.handleCodedError(error);
    }

    if (typeof error === 'string') {
      // Handle string errors
      return this.handleStringError(error);
    }

    // Handle generic errors
    return new OAuthError({
      type: OAuthErrorType.NETWORK_ERROR,
      message: error?.message || 'Unknown OAuth error',
      retryable: true,
      requiresReauth: false
    });
  }

  private static handleHttpError(error: any): OAuthError {
    const status = error.response?.status;
    const data = error.response?.data;

    switch (status) {
      case 401:
        if (data?.error === 'invalid_grant' || data?.error_description?.includes('invalid_grant')) {
          return new OAuthError({
            type: OAuthErrorType.TOKEN_REVOKED,
            message: 'OAuth token has been revoked by user',
            retryable: false,
            requiresReauth: true,
            code: data?.error
          });
        }
        
        return new OAuthError({
          type: OAuthErrorType.TOKEN_EXPIRED,
          message: 'OAuth token has expired',
          retryable: true,
          requiresReauth: false,
          code: data?.error
        });

      case 403:
        if (data?.error?.message?.includes('insufficient scope') || 
            data?.error_description?.includes('scope')) {
          return new OAuthError({
            type: OAuthErrorType.INSUFFICIENT_SCOPE,
            message: 'Insufficient OAuth permissions for this operation',
            retryable: false,
            requiresReauth: true,
            code: data?.error
          });
        }
        
        return new OAuthError({
          type: OAuthErrorType.RATE_LIMITED,
          message: 'API rate limit exceeded',
          retryable: true,
          requiresReauth: false,
          code: data?.error
        });

      case 429:
        return new OAuthError({
          type: OAuthErrorType.RATE_LIMITED,
          message: 'Too many requests - rate limit exceeded',
          retryable: true,
          requiresReauth: false,
          code: data?.error
        });

      case 400:
        if (data?.error === 'invalid_request') {
          return new OAuthError({
            type: OAuthErrorType.INVALID_GRANT,
            message: data?.error_description || 'Invalid OAuth request',
            retryable: false,
            requiresReauth: true,
            code: data?.error
          });
        }
        break;

      case 500:
      case 502:
      case 503:
      case 504:
        return new OAuthError({
          type: OAuthErrorType.NETWORK_ERROR,
          message: `Server error (${status}): ${data?.message || 'Service temporarily unavailable'}`,
          retryable: true,
          requiresReauth: false,
          code: data?.error
        });
    }

    return new OAuthError({
      type: OAuthErrorType.NETWORK_ERROR,
      message: `HTTP ${status}: ${data?.message || data?.error_description || 'Unknown error'}`,
      retryable: status >= 500,
      requiresReauth: status === 401 || status === 403,
      code: data?.error
    });
  }

  private static handleCodedError(error: any): OAuthError {
    const code = error.code;
    const message = error.message || error.description;

    switch (code) {
      case 'ENOTFOUND':
      case 'ECONNREFUSED':
      case 'ETIMEDOUT':
        return new OAuthError({
          type: OAuthErrorType.NETWORK_ERROR,
          message: `Network connectivity error: ${message}`,
          retryable: true,
          requiresReauth: false,
          code
        });

      case 'invalid_grant':
        return new OAuthError({
          type: OAuthErrorType.TOKEN_REVOKED,
          message: 'OAuth grant is invalid or has been revoked',
          retryable: false,
          requiresReauth: true,
          code
        });

      case 'invalid_token':
        return new OAuthError({
          type: OAuthErrorType.TOKEN_EXPIRED,
          message: 'OAuth token is invalid or expired',
          retryable: true,
          requiresReauth: false,
          code
        });

      case 'insufficient_scope':
        return new OAuthError({
          type: OAuthErrorType.INSUFFICIENT_SCOPE,
          message: 'Insufficient permissions for this operation',
          retryable: false,
          requiresReauth: true,
          code
        });

      default:
        return new OAuthError({
          type: OAuthErrorType.NETWORK_ERROR,
          message: `Error ${code}: ${message}`,
          retryable: true,
          requiresReauth: false,
          code
        });
    }
  }

  private static handleStringError(error: string): OAuthError {
    const lowerError = error.toLowerCase();

    if (lowerError.includes('token') && lowerError.includes('expired')) {
      return new OAuthError({
        type: OAuthErrorType.TOKEN_EXPIRED,
        message: error,
        retryable: true,
        requiresReauth: false
      });
    }

    if (lowerError.includes('unauthorized') || lowerError.includes('401')) {
      return new OAuthError({
        type: OAuthErrorType.TOKEN_EXPIRED,
        message: error,
        retryable: true,
        requiresReauth: false
      });
    }

    if (lowerError.includes('forbidden') || lowerError.includes('403')) {
      return new OAuthError({
        type: OAuthErrorType.INSUFFICIENT_SCOPE,
        message: error,
        retryable: false,
        requiresReauth: true
      });
    }

    if (lowerError.includes('rate limit') || lowerError.includes('429')) {
      return new OAuthError({
        type: OAuthErrorType.RATE_LIMITED,
        message: error,
        retryable: true,
        requiresReauth: false
      });
    }

    if (lowerError.includes('network') || lowerError.includes('connection')) {
      return new OAuthError({
        type: OAuthErrorType.NETWORK_ERROR,
        message: error,
        retryable: true,
        requiresReauth: false
      });
    }

    return new OAuthError({
      type: OAuthErrorType.NETWORK_ERROR,
      message: error,
      retryable: true,
      requiresReauth: false
    });
  }

  // Convenience method for checking if an error requires immediate reauth
  static requiresReauth(error: any): boolean {
    const processedError = this.processError(error);
    return processedError.requiresReauth;
  }

  // Convenience method for checking if an error is retryable
  static isRetryable(error: any): boolean {
    const processedError = this.processError(error);
    return processedError.retryable;
  }

  // Method to get user-friendly error message
  static getUserMessage(error: any, serviceType: string): string {
    const processedError = this.processError(error);
    
    switch (processedError.type) {
      case OAuthErrorType.TOKEN_EXPIRED:
        return `Your ${serviceType} session has expired. We'll try to refresh it automatically.`;
      
      case OAuthErrorType.TOKEN_REVOKED:
        return `Your ${serviceType} access has been revoked. Please reconnect to continue.`;
      
      case OAuthErrorType.INSUFFICIENT_SCOPE:
        return `Additional ${serviceType} permissions are required. Please reconnect with the necessary permissions.`;
      
      case OAuthErrorType.RATE_LIMITED:
        return `${serviceType} API rate limit exceeded. Please try again in a few minutes.`;
      
      case OAuthErrorType.AUTH_REQUIRED:
        return `Please connect your ${serviceType} account to use this feature.`;
      
      case OAuthErrorType.NETWORK_ERROR:
        return `Network error connecting to ${serviceType}. Please check your connection and try again.`;
      
      default:
        return `${serviceType} error: ${processedError.message}`;
    }
  }
}