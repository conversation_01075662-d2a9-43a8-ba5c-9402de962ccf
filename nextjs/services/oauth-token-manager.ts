import { getSupabaseClient } from "@/models/db";
import { PostgrestError } from "@supabase/supabase-js";
import crypto from 'crypto';

// OAuth Token Types
export type OAuthTokenType = 'basic' | 'analytics' | 'search_console' | 'combined';

export interface OAuthTokenSet {
  id: string;
  userId: string;
  projectId: string;
  tokenType: OAuthTokenType;
  scopes: string[];
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
  tokenData?: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastRefreshedAt?: string;
}

export interface OAuthState {
  id: string;
  userId: string;
  projectId: string;
  stateToken: string;
  codeVerifier: string;
  tokenType: OAuthTokenType;
  scopes: string[];
  redirectUrl?: string;
  createdAt: string;
  expiresAt: string;
  isUsed: boolean;
}

export enum OAuthErrorType {
  TOKEN_EXPIRED = 'token_expired',
  TOKEN_REVOKED = 'token_revoked',
  INSUFFICIENT_SCOPE = 'insufficient_scope',
  RATE_LIMITED = 'rate_limited',
  NETWORK_ERROR = 'network_error',
  INVALID_GRANT = 'invalid_grant',
  AUTH_REQUIRED = 'auth_required',
  INVALID_STATE = 'invalid_state'
}

export class OAuthError extends Error {
  public type: OAuthErrorType;
  public retryable: boolean;
  public requiresReauth: boolean;
  public code?: string;

  constructor(params: {
    type: OAuthErrorType;
    message: string;
    retryable: boolean;
    requiresReauth: boolean;
    code?: string;
  }) {
    super(params.message);
    this.type = params.type;
    this.retryable = params.retryable;
    this.requiresReauth = params.requiresReauth;
    this.code = params.code;
    this.name = 'OAuthError';
  }
}

class OAuthTokenManager {
  private static instance: OAuthTokenManager;
  private refreshPromises = new Map<string, Promise<OAuthTokenSet>>();

  static getInstance(): OAuthTokenManager {
    if (!this.instance) {
      this.instance = new OAuthTokenManager();
    }
    return this.instance;
  }

  // PKCE and State Management
  generateCodeVerifier(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  generateCodeChallenge(verifier: string): string {
    return crypto.createHash('sha256').update(verifier).digest('base64url');
  }

  generateState(): string {
    return crypto.randomUUID();
  }

  // Store OAuth state for PKCE flow
  async storeOAuthState(
    userId: string,
    projectId: string,
    tokenType: OAuthTokenType,
    scopes: string[],
    redirectUrl?: string
  ): Promise<{ state: string; codeVerifier: string }> {
    const client = getSupabaseClient();
    const state = this.generateState();
    const codeVerifier = this.generateCodeVerifier();

    const { error } = await client.rpc('store_oauth_state', {
      p_user_id: userId,
      p_project_id: projectId,
      p_state_token: state,
      p_code_verifier: codeVerifier,
      p_token_type: tokenType,
      p_scopes: scopes,
      p_redirect_url: redirectUrl
    });

    if (error) {
      throw new Error(`Failed to store OAuth state: ${error.message}`);
    }

    return { state, codeVerifier };
  }

  // Verify and consume OAuth state
  async verifyOAuthState(stateToken: string): Promise<OAuthState> {
    const client = getSupabaseClient();

    const { data, error } = await client.rpc('verify_oauth_state', {
      p_state_token: stateToken
    });

    if (error || !data) {
      throw new OAuthError({
        type: OAuthErrorType.INVALID_STATE,
        message: 'Invalid or expired OAuth state',
        retryable: false,
        requiresReauth: true
      });
    }

    return {
      id: data.id,
      userId: data.user_id,
      projectId: data.project_id,
      stateToken: data.state_token,
      codeVerifier: data.code_verifier,
      tokenType: data.token_type,
      scopes: data.scopes,
      redirectUrl: data.redirect_url,
      createdAt: data.created_at,
      expiresAt: data.expires_at,
      isUsed: data.is_used
    };
  }

  // Token Storage and Retrieval
  async storeTokenSet(tokenSet: Omit<OAuthTokenSet, 'id' | 'createdAt' | 'updatedAt'>): Promise<OAuthTokenSet> {
    const client = getSupabaseClient();

    const { data, error } = await client.rpc('store_oauth_tokens', {
      p_user_id: tokenSet.userId,
      p_project_id: tokenSet.projectId,
      p_token_type: tokenSet.tokenType,
      p_scopes: tokenSet.scopes,
      p_access_token: tokenSet.accessToken,
      p_refresh_token: tokenSet.refreshToken,
      p_expires_at: new Date(tokenSet.expiresAt).toISOString(),
      p_token_data: tokenSet.tokenData || {}
    });

    if (error) {
      throw new Error(`Failed to store OAuth tokens: ${error.message}`);
    }

    return this.mapDbTokenToTokenSet(data);
  }

  async getStoredTokenSet(
    userId: string,
    projectId: string,
    tokenType: OAuthTokenType
  ): Promise<OAuthTokenSet | null> {
    const client = getSupabaseClient();

    const { data, error } = await client
      .from('user_oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('project_id', projectId)
      .eq('token_type', tokenType)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get OAuth tokens: ${error.message}`);
    }

    if (!data) {
      return null;
    }

    return this.mapDbTokenToTokenSet(data);
  }

  async getValidTokenSet(
    userId: string,
    projectId: string,
    serviceType: 'analytics' | 'search_console'
  ): Promise<OAuthTokenSet> {
    // Try to get specific token type first
    let tokenSet = await this.getStoredTokenSet(userId, projectId, serviceType);

    // Fallback to combined tokens if available
    if (!tokenSet) {
      tokenSet = await this.getStoredTokenSet(userId, projectId, 'combined');
    }

    if (!tokenSet) {
      throw new OAuthError({
        type: OAuthErrorType.AUTH_REQUIRED,
        message: `No ${serviceType} authorization found`,
        retryable: false,
        requiresReauth: true
      });
    }

    // Check if token is about to expire (within 5 minutes)
    const expiryBuffer = 5 * 60 * 1000; // 5 minutes
    if (tokenSet.expiresAt <= Date.now() + expiryBuffer) {
      try {
        tokenSet = await this.refreshTokens(tokenSet);
      } catch (error) {
        // If refresh fails, mark token as inactive and require reauth
        await this.markTokenSetInactive(tokenSet);
        throw error;
      }
    }

    return tokenSet;
  }

  // Token Refresh
  async refreshTokens(tokenSet: OAuthTokenSet): Promise<OAuthTokenSet> {
    const key = `${tokenSet.userId}-${tokenSet.projectId}-${tokenSet.tokenType}`;

    // Prevent multiple concurrent refresh requests for the same token
    if (this.refreshPromises.has(key)) {
      return await this.refreshPromises.get(key)!;
    }

    const refreshPromise = this.performTokenRefresh(tokenSet);
    this.refreshPromises.set(key, refreshPromise);

    try {
      const result = await refreshPromise;
      return result;
    } finally {
      this.refreshPromises.delete(key);
    }
  }

  private async performTokenRefresh(tokenSet: OAuthTokenSet): Promise<OAuthTokenSet> {
    if (!tokenSet.refreshToken) {
      throw new OAuthError({
        type: OAuthErrorType.AUTH_REQUIRED,
        message: 'No refresh token available, re-authorization required',
        retryable: false,
        requiresReauth: true
      });
    }

    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          client_id: process.env.AUTH_GOOGLE_ID!,
          client_secret: process.env.AUTH_GOOGLE_SECRET!,
          refresh_token: tokenSet.refreshToken,
          grant_type: 'refresh_token'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error_description || 'Token refresh failed');
      }

      const tokenData = await response.json();

      // Update token set with new access token
      const updatedTokenSet = {
        ...tokenSet,
        accessToken: tokenData.access_token,
        expiresAt: Date.now() + (tokenData.expires_in * 1000),
        lastRefreshedAt: new Date().toISOString()
      };

      // Store updated tokens in database
      const client = getSupabaseClient();
      const { error } = await client.rpc('refresh_oauth_tokens', {
        p_user_id: tokenSet.userId,
        p_project_id: tokenSet.projectId,
        p_token_type: tokenSet.tokenType,
        p_new_access_token: updatedTokenSet.accessToken,
        p_new_expires_at: new Date(updatedTokenSet.expiresAt).toISOString()
      });

      if (error) {
        throw new Error(`Failed to update refreshed tokens: ${error.message}`);
      }

      return updatedTokenSet;
    } catch (error: any) {
      throw this.handleGoogleAPIError(error);
    }
  }

  // Token Revocation
  async revokeTokenSet(
    userId: string,
    projectId: string,
    tokenType: OAuthTokenType
  ): Promise<void> {
    const tokenSet = await this.getStoredTokenSet(userId, projectId, tokenType);

    if (tokenSet) {
      try {
        // Revoke token with Google
        await fetch(`https://oauth2.googleapis.com/revoke?token=${tokenSet.accessToken}`, {
          method: 'POST'
        });
      } catch (error) {
        console.warn('Failed to revoke token with Google:', error);
      }

      // Mark as inactive in database
      await this.markTokenSetInactive(tokenSet);
    }
  }

  private async markTokenSetInactive(tokenSet: OAuthTokenSet): Promise<void> {
    const client = getSupabaseClient();

    const { error } = await client.rpc('revoke_oauth_tokens', {
      p_user_id: tokenSet.userId,
      p_project_id: tokenSet.projectId,
      p_token_type: tokenSet.tokenType
    });

    if (error) {
      console.error('Failed to mark token as inactive:', error);
    }
  }

  // OAuth Flow Management
  async initiateProgressiveAuth(
    userId: string,
    projectId: string,
    tokenType: OAuthTokenType,
    redirectUrl?: string
  ): Promise<{ authUrl: string; state: string }> {
    const scopes = this.getScopesForTokenType(tokenType);
    const { state, codeVerifier } = await this.storeOAuthState(
      userId,
      projectId,
      tokenType,
      scopes,
      redirectUrl
    );

    const authUrl = this.buildAuthUrl(scopes, state, codeVerifier);

    return { authUrl, state };
  }

  async exchangeCodeForTokens(
    code: string,
    state: string
  ): Promise<OAuthTokenSet> {
    // Verify state and get stored data
    const storedState = await this.verifyOAuthState(state);

    // Exchange code for tokens
    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.AUTH_GOOGLE_ID!,
        client_secret: process.env.AUTH_GOOGLE_SECRET!,
        code,
        code_verifier: storedState.codeVerifier,
        grant_type: 'authorization_code',
        redirect_uri: storedState.redirectUrl || `${process.env.NEXTAUTH_URL}/api/auth/callback/google`
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error_description || 'Token exchange failed');
    }

    const tokenData = await response.json();

    // Store tokens in database
    const tokenSet = await this.storeTokenSet({
      userId: storedState.userId,
      projectId: storedState.projectId,
      tokenType: storedState.tokenType,
      scopes: storedState.scopes,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      expiresAt: Date.now() + (tokenData.expires_in * 1000),
      tokenData: tokenData,
      isActive: true,
      lastRefreshedAt: undefined
    });

    return tokenSet;
  }

  // Utility Methods
  private getScopesForTokenType(tokenType: OAuthTokenType): string[] {
    const scopeMap = {
      'basic': ['openid', 'email', 'profile'],
      'analytics': ['openid', 'email', 'profile', 'https://www.googleapis.com/auth/analytics.readonly'],
      'search_console': ['openid', 'email', 'profile', 'https://www.googleapis.com/auth/webmasters.readonly'],
      'combined': [
        'openid', 'email', 'profile',
        'https://www.googleapis.com/auth/analytics.readonly',
        'https://www.googleapis.com/auth/webmasters.readonly'
      ]
    };

    return scopeMap[tokenType] || scopeMap['analytics'];
  }

  private buildAuthUrl(scopes: string[], state: string, codeVerifier: string): string {
    const codeChallenge = this.generateCodeChallenge(codeVerifier);
    const params = new URLSearchParams({
      client_id: process.env.AUTH_GOOGLE_ID!,
      redirect_uri: `${process.env.NEXTAUTH_URL}/oauth/callback`,
      response_type: 'code',
      scope: scopes.join(' '),
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
      access_type: 'offline',
      prompt: 'consent'
    });

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
  }

  private mapDbTokenToTokenSet(data: any): OAuthTokenSet {
    return {
      id: data.id,
      userId: data.user_id,
      projectId: data.project_id,
      tokenType: data.token_type,
      scopes: data.scope_requested,
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresAt: new Date(data.expires_at).getTime(),
      tokenData: data.token_data,
      isActive: data.is_active,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      lastRefreshedAt: data.last_refreshed_at
    };
  }

  private handleGoogleAPIError(error: any): OAuthError {
    // Google API specific error handling
    if (error.response?.status === 401) {
      if (error.response.data?.error === 'invalid_grant') {
        return new OAuthError({
          type: OAuthErrorType.TOKEN_REVOKED,
          message: 'OAuth token has been revoked by user',
          retryable: false,
          requiresReauth: true
        });
      }

      return new OAuthError({
        type: OAuthErrorType.TOKEN_EXPIRED,
        message: 'OAuth token has expired',
        retryable: true,
        requiresReauth: false
      });
    }

    if (error.response?.status === 403) {
      if (error.response.data?.error?.message?.includes('insufficient scope')) {
        return new OAuthError({
          type: OAuthErrorType.INSUFFICIENT_SCOPE,
          message: 'Insufficient OAuth permissions for this operation',
          retryable: false,
          requiresReauth: true
        });
      }

      return new OAuthError({
        type: OAuthErrorType.RATE_LIMITED,
        message: 'API rate limit exceeded',
        retryable: true,
        requiresReauth: false
      });
    }

    return new OAuthError({
      type: OAuthErrorType.NETWORK_ERROR,
      message: error.message || 'Unknown OAuth error',
      retryable: true,
      requiresReauth: false
    });
  }

  // Token Status Checking
  async getOAuthStatus(
    userId: string,
    projectId: string
  ): Promise<{
    basic: { authorized: boolean; scopes: string[]; expiresAt?: number };
    analytics: { authorized: boolean; scopes: string[]; expiresAt?: number };
    searchConsole: { authorized: boolean; scopes: string[]; expiresAt?: number };
  }> {
    const client = getSupabaseClient();

    const { data: tokens } = await client
      .from('user_oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('project_id', projectId)
      .eq('is_active', true);

    const status = {
      basic: { authorized: false, scopes: [], expiresAt: undefined },
      analytics: { authorized: false, scopes: [], expiresAt: undefined },
      searchConsole: { authorized: false, scopes: [], expiresAt: undefined }
    };

    if (tokens) {
      for (const token of tokens) {
        const isValid = new Date(token.expires_at).getTime() > Date.now();
        const tokenInfo = {
          authorized: isValid,
          scopes: token.scope_requested,
          expiresAt: isValid ? new Date(token.expires_at).getTime() : undefined
        };

        switch (token.token_type) {
          case 'basic':
            status.basic = tokenInfo;
            break;
          case 'analytics':
            status.analytics = tokenInfo;
            break;
          case 'search_console':
            status.searchConsole = tokenInfo;
            break;
          case 'combined':
            // Combined tokens provide both analytics and search console access
            status.analytics = tokenInfo;
            status.searchConsole = tokenInfo;
            break;
        }
      }
    }

    return status;
  }

  // Cleanup expired states
  async cleanupExpiredStates(): Promise<number> {
    const client = getSupabaseClient();
    const { data } = await client.rpc('cleanup_expired_oauth_states');
    return data || 0;
  }
}

export const oauthTokenManager = OAuthTokenManager.getInstance();