import { google } from 'googleapis';
import { oauthTokenManager, OAuthTokenSet } from './oauth-token-manager';
import { OAuthErrorHandler } from './oauth-error-handler';

interface GoogleAnalyticsProperty {
  propertyId: string;
  displayName: string;
  websiteUrl: string;
  timeZone: string;
  currencyCode: string;
  accountId: string;
  accountDisplayName: string;
}

interface AnalyticsData {
  pageviews: number;
  sessions: number;
  users: number;
  bounceRate: number;
  avgSessionDuration: number;
  date: string;
  source: string;
  medium: string;
  campaign?: string;
  page?: string;
}

interface DateRange {
  startDate: string;
  endDate: string;
}

class EnhancedGoogleAnalyticsService {
  private createOAuthClient(tokenSet: OAuthTokenSet) {
    const oauth2Client = new google.auth.OAuth2();
    
    oauth2Client.setCredentials({
      access_token: tokenSet.accessToken,
      refresh_token: tokenSet.refreshToken,
      expiry_date: tokenSet.expiresAt
    });

    return oauth2Client;
  }

  async getUserGoogleProperties(
    userId: string, 
    projectId: string
  ): Promise<GoogleAnalyticsProperty[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'analytics'
        );
        
        const auth = this.createOAuthClient(tokenSet);
        const analyticsAdmin = google.analyticsadmin({ version: 'v1alpha', auth });

        const accountsResponse = await analyticsAdmin.accounts.list();
        const properties: GoogleAnalyticsProperty[] = [];

        if (accountsResponse.data.accounts) {
          for (const account of accountsResponse.data.accounts) {
            try {
              const propertiesResponse = await analyticsAdmin.properties.list({
                filter: `parent:${account.name}`
              });

              if (propertiesResponse.data.properties) {
                for (const property of propertiesResponse.data.properties) {
                  properties.push({
                    propertyId: property.name?.split('/').pop() || '',
                    displayName: property.displayName || '',
                    websiteUrl: '', // GA4 Admin API doesn't provide websiteUrl
                    timeZone: property.timeZone || '',
                    currencyCode: property.currencyCode || '',
                    accountId: account.name?.split('/').pop() || '',
                    accountDisplayName: account.displayName || ''
                  });
                }
              }
            } catch (propertyError) {
              console.warn('Failed to fetch properties for account:', account.name, propertyError);
            }
          }
        }

        return properties;
      },
      userId,
      projectId,
      'analytics',
      { maxRetries: 2 }
    );
  }

  async getUserAnalyticsData(
    userId: string,
    projectId: string,
    propertyId: string,
    dateRange: DateRange,
    dimensions: string[] = ['date'],
    metrics: string[] = ['activeUsers', 'sessions', 'screenPageViews']
  ): Promise<AnalyticsData[]> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'analytics'
        );

        const auth = this.createOAuthClient(tokenSet);
        const analyticsDataApi = google.analyticsdata({ version: 'v1beta', auth });

        const response = await analyticsDataApi.properties.runReport({
          property: `properties/${propertyId}`,
          requestBody: {
            dateRanges: [
              {
                startDate: dateRange.startDate,
                endDate: dateRange.endDate
              }
            ],
            dimensions: dimensions.map(name => ({ name })),
            metrics: metrics.map(name => ({ name }))
          }
        });

        const analyticsData: AnalyticsData[] = [];

        if (response.data.rows) {
          for (const row of response.data.rows) {
            const dimensionValues = row.dimensionValues || [];
            const metricValues = row.metricValues || [];

            // Extract dimension values
            const dateValue = dimensionValues[0]?.value || '';
            const sourceValue = dimensionValues[1]?.value || 'direct';
            const mediumValue = dimensionValues[2]?.value || 'none';
            const campaignValue = dimensionValues[3]?.value || '';
            const pageValue = dimensionValues[4]?.value || '';

            // Extract metric values
            const users = parseInt(metricValues[0]?.value || '0');
            const sessions = parseInt(metricValues[1]?.value || '0');
            const pageviews = parseInt(metricValues[2]?.value || '0');
            const bounceRate = parseFloat(metricValues[3]?.value || '0');
            const avgSessionDuration = parseFloat(metricValues[4]?.value || '0');

            analyticsData.push({
              date: dateValue,
              users,
              sessions,
              pageviews,
              bounceRate,
              avgSessionDuration,
              source: sourceValue,
              medium: mediumValue,
              campaign: campaignValue || undefined,
              page: pageValue || undefined
            });
          }
        }

        return analyticsData;
      },
      userId,
      projectId,
      'analytics',
      { maxRetries: 2 }
    );
  }

  async testConnection(
    userId: string, 
    projectId: string
  ): Promise<{ 
    isValid: boolean; 
    error?: string; 
    hasAnalytics: boolean; 
    properties?: GoogleAnalyticsProperty[];
  }> {
    try {
      const tokenSet = await oauthTokenManager.getValidTokenSet(
        userId, 
        projectId, 
        'analytics'
      );

      const auth = this.createOAuthClient(tokenSet);
      
      // Test Analytics access by listing accounts
      const analyticsAdmin = google.analyticsadmin({ version: 'v1alpha', auth });
      await analyticsAdmin.accounts.list();

      // If successful, get properties for additional verification
      const properties = await this.getUserGoogleProperties(userId, projectId);

      return {
        isValid: true,
        hasAnalytics: true,
        properties
      };
    } catch (error: any) {
      console.error('Analytics connection test failed:', error);
      
      const userMessage = OAuthErrorHandler.getUserMessage(error, 'Google Analytics');
      
      return {
        isValid: false,
        hasAnalytics: false,
        error: userMessage
      };
    }
  }

  // Get real-time analytics data
  async getRealtimeData(
    userId: string,
    projectId: string,
    propertyId: string,
    metrics: string[] = ['activeUsers']
  ): Promise<any> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'analytics'
        );

        const auth = this.createOAuthClient(tokenSet);
        const analyticsDataApi = google.analyticsdata({ version: 'v1beta', auth });

        const response = await analyticsDataApi.properties.runRealtimeReport({
          property: `properties/${propertyId}`,
          requestBody: {
            metrics: metrics.map(name => ({ name }))
          }
        });

        return response.data;
      },
      userId,
      projectId,
      'analytics',
      { maxRetries: 1 } // Fewer retries for real-time data
    );
  }

  // Get audience insights
  async getAudienceInsights(
    userId: string,
    projectId: string,
    propertyId: string,
    dateRange: DateRange
  ): Promise<any> {
    return await OAuthErrorHandler.handleWithRetry(
      async () => {
        const tokenSet = await oauthTokenManager.getValidTokenSet(
          userId, 
          projectId, 
          'analytics'
        );

        const auth = this.createOAuthClient(tokenSet);
        const analyticsDataApi = google.analyticsdata({ version: 'v1beta', auth });

        const response = await analyticsDataApi.properties.runReport({
          property: `properties/${propertyId}`,
          requestBody: {
            dateRanges: [dateRange],
            dimensions: [
              { name: 'country' },
              { name: 'city' },
              { name: 'deviceCategory' },
              { name: 'operatingSystem' }
            ],
            metrics: [
              { name: 'activeUsers' },
              { name: 'sessions' },
              { name: 'bounceRate' },
              { name: 'sessionDuration' }
            ]
          }
        });

        return response.data;
      },
      userId,
      projectId,
      'analytics',
      { maxRetries: 2 }
    );
  }

  // Check OAuth status for Analytics
  async checkOAuthStatus(
    userId: string,
    projectId: string
  ): Promise<{
    authorized: boolean;
    scopes: string[];
    expiresAt?: number;
    error?: string;
  }> {
    try {
      const status = await oauthTokenManager.getOAuthStatus(userId, projectId);
      return status.analytics;
    } catch (error) {
      console.error('Failed to check OAuth status:', error);
      return {
        authorized: false,
        scopes: [],
        error: 'Failed to check authorization status'
      };
    }
  }

  // Revoke OAuth access
  async revokeAccess(
    userId: string,
    projectId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await oauthTokenManager.revokeTokenSet(userId, projectId, 'analytics');
      return { success: true };
    } catch (error: any) {
      console.error('Failed to revoke Analytics access:', error);
      return { 
        success: false, 
        error: error.message || 'Failed to revoke access' 
      };
    }
  }
}

export const enhancedGoogleAnalyticsService = new EnhancedGoogleAnalyticsService();