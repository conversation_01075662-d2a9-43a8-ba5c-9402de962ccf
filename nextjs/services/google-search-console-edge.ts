import { getUserGoogleSearchConsoleConfig } from '@/models/user-configs';
import { getCanonicalDomain } from '@/utils/url-normalization';

interface BacklinkData {
  url: string;
  title: string;
  domain: string;
  anchorText: string;
  link_type: 'dofollow' | 'nofollow';
  discoveredAt: string;
  sourceUrl: string;
  isActive: boolean;
  dr_score?: number;
  traffic?: number;
}

interface GoogleSearchConsoleConfig {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

interface SearchConsoleSearchAnalyticsRequest {
  startDate: string;
  endDate: string;
  dimensions?: string[];
  searchType?: 'web' | 'image' | 'video';
  dimensionFilterGroups?: any[];
  aggregationType?: 'auto' | 'byProperty' | 'byPage';
  rowLimit?: number;
  startRow?: number;
}

interface SearchConsoleResponse {
  rows?: Array<{
    keys: string[];
    clicks: number;
    impressions: number;
    ctr: number;
    position: number;
  }>;
}

// Base64 URL encode function
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// Edge Runtime compatible JWT token generator using Web Crypto API
async function createJWTToken(serviceAccountConfig: GoogleSearchConsoleConfig): Promise<string> {
  const now = Math.floor(Date.now() / 1000);
  
  // JWT header
  const header = {
    alg: 'RS256',
    typ: 'JWT'
  };

  // JWT payload
  const payload = {
    iss: serviceAccountConfig.client_email,
    scope: 'https://www.googleapis.com/auth/webmasters.readonly',
    aud: 'https://oauth2.googleapis.com/token',
    exp: now + 3600,
    iat: now,
  };

  // Encode header and payload
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  // Create the signing input
  const signingInput = `${encodedHeader}.${encodedPayload}`;

  // Clean and parse the private key
  const privateKeyPem = serviceAccountConfig.private_key.replace(/\\n/g, '\n');
  const privateKeyBase64 = privateKeyPem
    .replace(/-----BEGIN PRIVATE KEY-----/, '')
    .replace(/-----END PRIVATE KEY-----/, '')
    .replace(/\s/g, '');

  // Convert base64 to ArrayBuffer
  const binaryString = atob(privateKeyBase64);
  const keyBuffer = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    keyBuffer[i] = binaryString.charCodeAt(i);
  }

  // Import the private key
  const cryptoKey = await crypto.subtle.importKey(
    'pkcs8',
    keyBuffer,
    {
      name: 'RSASSA-PKCS1-v1_5',
      hash: 'SHA-256',
    },
    false,
    ['sign']
  );

  // Sign the JWT
  const encoder = new TextEncoder();
  const signature = await crypto.subtle.sign(
    'RSASSA-PKCS1-v1_5',
    cryptoKey,
    encoder.encode(signingInput)
  );

  // Encode the signature
  const signatureArray = new Uint8Array(signature);
  const signatureBase64 = base64UrlEncode(
    String.fromCharCode.apply(null, Array.from(signatureArray))
  );

  // Return the complete JWT
  return `${signingInput}.${signatureBase64}`;
}

// Get OAuth2 access token using JWT
async function getAccessToken(serviceAccountConfig: GoogleSearchConsoleConfig): Promise<string> {
  const jwt = await createJWTToken(serviceAccountConfig);
  
  const response = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
      assertion: jwt,
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to get access token: ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

// Make API call to Search Console
async function makeSearchConsoleAPICall(
  endpoint: string,
  accessToken: string,
  method: 'GET' | 'POST' = 'GET',
  body?: any
): Promise<any> {
  const url = `https://searchconsole.googleapis.com/webmasters/v3/${endpoint}`;
  
  const options: RequestInit = {
    method,
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
  };

  if (body && method === 'POST') {
    options.body = JSON.stringify(body);
  }

  const response = await fetch(url, options);

  if (!response.ok) {
    throw new Error(`Search Console API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

// Edge-compatible Google Search Console service
export const googleSearchConsoleServiceEdge = {
  // Test API connection with stored configuration
  async testConnection(userId: string): Promise<{ success: boolean; message: string }> {
    try {
      const { data: config } = await getUserGoogleSearchConsoleConfig(userId, '');
      if (!config) {
        return { success: false, message: 'No Google Search Console configuration found' };
      }

      const serviceAccountConfig = config.configData as GoogleSearchConsoleConfig;
      return await this.testConfiguration(serviceAccountConfig);
    } catch (error) {
      console.error('Google Search Console test connection error:', error);
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  },

  // Test API connection with provided configuration
  async testConfiguration(configData: GoogleSearchConsoleConfig): Promise<{ success: boolean; message: string }> {
    try {
      const accessToken = await getAccessToken(configData);
      
      // Test by getting sites list
      await makeSearchConsoleAPICall('sites', accessToken);
      
      return { success: true, message: 'Connection successful' };
    } catch (error) {
      console.error('Google Search Console test configuration error:', error);
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  },

  // Get list of sites
  async getSites(userId: string): Promise<string[]> {
    try {
      const { data: config } = await getUserGoogleSearchConsoleConfig(userId, '');
      if (!config) {
        throw new Error('No Google Search Console configuration found');
      }

      const serviceAccountConfig = config.configData as GoogleSearchConsoleConfig;
      const accessToken = await getAccessToken(serviceAccountConfig);
      
      const response = await makeSearchConsoleAPICall('sites', accessToken);
      
      return response.siteEntry?.map((site: any) => site.siteUrl) || [];
    } catch (error) {
      console.error('Error getting Search Console sites:', error);
      throw error;
    }
  },

  // Get search analytics data
  async getSearchAnalytics(
    userId: string,
    siteUrl: string,
    request: SearchConsoleSearchAnalyticsRequest
  ): Promise<SearchConsoleResponse> {
    try {
      const { data: config } = await getUserGoogleSearchConsoleConfig(userId, '');
      if (!config) {
        throw new Error('No Google Search Console configuration found');
      }

      const serviceAccountConfig = config.configData as GoogleSearchConsoleConfig;
      const accessToken = await getAccessToken(serviceAccountConfig);
      
      const endpoint = `sites/${encodeURIComponent(siteUrl)}/searchAnalytics/query`;
      
      return await makeSearchConsoleAPICall(endpoint, accessToken, 'POST', request);
    } catch (error) {
      console.error('Error getting search analytics:', error);
      throw error;
    }
  },

  // Get backlinks from Search Console (linking sites)
  async getBacklinksFromSearchConsole(userId: string, domain: string): Promise<BacklinkData[]> {
    try {
      const { data: config } = await getUserGoogleSearchConsoleConfig(userId, '');
      if (!config) {
        throw new Error('No Google Search Console configuration found');
      }

      const canonicalDomain = getCanonicalDomain(domain);
      const serviceAccountConfig = config.configData as GoogleSearchConsoleConfig;
      const accessToken = await getAccessToken(serviceAccountConfig);
      
      // Get sites to find the correct property URL format
      const sites = await this.getSites(userId);
      const siteUrl = sites.find(site => 
        site.includes(canonicalDomain) || 
        getCanonicalDomain(site) === canonicalDomain
      );

      if (!siteUrl) {
        throw new Error(`Domain ${domain} not found in Search Console properties`);
      }

      // Get search analytics data for external links
      const searchRequest: SearchConsoleSearchAnalyticsRequest = {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
        dimensions: ['page', 'query'],
        searchType: 'web',
        rowLimit: 1000
      };

      const searchData = await this.getSearchAnalytics(userId, siteUrl, searchRequest);
      
      // Transform search console data to backlink format
      const backlinks: BacklinkData[] = [];
      
      if (searchData.rows) {
        for (const row of searchData.rows) {
          const [pageUrl, query] = row.keys;
          
          backlinks.push({
            url: pageUrl,
            title: `Search result for: ${query}`,
            domain: new URL(pageUrl).hostname,
            anchorText: query,
            link_type: 'dofollow' as const,
            discoveredAt: new Date().toISOString(),
            sourceUrl: 'https://search.google.com',
            isActive: true,
            traffic: row.clicks,
            dr_score: Math.round(row.position)
          });
        }
      }

      return backlinks;
    } catch (error) {
      console.error('Error getting backlinks from Search Console:', error);
      throw error;
    }
  }
};