import { Items } from '@/types/items';

export async function getPublicItems(page = 1, limit = 9, filters?: {
  tags?: string[];
  is_official?: boolean;
  is_recommended?: boolean;
  lang?: string;
}) {
  try {
    const searchParams = new URLSearchParams();
    searchParams.append('page', page.toString());
    searchParams.append('limit', limit.toString());
    
    // Add filters to query parameters
    if (filters) {
      // Add tags if provided
      if (filters.tags && filters.tags.length > 0) {
        filters.tags.forEach(tag => searchParams.append('tags', tag));
      }
      
      // Add boolean filters if provided
      if (filters.is_official !== undefined) {
        searchParams.append('is_official', filters.is_official.toString());
      }
      
      if (filters.is_recommended !== undefined) {
        searchParams.append('is_recommended', filters.is_recommended.toString());
      }
      
      // Add language if provided
      if (filters.lang) {
        searchParams.append('lang', filters.lang);
      }
      
      // If we have any tags, use the tags endpoint
      if (filters.tags && filters.tags.length > 0) {
        const response = await fetch(`/api/items/tags?${searchParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch Items by tags');
        }
        
        const count = response.headers.get('X-Total-Count');
        const data = await response.json();
        
        return { 
          data, 
          count: count ? parseInt(count, 10) : data.length 
        };
      }
    }
    
    // If no tags or if all filters are undefined, use the regular public endpoint
    const response = await fetch(`/api/items/public?${searchParams.toString()}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch public Items');
    }
    
    return response.json();
  } catch (error) {
    console.error('Error in getPublicItems:', error);
    return { data: [], count: 0 };
  }
}

export async function getItemsTags() {
  const response = await fetch(`/api/items/tags`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch Item tags');
  }

  console.log("getItemsTags", response.json())
  
  return response.json();
}

export async function getItemByUuid(uuid: string) {
  const response = await fetch(`/api/items/${uuid}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch Item by UUID');
  }
  
  return response.json();
}

export async function incrementItemClicks(uuid: string) {
  const response = await fetch(`/api/items/${uuid}/click`, {
    method: 'POST'
  });
  
  if (!response.ok) {
    throw new Error('Failed to increment Item clicks');
  }
  
  return true;
}

export async function searchItems(query: string, lang = 'en', page = 1, limit = 50) {
  const searchParams = new URLSearchParams();
  searchParams.append('q', query);
  searchParams.append('lang', lang);
  searchParams.append('page', page.toString());
  searchParams.append('limit', limit.toString());
  
  const response = await fetch(`/api/items/search?${searchParams.toString()}`);
  
  if (!response.ok) {
    throw new Error('Failed to search Items');
  }
  
  return response.json();
}

export async function getItemsCount() {
  const response = await fetch('/api/items/count');
  
  if (!response.ok) {
    throw new Error('Failed to get Items count');
  }
  
  return response.json();
}

export async function getTagCounts(tags?: string[]) {
  let url = '/api/items/tags/count';
  
  if (tags && tags.length > 0) {
    const searchParams = new URLSearchParams();
    tags.forEach(tag => searchParams.append('tags', tag));
    url += `?${searchParams.toString()}`;
  }
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error('Failed to fetch tag counts');
  }
  
  return response.json();
} 