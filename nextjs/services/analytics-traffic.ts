/**
 * Analytics Traffic Service
 * Collects traffic contribution data from various analytics platforms (Plausible, Google Analytics, Umami)
 * to track how much traffic each discovered link contributes to the website
 */

interface TrafficContribution {
  referral_traffic: number;
  analytics_source: 'plausible' | 'google-analytics' | 'umami' | 'manual';
  traffic_period: string;
  last_traffic_update: string;
  traffic_contribution_percentage?: number;
}

interface PlausibleReferrerData {
  referrer: string;
  visitors: number;
  pageviews: number;
  bounce_rate: number;
}

interface GoogleAnalyticsReferrerData {
  source: string;
  medium: string;
  sessions: number;
  users: number;
  pageviews: number;
}

/**
 * Fetch traffic data from Plausible Analytics
 */
export async function fetchPlausibleTrafficContribution(
  domain: string,
  referrerDomain: string,
  period: string = 'last_30_days',
  apiKey?: string
): Promise<TrafficContribution | null> {
  if (!apiKey) {
    console.warn('Plausible API key not provided');
    return null;
  }

  try {
    const baseUrl = 'https://plausible.io/api/v1/stats';
    const params = new URLSearchParams({
      site_id: domain,
      period: period,
      metrics: 'visitors,pageviews',
      filters: `referrer==${referrerDomain}`,
    });

    const response = await fetch(`${baseUrl}/breakdown?${params}`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Plausible API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Extract referrer data
    const referrerData = data.results?.find((item: PlausibleReferrerData) => 
      item.referrer.includes(referrerDomain)
    );

    if (!referrerData) {
      return {
        referral_traffic: 0,
        analytics_source: 'plausible',
        traffic_period: period,
        last_traffic_update: new Date().toISOString(),
        traffic_contribution_percentage: 0,
      };
    }

    // Get total site traffic for percentage calculation
    const totalTrafficResponse = await fetch(`${baseUrl}/aggregate?${new URLSearchParams({
      site_id: domain,
      period: period,
      metrics: 'pageviews',
    })}`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    let totalTraffic = 1; // Avoid division by zero
    if (totalTrafficResponse.ok) {
      const totalData = await totalTrafficResponse.json();
      totalTraffic = totalData.results?.pageviews || 1;
    }

    const contributionPercentage = (referrerData.pageviews / totalTraffic) * 100;

    return {
      referral_traffic: referrerData.pageviews || referrerData.visitors || 0,
      analytics_source: 'plausible',
      traffic_period: period,
      last_traffic_update: new Date().toISOString(),
      traffic_contribution_percentage: contributionPercentage,
    };

  } catch (error) {
    console.error('Error fetching Plausible traffic data:', error);
    return null;
  }
}

/**
 * Fetch traffic data from Google Analytics
 */
export async function fetchGoogleAnalyticsTrafficContribution(
  propertyId: string,
  referrerDomain: string,
  period: string = 'last_30_days',
  accessToken?: string
): Promise<TrafficContribution | null> {
  if (!accessToken) {
    console.warn('Google Analytics access token not provided');
    return null;
  }

  try {
    // Convert period to GA4 date format
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'last_7_days':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'last_30_days':
      default:
        startDate.setDate(endDate.getDate() - 30);
        break;
    }

    const dateRange = {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    };

    // GA4 Data API request
    const requestBody = {
      dateRanges: [dateRange],
      dimensions: [
        { name: 'sessionSource' },
        { name: 'sessionMedium' }
      ],
      metrics: [
        { name: 'sessions' },
        { name: 'totalUsers' },
        { name: 'screenPageViews' }
      ],
      dimensionFilter: {
        filter: {
          fieldName: 'sessionSource',
          stringFilter: {
            matchType: 'CONTAINS',
            value: referrerDomain
          }
        }
      }
    };

    const response = await fetch(
      `https://analyticsdata.googleapis.com/v1beta/properties/${propertyId}:runReport`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      throw new Error(`Google Analytics API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    const referrerSessions = data.rows?.reduce((total: number, row: any) => {
      return total + parseInt(row.metricValues[0].value || '0');
    }, 0) || 0;

    const referrerPageviews = data.rows?.reduce((total: number, row: any) => {
      return total + parseInt(row.metricValues[2].value || '0');
    }, 0) || 0;

    // Get total site traffic for percentage calculation
    const totalRequestBody = {
      dateRanges: [dateRange],
      metrics: [{ name: 'screenPageViews' }]
    };

    const totalResponse = await fetch(
      `https://analyticsdata.googleapis.com/v1beta/properties/${propertyId}:runReport`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(totalRequestBody),
      }
    );

    let totalTraffic = 1;
    if (totalResponse.ok) {
      const totalData = await totalResponse.json();
      totalTraffic = parseInt(totalData.rows?.[0]?.metricValues?.[0]?.value || '1');
    }

    const contributionPercentage = (referrerPageviews / totalTraffic) * 100;

    return {
      referral_traffic: referrerPageviews,
      analytics_source: 'google-analytics',
      traffic_period: period,
      last_traffic_update: new Date().toISOString(),
      traffic_contribution_percentage: contributionPercentage,
    };

  } catch (error) {
    console.error('Error fetching Google Analytics traffic data:', error);
    return null;
  }
}

/**
 * Fetch traffic data from Umami Analytics
 */
export async function fetchUmamiTrafficContribution(
  websiteId: string,
  referrerDomain: string,
  period: string = 'last_30_days',
  apiKey?: string,
  baseUrl: string = 'https://analytics.umami.is'
): Promise<TrafficContribution | null> {
  if (!apiKey) {
    console.warn('Umami API key not provided');
    return null;
  }

  try {
    // Convert period to date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'last_7_days':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'last_30_days':
      default:
        startDate.setDate(endDate.getDate() - 30);
        break;
    }

    const params = new URLSearchParams({
      startAt: startDate.getTime().toString(),
      endAt: endDate.getTime().toString(),
      unit: 'day',
    });

    // Get referrer data
    const response = await fetch(
      `${baseUrl}/api/websites/${websiteId}/stats?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Umami API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Umami API structure may vary, adjust based on actual API response
    const referrerTraffic = data.referrers?.find((ref: any) => 
      ref.x.includes(referrerDomain)
    )?.y || 0;

    const totalTraffic = data.pageviews?.value || 1;
    const contributionPercentage = (referrerTraffic / totalTraffic) * 100;

    return {
      referral_traffic: referrerTraffic,
      analytics_source: 'umami',
      traffic_period: period,
      last_traffic_update: new Date().toISOString(),
      traffic_contribution_percentage: contributionPercentage,
    };

  } catch (error) {
    console.error('Error fetching Umami traffic data:', error);
    return null;
  }
}

/**
 * Main function to update traffic contribution for discovered links
 */
export async function updateDiscoveredLinkTrafficContribution(
  projectId: string,
  linkId: string,
  analyticsConfig: {
    platform: 'plausible' | 'google-analytics' | 'umami';
    apiKey?: string;
    propertyId?: string; // For Google Analytics
    websiteId?: string; // For Umami
    domain: string;
  },
  period: string = 'last_30_days'
): Promise<TrafficContribution | null> {
  const { platform, domain } = analyticsConfig;

  try {
    // Get the discovered link to extract referrer domain
    const response = await fetch(`/api/projects/${projectId}/discovered-links/${linkId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch discovered link data');
    }

    const linkData = await response.json();
    const referrerDomain = new URL(linkData.url).hostname;

    let trafficData: TrafficContribution | null = null;

    switch (platform) {
      case 'plausible':
        trafficData = await fetchPlausibleTrafficContribution(
          domain,
          referrerDomain,
          period,
          analyticsConfig.apiKey
        );
        break;

      case 'google-analytics':
        trafficData = await fetchGoogleAnalyticsTrafficContribution(
          analyticsConfig.propertyId!,
          referrerDomain,
          period,
          analyticsConfig.apiKey
        );
        break;

      case 'umami':
        trafficData = await fetchUmamiTrafficContribution(
          analyticsConfig.websiteId!,
          referrerDomain,
          period,
          analyticsConfig.apiKey
        );
        break;

      default:
        throw new Error(`Unsupported analytics platform: ${platform}`);
    }

    if (trafficData) {
      // Update the discovered link with traffic contribution data
      const updateResponse = await fetch(`/api/projects/${projectId}/discovered-links/${linkId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          referral_traffic: trafficData.referral_traffic,
          analytics_source: trafficData.analytics_source,
          traffic_period: trafficData.traffic_period,
          last_traffic_update: trafficData.last_traffic_update,
          traffic_contribution_percentage: trafficData.traffic_contribution_percentage,
        }),
      });

      if (!updateResponse.ok) {
        throw new Error('Failed to update discovered link with traffic data');
      }
    }

    return trafficData;

  } catch (error) {
    console.error('Error updating traffic contribution:', error);
    return null;
  }
}

/**
 * Batch update traffic contribution for all discovered links in a project
 */
export async function batchUpdateProjectTrafficContribution(
  projectId: string,
  analyticsConfig: {
    platform: 'plausible' | 'google-analytics' | 'umami';
    apiKey?: string;
    propertyId?: string;
    websiteId?: string;
    domain: string;
  },
  period: string = 'last_30_days'
): Promise<{ updated: number; failed: number; errors: string[] }> {
  const results = { updated: 0, failed: 0, errors: [] as string[] };

  try {
    // Get all discovered links for the project
    const response = await fetch(`/api/projects/${projectId}/discovered-links`);
    if (!response.ok) {
      throw new Error('Failed to fetch discovered links');
    }

    const { links } = await response.json();

    // Update traffic contribution for each link
    for (const link of links) {
      try {
        const trafficData = await updateDiscoveredLinkTrafficContribution(
          projectId,
          link.id,
          analyticsConfig,
          period
        );

        if (trafficData) {
          results.updated++;
        } else {
          results.failed++;
          results.errors.push(`Failed to get traffic data for ${link.domain}`);
        }

      } catch (error) {
        results.failed++;
        results.errors.push(`Error updating ${link.domain}: ${error}`);
      }
    }

  } catch (error) {
    results.errors.push(`Batch update failed: ${error}`);
  }

  return results;
}