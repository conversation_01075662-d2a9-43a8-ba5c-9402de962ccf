interface AnalyticsConfig {
  provider: 'google' | 'plausible' | 'umami';
  api_key: string;
  website_id: string;
  base_url?: string;
  domain: string;
}

interface TestResult {
  isValid: boolean;
  error?: string;
  data?: any;
}

// Google Analytics 测试连接
async function testGoogleAnalytics(config: AnalyticsConfig): Promise<TestResult> {
  try {
    // 验证 JSON 格式
    let credentials;
    try {
      credentials = JSON.parse(config.api_key);
    } catch (e) {
      return { isValid: false, error: "API密钥格式错误，请提供有效的JSON格式服务账号密钥" };
    }

    // 验证必要字段
    const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
    for (const field of requiredFields) {
      if (!credentials[field]) {
        return { isValid: false, error: `服务账号密钥缺少必要字段: ${field}` };
      }
    }

    // 如果需要安装 @google-analytics/data 包，可以在生产环境中使用真实的 API 调用
    // 这里进行基本的格式验证和模拟测试
    if (credentials.type !== 'service_account') {
      return { isValid: false, error: "请提供服务账号类型的密钥" };
    }

    // 模拟 API 调用测试（生产环境中应该调用真实的 Google Analytics API）
    // const { BetaAnalyticsDataClient } = require('@google-analytics/data');
    // const analyticsDataClient = new BetaAnalyticsDataClient({ credentials });
    // await analyticsDataClient.runReport({ property: `properties/${config.website_id}` });

    return { 
      isValid: true, 
      data: { 
        client_email: credentials.client_email,
        project_id: credentials.project_id 
      } 
    };
  } catch (error: any) {
    return { isValid: false, error: error.message || "Google Analytics 连接测试失败" };
  }
}

// Helper function to create fetch with timeout
async function fetchWithTimeout(url: string, options: RequestInit = {}, timeoutMs: number = 10000): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error: any) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或API服务可用性');
    }
    throw error;
  }
}

// Plausible Analytics 测试连接
async function testPlausibleAnalytics(config: AnalyticsConfig): Promise<TestResult> {
  try {
    const baseUrl = config.base_url || 'https://plausible.io';
    console.log(`Testing Plausible connection to: ${baseUrl}`);
    
    // Ensure API key contains only valid characters and encode properly
    const cleanApiKey = config.api_key.trim();
    if (!cleanApiKey) {
      return { isValid: false, error: "API Token 不能为空" };
    }
    
    // Validate API key format (basic validation)
    if (cleanApiKey.includes(' ') || cleanApiKey.length < 10) {
      return { isValid: false, error: "API Token 格式无效，请检查是否包含特殊字符或过短" };
    }
    
    // Test basic API access with the specific site_id
    const apiUrl = `${baseUrl}/api/v1/stats/aggregate?site_id=${encodeURIComponent(config.website_id)}&period=day&metrics=visitors`;
    console.log(`Plausible API test URL: ${apiUrl}`);
    
    let response;
    try {
      response = await fetchWithTimeout(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${cleanApiKey}`,
          'Content-Type': 'application/json'
        }
      }, 10000);
    } catch (fetchError: any) {
      console.error('Plausible API fetch error:', fetchError.message);
      return { isValid: false, error: `连接失败: ${fetchError.message}` };
    }

    console.log(`Plausible API response status: ${response.status}`);

    if (response.status === 401) {
      return { isValid: false, error: "API Token 无效或已过期" };
    }

    if (response.status === 402) {
      return { isValid: false, error: "API 访问需要付费计划" };
    }

    if (response.status === 403) {
      return { isValid: false, error: "API Token 权限不足" };
    }

    if (response.status === 404) {
      return { isValid: false, error: `网站 ${config.website_id} 不存在或无访问权限。请检查网站ID是否正确。` };
    }

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error(`Plausible API error response: ${errorText}`);
      
      // Try to parse error message from Plausible API
      try {
        const errorData = JSON.parse(errorText);
        if (errorData.error) {
          return { isValid: false, error: `Plausible API 错误: ${errorData.error}` };
        }
      } catch (parseError) {
        // Ignore JSON parse errors, use fallback message
      }
      
      return { isValid: false, error: `API 调用失败: ${response.status} ${response.statusText}` };
    }

    // If we get here, the API call was successful
    let responseData;
    try {
      responseData = await response.json();
      console.log('Plausible API response:', responseData);
    } catch (jsonError) {
      console.error('Plausible API JSON parse error:', jsonError);
      // Even if we can't parse the response, the API call succeeded
      return { isValid: true, data: { website_id: config.website_id, base_url: baseUrl, note: 'API连接成功，但响应格式异常' } };
    }

    console.log('Plausible connection test successful');
    return { isValid: true, data: { website_id: config.website_id, base_url: baseUrl } };
  } catch (error: any) {
    console.error('Plausible connection test error:', error);
    return { isValid: false, error: error.message || "Plausible Analytics 连接测试失败" };
  }
}

// Umami Analytics 测试连接
async function testUmamiAnalytics(config: AnalyticsConfig): Promise<TestResult> {
  try {
    if (!config.base_url) {
      return { isValid: false, error: "请提供 Umami 实例的 URL" };
    }

    console.log(`Testing Umami connection to: ${config.base_url}`);

    // Multiple authentication methods for different Umami versions
    const authMethods = [];
    
    // If api_key looks like username:password, try login authentication first
    if (config.api_key && config.api_key.includes(':')) {
      const [username, password] = config.api_key.split(':');
      authMethods.push({
        type: 'login',
        username,
        password,
        description: 'Username/password login (self-hosted)'
      });
    }
    
    // Standard API token methods
    authMethods.push(
      // Umami v2+ with API token
      {
        type: 'header',
        header: 'Authorization',
        value: `Bearer ${config.api_key}`,
        description: 'Bearer token (v2+)'
      },
      // Umami v1 with API key header
      {
        type: 'header',
        header: 'x-umami-api-key',
        value: config.api_key,
        description: 'API key header (v1)'
      }
    );

    // Multiple possible API endpoints for different Umami versions
    const possibleEndpoints = [
      `${config.base_url}/api/websites`,        // Common endpoint
      `${config.base_url}/api/website`,         // Alternative singular form
      `${config.base_url}/api/v1/websites`,     // Versioned endpoint
      `${config.base_url}/api/v2/websites`,     // v2 endpoint
      `${config.base_url}/dashboard/api/websites`, // Self-hosted with dashboard prefix
    ];
    
    console.log(`Testing Umami API endpoints: ${possibleEndpoints.join(', ')}`);
    
    let response;
    let websites;
    let lastError = '';
    
    // Try each authentication method with each endpoint
    for (const authMethod of authMethods) {
      for (const endpoint of possibleEndpoints) {
        try {
          console.log(`Testing Umami ${authMethod.description} with endpoint: ${endpoint}`);
          
          let requestHeaders: Record<string, string> = {
            'Content-Type': 'application/json'
          };
          
          // Handle login authentication for self-hosted instances
          if (authMethod.type === 'login') {
            // First, attempt to login and get session token
            try {
              const loginResponse = await fetchWithTimeout(`${config.base_url}/api/auth/login`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  username: authMethod.username,
                  password: authMethod.password
                })
              }, 10000);
              
              if (loginResponse.ok) {
                const loginData = await loginResponse.json();
                // Use session token or cookie from login response
                if (loginData.token) {
                  requestHeaders['Authorization'] = `Bearer ${loginData.token}`;
                } else {
                  // Try to extract session cookie
                  const cookies = loginResponse.headers.get('set-cookie');
                  if (cookies) {
                    requestHeaders['Cookie'] = cookies;
                  }
                }
              } else {
                console.log(`Login failed for ${authMethod.description}: ${loginResponse.status}`);
                continue; // Try next endpoint
              }
            } catch (loginError) {
              console.log(`Login error for ${authMethod.description}:`, loginError);
              continue; // Try next endpoint
            }
          } else {
            // Standard header authentication
            requestHeaders[authMethod.header] = authMethod.value;
          }
          
          response = await fetchWithTimeout(endpoint, {
            headers: requestHeaders
          }, 10000);

          console.log(`Umami API response status with ${authMethod.description} at ${endpoint}: ${response.status}`);

          if (response.ok) {
            // Successfully authenticated, try to parse response
            try {
              websites = await response.json();
              
              if (Array.isArray(websites)) {
                console.log(`Umami returned ${websites.length} websites with ${authMethod.description} at ${endpoint}`);
                
                // Check if specified website_id exists
                const website = websites.find((w: any) => w.id === config.website_id || w.website_id === config.website_id);
                if (!website) {
                  console.log(`Website ID ${config.website_id} not found in available websites`);
                  return { isValid: false, error: `Website ID ${config.website_id} 不存在或无访问权限` };
                }

                console.log('Umami connection test successful');
                return { 
                  isValid: true, 
                  data: { 
                    website_id: config.website_id, 
                    website_name: website.name || website.domain,
                    base_url: config.base_url,
                    auth_method: authMethod.description,
                    api_endpoint: endpoint
                  } 
                };
              }
            } catch (jsonError) {
              console.error('Umami API JSON parse error:', jsonError);
              lastError = "API 响应格式错误";
              continue; // Try next endpoint
            }
          } else if (response.status === 401) {
            lastError = `认证失败 (${authMethod.description} at ${endpoint})`;
            console.log(`Auth method ${authMethod.description} failed with 401 at ${endpoint}`);
            continue; // Try next endpoint
          } else if (response.status === 403) {
            lastError = `权限不足 (${authMethod.description} at ${endpoint})`;
            console.log(`Auth method ${authMethod.description} failed with 403 at ${endpoint}`);
            continue; // Try next endpoint
          } else if (response.status === 404) {
            lastError = `端点不存在 (${endpoint})`;
            console.log(`Endpoint ${endpoint} not found (404) with ${authMethod.description}`);
            continue; // Try next endpoint
          } else {
            const errorText = await response.text().catch(() => 'Unknown error');
            lastError = `API 调用失败: ${response.status} ${response.statusText} at ${endpoint}`;
            console.error(`Umami API error response with ${authMethod.description} at ${endpoint}:`, errorText);
            continue; // Try next endpoint
          }
        } catch (fetchError: any) {
          console.error(`Umami API fetch error with ${authMethod.description} at ${endpoint}:`, fetchError.message);
          lastError = `连接失败: ${fetchError.message}`;
          continue; // Try next endpoint
        }
      }
      
      // If we have found websites with any endpoint, break out of auth method loop
      if (websites && websites.length > 0) {
        break;
      }
    }

    // All authentication methods and endpoints failed
    let errorMessage = "无法连接到 Umami API。";
    
    if (lastError.includes('端点不存在') || lastError.includes('404')) {
      errorMessage += " API 端点问题：\n";
      errorMessage += "1. 自部署 Umami：尝试的所有 API 端点都返回 404\n";
      errorMessage += "2. 可能的原因：\n";
      errorMessage += "   - Umami 版本不支持 API 功能\n";
      errorMessage += "   - API 路径配置不同\n";
      errorMessage += "   - 需要额外的路径前缀\n";
      errorMessage += "3. 解决方案：\n";
      errorMessage += "   - 检查 Umami 版本是否支持 API\n";
      errorMessage += "   - 确认 API 路径（通常是 /api/websites）\n";
      errorMessage += "   - 对于自部署：可能需要联系管理员启用 API";
    } else if (lastError.includes('认证失败')) {
      errorMessage += " 认证问题：\n";
      errorMessage += "1. 官方 Umami Cloud：在 API 密钥字段输入生成的 API Token\n";
      errorMessage += "2. 自部署实例：\n";
      errorMessage += "   - 有 API Token：输入 Token\n";
      errorMessage += "   - 无 API Token：输入 用户名:密码 格式\n";
      errorMessage += "3. 确保 API 密钥格式正确（无额外空格）";
    } else if (lastError.includes('权限不足')) {
      errorMessage += " 权限问题：\n";
      errorMessage += "1. API Token 缺少网站访问权限\n";
      errorMessage += "2. 用户账户无法查看指定网站\n";
      errorMessage += "3. 联系 Umami 管理员分配适当权限";
    } else {
      errorMessage += ` 详细错误：${lastError}\n\n`;
      errorMessage += "常见解决方案：\n";
      errorMessage += "1. 检查 Umami 实例 URL 是否正确\n";
      errorMessage += "2. 确认网络连接正常\n";
      errorMessage += "3. 验证 Umami 版本支持 API\n";
      errorMessage += "4. 对于自部署：确认 API 功能已启用";
    }

    return { isValid: false, error: errorMessage };
  } catch (error: any) {
    console.error('Umami connection test error:', error);
    return { isValid: false, error: error.message || "Umami Analytics 连接测试失败" };
  }
}

// 主要的测试连接函数
export async function testAnalyticsConnection(config: AnalyticsConfig): Promise<TestResult> {
  switch (config.provider) {
    case 'google':
      return testGoogleAnalytics(config);
    case 'plausible':
      return testPlausibleAnalytics(config);
    case 'umami':
      return testUmamiAnalytics(config);
    default:
      return { isValid: false, error: "不支持的分析平台" };
  }
}

// 获取分析平台的网站列表（用于绑定 analytics_id）
export async function getAnalyticsWebsites(config: AnalyticsConfig): Promise<{ websites: any[], error?: string }> {
  try {
    switch (config.provider) {
      case 'plausible': {
        const baseUrl = config.base_url || 'https://plausible.io';
        const cleanApiKey = config.api_key.trim();
        
        // For Plausible, the /api/v1/sites endpoint might not be available in all versions
        // or might require specific permissions. Let's try multiple approaches.
        
        console.log('Plausible API call:', `${baseUrl}/api/v1/sites`);
        
        // Try multiple authentication methods and endpoints for Plausible
        const authMethods = [
          { header: 'Authorization', value: `Bearer ${cleanApiKey}` },
          { header: 'Authorization', value: `Token ${cleanApiKey}` },
          { header: 'X-API-Key', value: cleanApiKey }
        ];
        
        const possibleEndpoints = [
          `${baseUrl}/api/v1/sites`,
          `${baseUrl}/api/v2/sites`,
          `${baseUrl}/api/sites`
        ];
        
        let lastError = '';
        let sitesData = [];
        let response;
        
        // Try each authentication method with each endpoint
        for (const authMethod of authMethods) {
          for (const endpoint of possibleEndpoints) {
            try {
              console.log(`Plausible API call: ${endpoint} with ${authMethod.header}`);
              response = await fetchWithTimeout(endpoint, {
                headers: {
                  [authMethod.header]: authMethod.value,
                  'Content-Type': 'application/json'
                }
              }, 8000); // Shorter timeout for website fetching
              
              if (response.ok) {
                const data = await response.json();
                if (Array.isArray(data)) {
                  sitesData = data;
                  break; // Success, use this endpoint and auth method
                }
              } else if (response.status === 404) {
                // Try next endpoint
                continue;
              } else {
                // Other error, capture for debugging
                const errorText = await response.text();
                lastError = errorText;
                console.log(`Auth method ${authMethod.header} failed with status ${response.status}:`, errorText);
              }
            } catch (error: any) {
              lastError = error.message;
              console.log(`Auth method ${authMethod.header} failed with error:`, error.message);
              continue;
            }
          }
          
          // If we found sites data, break out of auth method loop
          if (sitesData.length > 0) {
            break;
          }
        }
        
        console.log('Plausible API response status:', response?.status);
        
        // If we couldn't get sites data, provide helpful error message but don't fail completely
        if (sitesData.length === 0) {
          console.log('Plausible API error response:', lastError);
          
          // Return empty array with detailed error for user guidance
          let errorMessage = "无法自动获取网站列表。";
          
          if (response?.status === 401) {
            errorMessage += " API Token 认证失败。请确保：\n";
            errorMessage += "1. API Token 在 Plausible 设置中正确创建\n";
            errorMessage += "2. Token 具有 'Sites:Read' 权限\n";
            errorMessage += "3. Token 格式正确（无额外空格）";
          } else if (response?.status === 404) {
            errorMessage += " 无法找到网站列表 API 端点。可能原因：\n";
            errorMessage += "1. Plausible 版本不支持此 API\n";
            errorMessage += "2. 自托管实例的 API 路径不同\n";
            errorMessage += "3. API Token 权限不足";
          } else {
            errorMessage += " 请检查：\n";
            errorMessage += "1. API Token 是否正确创建\n";
            errorMessage += "2. Token 权限是否包含网站访问\n";
            errorMessage += "3. Plausible 实例 URL 是否正确";
          }
          
          errorMessage += "\n\n您可以手动输入网站域名（如：example.com）。";
          
          return { 
            websites: [], 
            error: errorMessage
          };
        }
        
        console.log('Plausible sites response:', sitesData);
        
        return { websites: sitesData.map((site: any) => ({ id: site.domain, name: site.domain })) };
      }
      
      case 'umami': {
        if (!config.base_url) {
          return { websites: [], error: "请提供 Umami 实例 URL" };
        }
        
        try {
        // Multiple authentication methods for different Umami versions
        const authMethods = [];
        
        // If api_key looks like username:password, try login authentication first
        if (config.api_key && config.api_key.includes(':')) {
          const [username, password] = config.api_key.split(':');
          authMethods.push({
            type: 'login',
            username,
            password,
            description: 'Username/password login (self-hosted)'
          });
        }
        
        // Standard API token methods
        authMethods.push(
          // Umami v2+ with API token
          {
            type: 'header',
            header: 'Authorization',
            value: `Bearer ${config.api_key}`,
            description: 'Bearer token (v2+)'
          },
          // Umami v1 with API key header
          {
            type: 'header',
            header: 'x-umami-api-key',
            value: config.api_key,
            description: 'API key header (v1)'
          }
        );
        
        let lastError = '';
        let websites = [];
        let response;
        
        // Multiple possible API endpoints for different Umami versions
        const possibleEndpoints = [
          `${config.base_url}/api/websites`,        // Common endpoint
          `${config.base_url}/api/website`,         // Alternative singular form
          `${config.base_url}/api/v1/websites`,     // Versioned endpoint
          `${config.base_url}/api/v2/websites`,     // v2 endpoint
          `${config.base_url}/dashboard/api/websites`, // Self-hosted with dashboard prefix
        ];

        // Try each authentication method with each endpoint
        for (const authMethod of authMethods) {
          for (const endpoint of possibleEndpoints) {
            try {
              console.log(`Umami API call with ${authMethod.description} at ${endpoint}`);
              
              let requestHeaders: Record<string, string> = {
                'Content-Type': 'application/json'
              };
              
              // Handle login authentication for self-hosted instances
              if (authMethod.type === 'login') {
                // First, attempt to login and get session token
                try {
                  const loginResponse = await fetchWithTimeout(`${config.base_url}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                      username: authMethod.username,
                      password: authMethod.password
                    })
                  }, 8000);
                  
                  if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    // Use session token or cookie from login response
                    if (loginData.token) {
                      requestHeaders['Authorization'] = `Bearer ${loginData.token}`;
                    } else {
                      // Try to extract session cookie
                      const cookies = loginResponse.headers.get('set-cookie');
                      if (cookies) {
                        requestHeaders['Cookie'] = cookies;
                      }
                    }
                  } else {
                    console.log(`Login failed for ${authMethod.description}: ${loginResponse.status}`);
                    continue; // Try next endpoint
                  }
                } catch (loginError) {
                  console.log(`Login error for ${authMethod.description}:`, loginError);
                  continue; // Try next endpoint
                }
              } else {
                // Standard header authentication
                requestHeaders[authMethod.header] = authMethod.value;
              }
              
              response = await fetchWithTimeout(endpoint, {
                headers: requestHeaders
              }, 8000);
            
              if (response.ok) {
                const data = await response.json();
                if (Array.isArray(data)) {
                  websites = data;
                  console.log(`Successfully fetched ${websites.length} websites with ${authMethod.description} at ${endpoint}`);
                  break; // Success, use this endpoint and auth method
                }
              } else if (response.status === 401) {
                lastError = `认证失败 (${authMethod.description} at ${endpoint})`;
                console.log(`Auth method ${authMethod.description} failed with 401 at ${endpoint}`);
                continue; // Try next endpoint
              } else if (response.status === 403) {
                lastError = `权限不足 (${authMethod.description} at ${endpoint})`;
                console.log(`Auth method ${authMethod.description} failed with 403 at ${endpoint}`);
                continue; // Try next endpoint
              } else if (response.status === 404) {
                lastError = `端点不存在 (${endpoint})`;
                console.log(`Endpoint ${endpoint} not found (404) with ${authMethod.description}`);
                continue; // Try next endpoint
              } else {
                const errorText = await response.text().catch(() => 'Unknown error');
                lastError = `API 调用失败: ${response.status} ${response.statusText} at ${endpoint}`;
                console.log(`Auth method ${authMethod.description} failed with status ${response.status} at ${endpoint}:`, errorText);
                continue; // Try next endpoint
              }
            } catch (error: any) {
              lastError = `连接失败: ${error.message}`;
              console.log(`Auth method ${authMethod.description} failed with error at ${endpoint}:`, error.message);
              continue; // Try next endpoint
            }
          }
          
          // If we have found websites with any endpoint, break out of auth method loop
          if (websites.length > 0) {
            break;
          }
        }
        
        console.log('Umami API response status:', response?.status);
        
        // If we couldn't get websites data, provide helpful error message
        if (websites.length === 0) {
          console.log('Umami API error response:', lastError);
          
          let errorMessage = "无法获取网站列表。";
          
          if (lastError.includes('端点不存在') || lastError.includes('404')) {
            errorMessage += " API 端点问题：\n";
            errorMessage += "1. 自部署 Umami：尝试的所有 API 端点都返回 404\n";
            errorMessage += "2. 可能的原因：\n";
            errorMessage += "   - Umami 版本不支持 API 功能\n";
            errorMessage += "   - API 路径配置不同\n";
            errorMessage += "   - 需要额外的路径前缀\n";
            errorMessage += "3. 解决方案：\n";
            errorMessage += "   - 检查 Umami 版本是否支持 API\n";
            errorMessage += "   - 确认 API 路径（通常是 /api/websites）\n";
            errorMessage += "   - 对于自部署：可能需要联系管理员启用 API";
          } else if (lastError.includes('认证失败')) {
            errorMessage += " 认证问题：\n";
            errorMessage += "1. 官方 Umami Cloud：在 API 密钥字段输入生成的 API Token\n";
            errorMessage += "2. 自部署实例：\n";
            errorMessage += "   - 有 API Token：输入 Token\n";
            errorMessage += "   - 无 API Token：输入 用户名:密码 格式\n";
            errorMessage += "3. 确保 API 密钥格式正确（无额外空格）";
          } else if (lastError.includes('权限不足')) {
            errorMessage += " 权限问题：\n";
            errorMessage += "1. API Token 缺少网站访问权限\n";
            errorMessage += "2. 用户账户无法查看指定网站\n";
            errorMessage += "3. 联系 Umami 管理员分配适当权限";
          } else {
            errorMessage += ` 详细错误：${lastError}\n\n`;
            errorMessage += "常见解决方案：\n";
            errorMessage += "1. 检查 Umami 实例 URL 是否正确\n";
            errorMessage += "2. 确认网络连接正常\n";
            errorMessage += "3. 验证 Umami 版本支持 API\n";
            errorMessage += "4. 对于自部署：确认 API 功能已启用";
          }
          
          return { 
            websites: [], 
            error: errorMessage
          };
        }
        
        console.log('Umami websites response:', websites);
        
        return { 
          websites: websites.map((site: any) => ({ 
            id: site.id || site.website_id, 
            name: site.name || site.domain 
          })) 
        };
        } catch (error: any) {
          return { websites: [], error: `获取网站列表失败: ${error.message}` };
        }
      }
      
      case 'google': {
        // Google Analytics 需要通过 GA4 API 获取属性列表
        // 这里返回基本信息，实际应该调用 Google Analytics Admin API
        return { 
          websites: [{ 
            id: config.website_id, 
            name: `GA4 Property ${config.website_id}` 
          }] 
        };
      }
      
      default:
        return { websites: [], error: "不支持的分析平台" };
    }
  } catch (error: any) {
    return { websites: [], error: error.message || "获取网站列表失败" };
  }
}

export const analyticsService = {
  testConnection: testAnalyticsConnection,
  getWebsites: getAnalyticsWebsites
}; 