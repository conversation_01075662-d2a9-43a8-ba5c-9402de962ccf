import { UserCaseType } from '@/types/usercase'
import { getTweet } from 'react-tweet/api'

interface ExtractMediaContentResult {
  success: boolean
  data?: {
    type: UserCaseType
    title?: string
    description?: string
    authorName?: string
    authorAvatar?: string
    content?: string
    mediaUrl?: string
    id?: string
    imageUrls?: string[]
    videoUrls?: string[]
  }
  error?: string
}

interface JikePicture {
  picUrl: string;
  format: string;
}

/**
 * Extract media content from a URL
 * @param url URL to extract content from
 * @param type Optional type of content (twitter, youtube, jike)
 */
export async function extractMediaContent(
  url: string,
  type?: UserCaseType
): Promise<ExtractMediaContentResult> {
  try {
    // Determine the content type if not provided
    if (!type) {
      if (url.includes('twitter.com') || url.includes('x.com')) {
        type = 'twitter'
      } else if (url.includes('youtube.com') || url.includes('youtu.be')) {
        type = 'youtube'
      } else if (url.includes('okjike.com')) {
        type = 'jike'
      } else {
        return { success: false, error: 'Unsupported media type' }
      }
    }

    // Extract based on content type
    switch (type) {
      case 'twitter':
      case 'x':
        return await extractTwitterContent(url)
      case 'youtube':
        return await extractYoutubeContent(url)
      case 'jike':
        return await extractJikeContent(url)
      default:
        return { success: false, error: 'Unsupported media type' }
    }
  } catch (error: any) {
    console.error('Error extracting media content:', error)
    return { success: false, error: error.message || 'Failed to extract content' }
  }
}

/**
 * Extract Twitter/X content using the Twitter API
 */
async function extractTwitterContent(url: string): Promise<ExtractMediaContentResult> {
  try {
    // Extract tweet ID from URL
    const tweetId = extractTwitterId(url)
    if (!tweetId) {
      return { success: false, error: 'Invalid Twitter/X URL' }
    }

    // Fetch tweet data using react-tweet/api
    const tweet = await getTweet(tweetId)
    
    if (!tweet) {
      return { success: false, error: 'Failed to fetch tweet data' }
    }
    
    // Extract images and videos
    const imageUrls: string[] = []
    const videoUrls: string[] = []
    
    // Extract photos if available
    if (tweet.photos && tweet.photos.length > 0) {
      tweet.photos.forEach(photo => {
        if (photo.url) {
          imageUrls.push(photo.url)
        }
      })
    }
    
    // Extract video if available
    if (tweet.video && tweet.video.variants && tweet.video.variants.length > 0) {
      tweet.video.variants.forEach(variant => {
        if (variant.src) {
          videoUrls.push(variant.src)
        }
      })
    }
    
    // Extract text content
    let content = tweet.text || ''
    
    return {
      success: true,
      data: {
        type: 'twitter',
        id: tweetId,
        authorName: tweet.user?.name,
        authorAvatar: tweet.user?.profile_image_url_https,
        content,
        imageUrls,
        videoUrls
      }
    }
  } catch (error: any) {
    console.error('Error extracting Twitter content:', error)
    return { success: false, error: error.message || 'Failed to extract Twitter content' }
  }
}

/**
 * Extract YouTube content
 */
async function extractYoutubeContent(url: string): Promise<ExtractMediaContentResult> {
  try {
    // Extract video ID from URL
    const videoId = extractYoutubeId(url)
    if (!videoId) {
      return { success: false, error: 'Invalid YouTube URL' }
    }

    return {
      success: true,
      data: {
        type: 'youtube',
        id: videoId,
        mediaUrl: `https://www.youtube.com/embed/${videoId}`,
        videoUrls: [`https://www.youtube.com/embed/${videoId}`]
      }
    }
  } catch (error: any) {
    console.error('Error extracting YouTube content:', error)
    return { success: false, error: error.message || 'Failed to extract YouTube content' }
  }
}

/**
 * Extract Jike content using the scraper service
 */
async function extractJikeContent(url: string): Promise<ExtractMediaContentResult> {
  try {
    // Extract post ID from URL for validation
    const postId = extractJikeId(url)
    if (!postId) {
      return { success: false, error: 'Invalid Jike URL' }
    }

    // Use the scraper service to extract the content
    const scraperUrl = process.env.BACKEND_SERVER_URL || 'http://localhost:3001';
    const response = await fetch(`${scraperUrl}/api/jike/extract`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ url })
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { 
        success: false, 
        error: errorData.error || `Failed to fetch Jike post data: ${response.status}` 
      };
    }

    // Return the response directly as it matches our expected format
    return await response.json();
  } catch (error: any) {
    console.error('Error extracting Jike content:', error)
    return { success: false, error: error.message || 'Failed to extract Jike content' }
  }
}

/**
 * Extract Twitter ID from URL
 */
function extractTwitterId(url: string): string | null {
  try {
    // Handle various Twitter URL formats
    // https://twitter.com/username/status/1234567890123456789
    // https://x.com/username/status/1234567890123456789
    const regex = /(?:twitter\.com|x\.com)\/(?:\w+)\/status\/(\d+)/
    const match = url.match(regex)
    return match ? match[1] : null
  } catch (error) {
    console.error('Error extracting Twitter ID:', error)
    return null
  }
}

/**
 * Extract YouTube ID from URL
 */
function extractYoutubeId(url: string): string | null {
  try {
    // Handle various YouTube URL formats
    // https://www.youtube.com/watch?v=VIDEO_ID
    // https://youtu.be/VIDEO_ID
    // https://www.youtube.com/embed/VIDEO_ID
    let regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    let match = url.match(regex)
    return match ? match[1] : null
  } catch (error) {
    console.error('Error extracting YouTube ID:', error)
    return null
  }
}

/**
 * Extract Jike ID from URL
 */
function extractJikeId(url: string): string | null {
  try {
    // Handle Jike URL format
    // https://m.okjike.com/originalPosts/POST_ID
    const regex = /okjike\.com\/(?:originalPosts|repost)\/([a-zA-Z0-9]+)/
    const match = url.match(regex)
    return match ? match[1] : null
  } catch (error) {
    console.error('Error extracting Jike ID:', error)
    return null
  }
} 