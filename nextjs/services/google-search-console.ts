import { JWT } from 'google-auth-library';
import { google } from 'googleapis';
import { getUserGoogleSearchConsoleConfig, getUserGoogleAnalyticsConfig, GoogleTokenData } from '@/models/user-configs';
import { getCanonicalDomain } from '@/utils/url-normalization';

interface BacklinkData {
  url: string;
  title: string;
  domain: string;
  anchorText: string;
  link_type: 'dofollow' | 'nofollow';
  discoveredAt: string;
  sourceUrl: string;
  isActive: boolean;
  dr_score?: number;
  traffic?: number;
}

interface GoogleSearchConsoleConfig {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

class GoogleSearchConsoleService {
  private getAuthClient(config: GoogleSearchConsoleConfig): JWT {
    return new JWT({
      email: config.client_email,
      key: config.private_key.replace(/\\n/g, '\n'),
      scopes: ['https://www.googleapis.com/auth/webmasters.readonly']
    });
  }

  private async getSearchConsoleClient(userId: string, projectId: string = '') {
    // Try OAuth first (new method), fallback to service account (legacy)
    const { data: oauthConfig } = await getUserGoogleAnalyticsConfig(userId, projectId);
    
    if (oauthConfig && oauthConfig.isActive && oauthConfig.oauthTokens) {
      return this.getOAuthSearchConsoleClient(oauthConfig.oauthTokens);
    }

    // Fallback to legacy service account method
    const { data: config, error } = await getUserGoogleSearchConsoleConfig(userId, projectId);
    
    if (error || !config || !config.configData) {
      throw new Error('Google Search Console not configured for this user');
    }

    const authClient = this.getAuthClient(config.configData);
    return google.searchconsole({ version: 'v1', auth: authClient });
  }

  private async getOAuthSearchConsoleClient(tokenData: GoogleTokenData) {
    // Check if token is expired
    if (Date.now() >= tokenData.expiresAt) {
      throw new Error('OAuth token expired');
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_OAUTH_CLIENT_ID,
      process.env.GOOGLE_OAUTH_CLIENT_SECRET,
      process.env.GOOGLE_OAUTH_REDIRECT_URI
    );

    oauth2Client.setCredentials({
      access_token: tokenData.accessToken,
      refresh_token: tokenData.refreshToken,
      expiry_date: tokenData.expiresAt
    });

    return google.searchconsole({ version: 'v1', auth: oauth2Client });
  }

  async isDomainVerified(domain: string, userId: string, projectId: string = ''): Promise<boolean> {
    try {
      const searchconsole = await this.getSearchConsoleClient(userId, projectId);
      
      const response = await searchconsole.sites.list();
      const sites = response.data.siteEntry || [];
      
      // Normalize domain for comparison
      const normalizedDomain = getCanonicalDomain(domain) || domain;
      const siteUrl = `https://${normalizedDomain}/`;
      const verifiedSite = sites.find(site => 
        site.siteUrl === siteUrl && site.permissionLevel === 'siteOwner'
      );
      
      return !!verifiedSite;
    } catch (error) {
      console.error('Error checking domain verification:', error);
      return false;
    }
  }

  async discoverBacklinks(domain: string, userId: string, projectId: string = ''): Promise<BacklinkData[]> {
    try {
      const searchconsole = await this.getSearchConsoleClient(userId, projectId);
      // Normalize domain for consistent API calls
      const normalizedDomain = getCanonicalDomain(domain) || domain;
      const siteUrl = `https://${normalizedDomain}/`;
      
      // Get external links data
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 90); // Last 90 days
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          dimensions: ['page', 'query'],
          dimensionFilterGroups: [{
            filters: [{
              dimension: 'country',
              operator: 'equals',
              expression: 'usa'
            }]
          }],
          rowLimit: 1000
        }
      });

      const rows = response.data.rows || [];
      const backlinks: BacklinkData[] = [];

      for (const row of rows) {
        const [pageUrl, query] = row.keys || [];
        if (pageUrl && query) {
          try {
            const url = new URL(pageUrl);
            const linkDomain = url.hostname;
            
            // Skip internal links
            if (linkDomain === domain) continue;
            
            backlinks.push({
              url: pageUrl,
              title: query, // Using query as title approximation
              domain: linkDomain,
              anchorText: query,
              link_type: 'dofollow', // GSC doesn't provide this info
              discoveredAt: new Date().toISOString(),
              sourceUrl: pageUrl,
              isActive: true,
              traffic: row.impressions || 0
            });
          } catch (urlError) {
            // Skip invalid URLs
            continue;
          }
        }
      }

      return backlinks;
    } catch (error) {
      console.error('Error discovering backlinks:', error);
      throw error;
    }
  }

  async getExternalLinks(domain: string, userId: string, projectId: string = ''): Promise<BacklinkData[]> {
    try {
      // This is an alternative method to get external links
      // In practice, GSC API is limited for backlink discovery
      // This method would be enhanced with additional API calls
      
      const searchconsole = await this.getSearchConsoleClient(userId, projectId);
      const siteUrl = `https://${domain}/`;
      
      // Try to get referring domains through search analytics
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 30);
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          dimensions: ['page'],
          rowLimit: 500
        }
      });

      const backlinks: BacklinkData[] = [];
      const rows = response.data.rows || [];

      for (const row of rows) {
        const [pageUrl] = row.keys || [];
        if (pageUrl) {
          try {
            const url = new URL(pageUrl);
            
            backlinks.push({
              url: pageUrl,
              title: `Page from ${url.hostname}`,
              domain: url.hostname,
              anchorText: 'External link',
              link_type: 'dofollow',
              discoveredAt: new Date().toISOString(),
              sourceUrl: pageUrl,
              isActive: true,
              traffic: row.clicks || 0
            });
          } catch (urlError) {
            continue;
          }
        }
      }

      return backlinks;
    } catch (error) {
      console.error('Error getting external links:', error);
      return [];
    }
  }

  async testConfiguration(configData: GoogleSearchConsoleConfig): Promise<{ isValid: boolean; error?: string }> {
    try {
      const authClient = this.getAuthClient(configData);
      const searchconsole = google.searchconsole({ version: 'v1', auth: authClient });
      
      // Test by listing sites
      const response = await searchconsole.sites.list();
      
      return { 
        isValid: true 
      };
    } catch (error: any) {
      return { 
        isValid: false, 
        error: error.message || 'Failed to authenticate with Google Search Console' 
      };
    }
  }

  // Enhanced OAuth methods for Google Search Console

  async getSearchConsoleProperties(userId: string, projectId: string): Promise<any[]> {
    try {
      const searchconsole = await this.getSearchConsoleClient(userId, projectId);
      
      const response = await searchconsole.sites.list();
      const sites = response.data.siteEntry || [];
      
      return sites.map(site => ({
        siteUrl: site.siteUrl,
        permissionLevel: site.permissionLevel,
        verified: site.permissionLevel === 'siteOwner' || site.permissionLevel === 'siteFullUser'
      }));
    } catch (error) {
      console.error('Error fetching Search Console properties:', error);
      throw error;
    }
  }

  async getSearchAnalyticsData(
    siteUrl: string,
    userId: string,
    projectId: string,
    startDate: string,
    endDate: string,
    dimensions: string[] = ['page'],
    rowLimit: number = 1000
  ): Promise<any> {
    try {
      const searchconsole = await this.getSearchConsoleClient(userId, projectId);
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions,
          rowLimit
        }
      });

      return {
        rows: response.data.rows || [],
        responseAggregationType: response.data.responseAggregationType
      };
    } catch (error) {
      console.error('Error fetching search analytics data:', error);
      throw error;
    }
  }

  async testOAuthConnection(userId: string, projectId: string): Promise<{ isValid: boolean; error?: string }> {
    try {
      const { data: oauthConfig } = await getUserGoogleAnalyticsConfig(userId, projectId);
      
      if (!oauthConfig || !oauthConfig.isActive || !oauthConfig.oauthTokens) {
        return { 
          isValid: false, 
          error: 'No OAuth configuration found' 
        };
      }

      const searchconsole = await this.getOAuthSearchConsoleClient(oauthConfig.oauthTokens);
      
      // Test by listing sites
      const response = await searchconsole.sites.list();
      
      return { 
        isValid: true 
      };
    } catch (error: any) {
      return { 
        isValid: false, 
        error: error.message || 'Failed to authenticate with OAuth tokens' 
      };
    }
  }
}

export const googleSearchConsoleService = new GoogleSearchConsoleService(); 