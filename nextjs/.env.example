# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "AiMCP"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
NEXT_PUBLIC_SUPABASE_URL = ""
NEXT_PUBLIC_SUPABASE_ANON_KEY = ""
SUPABASE_SERVICE_ROLE_KEY = ""
SUPABASE_SCHEMA = ""

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = ""
AUTH_GOOGLE_SECRET = ""
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "false"
# Required for one-tap login
NEXT_PUBLIC_AUTH_GOOGLE_ID = ""
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# -----------------------------------------------------------------------------
# Google APIs for Analytics & Search Console Integration
# https://console.cloud.google.com/apis/library
# -----------------------------------------------------------------------------
# Enable these APIs in Google Cloud Console:
# - Google Analytics Data API
# - Google Analytics Admin API  
# - Google Search Console API
# 
# The AUTH_GOOGLE_ID and AUTH_GOOGLE_SECRET above will be used
# for both authentication and Analytics/Search Console access

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

# -----------------------------------------------------------------------------
# AI Settings
# -----------------------------------------------------------------------------
AI_MODEL_NAME = ""
AI_API_KEY = ""
AI_API_BASE_URL = ""
EMBEDDING_MODEL_NAME = "text-embedding-3-small"
EMBEDDING_MODEL_EN = "text-embedding-3-small"
EMBEDDING_MODEL_ZH = "text-embedding-3-small"
EMBEDDING_DIMENSIONS = "512"

# -----------------------------------------------------------------------------
# Admin Settings
# -----------------------------------------------------------------------------
ADMIN_EMAILS = ""
ADMIN_API_TOKEN = ""

# -----------------------------------------------------------------------------
# Theme Settings
# -----------------------------------------------------------------------------
NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

# -----------------------------------------------------------------------------
# Notification Settings
# -----------------------------------------------------------------------------
# Feishu (Lark) Notification
NOTIFICATION_FEISHU_ENABLED = "false"
NOTIFICATION_FEISHU_WEBHOOK_URL = ""

# -----------------------------------------------------------------------------
# Email Settings with Resend
# https://resend.com/docs
# -----------------------------------------------------------------------------
RESEND_API_KEY = ""
RESEND_EMAIL_FROM = "<EMAIL>"
EMAIL_NOTIFICATIONS_ENABLED = "true"

# -----------------------------------------------------------------------------
# Github Settings
# -----------------------------------------------------------------------------
GITHUB_TOKEN = ""

# -----------------------------------------------------------------------------
# Backend Server Settings
# -----------------------------------------------------------------------------
BACKEND_SERVER_URL = ""
BACKEND_WORKER_URL = ""
BACKEND_WORKER_API_KEY = ""

# -----------------------------------------------------------------------------
# Newsletter Settings with Beehiiv
# https://beehiiv.com/docs
# -----------------------------------------------------------------------------
BEEHIIV_API_KEY = ""
BEEHIIV_PUBLICATION_ID = ""
BEEHIIV_ENABLED = "false"

# -----------------------------------------------------------------------------
# Umami Settings
# https://umami.is/docs
# -----------------------------------------------------------------------------
NEXT_PUBLIC_UMAMI_WEBSITE_ID = ""
NEXT_PUBLIC_UMAMI_SCRIPT_URL = ""
UMAMI_ENABLED = "false"

# -----------------------------------------------------------------------------
# Plausible Settings
# https://plausible.io/docs
# -----------------------------------------------------------------------------
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = ""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = ""
PLAUSIBLE_ENABLED = "false"

# -----------------------------------------------------------------------------
# Invitation credit
INVITATION_DEFAULT_CREDIT = 5

# -----------------------------------------------------------------------------
# WHOIS API Settings
# https://whoisxmlapi.com or https://whoisapi.whoisxmlapi.com
# -----------------------------------------------------------------------------
WHOIS_API_KEY = "" 