'use client';

import { useState, useEffect, useCallback } from 'react';
import { useOAuthError } from './useOAuthError';

interface OAuthServiceStatus {
  authorized: boolean;
  scopes: string[];
  expiresAt?: number;
  error?: string;
}

interface OAuthStatusData {
  basic: OAuthServiceStatus;
  analytics: OAuthServiceStatus;
  searchConsole: OAuthServiceStatus;
}

interface UseOAuthStatusOptions {
  projectId: string;
  userId: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

interface UseOAuthStatusReturn {
  status: OAuthStatusData | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  isConnected: (service: keyof OAuthStatusData) => boolean;
  isExpiringSoon: (service: keyof OAuthStatusData, bufferMinutes?: number) => boolean;
  getExpirationDate: (service: keyof OAuthStatusData) => Date | null;
  getConnectionSummary: () => {
    connected: number;
    total: number;
    percentage: number;
    allConnected: boolean;
    noneConnected: boolean;
  };
}

export const useOAuthStatus = ({
  projectId,
  userId,
  autoRefresh = false,
  refreshInterval = 60000 // 1 minute
}: UseOAuthStatusOptions): UseOAuthStatusReturn => {
  const [status, setStatus] = useState<OAuthStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { handleAsyncOperation } = useOAuthError();

  const refresh = useCallback(async () => {
    if (!projectId || !userId) {
      setError('Missing project ID or user ID');
      setLoading(false);
      return;
    }

    await handleAsyncOperation(
      async () => {
        const response = await fetch(`/api/projects/${projectId}/oauth/status`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch OAuth status: ${response.statusText}`);
        }
        
        const data = await response.json();
        setStatus(data);
        setError(null);
        return data;
      },
      'OAuth Status Check',
      {
        onSuccess: (data) => {
          setStatus(data);
          setError(null);
        },
        onError: (err) => {
          console.error('OAuth status check failed:', err);
          setError(err.message || 'Failed to check OAuth status');
        }
      }
    );
    
    setLoading(false);
  }, [projectId, userId, handleAsyncOperation]);

  const isConnected = useCallback((service: keyof OAuthStatusData): boolean => {
    if (!status) return false;
    return status[service]?.authorized || false;
  }, [status]);

  const isExpiringSoon = useCallback((service: keyof OAuthStatusData, bufferMinutes: number = 60): boolean => {
    if (!status || !status[service]?.authorized) return false;
    
    const expiresAt = status[service].expiresAt;
    if (!expiresAt) return false;
    
    const bufferMs = bufferMinutes * 60 * 1000;
    return expiresAt <= Date.now() + bufferMs;
  }, [status]);

  const getExpirationDate = useCallback((service: keyof OAuthStatusData): Date | null => {
    if (!status || !status[service]?.authorized) return null;
    
    const expiresAt = status[service].expiresAt;
    return expiresAt ? new Date(expiresAt) : null;
  }, [status]);

  const getConnectionSummary = useCallback(() => {
    if (!status) {
      return {
        connected: 0,
        total: 2, // analytics and searchConsole
        percentage: 0,
        allConnected: false,
        noneConnected: true
      };
    }

    const services: (keyof OAuthStatusData)[] = ['analytics', 'searchConsole'];
    const connected = services.filter(service => isConnected(service)).length;
    const total = services.length;
    
    return {
      connected,
      total,
      percentage: total > 0 ? Math.round((connected / total) * 100) : 0,
      allConnected: connected === total,
      noneConnected: connected === 0
    };
  }, [status, isConnected]);

  // Initial load
  useEffect(() => {
    refresh();
  }, [refresh]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !projectId || !userId) return;

    const interval = setInterval(() => {
      refresh();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refresh, projectId, userId]);

  // Monitor for expiring tokens
  useEffect(() => {
    if (!status || !autoRefresh) return;

    const checkExpirations = () => {
      const services: (keyof OAuthStatusData)[] = ['analytics', 'searchConsole'];
      
      services.forEach(service => {
        if (isExpiringSoon(service, 30)) { // 30 minutes warning
          console.warn(`OAuth token for ${service} is expiring soon`);
        }
      });
    };

    // Check immediately
    checkExpirations();

    // Check every 5 minutes
    const interval = setInterval(checkExpirations, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [status, autoRefresh, isExpiringSoon]);

  return {
    status,
    loading,
    error,
    refresh,
    isConnected,
    isExpiringSoon,
    getExpirationDate,
    getConnectionSummary
  };
};