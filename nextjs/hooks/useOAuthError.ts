'use client';

import { useCallback } from 'react';
import { toast } from 'sonner';
import { OAuthErrorType } from '@/services/oauth-token-manager';

interface OAuthError {
  type: OAuthErrorType;
  message: string;
  retryable: boolean;
  requiresReauth: boolean;
  code?: string;
}

export const useOAuthError = () => {
  const handleOAuthError = useCallback((error: OAuthError | any, serviceType: string) => {
    // Handle structured OAuth errors
    if (error && typeof error === 'object' && 'type' in error) {
      switch (error.type) {
        case OAuthErrorType.TOKEN_EXPIRED:
          toast.warning(
            `${serviceType} session expired. We'll try to refresh it automatically.`,
            {
              description: 'If this continues, you may need to reconnect.',
              duration: 5000
            }
          );
          break;
          
        case OAuthErrorType.TOKEN_REVOKED:
          toast.error(
            `${serviceType} access has been revoked.`,
            {
              description: 'Please reconnect to continue using this feature.',
              duration: 7000,
              action: {
                label: 'Reconnect',
                onClick: () => {
                  // This will be handled by the component
                  console.log('Reconnect action triggered');
                }
              }
            }
          );
          break;
          
        case OAuthErrorType.INSUFFICIENT_SCOPE:
          toast.error(
            `Additional ${serviceType} permissions required.`,
            {
              description: 'Please reconnect with the required permissions.',
              duration: 7000
            }
          );
          break;
          
        case OAuthErrorType.RATE_LIMITED:
          toast.warning(
            `${serviceType} API rate limit exceeded.`,
            {
              description: 'Please try again in a few minutes.',
              duration: 5000
            }
          );
          break;
          
        case OAuthErrorType.AUTH_REQUIRED:
          toast.info(
            `Please connect your ${serviceType} account to use this feature.`,
            {
              description: 'Click the Connect button to get started.',
              duration: 5000
            }
          );
          break;
          
        case OAuthErrorType.INVALID_STATE:
          toast.error(
            'Security validation failed.',
            {
              description: 'Please try the connection process again.',
              duration: 5000
            }
          );
          break;
          
        case OAuthErrorType.INVALID_GRANT:
          toast.error(
            `${serviceType} authorization is invalid.`,
            {
              description: 'Please reconnect to restore access.',
              duration: 7000
            }
          );
          break;
          
        case OAuthErrorType.NETWORK_ERROR:
          toast.error(
            `Network error connecting to ${serviceType}.`,
            {
              description: 'Please check your connection and try again.',
              duration: 5000
            }
          );
          break;
          
        default:
          toast.error(
            `${serviceType} error: ${error.message}`,
            {
              description: error.retryable ? 'You can try again.' : 'Please contact support if this persists.',
              duration: 5000
            }
          );
      }
      
      return {
        shouldRetry: error.retryable,
        requiresReauth: error.requiresReauth,
        userMessage: error.message
      };
    }
    
    // Handle generic errors
    const errorMessage = error?.message || error?.toString() || 'Unknown error';
    toast.error(
      `${serviceType} error: ${errorMessage}`,
      {
        description: 'Please try again or contact support if this persists.',
        duration: 5000
      }
    );
    
    return {
      shouldRetry: true,
      requiresReauth: false,
      userMessage: errorMessage
    };
  }, []);

  const handleAsyncOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    serviceType: string,
    options: {
      successMessage?: string;
      loadingMessage?: string;
      onSuccess?: (result: T) => void;
      onError?: (error: any) => void;
      retryCount?: number;
    } = {}
  ): Promise<{ success: boolean; data?: T; error?: any }> => {
    const { 
      successMessage, 
      loadingMessage, 
      onSuccess, 
      onError, 
      retryCount = 0 
    } = options;
    
    let toastId: string | number | undefined;
    
    if (loadingMessage) {
      toastId = toast.loading(loadingMessage);
    }
    
    try {
      const result = await operation();
      
      if (toastId) {
        toast.dismiss(toastId);
      }
      
      if (successMessage) {
        toast.success(successMessage);
      }
      
      onSuccess?.(result);
      
      return { success: true, data: result };
    } catch (error: any) {
      if (toastId) {
        toast.dismiss(toastId);
      }
      
      const errorInfo = handleOAuthError(error, serviceType);
      onError?.(error);
      
      // Retry logic for retryable errors
      if (errorInfo.shouldRetry && retryCount > 0 && !errorInfo.requiresReauth) {
        console.log(`Retrying operation for ${serviceType}, attempts left: ${retryCount - 1}`);
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, 3 - retryCount) * 1000));
        
        return handleAsyncOperation(operation, serviceType, {
          ...options,
          retryCount: retryCount - 1
        });
      }
      
      return { success: false, error };
    }
  }, [handleOAuthError]);

  return { 
    handleOAuthError, 
    handleAsyncOperation 
  };
};