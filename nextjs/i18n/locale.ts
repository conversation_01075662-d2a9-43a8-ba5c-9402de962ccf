import { Pathnames } from "next-intl/routing";

// export const locales = ['en', 'zh', 'fr', 'es', 'de', 'ja', 'ko', 'ru'];
export const locales = ['en', 'zh'];

export const localeNames: any = {
  en: "English",
  zh: "中文",
  // ja: "日本語",
  // ko: "한국어",
  // fr: "Français",
  // es: "Español",
  // de: "Deutsch",
  // ru: "Русский",
};

export const defaultLocale = "en";

export const localePrefix = "always";

export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";

export const pathnames = {
  en: {
    "privacy-policy": "/privacy-policy",
    "terms-of-service": "/terms-of-service",
  },
} satisfies Pathnames<typeof locales>;
