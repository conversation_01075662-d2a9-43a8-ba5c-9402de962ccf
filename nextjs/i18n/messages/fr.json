{"user": {"sign_in": "Se connecter", "sign_out": "Se déconnecter", "my_orders": "<PERSON><PERSON> commandes", "no_auth_or_email": "Aucune authentification ou email trouvé"}, "task_succeeded": "Tâche terminée avec succès", "file": "<PERSON><PERSON><PERSON>", "status": {"online": "En ligne", "view_pending": "Voir en attente", "created": "<PERSON><PERSON><PERSON>", "no_pending": "Aucun élément en attente", "error": "<PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON><PERSON>", "title": "Statut", "failed": "<PERSON><PERSON><PERSON>", "active": "Actif", "description": "Description", "inactive": "Inactif", "pending": "En attente", "offline": "<PERSON><PERSON> ligne"}, "link_type": {}, "user_id": "ID Utilisateur", "historyLimit": "Limite d'Historique", "withStats": "Avec Statistiques", "action": "Action", "table": {"created_at": "<PERSON><PERSON><PERSON>", "id": "ID", "actions": "Actions", "name": "Nom", "updated_at": "Mis à jour le", "event_type": "Type d'Événement", "status": "Statut", "sent_at": "<PERSON><PERSON><PERSON>", "tools_count": "Nombre d'Outils", "type": "Type", "recipient": "<PERSON><PERSON><PERSON>", "subject": "Sujet", "item_uuid": "UUID de l'Élément"}, "no_image_result": "Aucun résultat d'image", "no_logs": "Aucun log disponible", "no_templates": "Aucun modèle disponible", "category": "<PERSON><PERSON><PERSON><PERSON>", "result_image": "Image de Résultat", "cancel_button": "Annuler", "completed_at": "<PERSON><PERSON><PERSON><PERSON> le", "error_reason": "Raison de l'Erreur", "my_tasks": "<PERSON><PERSON>", "T": "T", "update_error": "<PERSON><PERSON><PERSON> <PERSON> Mise à Jour", "saved_success": "Enregistré avec <PERSON>", "create_error": "Erreur de Création", "details": {}, "selected_items": "Éléments Sélectionnés", "no_json_result": "Aucun résultat JSON", "searchTerm": "<PERSON><PERSON><PERSON>", "task_status": "Statut de Tâche", "type": "Type", "empty": {"no_tools": "Aucun outil disponible", "no_parameters": "Aucun paramètre disponible", "no_tools_to_translate": "Aucun outil à traduire"}, "prev_page": "<PERSON>", "error_loading": "Erreur de Chargement", "provider": "Fournisseur", "content": "Contenu", "search": "<PERSON><PERSON><PERSON>", "task_processing": "Traitement de Tâche", "projectId": "ID du Projet", "create_success": "<PERSON><PERSON><PERSON> a<PERSON>", "div": "Division", "my_tasks_description": "Description de Mes Tâches", "print_result": "Imprimer le Résultat", "slug": "Slug", "a": "<PERSON><PERSON>", "brief": "Résumé", "test": "Test", "sort": "<PERSON><PERSON>", "timeRange": "<PERSON><PERSON>", "query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "author_avatar_url": "URL de l'Avatar de l'Auteur", "saved": "Enregistré", "tab_json": "Onglet JSON", "saved_error": "<PERSON><PERSON><PERSON> d'Enregistrement", "url": "URL", "update_success": "Mis à Jour avec Succès", "tip": "Conseil", "host": "<PERSON><PERSON><PERSON>", "task_view": "Vue de Tâche", "processinfo": "Informations de Processus", "isPaid": "Est Payant", "q": "Question", "is_recommended": "Est Recommandé", "checked_at": "<PERSON><PERSON><PERSON><PERSON><PERSON> le", "page": "Page", "keywords": "Mots-clés", "video_urls": "URLs Vidéo", "uuid": "UUID", "tab_image": "Onglet Image", "sessionId": "ID de Session", "no_tasks": "Aucune tâche disponible", "goHome": "Retour à l'Accueil", "task_date": "Date de Tâche", "current": "Actuel", "image_urls": "URLs d'Image", "create_button": "<PERSON><PERSON><PERSON>", "tab_text": "Onglet Texte", "processing_time": "Temps de Traitement", "task_credit_cost": "Coût de Crédit de Tâche", "cover_url": "URL de Couverture", "save": "Enregistrer", "Authorization": "Autorisation", "signin_type": "Type de Connexion", "invite_code": "Code d'Invitation", "lang": "<PERSON><PERSON>", "view_task_result": "Voir le Résultat de Tâche", "refresh_button": "Actualiser", "back_to_submissions": "Retour aux Soumissions", "results_title": "Résultats", "saving": "Enregistrement...", "USD": "USD", "offset": "Décalage", "website_url": "URL du Site Web", "task_product": "Produit de Tâche", "tagName": "<PERSON><PERSON>", "task_pending": "Tâche en Attente", "stdio": "Entrée/Sortie Standard", "sse": "Événements Envoyés par le Serveur", "includeHistory": "Inclure l'Historique", "is_official": "<PERSON><PERSON> Officiel", "task_result": "Résultat de Tâche", "title": "Titre", "limit": "Limite", "process_button": "Traiter", "next_page": "<PERSON>", "author_name": "Nom de l'Auteur", "locale": "Paramètres Régionaux", "update_button": "Mettre à Jour", "description": "Description", "field": "<PERSON><PERSON>", "api_key": "Clé API", "ai_summary": "Résumé IA", "domain": {"management": {"title": "Gestion des Domaines", "description": "<PERSON><PERSON><PERSON> les dates d'expiration des domaines et évitez les renouvellements manqués", "addDomain": "Ajouter un Domaine", "editDomain": "Modifier le Domaine", "deleteDomain": "Su<PERSON><PERSON>er le Domaine", "refreshWhois": "Actualiser WHOIS", "bulkImport": "Importation en Masse", "associateProject": "Associer un Projet", "removeAssociation": "Supprimer l'Association", "searchPlaceholder": "Rechercher des domaines...", "noDomains": "Aucun domaine trouvé", "loading": "Chargement...", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce domaine ?", "confirmDeleteMessage": "Cette action ne peut pas être annulée.", "domainAdded": "Domaine ajouté avec succès", "domainUpdated": "Domaine mis à jour avec succès", "domainDeleted": "Domaine supprimé avec succès", "whoisRefreshed": "Données WHOIS actualisées avec succès", "errorAddingDomain": "<PERSON><PERSON>ur lors de l'ajout du domaine", "errorUpdatingDomain": "Erreur lors de la mise à jour du domaine", "errorDeletingDomain": "<PERSON><PERSON><PERSON> lors de la suppression du domaine", "errorRefreshingWhois": "Erreur lors de l'actualisation des données WHOIS", "expirationTracking": "Suivez les dates d'expiration pour éviter les renouvellements manqués"}, "status": {"active": "Actif", "expired": "Expiré", "expiring": "Expire <PERSON><PERSON><PERSON><PERSON>", "unknown": "Inconnu"}, "stats": {"total": "Total des Domaines", "active": "Actif", "expiring": "Expire <PERSON><PERSON><PERSON><PERSON>", "expired": "Expiré", "projects": "Projets Associés"}, "form": {"domain": "Domaine", "registrar": "Registraire", "dnsProvider": "Fournisseur DNS", "createdDate": "Date de Création", "expiryDate": "Date d'Expiration", "registrationPrice": "Prix d'Enregistrement", "renewalPrice": "Prix de Renouvellement", "currency": "<PERSON><PERSON>", "autoRenew": "Renouvellement Automatique", "monitorExpiry": "Surveiller l'Expiration", "alertDaysBefore": "<PERSON><PERSON><PERSON>", "notes": "Notes", "tags": "Étiquettes", "isFavorite": "<PERSON><PERSON><PERSON>", "nameServers": "Serveurs de Noms", "required": "Requis", "optional": "Optionnel", "enterDomain": "Entrez le nom de domaine", "enterRegistrar": "Entrez le nom du registraire", "enterDnsProvider": "Entrez le fournisseur DNS", "enterNotes": "Entrez des notes", "enterTags": "Entrez des étiquettes (séparées par des virgules)", "selectCurrency": "Sélectionner une devise", "selectProject": "Sélectionner un projet", "daysBeforeExpiry": "Jours avant expiration"}, "filter": {"all": "Tous", "status": "Statut", "registrar": "Registraire", "dnsProvider": "Fournisseur DNS", "project": "Projet", "expiring": "Expire <PERSON><PERSON><PERSON><PERSON>", "favorites": "<PERSON><PERSON><PERSON>"}, "sort": {"domain": "Domaine", "expiryDate": "Date d'Expiration", "createdDate": "Date de Création", "registrar": "Registraire", "ascending": "Croissant", "descending": "Décroissant"}, "table": {"domain": "Domaine", "registrar": "Registraire", "dnsProvider": "Fournisseur DNS", "status": "Statut", "expiryDate": "Date d'Expiration", "createdDate": "Date de Création", "projects": "Projets", "actions": "Actions", "daysUntilExpiry": "Jours Avant Expiration", "whoisLastUpdated": "WHOIS Mis à Jour", "expires": "Expire", "renewalPrice": "Prix de Renouvellement", "never": "<PERSON><PERSON>", "view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Actualiser", "associate": "Associer", "favorite": "<PERSON><PERSON><PERSON>", "unfavorite": "Retirer des Favoris"}, "whois": {"data": "Données WHOIS", "lastUpdated": "Dernière Mise à Jour", "cacheExpires": "Le Cache Expire", "refreshing": "Actualisation...", "noData": "Aucune donnée WHOIS disponible", "expired": "Données WHOIS expirées", "clickToRefresh": "Cliquez pour actualiser les données WHOIS"}, "project": {"association": "Association de Projet", "associatedProjects": "Projets Associés", "noAssociatedProjects": "Aucun projet associé", "selectProject": "Sélectionnez un projet à associer", "isPrimary": "Domaine principal pour le projet", "associationAdded": "Association de projet ajoutée", "associationRemoved": "Association de projet supprimée", "errorAddingAssociation": "Erreur lors de l'ajout de l'association de projet", "errorRemovingAssociation": "Erreur lors de la suppression de l'association de projet"}, "monitoring": {"title": "Surveillance d'Expiration de Domaines", "expiryMonitoring": "Surveillance d'Expiration", "alertSettings": "Paramètres d'Alerte", "lastAlertSent": "Dernière Alerte Envoy<PERSON>", "noAlerts": "Aucune alerte envoyée encore", "alertsEnabled": "Alertes Activées", "alertsDisabled": "Alertes Désactivées", "preventMissedRenewals": "Prévenez les renouvellements manqués avec des alertes d'expiration"}, "import": {"title": "Importation en Masse de Domaines", "description": "Importez plusieurs domaines à la fois", "format": "Format : domaine.com,registraire,fournisseur-dns", "example": "Exemple : exemple.com,<PERSON><PERSON><PERSON><PERSON>,Cloudflare", "paste": "Collez les domaines (un par ligne)", "import": "Importer les Domaines", "importing": "Importation...", "success": "{count} domaines importés avec succès", "error": "Erreur lors de l'importation des domaines", "invalidFormat": "Format invalide à la ligne {line}", "duplicateDomain": "Domaine en double : {domain}"}}, "task_failed_status": "Tâche <PERSON>", "no_text_result": "Aucun résultat de texte", "invitation": {"invites_count": "Nombre d'Invitations", "earn_credits": "Gagner des Crédits", "share_title": "Partager l'Invitation", "credits_per_invite": "Crédits par Invitation", "copy": "<PERSON><PERSON><PERSON>", "share_text": "Partagez ce lien d'invitation avec vos amis !", "copy_success": "Copié dans le presse-papiers !", "how_it_works": "Comment ça Marche", "your_code": "Votre Code", "copying": "Copie en cours...", "share_success": "Partagé avec succès !", "share_description": "Invitez des amis et gagnez des crédits", "share_code": "Partager le Code", "title": "Système d'Invitation", "share_invite_link": "Partager le Lien d'Invitation", "copy_failed": "Échec de la copie", "share": "Partager", "share_failed": "Échec du partage", "successful_invites": "Invitations <PERSON><PERSON><PERSON><PERSON>"}, "editor": {"result_image": "Image de Résultat", "copied_json": "JSON copié dans le presse-papiers !", "results_title": "Résultats", "copy": "<PERSON><PERSON><PERSON>", "usage_processing_time": "Temps de Traitement", "my_tasks": "<PERSON><PERSON>", "usage_supported_formats": "Formats Supportés", "upload_description": "Téléchargez votre fichier pour traitement", "drop_files": "Dé<PERSON>z les fichiers ici pour télécharger", "tab_text": "Texte", "no_json_result": "Aucun résultat JSON", "no_text_result": "Aucun résultat de texte", "processing_file": "Traitement du fichier...", "processing": "Traitement...", "file_selected": "<PERSON><PERSON><PERSON>", "error_polling": "Erreur lors de la récupération des résultats", "unexpected_error": "Une erreur inattendue s'est produite", "usage_max_file_size": "<PERSON><PERSON> maximale de fi<PERSON>er", "task_complete": "Tâche terminée", "no_image_result": "Aucun résultat d'image", "usage_tips_title": "Conseils d'Utilisation", "error_no_file": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "process_file": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>", "tab_json": "JSON", "usage_sign_in": "Connectez-vous pour utiliser cette fonction", "error_file_size": "La taille du fichier dépasse la limite", "usage_credit_cost": "Coût de crédit", "error_file_type": "Type de fichier non supporté", "task_timeout": "<PERSON><PERSON><PERSON> d'attente de tâche dépassé", "error_create_task": "Erreur lors de la création de tâche", "upload_title": "Télécharger un Fichier", "copied_text": "Texte copié dans le presse-papiers !", "upload_process_prompt": "Téléchargez et traitez votre fichier", "file_type": "Type de fichier", "remove_file": "<PERSON><PERSON><PERSON><PERSON> le fichier", "results_description": "Résultats du traitement", "tab_image": "Image", "max_file_size": "<PERSON><PERSON> maximale de fi<PERSON>er", "unknown_error": "<PERSON><PERSON><PERSON> inconnue", "task_failed": "Tâche échouée"}, "linkResources": {"title": "Bibliothèque de Ressources de Liens Externes", "description": "<PERSON><PERSON><PERSON> votre collection de ressources de liens externes et suivez leur efficacité pour vos projets", "loading": "Chargement...", "noResults": "<PERSON><PERSON><PERSON> ressource trouvée", "resultsCount": "{count} ressources trouvées", "paid": "Payant", "free": "<PERSON><PERSON><PERSON>", "common": {"home": "Accueil"}, "seo": {"title": "Gestion de Ressources de Liens Externes", "description": "Votre base de données de plateformes de liens externes vérifiées avec suivi de performance", "keywords": "ressources de liens externes, bibliothèque de liens, gestion de projets, outils SEO, construction de liens", "categoryDescription": "Parcourir les ressources de backlinks {category} dans votre bibliothèque", "paidDescription": "Ressources de backlinks premium dans votre collection", "freeDescription": "Ressources de backlinks gratuites dans votre collection", "page": "Page", "additionalTitle": "Comment Fonctionne Votre Bibliothèque de Ressources Personnelle", "howItWorks": "Comment ça Marche", "howItWorksDesc": "Votre approche systématique pour gérer les ressources de backlinks", "step1": "Construisez votre bibliothèque personnelle de plateformes de soumission vérifiées", "step2": "Su<PERSON>z les métriques de performance et les taux de succès", "step3": "Mettez à jour le statut des ressources basé sur les résultats du projet", "step4": "Maintenez la qualité grâce à une révision et des mises à jour régulières", "benefits": "Avantages", "benefitsDesc": "Pourquoi maintenir votre bibliothèque de ressources personnelle", "benefit1": "Collection organisée adaptée à vos projets", "benefit2": "Su<PERSON>z les taux de succès et le ROI pour chaque ressource", "benefit3": "Évi<PERSON>z de perdre du temps sur des plateformes de faible qualité", "benefit4": "Construisez des connaissances institutionnelles au fil du temps"}, "stats": {"totalResources": "Total des Ressources dans la Bibliothèque", "categories": "Catégories Suivies", "highAuthority": "Taux de Succès <PERSON>"}, "features": {"title": "Fonctionnalités de la Bibliothèque Personnelle", "highQuality": "Suivi de Qualité", "highQualityDesc": "Surveillez les performances et les taux de succès", "verified": "Vérification Personnelle", "verifiedDesc": "<PERSON><PERSON>z votre propre expérience avec chaque plateforme", "qualityAssured": "<PERSON><PERSON> à Jour Continues", "qualityAssuredDesc": "Gardez votre bibliothèque actuelle et efficace", "updated": "Surveillance des Performances", "updatedDesc": "<PERSON><PERSON>z le ROI et l'efficacité au fil du temps"}, "filters": {"searchPlaceholder": "Rechercher vos ressources...", "categoryPlaceholder": "Sélectionner une catégorie", "allCategories": "Toutes les Catégories", "pricingPlaceholder": "Sélectionner le prix", "allPricing": "Tous les Prix", "free": "Gratuit Seulement", "paid": "Payant Seulement", "sortPlaceholder": "Trier par", "sortByDRDesc": "Classement de Domaine (Élevé à Faible)", "sortByDRAsc": "Classement de Domaine (Faible à Élevé)", "sortByTrafficDesc": "Trafic (Élevé à Faible)", "sortByTrafficAsc": "Trafic (Faible à Élevé)", "sortBySuccessRateDesc": "<PERSON><PERSON>ès (Élevé à Faible)", "sortByNewest": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "categories": {"directory": "Annuaire", "blog": "Blog", "news": "Actualités", "resource-page": "<PERSON> Ressources", "guest-post": "Article Invité", "forum": "Forum", "social-media": "Réseaux Sociaux", "press-release": "Communiqué de Presse", "startup-directory": "Annuaire de Startups", "tool-directory": "Annuaire d'Outils"}, "details": {"submissionMethod": "Méthode de Soumission", "responseTime": "Temps de Réponse", "successRate": "Taux de Succès Personnel", "priceRange": "Fourchette de Prix", "requirements": "Exigences", "lastUsed": "Dernière Utilisation", "personalNotes": "Notes Personnelles"}, "actions": {"submitHere": "<PERSON><PERSON><PERSON><PERSON>", "visitWebsite": "Visiter le Site Web", "contact": "Contact", "updateStatus": "Mettre à Jour le Statut", "addNotes": "Ajouter des Notes"}, "pagination": {"page": "Page", "of": "de", "previous": "Précédent", "next": "Suivant"}, "faq": {"title": "Questions Fréquemment Po<PERSON>ées", "q1": "Qu'est-ce qu'une bibliothèque personnelle de ressources de backlinks ?", "a1": "Une collection organisée de plateformes de backlinks que vous avez personnellement testées et vérifiées pour vos projets parallèles.", "q2": "Comment suivez-vous les performances des ressources ?", "a2": "Enregistrez les taux de succès, les temps de réponse et les notes personnelles pour chaque plateforme que vous utilisez.", "q3": "<PERSON><PERSON><PERSON>-je importer des ressources existantes ?", "a3": "<PERSON><PERSON>, vous pouvez importer en masse vos ressources de backlinks existantes et ajouter des données de performance.", "q4": "À quelle fréquence dois-je mettre à jour ma bibliothèque ?", "a4": "Révisez et mettez à jour votre bibliothèque chaque semaine en fonction des nouvelles soumissions et des résultats.", "q5": "Quelles métriques dois-je suivre ?", "a5": "Su<PERSON><PERSON> les taux de succès, les temps de réponse, les coûts et les notes d'efficacité personnelle.", "q6": "Comment cela aide-t-il mes projets parallèles ?", "a6": "Maintenez une bibliothèque de ressources de qualité pour construire efficacement des backlinks pour les futurs projets."}}, "links": {"traffic": "Trafic", "title": "<PERSON><PERSON>", "add": "Ajouter un lien", "saving": "Enregistrement...", "cancel": "Annuler", "update": "Mettre à jour", "a": "<PERSON><PERSON>", "dr_score": "Score DR", "add_link": "Ajouter un lien"}, "SubmitPage": {"heading": "Ajouter une Ressource à la Bibliothèque", "home": "Accueil", "submit": "Ajouter une ressource", "title": "Ajouter une ressource", "description": "Ajoutez une nouvelle ressource de backlink à votre bibliothèque personnelle"}, "SearchPage": {"search": "<PERSON><PERSON><PERSON>", "title": "Rechercher des ressources", "description": "Recherchez dans votre bibliothèque personnelle de ressources de backlinks", "home": "Accueil"}, "metadata": {"title": "MyBackLinks - Gestion de Ressources de Liens Externes et Analyses de Projets", "description": "Gérez votre bibliothèque de ressources de liens externes, suivez les valeurs DR et les backlinks des projets, surveillez l'expiration des domaines et intégrez avec les plateformes d'analyse", "keywords": "bibliothèque de liens externes, analyses de projets, gestion de domaines, suivi DR, intégration SEMrush, analyses plausible"}, "NotFound": {"title": "Page Non Trouvée", "description": "La page que vous recherchez n'existe pas ou a été déplacée.", "goHome": "Retour à l'Accueil"}, "sign_modal": {"sign_in_title": "Se connecter", "sign_in_description": "Connectez-vous pour accéder à votre bibliothèque personnelle de ressources de backlinks, au suivi de projets parallèles et aux outils de gestion de domaines", "cancel_title": "Annuler", "google_sign_in": "Google", "github_sign_in": "GitHub"}, "integrations": "Intégrations", "projects": {"title": "Projets", "description": "Su<PERSON>z les valeurs DR, les backlinks et les analyses de vos projets", "addProject": "Ajouter un Projet", "editProject": "Modifier le Projet", "deleteProject": "Supprimer le Projet", "viewAnalytics": "Voir les Analyses", "projectUrl": "URL du Projet", "projectName": "Nom du Projet", "projectDescription": "Description du Projet", "noProjects": "Aucun projet trouvé", "createFirst": "Créez votre premier projet pour commencer le suivi", "drTracking": {"title": "Suivi du Classement de Domaine", "currentDR": "DR Actuel", "drHistory": "Historique DR", "lastUpdated": "Dernière Mise à Jour", "uploadSemrush": "Télécharger un Fichier SEMrush", "manualUpdate": "<PERSON>se <PERSON> Jo<PERSON>", "limitedQueries": "Requêtes manuelles limitées disponibles"}, "backlinks": {"title": "Suivi des Backlinks", "totalBacklinks": "Total des Backlinks", "newBacklinks": "Nouveaux Backlinks", "lostBacklinks": "Backlinks Perdus", "referringDomains": "Domaines Référents", "backlinkSources": "Sources de Backlinks", "qualityScore": "Score de Qualité", "noDeadLinkTracking": "Note : Suivi des liens morts non disponible"}, "analytics": {"title": "<PERSON><PERSON><PERSON>", "traffic": "Aperçu du Trafic", "backlinks": "Backlinks", "domains": "Domaines Associés", "lastUpdated": "Dernière Mise à Jour (Hebdomadaire)", "plausibleData": "Analyses Plausible", "googleAnalyticsData": "Google Analytics", "googleConsoleData": "Google Search Console", "trafficSources": "Sources de Trafic", "topPages": "Pages Principales", "keywords": "Mots-clés", "noData": "Au<PERSON>ne donnée d'analyse disponible", "weeklyUpdates": "Donn<PERSON> mises à jour chaque semaine"}, "resourceLibrary": {"title": "Mises à Jour de la Bibliothèque de Ressources", "description": "Mettez à jour votre bibliothèque de ressources de backlinks basée sur les résultats de ce projet", "addSuccessful": "Ajouter des Ressources Réussies", "markUnsuccessful": "Marquer les Ressources Non Réussies", "updateNotes": "Mettre à Jour les Notes de Ressources", "trackPerformance": "Suivre les Performances"}, "limits": {"freeProjects": "Les utilisateurs gratuits peuvent suivre jusqu'à 5 projets", "freeUpdateFrequency": "Données d'analyse et DR mises à jour chaque semaine", "freeManualQueries": "10 requêtes DR manuelles par mois", "premiumProjects": "Les utilisateurs premium peuvent suivre jusqu'à 1000 projets", "premiumUpdateFrequency": "Données d'analyse et DR mises à jour chaque semaine", "premiumManualQueries": "100 requêtes DR manuelles par mois", "upgradePrompt": "Passez à Premium pour plus de projets et de requêtes manuelles", "noRealTimeTracking": "Suivi en temps réel non disponible", "noApiAccess": "Accès API non fourni", "noDataExport": "Exportation de données non disponible"}}, "analytics": {"title": "Analyses", "traffic": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> le trafic de votre site web depuis les plateformes d'analyse intégrées", "plausible": "Plausible Analytics", "googleAnalytics": "Google Analytics", "googleConsole": "Google Search Console", "visitors": "Visiteurs", "pageViews": "<PERSON><PERSON>", "sessions": "Sessions", "bounceRate": "<PERSON><PERSON>", "avgSessionDuration": "Durée Moyenne de Session", "topReferrers": "Principaux Référents", "topCountries": "Principaux Pays", "searchQueries": "<PERSON><PERSON><PERSON><PERSON><PERSON> de Recherche", "clicks": "C<PERSON>s", "impressions": "Impressions", "ctr": "<PERSON><PERSON>", "avgPosition": "Position Moyenne", "weeklyUpdates": "Donn<PERSON> mises à jour chaque semaine", "notRealTime": "Pas de données en temps réel"}, "backlinks": {"title": "<PERSON><PERSON><PERSON>", "description": "Surveillez votre profil de backlinks et croissance (via import SEMrush ou requêtes manuelles)", "totalBacklinks": "Total des Backlinks", "newBacklinks": "Nouveaux Backlinks", "lostBacklinks": "Backlinks Perdus", "referringDomains": "Domaines Référents", "domainRating": "Classement de Domaine", "urlRating": "Classement d'URL", "anchorText": "Texte d'Ancrage", "linkType": "Type de Lien", "status": "Statut", "firstSeen": "Vu pour la Première Fois", "lastSeen": "Vu pour la Dernière Fois", "semrushImport": "Import de Fichier SEMrush", "manualQuery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "queryLimits": "Limites de requête s'appliquent", "noDeadLinkTracking": "Suivi des liens morts non disponible"}, "integration": {"title": "Intégration d'Analyses", "description": "Connectez vos fournisseurs d'analyses pour suivre les données", "plausible": {"title": "Plausible Analytics", "description": "Connectez votre compte Plausible Analytics pour les données de trafic", "apiKey": "Clé API", "siteId": "ID du Site", "connected": "Connecté", "notConnected": "Non Connecté", "connect": "Connecter", "disconnect": "Déconnecter"}, "googleAnalytics": {"title": "Google Analytics", "description": "Connectez votre compte Google Analytics pour les insights de trafic", "connected": "Connecté", "notConnected": "Non Connecté", "connect": "Connecter avec Google", "disconnect": "Déconnecter", "selectProperty": "Sélectionner une Propriété"}, "googleConsole": {"title": "Google Search Console", "description": "Connectez votre compte Google Search Console pour les données de recherche", "connected": "Connecté", "notConnected": "Non Connecté", "connect": "Connecter avec Google", "disconnect": "Déconnecter", "selectProperty": "Sélectionner une Propriété"}, "limitations": {"title": "Limitations d'Intégration", "weeklyUpdates": "Données mises à jour chaque semaine, pas en temps réel", "noApiAccess": "Accès API non fourni", "noDataExport": "Exportation de données non disponible"}}}, "pricing": {"title": "Tarification", "free": {"title": "Plan G<PERSON>uit", "price": "<PERSON><PERSON><PERSON>", "features": {"0": "Jusqu'à 5 projets", "1": "Bibliothèque de ressources de liens externes", "2": "Suivi <PERSON> de base (10 requêtes manuelles/mois)", "3": "Surveillance d'expiration de domaines", "4": "Intégration analytique (Plausible, Google)", "5": "Mises à jour hebdomadaires des données"}, "limitations": {"0": "Limité à 5 projets", "1": "10 requêtes DR manuelles par mois", "2": "Mises à jour hebdomadaires des données seulement", "3": "Pas d'accès API", "4": "Pas d'exportation de données", "5": "Pas de suivi des liens morts"}}, "premium": {"title": "Plan Premium", "price": "29€/mois", "features": {"0": "Jusqu'à 1000 projets", "1": "Gestion avancée de la bibliothèque de ressources", "2": "<PERSON><PERSON>i <PERSON> amélioré (100 requêtes manuelles/mois)", "3": "Surveillance avancée des domaines avec alertes", "4": "Intégration analytique prioritaire", "5": "Mises à jour hebdomadaires des données", "6": "Support prioritaire", "7": "Rapports avancés"}, "upgrade": "Passer à Premium", "benefits": {"0": "Plus de requêtes DR manuelles", "1": "<PERSON><PERSON><PERSON> jusqu'à 1000 projets", "2": "Alertes de domaine avancées", "3": "Support client prioritaire"}, "limitations": {"0": "Encore des mises à jour hebdomadaires (pas en temps réel)", "1": "Pas d'accès API", "2": "Pas d'exportation de données", "3": "Pas de suivi des liens morts"}}}, "limitations": {"title": "Limitations de la Plateforme", "description": "Comprendre ce que MyBackLinks fait et ne supporte pas", "noDeadLinkTracking": {"title": "Pas de Suivi des Liens Morts", "description": "Détection automatique et surveillance des liens morts non disponible"}, "noApiAccess": {"title": "Pas d'Accès API", "description": "Points de terminaison API pour intégrations externes non fournis"}, "noDataExport": {"title": "Pas d'Exportation de Données", "description": "Fonctionnalité d'exportation massive de données non disponible"}, "weeklyUpdates": {"title": "Mises à Jour Hebdomadaires des Données", "description": "Les données sont mises à jour environ une fois par semaine, pas en temps réel"}, "manualQueries": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Les requêtes manuelles DR et backlinks sont limitées selon votre plan"}}, "features": {"title": "Fonctionnalités Principales", "description": "Ce que MyBackLinks vous aide à accomplir", "personalLibrary": {"title": "Bibliothèque Personnelle de Ressources de Backlinks", "description": "Maintenez et organisez votre collection de plateformes de soumission de backlinks"}, "projectTracking": {"title": "Suivi de Projets Parallèles", "description": "Suivez les valeurs DR et backlinks pour vos projets parallèles via import SEMrush ou requêtes manuelles"}, "resourceMaintenance": {"title": "Maintenance de Bibliothèque de Ressources", "description": "Mettez à jour votre bibliothèque de ressources basée sur les résultats et performances du projet"}, "domainManagement": {"title": "Gestion d'Expiration de Domaines", "description": "Surveillez les dates d'expiration des domaines et évitez les renouvellements manqués"}, "analyticsIntegration": {"title": "Intégration d'Analyses", "description": "Connectez-vous avec Plausible, Google Analytics et Google Search Console pour des insights de trafic"}}, "subscription": {"title": "Gestion d'Abonnement", "description": "G<PERSON>rez votre abonnement MyBackLinks et consultez les statistiques d'utilisation", "current_plan": "Plan Actuel", "usage_summary": "Résumé d'Utilisation", "upgrade_plan": "Mettre à Niveau le Plan", "manage_billing": "G<PERSON>rer la Facturation", "features": "Fonctionnalités", "limits": "Limites", "projects": "Projets", "domains": "Domaines", "link_resources": "Ressources de Liens", "dr_queries": "Requêtes DR", "traffic_updates": "<PERSON><PERSON> à Jour de Trafic", "free_tier": "Niveau Gratuit", "professional_tier": "Niveau Professionnel", "unlimited": "Illimité", "per_month": "par mois", "upgrade_now": "Mettre à Niveau Maintenant", "contact_support": "<PERSON><PERSON> le <PERSON>"}, "blog": {"title": "Blog", "description": "Dernières idées et mises à jour de notre équipe", "read_more_text": "Lire Plus"}, "blocks": {"hero": {"title": "Construisez votre Bibliothèque Ultime de Ressources de Liens Externes", "description": "Suivez le trafic du site web, surveillez les évaluations de domaine et gérez votre portefeuille de liens externes pour une meilleure promotion et croissance de projets.", "announcement": {"title": "Nouveau Tableau de Bord Analytique Disponible", "url": "/dashboard"}, "buttons": {"primary": "Commencer à Construire votre Bibliothèque", "secondary": "Voir la Démo en Direct"}, "tip": "Le plan gratuit inclut jusqu'à 5 projets et une intégration analytique de base"}, "feature1": {"title": "Analyses de Trafic et DR en Temps Réel", "description": "Surveillez les performances de vos projets parallèles avec une intégration analytique complète et un suivi des évaluations de domaine pour optimiser votre stratégie de croissance.", "features": {"traffic_monitoring": {"title": "Intégration d'Analyses de Trafic", "description": "Connectez-vous à Google Analytics, Plausible et Google Search Console pour des aperçus de trafic unifiés sur tous vos projets."}, "dr_tracking": {"title": "Surveillance des Évaluations de Domaine", "description": "Suivez les changements de DR au fil du temps via l'intégration SEMrush ou les requêtes manuelles pour mesurer efficacement vos progrès SEO."}, "backlink_analysis": {"title": "Gestion du Portefeuille de Backlinks", "description": "Surveillez les nouveaux backlinks et ceux perdus, analysez les domaines référents et suivez la qualité de vos efforts de création de liens."}, "performance_insights": {"title": "Aperçus de Performance de Croissance", "description": "Obtenez des aperçus exploitables sur les tendances de trafic, les classements de mots-clés et les opportunités de backlinks pour accélérer la croissance des projets."}}}, "feature2": {"title": "Gestion Intelligente des Ressources de Liens Externes", "description": "Construisez et maintenez votre base de données personnelle d'opportunités de backlinks de haute qualité avec suivi des performances et surveillance des taux de succès.", "features": {"resource_library": {"title": "Base de Données de Ressources Curées", "description": "Organisez les opportunités de liens externes par catégorie, suivez les taux de succès et construisez votre flux de travail de soumission personnalisé."}, "performance_tracking": {"title": "Ana<PERSON><PERSON> des Taux de Succès", "description": "Surveillez les temps de réponse, les taux d'approbation et le ROI pour chaque ressource afin d'optimiser votre stratégie d'approche au fil du temps."}, "quality_management": {"title": "Système d'Assurance Qualité", "description": "Évaluez et examinez les plateformes en fonction de votre expérience, maintenant une bibliothèque de ressources de haute qualité pour les futures campagnes."}}}, "feature": {"title": "Gestion Complète du Portefeuille de Domaines", "description": "Ne manquez plus jamais un renouvellement de domaine avec surveillance automatisée d'expiration et outils complets de gestion de domaines.", "features": {"expiration_monitoring": {"title": "Alertes d'Expiration", "description": "Recevez des notifications opportunes avant l'expiration du domaine pour prévenir les interruptions de service et protéger votre marque."}, "whois_tracking": {"title": "Gestion des Données WHOIS", "description": "Suivez automatiquement les informations de registraire, les paramètres DNS et les coûts de renouvellement pour tous vos domaines en un seul endroit."}, "project_association": {"title": "Intégration de Projets", "description": "Liez les domaines à des projets spécifiques pour un suivi holistique de votre portefeuille d'actifs numériques et des métriques de performance."}}}, "pricing": {"title": "Choisissez Votre Plan de Croissance", "description": "Commencez gratuitement et évoluez avec vos projets. Pas de frais cachés, annulez à tout moment.", "plans": {"free": {"name": "Débutant", "price": "<PERSON><PERSON><PERSON>", "description": "Parfait pour les tests et petits projets", "features": ["Jusqu'à 5 projets de suivi", "Bibliothèque de ressources de liens externes", "Suivi DR de base (10 requêtes/mois)", "Surveillance d'expiration de domaines", "Intégration analytique", "Mises à jour hebdomadaires des données"], "button": "Commencer Gratuitement", "tip": "Aucune carte de crédit requise"}, "premium": {"name": "Professionnel", "price": "29€", "unit": "/mois", "description": "Idéal pour les constructeurs sérieux de projets parallèles", "features": ["Jusqu'à 1000 projets de suivi", "Gestion avancée des ressources", "<PERSON><PERSON>i <PERSON> amélioré (100 requêtes/mois)", "Surveillance avancée des domaines", "Intégration analytique prioritaire", "Mises à jour hebdomadaires des données", "Support prioritaire", "Rapports avancés"], "button": "Passer à Pro", "popular": true}}}, "testimonials": {"title": "Aimé par les Constructeurs de Projets Parallèles", "description": "<PERSON><PERSON>ez comment les créateurs font croître leurs projets avec notre plateforme"}, "faq": {"title": "Questions Fréquemment Po<PERSON>ées", "description": "Tout ce que vous devez savoir sur la construction de votre bibliothèque de ressources de liens externes", "questions": {"what_is_platform": {"question": "Qu'est-ce que MyBackLinks et comment aide-t-il mes projets parallèles ?", "answer": "MyBackLinks vous aide à construire et maintenir une bibliothèque personnelle d'opportunités de liens externes tout en suivant les métriques de performance de vos projets comme le trafic, DR et les backlinks."}, "how_track_dr": {"question": "Comment suivez-vous l'évaluation de domaine et les backlinks ?", "answer": "Nous nous intégrons avec SEMrush pour l'import de données et fournissons des options de requête manuelle. Vous pouvez télécharger des rapports SEMrush ou utiliser nos requêtes manuelles limitées basées sur votre plan."}, "analytics_integration": {"question": "Quelles plateformes analytiques supportez-vous ?", "answer": "Nous nous intégrons avec Google Analytics, Google Search Console et Plausible Analytics pour fournir des aperçus de trafic unifiés pour vos projets."}, "resource_library": {"question": "Comment fonctionne la bibliothèque de ressources de liens externes ?", "answer": "Vous pouvez organiser et suivre les opportunités de liens externes par catégorie, surveiller les taux de succès et construire votre flux de travail d'approche personnalisé pour de meilleurs résultats."}, "data_updates": {"question": "À quelle fréquence les données sont-elles mises à jour ?", "answer": "Les données analytiques et DR sont mises à jour environ une fois par semaine. Nous ne fournissons pas de suivi en temps réel pour garder les coûts gérables et nous concentrer sur les tendances."}, "plan_limits": {"question": "Quelles sont les différences entre les plans gratuit et premium ?", "answer": "Les utilisateurs gratuits peuvent suivre jusqu'à 5 projets avec 10 requêtes DR manuelles par mois. Les utilisateurs premium obtiennent jusqu'à 1000 projets avec 100 requêtes manuelles et un support prioritaire."}}}, "cta": {"title": "<PERSON>r<PERSON><PERSON> à Développer votre Empire de Projets Parallèles ?", "description": "Rejoignez des milliers de créateurs construisant des bibliothèques durables de liens externes et suivant le succès de leurs projets.", "button": {"primary": "Commencer à Construire Gratuitement", "secondary": "Voir la Démo en Direct"}}}, "faq": {"helpful_information": "Informations utiles", "still_have_questions": "Vous avez encore des questions ?", "contact_support_description": "Nous sommes là pour vous aider à construire votre empire de liens externes", "contact_support": "<PERSON><PERSON> le <PERSON>"}}