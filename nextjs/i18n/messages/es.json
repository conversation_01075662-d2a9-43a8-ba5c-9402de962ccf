{"user": {"sign_in": "<PERSON><PERSON><PERSON>", "sign_out": "<PERSON><PERSON><PERSON>", "my_orders": "<PERSON>s Pedidos", "no_auth_or_email": "No se encontró autenticación o email"}, "task_succeeded": "Tarea completada exitosamente", "file": "Archivo", "status": {"online": "En línea", "view_pending": "Ver pendientes", "created": "<PERSON><PERSON><PERSON>", "no_pending": "Sin elementos pendientes", "error": "Error", "sent": "Enviado", "title": "Estado", "failed": "Fallido", "active": "Activo", "description": "Descripción", "inactive": "Inactivo", "pending": "Pendiente", "offline": "Fuera de línea"}, "link_type": {}, "user_id": "ID de Usuario", "historyLimit": "Límite de Historial", "withStats": "Con Estadísticas", "action": "Acción", "table": {"created_at": "Creado en", "id": "ID", "actions": "Acciones", "name": "Nombre", "updated_at": "Actualizado en", "event_type": "Tipo de Evento", "status": "Estado", "sent_at": "Enviado en", "tools_count": "Cantidad de Herramientas", "type": "Tipo", "recipient": "Destinatario", "subject": "<PERSON><PERSON><PERSON>", "item_uuid": "UUID del Elemento"}, "no_image_result": "Sin resultado de imagen", "no_logs": "Sin registros disponibles", "no_templates": "Sin plantillas disponibles", "category": "Categoría", "result_image": "Imagen de Resultado", "cancel_button": "<PERSON><PERSON><PERSON>", "completed_at": "Completado en", "error_reason": "Razón del Error", "my_tasks": "<PERSON><PERSON>", "T": "T", "update_error": "Error de Actualización", "saved_success": "Guardado Exitosamente", "create_error": "Error de Creación", "details": {}, "selected_items": "Elementos Seleccionados", "no_json_result": "Sin resultado JSON", "searchTerm": "Término de Búsqueda", "task_status": "Estado de Tarea", "type": "Tipo", "empty": {"no_tools": "Sin herramientas disponibles", "no_parameters": "Sin parámetros disponibles", "no_tools_to_translate": "Sin herramientas para traducir"}, "prev_page": "Página Anterior", "error_loading": "<PERSON><PERSON><PERSON>", "provider": "<PERSON><PERSON><PERSON><PERSON>", "content": "Contenido", "search": "Buscar", "task_processing": "<PERSON>ces<PERSON><PERSON>", "projectId": "ID del Proyecto", "create_success": "Creado Exitosamente", "div": "División", "my_tasks_description": "Descripción de Mis Tareas", "print_result": "<PERSON><PERSON><PERSON><PERSON>", "slug": "Slug", "a": "Enlace", "brief": "Resumen", "test": "Prueba", "sort": "Ordenar", "timeRange": "<PERSON><PERSON>", "query": "Consulta", "author_avatar_url": "URL del Avatar del Autor", "saved": "Guardado", "tab_json": "Pestaña JSON", "saved_error": "<PERSON><PERSON><PERSON> al Guardar", "url": "URL", "update_success": "Actualizado Exitosamente", "tip": "Consejo", "host": "<PERSON><PERSON><PERSON><PERSON>", "task_view": "Vista de Tarea", "processinfo": "Información del Proceso", "isPaid": "Es de Pago", "q": "Pregunta", "is_recommended": "<PERSON><PERSON>", "checked_at": "Verificado en", "page": "<PERSON><PERSON><PERSON><PERSON>", "keywords": "Palabra<PERSON>", "video_urls": "URLs de Video", "uuid": "UUID", "tab_image": "Pestaña de Imagen", "sessionId": "ID de Sesión", "no_tasks": "Sin tareas disponibles", "goHome": "<PERSON><PERSON> <PERSON> Inici<PERSON>", "task_date": "<PERSON><PERSON>", "current": "Actual", "image_urls": "URLs de Imagen", "create_button": "<PERSON><PERSON><PERSON>", "tab_text": "Pestaña de Texto", "processing_time": "Tiempo de Procesamiento", "task_credit_cost": "Costo de Crédito de Tarea", "cover_url": "URL de Portada", "save": "Guardar", "Authorization": "Autorización", "signin_type": "Tipo de Inicio de Sesión", "invite_code": "Código de Invitación", "lang": "Idioma", "view_task_result": "<PERSON>er Resultado <PERSON>", "refresh_button": "Actualizar", "back_to_submissions": "Volver a Envíos", "results_title": "Resul<PERSON><PERSON>", "saving": "Guardando...", "USD": "USD", "offset": "Desplazamiento", "website_url": "URL del Sitio Web", "task_product": "Producto de Tarea", "tagName": "Nombre de Etiqueta", "task_pending": "<PERSON><PERSON>", "stdio": "Entrada/Salida Estándar", "sse": "Eventos Enviados por Servidor", "includeHistory": "Incluir <PERSON>", "is_official": "Es Oficial", "task_result": "Resultado de Tarea", "title": "<PERSON><PERSON><PERSON><PERSON>", "limit": "Límite", "process_button": "Procesar", "next_page": "<PERSON><PERSON><PERSON><PERSON>", "author_name": "Nombre del Autor", "locale": "Configuración Regional", "update_button": "Actualizar", "description": "Descripción", "field": "Campo", "api_key": "Clave API", "ai_summary": "Resumen de IA", "domain": {"management": {"title": "Gestión de Dominios", "description": "Gestiona fechas de expiración de dominios y evita renovaciones perdidas", "addDomain": "A<PERSON><PERSON><PERSON>", "editDomain": "<PERSON><PERSON>", "deleteDomain": "Eliminar Dom<PERSON>", "refreshWhois": "Actualizar WHOIS", "bulkImport": "Importación Masiva", "associateProject": "Asociar Proyecto", "removeAssociation": "Eliminar Asociación", "searchPlaceholder": "Buscar dominios...", "noDomains": "No se encontraron dominios", "loading": "Cargando...", "confirmDelete": "¿Estás seguro de que quieres eliminar este dominio?", "confirmDeleteMessage": "Esta acción no se puede deshacer.", "domainAdded": "<PERSON>inio ag<PERSON> exitosamente", "domainUpdated": "Dominio actualizado exitosamente", "domainDeleted": "Dominio eliminado exitosamente", "whoisRefreshed": "Datos WHOIS actualizados exitosamente", "errorAddingDomain": "Error al agregar dominio", "errorUpdatingDomain": "<PERSON><PERSON>r al actualizar dominio", "errorDeletingDomain": "Error al eliminar dominio", "errorRefreshingWhois": "Error al actualizar datos WHOIS", "expirationTracking": "Rastrea fechas de expiración para evitar renovaciones perdidas"}, "status": {"active": "Activo", "expired": "<PERSON><PERSON><PERSON>", "expiring": "Expirando <PERSON>", "unknown": "Desconocido"}, "stats": {"total": "Total de Dominios", "active": "Activo", "expiring": "Expirando <PERSON>", "expired": "<PERSON><PERSON><PERSON>", "projects": "Proyectos Asociados"}, "form": {"domain": "<PERSON>inio", "registrar": "Registrador", "dnsProvider": "Proveedor DNS", "createdDate": "Fecha de Creación", "expiryDate": "Fecha de Expiración", "registrationPrice": "Precio de Registro", "renewalPrice": "Precio de Renovación", "currency": "Moneda", "autoRenew": "Renovación Automática", "monitorExpiry": "Monitorear Expiración", "alertDaysBefore": "<PERSON><PERSON><PERSON>", "notes": "Notas", "tags": "Etiquetas", "isFavorite": "<PERSON><PERSON><PERSON><PERSON>", "nameServers": "<PERSON><PERSON><PERSON> Nombres", "required": "Requerido", "optional": "Opcional", "enterDomain": "Ingresa nombre de dominio", "enterRegistrar": "Ingresa nombre del registrador", "enterDnsProvider": "Ingresa proveedor DNS", "enterNotes": "Ingresa notas", "enterTags": "Ingresa etiquetas (separadas por comas)", "selectCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectProject": "Seleccionar proyecto", "daysBeforeExpiry": "Días antes de expiración"}, "filter": {"all": "Todos", "status": "Estado", "registrar": "Registrador", "dnsProvider": "Proveedor DNS", "project": "Proyecto", "expiring": "Expirando <PERSON>", "favorites": "<PERSON><PERSON><PERSON><PERSON>"}, "sort": {"domain": "<PERSON>inio", "expiryDate": "Fecha de Expiración", "createdDate": "Fecha de Creación", "registrar": "Registrador", "ascending": "Ascendente", "descending": "Descendente"}, "table": {"domain": "<PERSON>inio", "registrar": "Registrador", "dnsProvider": "Proveedor DNS", "status": "Estado", "expiryDate": "Fecha de Expiración", "createdDate": "Fecha de Creación", "projects": "Proyectos", "actions": "Acciones", "daysUntilExpiry": "Días Hasta Expiración", "whoisLastUpdated": "WHOIS Actualizado", "expires": "Expira", "renewalPrice": "Precio de Renovación", "never": "Nunca", "view": "<PERSON>er", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "refresh": "Actualizar", "associate": "Asociar", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "unfavorite": "<PERSON><PERSON><PERSON>"}, "whois": {"data": "Datos WHOIS", "lastUpdated": "Última Actualización", "cacheExpires": "El Cache Expira", "refreshing": "Actualizando...", "noData": "Sin datos WHOIS disponibles", "expired": "Datos WHOIS expirados", "clickToRefresh": "Haz clic para actualizar datos WHOIS"}, "project": {"association": "Asociación de Proyecto", "associatedProjects": "Proyectos Asociados", "noAssociatedProjects": "Sin proyectos asociados", "selectProject": "Selecciona un proyecto para asociar", "isPrimary": "Dominio principal para el proyecto", "associationAdded": "Asociación de proyecto agregada", "associationRemoved": "Asociación de proyecto eliminada", "errorAddingAssociation": "Error al agregar asociación de proyecto", "errorRemovingAssociation": "Error al eliminar asociación de proyecto"}, "monitoring": {"title": "Monitoreo de Expiración de Dominios", "expiryMonitoring": "Monitoreo de Expiración", "alertSettings": "Configuraciones de Alerta", "lastAlertSent": "Última Alerta Enviada", "noAlerts": "Sin alertas enviadas aún", "alertsEnabled": "<PERSON><PERSON><PERSON>", "alertsDisabled": "<PERSON><PERSON><PERSON>", "preventMissedRenewals": "Prevén renovaciones perdidas con alertas de expiración"}, "import": {"title": "Importación Masiva de Dominios", "description": "Importa múltiples dominios a la vez", "format": "Formato: dominio.com,registrador,proveedor-dns", "example": "Ejemplo: ejemplo.com,<PERSON><PERSON><PERSON><PERSON>,Cloudflare", "paste": "Pega dominios (uno por línea)", "import": "Importar Dominios", "importing": "Importando...", "success": "Se importaron exitosamente {count} dominios", "error": "Error al importar dominios", "invalidFormat": "Formato inválido en la línea {line}", "duplicateDomain": "Dominio duplicado: {domain}"}}, "task_failed_status": "<PERSON><PERSON>", "no_text_result": "Sin resultado de texto", "invitation": {"invites_count": "Cantidad de Invitaciones", "earn_credits": "<PERSON><PERSON><PERSON>", "share_title": "Compartir Invitación", "credits_per_invite": "Créditos por Invitación", "copy": "Copiar", "share_text": "¡Comparte este enlace de invitación con tus amigos!", "copy_success": "¡Copiado al portapapeles!", "how_it_works": "Cómo Funciona", "your_code": "Tu Código", "copying": "Copiando...", "share_success": "¡Compartido exitosamente!", "share_description": "Invita amigos y gana créditos", "share_code": "Compartir Código", "title": "Sistema de Invitación", "share_invite_link": "Compartir Enlace de Invitación", "copy_failed": "Error al copiar", "share": "Compartir", "share_failed": "Error al compartir", "successful_invites": "Invitaciones Exitosas"}, "editor": {"result_image": "Imagen de Resultado", "copied_json": "¡JSON copiado al portapapeles!", "results_title": "Resul<PERSON><PERSON>", "copy": "Copiar", "usage_processing_time": "Tiempo de Procesamiento", "my_tasks": "<PERSON><PERSON>", "usage_supported_formats": "Formatos Soportados", "upload_description": "Sube tu archivo para procesamiento", "drop_files": "Suelta archivos aquí para subir", "tab_text": "Texto", "no_json_result": "Sin resultado JSON", "no_text_result": "Sin resultado de texto", "processing_file": "Procesando archivo...", "processing": "Procesando...", "file_selected": "Archivo seleccionado", "error_polling": "Error al obtener resultados", "unexpected_error": "Ocurrió un error inesperado", "usage_max_file_size": "Tamaño máximo de archivo", "task_complete": "<PERSON><PERSON> completada", "no_image_result": "Sin resultado de imagen", "usage_tips_title": "Consejos de Uso", "error_no_file": "No se seleccionó archivo", "process_file": "Procesar Archivo", "error": "Error", "tab_json": "JSON", "usage_sign_in": "Inicia sesión para usar esta función", "error_file_size": "El tamaño del archivo excede el límite", "usage_credit_cost": "Costo de crédito", "error_file_type": "Tipo de archivo no soportado", "task_timeout": "Tiempo de espera de tarea agotado", "error_create_task": "Error al crear tarea", "upload_title": "Subir Archivo", "copied_text": "¡Texto copiado al portapapeles!", "upload_process_prompt": "Sube y procesa tu archivo", "file_type": "Tipo de archivo", "remove_file": "Eliminar archivo", "results_description": "Resultados del procesamiento", "tab_image": "Imagen", "max_file_size": "Tamaño máximo de archivo", "unknown_error": "Error descon<PERSON>", "task_failed": "<PERSON><PERSON> fall<PERSON>"}, "linkResources": {"title": "Biblioteca de Recursos de Enlaces Externos", "description": "Gestiona tu colección de recursos de enlaces externos y rastrea su efectividad para tus proyectos", "loading": "Cargando...", "noResults": "No se encontraron recursos", "resultsCount": "{count} recursos encontrados", "paid": "<PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON><PERSON>", "common": {"home": "<PERSON><PERSON>o"}, "seo": {"title": "Gestión de Recursos de Enlaces Externos", "description": "Tu base de datos de plataformas de enlaces externos verificadas con seguimiento de rendimiento", "keywords": "recursos de enlaces externos, biblioteca de enlaces, gestión de proyectos, herramientas SEO, construcción de enlaces", "categoryDescription": "Explorar recursos de backlinks de {category} en tu biblioteca", "paidDescription": "Recursos de backlinks premium en tu colección", "freeDescription": "Recursos de backlinks gratuitos en tu colección", "page": "<PERSON><PERSON><PERSON><PERSON>", "additionalTitle": "Cómo Funciona tu Biblioteca de Recursos Personal", "howItWorks": "Cómo Funciona", "howItWorksDesc": "Tu enfoque sistemático para gestionar recursos de backlinks", "step1": "Construye tu biblioteca personal de plataformas de envío verificadas", "step2": "Rastrea métricas de rendimiento y tasas de éxito", "step3": "Actualiza el estado de los recursos basándote en los resultados del proyecto", "step4": "Mantén la calidad a través de revisión y actualizaciones regulares", "benefits": "<PERSON><PERSON><PERSON><PERSON>", "benefitsDesc": "Por qué mantener tu biblioteca de recursos personal", "benefit1": "Colección curada adaptada a tus proyectos", "benefit2": "Rastrea tasas de éxito y ROI para cada recurso", "benefit3": "<PERSON><PERSON>ta perder tiempo en plataformas de baja calidad", "benefit4": "Construye conocimiento institucional a lo largo del tiempo"}, "stats": {"totalResources": "Total de Recursos en la Biblioteca", "categories": "Categor<PERSON>", "highAuthority": "Tasa de Éxito Promedio"}, "features": {"title": "Características de la Biblioteca Personal", "highQuality": "Seguimiento de Calidad", "highQualityDesc": "Monitorea rendimiento y tasas de éxito", "verified": "Verificación Personal", "verifiedDesc": "Rastrea tu propia experiencia con cada plataforma", "qualityAssured": "Actualizaciones Continuas", "qualityAssuredDesc": "Mantén tu biblioteca actual y efectiva", "updated": "Monitoreo de Rendimiento", "updatedDesc": "Rastrea ROI y efectividad a lo largo del tiempo"}, "filters": {"searchPlaceholder": "Buscar tus recursos...", "categoryPlaceholder": "Seleccionar categoría", "allCategories": "Todas las Categorías", "pricingPlaceholder": "Seleccionar precio", "allPricing": "Todos los Precios", "free": "Solo Gratuito", "paid": "Solo de Pago", "sortPlaceholder": "Ordenar por", "sortByDRDesc": "Clasificación de Dominio (Alto a Bajo)", "sortByDRAsc": "Clasificación de Dominio (Bajo a Alto)", "sortByTrafficDesc": "Tráfico (Alto a Bajo)", "sortByTrafficAsc": "T<PERSON>áfico (Bajo a Alto)", "sortBySuccessRateDesc": "<PERSON><PERSON> (Alto a Bajo)", "sortByNewest": "Agregado Recientemente"}, "categories": {"directory": "Directorio", "blog": "Blog", "news": "Noticias", "resource-page": "Página de Recursos", "guest-post": "Artículo de Invitado", "forum": "Foro", "social-media": "Redes Sociales", "press-release": "Comunicado de Prensa", "startup-directory": "Directorio de Startups", "tool-directory": "Directorio de Herramientas"}, "details": {"submissionMethod": "<PERSON><PERSON><PERSON><PERSON>", "responseTime": "Tiempo de Respuesta", "successRate": "Tasa de Éxito Personal", "priceRange": "<PERSON><PERSON>", "requirements": "Requisitos", "lastUsed": "<PERSON><PERSON><PERSON>", "personalNotes": "Notas Personales"}, "actions": {"submitHere": "<PERSON><PERSON><PERSON>", "visitWebsite": "Visitar Sitio Web", "contact": "Contacto", "updateStatus": "<PERSON><PERSON><PERSON><PERSON>", "addNotes": "<PERSON><PERSON><PERSON><PERSON>"}, "pagination": {"page": "<PERSON><PERSON><PERSON><PERSON>", "of": "de", "previous": "Anterior", "next": "Siguient<PERSON>"}, "faq": {"title": "Preguntas Frecuentes", "q1": "¿Qué es una biblioteca personal de recursos de backlinks?", "a1": "Una colección curada de plataformas de backlinks que has probado y verificado personalmente para tus proyectos paralelos.", "q2": "¿Cómo rastreas el rendimiento de los recursos?", "a2": "Registra tasas de éxito, tiempos de respuesta y notas personales para cada plataforma que uses.", "q3": "¿Puedo importar recursos existentes?", "a3": "<PERSON><PERSON>, puedes importar en masa tus recursos de backlinks existentes y agregar datos de rendimiento.", "q4": "¿Con qué frecuencia debo actualizar mi biblioteca?", "a4": "Revisa y actualiza tu biblioteca semanalmente basándote en nuevos envíos y resultados.", "q5": "¿Qué métricas debo rastrear?", "a5": "Rastrea tasas de éxito, tiempos de respuesta, costos y notas de efectividad personal.", "q6": "¿Cómo ayuda esto a mis proyectos paralelos?", "a6": "Mantén una biblioteca de recursos de calidad para construir backlinks eficientemente para futuros proyectos."}}, "links": {"traffic": "Tráfico", "title": "Enlaces", "add": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Guardando...", "cancel": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "a": "Enlace", "dr_score": "Puntuación DR", "add_link": "<PERSON><PERSON><PERSON><PERSON>"}, "SubmitPage": {"heading": "Agregar Recurso a la Biblioteca", "home": "<PERSON><PERSON>o", "submit": "<PERSON>g<PERSON><PERSON>", "title": "<PERSON>g<PERSON><PERSON>", "description": "Agrega un nuevo recurso de backlink a tu biblioteca personal"}, "SearchPage": {"search": "Buscar", "title": "Buscar Recursos", "description": "Busca en tu biblioteca personal de recursos de backlinks", "home": "<PERSON><PERSON>o"}, "metadata": {"title": "MyBackLinks - Gestión de Recursos de Enlaces Externos y Analíticas de Proyectos", "description": "Gestiona tu biblioteca de recursos de enlaces externos, rastrea valores DR y backlinks de proyectos, monitorea expiración de dominios e integra con plataformas de analíticas", "keywords": "biblioteca de enlaces externos, analíticas de proyectos, gestión de dominios, seguimiento DR, integración SEMrush, analíticas plausible"}, "NotFound": {"title": "Página No Encontrada", "description": "La página que buscas no existe o ha sido movida.", "goHome": "<PERSON><PERSON> <PERSON> Inici<PERSON>"}, "sign_modal": {"sign_in_title": "<PERSON><PERSON><PERSON>", "sign_in_description": "Inicia sesión para acceder a tu biblioteca personal de backlinks, seguimiento de proyectos paralelos y herramientas de gestión de dominios", "cancel_title": "<PERSON><PERSON><PERSON>", "google_sign_in": "Google", "github_sign_in": "GitHub"}, "integrations": "Integraciones", "projects": {"title": "Proyectos", "description": "Rastrea valores DR, backlinks y analíticas de tus proyectos", "addProject": "Agregar Proyecto", "editProject": "Editar Proye<PERSON>o", "deleteProject": "Eliminar Proyecto", "viewAnalytics": "Ver Analíticas", "projectUrl": "URL del Proyecto", "projectName": "Nombre del Proyecto", "projectDescription": "Descripción del Proyecto", "noProjects": "No se encontraron proyectos", "createFirst": "Crea tu primer proyecto para comenzar el seguimiento", "drTracking": {"title": "Seguimiento de Clasificación de Dominio", "currentDR": "DR Actual", "drHistory": "Historial de DR", "lastUpdated": "Última Actualización", "uploadSemrush": "Subir Archivo SEMrush", "manualUpdate": "Actualización Manual", "limitedQueries": "Consultas manuales limitadas disponibles"}, "backlinks": {"title": "Seguimiento de Backlinks", "totalBacklinks": "Total de Backlinks", "newBacklinks": "Nuevos Backlinks", "lostBacklinks": "Backlinks Perdidos", "referringDomains": "Dominios de Referencia", "backlinkSources": "Fuentes de Backlinks", "qualityScore": "Puntuación de Calidad", "noDeadLinkTracking": "Nota: Seguimiento de enlaces muertos no disponible"}, "analytics": {"title": "Analíticas del Proyecto", "traffic": "Resumen de Tráfico", "backlinks": "Backlinks", "domains": "<PERSON><PERSON><PERSON>", "lastUpdated": "Última Actualización (Semanal)", "plausibleData": "Analíticas Plausible", "googleAnalyticsData": "Google Analytics", "googleConsoleData": "Google Search Console", "trafficSources": "Fuentes de Tráfico", "topPages": "Páginas Principales", "keywords": "Palabra<PERSON>", "noData": "Sin datos de analíticas disponibles", "weeklyUpdates": "Datos actualizados semanalmente"}, "resourceLibrary": {"title": "Actualizaciones de Biblioteca de Recursos", "description": "Actualiza tu biblioteca de recursos de backlinks basándote en los resultados de este proyecto", "addSuccessful": "Agregar <PERSON><PERSON>", "markUnsuccessful": "Marcar Recurs<PERSON> No Exitosos", "updateNotes": "Actualizar Notas de Recursos", "trackPerformance": "<PERSON><PERSON><PERSON><PERSON>"}, "limits": {"freeProjects": "Los usuarios gratuitos pueden rastrear hasta 5 proyectos", "freeUpdateFrequency": "Datos de analíticas y DR actualizados semanalmente", "freeManualQueries": "10 consultas DR manuales por mes", "premiumProjects": "Los usuarios premium pueden rastrear hasta 1000 proyectos", "premiumUpdateFrequency": "Datos de analíticas y DR actualizados semanalmente", "premiumManualQueries": "100 consultas DR manuales por mes", "upgradePrompt": "Actualiza a Premium para más proyectos y consultas manuales", "noRealTimeTracking": "Seguimiento en tiempo real no disponible", "noApiAccess": "Acceso API no proporcionado", "noDataExport": "Exportación de datos no disponible"}}, "analytics": {"title": "Analíticas", "traffic": {"title": "Analíticas de Tráfico", "description": "Rastrea el tráfico de tu sitio web desde plataformas de analíticas integradas", "plausible": "Plausible Analytics", "googleAnalytics": "Google Analytics", "googleConsole": "Google Search Console", "visitors": "Visitantes", "pageViews": "Vistas de Página", "sessions": "Sesiones", "bounceRate": "<PERSON><PERSON>", "avgSessionDuration": "Duración Promedio de Sesión", "topReferrers": "Principales Referencias", "topCountries": "Principales Países", "searchQueries": "Consultas de Búsqueda", "clicks": "C<PERSON>s", "impressions": "Impresiones", "ctr": "<PERSON><PERSON>", "avgPosition": "Posición Promedio", "weeklyUpdates": "Datos actualizados semanalmente", "notRealTime": "No son datos en tiempo real"}, "backlinks": {"title": "Analíticas de Backlinks", "description": "Monitorea tu perfil de backlinks y crecimiento (vía importación SEMrush o consultas manuales)", "totalBacklinks": "Total de Backlinks", "newBacklinks": "Nuevos Backlinks", "lostBacklinks": "Backlinks Perdidos", "referringDomains": "Dominios de Referencia", "domainRating": "Clasificación de Dominio", "urlRating": "Clasificación de URL", "anchorText": "Texto de Anclaje", "linkType": "<PERSON><PERSON><PERSON>", "status": "Estado", "firstSeen": "Visto por Primera Vez", "lastSeen": "Visto por Última Vez", "semrushImport": "Importación de Archivo SEMrush", "manualQuery": "Consulta Manual", "queryLimits": "Se aplican límites de consulta", "noDeadLinkTracking": "Seguimiento de enlaces muertos no disponible"}, "integration": {"title": "Integración de Analíticas", "description": "Conecta tus proveedores de analíticas para rastrear datos", "plausible": {"title": "Plausible Analytics", "description": "Conecta tu cuenta de Plausible Analytics para datos de tráfico", "apiKey": "Clave API", "siteId": "ID del Sitio", "connected": "Conectado", "notConnected": "No Conectado", "connect": "Conectar", "disconnect": "Desconectar"}, "googleAnalytics": {"title": "Google Analytics", "description": "Conecta tu cuenta de Google Analytics para perspectivas de tráfico", "connected": "Conectado", "notConnected": "No Conectado", "connect": "Conectar con Google", "disconnect": "Desconectar", "selectProperty": "Seleccionar Propiedad"}, "googleConsole": {"title": "Google Search Console", "description": "Conecta tu cuenta de Google Search Console para datos de búsqueda", "connected": "Conectado", "notConnected": "No Conectado", "connect": "Conectar con Google", "disconnect": "Desconectar", "selectProperty": "Seleccionar Propiedad"}, "limitations": {"title": "Limitaciones de Integración", "weeklyUpdates": "Datos actualizados semanalmente, no en tiempo real", "noApiAccess": "Acceso API no proporcionado", "noDataExport": "Exportación de datos no disponible"}}}, "pricing": {"title": "<PERSON><PERSON><PERSON>", "free": {"title": "Plan Gratuito", "price": "<PERSON><PERSON><PERSON><PERSON>", "features": {"0": "Hasta 5 proyectos", "1": "Biblioteca de recursos de enlaces externos", "2": "Seguimiento básico de DR (10 consultas manuales/mes)", "3": "Monitoreo de expiración de dominios", "4": "Integración de analíticas (Plausible, Google)", "5": "Actualizaciones semanales de datos"}, "limitations": {"0": "Limitado a 5 proyectos", "1": "10 consultas DR manuales por mes", "2": "Solo actualizaciones semanales de datos", "3": "Sin acceso API", "4": "Sin exportación de datos", "5": "Sin seguimiento de enlaces muertos"}}, "premium": {"title": "Plan Premium", "price": "$29/mes", "features": {"0": "Hasta 1000 proyectos", "1": "Gestión avanzada de biblioteca de recursos", "2": "Seguimiento mejorado de DR (100 consultas manuales/mes)", "3": "Monitoreo avanzado de dominios con alertas", "4": "Integración prioritaria de analíticas", "5": "Actualizaciones semanales de datos", "6": "Soporte prioritario", "7": "<PERSON><PERSON> a<PERSON>"}, "upgrade": "Actualizar a Premium", "benefits": {"0": "Más consultas DR manuales", "1": "Rastrea hasta 1000 proyectos", "2": "Alertas avanzadas de dominio", "3": "Soporte al cliente prioritario"}, "limitations": {"0": "Aún actualizaciones semanales (no en tiempo real)", "1": "Sin acceso API", "2": "Sin exportación de datos", "3": "Sin seguimiento de enlaces muertos"}}}, "limitations": {"title": "Limitaciones de la Plataforma", "description": "Entendiendo lo que MyBackLinks hace y no soporta", "noDeadLinkTracking": {"title": "Sin Seguimiento de Enlaces Muertos", "description": "Detección automática y monitoreo de enlaces muertos no está disponible"}, "noApiAccess": {"title": "Sin Acceso API", "description": "Endpoints API para integraciones externas no están proporcionados"}, "noDataExport": {"title": "Sin Exportación de Datos", "description": "Funcionalidad de exportación masiva de datos no está disponible"}, "weeklyUpdates": {"title": "Actualizaciones Semanales de Datos", "description": "Los datos se actualizan aproximadamente una vez por semana, no en tiempo real"}, "manualQueries": {"title": "Consultas Manuales Limitadas", "description": "Las consultas manuales de DR y backlinks están limitadas según tu plan"}}, "features": {"title": "Características Principales", "description": "Lo que MyBackLinks te ayuda a lograr", "personalLibrary": {"title": "Biblioteca Personal de Recursos de Backlinks", "description": "Mantén y organiza tu colección de plataformas de envío de backlinks"}, "projectTracking": {"title": "Seguimiento de Proyectos Paralelos", "description": "Rastrea valores DR y backlinks para tus proyectos paralelos vía importación SEMrush o consultas manuales"}, "resourceMaintenance": {"title": "Mantenimiento de Biblioteca de Recursos", "description": "Actualiza tu biblioteca de recursos basándote en resultados y rendimiento del proyecto"}, "domainManagement": {"title": "Gestión de Expiración de Dominios", "description": "Monitorea fechas de expiración de dominios y evita renovaciones perdidas"}, "analyticsIntegration": {"title": "Integración de Analíticas", "description": "Conéctate con Plausible, Google Analytics y Google Search Console para perspectivas de tráfico"}}, "subscription": {"title": "Gestión de Suscripción", "description": "Gestiona tu suscripción de MyBackLinks y ve estadísticas de uso", "current_plan": "Plan Actual", "usage_summary": "Resumen de Uso", "upgrade_plan": "Actualizar Plan", "manage_billing": "Gestionar Facturación", "features": "Características", "limits": "Límites", "projects": "Proyectos", "domains": "<PERSON><PERSON><PERSON>", "link_resources": "Recursos de Enlaces", "dr_queries": "Consultas DR", "traffic_updates": "Actualizaciones de Tráfico", "free_tier": "<PERSON><PERSON>", "professional_tier": "Nivel Profesional", "unlimited": "<PERSON><PERSON><PERSON><PERSON>", "per_month": "por mes", "upgrade_now": "<PERSON><PERSON><PERSON><PERSON>", "contact_support": "<PERSON><PERSON>"}, "blog": {"title": "Blog", "description": "Últimas perspectivas y actualizaciones de nuestro equipo", "read_more_text": "<PERSON><PERSON>"}, "blocks": {"hero": {"title": "Construye tu Biblioteca Definitiva de Recursos de Enlaces Externos", "description": "Rastrea el tráfico del sitio web, monitorea las calificaciones de dominio y gestiona tu cartera de enlaces externos para mejor promoción y crecimiento de proyectos.", "announcement": {"title": "Nuevo Panel de Análisis Disponible", "url": "/dashboard"}, "buttons": {"primary": "Comenzar a Construir tu Biblioteca", "secondary": "Ver Demo en Vivo"}, "tip": "El plan gratuito incluye hasta 5 proyectos e integración básica de análisis"}, "feature1": {"title": "Análisis de Tráfico y DR en Tiempo Real", "description": "Monitorea el rendimiento de tus proyectos paralelos con integración completa de análisis y seguimiento de calificación de dominio para optimizar tu estrategia de crecimiento.", "features": {"traffic_monitoring": {"title": "Integración de Análisis de Tráfico", "description": "Conecta con Google Analytics, Plausible y Google Search Console para obtener información unificada de tráfico en todos tus proyectos."}, "dr_tracking": {"title": "Monitoreo de Calificación de Dominio", "description": "Rastrea cambios de DR a lo largo del tiempo mediante integración con SEMrush o consultas manuales para medir efectivamente tu progreso SEO."}, "backlink_analysis": {"title": "Gestión de Cartera de Backlinks", "description": "Monitorea backlinks nuevos y perdidos, analiza dominios de referencia y rastrea la calidad de tus esfuerzos de construcción de enlaces."}, "performance_insights": {"title": "Insights de Rendimiento de Crecimiento", "description": "Obtén insights accionables sobre tendencias de tráfico, rankings de palabras clave y oportunidades de backlinks para acelerar el crecimiento del proyecto."}}}, "feature2": {"title": "Gestión Inteligente de Recursos de Enlaces Externos", "description": "Construye y mantén tu base de datos personal de oportunidades de backlinks de alta calidad con seguimiento de rendimiento y monitoreo de tasas de éxito.", "features": {"resource_library": {"title": "Base de Datos de Recursos Curados", "description": "Organiza oportunidades de enlaces externos por categoría, rastrea tasas de éxito y construye tu flujo de trabajo de envío personalizado."}, "performance_tracking": {"title": "Análisis de Tasa de Éxito", "description": "Monitorea tiempos de respuesta, tasas de aprobación y ROI para cada recurso para optimizar tu estrategia de divulgación a lo largo del tiempo."}, "quality_management": {"title": "Sistema de Aseguramiento de Calidad", "description": "Califica y revisa plataformas basándote en tu experiencia, manteniendo una biblioteca de recursos de alta calidad para futuras campañas."}}}, "feature": {"title": "Gestión Completa de Cartera de Dominios", "description": "Nunca pierdas una renovación de dominio nuevamente con monitoreo automatizado de expiración y herramientas completas de gestión de dominios.", "features": {"expiration_monitoring": {"title": "Alertas de Expiración", "description": "Recibe notificaciones oportunas antes de la expiración del dominio para prevenir interrupciones del servicio y proteger tu marca."}, "whois_tracking": {"title": "Gestión de Datos WHOIS", "description": "Rastrea automáticamente información del registrador, configuraciones DNS y costos de renovación para todos tus dominios en un solo lugar."}, "project_association": {"title": "Integración de Proyectos", "description": "Vincula dominios a proyectos específicos para un seguimiento holístico de tu cartera de activos digitales y métricas de rendimiento."}}}, "pricing": {"title": "Elige tu Plan de Crecimiento", "description": "Comienza gratis y escala con tus proyectos. Sin tarifas ocultas, cancela en cualquier momento.", "plans": {"free": {"name": "Inicial", "price": "<PERSON><PERSON><PERSON>", "description": "Perfecto para pruebas y proyectos pequeños", "features": ["Hasta 5 proyectos de seguimiento", "Biblioteca de recursos de enlaces externos", "Seguimiento básico de DR (10 consultas/mes)", "Monitoreo de expiración de dominios", "Integración de análisis", "Actualizaciones semanales de datos"], "button": "<PERSON><PERSON><PERSON>", "tip": "No se requiere tarjeta de crédito"}, "premium": {"name": "Profesional", "price": "$29", "unit": "/mes", "description": "Ideal para constructores serios de proyectos paralelos", "features": ["Hasta 1000 proyectos de seguimiento", "Gestión avanzada de recursos", "Seguimiento mejorado de DR (100 consultas/mes)", "Monitoreo a<PERSON>o de dominios", "Integración prioritaria de análisis", "Actualizaciones semanales de datos", "Soporte prioritario", "<PERSON><PERSON> a<PERSON>"], "button": "Actualizar a Pro", "popular": true}}}, "testimonials": {"title": "Amado por Constructores de Proyectos Paralelos", "description": "Ve cómo los creadores están haciendo crecer sus proyectos con nuestra plataforma"}, "faq": {"title": "Preguntas Frecuentes", "description": "Todo lo que necesitas saber sobre la construcción de tu biblioteca de recursos de enlaces externos", "questions": {"what_is_platform": {"question": "¿Qué es MyBackLinks y cómo ayuda a mis proyectos paralelos?", "answer": "MyBackLinks te ayuda a construir y mantener una biblioteca personal de oportunidades de enlaces externos mientras rastreas las métricas de rendimiento de tus proyectos como tráfico, DR y backlinks."}, "how_track_dr": {"question": "¿Cómo rastrean la calificación de dominio y los backlinks?", "answer": "Nos integramos con SEMrush para importación de datos y proporcionamos opciones de consulta manual. Puedes subir reportes de SEMrush o usar nuestras consultas manuales limitadas basadas en tu plan."}, "analytics_integration": {"question": "¿Qué plataformas de análisis so<PERSON>?", "answer": "Nos integramos con Google Analytics, Google Search Console y Plausible Analytics para proporcionar insights de tráfico unificados para tus proyectos."}, "resource_library": {"question": "¿Cómo funciona la biblioteca de recursos de enlaces externos?", "answer": "Puedes organizar y rastrear oportunidades de enlaces externos por categoría, monitorear tasas de éxito y construir tu flujo de trabajo de divulgación personalizado para mejores resultados."}, "data_updates": {"question": "¿Con qué frecuencia se actualizan los datos?", "answer": "Los datos de análisis y DR se actualizan aproximadamente una vez por semana. No proporcionamos seguimiento en tiempo real para mantener los costos manejables y enfocarnos en tendencias."}, "plan_limits": {"question": "¿<PERSON><PERSON><PERSON>les son las diferencias entre los planes gratuito y premium?", "answer": "Los usuarios gratuitos pueden rastrear hasta 5 proyectos con 10 consultas DR manuales por mes. Los usuarios premium obtienen hasta 1000 proyectos con 100 consultas manuales y soporte prioritario."}}}, "cta": {"title": "¿Listo para Hacer <PERSON> tu Imperio de Proyectos Paralelos?", "description": "Únete a miles de creadores construyendo bibliotecas sostenibles de enlaces externos y rastreando el éxito de sus proyectos.", "button": {"primary": "Comenzar a Construir Gratis", "secondary": "Ver Demo en Vivo"}}}, "faq": {"helpful_information": "Información útil", "still_have_questions": "¿Aún tienes preguntas?", "contact_support_description": "Estamos aquí para ayudarte a construir tu imperio de enlaces externos", "contact_support": "<PERSON><PERSON>"}}