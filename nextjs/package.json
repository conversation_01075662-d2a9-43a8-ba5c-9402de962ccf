{"name": "MyBacklinks", "version": "1.0.0", "private": true, "author": "<PERSON><PERSON><PERSON>", "homepage": "https://mybacklinks.app", "scripts": {"dev": "NODE_NO_WARNINGS=1 next dev", "build": "next build", "start": "NODE_NO_WARNINGS=1 next start", "lint": "next lint", "analyze": "ANALYZE=true pnpm build", "cf:build": "npx @opennextjs/cloudflare build", "cf:preview": "pnpm cf:build && npx wrangler dev", "cf:deploy": "pnpm cf:build && npx @opennextjs/cloudflare deploy", "cf:env": "node ../tools/scripts/generate-wrangler-config.js nextjs"}, "dependencies": {"@ai-sdk/openai": "^1.0.18", "@ai-sdk/openai-compatible": "^0.0.17", "@ai-sdk/provider": "^1.0.4", "@ai-sdk/provider-utils": "^2.0.7", "@ai-sdk/replicate": "^0.0.2", "@aws-sdk/client-s3": "^3.740.0", "@aws-sdk/lib-storage": "^3.740.0", "@devnomic/marquee": "^1.0.2", "@google-analytics/data": "^5.1.0", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@modelcontextprotocol/sdk": "^1.11.0", "@next/mdx": "^15.1.3", "@next/third-parties": "^15.1.2", "@opennextjs/cloudflare": "^1.4.0", "@openpanel/nextjs": "^1.0.7", "@openrouter/ai-sdk-provider": "^0.0.6", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "^5.4.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.47.10", "@types/canvas-confetti": "^1.9.0", "@types/mdx": "^2.0.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.7", "add": "^2.0.6", "ai": "^4.0.33", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-fade": "^8.5.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.15.0", "google-auth-library": "^9.15.1", "google-one-tap": "^1.0.6", "googleapis": "^140.0.1", "highlight.js": "^11.11.0", "jsdom": "^26.1.0", "lucide-react": "^0.439.0", "markdown-it": "^14.1.0", "moment": "^2.30.1", "next": "14.2.15", "next-auth": "5.0.0-beta.29", "next-axiom": "^1.9.1", "next-intl": "^3.26.3", "next-themes": "^0.4.4", "openai": "^4.78.1", "proxy-agent": "^6.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icon-cloud": "^4.1.4", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-tweet": "^3.2.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "resend": "^4.2.0", "simple-flakeid": "^0.0.5", "sonner": "^1.7.1", "stripe": "^17.5.0", "swr": "^2.3.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "undici": "^7.12.0", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.1.3", "@types/jsdom": "^21.1.7", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5", "vercel": "39.1.1"}}