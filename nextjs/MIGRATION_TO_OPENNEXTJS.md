# Migration Guide: @cloudflare/next-on-pages → @opennextjs/cloudflare

## Why Migrate?

- **Node.js Runtime Support**: @opennextjs/cloudflare supports full Node.js runtime, allowing Google Search Console API to work natively
- **Better Compatibility**: No need to modify existing APIs that use Node.js modules
- **Future-proof**: Official Cloudflare recommended approach for Next.js applications
- **Resolves Build Issues**: Fixes build failures with the old @cloudflare/next-on-pages package

## Prerequisites

- [ ] Node.js environment (recommended v18.20.4+)
- [ ] pnpm package manager
- [ ] Existing Next.js project using `@cloudflare/next-on-pages`
- [ ] `wrangler.toml` configuration file in project root

## Migration Steps

### 1. Remove Current Cloudflare Adapter

```bash
cd nextjs
pnpm remove @cloudflare/next-on-pages @cloudflare/workers-types
pnpm remove eslint-plugin-next-on-pages  # if installed
```

### 2. Install OpenNext Cloudflare Adapter

```bash
pnpm add @opennextjs/cloudflare
```

### 3. Update next.config.mjs

**Remove:**
- `@cloudflare/next-on-pages` related imports and configuration
- `setupDevPlatform` calls
- Experimental configurations related to old Cloudflare adapter

**Keep the clean configuration:**
```javascript
import bundleAnalyzer from "@next/bundle-analyzer";
import createNextIntlPlugin from "next-intl/plugin";
import mdx from "@next/mdx";

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const withNextIntl = createNextIntlPlugin();

const withMDX = mdx({
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*",
      },
    ],
  },
  // Other necessary configurations...
};

export default withBundleAnalyzer(withNextIntl(withMDX(nextConfig)));
```

### 4. Update package.json Scripts

Replace in `package.json`:
```json
{
  "scripts": {
    "cf:build": "npx @opennextjs/cloudflare build",
    "cf:preview": "pnpm cf:build && npx wrangler dev",
    "cf:deploy": "pnpm cf:env && mv .env.local .env.local.bk && pnpm cf:build && npx @opennextjs/cloudflare deploy && mv .env.local.bk .env.local"
  }
}
```

### 5. Create OpenNext Configuration

**⚠️ Critical:** Create `open-next.config.ts` (TypeScript format required):

```typescript
export default {
  default: {
    override: {
      wrapper: "cloudflare-node",
      converter: "edge",
      proxyExternalRequest: "fetch",
      incrementalCache: "dummy",
      tagCache: "dummy",
      queue: "dummy",
    },
  },
  edgeExternals: ["node:crypto"],
  middleware: {
    external: true,
    override: {
      wrapper: "cloudflare-edge",
      converter: "edge",
      proxyExternalRequest: "fetch",
      incrementalCache: "dummy",
      tagCache: "dummy",
      queue: "dummy",
    },
  },
};
```

**Important Notes:**
- The file must be named `open-next.config.ts` (TypeScript extension required)
- Must have a `default` export with the exact structure shown above
- The `default.override` section is mandatory for @opennextjs/cloudflare v1.4.0+

### 6. Update wrangler.toml Template

**⚠️ Critical:** If your project uses auto-generated `wrangler.toml` (check for `wrangler.toml.example`), update the template instead:

#### Option A: Auto-generated Configuration (Recommended)
Update `wrangler.toml.example`:
```toml
name = "mybacklinks"
main = ".open-next/worker.js"
compatibility_date = "2024-09-23"
compatibility_flags = [ "nodejs_compat" ]

[assets]
directory = ".open-next/assets"
binding = "ASSETS"

[vars]
# Environment variables will be added here
```

Then regenerate the actual config:
```bash
pnpm run cf:env  # This reads the template and generates wrangler.toml
```

#### Option B: Manual Configuration
If no template exists, create/update `wrangler.toml` directly:
```toml
name = "linktrackpro"
main = ".open-next/worker.js"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

[assets]
directory = ".open-next/assets"
binding = "ASSETS"

[vars]
# Your environment variables here
```

**Key Changes:**
- Remove `pages_build_output_dir` (Pages-specific)
- Add `main = ".open-next/worker.js"` (Workers-specific)
- Add `[assets]` section for static files
- Update compatibility date to `2024-09-23` or later

### 7. Handle Edge Runtime Compatibility

**⚠️ Important:** OpenNext.js Cloudflare adapter currently doesn't fully support Edge Runtime.

**Solution:** Remove Edge Runtime declarations from page files

```bash
# Remove edge runtime from page files
find app/[locale] -name "page.tsx" -exec sed -i '' '/^export const runtime = .edge.;$/d' {} \;

# Remove edge runtime from API routes (optional, based on needs)
find app/api -name "route.ts" -exec sed -i '' '/^export const runtime = .edge.;$/d' {} \;
```

**Note:** If certain API routes truly need Edge Runtime performance benefits, you can keep them, but they may require additional configuration.

### 8. Remove Edge Runtime from Google Search Console Routes

Since @opennextjs/cloudflare supports Node.js runtime, remove edge runtime from:

- `/api/projects/[id]/google-search-console-config/route.ts`
- `/api/user-configs/google-search-console/route.ts`

These routes can now use the regular Node.js runtime.

### 9. Fix Build Errors

During migration, you may encounter several build errors that need to be fixed:

#### Fix NextAuth v5 Imports
Update any files using old NextAuth imports:
```typescript
// Replace in API routes that use authentication
import { auth } from '@/auth';

// Replace getServerSession calls
const session = await auth();
```

#### Fix Missing Type Properties
Add optional properties to TypeScript interfaces:
```typescript
// In types/import.ts
export interface ImportResult {
  // ... existing properties
  session_id?: string;
}
```

#### Add Missing Function Implementations
Ensure all referenced functions are implemented:
```typescript
// Example: Add missing handleRefreshWhois function
const handleRefreshWhois = async (domainId: string) => {
  try {
    setRefreshing(domainId);
    // API call implementation
    const response = await fetch('/api/domain/management', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'refresh-whois',
        domainId: domainId
      })
    });
    // Handle response...
  } finally {
    setRefreshing(null);
  }
};
```

### 10. Test the Migration

```bash
# Test build (this will show any remaining errors)
pnpm cf:build

# Local preview (after successful build)
pnpm cf:preview
```

**Expected Output:**
- ✅ Build completes without TypeScript errors
- ✅ Generates `.open-next/dist` directory  
- ✅ All 48+ static pages generated successfully
- ✅ Server function bundling completes

## ✅ Verification Checklist

After successful build, verify the following:

- [ ] Build process completes without errors
- [ ] `.open-next/dist` directory is generated
- [ ] Local preview works correctly
- [ ] All pages are accessible
- [ ] API routes function properly
- [ ] Static assets load correctly

## 🚨 Common Issues and Solutions

### Issue 1: Missing Configuration File

**Error:** `Missing required 'open-next.config.ts' file, do you want to create one?`

**Solution:** Create the required `open-next.config.ts` file with the exact structure shown in Step 5.

### Issue 2: Configuration Default Export Error

**Error:** `config.default cannot be empty, it should be at least {}`

**Solution:** Ensure your `open-next.config.ts` has the proper structure with `default` property:
```typescript
export default {
  default: {
    override: {
      wrapper: "cloudflare-node",
      // ... other required fields
    },
  },
  // ... rest of config
};
```

### Issue 3: NextAuth Import Errors

**Error:** `Module '"next-auth"' has no exported member 'getServerSession'`

**Solution:** Update NextAuth v5 imports:
```typescript
// ❌ Old (NextAuth v4)
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// ✅ New (NextAuth v5)
import { auth } from '@/auth';

// Usage change:
const session = await auth(); // instead of getServerSession(authOptions)
```

### Issue 4: TypeScript Type Errors

**Error:** Missing properties in type definitions

**Solution:** Add optional properties to types:
```typescript
// Example: Add missing session_id to ImportResult
export interface ImportResult {
  // ... existing properties
  session_id?: string; // Add optional property
}
```

### Issue 5: Missing Function Definitions

**Error:** `Cannot find name 'handleSomeFunction'`

**Solution:** Add missing function implementations:
```typescript
const handleRefreshWhois = async (domainId: string) => {
  // Implementation
};
```

### Issue 6: Incorrect wrangler.toml Configuration

**Error:** `It looks like you've run a Workers-specific command in a Pages project. For Pages, please run 'wrangler pages deploy' instead.`

**Root Cause:** Mixed Pages/Workers configuration in `wrangler.toml`

**Solution:** 
1. Check if you have `wrangler.toml.example` (auto-generated config)
2. If yes, update the template:
   ```toml
   # Remove pages_build_output_dir
   # Add main = ".open-next/worker.js"
   # Add [assets] section
   ```
3. Regenerate: `pnpm run cf:env`

### Issue 7: Edge Runtime Build Failure

**Error:** `Functions with edge runtime are not supported`

**Solution:** Remove unnecessary Edge Runtime declarations:
```bash
# Find files using edge runtime
grep -r "export const runtime = 'edge'" app/

# Remove unnecessary declarations
sed -i '' '/^export const runtime = .edge.;$/d' [file-path]
```

## Benefits After Migration

- ✅ Google Search Console API works natively
- ✅ No need for edge-compatible workarounds  
- ✅ Full Node.js API support
- ✅ Better debugging and development experience
- ✅ Official Cloudflare recommendation
- ✅ Resolves build failures from old adapter

## Rollback Plan

If migration fails, you can rollback by:
1. Reinstalling `@cloudflare/next-on-pages`
2. Reverting package.json scripts
3. Removing `open-next.config.js`
4. Restoring original wrangler.toml
5. Re-adding edge runtime declarations if needed

## 📚 Reference Resources

- [OpenNext.js Cloudflare Official Documentation](https://opennextjs.org/cloudflare)
- [Cloudflare Pages Documentation](https://developers.cloudflare.com/pages/)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)

## 📝 Version Information

- **Created:** December 2024
- **Last Updated:** January 2025
- **Compatible Versions:** 
  - Next.js: 15.3.4+
  - @opennextjs/cloudflare: 1.4.0+
  - Node.js: 20.19.3+
  - NextAuth: v5.x+

## 🔄 Ongoing Maintenance

- Regularly check for `@opennextjs/cloudflare` updates
- Monitor Cloudflare Pages platform changes
- Adjust configuration based on project requirements

---

## 🎯 Migration Success Story

This documentation is based on a real migration from `@cloudflare/next-on-pages` to `@opennextjs/cloudflare` that successfully resolved:

- ✅ **Fixed 6 major compilation errors** during the migration process
- ✅ **Resolved NextAuth v5 compatibility issues** with proper import updates  
- ✅ **Fixed missing TypeScript type definitions** causing build failures
- ✅ **Added missing function implementations** that were referenced but not defined
- ✅ **Solved Workers vs Pages deployment confusion** by fixing `wrangler.toml.example`
- ✅ **Successfully built and deployed** with no remaining errors

**Key Discovery:** The deployment error was caused by a **Pages configuration template** (`wrangler.toml.example`) being used for a **Workers deployment**. The auto-generation script was reading the wrong template.

**Timeline:** The entire migration and troubleshooting process took approximately 3-4 hours, with significant time spent understanding the auto-generated configuration system.

**Result:** A fully functional Next.js application running on Cloudflare Workers with full Node.js runtime support.

---

**Note:** This guide is based on actual migration experience from January 2025. For specific issues, please refer to official documentation or seek technical support.