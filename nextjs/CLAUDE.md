# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LinkTrackPro is a Next.js 14 SaaS application for link management and analytics tracking. It's a multi-tenant platform that allows users to organize links into projects, track analytics from multiple providers, and manage link discovery.

## Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **UI**: React 18, Tailwind CSS, shadcn/ui components
- **Database**: PostgreSQL with Supabase backend
- **Authentication**: NextAuth v5 (Google, GitHub OAuth)
- **Internationalization**: next-intl (8 languages supported)
- **Analytics**: Google Analytics, Plausible, Umami integration
- **AI**: Multiple providers (OpenAI, Anthropic, Google, etc.)

## Development Commands

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Type checking
npm run type-check

# Lint code
npm run lint
```

## Database Setup

The application uses PostgreSQL with a comprehensive schema in the `link_track` namespace:

- **Installation**: Run `data/install.sql` to create the complete schema
- **Key tables**: `projects`, `links`, `analytics_data`, `users`, `discovered_links`
- **Features**: UUID primary keys, vector search with pgvector, credit system
- **Environment**: Configure database connection via Supabase environment variables

## Project Structure

```
app/                    # Next.js App Router pages and API routes
├── api/               # API endpoints for all features
├── [locale]/          # Internationalized pages
components/            # React components
├── blocks/           # Feature-specific component groups
├── ui/              # shadcn/ui base components
data/                 # Database schemas and migrations
models/              # TypeScript data models and utilities
services/            # External service integrations
types/               # TypeScript type definitions
messages/            # Internationalization files
```

## Architecture Patterns

### Multi-tenant Design
- All data is scoped by `user_id` and `project_id`
- Projects contain links and analytics configurations
- Credit-based usage tracking system

### Analytics Integration
- Unified data format across providers (Google Analytics, Plausible, Umami)
- Service-based architecture in `services/` directory
- User configurations stored per project for each analytics provider

### Component Architecture
- Feature-based organization in `components/blocks/`
- Reusable UI components from shadcn/ui
- Type-safe props with comprehensive TypeScript definitions

### API Design
- RESTful endpoints following `/api/projects/[id]/resource` pattern
- Consistent error handling and response formats
- Authentication middleware integration

## Key Features

1. **Link Management**: Batch import, discovery tools, categorization
2. **Analytics Tracking**: Multi-provider support with unified dashboards
3. **Project Organization**: Hierarchical structure for managing multiple link collections
4. **Internationalization**: Full i18n support with 8 languages
5. **AI Integration**: Content analysis and link processing capabilities

## Development Notes

- **No formal testing framework** is currently set up
- **Database migrations** should be added to `data/install.sql`
- **Environment variables** follow Supabase and NextAuth patterns
- **Deployment**: Configured for Vercel, Cloudflare Pages, and self-hosting
- **Type safety**: Comprehensive TypeScript coverage across all modules

## Important File Locations

- **Database schema**: `data/install.sql`
- **API routes**: `app/api/` directory
- **Main dashboard**: `components/blocks/link-dashboard/`
- **Type definitions**: `types/` directory (especially `types/links.ts`)
- **Service integrations**: `services/` directory
- **Models and utilities**: `models/` directory