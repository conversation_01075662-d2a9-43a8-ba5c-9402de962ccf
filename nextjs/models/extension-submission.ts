import { getSupabaseClient } from "./db";
import { PostgrestError } from "@supabase/supabase-js";
import { getUuid } from "@/lib/hash";

// TypeScript interfaces for extension submissions
export interface ExtensionSubmissionData {
  id?: string;
  user_id: string;
  project_id: string;
  link_id: string;
  target_url: string;
  submission_data: Record<string, any>;
  status: 'pending' | 'submitted' | 'failed' | 'completed';
  created_at?: string;
  updated_at?: string;
}

export interface ExtensionSubmissionWithDetails extends ExtensionSubmissionData {
  projects?: {
    name: string;
    domain: string;
  }[];
  link_resources?: {
    url: string;
    title: string;
  }[];
}

export interface CreateExtensionSubmissionData {
  project_id: string;
  link_id: string;
  target_url: string;
  form_data?: Record<string, any>;
}

// ========================================
// EXTENSION SUBMISSION CRUD OPERATIONS
// ========================================

/**
 * Create a new extension submission record
 */
export async function createExtensionSubmission(
  data: CreateExtensionSubmissionData,
  userId: string
): Promise<{ data: ExtensionSubmissionData | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const submissionId = getUuid();
  const now = new Date().toISOString();
  
  const { data: submission, error } = await client
    .from('extension_submissions')
    .insert({
      id: submissionId,
      user_id: userId,
      project_id: data.project_id,
      link_id: data.link_id,
      target_url: data.target_url,
      submission_data: data.form_data || {},
      status: 'submitted',
      created_at: now,
      updated_at: now
    })
    .select()
    .single();
  
  return { data: submission, error };
}

/**
 * Get extension submission by ID with user ownership validation
 */
export async function getExtensionSubmissionById(
  id: string,
  userId: string
): Promise<{ data: ExtensionSubmissionWithDetails | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const { data: submission, error } = await client
    .from('extension_submissions')
    .select(`
      id,
      user_id,
      project_id,
      link_id,
      target_url,
      submission_data,
      status,
      created_at,
      updated_at,
      projects!inner(name, domain),
      link_resources!inner(url, title)
    `)
    .eq('id', id)
    .eq('user_id', userId)
    .single();
  
  return { data: submission, error };
}

/**
 * Update extension submission status
 */
export async function updateExtensionSubmissionStatus(
  id: string,
  status: 'pending' | 'submitted' | 'failed' | 'completed',
  userId: string
): Promise<{ data: ExtensionSubmissionData | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const { data: updatedSubmission, error } = await client
    .from('extension_submissions')
    .update({
      status,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .eq('user_id', userId)
    .select()
    .single();
  
  return { data: updatedSubmission, error };
}

/**
 * Validate that a user owns the specified project
 */
export async function validateProjectOwnership(
  projectId: string,
  userId: string
): Promise<{ isValid: boolean, error?: PostgrestError }> {
  const client = getSupabaseClient();
  
  const { data: project, error } = await client
    .from('projects')
    .select('id')
    .eq('id', projectId)
    .eq('user_id', userId)
    .single();
  
  if (error) {
    return { isValid: false, error };
  }
  
  return { isValid: !!project };
}

/**
 * Validate that a user owns the specified link resource
 */
export async function validateLinkResourceOwnership(
  linkId: string,
  userId: string
): Promise<{ isValid: boolean, error?: PostgrestError }> {
  const client = getSupabaseClient();
  
  const { data: link, error } = await client
    .from('link_resources')
    .select('id')
    .eq('id', linkId)
    .eq('user_id', userId)
    .single();
  
  if (error) {
    return { isValid: false, error };
  }
  
  return { isValid: !!link };
}

/**
 * Validate that a user owns the specified submission
 */
export async function validateSubmissionOwnership(
  submissionId: string,
  userId: string
): Promise<{ isValid: boolean, error?: PostgrestError }> {
  const client = getSupabaseClient();
  
  const { data: submission, error } = await client
    .from('extension_submissions')
    .select('id, user_id')
    .eq('id', submissionId)
    .eq('user_id', userId)
    .single();
  
  if (error) {
    return { isValid: false, error };
  }
  
  return { isValid: !!submission };
}

/**
 * Get extension submissions by user (with pagination)
 */
export async function getExtensionSubmissionsByUser(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<{ data: ExtensionSubmissionWithDetails[] | null, error: PostgrestError | null, count: number | null }> {
  const client = getSupabaseClient();
  
  const { data, error, count } = await client
    .from('extension_submissions')
    .select(`
      id,
      user_id,
      project_id,
      link_id,
      target_url,
      submission_data,
      status,
      created_at,
      updated_at,
      projects!inner(name, domain),
      link_resources!inner(url, title)
    `, { count: 'exact' })
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
  
  return { data, error, count };
}

/**
 * Delete extension submission by ID with user ownership validation
 */
export async function deleteExtensionSubmission(
  id: string,
  userId: string
): Promise<{ success: boolean, error?: PostgrestError }> {
  const client = getSupabaseClient();
  
  const { error } = await client
    .from('extension_submissions')
    .delete()
    .eq('id', id)
    .eq('user_id', userId);
  
  return { success: !error, error };
}