import { getSupabaseClient } from "./db";
import { PostgrestError } from "@supabase/supabase-js";

// OAuth token data interface
export interface GoogleTokenData {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  scope: string;
  tokenType: string;
}

// Google Analytics property interface
export interface GoogleAnalyticsProperty {
  propertyId: string;
  displayName: string;
  websiteUrl: string;
  timeZone: string;
  currencyCode: string;
  accountId: string;
  accountDisplayName: string;
}

export interface UserConfig {
  id: number;
  userId: string;
  projectId: string;
  configType: string;
  configName: string;
  configData: any;
  isActive: boolean;
  created_at: string;
  updated_at: string;
  // OAuth enhancement fields
  oauthTokens?: GoogleTokenData;
  properties?: GoogleAnalyticsProperty[];
  permissions?: string[];
  lastSyncAt?: string;
}

export interface GoogleSearchConsoleConfig {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

// Create or update user configuration
export async function saveUserConfig(
  userId: string,
  projectId: string,
  configType: string,
  configName: string,
  configData: any
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // First check if config already exists
  const { data: existingConfig } = await client
    .from("user_configs")
    .select("*")
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('config_type', configType)
    .single();

  if (existingConfig) {
    // Update existing config
    const { data, error } = await client
      .from("user_configs")
      .update({
        config_name: configName,
        config_data: configData,
        is_active: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', existingConfig.id)
      .select()
      .single();

    if (data) {
      return {
        data: {
          id: data.id,
          userId: data.user_id,
          projectId: data.project_id,
          configType: data.config_type,
          configName: data.config_name,
          configData: data.config_data,
          isActive: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        },
        error
      };
    }
    return { data: null, error };
  } else {
    // Create new config
    const { data, error } = await client
      .from("user_configs")
      .insert({
        user_id: userId,
        project_id: projectId,
        config_type: configType,
        config_name: configName,
        config_data: configData,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (data) {
      return {
        data: {
          id: data.id,
          userId: data.user_id,
          projectId: data.project_id,
          configType: data.config_type,
          configName: data.config_name,
          configData: data.config_data,
          isActive: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        },
        error
      };
    }
    return { data: null, error };
  }
}

// Get user configuration by type
export async function getUserConfig(
  userId: string,
  projectId: string,
  configType: string
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("user_configs")
    .select("*")
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('config_type', configType)
    .eq('is_active', true)
    .single();

  if (data) {
    return {
      data: {
        id: data.id,
        userId: data.user_id,
        projectId: data.project_id,
        configType: data.config_type,
        configName: data.config_name,
        configData: data.config_data,
        isActive: data.is_active,
        created_at: data.created_at,
        updated_at: data.updated_at
      },
      error
    };
  }
  return { data: null, error };
}

// Get all user configurations for a project
export async function getUserConfigs(
  userId: string,
  projectId: string
): Promise<{ data: UserConfig[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("user_configs")
    .select("*")
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  if (data) {
    const configs = data.map(item => ({
      id: item.id,
      userId: item.user_id,
      projectId: item.project_id,
      configType: item.config_type,
      configName: item.config_name,
      configData: item.config_data,
      isActive: item.is_active,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));
    return { data: configs, error };
  }
  return { data: null, error };
}

// Delete user configuration
export async function deleteUserConfig(
  userId: string,
  projectId: string,
  configType: string
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from("user_configs")
    .update({ is_active: false })
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('config_type', configType);

  return { error };
}

// Specific helper for Google Search Console
export async function getUserGoogleSearchConsoleConfig(
  userId: string,
  projectId: string
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  return getUserConfig(userId, projectId, 'google_search_console');
}

export async function saveGoogleSearchConsoleConfig(
  userId: string,
  projectId: string,
  configName: string,
  configData: GoogleSearchConsoleConfig
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  return saveUserConfig(userId, projectId, 'google_search_console', configName, configData);
}

// Enhanced functions for Google Analytics OAuth integration

// Get Google Analytics OAuth configuration
export async function getUserGoogleAnalyticsConfig(
  userId: string,
  projectId: string
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  return getUserConfig(userId, projectId, 'google_analytics_oauth');
}

// Save Google Analytics OAuth configuration with enhanced fields
export async function saveGoogleAnalyticsConfig(
  userId: string,
  projectId: string,
  configName: string,
  configData: any,
  oauthTokens?: GoogleTokenData,
  properties?: GoogleAnalyticsProperty[],
  permissions?: string[]
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // First check if config already exists
  const { data: existingConfig } = await client
    .from("user_configs")
    .select("*")
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('config_type', 'google_analytics_oauth')
    .single();

  const updateData = {
    config_name: configName,
    config_data: configData,
    is_active: true,
    updated_at: new Date().toISOString(),
    last_sync_at: new Date().toISOString()
  };

  // Add OAuth fields if provided
  if (oauthTokens) {
    (updateData as any).oauth_tokens = oauthTokens;
  }
  if (properties) {
    (updateData as any).properties = properties;
  }
  if (permissions) {
    (updateData as any).permissions = permissions;
  }

  if (existingConfig) {
    // Update existing config
    const { data, error } = await client
      .from("user_configs")
      .update(updateData)
      .eq('id', existingConfig.id)
      .select()
      .single();

    if (data) {
      return {
        data: {
          id: data.id,
          userId: data.user_id,
          projectId: data.project_id,
          configType: data.config_type,
          configName: data.config_name,
          configData: data.config_data,
          isActive: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at,
          oauthTokens: data.oauth_tokens,
          properties: data.properties,
          permissions: data.permissions,
          lastSyncAt: data.last_sync_at
        },
        error
      };
    }
    return { data: null, error };
  } else {
    // Create new config
    const insertData = {
      user_id: userId,
      project_id: projectId,
      config_type: 'google_analytics_oauth',
      created_at: new Date().toISOString(),
      ...updateData
    };

    const { data, error } = await client
      .from("user_configs")
      .insert(insertData)
      .select()
      .single();

    if (data) {
      return {
        data: {
          id: data.id,
          userId: data.user_id,
          projectId: data.project_id,
          configType: data.config_type,
          configName: data.config_name,
          configData: data.config_data,
          isActive: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at,
          oauthTokens: data.oauth_tokens,
          properties: data.properties,
          permissions: data.permissions,
          lastSyncAt: data.last_sync_at
        },
        error
      };
    }
    return { data: null, error };
  }
}

// Update OAuth tokens specifically
export async function updateOAuthTokens(
  userId: string,
  projectId: string,
  oauthTokens: GoogleTokenData
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("user_configs")
    .update({
      oauth_tokens: oauthTokens,
      updated_at: new Date().toISOString(),
      last_sync_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('config_type', 'google_analytics_oauth')
    .eq('is_active', true)
    .select()
    .single();

  if (data) {
    return {
      data: {
        id: data.id,
        userId: data.user_id,
        projectId: data.project_id,
        configType: data.config_type,
        configName: data.config_name,
        configData: data.config_data,
        isActive: data.is_active,
        created_at: data.created_at,
        updated_at: data.updated_at,
        oauthTokens: data.oauth_tokens,
        properties: data.properties,
        permissions: data.permissions,
        lastSyncAt: data.last_sync_at
      },
      error
    };
  }
  return { data: null, error };
}

// Update Google Analytics properties
export async function updateGoogleAnalyticsProperties(
  userId: string,
  projectId: string,
  properties: GoogleAnalyticsProperty[]
): Promise<{ data: UserConfig | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("user_configs")
    .update({
      properties: properties,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .eq('project_id', projectId)
    .eq('config_type', 'google_analytics_oauth')
    .eq('is_active', true)
    .select()
    .single();

  if (data) {
    return {
      data: {
        id: data.id,
        userId: data.user_id,
        projectId: data.project_id,
        configType: data.config_type,
        configName: data.config_name,
        configData: data.config_data,
        isActive: data.is_active,
        created_at: data.created_at,
        updated_at: data.updated_at,
        oauthTokens: data.oauth_tokens,
        properties: data.properties,
        permissions: data.permissions,
        lastSyncAt: data.last_sync_at
      },
      error
    };
  }
  return { data: null, error };
} 