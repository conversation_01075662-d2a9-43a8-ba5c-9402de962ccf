import { User } from "@/types/user";
import { getSupabaseClient } from "./db";
import { v4 as uuidv4 } from 'uuid';

export async function insertUser(user: User) {
  // Generate a unique invite code for the user if not already set
  if (!user.invite_code) {
    user.invite_code = uuidv4().substring(0, 8);
  }
  
  const maxRetries = 3;
  let lastError: any = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const supabase = getSupabaseClient();
      console.log(`Attempting to insert user with schema (attempt ${attempt}):`, process.env.SUPABASE_SCHEMA);
      
      const { data, error } = await supabase.from("users").insert(user);

      if (error) {
        console.error(`Supabase insert error (attempt ${attempt}):`, error);
        lastError = error;
        
        // If this is a network error and we have retries left, continue to next attempt
        if (attempt < maxRetries && (error.message?.includes('fetch failed') || error.message?.includes('network'))) {
          console.log(`insertUser: Network error, retrying in ${attempt * 1000}ms...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }
        
        throw error;
      }

      console.log('User inserted successfully:', data);
      return data;
    } catch (err) {
      console.error(`Insert user failed with error (attempt ${attempt}):`, err);
      lastError = err;
      
      // If this is a network error and we have retries left, continue to next attempt
      if (attempt < maxRetries && (err instanceof Error && (err.message?.includes('fetch failed') || err.message?.includes('network')))) {
        console.log(`insertUser: Network exception, retrying in ${attempt * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }
      
      throw new Error(`Failed to insert user: ${err instanceof Error ? err.message : String(err)}`);
    }
  }
  
  throw new Error(`Failed to insert user after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : String(lastError)}`);
}

export async function findUserByEmail(
  email: string
): Promise<User | undefined> {
  console.log('findUserByEmail: Looking for user with email:', email);
  
  const maxRetries = 3;
  let lastError: any = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const supabase = getSupabaseClient();
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("email", email)
        .limit(1)
        .single();

      if (error) {
        console.log(`findUserByEmail: Supabase error (attempt ${attempt}):`, error);
        // Don't return undefined for "PGRST116" (no rows) error, this is expected for new users
        if (error.code === 'PGRST116') {
          console.log('findUserByEmail: No user found (new user)');
          return undefined;
        }
        
        lastError = error;
        // If this is a network error and we have retries left, continue to next attempt
        if (attempt < maxRetries && (error.message?.includes('fetch failed') || error.message?.includes('network'))) {
          console.log(`findUserByEmail: Network error, retrying in ${attempt * 1000}ms...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }
        
        console.error('findUserByEmail: Unexpected error:', error);
        return undefined;
      }

      console.log('findUserByEmail: Found user:', { uuid: data?.uuid, email: data?.email });
      return data;
    } catch (err) {
      console.error(`findUserByEmail: Exception (attempt ${attempt}):`, err);
      lastError = err;
      
      // If this is a network error and we have retries left, continue to next attempt
      if (attempt < maxRetries && (err instanceof Error && (err.message?.includes('fetch failed') || err.message?.includes('network')))) {
        console.log(`findUserByEmail: Network exception, retrying in ${attempt * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }
      
      return undefined;
    }
  }
  
  console.error('findUserByEmail: All retry attempts failed:', lastError);
  return undefined;
}

export async function findUserByUuid(uuid: string): Promise<User | undefined> {
  console.log('findUserByUuid: Looking for user with UUID:', uuid);
  
  const maxRetries = 3;
  let lastError: any = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const supabase = getSupabaseClient();
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("uuid", uuid)
        .single();

      if (error) {
        console.log(`findUserByUuid: Supabase error (attempt ${attempt}):`, error);
        // Don't return undefined for "PGRST116" (no rows) error, this is expected for non-existent users
        if (error.code === 'PGRST116') {
          console.log('findUserByUuid: No user found');
          return undefined;
        }
        
        lastError = error;
        // If this is a network error and we have retries left, continue to next attempt
        if (attempt < maxRetries && (error.message?.includes('fetch failed') || error.message?.includes('network'))) {
          console.log(`findUserByUuid: Network error, retrying in ${attempt * 1000}ms...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }
        
        console.error('findUserByUuid: Unexpected error:', error);
        return undefined;
      }

      console.log('findUserByUuid: Found user:', { uuid: data?.uuid, email: data?.email });
      return data;
    } catch (err) {
      console.error(`findUserByUuid: Exception (attempt ${attempt}):`, err);
      lastError = err;
      
      // If this is a network error and we have retries left, continue to next attempt
      if (attempt < maxRetries && (err instanceof Error && (err.message?.includes('fetch failed') || err.message?.includes('network')))) {
        console.log(`findUserByUuid: Network exception, retrying in ${attempt * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }
      
      return undefined;
    }
  }
  
  console.error('findUserByUuid: All retry attempts failed:', lastError);
  return undefined;
}

export async function getUsers(
  page: number = 1,
  limit: number = 50
): Promise<User[] | undefined> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 50;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("users")
    .select("*")
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    return undefined;
  }

  return data;
}

export async function findUserByInviteCode(inviteCode: string): Promise<User | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("users")
    .select("*")
    .eq("invite_code", inviteCode)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function incrementInvitesCount(userUuid: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("users")
    .update({ invites_count: supabase.rpc('increment_invites_count') })
    .eq("uuid", userUuid);

  return !error;
}

export async function updateUserInvitedBy(userUuid: string, invitedByUuid: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("users")
    .update({ invited_by: invitedByUuid })
    .eq("uuid", userUuid);

  return !error;
}

// New function to verify API key and check rate limits
export async function verifyApiKeyAndRateLimit(apiKey: string): Promise<{ isValid: boolean; message?: string; userUuid?: string }> {
  if (!apiKey) {
    return { isValid: false, message: 'Missing API key' };
  }
  
  const supabase = getSupabaseClient();
  
  // Get the API key record
  const { data: apiKeyData, error: apiKeyError } = await supabase
    .from("apikeys")
    .select("*")
    .eq("api_key", apiKey)
    .eq("status", "created")
    .single();
  
  if (apiKeyError || !apiKeyData) {
    return { isValid: false, message: 'Invalid API key' };
  }
  
  const userUuid = apiKeyData.user_uuid;
  
  // Get user rate limit info
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("api_rate_limit, api_requests_current, api_rate_reset")
    .eq("uuid", userUuid)
    .single();
  
  if (userError || !userData) {
    return { isValid: false, message: 'User not found' };
  }
  
  const now = new Date();
  let rateLimit = userData.api_rate_limit || 20;
  let currentCount = userData.api_requests_current || 0;
  let resetTime = userData.api_rate_reset ? new Date(userData.api_rate_reset) : null;
  
  // Check if rate limit period has expired and reset if needed
  if (!resetTime || resetTime < now) {
    resetTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
    currentCount = 0;
  }
  
  // Check if rate limit is exceeded
  if (currentCount >= rateLimit) {
    const resetTimeStr = resetTime.toISOString();
    return { 
      isValid: false, 
      message: `Rate limit exceeded. Limit is ${rateLimit} requests per hour. Reset at ${resetTimeStr}` 
    };
  }
  
  // Increment the request count
  currentCount++;
  
  // Update the API key usage statistics
  await supabase
    .from("apikeys")
    .update({
      requests_count: (apiKeyData.requests_count || 0) + 1,
      requests_current_period: (apiKeyData.requests_current_period || 0) + 1,
      rate_limit_reset: resetTime.toISOString()
    })
    .eq("api_key", apiKey);
    
  // Update the user rate limit counters
  await supabase
    .from("users")
    .update({
      api_requests_current: currentCount,
      api_rate_reset: resetTime.toISOString()
    })
    .eq("uuid", userUuid);
  
  return { isValid: true, userUuid };
}

// Extension API key authentication function
export async function getUserByApiKey(apiKey: string): Promise<User | null> {
  if (!apiKey) {
    return null;
  }
  
  try {
    const crypto = require('crypto');
    const { getSupabaseClient } = require('@/models/db');
    const supabase = getSupabaseClient();
    
    // Hash the API key for lookup
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
    
    // First, try to find in extension_api_keys table
    const { data: extensionKey, error: extensionKeyError } = await supabase
      .from("extension_api_keys")
      .select("user_id, is_active")
      .eq("key_hash", keyHash)
      .eq("is_active", true)
      .single();
    
    if (extensionKey && !extensionKeyError) {
      // Update last_used timestamp
      await supabase
        .from("extension_api_keys")
        .update({ last_used: new Date().toISOString() })
        .eq("key_hash", keyHash);
      
      // Get user data
      const user = await findUserByUuid(extensionKey.user_id);
      return user || null;
    }
    
    // Fallback: try legacy apikeys table for backward compatibility
    const legacySupabase = getSupabaseClient();
    const { data: legacyKey, error: legacyKeyError } = await legacySupabase
      .from("apikeys")
      .select("user_uuid")
      .eq("api_key", apiKey)
      .eq("status", "created")
      .single();
    
    if (legacyKey && !legacyKeyError) {
      const user = await findUserByUuid(legacyKey.user_uuid);
      return user || null;
    }
    
    return null;
  } catch (error) {
    console.error('Error validating API key:', error);
    return null;
  }
}
