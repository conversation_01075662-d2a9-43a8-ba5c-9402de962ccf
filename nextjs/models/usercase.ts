import { UserCase, UserCaseType } from "@/types/usercase";
import { getSupabaseClient } from "./db";
import { v4 as uuidv4 } from "uuid";

export enum UserCaseStatus {
  Online = "online",
  Offline = "offline",
  Deleted = "deleted",
}

/**
 * Insert a new user case
 */
export async function insertUserCase(userCase: UserCase): Promise<UserCase> {
  const supabase = getSupabaseClient();
  
  // Generate uuid if not provided
  if (!userCase.uuid) {
    userCase.uuid = uuidv4();
  }
  
  const { data, error } = await supabase
    .from("user_cases")
    .insert(userCase)
    .select()
    .single();

  if (error) {
    console.error("Error inserting user case:", error);
    throw error;
  }

  return data;
}

/**
 * Get all user cases with pagination
 */
export async function getAllUserCases(page = 1, limit = 50): Promise<{ data: UserCase[], count: number }> {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  const { data, error, count } = await supabase
    .from("user_cases")
    .select("*", { count: "exact" })
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error("Error fetching user cases:", error);
    throw error;
  }

  return { data, count: count || 0 };
}

/**
 * Find a user case by UUID
 */
export async function findUserCaseByUuid(uuid: string): Promise<UserCase | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("user_cases")
    .select("*")
    .eq("uuid", uuid)
    .single();

  if (error) {
    console.error("Error finding user case:", error);
    if (error.code === "PGRST116") {
      // Not found
      return undefined;
    }
    throw error;
  }

  return data;
}

/**
 * Update a user case
 */
export async function updateUserCase(uuid: string, userCase: Partial<UserCase>) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("user_cases")
    .update(userCase)
    .eq("uuid", uuid)
    .select()
    .single();

  if (error) {
    console.error("Error updating user case:", error);
    throw error;
  }

  return data;
}

/**
 * Soft delete a user case (mark as deleted)
 */
export async function deleteUserCase(uuid: string) {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("user_cases")
    .update({ status: "deleted" })
    .eq("uuid", uuid);

  if (error) {
    console.error("Error deleting user case:", error);
    throw error;
  }

  return true;
}

/**
 * Get user cases for a specific locale
 */
export async function getPublicUserCasesByLocale(locale: string, page = 1, limit = 50): Promise<{ data: UserCase[], count: number }> {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  // Get all user cases regardless of locale as we're handling multilingual content
  const { data, error, count } = await supabase
    .from("user_cases")
    .select("*", { count: "exact" })
    .eq("status", "online")
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error("Error fetching user cases by locale:", error);
    throw error;
  }

  // Filter to include only user cases that:
  // 1. Have the requested locale set as their primary locale, OR
  // 2. Have content/ai_summary in the requested locale, OR
  // 3. Have content/ai_summary in the default 'en' locale
  const filteredData = data?.filter(userCase => {
    // Case 1: Primary locale matches
    if (userCase.locale === locale) {
      return true;
    }
    
    // Case 2: Has content in the requested locale
    if (userCase.content && typeof userCase.content === 'object' && locale in userCase.content) {
      return true;
    }
    
    // Case 3: Has content in the default 'en' locale
    if (userCase.content && typeof userCase.content === 'object' && 'en' in userCase.content) {
      return true;
    }
    
    // Case 4: Has ai_summary in the requested locale
    if (userCase.ai_summary && typeof userCase.ai_summary === 'object' && locale in userCase.ai_summary) {
      return true;
    }
    
    // Case 5: Has ai_summary in the default 'en' locale
    if (userCase.ai_summary && typeof userCase.ai_summary === 'object' && 'en' in userCase.ai_summary) {
      return true;
    }
    
    // Case 6: Has details field with content
    return !!userCase.details;
  });

  // Process user cases to include only required fields
  const transformedData = (filteredData || []).map(userCase => {
    return {
      uuid: userCase.uuid,
      type: userCase.type,
      url: userCase.url,
      details: userCase.content && typeof userCase.content === 'object' && locale in userCase.content 
        ? userCase.content[locale] 
        : userCase.details,
      created: userCase.created_at,
      updated: userCase.updated_at,
      ai_summary: userCase.ai_summary && typeof userCase.ai_summary === 'object' && locale in userCase.ai_summary
        ? userCase.ai_summary[locale]
        : "",
      related_items: userCase.related_items,
      title: userCase.title,
      author_name: userCase.author_name,
      author_avatar_url: userCase.author_avatar_url,
      image_urls: userCase.image_urls || [],
      video_urls: userCase.video_urls || []
    };
  });

  return { data: transformedData, count: count || 0 };
}

/**
 * Get user cases by Item UUID
 */
export async function getUserCasesByItemUuid(itemUuid: string, locale?: string, page = 1, limit = 10): Promise<{ data: UserCase[], count: number }> {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  // Get data and count in a single query
  const { data, error, count } = await supabase
    .from("user_cases")
    .select("*", { count: "exact" })
    .eq("status", UserCaseStatus.Online)
    .contains("related_items", [itemUuid])
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error("Error fetching user cases by Item UUID:", error);
    return { data: [], count: 0 };
  }
  
  // If locale is provided, sort results to prioritize cases with content in the specified locale
  if (locale && data) {
    data.sort((a, b) => {
      // Case with matching locale as primary
      if (a.locale === locale && b.locale !== locale) return -1;
      if (a.locale !== locale && b.locale === locale) return 1;
      
      // Case with content in specified locale
      const aHasLocaleContent = a.content && typeof a.content === 'object' && locale in a.content;
      const bHasLocaleContent = b.content && typeof b.content === 'object' && locale in b.content;
      if (aHasLocaleContent && !bHasLocaleContent) return -1;
      if (!aHasLocaleContent && bHasLocaleContent) return 1;
      
      // Default sort by creation date, newest first
      return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
    });
  }

  return { data: data || [], count: count || 0 };
}

/**
 * Add translation to a user case
 */
export async function addUserCaseTranslation(
  uuid: string, 
  locale: string, 
  content: string, 
  aiSummary: string
) {
  const supabase = getSupabaseClient();
  
  // Get current user case
  const { data: userCase, error: fetchError } = await supabase
    .from("user_cases")
    .select("content, ai_summary")
    .eq("uuid", uuid)
    .single();
    
  if (fetchError) {
    console.error("Error fetching user case for translation:", fetchError);
    throw fetchError;
  }
  
  // Prepare updated data
  const updatedContent = { 
    ...(userCase.content || {}), 
    [locale]: content 
  };
  
  const updatedAiSummary = { 
    ...(userCase.ai_summary || {}), 
    [locale]: aiSummary 
  };
  
  // Update user case with new translations
  const { data, error } = await supabase
    .from("user_cases")
    .update({ 
      content: updatedContent, 
      ai_summary: updatedAiSummary 
    })
    .eq("uuid", uuid)
    .select()
    .single();

  if (error) {
    console.error("Error adding user case translation:", error);
    throw error;
  }

  return data;
}

export async function getUserCasesByRelatedItem(
  itemUuid: string,
  locale?: string,
  page: number = 1,
  limit: number = 50
): Promise<{ data: UserCase[], count: number }> {
  const supabase = getSupabaseClient();
  
  // Get data and count in a single query
  const { data, error, count } = await supabase
    .from("user_cases")
    .select("*", { count: "exact" })
    .contains("related_items", [itemUuid])
    .eq("status", UserCaseStatus.Online)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    console.error("Error fetching user cases by related Item:", error);
    return { data: [], count: 0 };
  }
  
  // If locale is provided, sort results to prioritize cases with content in the specified locale
  if (locale && data) {
    data.sort((a, b) => {
      // Case with matching locale as primary
      if (a.locale === locale && b.locale !== locale) return -1;
      if (a.locale !== locale && b.locale === locale) return 1;
      
      // Case with content in specified locale
      const aHasLocaleContent = a.content && typeof a.content === 'object' && locale in a.content;
      const bHasLocaleContent = b.content && typeof b.content === 'object' && locale in b.content;
      if (aHasLocaleContent && !bHasLocaleContent) return -1;
      if (!aHasLocaleContent && bHasLocaleContent) return 1;
      
      // Default sort by creation date, newest first
      return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
    });
  }

  return { data: data || [], count: count || 0 };
}

export async function getUserCasesByType(
  type: UserCaseType,
  locale?: string,
  page: number = 1, 
  limit: number = 50
): Promise<{ data: UserCase[], count: number }> {
  const supabase = getSupabaseClient();
  
  // Get data and count in a single query
  const { data, error, count } = await supabase
    .from("user_cases")
    .select("*", { count: "exact" })
    .eq("type", type)
    .eq("status", UserCaseStatus.Online)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    console.error("Error fetching user cases by type:", error);
    return { data: [], count: 0 };
  }
  
  // If locale is provided, sort results to prioritize cases with content in the specified locale
  if (locale && data) {
    data.sort((a, b) => {
      // Case with matching locale as primary
      if (a.locale === locale && b.locale !== locale) return -1;
      if (a.locale !== locale && b.locale === locale) return 1;
      
      // Case with content in specified locale
      const aHasLocaleContent = a.content && typeof a.content === 'object' && locale in a.content;
      const bHasLocaleContent = b.content && typeof b.content === 'object' && locale in b.content;
      if (aHasLocaleContent && !bHasLocaleContent) return -1;
      if (!aHasLocaleContent && bHasLocaleContent) return 1;
      
      // Default sort by creation date, newest first
      return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
    });
  }

  return { data: data || [], count: count || 0 };
} 