import { Items, ItemLocalization } from "@/types/items";
import { getSupabaseClient } from "@/models/db";
// getUserInfo might be needed for admin checks later, but not directly in model functions
// import { getUserInfo } from "@/services/user";
import { PostgrestError } from "@supabase/supabase-js";
import { v4 as uuidv4 } from "uuid";

// Items CRUD
// addItems(items)

export async function addItems(itemsData: Partial<Items> & { localizations?: Partial<ItemLocalization>[] }): Promise<{ data: Items | null, error: PostgrestError | Error | null }> {
  const client = getSupabaseClient();
  const item_uuid = itemsData.uuid || uuidv4();

  // Separate localizations if provided
  const { localizations, ...itemFields } = itemsData;

  // Ensure required fields for items table
  if (!itemFields.name || !itemFields.brief || !itemFields.website_url || !itemFields.author_name) {
      return { data: null, error: new Error("Missing required fields for Item: name, brief, website_url, author_name") };
  }

  // Insert Item first
  const { data: insertedItem, error: itemError } = await client
    .from("items")
    .insert({
      ...itemFields,
      uuid: item_uuid,
      updated_at: new Date().toISOString(), // Ensure updated_at is set
    })
    .select()
    .single();

  if (itemError) {
    console.error("Error adding Item:", itemError);
    return { data: null, error: itemError };
  }

  // Add localizations if provided
  if (localizations && localizations.length > 0 && insertedItem) {
    const localizationInserts = localizations
      .filter(loc => loc.language_code && loc.brief && loc.detail) // Filter out invalid localizations
      .map(loc => ({
        item_uuid: item_uuid,
        language_code: loc.language_code!,
        brief: loc.brief!,
        detail: loc.detail!,
        processinfo: loc.processinfo || '',
      }));

    if (localizationInserts.length > 0) {
        const { error: localizationError } = await client
            .from("item_localizations")
            .insert(localizationInserts);

        if (localizationError) {
            console.error("Error adding localizations:", localizationError);
            // If adding localizations fails, the Item is already inserted.
            // Return the inserted Item but log the localization error.
            // Consider if a rollback mechanism is needed outside this function.
            return { data: insertedItem, error: localizationError }; // Return partial success with error
        }
    }
  }

  return { data: insertedItem, error: null };

  /* // Transaction code removed due to type errors
  try {
    const result = await client.tx(async (tx) => {
      // ... (transaction logic) ...
    });
    return { data: result, error: null };
  } catch (error: any) {
    console.error("Transaction failed for addItems:", error);
    const pgError = error as PostgrestError;
    return { data: null, error: pgError.message ? pgError : new Error(error.message || "Transaction failed") };
  }
  */
}


// archiveItem(uuid) - Soft delete by setting a status or flag (assuming no status field yet, we'll skip implementation or add one)
// Keeping hard delete for now as per original code, but transaction added for safety.
export async function archiveItem(uuid: string): Promise<{ error: PostgrestError | Error | null }> {
  const client = getSupabaseClient();

  // First delete related localizations due to foreign key constraint
  const { error: localizationError } = await client
    .from("item_localizations")
    .delete()
    .eq("item_uuid", uuid);

  if (localizationError) {
    // If deleting localizations fails, don't proceed to delete the Item
    console.error("Error deleting localizations for Item:", localizationError);
    return { error: localizationError };
  }

  // Then delete the Item itself
  const { error: itemError } = await client
    .from("items")
    .delete()
    .eq("uuid", uuid); // Corrected field name

  if (itemError) {
    console.error("Error deleting Item:", itemError);
    return { error: itemError };
  }

  return { error: null };
}

// updateItems(items)
export async function updateItems(items: Partial<Items>): Promise<{ data: Items | null, error: PostgrestError | Error | null, isNewlyPublic?: boolean }> {
  if (!items.uuid) {
    return { data: null, error: new Error("Item UUID is required for update") };
  }

  const client = getSupabaseClient();
  // Exclude fields that shouldn't be directly updated this way
  const { uuid, created_at, clicks, ...updateData } = items;

  // Check if the Item is being set to public for the first time
  let isNewlyPublic = false;
  if (updateData.allow_public === true) {
    // Get the current status
    const { data: currentData } = await client
      .from("items")
      .select("allow_public, website_url")
      .eq("uuid", uuid)
      .single();
    
    if (currentData && currentData.allow_public === false) {
      isNewlyPublic = true;
      // 新增：查找 item_submission 并设为 approved
      if (currentData.website_url) {
        const { findSubmissionByWebsiteUrl, approveSubmission } = await import("@/models/submission");
        const submission = await findSubmissionByWebsiteUrl(currentData.website_url);
        if (submission && submission.status !== "approved") {
          await approveSubmission(submission.id);
        }
      }
    }
  }

  // Ensure required fields aren't being nulled out if they are part of the update
  // (Add checks here if needed, e.g., if name is being updated, ensure it's not empty)

  const { data, error } = await client
    .from("items")
    .update({
      ...updateData,
      updated_at: new Date().toISOString(), // Always update timestamp
    })
    .eq("uuid", uuid)
    .select()
    .single();

  if (error) {
    console.error("Error updating Item:", error);
  }

  return { data, error, isNewlyPublic };
}

// getItemByUuid(uuid, language_code) - Refactored to use a single query with join
export async function getItemByUuid(uuid: string, language_code: string = "en"): Promise<{ data: (Items & { localization?: Partial<ItemLocalization> }) | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Select only necessary fields from Item data
  const { data: itemData, error: itemError } = await client
    .from("items")
    .select("*") // Select all fields to ensure we have all required Items fields
    .eq("uuid", uuid)
    .single();

  if (itemError || !itemData) {
    // If Item not found, return error or null data
    if (itemError && itemError.code === 'PGRST116') { // PostgREST code for "Not Found"
        return { data: null, error: null }; // Or return the error if preferred: { data: null, error: itemError }
    }
    console.error("Error fetching Item by UUID:", itemError);
    return { data: null, error: itemError };
  }

  // Fetch specific localization (only fetch brief and processinfo, not detail)
  const { data: localizationData, error: locError } = await client
    .from("item_localizations")
    .select("language_code, brief, processinfo")
    .eq("item_uuid", uuid)
    .eq("language_code", language_code)
    .maybeSingle(); // Use maybeSingle as localization might not exist

  if (locError) {
    // Log error but don't fail the whole request if only localization is missing
    console.warn(`Error fetching localization for ${uuid} [${language_code}]:`, locError);
  }

  // Use item_location as brief if localization doesn't have brief
  const resultData: Items & { localization?: Partial<ItemLocalization> } = {
    ...itemData,
    ...(localizationData && { 
      localization: {
        ...localizationData,
        // If localization brief is missing, use item_location as brief
        brief: localizationData.brief || itemData.item_location || "",
      } 
    })
  };

  return { data: resultData, error: null }; // Return null error even if localization failed, as Item was found
}

// getItemsByWebsiteUrl(website_url)
export async function getItemsByWebsiteUrl(website_url: string): Promise<{ data: Items | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  const { data, error } = await client
   .from("items")
   .select("*")
   .eq("website_url", website_url)
   .single();
  if (error) {
    console.error("Error fetching Item by website URL:", error);
    return { data: null, error };
  }
  return { data, error: null }
}


// getAllPublicItems(limit, page)
export async function getAllPublicItems(limit: number = 50, page: number = 1, language_code: string = "en", filters?: { official?: boolean, tags?: string[], recommended?: boolean }): Promise<{ data: Items[] | null, error: PostgrestError | null, count: number | null }> {
  const client = getSupabaseClient();
  const offset = (page - 1) * limit;

  // Ensure limit is reasonable
  const safeLimit = Math.max(1, Math.min(limit, 100)); // Example: Max 100 per page

  // Start building the query
  let query = client
    .from("items")
    .select("*", { count: 'exact' }) // Fetch count along with data
    .eq("allow_public", true);

  // Apply filters if provided
  if (filters) {
    if (filters.official === true) {
      query = query.eq("is_official", true);
    }
    
    if (filters.recommended === true) {
      query = query.eq("is_recommended", true);
    }
    
    if (filters.tags && filters.tags.length > 0) {
      // Using containsAny to find Items that have any of the specified tags
      const tagsArray = `{${filters.tags.join(',')}}`;
      query = query.contains('tags', tagsArray);
    }
  }

  // Apply ordering and pagination
  query = query.order("updated_at", { ascending: false })
               .range(offset, offset + safeLimit - 1);

  // Execute the query
  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching public Items:", error);
    return { data: null, error, count: null };
  }

  if (!data || data.length === 0) {
    return { data, error, count };
  }

  // Get all Item UUIDs
  const itemUuids = data.map(item => item.uuid);

  // Fetch localizations for all Items in the requested language
  const { data: localizations, error: locError } = await client
    .from("item_localizations")
    .select("item_uuid, brief")
    .in("item_uuid", itemUuids)
    .eq("language_code", language_code);

  if (locError) {
    console.error("Error fetching localizations for Items:", locError);
    // Continue with the original data if there's an error fetching localizations
  }

  // Create a map of localizations by item_uuid
  const localizationMap = new Map();
  if (localizations) {
    localizations.forEach(loc => {
      localizationMap.set(loc.item_uuid, loc.brief);
    });
  }

  // Replace brief with localized brief if available
  const localizedData = data.map(item => {
    const localizedBrief = localizationMap.get(item.uuid);
    return {
      ...item,
      brief: localizedBrief || item.brief, // Use localized brief if available, otherwise use default
      // If you want to use item_location as fallback:
      // brief: localizedBrief || item.item_location || item.brief,
    };
  });

  return { data: localizedData, error, count };
}

// getAllItems with filtering support
export async function getAllItems(
  options: {
    limit?: number;
    page?: number;
    filter?: {
      is_recommended?: boolean;
      is_official?: boolean;
      allow_public?: boolean;
      search?: string;
      tags?: string[];
      localization?: string;
    }
  } = {}
): Promise<{ data: Items[] | null, error: PostgrestError | null, count: number | null }> {
  const client = getSupabaseClient();
  const limit = options.limit || 20;
  const page = options.page || 1;
  const offset = (page - 1) * limit;
  
  let query = client
    .from("items")
    .select("*", { count: 'exact' });
  
  // Apply filters if provided
  if (options.filter) {
    if (options.filter.is_recommended !== undefined) {
      query = query.eq("is_recommended", options.filter.is_recommended);
    }
    
    if (options.filter.is_official !== undefined) {
      query = query.eq("is_official", options.filter.is_official);
    }
    
    if (options.filter.allow_public !== undefined) {
      query = query.eq("allow_public", options.filter.allow_public);
    }
    
    if (options.filter.search) {
      query = query.or(`name.ilike.%${options.filter.search}%,brief.ilike.%${options.filter.search}%,author_name.ilike.%${options.filter.search}%`);
    }
    
    if (options.filter.tags && options.filter.tags.length > 0) {
      // Filter for Items that contain any of the provided tags
      const tagConditions = options.filter.tags.map(tag => `tags.cs.{${tag}}`);
      query = query.or(tagConditions.join(','));
    }
  }
  
  // Order by creation date, newest first
  query = query.order("created_at", { ascending: false });
  
  // Apply pagination
  query = query.range(offset, offset + limit - 1);
  
  const { data, error, count } = await query;

  // 根据localization从item_localizations表中获取brief和processinfo
  if (options.filter && options.filter.localization && data) {
    const { data: localizations, error: locError } = await client
      .from("item_localizations")
      .select("item_uuid, brief, processinfo")
      .eq("language_code", options.filter.localization)
      .in("item_uuid", data.map(item => item.uuid));

    if (locError) {
      console.error("Error fetching localizations for Items:", locError);
    }
    
    // 将localization数据添加到data中
    data.forEach(item => {
      const localization = localizations?.find(loc => loc.item_uuid === item.uuid);
      if (localization) {
        item.brief = localization.brief;
      }
    });
  }
  
  return { data, error, count };
}

// getRandomPublicItems(limit) - Refactored to use RPC
export async function getRandomPublicItems(limit: number = 10): Promise<{ data: Items[] | null, error: PostgrestError | null }> {
    const client = getSupabaseClient();
    const safeLimit = Math.max(1, Math.min(limit, 50)); // Example: Max 50 random items

    // Call the RPC function created in SQL
    const { data, error } = await client.rpc('get_random_public_items', { limit_count: safeLimit });

    if (error) {
        console.error("Error calling get_random_public_items RPC:", error);
        return { data: null, error };
    }

    // The RPC function is expected to return an array of items records
    return { data: data as Items[] | null, error: null };
}


// getPublicItemsByTags(tags, limit) - Enhanced to support filtering by official and recommended status
export async function getPublicItemsByTags(
    tags: string[] = [], 
    limit: number = 50,
    filters?: {
        is_official?: boolean,
        is_recommended?: boolean
    },
    language_code: string = "en"
): Promise<{ data: Items[] | null, error: PostgrestError | null, count: number | null }> {
    const client = getSupabaseClient();
    const safeLimit = Math.max(1, Math.min(limit, 100));

    // Start with a query builder
    let query = client
        .from('items')
        .select('*', { count: 'exact' })
        .eq('allow_public', true);

    // Apply tag filters if provided
    if (Array.isArray(tags) && tags.length > 0) {
        const tagsFormatted = `{${tags.map(tag => `"${tag.replace(/"/g, '""')}"`).join(',')}}`;
        query = query.overlaps('tags', tagsFormatted);
    }

    // Apply additional filters if provided
    if (filters) {
        if (filters.is_official !== undefined) {
            query = query.eq('is_official', filters.is_official);
        }
        
        if (filters.is_recommended !== undefined) {
            query = query.eq('is_recommended', filters.is_recommended);
        }
    }

    // Execute query with limit
    const { data, error, count } = await query.limit(safeLimit);

    if (error) {
        console.error("Error fetching public Items by tags:", error);
        return { data: null, error, count: null };
    }

    if (!data || data.length === 0) {
        return { data, error, count };
    }

    // Get all Item UUIDs
    const itemUuids = data.map(item => item.uuid);

    // Fetch localizations for all Items in the requested language
    const { data: localizations, error: locError } = await client
        .from("item_localizations")
        .select("item_uuid, brief")
        .in("item_uuid", itemUuids)
        .eq("language_code", language_code);

    if (locError) {
        console.error("Error fetching localizations for Items by tags:", locError);
        // Continue with the original data if there's an error fetching localizations
    }

    // Create a map of localizations by item_uuid
    const localizationMap = new Map();
    if (localizations) {
        localizations.forEach(loc => {
            localizationMap.set(loc.item_uuid, loc.brief);
        });
    }

    // Replace brief with localized brief if available
    const localizedData = data.map(item => {
        const localizedBrief = localizationMap.get(item.uuid);
        return {
            ...item,
            brief: localizedBrief || item.brief, // Use localized brief if available, otherwise use default
            // If you want to use item_location as fallback:
            // brief: localizedBrief || item.item_location || item.brief,
        };
    });

    return { data: localizedData, error, count };
}

// getAllTags() - Refactored to use RPC for efficiency
// Returns a list of unique tags and their counts across all public Items
export async function getAllTags(): Promise<{ data: { tag: string, count: number }[] | null, error: PostgrestError | null }> {
    const client = getSupabaseClient();

    // Call the RPC function created in SQL
    const { data, error } = await client.rpc('get_all_item_tags');

    if (error) {
        console.error("Error calling get_all_item_tags RPC:", error);
        return { data: null, error };
    }

    // The RPC function is expected to return JSON array like [{ "tag": "...", "count": ... }]
    return { data: data as { tag: string, count: number }[] | null, error: null };
}


// incrementClicks(uuid) - Uses existing RPC, seems fine
export async function incrementClicks(uuid: string): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { error } = await client.rpc('increment_item_clicks', { item_uuid_param: uuid });

  if (error) {
    console.error("Error incrementing clicks via RPC:", error);
  }

  return { error };
}

// Items LOCALIZATION CRUD
// updateItemLocalizations(uuid, language_code, brief, detail, processinfo) - Uses upsert, seems fine
export async function updateItemLocalizations(
    item_uuid: string,
    language_code: string,
    brief: string,
    detail: string,
    processinfo: string = ''
): Promise<{ data: ItemLocalization | null, error: PostgrestError | Error | null }> {

  // Basic validation
  if (!item_uuid || !language_code || !brief || !detail) {
      return { data: null, error: new Error("Missing required fields for localization update: item_uuid, language_code, brief, detail") };
  }

  const client = getSupabaseClient();

  const { data, error } = await client
    .from("item_localizations")
    .upsert(
      {
        item_uuid,
        language_code,
        brief,
        detail,
        processinfo,
      },
      {
        onConflict: 'item_uuid, language_code', // Specify conflict target columns
        // defaultToNull: false, // Ensure missing fields in input don't null existing DB values (if needed)
      }
    )
    .select()
    .single(); // Expecting a single record to be upserted/returned

  if (error) {
    console.error("Error upserting Item localization:", error);
  }

  return { data, error };
}

// searchItems - Search Items by query string in name, brief, or detail including item_localizations
export async function searchItems(query: string, language_code: string = "en", page: number = 1, limit: number = 50): Promise<{ data: Partial<Items & { item_uuid: string }>[] | null, count: number | null, error: PostgrestError | Error | null }> {
  const client = getSupabaseClient();
  const offset = (page - 1) * limit;
  const safeLimit = Math.max(1, Math.min(limit, 100)); // Max 100 per page

  try {
    // First search in the items table
    const { data: itemsData, error: itemsError, count: itemsCount } = await client
      .from("items")
      .select("uuid, name, brief, clicks", { count: 'exact' })
      .or(`name.ilike.%${query}%,brief.ilike.%${query}%`)
      .eq("allow_public", true)
      .order("clicks", { ascending: false }) // Sort by clicks in descending order
      .range(offset, offset + safeLimit - 1);

    if (itemsError) {
      console.error("Error searching Items:", itemsError);
      return { data: null, count: null, error: itemsError };
    }

    // Then search in item_localizations for the specific language, join items and filter allow_public = true
    const { data: locData, error: locError, count: locCount } = await client
      .from("item_localizations")
      .select(`
        item_uuid,
        brief,
        detail,
        items!inner(
          name,
          clicks,
          allow_public
        )
      `, { count: 'exact' })
      .eq("language_code", language_code)
      .or(`brief.ilike.%${query}%,detail.ilike.%${query}%`)
      .eq("items.allow_public", true)
      .order("items(clicks)", { ascending: false })
      .range(offset, offset + safeLimit - 1);

    if (locError) {
      console.error("Error searching Item localizations:", locError);
      // Still return itemsData if we have it, just log the localization error
      if (itemsData) {
        return {
          data: itemsData.map(item => ({
            item_uuid: item.uuid,
            name: item.name,
            brief: item.brief,
            clicks: item.clicks
          })),
          count: itemsCount,
          error: null
        };
      }
      return { data: null, count: null, error: locError };
    }

    // Format the localization data to match the expected structure
    const formattedLocData = locData?.map((loc: any) => ({
      item_uuid: loc.item_uuid,
      name: loc.items.name,
      brief: loc.brief,
      clicks: loc.items.clicks
    })) || [];

    // Combine results from both searches and remove duplicates
    // If an Item has both a match in items and item_localizations, prioritize the localization
    const seenItemUuids = new Set<string>();
    const combinedResults: any[] = [];
    
    // Add localization results first (higher priority)
    formattedLocData.forEach(item => {
      combinedResults.push(item);
      seenItemUuids.add(item.item_uuid);
    });
    
    // Add results from items table if not already included
    if (itemsData) {
      itemsData.forEach(item => {
        if (!seenItemUuids.has(item.uuid)) {
          combinedResults.push({
            item_uuid: item.uuid,
            name: item.name,
            brief: item.brief,
            clicks: item.clicks
          });
          seenItemUuids.add(item.uuid);
        }
      });
    }
    
    // Sort combined results by clicks in descending order
    combinedResults.sort((a, b) => (b.clicks || 0) - (a.clicks || 0));
    
    // Apply pagination to the combined results
    const paginatedResults = combinedResults.slice(0, safeLimit);
    
    return {
      data: paginatedResults,
      count: (itemsCount || 0) + (locCount || 0),
      error: null
    };
  } catch (err) {
    console.error("Unexpected error searching Items:", err);
    return { data: null, count: null, error: err };
  }
}

// countPublicItems - Get total count of public Items
export async function countPublicItems(): Promise<{ count: number | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  try {
    const { count, error } = await client
      .from("items")
      .select("*", { count: 'exact', head: true })
      .eq("allow_public", true);

    if (error) {
      console.error("Error counting public Items:", error);
      return { count: null, error };
    }

    return { count, error: null };
  } catch (err: any) {
    console.error("Unexpected error counting Items:", err);
    return { count: null, error: err };
  }
}

// getTagCounts - Get tag counts for all tags or specific tags
export async function getTagCounts(tagList?: string[]): Promise<{ data: { tag: string, count: number }[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  try {
    // If specific tags are provided, get counts only for those tags
    if (tagList && tagList.length > 0) {
      const tagsFormatted = `{${tagList.map(tag => `"${tag.replace(/"/g, '""')}"`).join(',')}}`;
      
      const { data, error } = await client.rpc('get_specific_tag_counts', { 
        tags_param: tagsFormatted 
      });

      if (error) {
        console.error("Error calling get_specific_tag_counts RPC:", error);
        return { data: null, error };
      }

      return { data, error: null };
    } 
    // Otherwise, get counts for all tags
    else {
      // This reuses the existing getAllTags function which already returns counts
      return getAllTags();
    }
  } catch (err: any) {
    console.error("Unexpected error getting tag counts:", err);
    return { data: null, error: err };
  }
}

// getItemsLeaderboard - Get public Items sorted by clicks or stars
export async function getItemsLeaderboard(
  sort_by: 'clicks' | 'stars' = 'clicks',
  limit: number = 50,
  page: number = 1,
  language_code: string = "en"
): Promise<{ data: Items[] | null, error: PostgrestError | null, count: number | null }> {
  const client = getSupabaseClient();
  const offset = (page - 1) * limit;
  const safeLimit = Math.max(1, Math.min(limit, 100)); // Max 100 per page

  // Build the query to select Items
  let query = client
    .from("items")
    .select("*", { count: 'exact' })
    .eq("allow_public", true);
  
  // Order by selected sort criteria
  if (sort_by === 'stars') {
    // Note: PostgreSQL's JSONB field ordering with nulls last
    query = query.order("metadata->stars", { ascending: false, nullsFirst: false });
  } else {
    // Default sort by clicks
    query = query.order("clicks", { ascending: false });
  }
  
  // Apply pagination
  query = query.range(offset, offset + safeLimit - 1);
  
  // Execute the query
  const { data, error, count } = await query;
  
  if (error) {
    console.error(`Error fetching Items leaderboard (sort: ${sort_by}):`, error);
    return { data: null, error, count: null };
  }
  
  if (!data || data.length === 0) {
    return { data: [], error: null, count };
  }

  // Get all Item UUIDs
  const itemUuids = data.map(item => item.uuid);

  // Fetch localizations for all Items in the requested language
  const { data: localizations, error: locError } = await client
    .from("item_localizations")
    .select("item_uuid, brief")
    .in("item_uuid", itemUuids)
    .eq("language_code", language_code);

  if (locError) {
    console.error(`Error fetching localizations for leaderboard Items:`, locError);
    // Continue with the original data if there's an error fetching localizations
  }

  // Create a map of localizations by item_uuid
  const localizationMap = new Map();
  if (localizations) {
    localizations.forEach(loc => {
      localizationMap.set(loc.item_uuid, loc.brief);
    });
  }

  // Replace brief with localized brief if available
  const localizedData = data.map(item => {
    const localizedBrief = localizationMap.get(item.uuid);
    return {
      ...item,
      brief: localizedBrief || item.brief,
    };
  });

  return { data: localizedData, error: null, count };
}

// Get localization data for an Item by UUID
export async function getItemLocalizationsByUuid(uuid: string): Promise<{ data: Partial<ItemLocalization>[] | null, error: PostgrestError | Error | null }> {
  if (!uuid) {
    return { data: null, error: new Error("Missing required UUID parameter") };
  }

  const client = getSupabaseClient();
  
  // Get all available localizations for this Item with brief and processinfo
  const { data, error } = await client
    .from("item_localizations")
    .select("language_code, brief, processinfo")
    .eq("item_uuid", uuid);
    
  if (error) {
    console.error("Error fetching Item localizations:", error);
    return { data: null, error };
  }
  
  return { data, error: null };
}

export async function getItemStats() {
  const supabase = getSupabaseClient();
  
  // Count total Items
  const { count: totalItems } = await supabase
    .from("items")
    .select("*", { count: 'exact', head: true });
  
  // Count official Items
  const { count: officialItems } = await supabase
    .from("items")
    .select("*", { count: 'exact', head: true })
    .eq("is_official", true);
  
  // Count recommended Items
  const { count: recommendedItems } = await supabase
    .from("items")
    .select("*", { count: 'exact', head: true })
    .eq("is_recommended", true);
  
  // Count public Items
  const { count: publicItems } = await supabase
    .from("items")
    .select("*", { count: 'exact', head: true })
    .eq("allow_public", true);
  
  // Count total submissions
  const { count: totalSubmissions } = await supabase
    .from("submissions")
    .select("*", { count: 'exact', head: true });
  
  // Count pending submissions
  const { count: pendingSubmissions } = await supabase
    .from("submissions")
    .select("*", { count: 'exact', head: true })
    .eq("status", "pending");
  
  // Count item localizations
  const { count: totalLocalizations } = await supabase
    .from("item_localizations")
    .select("*", { count: 'exact', head: true });
  
  // Sum all Item clicks
  const { data: clicksData } = await supabase
    .from("items")
    .select("clicks");
  
  const totalClicks = clicksData?.reduce((sum, item) => sum + (item.clicks || 0), 0) || 0;
  
  return {
    totalItems,
    officialItems,
    recommendedItems,
    publicItems,
    totalSubmissions,
    pendingSubmissions,
    totalLocalizations,
    totalClicks
  };
}

// Embedding search functions
export async function searchItemsByEmbedding(
  embedding: number[],
  language: string = 'en',
  threshold: number = 0.75,
  limit: number = 10
): Promise<{ matchingUuids: any[] | null, count: number | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  // Use the appropriate function based on language
  const matchFunction = 'match_items_localized';
  const countFunction = 'count_matching_items_localized';

  // Prepare RPC parameters
  const rpcParams: any = {
    query_embedding: embedding,
    match_threshold: threshold,
    match_count: limit,
    p_language_code: language
  };

  // Use the appropriate function to find similar items
  const { data: matchingUuids, error } = await client
    .rpc(matchFunction, rpcParams);

  if (error) {
    console.error(`Error searching with embeddings (${language}):`, error);
    return { matchingUuids: null, count: null, error };
  }

  // Count total results that would match this threshold
  const countParams: any = {
    query_embedding: embedding,
    match_threshold: threshold,
    p_language_code: language
  };

  // Use the count function to get total matches
  const { data: countData, error: countError } = await client
    .rpc(countFunction, countParams);

  if (countError) {
    console.error(`Error counting matches (${language}):`, countError);
  }

  return { matchingUuids, count: countData, error: null };
}

export async function getItemDetailsByUuids(
  matchingUuids: { uuid: string, similarity: number }[],
  language: string = 'en'
): Promise<any[]> {
  const isEnglish = language === 'en';
  
  // Fetch full Item data for each matching UUID
  const itemItems = await Promise.all(
    matchingUuids.map(async (item: { uuid: string, similarity: number }) => {
      try {
        let itemData: any = null;
        const supabase = getSupabaseClient();
        
        if (isEnglish) {
          // Get main Item data for English results
          const { data, error } = await supabase
            .from('items')
            .select('*')
            .eq('uuid', item.uuid)
            .single();
            
          if (error) {
            console.error(`Error fetching Item data for uuid ${item.uuid}:`, error);
            return null;
          }
          
          itemData = data;
        } else {
          // Get localized content for non-English results
          const { data: locData, error: locError } = await supabase
            .from('item_localizations')
            .select('*')
            .eq('item_uuid', item.uuid)
            .eq('language_code', language)
            .single();
            
          if (locError) {
            console.error(`Error fetching localization for uuid ${item.uuid}:`, locError);
            return null;
          }
          
          // Also fetch basic Item info to include with localization
          const { data: itemBasicData, error: itemError } = await supabase
            .from('items')
            .select('uuid, name, website_url, item_avatar_url, user_avatar_url')
            .eq('uuid', item.uuid)
            .single();
            
          if (itemError) {
            console.error(`Error fetching basic Item data for uuid ${item.uuid}:`, itemError);
            return null;
          }
          
          itemData = {
            ...locData,
            item: itemBasicData
          };
        }
        
        if (itemData) {
          return {
            ...itemData,
            similarity: item.similarity
          };
        }
        return null;
      } catch (err) {
        console.error(`Error fetching Item data for uuid ${item.uuid}:`, err);
        return null;
      }
    })
  );

  // Filter out any null results
  return itemItems.filter(item => item !== null);
}

// Tag management functions
export async function getTagById(tagId: number): Promise<{ data: { name: string } | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  return await client
    .from('tags')
    .select('name')
    .eq('id', tagId)
    .single();
}

export async function getItemsWithTag(tagId: number): Promise<{ data: { item_id: number }[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  return await client
    .from('items_tags')
    .select('item_id')
    .eq('tag_id', tagId);
}

export async function removeTagFromItems(tagId: number): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const { error } = await client
    .from('items_tags')
    .delete()
    .eq('tag_id', tagId);
    
  return { error };
}

export async function updateItemTags(itemId: number, updatedTags: string[]): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const { error } = await client
    .from('items')
    .update({ tags: updatedTags })
    .eq('id', itemId);
    
  return { error };
}

export async function deleteTag(tagId: number): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const { error } = await client
    .from('tags')
    .delete()
    .eq('id', tagId);
    
  return { error };
}

// Get Items that have a specific tag in their tags array
export async function getItemsByTagName(tagName: string): Promise<{ data: { id: number, tags: string[] }[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  return await client
    .from('items')
    .select('id, tags')
    .contains('tags', [tagName]);
}