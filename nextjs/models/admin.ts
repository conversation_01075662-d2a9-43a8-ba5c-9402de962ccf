import { getSupabaseClient } from "./db";
import { PostgrestError } from "@supabase/supabase-js";

// ========================================
// ADMIN DATA MANAGEMENT OPERATIONS
// ========================================

export interface AdminQueryOptions {
  table: string;
  limit: number;
  offset: number;
  search?: string;
}

export interface AdminQueryResult<T> {
  data: T[] | null;
  error: PostgrestError | null;
  count: number | null;
}

export interface AdminDeleteResult {
  success: number;
  failed: number;
  errors: string[];
}

// Get data from any table with admin privileges
export async function getAdminTableData<T = any>({
  table,
  limit,
  offset,
  search
}: AdminQueryOptions): Promise<AdminQueryResult<T>> {
  const client = getSupabaseClient();

  // Map frontend table names to actual database table names
  let tableName: string;
  let orderColumn: string;

  switch (table) {
    case "projects":
      tableName = "projects";
      orderColumn = "created_at";
      break;
    case "linkResources":
      tableName = "link_resources";
      orderColumn = "created_at";
      break;
    case "allLinks":
      tableName = "all_links";
      orderColumn = "last_updated";
      break;
    case "allLinksHistory":
      tableName = "all_links_history";
      orderColumn = "checked_at";
      break;
    case "discoveredLinks":
      tableName = "discovered_links";
      orderColumn = "discovered_at";
      break;
    case "userConfigs":
      tableName = "user_configs";
      orderColumn = "created_at";
      break;
    default:
      return {
        data: null,
        error: { message: "Invalid table specified" } as PostgrestError,
        count: null
      };
  }

  try {
    // Build the main query
    let query = client
      .from(tableName)
      .select("*")
      .order(orderColumn, { ascending: false })
      .range(offset, offset + limit - 1);

    // Build the count query
    let countQuery = client
      .from(tableName)
      .select("*", { count: "exact", head: true });

    // Add search conditions if provided
    if (search) {
      const searchConditions = getSearchConditions(table, search);
      if (searchConditions) {
        query = query.or(searchConditions);
        countQuery = countQuery.or(searchConditions);
      }
    }

    // Execute both queries in parallel
    const [{ data, error }, { count, error: countError }] = await Promise.all([
      query,
      countQuery
    ]);

    if (error || countError) {
      return {
        data: null,
        error: error || countError,
        count: null
      };
    }

    return {
      data: data || [],
      error: null,
      count: count || 0
    };
  } catch (error) {
    return {
      data: null,
      error: { message: error instanceof Error ? error.message : 'Unknown error' } as PostgrestError,
      count: null
    };
  }
}

// Delete multiple items from any table with admin privileges
export async function deleteAdminTableItems(
  table: string,
  itemIds: string[]
): Promise<AdminDeleteResult> {
  const client = getSupabaseClient();

  if (!itemIds || itemIds.length === 0) {
    return {
      success: 0,
      failed: 0,
      errors: ["No items provided for deletion"]
    };
  }

  // Map table names and determine ID column
  let tableName: string;
  let idColumn = "id";

  switch (table) {
    case "projects":
      tableName = "projects";
      break;
    case "linkResources":
      tableName = "link_resources";
      break;
    case "allLinks":
      tableName = "all_links";
      break;
    case "allLinksHistory":
      tableName = "all_links_history";
      break;
    case "discoveredLinks":
      tableName = "discovered_links";
      break;
    case "userConfigs":
      tableName = "user_configs";
      break;
    default:
      return {
        success: 0,
        failed: itemIds.length,
        errors: ["Invalid table specified"]
      };
  }

  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  // Process deletions in batches for better performance
  const batchSize = 50; // Supabase limit for bulk operations
  
  for (let i = 0; i < itemIds.length; i += batchSize) {
    const batch = itemIds.slice(i, i + batchSize);
    
    try {
      const { error } = await client
        .from(tableName)
        .delete()
        .in(idColumn, batch);

      if (error) {
        failed += batch.length;
        errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
      } else {
        success += batch.length;
      }
    } catch (error) {
      failed += batch.length;
      errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return { success, failed, errors };
}

// Helper function to build search conditions for different tables
function getSearchConditions(table: string, search: string): string | null {
  const searchTerm = search.trim();
  if (!searchTerm) return null;

  switch (table) {
    case "projects":
      return `name.ilike.%${searchTerm}%,domain.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`;
    case "linkResources":
      return `title.ilike.%${searchTerm}%,url.ilike.%${searchTerm}%,source.ilike.%${searchTerm}%`;
    case "allLinks":
    case "allLinksHistory":
      return `domain.ilike.%${searchTerm}%`;
    case "discoveredLinks":
      return `title.ilike.%${searchTerm}%,url.ilike.%${searchTerm}%,anchor_text.ilike.%${searchTerm}%`;
    case "userConfigs":
      return `config_name.ilike.%${searchTerm}%,config_type.ilike.%${searchTerm}%,user_id.ilike.%${searchTerm}%`;
    default:
      return null;
  }
}

// ========================================
// ADMIN STATISTICS OPERATIONS
// ========================================

export async function getAdminTableCount(tableName: string): Promise<{ count: number | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { count, error } = await client
    .from(tableName)
    .select("*", { count: "exact", head: true });

  return { count, error };
}

export async function getAdminOverviewStats(): Promise<{
  data: {
    projects: number;
    linkResources: number;
    discoveredLinks: number;
    allLinks: number;
    userConfigs: number;
  } | null;
  error: PostgrestError | null;
}> {
  try {
    const [
      projectsResult,
      linkResourcesResult,
      discoveredLinksResult,
      allLinksResult,
      userConfigsResult
    ] = await Promise.all([
      getAdminTableCount("projects"),
      getAdminTableCount("link_resources"),
      getAdminTableCount("discovered_links"),
      getAdminTableCount("all_links"),
      getAdminTableCount("user_configs")
    ]);

    // Check for errors
    const errors = [
      projectsResult.error,
      linkResourcesResult.error,
      discoveredLinksResult.error,
      allLinksResult.error,
      userConfigsResult.error
    ].filter(Boolean);

    if (errors.length > 0) {
      return {
        data: null,
        error: errors[0] // Return first error
      };
    }

    return {
      data: {
        projects: projectsResult.count || 0,
        linkResources: linkResourcesResult.count || 0,
        discoveredLinks: discoveredLinksResult.count || 0,
        allLinks: allLinksResult.count || 0,
        userConfigs: userConfigsResult.count || 0
      },
      error: null
    };
  } catch (error) {
    return {
      data: null,
      error: { message: error instanceof Error ? error.message : 'Unknown error' } as PostgrestError
    };
  }
}

// ========================================
// ADMIN AUTHENTICATION AND VALIDATION
// ========================================


/**
 * Get admin user data by user ID
 */
export async function getAdminUserData(userId: string): Promise<{
  data: any | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const { data: userData, error } = await client
    .from('users')
    .select('*')
    .eq('uuid', userId)
    .single();

  return { data: userData, error };
}

/**
 * Check if user is admin and return validation result
 */
export async function checkAdminAuth(userId: string): Promise<{
  success: boolean;
  error?: string;
  status?: number;
  userData?: any;
}> {
  if (!userId) {
    return { 
      success: false, 
      error: 'Unauthorized', 
      status: 401 
    };
  }

  // Get user data first
  const { data: userData, error } = await getAdminUserData(userId);
  
  if (error || !userData) {
    return { 
      success: false, 
      error: 'Failed to validate user', 
      status: 500 
    };
  }

  // Check if user is admin based on email
  const adminEmailsEnv = process.env.ADMIN_EMAILS || '';
  const adminEmails = adminEmailsEnv.split(',').map(e => e.trim().toLowerCase()).filter(e => e);
  
  if (!adminEmails.includes(userData.email?.toLowerCase())) {
    return { 
      success: false, 
      error: 'Forbidden', 
      status: 403 
    };
  }

  return { 
    success: true, 
    userData 
  };
}