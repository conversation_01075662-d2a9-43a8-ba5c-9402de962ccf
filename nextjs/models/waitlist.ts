import { getSupabaseClient } from './db';
import type {
  WaitlistEntry,
  WaitlistSubmission,
  WaitlistStats,
  WaitlistBySource,
  RecentWaitlistSignup
} from '@/types/waitlist';

export class WaitlistModel {
  private supabase = getSupabaseClient();

  /**
   * Add user to waitlist
   */
  async addToWaitlist(
    data: WaitlistSubmission,
    metadata?: {
      user_agent?: string;
      ip_address?: string;
      referrer?: string;
    }
  ): Promise<WaitlistEntry> {
    const { data: result, error } = await this.supabase
      .rpc('add_to_waitlist', {
        p_email: data.email,
        p_name: data.name || null,
        p_message: data.message || null,
        p_source: data.source || 'landing_page',
        p_user_agent: metadata?.user_agent || null,
        p_ip_address: metadata?.ip_address || null,
        p_referrer: metadata?.referrer || null,
        p_utm_source: data.utm_source || null,
        p_utm_medium: data.utm_medium || null,
        p_utm_campaign: data.utm_campaign || null,
        p_metadata: metadata ? JSON.stringify(metadata) : '{}'
      });

    if (error) {
      console.error('Error adding to waitlist:', error);
      throw new Error(`Failed to add to waitlist: ${error.message}`);
    }

    return result as WaitlistEntry;
  }

  /**
   * Get waitlist statistics
   */
  async getWaitlistStats(): Promise<WaitlistStats> {
    const { data, error } = await this.supabase
      .rpc('get_waitlist_stats');

    if (error) {
      console.error('Error getting waitlist stats:', error);
      throw new Error(`Failed to get waitlist stats: ${error.message}`);
    }

    return data[0] as WaitlistStats;
  }

  /**
   * Get waitlist signups by source
   */
  async getWaitlistBySource(): Promise<WaitlistBySource[]> {
    const { data, error } = await this.supabase
      .rpc('get_waitlist_by_source');

    if (error) {
      console.error('Error getting waitlist by source:', error);
      throw new Error(`Failed to get waitlist by source: ${error.message}`);
    }

    return data as WaitlistBySource[];
  }

  /**
   * Get recent waitlist signups
   */
  async getRecentSignups(limit: number = 10): Promise<RecentWaitlistSignup[]> {
    const { data, error } = await this.supabase
      .rpc('get_recent_waitlist_signups', { p_limit: limit });

    if (error) {
      console.error('Error getting recent signups:', error);
      throw new Error(`Failed to get recent signups: ${error.message}`);
    }

    return data as RecentWaitlistSignup[];
  }

  /**
   * Update waitlist status
   */
  async updateStatus(id: string, status: WaitlistEntry['status']): Promise<boolean> {
    const { data, error } = await this.supabase
      .rpc('update_waitlist_status', {
        p_id: id,
        p_status: status
      });

    if (error) {
      console.error('Error updating waitlist status:', error);
      throw new Error(`Failed to update waitlist status: ${error.message}`);
    }

    return data as boolean;
  }

  /**
   * Get all waitlist entries with pagination
   */
  async getWaitlistEntries(
    page: number = 1,
    limit: number = 50,
    status?: WaitlistEntry['status']
  ): Promise<{ data: WaitlistEntry[]; count: number }> {
    let query = this.supabase
      .from('waitlist')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error getting waitlist entries:', error);
      throw new Error(`Failed to get waitlist entries: ${error.message}`);
    }

    return {
      data: data as WaitlistEntry[],
      count: count || 0
    };
  }

  /**
   * Search waitlist entries by email
   */
  async searchByEmail(email: string): Promise<WaitlistEntry | null> {
    const { data, error } = await this.supabase
      .from('waitlist')
      .select('*')
      .eq('email', email.toLowerCase().trim())
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error searching waitlist by email:', error);
      throw new Error(`Failed to search waitlist: ${error.message}`);
    }

    return data as WaitlistEntry | null;
  }

  /**
   * Delete waitlist entry
   */
  async deleteEntry(id: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('waitlist')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting waitlist entry:', error);
      throw new Error(`Failed to delete waitlist entry: ${error.message}`);
    }

    return true;
  }
}

export const waitlistModel = new WaitlistModel(); 