import { getSupabaseClient } from './db'; // Use Supabase client
import { AffiliateProduct } from '@/types/affiliate';

// Get featured affiliate products (limit by count)
export async function getFeaturedAffiliateProducts(limit = 3) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('affiliate_products')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    .limit(limit);
    
  if (error) {
    console.error('Error fetching affiliate products:', error);
    return { data: [], error };
  }
  
  // 转换数据库字段名为类型中使用的camelCase
  const formattedData = data?.map(item => ({
    id: item.id,
    uuid: item.uuid,
    title: item.title,
    description: item.description,
    image_url: item.image_url,
    link: item.link,
    tags: item.tags,
    is_active: item.is_active,
    created_at: item.created_at,
    updated_at: item.updated_at,
    clicks: item.clicks
  }));
  
  return { data: formattedData as AffiliateProduct[], error: null };
}

// Get all affiliate products for admin page
export async function getAllAffiliateProducts() {
  const supabase = getSupabaseClient();
  
  // 尝试直接使用schema.table格式访问
  let { data, error } = await supabase
    .from('affiliate_products')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all affiliate products:', error);
  }
    
  if (!data || data.length === 0) {
    return { data: [], error };
  }
  
  // 转换数据库字段名为类型中使用的camelCase
  const formattedData = data?.map(item => ({
    id: item.id,
    uuid: item.uuid,
    title: item.title,
    description: item.description,
    image_url: item.image_url,
    link: item.link,
    tags: item.tags,
    is_active: item.is_active,
    created_at: item.created_at,
    updated_at: item.updated_at,
    clicks: item.clicks
  }));
  
  return { data: formattedData as AffiliateProduct[], error: null };
}

// Create a new affiliate product
export async function createAffiliateProduct(product: Omit<AffiliateProduct, 'id' | 'created_at' | 'updated_at' | 'clicks'>) {
  const supabase = getSupabaseClient();
  
  // 转换为数据库使用的snake_case
  const dbProduct = {
    uuid: product.uuid,
    title: product.title,
    description: product.description,
    image_url: product.image_url,
    link: product.link,
    tags: product.tags,
    is_active: product.is_active,
    clicks: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  const { data, error } = await supabase
    .from('affiliate_products')
    .insert(dbProduct)
    .select()
    .single();
    
  if (error) {
    console.error('Error creating affiliate product:', error);
    return { data: null, error };
  }
  
  // 转换回camelCase
  const formattedData = {
    id: data.id,
    uuid: data.uuid,
    title: data.title,
    description: data.description,
    image_url: data.image_url,
    link: data.link,
    tags: data.tags,
    is_active: data.is_active,
    created_at: data.created_at,
    updated_at: data.updated_at,
    clicks: data.clicks
  };
  
  return { data: formattedData as AffiliateProduct, error: null };
}

// Update an existing affiliate product
export async function updateAffiliateProduct(id: string, updates: Partial<AffiliateProduct>) {
  const supabase = getSupabaseClient();
  
  // 转换为数据库使用的snake_case
  const dbUpdates: any = {};
  if (updates.title !== undefined) dbUpdates.title = updates.title;
  if (updates.description !== undefined) dbUpdates.description = updates.description;
  if (updates.image_url !== undefined) dbUpdates.image_url = updates.image_url;
  if (updates.link !== undefined) dbUpdates.link = updates.link;
  if (updates.tags !== undefined) dbUpdates.tags = updates.tags;
  if (updates.is_active !== undefined) dbUpdates.is_active = updates.is_active;
  dbUpdates.updated_at = new Date().toISOString();
  
  const { data, error } = await supabase
    .from('affiliate_products')
    .update(dbUpdates)
    .eq('id', id)
    .select()
    .single();
    
  if (error) {
    console.error('Error updating affiliate product:', error);
    return { data: null, error };
  }
  
  // 转换回camelCase
  const formattedData = {
    id: data.id,
    uuid: data.uuid,
    title: data.title,
    description: data.description,
    image_url: data.image_url,
    link: data.link,
    tags: data.tags,
    is_active: data.is_active,
    created_at: data.created_at,
    updated_at: data.updated_at,
    clicks: data.clicks
  };
  
  return { data: formattedData as AffiliateProduct, error: null };
}

// Delete an affiliate product
export async function deleteAffiliateProduct(id: string) {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from('affiliate_products')
    .delete()
    .eq('id', id);
    
  if (error) {
    console.error('Error deleting affiliate product:', error);
    return { error };
  }
  
  return { error: null };
}

// Increment click count for an affiliate product
export async function incrementAffiliateClicks(id: string) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase.rpc('increment_affiliate_clicks', { product_id: id });
  
  if (error) {
    console.error('Error incrementing clicks:', error);
    return { data: null, error };
  }
  
  return { data, error: null };
}