import { getSupabaseClient } from "@/models/db";
import { v4 as uuidv4 } from "uuid";
import { PostgrestError } from "@supabase/supabase-js";

export interface EmailTemplate {
  id: number;
  name: string;
  subject: string;
  body: string;
  event_type: 'item_submitted' | 'item_published' | 'item_rejected';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmailLog {
  id: number;
  uuid: string;
  template_id: number;
  recipient: string;
  subject: string;
  body: string;
  event_type: 'item_submitted' | 'item_published' | 'item_rejected';
  status: 'pending' | 'sent' | 'failed';
  error_message?: string;
  metadata: any;
  created_at: string;
  sent_at?: string;
}

// Get all email templates
export async function getEmailTemplates(): Promise<{ 
  data: EmailTemplate[] | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const { data, error } = await client
    .from("email_templates")
    .select("*")
    .order("created_at", { ascending: false });
    
  return { data, error };
}

// Get email template by ID
export async function getEmailTemplateById(id: number): Promise<{ 
  data: EmailTemplate | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const { data, error } = await client
    .from("email_templates")
    .select("*")
    .eq("id", id)
    .single();
    
  return { data, error };
}

// Get email template by event type
export async function getEmailTemplateByEventType(eventType: string): Promise<{ 
  data: EmailTemplate | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const { data, error } = await client
    .from("email_templates")
    .select("*")
    .eq("event_type", eventType)
    .eq("is_active", true)
    .single();
    
  return { data, error };
}

// Create or update email template
export async function upsertEmailTemplate(template: Partial<EmailTemplate>): Promise<{ 
  data: EmailTemplate | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const updateData = {
    ...template,
    updated_at: new Date().toISOString()
  };
  
  if (!template.id) {
    // Create new template
    const { data, error } = await client
      .from("email_templates")
      .insert({
        ...updateData,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
      
    return { data, error };
  }
  
  // Update existing template
  const { data, error } = await client
    .from("email_templates")
    .update(updateData)
    .eq("id", template.id)
    .select()
    .single();
    
  return { data, error };
}

// Log email for sending and get the log entry
export async function createEmailLog(
  templateId: number,
  recipient: string,
  subject: string,
  body: string,
  eventType: 'item_submitted' | 'item_published' | 'item_rejected',
  metadata: any = {}
): Promise<{ 
  data: EmailLog | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const { data, error } = await client
    .from("email_logs")
    .insert({
      uuid: uuidv4(),
      template_id: templateId,
      recipient,
      subject,
      body,
      event_type: eventType,
      status: 'pending',
      metadata,
      created_at: new Date().toISOString()
    })
    .select()
    .single();
    
  return { data, error };
}

// Update email log status
export async function updateEmailLogStatus(
  id: number,
  status: 'sent' | 'failed',
  errorMessage?: string
): Promise<{ 
  data: EmailLog | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const updateData: any = {
    status,
  };
  
  if (status === 'sent') {
    updateData.sent_at = new Date().toISOString();
  }
  
  if (status === 'failed' && errorMessage) {
    updateData.error_message = errorMessage;
  }
  
  const { data, error } = await client
    .from("email_logs")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();
    
  return { data, error };
}

// Get all email logs with pagination
export async function getEmailLogs(
  limit: number = 50, 
  page: number = 1, 
  status?: 'pending' | 'sent' | 'failed'
): Promise<{ 
  data: EmailLog[] | null;
  error: PostgrestError | null;
  count: number | null;
}> {
  const client = getSupabaseClient();
  const offset = (page - 1) * limit;
  
  let query = client
    .from("email_logs")
    .select("*", { count: 'exact' })
    .order("created_at", { ascending: false });
    
  if (status) {
    query = query.eq("status", status);
  }
  
  const { data, error, count } = await query
    .range(offset, offset + limit - 1);
    
  return { data, error, count };
}

// Get pending emails to be sent
export async function getPendingEmails(limit: number = 50): Promise<{ 
  data: EmailLog[] | null;
  error: PostgrestError | null;
}> {
  const client = getSupabaseClient();
  
  const { data, error } = await client
    .from("email_logs")
    .select("*")
    .eq("status", "pending")
    .order("created_at", { ascending: true })
    .limit(limit);
    
  return { data, error };
} 