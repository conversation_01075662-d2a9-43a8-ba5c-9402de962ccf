import { getSupabaseClient } from './db';
import { DomainInfo, DomainWhoisData, DomainFilters, DomainStats } from '@/types/domain';
import { validateDomainFormat } from '@/utils/url-normalization';

export class DomainModel {
  /**
   * Get all domains for a user with project counts and association details
   * Automatically discovers and associates related projects
   */
  static async getUserDomains(userId: string, filters?: DomainFilters, autoAssociate: boolean = true): Promise<DomainInfo[]> {
    const supabase = getSupabaseClient();
    
    // Auto-discover and update project associations for all domains (with throttling)
    if (autoAssociate) {
      await this.updateAllDomainProjectAssociationsThrottled(userId);
    }
    
    // Get domains from the dedicated domain_management table with project associations
    let query = supabase
      .from('domain_management_with_projects')
      .select('*')
      .eq('user_id', userId);

    const { data: domainData, error: domainError } = await query;
    
    if (domainError) {
      throw domainError;
    }

    if (!domainData || domainData.length === 0) {
      return [];
    }

    let domains = domainData.map(row => {
      const whoisData: DomainWhoisData = row.whois_data || {};
      const isActive = row.status === 'active' && (!row.expiry_date || new Date(row.expiry_date) > new Date());
      
      return {
        id: row.id,
        domain: row.domain,
        registrar: row.registrar || whoisData.registrar,
        createdDate: row.created_date || whoisData.createdDate,
        expiryDate: row.expiry_date || whoisData.expiryDate,
        status: row.status || whoisData.status,
        nameServers: row.name_servers || whoisData.nameServers,
        registrationPrice: row.registration_price,
        renewalPrice: row.renewal_price,
        currency: row.currency,
        dnsProvider: row.dns_provider || this.inferDnsProvider(row.name_servers || whoisData.nameServers),
        autoRenew: row.auto_renew,
        projectCount: row.project_count || 0,
        associatedProjects: row.associated_projects || [],
        lastUpdated: row.whois_last_updated,
        isActive,
        notes: row.notes,
        tags: row.tags || [],
        isFavorite: row.is_favorite,
        monitorExpiry: row.monitor_expiry,
        alertDaysBefore: row.alert_days_before
      };
    });

    // Apply filters
    if (filters?.status && filters.status !== 'all') {
      switch (filters.status) {
        case 'active':
          domains = domains.filter(d => d.isActive);
          break;
        case 'expired':
          domains = domains.filter(d => !d.isActive && d.expiryDate && new Date(d.expiryDate) < new Date());
          break;
        case 'expiring':
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
          domains = domains.filter(d => 
            d.isActive && 
            d.expiryDate && 
            new Date(d.expiryDate) <= thirtyDaysFromNow
          );
          break;
      }
    }

    if (filters?.registrar) {
      domains = domains.filter(d => d.registrar === filters.registrar);
    }

    if (filters?.dnsProvider) {
      domains = domains.filter(d => d.dnsProvider === filters.dnsProvider);
    }

    // Apply sorting
    if (filters?.sortBy) {
      domains.sort((a, b) => {
        let aVal: any, bVal: any;
        
        switch (filters.sortBy) {
          case 'domain':
            aVal = a.domain;
            bVal = b.domain;
            break;
          case 'expiryDate':
            aVal = a.expiryDate ? new Date(a.expiryDate) : new Date(0);
            bVal = b.expiryDate ? new Date(b.expiryDate) : new Date(0);
            break;
          case 'createdDate':
            aVal = a.createdDate ? new Date(a.createdDate) : new Date(0);
            bVal = b.createdDate ? new Date(b.createdDate) : new Date(0);
            break;
          case 'projectCount':
            aVal = a.projectCount;
            bVal = b.projectCount;
            break;
          case 'renewalPrice':
            aVal = a.renewalPrice || 0;
            bVal = b.renewalPrice || 0;
            break;
          default:
            aVal = a.domain;
            bVal = b.domain;
        }

        if (aVal < bVal) return filters.sortOrder === 'desc' ? 1 : -1;
        if (aVal > bVal) return filters.sortOrder === 'desc' ? -1 : 1;
        return 0;
      });
    } else {
      domains.sort((a, b) => a.domain.localeCompare(b.domain));
    }

    return domains;
  }

  /**
   * Get domain statistics for a user using database function
   */
  static async getUserDomainStats(userId: string): Promise<DomainStats> {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .rpc('get_user_domain_statistics', { p_user_id: userId });

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      return {
        totalDomains: 0,
        activeDomains: 0,
        expiredDomains: 0,
        expiringDomains: 0,
        totalProjects: 0
      };
    }

    return {
      totalDomains: data[0].total_domains || 0,
      activeDomains: data[0].active_domains || 0,
      expiredDomains: data[0].expired_domains || 0,
      expiringDomains: data[0].expiring_soon || 0,
      totalProjects: data[0].total_projects || 0
    };
  }

  /**
   * Create or update domain record
   */
  static async createOrUpdateDomain(userId: string, domainData: {
    domain: string;
    registrar?: string;
    createdDate?: string;
    expiryDate?: string;
    registrationPrice?: number;
    renewalPrice?: number;
    currency?: string;
    autoRenew?: boolean;
    dnsProvider?: string;
    nameServers?: string[];
    status?: string;
    notes?: string;
    tags?: string[];
    monitorExpiry?: boolean;
    alertDaysBefore?: number;
    whoisData?: DomainWhoisData;
  }): Promise<DomainInfo> {
    const supabase = getSupabaseClient();
    
    // Validate domain format before processing
    if (!validateDomainFormat(domainData.domain)) {
      throw new Error('Invalid domain format. Only top-level domains (xxx.xxx) are allowed.');
    }
    
    const insertData = {
      domain: domainData.domain,
      user_id: userId,
      registrar: domainData.registrar,
      created_date: domainData.createdDate,
      expiry_date: domainData.expiryDate,
      registration_price: domainData.registrationPrice,
      renewal_price: domainData.renewalPrice,
      currency: domainData.currency || 'USD',
      auto_renew: domainData.autoRenew || false,
      dns_provider: domainData.dnsProvider,
      name_servers: domainData.nameServers,
      status: domainData.status || 'active',
      notes: domainData.notes,
      tags: domainData.tags || [],
      monitor_expiry: domainData.monitorExpiry !== false,
      alert_days_before: domainData.alertDaysBefore || 30,
      whois_data: domainData.whoisData ? JSON.stringify(domainData.whoisData) : null
    };

    const { data, error } = await supabase
      .from('domain_management')
      .upsert(insertData, { 
        onConflict: 'domain,user_id',
        ignoreDuplicates: false 
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Convert database row to DomainInfo format
    return this.convertDbRowToDomainInfo(data);
  }

  /**
   * Update domain WHOIS data
   */
  static async updateDomainWhois(userId: string, domain: string, whoisData: DomainWhoisData): Promise<void> {
    const supabase = getSupabaseClient();
    
    const updateData: any = {
      whois_data: JSON.stringify(whoisData)
    };

    // Update registrar, dates, and nameservers from WHOIS if available
    if (whoisData.registrar) updateData.registrar = whoisData.registrar;
    if (whoisData.createdDate) updateData.created_date = whoisData.createdDate;
    if (whoisData.expiryDate) updateData.expiry_date = whoisData.expiryDate;
    if (whoisData.nameServers) {
      updateData.name_servers = whoisData.nameServers;
      updateData.dns_provider = this.inferDnsProvider(whoisData.nameServers);
    }

    const { error } = await supabase
      .from('domain_management')
      .update(updateData)
      .eq('user_id', userId)
      .eq('domain', domain);

    if (error) {
      throw error;
    }
  }

  /**
   * Get unique registrars for filter options
   */
  static async getUserRegistrars(userId: string): Promise<string[]> {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('domain_management')
      .select('registrar')
      .eq('user_id', userId)
      .not('registrar', 'is', null);

    if (error) {
      throw error;
    }

    const registrars = new Set<string>();
    data?.forEach(row => {
      if (row.registrar) {
        registrars.add(row.registrar);
      }
    });

    return Array.from(registrars).sort();
  }

  /**
   * Get unique DNS providers for filter options
   */
  static async getUserDnsProviders(userId: string): Promise<string[]> {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('domain_management')
      .select('dns_provider')
      .eq('user_id', userId)
      .not('dns_provider', 'is', null);

    if (error) {
      throw error;
    }

    const providers = new Set<string>();
    data?.forEach(row => {
      if (row.dns_provider) {
        providers.add(row.dns_provider);
      }
    });

    return Array.from(providers).sort();
  }

  /**
   * Associate domain with project
   */
  static async associateDomainWithProject(userId: string, domainId: string, projectId: string, isPrimary = false): Promise<void> {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('domain_project_associations')
      .upsert({
        domain_id: domainId,
        project_id: projectId,
        user_id: userId,
        is_primary: isPrimary
      }, {
        onConflict: 'domain_id,project_id'
      });

    if (error) {
      throw error;
    }
  }

  /**
   * Remove domain-project association
   */
  static async removeDomainProjectAssociation(domainId: string, projectId: string): Promise<void> {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('domain_project_associations')
      .delete()
      .eq('domain_id', domainId)
      .eq('project_id', projectId);

    if (error) {
      throw error;
    }
  }

  /**
   * Get expiring domains for a user
   */
  static async getExpiringDomains(userId: string, daysAhead = 30): Promise<any[]> {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .rpc('get_expiring_domains', { 
        p_user_id: userId, 
        p_days_ahead: daysAhead 
      });

    if (error) {
      throw error;
    }

    return data || [];
  }

  /**
   * Delete domain record
   */
  static async deleteDomain(userId: string, domainId: string): Promise<void> {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('domain_management')
      .delete()
      .eq('id', domainId)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }
  }

  // Cache for throttling association updates (userId -> timestamp)
  private static associationUpdateCache = new Map<string, number>();
  private static readonly ASSOCIATION_UPDATE_THROTTLE_MS = 5 * 60 * 1000; // 5 minutes

  /**
   * Update project associations for all user domains (with throttling)
   */
  static async updateAllDomainProjectAssociationsThrottled(userId: string): Promise<void> {
    const now = Date.now();
    const lastUpdate = this.associationUpdateCache.get(userId) || 0;
    
    // Skip if updated within throttle period
    if (now - lastUpdate < this.ASSOCIATION_UPDATE_THROTTLE_MS) {
      return;
    }
    
    // Update cache
    this.associationUpdateCache.set(userId, now);
    
    // Perform actual update
    await this.updateAllDomainProjectAssociations(userId);
  }

  /**
   * Update project associations for all user domains
   */
  static async updateAllDomainProjectAssociations(userId: string): Promise<void> {
    const supabase = getSupabaseClient();
    
    try {
      // Get all user domains (without project associations to avoid recursion)
      const { data: domains, error: domainsError } = await supabase
        .from('domain_management')
        .select('id, domain')
        .eq('user_id', userId);

      if (domainsError || !domains) {
        console.warn('Failed to fetch domains for association update:', domainsError);
        return;
      }

      // Get all user projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('id, name, domain')
        .eq('user_id', userId);

      if (projectsError || !projects) {
        console.warn('Failed to fetch projects for association update:', projectsError);
        return;
      }

      // Track total associations created
      let totalAssociations = 0;

      // For each domain, find matching projects
      for (const domain of domains) {
        for (const project of projects) {
          try {
            // Extract top-level domain from project
            const projectUrl = new URL(project.domain.startsWith('http') ? project.domain : `https://${project.domain}`);
            let projectDomain = projectUrl.hostname.toLowerCase().replace(/^www\./, '');
            const projectTopLevelDomain = this.extractTopLevelDomainFromHostname(projectDomain);
            
            // Check if project's top-level domain matches the domain
            if (projectTopLevelDomain === domain.domain.toLowerCase()) {
              // Check if association already exists
              const { data: existingAssociation } = await supabase
                .from('domain_project_associations')
                .select('id')
                .eq('domain_id', domain.id)
                .eq('project_id', project.id)
                .single();

              if (!existingAssociation) {
                // Create association
                await this.associateDomainWithProject(userId, domain.id, project.id, false);
                totalAssociations++;
              }
            }
          } catch (urlError) {
            // If URL parsing fails, try direct string comparison
            const cleanProjectDomain = project.domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
            const projectTopLevelDomain = this.extractTopLevelDomainFromHostname(cleanProjectDomain);
            
            if (projectTopLevelDomain === domain.domain.toLowerCase()) {
              // Check if association already exists
              const { data: existingAssociation } = await supabase
                .from('domain_project_associations')
                .select('id')
                .eq('domain_id', domain.id)
                .eq('project_id', project.id)
                .single();

              if (!existingAssociation) {
                await this.associateDomainWithProject(userId, domain.id, project.id, false);
                totalAssociations++;
              }
            }
          }
        }
      }

      if (totalAssociations > 0) {
        console.log(`Auto-associated ${totalAssociations} domain-project relationships for user ${userId}`);
      }
    } catch (error) {
      console.error('Error updating domain-project associations:', error);
    }
  }

  /**
   * Auto-discover and associate related projects with a domain
   */
  static async discoverAndAssociateRelatedProjects(userId: string, domain: string, domainId: string): Promise<{
    associatedCount: number;
    projects: Array<{ id: string; name: string; domain: string }>;
  }> {
    const supabase = getSupabaseClient();
    
    try {
      // Get all user's projects
      const { data: projects, error } = await supabase
        .from('projects')
        .select('id, name, domain')
        .eq('user_id', userId);

      if (error || !projects) {
        console.error('Error fetching user projects:', error);
        return { associatedCount: 0, projects: [] };
      }

      const relatedProjects = [];
      
      for (const project of projects) {
        try {
          // Check if project domain matches the added domain
          const projectUrl = new URL(project.domain.startsWith('http') ? project.domain : `https://${project.domain}`);
          let projectDomain = projectUrl.hostname.toLowerCase().replace(/^www\./, '');
          
          // Extract top-level domain from project
          const projectTopLevelDomain = this.extractTopLevelDomainFromHostname(projectDomain);
          
          // Check if project's top-level domain matches the added domain
          if (projectTopLevelDomain === domain.toLowerCase()) {
            // Check if association already exists
            const { data: existingAssociation } = await supabase
              .from('domain_project_associations')
              .select('id')
              .eq('domain_id', domainId)
              .eq('project_id', project.id)
              .single();

            if (!existingAssociation) {
              // Create association
              await this.associateDomainWithProject(userId, domainId, project.id, false);
              relatedProjects.push({
                id: project.id,
                name: project.name,
                domain: project.domain
              });
            }
          }
        } catch (urlError) {
          // If URL parsing fails, try direct string comparison
          const cleanProjectDomain = project.domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
          const projectTopLevelDomain = this.extractTopLevelDomainFromHostname(cleanProjectDomain);
          
          if (projectTopLevelDomain === domain.toLowerCase()) {
            // Check if association already exists
            const { data: existingAssociation } = await supabase
              .from('domain_project_associations')
              .select('id')
              .eq('domain_id', domainId)
              .eq('project_id', project.id)
              .single();

            if (!existingAssociation) {
              await this.associateDomainWithProject(userId, domainId, project.id, false);
              relatedProjects.push({
                id: project.id,
                name: project.name,
                domain: project.domain
              });
            }
          }
        }
      }

      console.log(`Auto-associated ${relatedProjects.length} projects with domain ${domain}`);
      return {
        associatedCount: relatedProjects.length,
        projects: relatedProjects
      };
    } catch (error) {
      console.error('Error discovering related projects:', error);
      return { associatedCount: 0, projects: [] };
    }
  }

  /**
   * Extract top-level domain from hostname
   */
  private static extractTopLevelDomainFromHostname(hostname: string): string {
    if (!hostname) return '';
    
    const parts = hostname.split('.');
    if (parts.length >= 2) {
      // Return the last two parts (domain.tld)
      return parts.slice(-2).join('.').toLowerCase();
    }
    
    return hostname.toLowerCase();
  }

  /**
   * Add domain from project domain (migration helper)
   */
  static async addDomainFromProject(userId: string, projectDomain: string, projectId: string): Promise<DomainInfo | null> {
    try {
      // Check if domain already exists
      const existing = await this.getUserDomains(userId);
      const existingDomain = existing.find(d => d.domain === projectDomain);
      
      if (existingDomain) {
        // Just associate with project if not already associated
        await this.associateDomainWithProject(userId, existingDomain.id, projectId);
        return existingDomain;
      }

      // Create new domain record
      const domainData = await this.createOrUpdateDomain(userId, {
        domain: projectDomain,
        notes: `Auto-imported from project migration`
      });

      // Associate with project
      await this.associateDomainWithProject(userId, domainData.id, projectId, true);

      return domainData;
    } catch (error) {
      console.error('Error adding domain from project:', error);
      return null;
    }
  }

  /**
   * Infer DNS provider from nameservers
   */
  private static inferDnsProvider(nameServers?: string[]): string | undefined {
    if (!nameServers || nameServers.length === 0) return undefined;

    const firstNs = nameServers[0].toLowerCase();
    
    if (firstNs.includes('cloudflare')) return 'Cloudflare';
    if (firstNs.includes('amazonaws')) return 'AWS Route 53';
    if (firstNs.includes('googledomains') || firstNs.includes('google')) return 'Google Domains';
    if (firstNs.includes('godaddy')) return 'GoDaddy';
    if (firstNs.includes('namecheap')) return 'Namecheap';
    if (firstNs.includes('digitalocean')) return 'DigitalOcean';
    if (firstNs.includes('linode')) return 'Linode';
    if (firstNs.includes('vultr')) return 'Vultr';
    
    return 'Other';
  }

  /**
   * Check if domain WHOIS data needs refresh
   */
  static async needsWhoisRefresh(userId: string, domain: string): Promise<boolean> {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('domain_management')
      .select('whois_cache_expires')
      .eq('user_id', userId)
      .eq('domain', domain)
      .single();

    if (error || !data?.whois_cache_expires) return true;

    return new Date(data.whois_cache_expires) <= new Date();
  }

  /**
   * Convert database row to DomainInfo format
   */
  private static convertDbRowToDomainInfo(row: any): DomainInfo {
    const whoisData: DomainWhoisData = row.whois_data ? JSON.parse(row.whois_data) : {};
    const isActive = row.status === 'active' && (!row.expiry_date || new Date(row.expiry_date) > new Date());
    
    return {
      id: row.id,
      domain: row.domain,
      registrar: row.registrar || whoisData.registrar,
      createdDate: row.created_date || whoisData.createdDate,
      expiryDate: row.expiry_date || whoisData.expiryDate,
      status: row.status || whoisData.status,
      nameServers: row.name_servers || whoisData.nameServers,
      registrationPrice: row.registration_price,
      renewalPrice: row.renewal_price,
      currency: row.currency,
      dnsProvider: row.dns_provider || this.inferDnsProvider(row.name_servers || whoisData.nameServers),
      autoRenew: row.auto_renew,
      projectCount: 0, // Will be populated by view
      associatedProjects: [],
      lastUpdated: row.whois_last_updated,
      isActive,
      notes: row.notes,
      tags: row.tags || [],
      isFavorite: row.is_favorite,
      monitorExpiry: row.monitor_expiry,
      alertDaysBefore: row.alert_days_before
    };
  }
} 