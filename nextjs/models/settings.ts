import { getSupabaseClient } from "./db";

export interface SystemSetting {
  id?: number;
  key: string;
  value: string;
  updated_at?: string;
}

export async function getSetting(key: string): Promise<string | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("settings")
    .select("value")
    .eq("key", key)
    .single();

  if (error || !data) {
    return null;
  }

  return data.value;
}

export async function upsertSetting(key: string, value: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("settings")
    .upsert({ key, value, updated_at: new Date().toISOString() }, 
    { onConflict: 'key' });

  return !error;
}

export async function getInvitationCredits(): Promise<number> {
  const credits = await getSetting('invitation_credits');
  return credits ? parseInt(credits, 10) : parseInt(process.env.INVITATION_DEFAULT_CREDIT || '0', 10); // Default to 5 credits if not set
} 