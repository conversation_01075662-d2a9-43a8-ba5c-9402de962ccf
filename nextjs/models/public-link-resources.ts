import { getSupabaseClient } from "./db";
import { PostgrestError } from "@supabase/supabase-js";
import { PublicLinkResource, PublicLinkResourceWithStats } from "@/types/links";

// Additional interfaces for operations
export interface CreatePublicLinkResourceData {
  domain?: string; // Optional in interface, will be generated from website_url
  title: string;
  website_url: string;
  submission_method: string;
  submission_url?: string;
  contact_email?: string;
  is_paid: boolean;
  price_range?: string;
  currency?: string;
  category?: string;
  description?: string;
  requirements?: string;
  response_time?: string;
  success_rate?: number;
}

export interface UpdatePublicLinkResourceData {
  domain?: string;
  title?: string;
  website_url?: string;
  submission_method?: string;
  submission_url?: string;
  contact_email?: string;
  is_paid?: boolean;
  price_range?: string;
  currency?: string;
  category?: string;
  description?: string;
  requirements?: string;
  response_time?: string;
  success_rate?: number;
  is_active?: boolean;
}

export interface PublicLinkResourceFilters {
  category?: string;
  isPaid?: boolean;
  search?: string;
  isActive?: boolean;
}

export interface BulkInsertConfig {
  mode: 'create_only' | 'update_existing' | 'upsert';
  skip_duplicates?: boolean;
  selected_rows?: number[];
}

export interface BulkInsertResult {
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  skipped_duplicates: number;
  errors: Array<{
    row_number: number;
    error_message: string;
    raw_data: any;
    severity: string;
  }>;
  duration_ms: number;
  completed_at: string;
}

// ========================================
// PUBLIC LINK RESOURCES CRUD OPERATIONS
// ========================================

/**
 * Get public link resources with stats and filtering
 */
export async function getPublicLinkResourcesWithStats(
  filters: PublicLinkResourceFilters = {},
  sort: string = 'dr_desc',
  limit: number = 50,
  offset: number = 0
): Promise<{ data: PublicLinkResourceWithStats[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Build base query for public_link_resources
  let query = client
    .from('public_link_resources')
    .select('*')
    .eq('is_active', filters.isActive ?? true);

  // Apply filters
  if (filters.category) {
    query = query.eq('category', filters.category);
  }

  if (filters.isPaid !== undefined) {
    query = query.eq('is_paid', filters.isPaid);
  }

  if (filters.search) {
    query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,domain.ilike.%${filters.search}%`);
  }

  // Get public link resources first
  const { data: publicResources, error: resourcesError } = await query;

  if (resourcesError) {
    return { data: null, error: resourcesError };
  }

  if (!publicResources || publicResources.length === 0) {
    return { data: [], error: null };
  }

  // Get all domains from public resources
  const domains = publicResources.map(resource => resource.domain);

  // Fetch domain stats from all_links table
  const { data: domainStats } = await client
    .from('all_links')
    .select('*')
    .in('domain', domains);

  // Create lookup map for domain stats
  const statsMap = (domainStats || []).reduce((map, stats) => {
    map[stats.domain] = stats;
    return map;
  }, {} as Record<string, any>);

  // Merge data
  const resourcesWithStats: PublicLinkResourceWithStats[] = publicResources.map(resource => {
    const stats = statsMap[resource.domain];
    
    return {
      id: resource.id,
      domain: resource.domain,
      title: resource.title,
      website_url: resource.website_url,
      submission_method: resource.submission_method,
      submission_url: resource.submission_url,
      contact_email: resource.contact_email,
      is_paid: resource.is_paid,
      price_range: resource.price_range,
      currency: resource.currency,
      category: resource.category,
      description: resource.description,
      requirements: resource.requirements,
      response_time: resource.response_time,
      success_rate: resource.success_rate,
      last_verified: resource.last_verified,
      is_active: resource.is_active,
      created_at: resource.created_at,
      updated_at: resource.updated_at,
      dr_score: stats?.dr_score,
      traffic: stats?.traffic || 0,
      backlink_count: stats?.backlink_count || 0,
      is_indexed: stats?.is_indexed || false,
    };
  });

  // Apply sorting
  resourcesWithStats.sort((a, b) => {
    switch (sort) {
      case 'dr_desc':
        return (b.dr_score || 0) - (a.dr_score || 0);
      case 'dr_asc':
        return (a.dr_score || 0) - (b.dr_score || 0);
      case 'traffic_desc':
        return (b.traffic || 0) - (a.traffic || 0);
      case 'traffic_asc':
        return (a.traffic || 0) - (b.traffic || 0);
      case 'success_rate_desc':
        return (b.success_rate || 0) - (a.success_rate || 0);
      case 'created_desc':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      default:
        return (b.dr_score || 0) - (a.dr_score || 0);
    }
  });

  // Apply pagination
  const paginatedResources = resourcesWithStats.slice(offset, offset + limit);

  return { data: paginatedResources, error: null };
}

/**
 * Get public link resources for admin (with pagination)
 */
export async function getPublicLinkResourcesForAdmin(
  limit: number = 50,
  offset: number = 0
): Promise<{ data: any[] | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Get public link resources first
  const { data: publicResources, error } = await client
    .from('public_link_resources')
    .select('*')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error || !publicResources) {
    return { data: null, error };
  }

  // Get all domains from public resources
  const domains = publicResources.map(resource => resource.domain);

  // Fetch domain stats from all_links table
  const { data: domainStats } = await client
    .from('all_links')
    .select('*')
    .in('domain', domains);

  // Create lookup map for domain stats
  const statsMap = (domainStats || []).reduce((map, stats) => {
    map[stats.domain] = stats;
    return map;
  }, {} as Record<string, any>);

  // Merge data
  const data = publicResources.map(resource => {
    const stats = statsMap[resource.domain];
    
    return {
      ...resource,
      all_links: stats ? {
        dr_score: stats.dr_score,
        traffic: stats.traffic,
        backlink_count: stats.backlink_count,
        is_indexed: stats.is_indexed
      } : null
    };
  });

  return { data, error: null };
}

/**
 * Get single public link resource by ID
 */
export async function getPublicLinkResourceById(
  id: string
): Promise<{ data: any | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Get public link resource first
  const { data: publicResource, error } = await client
    .from('public_link_resources')
    .select('*')
    .eq('id', id)
    .single();

  if (error || !publicResource) {
    return { data: null, error };
  }

  // Fetch domain stats from all_links table
  const { data: domainStats } = await client
    .from('all_links')
    .select('*')
    .eq('domain', publicResource.domain)
    .single();

  // Merge data
  const data = {
    ...publicResource,
    all_links: domainStats ? {
      dr_score: domainStats.dr_score,
      traffic: domainStats.traffic,
      backlink_count: domainStats.backlink_count,
      is_indexed: domainStats.is_indexed
    } : null
  };

  return { data, error: null };
}

/**
 * Create new public link resource
 */
export async function createPublicLinkResource(
  data: CreatePublicLinkResourceData
): Promise<{ data: PublicLinkResource | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // Extract domain from URL for normalization
  const domain = new URL(data.website_url).hostname.replace(/^www\./, '');

  const { data: resource, error } = await client
    .from('public_link_resources')
    .insert([{
      ...data,
      domain,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }])
    .select()
    .single();

  if (error) {
    return { data: null, error };
  }

  // Ensure domain exists in all_links table
  await ensureDomainInAllLinks(domain);

  return { data: resource, error: null };
}

/**
 * Update public link resource
 */
export async function updatePublicLinkResource(
  id: string,
  data: UpdatePublicLinkResourceData
): Promise<{ data: PublicLinkResource | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  // If website_url is being updated, update domain as well
  if (data.website_url) {
    data.domain = new URL(data.website_url).hostname.replace(/^www\./, '');
  }

  const { data: resource, error } = await client
    .from('public_link_resources')
    .update({
      ...data,
      updated_at: new Date().toISOString(),
    })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { data: null, error };
  }

  // If domain was updated, ensure it exists in all_links table
  if (data.domain) {
    await ensureDomainInAllLinks(data.domain);
  }

  return { data: resource, error: null };
}

/**
 * Delete public link resource
 */
export async function deletePublicLinkResource(
  id: string
): Promise<{ success: boolean, error?: PostgrestError }> {
  const client = getSupabaseClient();

  const { error } = await client
    .from('public_link_resources')
    .delete()
    .eq('id', id);

  return { success: !error, error };
}

/**
 * Bulk insert public link resources
 */
export async function bulkInsertPublicLinkResources(
  data: any[],
  config: BulkInsertConfig
): Promise<BulkInsertResult> {
  const client = getSupabaseClient();
  const startTime = Date.now();

  // Filter selected rows if specified
  const selectedData = config.selected_rows?.length 
    ? data.filter(item => config.selected_rows!.includes(item._row_number))
    : data.filter(item => item._selected);

  let successful_imports = 0;
  let failed_imports = 0;
  let skipped_duplicates = 0;
  const errors: any[] = [];

  // Process data based on configuration mode
  for (const item of selectedData) {
    try {
      // Clean the data - remove client-side fields
      const cleanData = {
        domain: item.domain,
        title: item.title,
        website_url: item.website_url,
        submission_method: item.submission_method,
        submission_url: item.submission_url,
        contact_email: item.contact_email,
        is_paid: item.is_paid,
        price_range: item.price_range,
        currency: item.currency || 'USD',
        category: item.category,
        description: item.description,
        requirements: item.requirements,
        response_time: item.response_time,
        success_rate: item.success_rate
      };

      if (config.mode === 'create_only') {
        // Check for duplicates if skip_duplicates is enabled
        if (config.skip_duplicates) {
          const { data: existing } = await client
            .from('public_link_resources')
            .select('id')
            .eq('domain', cleanData.domain)
            .single();

          if (existing) {
            skipped_duplicates++;
            continue;
          }
        }

        const { error } = await client
          .from('public_link_resources')
          .insert(cleanData);

        if (error) {
          if (error.code === '23505') { // Unique constraint violation
            skipped_duplicates++;
          } else {
            failed_imports++;
            errors.push({
              row_number: item._row_number,
              error_message: error.message,
              raw_data: item,
              severity: 'error'
            });
          }
        } else {
          successful_imports++;
        }
      } else if (config.mode === 'update_existing') {
        const { error } = await client
          .from('public_link_resources')
          .update(cleanData)
          .eq('domain', cleanData.domain);

        if (error) {
          failed_imports++;
          errors.push({
            row_number: item._row_number,
            error_message: error.message,
            raw_data: item,
            severity: 'error'
          });
        } else {
          successful_imports++;
        }
      } else if (config.mode === 'upsert') {
        const { error } = await client
          .from('public_link_resources')
          .upsert(cleanData, { onConflict: 'domain' });

        if (error) {
          failed_imports++;
          errors.push({
            row_number: item._row_number,
            error_message: error.message,
            raw_data: item,
            severity: 'error'
          });
        } else {
          successful_imports++;
        }
      }
    } catch (error) {
      failed_imports++;
      errors.push({
        row_number: item._row_number,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        raw_data: item,
        severity: 'error'
      });
    }
  }

  return {
    total_processed: selectedData.length,
    successful_imports,
    failed_imports,
    skipped_duplicates,
    errors,
    duration_ms: Date.now() - startTime,
    completed_at: new Date().toISOString()
  };
}

// ========================================
// HELPER FUNCTIONS
// ========================================

/**
 * Ensure a domain exists in the all_links table
 */
async function ensureDomainInAllLinks(domain: string): Promise<void> {
  const client = getSupabaseClient();

  await client
    .from('all_links')
    .upsert([{
      domain,
      traffic: 0,
      backlink_count: 0,
      is_indexed: false,
      last_updated: new Date().toISOString(),
    }], {
      onConflict: 'domain',
      ignoreDuplicates: true,
    });
}