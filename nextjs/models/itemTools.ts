import { getSupabaseClient } from "@/models/db";
import { ItemTool, Tool } from '@/types/ItemTools';

/**
 * Fetch a list of all Item tools
 * @returns Array of Item tools
 */
export async function getItemToolsList() {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('item_tools')
    .select('*')
    .order('updated_at', { ascending: false });
    
  return { data, error };
}

/**
 * Fetch tools for a specific Item by UUID
 * @param uuid - The UUID of the Item
 * @returns Item tools data or null if not found
 */
export async function getItemToolsByUuid(uuid: string) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('item_tools')
    .select('*')
    .eq('uuid', uuid)
    .single();
    
  return { data, error };
}

/**
 * Create or update Item tools
 * @param itemTools - The Item tools data to save
 * @returns The saved Item tools data
 */
export async function saveItemTools(itemTools: ItemTool) {
  const supabase = getSupabaseClient();
  
  // Update the updated_at timestamp
  itemTools.updated_at = new Date().toISOString();
  
  const { data, error } = await supabase
    .from('item_tools')
    .upsert(itemTools, { onConflict: 'uuid' })
    .select()
    .single();
    
  return { data, error };
}

/**
 * Delete Item tools
 * @param uuid - The UUID of the Item
 * @returns Success status
 */
export async function deleteItemTools(uuid: string) {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from('item_tools')
    .delete()
    .eq('uuid', uuid);
    
  return { success: !error, error };
}

/**
 * Update a specific tool within Item tools
 * @param itemUuid - The UUID of the Item
 * @param toolName - The name of the tool to update
 * @param toolData - The updated tool data
 * @returns The updated Item tools data
 */
export async function updateItemTool(itemUuid: string, toolName: string, toolData: Tool) {
  // First get the current Item tools
  const { data: itemTools, error: fetchError } = await getItemToolsByUuid(itemUuid);
  
  if (fetchError || !itemTools) {
    return { data: null, error: fetchError || new Error('Item tools not found') };
  }
  
  // Find and update the specific tool
  const updatedTools = itemTools.tools.map(tool => 
    tool.name === toolName ? { ...tool, ...toolData } : tool
  );
  
  // If tool wasn't found, add it
  if (!itemTools.tools.some(tool => tool.name === toolName)) {
    updatedTools.push(toolData);
  }
  
  // Update the Item tools with the modified tools array
  const updatedItemTools = {
    ...itemTools,
    tools: updatedTools,
    updated_at: new Date().toISOString()
  };
  
  // Save the updated Item tools
  return await saveItemTools(updatedItemTools);
} 