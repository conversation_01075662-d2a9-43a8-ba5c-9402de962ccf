import { getSupabaseClient } from './db'; // Use Supabase client
import { PostgrestError } from '@supabase/supabase-js';
import {
  AiTask,
  CreateAiTaskInput,
  UpdateAiTaskInput,
  AiTaskStatus,
  AiTaskCallbackPayload,
} from '../types/aiTask';
import { v4 as uuidv4 } from 'uuid'; // For generating order_no

// Define a standard response structure for model functions
interface ModelResponse<T> {
  data: T | null;
  error: PostgrestError | Error | null; // Use PostgrestError for Supabase errors
}

/**
 * Creates a new AI task record in the database using Supabase.
 * @param taskData - The data for the new task.
 * @returns ModelResponse containing the created AiTask object or an error (including 'INSUFFICIENT_CREDITS').
 */
export async function createAiTask(
  taskData: Omit<CreateAiTaskInput, 'order_no'> & { user_uuid: string }
): Promise<ModelResponse<AiTask>> {
  const client = getSupabaseClient();
  try {
    // Prepare parameters for the RPC call
    const params = {
      p_user_uuid: taskData.user_uuid,
      p_product_name: taskData.product_name,
      p_credit_cost: taskData.credit_cost,
      p_input_file_path: taskData.input_file_path,
      p_output_options: taskData.output_options || {}, // Ensure JSONB is not null
      p_callback_url: taskData.callback_url || null // Pass null if undefined
    };

    // Call the RPC function
    const { data: newTask, error } = await client.rpc(
        'create_ai_task_with_credit_deduction',
        params
    );

    if (error) {
      console.error('Error calling create_ai_task_with_credit_deduction RPC:', error);
      // Check if the error is the specific insufficient credits error
      if (error.message.includes('INSUFFICIENT_CREDITS')) {
          // Return a specific error structure or message that the API layer can check
          return { data: null, error: new Error('INSUFFICIENT_CREDITS') };
      }
      // Return the original PostgrestError for other DB errors
      return { data: null, error };
    }

    // The RPC function returns the created row directly
    return { data: newTask as AiTask, error: null };

  } catch (error: any) {
    // Catch unexpected errors during RPC call setup or execution
    console.error('Unexpected error in createAiTask:', error);
    return { data: null, error: error instanceof Error ? error : new Error('Failed to create AI task via RPC.') };
  }
}

/**
 * Retrieves an AI task by its unique order number using Supabase.
 * @param order_no - The order number of the task.
 * @returns ModelResponse containing the AiTask object or null/error.
 */
export async function getAiTaskByOrderNo(order_no: string): Promise<ModelResponse<AiTask>> {
  const client = getSupabaseClient();
  try {
    const { data: task, error } = await client
      .from('ai_tasks')
      .select('*')
      .eq('order_no', order_no)
      .maybeSingle(); // Use maybeSingle to return null if not found

    if (error) {
      console.error(`Error fetching AI task ${order_no}:`, error);
      return { data: null, error };
    }
    return { data: task as AiTask | null, error: null };
  } catch (error: any) {
    console.error(`Unexpected error fetching AI task ${order_no}:`, error);
    return { data: null, error: error instanceof Error ? error : new Error('Failed to fetch AI task.') };
  }
}

/**
 * Retrieves AI tasks for a specific user, with pagination using Supabase.
 * @param user_uuid - The UUID of the user.
 * @param page - The page number (1-based).
 * @param limit - The number of tasks per page.
 * @returns ModelResponse containing the list of tasks and total count, or an error.
 */
export async function getAiTasksByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 10
): Promise<ModelResponse<{ tasks: AiTask[]; total: number }>> {
  const client = getSupabaseClient();
  try {
    const offset = (page - 1) * limit;
    const { data: tasks, error, count } = await client
      .from('ai_tasks')
      .select('*', { count: 'exact' }) // Fetch count along with data
      .eq('user_uuid', user_uuid)
      .order('create_time', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error(`Error fetching AI tasks for user ${user_uuid}:`, error);
      return { data: null, error };
    }
    return { data: { tasks: tasks as AiTask[], total: count ?? 0 }, error: null };
  } catch (error: any) {
    console.error(`Unexpected error fetching AI tasks for user ${user_uuid}:`, error);
    return { data: null, error: error instanceof Error ? error : new Error('Failed to fetch user AI tasks.') };
  }
}

/**
 * Updates an AI task based on its order number using Supabase.
 * @param order_no - The order number of the task to update.
 * @param updateData - The data to update.
 * @returns ModelResponse containing the updated AiTask object or null/error.
 */
export async function updateAiTaskByOrderNo(
  order_no: string,
  updateData: UpdateAiTaskInput
): Promise<ModelResponse<AiTask>> {
  const client = getSupabaseClient();
  try {
    let finalUpdateData = { ...updateData };

    // Calculate cost_time if status is changing to SUCCEED or FAILED
    if (updateData.orderstatus && ['SUCCEED', 'FAILED'].includes(updateData.orderstatus)) {
        const taskResult = await getAiTaskByOrderNo(order_no); // Fetch using the same model function
        if (taskResult.data && taskResult.data.create_time) {
            const costTime = Math.round((new Date().getTime() - new Date(taskResult.data.create_time).getTime()) / 1000);
            finalUpdateData.cost_time = costTime;
        } else if (taskResult.error) {
            // Propagate error from fetching task if needed
            // return { data: null, error: taskResult.error };
            // Or just proceed, update might fail if task doesn't exist
        }
    }

    const { data: updatedTask, error } = await client
      .from('ai_tasks')
      .update({
        ...finalUpdateData,
        update_time: new Date().toISOString(), // Manually set update_time if trigger isn't used/reliable
        output_options: updateData.output_options || undefined, // Pass undefined if not updating
      })
      .eq('order_no', order_no)
      .select()
      .single(); // Expecting a single row back

    if (error) {
        // Supabase might return an error if the row doesn't exist (check error code if needed)
        console.error(`Error updating AI task ${order_no}:`, error);
        return { data: null, error };
    }
    // Check if data is null which means the record was not found
    if (!updatedTask) {
        return { data: null, error: new Error(`AI Task with order_no ${order_no} not found for update.`) };
    }

    return { data: updatedTask as AiTask, error: null };
  } catch (error: any) {
    console.error(`Unexpected error updating AI task ${order_no}:`, error);
    return { data: null, error: error instanceof Error ? error : new Error('Failed to update AI task.') };
  }
}

/**
 * Updates an AI task based on callback information using Supabase.
 * @param payload - The callback payload.
 * @returns ModelResponse containing the updated AiTask object or null/error.
 */
export async function handleAiTaskCallback(
  payload: AiTaskCallbackPayload
): Promise<ModelResponse<AiTask>> {
    const { order_no, orderstatus, ...rest } = payload;
    const updateData: UpdateAiTaskInput = { orderstatus, ...rest };

    // Calculate cost_time based on create_time if provided in payload or fetched
    if (!updateData.cost_time) {
        const taskResult = await getAiTaskByOrderNo(order_no);
        if (taskResult.data && taskResult.data.create_time) {
            updateData.cost_time = Math.round((new Date().getTime() - new Date(taskResult.data.create_time).getTime()) / 1000);
        } else {
            // Task not found or error fetching, proceed without cost_time or handle error
            updateData.cost_time = 0; // Default if create_time is unavailable
        }
    }

    // Use the generic update function
    return updateAiTaskByOrderNo(order_no, updateData);
}


/**
 * Deletes an AI task by its order number using Supabase.
 * @param order_no - The order number of the task to delete.
 * @returns ModelResponse containing the deleted AiTask object or null/error.
 */
export async function deleteAiTaskByOrderNo(order_no: string): Promise<ModelResponse<AiTask>> {
  const client = getSupabaseClient();
  try {
    // Supabase delete doesn't typically return the deleted row by default unless specified
    // We might need to fetch it first if we need to return it, or adjust the return type
    const { data: deletedData, error } = await client
      .from('ai_tasks')
      .delete()
      .eq('order_no', order_no)
      .select() // Try to select the deleted row (might depend on RLS/permissions)
      .single();

    if (error) {
      console.error(`Error deleting AI task ${order_no}:`, error);
      return { data: null, error };
    }
    // Check if deletedData is null (meaning not found or delete didn't return data)
    if (!deletedData) {
        // Check if the task existed before attempting delete to differentiate errors
        const check = await getAiTaskByOrderNo(order_no);
        if (!check.data && !check.error) {
             return { data: null, error: new Error(`AI Task with order_no ${order_no} not found.`) };
        }
        // If it existed but delete didn't return data, return success but null data
        // Or adjust return type to just { error: PostgrestError | null } for delete
        return { data: null, error: null }; // Indicate success but no data returned
    }

    return { data: deletedData as AiTask, error: null };
  } catch (error: any) {
    console.error(`Unexpected error deleting AI task ${order_no}:`, error);
    return { data: null, error: error instanceof Error ? error : new Error('Failed to delete AI task.') };
  }
}

// Get all AI tasks for the current user
export async function getUserAiTasksByUUID(user_uuid: string, options?: { limit?: number; offset?: number }) {
  try {
    const client = getSupabaseClient();

    
    let query = client
      .from('ai_tasks')
      .select('*')
      .eq('user_uuid', user_uuid)
      .order('create_time', { ascending: false });
    
    if (options?.limit) {
      query = query.limit(options.limit);
    }
    
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching user AI tasks:', error);
      return { data: null, error };
    }
    
    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching user AI tasks:', error);
    return { 
      data: null,
      error: error instanceof Error ? error : new Error('An unexpected error occurred') 
    };
  }
}

// Get all AI tasks (admin only)
export async function getAllAiTasks(options?: { 
  limit?: number; 
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  status?: string;
  userId?: string;
}) {
  try {
    const client = getSupabaseClient();
    // This should be used only in admin routes that verify admin permissions
    
    let query = client
      .from('ai_tasks')
      .select('*, users:user_uuid(email)');
    
    // Apply filters
    if (options?.status) {
      query = query.eq('orderstatus', options.status);
    }
    
    if (options?.userId) {
      query = query.eq('user_uuid', options.userId);
    }
    
    // Apply ordering
    const orderBy = options?.orderBy || 'create_time';
    const orderDirection = options?.orderDirection || 'desc';
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });
    
    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit);
    }
    
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching all AI tasks:', error);
      return { data: null, error };
    }
    
    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching all AI tasks:', error);
    return { 
      data: null,
      error: error instanceof Error ? error : new Error('An unexpected error occurred') 
    };
  }
}