import { Submission, Items } from "@/types/items";
import { getSupabaseClient } from "@/models/db";
import { fetchGithubRepoInfo } from "@/lib/github";
import { getItemsByWebsiteUrl } from "@/models/items"; // Import addItems to use on approval
import { PostgrestError } from "@supabase/supabase-js";
import { updateItems } from "@/models/items";
import { normalizeUrl } from "@/utils/url-normalization";

// ========================================
// SUBMISSION EMAIL OPERATIONS
// ========================================

export async function getSubmissionEmailByItem(
  itemName: string,
  authorName: string
): Promise<{ data: { email: string } | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();

  const { data, error } = await client
    .from("submissions")
    .select("email")
    .eq("name", itemName)
    .eq("author_name", authorName)
    .not("email", "is", null)
    .single();

  return { data, error };
}

// SUBMISSION CRUD
// getSubmissions(limit, page, status)
// MUST: require admin permission or token
export async function getSubmissions(
    limit: number = 50,
    page: number = 1,
    status?: string | null
): Promise<{ data: Submission[] | null, error: PostgrestError | Error | null, count: number | null }> {
    const client = getSupabaseClient();
    const offset = (page - 1) * limit;

    // Start building the query
    let query = client
        .from("submissions")
        .select("*", { count: 'exact' })
        .order("created_at", { ascending: false });

    // Apply status filter if provided
    if (status) {
        query = query.eq("status", status);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
        console.error("Error fetching Item Submissions:", error);
    }

    return { data, error, count };
}

// getUserSubmissionsByEmail(email, limit, page)
export async function getUserSubmissionsByEmail(email: string, limit: number = 50, page: number = 1): Promise<{ data: Submission[] | null, error: PostgrestError | Error | null, count: number | null }> {
    if (!email) {
        return { data: null, error: new Error("User email is required"), count: 0 };
    }

    const client = getSupabaseClient();
    const offset = (page - 1) * limit;
    const safeLimit = Math.max(1, Math.min(limit, 100));

    const { data, error, count } = await client
        .from("submissions")
        .select("*", { count: 'exact' })
        .eq("email", email) // Filter by email
        .order("created_at", { ascending: false })
        .range(offset, offset + safeLimit - 1);

    if (error) {
        console.error(`Error fetching submissions for email ${email}:`, error);
    }

    return { data, error, count };
}

// submitItems(Submission) -> Renamed to userSubmitItems for clarity
// Public access
export async function userSubmitItems(submissionData: Partial<Submission>): Promise<{ data: Submission | null, error: PostgrestError | null }> {
    const client = getSupabaseClient();

    // Ensure required fields
    if (!submissionData.name || !submissionData.author_name || !submissionData.website_url) {
        return { data: null, error: { message: "Missing required fields: name, author_name, website_url", details: "", hint: "", code: "400" } as PostgrestError };
    }

    // Normalize website URL for consistency
    const normalizedWebsiteUrl = normalizeUrl(submissionData.website_url!).normalized || submissionData.website_url;

    const { data, error } = await client
        .from("submissions")
        .insert({
            name: submissionData.name,
            author_name: submissionData.author_name,
            website_url: normalizedWebsiteUrl,
            item_avatar_url: submissionData.item_avatar_url || null,
            user_avatar_url: submissionData.user_avatar_url || null,
            email: submissionData.email || null,
            subscribe_newsletter: submissionData.subscribe_newsletter === undefined ? true : submissionData.subscribe_newsletter,
            detail: submissionData.detail || null,
            preprocessinfo: submissionData.preprocessinfo || {},
            status: 'pending', // Default status
            created_at: new Date().toISOString(),
        })
        .select()
        .single();

    if (error) {
        console.error("Error submitting Item:", error);
    }

    return { data, error };
}

// prefetchSubmissionInfo(github_url: string)
// Fetches GitHub info and structures it somewhat like Submission for preview
// Does not save to DB, just returns the info.
export async function prefetchSubmissionInfo(github_url: string): Promise<Partial<Submission> & { error?: string }> {
    const { metadata, readme, error: fetchError } = await fetchGithubRepoInfo(github_url);

    if (fetchError || !metadata) {
        return { error: fetchError || "Failed to fetch repository info." };
    }

    // Basic AI processing placeholder - just extracting brief from description for now
    // In a real scenario, you might call an AI service here to summarize readme, extract tags etc.
    const brief = metadata.description || "No description provided.";
    const tags: string[] = []; // Placeholder for AI-extracted tags

    // Construct a partial Submission object
    const preprocessinfo = {
        brief: brief,
        tags: tags,
        metadata: { // Store raw metadata fetched
            forks: metadata.forks,
            stars: metadata.stars,
            watchers: metadata.watchers,
            updated_at: metadata.updated_at,
        }
    };

    const submissionPreview: Partial<Submission> = {
        name: metadata.repo,
        author_name: metadata.owner,
        website_url: github_url,
        user_avatar_url: metadata.user_avatar_url || `https://github.com/${metadata.owner}.png`, // Use GitHub user avatar or fallback
        detail: readme || "", // Use README content as detail
        preprocessinfo: preprocessinfo as any, // Cast needed as Supabase types might expect specific JSONB structure
    };

    return submissionPreview;
}


// approveSubmission(id)
// MUST: require admin permission or token
// NOTICE: only update status in item_submission, update allow_public in item and send email(if enable), do not update others
export async function approveSubmission(id: number): Promise<{ data: Items | null, error: PostgrestError | Error | null }> {
    const client = getSupabaseClient();

    // 1. Update submission status
    const { error: updateError } = await client
        .from("submissions")
        .update({
            status: 'approved',
            approved_at: new Date().toISOString(),
            processed_at: new Date().toISOString(), // Mark as processed
        })
        .eq("id", id);

    if (updateError) {
        console.error("Error updating submission status to approved:", updateError);
        // Log error but proceed, as the Item was added
    }

    // 2. Fetch the items from items table
    const { data: itemSubmission, error: fetchSubmissionError } = await client
       .from("submissions")
       .select("*")
       .eq("id", id)
       .single();
    if (fetchSubmissionError ||!itemSubmission) {
        console.error("Error fetching Item submission for approval:", fetchSubmissionError);
        return { data: null, error: fetchSubmissionError || new Error("Submission not found") };
    }

    // 3. update items to allow_public = true
    const { error: updateItemError } = await updateItems({
        allow_public: true,
        website_url: itemSubmission.website_url
    });
    if (updateItemError) {
        console.error("Error updating Item to allow_public:", updateItemError);
    }

    const { data: newItem, error: fetchError } = await getItemsByWebsiteUrl(itemSubmission.website_url);
    if (fetchError ||!newItem) {
        console.error("Error fetching Item for approval:", fetchError);
        return { data: null, error: fetchError || new Error("Item not found") };
    }

    return { data: newItem, error: null }; // Return the newly approved Item
}

// rejectSubmission(id) - Optional but good practice
// MUST: require admin permission or token
export async function rejectSubmission(id: number): Promise<{ error: PostgrestError | Error | null }> {
    const client = getSupabaseClient();

     // Fetch submission to check status
    const { data: submission, error: fetchError } = await client
        .from("submissions")
        .select("status")
        .eq("id", id)
        .single();

    if (fetchError || !submission) {
        console.error("Error fetching submission for rejection:", fetchError);
        return { error: fetchError || new Error("Submission not found") };
    }

    // Update status to rejected
    const { error } = await client
        .from("submissions")
        .update({
            status: 'rejected',
            rejected_at: new Date().toISOString(),
            processed_at: new Date().toISOString(), // Mark as processed
        })
        .eq("id", id);

    if (error) {
        console.error("Error updating submission status to rejected:", error);
    }

    return { error };
}

// updateSubmissionStatus - Update status of a submission to pending or processed
// MUST: require admin permission or token
export async function updateSubmissionStatus(id: number, status: 'pending' | 'processed' | 'approved' | 'rejected'): Promise<{ error: PostgrestError | Error | null }> {
    const client = getSupabaseClient();

    // Validate status
    if (status !== 'pending' && status !== 'processed' && status !== 'approved' && status !== 'rejected') {
        return { error: new Error("Status must be 'pending' or 'processed' or 'approved' or 'rejected'") };
    }

    // Prepare update data
    const updateData: any = {
        status: status
    };

    // Reset timestamps based on the status
    if (status === 'pending') {
        updateData.processed_at = null;
        updateData.approved_at = null;
        updateData.rejected_at = null;
    } else if (status === 'processed') {
        updateData.processed_at = new Date().toISOString();
    } else if (status === 'approved') {
        updateData.approved_at = new Date().toISOString();
    } else if (status === 'rejected') {
        updateData.rejected_at = new Date().toISOString();
    }

    // Update the submission
    const { error } = await client
        .from("submissions")
        .update(updateData)
        .eq("id", id);

    if (error) {
        console.error(`Error updating submission ${id} to ${status}:`, error);
    }

    return { error };
}

// getSubmissionStatusCounts - Get counts of submissions by status
// MUST: require admin permission or token
export async function getSubmissionStatusCounts(): Promise<{
    all: number,
    pending: number,
    processed: number,
    approved: number,
    rejected: number,
    error: PostgrestError | Error | null
}> {
    const client = getSupabaseClient();

    // Initialize result object
    const result = {
        all: 0,
        pending: 0,
        processed: 0,
        approved: 0,
        rejected: 0,
        error: null as PostgrestError | Error | null
    };

    try {
        // Get total count
        const { count: allCount, error: allError } = await client
            .from("submissions")
            .select("*", { count: 'exact', head: true });

        if (allError) throw allError;
        result.all = allCount || 0;

        // Get pending count
        const { count: pendingCount, error: pendingError } = await client
            .from("submissions")
            .select("*", { count: 'exact', head: true })
            .eq("status", "pending");

        if (pendingError) throw pendingError;
        result.pending = pendingCount || 0;

        // Get processed count
        const { count: processedCount, error: processedError } = await client
            .from("submissions")
            .select("*", { count: 'exact', head: true })
            .eq("status", "processed");

        if (processedError) throw processedError;
        result.processed = processedCount || 0;

        // Get approved count
        const { count: approvedCount, error: approvedError } = await client
            .from("submissions")
            .select("*", { count: 'exact', head: true })
            .eq("status", "approved");

        if (approvedError) throw approvedError;
        result.approved = approvedCount || 0;

        // Get rejected count
        const { count: rejectedCount, error: rejectedError } = await client
            .from("submissions")
            .select("*", { count: 'exact', head: true })
            .eq("status", "rejected");

        if (rejectedError) throw rejectedError;
        result.rejected = rejectedCount || 0;

    } catch (error) {
        console.error("Error fetching Item submission counts:", error);
        result.error = error as PostgrestError | Error;
    }

    return result;
}

// 通过 website_url 查找 item_submission
export async function findSubmissionByWebsiteUrl(website_url: string): Promise<Submission | null> {
  const client = getSupabaseClient();
  const { data, error } = await client
    .from("submissions")
    .select("*")
    .eq("website_url", website_url)
    .single();
  if (error || !data) return null;
  return data as Submission;
}

// Get submission by ID
export async function getSubmissionById(id: number): Promise<{ data: Submission | null, error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  return await client
    .from("submissions")
    .select("*")
    .eq("id", id)
    .single();
}

// Update submission with preprocessinfo
export async function updateSubmissionPreprocessInfo(
  id: number, 
  preprocessinfo: any
): Promise<{ error: PostgrestError | null }> {
  const client = getSupabaseClient();
  
  const { error } = await client
    .from("submissions")
    .update({ preprocessinfo })
    .eq("id", id);
    
  return { error };
}

// Update submission status and preprocessinfo
export async function updateSubmissionStatusAndPreprocess(
  id: number,
  status: 'pending' | 'processed' | 'approved' | 'rejected',
  preprocessinfo?: any
): Promise<{ error: PostgrestError | Error | null }> {
  const client = getSupabaseClient();

  // First update the status
  const statusUpdateResult = await updateSubmissionStatus(id, status);
  
  if (statusUpdateResult.error) {
    return statusUpdateResult;
  }
  
  // Then update preprocessinfo if provided
  if (preprocessinfo) {
    const { error: preprocessError } = await client
      .from("submissions")
      .update({ preprocessinfo })
      .eq("id", id);

    if (preprocessError) {
      return { error: preprocessError };
    }
  }
  
  return { error: null };
}
