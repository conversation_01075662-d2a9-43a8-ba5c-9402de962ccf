import { getSupabaseClient } from "@/models/db";
import { cookies } from 'next/headers';
import { PostgrestError } from '@supabase/supabase-js';

export interface UserItem {
  uuid: string;
  user_uuid: string;
  item_uuid: string;
  description?: string;
  created_at: string;
  updated_at: string;
  item_name?: string;
  item_brief?: string;
  github_url?: string;
  website_url?: string;
  item_avatar_url?: string;
}

export async function getUserItems(user_uuid: string): Promise<{ data: any[], error: PostgrestError | Error | null }> {
  if (!user_uuid) {
    return { data: [], error: new Error("user_uuid is required") };
  }

  const client = getSupabaseClient();

  try {
    const { data, error } = await client
      .from("user_liked_items")
      .select(`
        *,
        items (
          uuid,
          name,
          brief,
          website_url,
          item_avatar_url
        )
      `)
      .eq("user_uuid", user_uuid);

    if (error) {
      console.error("Error fetching user Items:", error);
      return { data: [], error };
    }

    // Process data to include Item details in each user Item
    const processedData = data.map(item => ({
      ...item,
      item_uuid: item.item_uuid,
      item_name: item.items?.name,
      item_brief: item.items?.brief,
      website_url: item.items?.website_url,
      item_avatar_url: item.items?.item_avatar_url
    }));

    return { data: processedData, error: null };
  } catch (error) {
    console.error("Exception fetching user Items:", error);
    return { data: [], error: error as Error };
  }
}

export async function addUserItem(user_uuid: string, item_uuid: string, description?: string) {
  if (!user_uuid) {
    return { data: null, error: new Error("User UUID is required") };
  }

  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('user_liked_items')
    .insert([{ 
      item_uuid, 
      user_uuid,
      description 
    }])
    .select();

  return { data, error };
}

export async function deleteUserItem(user_uuid: string, uuid: string) {
  if (!user_uuid) {
    return { data: null, error: new Error("User UUID is required") };
  }

  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('user_liked_items')
    .delete()
    .eq('uuid', uuid)
    .eq('user_uuid', user_uuid);

  return { data, error };
}

export async function updateUserItem(user_uuid: string, uuid: string, description: string) {
  if (!user_uuid) {
    return { data: null, error: new Error("User UUID is required") };
  }

  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('user_liked_items')
    .update({ description })
    .eq('uuid', uuid)
    .eq('user_uuid', user_uuid)
    .select();

  return { data, error };
}

export async function getUserItemByItemUuid(user_uuid: string, item_uuid: string) {
  if (!user_uuid) {
    return { data: null, error: new Error("User UUID is required") };
  }

  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('user_liked_items')
    .select()
    .eq('item_uuid', item_uuid)
    .eq('user_uuid', user_uuid)
    .single();

  return { data, error };
} 