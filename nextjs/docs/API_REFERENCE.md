# MyBacklinks.app API Reference

## Overview

The MyBacklinks.app API provides comprehensive access to link tracking, analytics, and tier management functionality. All endpoints are protected by tier-based access control and rate limiting.

## Authentication

### Session Authentication (Web)
```typescript
// Automatic with NextAuth.js
// Session cookie: next-auth.session-token
```

### API Key Authentication
```bash
# Header-based authentication
curl -H "X-API-Key: your-api-key" \
  https://api.mybacklinks.app/api/projects
```

### OAuth 2.0 (Future)
```bash
# Bearer token authentication
curl -H "Authorization: Bearer your-oauth-token" \
  https://api.mybacklinks.app/api/projects
```

## Base URL

**Production:** `https://mybacklinks.app/api`
**Development:** `http://localhost:3000/api`

## Common Headers

```http
Content-Type: application/json
X-API-Key: your-api-key (if using API key auth)
User-Agent: MyBacklinks-Client/1.0
```

## Rate Limits

| Endpoint Type | Limit | Window |
|---------------|-------|---------|
| General API | 100 requests | 15 minutes |
| Tier Operations | 30 requests | 1 minute |
| DR Queries | 10 requests | 1 minute |
| Traffic Updates | 20 requests | 1 minute |
| Auth Endpoints | 5 requests | 15 minutes |

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 400 | Bad Request | Invalid request parameters |
| 401 | Authentication Required | Not authenticated |
| 403 | Plan Limit Reached | Tier limit exceeded |
| 429 | Rate Limit Exceeded | Too many requests |
| 500 | Internal Server Error | Server error |

## User & Tier Management

### Get User Tier Information
```http
GET /api/user/tier-info
```

**Response:**
```json
{
  "tierInfo": {
    "tier": "free",
    "subscription_status": "active",
    "limits": {
      "projects": {
        "used": 3,
        "limit": 5,
        "canCreate": true
      },
      "domains": {
        "used": 7,
        "limit": 10,
        "canAdd": true
      },
      "link_resources": {
        "used": 450,
        "limit": 1000,
        "canAdd": true
      },
      "monthly_dr_queries": {
        "used": 0,
        "limit": 0,
        "canUse": false
      },
      "monthly_traffic_updates": {
        "used": 0,
        "limit": 0,
        "canUse": false
      }
    },
    "usage_reset_date": "2024-08-01T00:00:00Z"
  }
}
```

**Tier Validation:**
- ✅ Free tier: Included
- ✅ Professional tier: Included

---

## Projects API

### List Projects
```http
GET /api/projects
```

**Query Parameters:**
- `limit` (optional): Number of projects to return (default: 50)
- `offset` (optional): Number of projects to skip (default: 0)
- `search` (optional): Search term for project names

**Response:**
```json
{
  "projects": [
    {
      "id": "proj_123",
      "name": "My Website",
      "domain": "example.com",
      "user_id": "user_456",
      "total_links": 25,
      "indexed_links": 18,
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-20T14:30:00Z"
    }
  ],
  "total": 1,
  "has_more": false
}
```

**Tier Validation:**
- ✅ Free tier: Up to 5 projects
- ✅ Professional tier: Up to 1,000 projects

### Create Project
```http
POST /api/projects
```

**Request Body:**
```json
{
  "name": "My New Website",
  "domain": "newsite.com",
  "description": "A new website project"
}
```

**Response:**
```json
{
  "project": {
    "id": "proj_789",
    "name": "My New Website",
    "domain": "newsite.com",
    "user_id": "user_456",
    "total_links": 0,
    "indexed_links": 0,
    "created_at": "2024-01-25T09:00:00Z",
    "updated_at": "2024-01-25T09:00:00Z"
  },
  "usage": {
    "tier": "free",
    "limits": {
      "projects": { "used": 4, "limit": 5 }
    }
  }
}
```

**Tier Validation:**
- ✅ Free tier: Up to 5 projects
- ✅ Professional tier: Up to 1,000 projects

**Error Response:**
```json
{
  "error": "Plan limit reached. Upgrade to continue.",
  "code": "TIER_LIMIT_EXCEEDED",
  "details": {
    "currentUsage": 5,
    "limit": 5
  }
}
```

### Get Project Details
```http
GET /api/projects/{project_id}
```

**Response:**
```json
{
  "project": {
    "id": "proj_123",
    "name": "My Website",
    "domain": "example.com",
    "user_id": "user_456",
    "total_links": 25,
    "indexed_links": 18,
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-20T14:30:00Z"
  },
  "analytics": {
    "total_traffic": 12500,
    "avg_dr_score": 45,
    "top_pages": [
      {
        "url": "https://example.com/blog/post-1",
        "traffic": 1200,
        "dr_score": 52
      }
    ]
  }
}
```

### Update Project
```http
PUT /api/projects/{project_id}
```

**Request Body:**
```json
{
  "name": "Updated Website Name",
  "description": "Updated description"
}
```

### Delete Project
```http
DELETE /api/projects/{project_id}
```

**Response:**
```json
{
  "success": true,
  "message": "Project deleted successfully"
}
```

---

## Links API

### List Links
```http
GET /api/links
```

**Query Parameters:**
- `project_id` (optional): Filter by project ID
- `domain` (optional): Filter by domain
- `status` (optional): Filter by status (active, pending, inactive)
- `is_indexed` (optional): Filter by indexed status (true/false)
- `limit` (optional): Number of links to return (default: 50)
- `offset` (optional): Number of links to skip (default: 0)

**Response:**
```json
{
  "links": [
    {
      "id": "link_123",
      "url": "https://example.com/page1",
      "domain": "example.com",
      "project_id": "proj_456",
      "user_id": "user_789",
      "title": "Example Page",
      "description": "An example page",
      "dr_score": 45,
      "traffic": 1200,
      "is_indexed": true,
      "status": "active",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-20T14:30:00Z"
    }
  ],
  "total": 1,
  "has_more": false
}
```

**Tier Validation:**
- ✅ Free tier: Up to 1,000 link resources
- ✅ Professional tier: Unlimited link resources

### Create Link
```http
POST /api/links
```

**Request Body:**
```json
{
  "url": "https://example.com/new-page",
  "project_id": "proj_456",
  "title": "New Page",
  "description": "A new page description",
  "traffic": 500
}
```

**Response:**
```json
{
  "link": {
    "id": "link_789",
    "url": "https://example.com/new-page",
    "domain": "example.com",
    "project_id": "proj_456",
    "user_id": "user_789",
    "title": "New Page",
    "description": "A new page description",
    "dr_score": null,
    "traffic": 500,
    "is_indexed": false,
    "status": "pending",
    "created_at": "2024-01-25T09:00:00Z",
    "updated_at": "2024-01-25T09:00:00Z"
  },
  "usage": {
    "tier": "free",
    "limits": {
      "link_resources": { "used": 451, "limit": 1000 }
    }
  }
}
```

**Tier Validation:**
- ✅ Free tier: Up to 1,000 link resources
- ✅ Professional tier: Unlimited link resources

### Get Link Details
```http
GET /api/links/{link_id}
```

### Update Link
```http
PUT /api/links/{link_id}
```

**Request Body:**
```json
{
  "title": "Updated Page Title",
  "description": "Updated description",
  "traffic": 600
}
```

### Delete Link
```http
DELETE /api/links/{link_id}
```

---

## Analytics API

### Trigger DR Update
```http
POST /api/cron-worker/trigger/dr
```

**Request Body:**
```json
{
  "project_id": "proj_123",
  "batch_size": 100
}
```

**Response:**
```json
{
  "success": true,
  "message": "DR update triggered successfully",
  "data": {
    "job_id": "job_456",
    "processed_links": 25,
    "estimated_completion": "2024-01-25T10:05:00Z"
  },
  "usage": {
    "tier": "paid",
    "limits": {
      "monthly_dr_queries": { "used": 15, "limit": 100 }
    }
  }
}
```

**Tier Validation:**
- ❌ Free tier: Not available
- ✅ Professional tier: 100 queries/month

**Error Response:**
```json
{
  "error": "DR queries are not available for free users. Please upgrade to Professional plan.",
  "code": "TIER_LIMIT_EXCEEDED",
  "details": {
    "currentUsage": 0,
    "limit": 0
  }
}
```

### Trigger Traffic Update
```http
POST /api/cron-worker/trigger/traffic
```

**Request Body:**
```json
{
  "project_id": "proj_123",
  "source": "google_analytics"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Traffic update triggered successfully",
  "data": {
    "job_id": "job_789",
    "processed_links": 30,
    "total_traffic": 15000
  },
  "usage": {
    "tier": "paid",
    "limits": {
      "monthly_traffic_updates": { "used": 8, "limit": 100 }
    }
  }
}
```

**Tier Validation:**
- ❌ Free tier: Not available
- ✅ Professional tier: 100 updates/month

### Get Domain Stats
```http
GET /api/domain-stats/{domain}
```

**Response:**
```json
{
  "domain": "example.com",
  "dr_score": 45,
  "traffic": 12500,
  "is_indexed": true,
  "last_updated": "2024-01-20T14:30:00Z",
  "history": [
    {
      "date": "2024-01-20",
      "dr_score": 45,
      "traffic": 12500
    },
    {
      "date": "2024-01-19",
      "dr_score": 44,
      "traffic": 11800
    }
  ]
}
```

### Update Domain Stats
```http
PUT /api/domain-stats/{domain}
```

**Request Body:**
```json
{
  "dr_score": 46,
  "traffic": 13000,
  "is_indexed": true
}
```

**Tier Validation:**
- ❌ Free tier: Not available
- ✅ Professional tier: 100 updates/month

---

## Domain Management API

### Get Domain Management
```http
GET /api/domain/management
```

**Response:**
```json
{
  "domains": [
    {
      "domain": "example.com",
      "whois_data": {
        "registrar": "GoDaddy",
        "expiry_date": "2024-12-15",
        "nameservers": ["ns1.godaddy.com", "ns2.godaddy.com"]
      },
      "ssl_info": {
        "issuer": "Let's Encrypt",
        "expiry_date": "2024-04-15",
        "valid": true
      },
      "last_checked": "2024-01-20T14:30:00Z"
    }
  ]
}
```

### Get Domain WHOIS
```http
GET /api/domain/whois?domain={domain}
```

**Response:**
```json
{
  "domain": "example.com",
  "whois_data": {
    "registrar": "GoDaddy",
    "creation_date": "2020-01-15",
    "expiry_date": "2024-12-15",
    "updated_date": "2023-12-15",
    "nameservers": ["ns1.godaddy.com", "ns2.godaddy.com"],
    "registrant_country": "US",
    "status": ["clientTransferProhibited", "clientUpdateProhibited"]
  }
}
```

### Get Domain Favicon
```http
GET /api/domain/favicon?domain={domain}
```

**Response:**
```json
{
  "domain": "example.com",
  "favicon_url": "https://example.com/favicon.ico",
  "favicon_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "cached": true
}
```

---

## Integrations API

### List Integrations
```http
GET /api/integrations
```

**Response:**
```json
{
  "integrations": [
    {
      "id": "int_123",
      "name": "Google Analytics",
      "type": "analytics",
      "status": "connected",
      "config": {
        "property_id": "GA_PROPERTY_123",
        "measurement_id": "G-ABCDEF123"
      },
      "last_sync": "2024-01-20T14:30:00Z"
    }
  ]
}
```

### Create Integration
```http
POST /api/integrations
```

**Request Body:**
```json
{
  "name": "Google Search Console",
  "type": "seo",
  "config": {
    "site_url": "https://example.com",
    "verification_method": "html_file"
  }
}
```

### Update Integration
```http
PUT /api/integrations/{integration_id}
```

### Delete Integration
```http
DELETE /api/integrations/{integration_id}
```

---

## Import/Export API

### CSV Import
```http
POST /api/import/csv-backlinks
```

**Request Body (multipart/form-data):**
```
file: [CSV file]
project_id: proj_123
```

**Response:**
```json
{
  "success": true,
  "imported": 150,
  "skipped": 5,
  "errors": [
    {
      "row": 12,
      "error": "Invalid URL format"
    }
  ]
}
```

### Batch Import
```http
POST /api/discovered-links/batch-import
```

**Request Body:**
```json
{
  "project_id": "proj_123",
  "links": [
    {
      "url": "https://example.com/page1",
      "title": "Page 1",
      "description": "Description 1"
    },
    {
      "url": "https://example.com/page2",
      "title": "Page 2",
      "description": "Description 2"
    }
  ]
}
```

---

## Webhook API

### Stripe Webhook
```http
POST /api/stripe-notify
```

**Headers:**
```
Stripe-Signature: webhook_signature
```

**Request Body:**
```json
{
  "id": "evt_123",
  "type": "customer.subscription.created",
  "data": {
    "object": {
      "id": "sub_123",
      "customer": "cus_123",
      "status": "active",
      "current_period_start": 1642694400,
      "current_period_end": 1645372800
    }
  }
}
```

---

## SDK Examples

### JavaScript/TypeScript
```typescript
import { MyBacklinksClient } from '@mybacklinks/sdk';

const client = new MyBacklinksClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://mybacklinks.app/api'
});

// Get tier info
const tierInfo = await client.user.getTierInfo();

// Create project
const project = await client.projects.create({
  name: 'My Website',
  domain: 'example.com'
});

// List links
const links = await client.links.list({
  project_id: project.id,
  limit: 10
});
```

### Python
```python
from mybacklinks import MyBacklinksClient

client = MyBacklinksClient(
    api_key='your-api-key',
    base_url='https://mybacklinks.app/api'
)

# Get tier info
tier_info = client.user.get_tier_info()

# Create project
project = client.projects.create(
    name='My Website',
    domain='example.com'
)

# List links
links = client.links.list(
    project_id=project['id'],
    limit=10
)
```

### PHP
```php
<?php
require_once 'vendor/autoload.php';

use MyBacklinks\Client;

$client = new Client([
    'api_key' => 'your-api-key',
    'base_url' => 'https://mybacklinks.app/api'
]);

// Get tier info
$tierInfo = $client->user->getTierInfo();

// Create project
$project = $client->projects->create([
    'name' => 'My Website',
    'domain' => 'example.com'
]);

// List links
$links = $client->links->list([
    'project_id' => $project['id'],
    'limit' => 10
]);
```

---

## Error Handling

### Common Error Response Format
```json
{
  "error": "Human-readable error message",
  "code": "MACHINE_READABLE_CODE",
  "details": {
    "field": "Additional context",
    "timestamp": "2024-01-25T09:00:00Z"
  },
  "request_id": "req_123456"
}
```

### Error Codes Reference

#### Authentication Errors
- `AUTHENTICATION_REQUIRED`: Missing or invalid authentication
- `INVALID_API_KEY`: API key is invalid or expired
- `SESSION_EXPIRED`: User session has expired

#### Authorization Errors
- `TIER_LIMIT_EXCEEDED`: User has exceeded their tier limits
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `FEATURE_NOT_AVAILABLE`: Feature not available for user's tier

#### Rate Limiting
- `RATE_LIMIT_EXCEEDED`: Too many requests within time window
- `QUOTA_EXCEEDED`: Monthly quota exceeded

#### Validation Errors
- `INVALID_REQUEST`: Request body validation failed
- `MISSING_REQUIRED_FIELD`: Required field is missing
- `INVALID_URL_FORMAT`: URL format is invalid
- `DOMAIN_NOT_FOUND`: Domain does not exist

#### Resource Errors
- `RESOURCE_NOT_FOUND`: Requested resource does not exist
- `RESOURCE_ALREADY_EXISTS`: Resource already exists
- `RESOURCE_LOCKED`: Resource is locked for editing

---

## Postman Collection

Download the official Postman collection: [MyBacklinks API Collection](https://api.mybacklinks.app/postman/collection.json)

### Environment Variables
```json
{
  "base_url": "https://mybacklinks.app/api",
  "api_key": "your-api-key",
  "project_id": "your-project-id"
}
```

---

## API Status

Check the current API status: [https://status.mybacklinks.app](https://status.mybacklinks.app)

### Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "timestamp": "2024-01-25T09:00:00Z",
  "version": "1.0.0"
}
```

---

## Support

For API support and questions:
- Documentation: [https://docs.mybacklinks.app](https://docs.mybacklinks.app)
- Email: <EMAIL>
- Discord: [MyBacklinks Community](https://discord.gg/mybacklinks)

---

**Last Updated:** January 25, 2024
**API Version:** v1.0.0