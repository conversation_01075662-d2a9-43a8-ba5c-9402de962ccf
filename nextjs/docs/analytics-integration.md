# 分析平台集成与数据刷新功能

本文档说明如何配置和使用LinkTrackPro的分析平台集成功能，实现数据自动刷新。

## 支持的分析平台

### 1. Google Analytics 4 (GA4)

**配置步骤：**

1. 在 [Google Cloud Console](https://console.cloud.google.com/) 创建项目
2. 启用 Google Analytics Data API
3. 创建服务账号并下载 JSON 密钥文件
4. 在 GA4 属性中为服务账号邮箱添加"查看者"权限

**环境变量配置：**
```bash
GOOGLE_ANALYTICS_SERVICE_ACCOUNT_KEY='{"type":"service_account",...}'
GOOGLE_ANALYTICS_PROPERTY_ID="your_property_id"
```

**API调用示例：**
```javascript
// 需要安装: npm install @google-analytics/data
const { BetaAnalyticsDataClient } = require('@google-analytics/data');

const analyticsDataClient = new BetaAnalyticsDataClient({
  credentials: JSON.parse(process.env.GOOGLE_ANALYTICS_SERVICE_ACCOUNT_KEY)
});

const [response] = await analyticsDataClient.runReport({
  property: `properties/${process.env.GOOGLE_ANALYTICS_PROPERTY_ID}`,
  dateRanges: [{ startDate: '30daysAgo', endDate: 'today' }],
  dimensions: [{ name: 'date' }],
  metrics: [
    { name: 'screenPageViews' },
    { name: 'sessions' },
    { name: 'bounceRate' }
  ]
});
```

### 2. Plausible Analytics

**配置步骤：**

1. 登录 [Plausible Analytics](https://plausible.io)
2. 在账户设置中生成 API Token
3. 确保目标网站已添加到 Plausible

**环境变量配置：**
```bash
PLAUSIBLE_API_KEY="your_api_token"
PLAUSIBLE_BASE_URL="https://plausible.io"  # 自托管时修改此URL
```

**API调用示例：**
```javascript
const response = await fetch(
  `${baseUrl}/api/v1/stats/timeseries?site_id=${siteId}&period=30d&metrics=pageviews,visitors,bounce_rate`,
  {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }
  }
);
```

### 3. Umami Analytics

**配置步骤：**

1. 登录 Umami 实例
2. 在设置中生成 API Token
3. 获取网站的 Website ID

**环境变量配置：**
```bash
UMAMI_API_KEY="your_api_token"
UMAMI_WEBSITE_ID="your_website_id"
UMAMI_BASE_URL="https://your-umami-instance.com"
```

**API调用示例：**
```javascript
const response = await fetch(
  `${baseUrl}/api/websites/${websiteId}/pageviews?startAt=${startTime}&endAt=${endTime}&unit=day`,
  {
    headers: {
      'x-umami-api-key': apiKey,
      'Content-Type': 'application/json'
    }
  }
);
```

## 数据刷新功能

### 自动刷新

系统会在以下情况自动刷新数据：
- 切换时间范围时
- 切换分析平台时
- 页面加载时

### 手动刷新

点击"刷新数据"按钮可以手动强制刷新：

1. **单一平台刷新** - 当前选中的分析平台
2. **全平台刷新** - 所有配置的活跃分析平台

### 刷新流程

```mermaid
graph TD
    A[点击刷新按钮] --> B[获取分析平台配置]
    B --> C{配置是否存在？}
    C -->|否| D[显示错误信息]
    C -->|是| E[调用分析平台API]
    E --> F{API调用成功？}
    F -->|否| G[显示API错误]
    F -->|是| H[获取链接数据]
    H --> I[合并数据]
    I --> J[更新前端显示]
    J --> K[显示成功消息]
```

## 数据结构

### AnalyticsData 接口

```typescript
interface AnalyticsData {
  date: string;           // 日期 (YYYY-MM-DD)
  dr_score: number;       // DR评分
  linkCount: number;      // 外链数量
  pageViews: number;      // 页面浏览量
  sessions?: number;      // 会话数
  bounceRate?: number;    // 跳出率 (%)
  avgDuration?: number;   // 平均停留时间 (秒)
}
```

### API响应格式

```json
{
  "success": true,
  "data": [
    {
      "date": "2024-01-01",
      "dr_score": 52,
      "linkCount": 35,
      "pageViews": 1250,
      "sessions": 875,
      "bounceRate": 45.2,
      "avgDuration": 125
    }
  ],
  "current": {
    "dr_score": 52,
    "linkCount": 35,
    "pageViews": 1250
  },
  "trends": {
    "dr_score": 2.5,
    "linkCount": -1.2,
    "pageViews": 15.8
  },
  "provider": "plausible",
  "timeRange": "30d",
  "lastUpdated": "2024-01-15T10:30:00Z",
  "message": "数据已从 plausible 分析平台获取"
}
```

## 错误处理

### 常见错误类型

1. **配置错误** - API密钥无效或配置缺失
2. **网络错误** - 无法连接到分析平台
3. **权限错误** - API密钥权限不足
4. **数据错误** - 返回数据格式不正确

### 错误处理策略

- **配置错误**: 显示配置提示，引导用户修复
- **网络错误**: 自动重试，降级到缓存数据
- **API错误**: 显示具体错误信息，提供解决建议
- **数据错误**: 使用默认数据结构，记录错误日志

## 性能优化

### 数据缓存

- API响应缓存 15 分钟
- 数据库查询结果缓存 5 分钟
- 前端状态缓存直到手动刷新

### 批量处理

- 支持多个分析平台并行刷新
- 链接数据批量查询
- 异步处理减少阻塞

### 限流控制

- 每个用户每分钟最多 10 次刷新请求
- 每个分析平台每小时最多 100 次API调用
- 自动降级到模拟数据

## 部署注意事项

1. **环境变量安全** - 生产环境使用加密存储
2. **API限制** - 注意各平台的调用限制
3. **监控报警** - 设置API失败率监控
4. **备份策略** - 定期备份历史分析数据

## 使用示例

### 前端调用

```typescript
// 获取分析数据
const response = await fetch(
  `/api/projects/${projectId}/analytics-data?provider=plausible&timeRange=30d`
);

// 刷新数据
const refreshResponse = await fetch(
  `/api/projects/${projectId}/analytics-data`,
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ action: 'refresh' })
  }
);
```

### 配置检查

使用分析配置对话框检查和测试API连接：

```typescript
// 组件中使用
<AnalyticsConfigDialog
  project_id={projectId}
  projectDomain={domain}
  configs={analyticsConfigs}
  onConfigsUpdate={handleConfigsUpdate}
/>
```

## 故障排除

### 常见问题

**Q: 数据不更新怎么办？**
A: 检查API密钥是否正确，查看浏览器控制台错误信息

**Q: 某个分析平台连接失败？**
A: 使用配置对话框测试连接，确认API密钥和权限

**Q: 数据显示不准确？**
A: 检查时区设置，确认分析平台的数据统计规则

**Q: 刷新按钮无响应？**
A: 检查网络连接，查看服务器日志获取详细错误信息

### 调试模式

开启调试模式查看详细日志：

```bash
DEBUG=analytics:* npm run dev
```

## 更新日志

- **v1.2.0** - 支持多平台并行刷新
- **v1.1.0** - 添加 Umami Analytics 支持  
- **v1.0.0** - 基础的 Google Analytics 和 Plausible 集成 