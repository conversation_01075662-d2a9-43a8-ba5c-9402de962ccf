# Google Search Console API 配置指南

本文档介绍如何配置 Google Search Console API 来自动发现外链。

## 1. 创建 Google Cloud 项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google Search Console API

## 2. 创建服务账号

1. 在 Google Cloud Console 中，转到 "IAM & Admin" > "Service Accounts"
2. 点击 "Create Service Account"
3. 输入服务账号名称和描述
4. 创建并下载 JSON 密钥文件

## 3. 配置 Search Console 权限

1. 访问 [Google Search Console](https://search.google.com/search-console)
2. 验证你要监控的域名
3. 在属性设置中，添加服务账号邮箱作为用户（至少需要 "Full" 权限）

## 4. 环境变量配置

在你的 `.env.development` 或 `.env.local` 文件中添加：

```bash
# 方法 1: 使用密钥文件路径
GOOGLE_SERVICE_ACCOUNT_KEY_FILE=/path/to/your/service-account-key.json

# 方法 2: 直接使用密钥内容（适用于部署环境）
GOOGLE_SERVICE_ACCOUNT_KEY_CONTENT='{"type":"service_account","project_id":"your-project","private_key_id":"...","private_key":"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"...","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/your-service%40your-project.iam.gserviceaccount.com"}'
```

## 5. API 功能说明

### discoverBacklinks(domain)
使用 Search Console 的发现数据获取指向域名的外链。

### getExternalLinks(domain)
通过搜索分析数据查找包含域名的搜索结果。

### isDomainVerified(domain)
检查域名是否在 Search Console 中已验证。

## 6. 限制和注意事项

1. **数据延迟**: Google Search Console 数据通常有 2-3 天的延迟
2. **API 配额**: 每天有请求限制，需要合理使用
3. **域名验证**: 只能获取已验证域名的数据
4. **数据完整性**: GSC 可能不会显示所有外链，建议结合其他工具

## 7. 故障排除

### 常见错误

1. **"Domain not verified"**: 确保域名在 Search Console 中已验证，并且服务账号有访问权限
2. **"Failed to discover backlinks"**: 检查 API 密钥配置和网络连接
3. **"No external backlinks found"**: 可能域名较新或外链较少，属于正常情况

### 调试建议

1. 检查服务账号密钥是否正确
2. 确认 Search Console API 已启用
3. 验证服务账号在 Search Console 中的权限
4. 查看控制台日志获取详细错误信息

## 8. 扩展功能

未来可以考虑集成其他外链发现服务：

- Ahrefs API
- Moz API
- SEMrush API
- Majestic API

这些服务通常提供更全面的外链数据，但需要付费订阅。 