# 项目分类功能文档

## 概述

项目分类功能允许用户为项目设置自定义分类，便于组织和管理项目。支持预定义分类和用户自定义分类，并提供丰富的图标和颜色标识。

## 功能特性

### 1. 自定义分类
- 用户可以为每个项目设置自定义分类
- 支持中英文分类名称
- 分类名称最长100个字符

### 2. 预定义分类
系统预设了常见的项目分类，包括：

**技术类**
- 工具 🔧 - 实用工具和软件
- 开发 💻 - 开发工具和编程资源  
- 技术 ⚙️ - 技术相关项目

**娱乐类**
- 游戏 🎮 - 游戏相关项目
- 音乐 🎵 - 音乐和音频项目
- 摄影 📷 - 摄影和图像处理

**内容类**
- 博客 📖 - 博客和内容网站
- 新闻 🌐 - 新闻和资讯网站
- 教育 🎓 - 教育和学习资源

**商业类**
- 电商 🛒 - 电子商务网站
- 商业 💼 - 商业和企业网站
- 营销 📈 - 营销和推广项目

**生活类**
- 健康 ❤️ - 健康和医疗相关
- 美食 🍽️ - 美食和餐饮
- 旅行 ✈️ - 旅行和旅游
- 生活 🏠 - 生活方式和日常

**其他**
- 艺术 🎨 - 艺术和创意项目
- 社交 👥 - 社交网络和社区
- 汽车 🚗 - 汽车相关项目
- 安全 🛡️ - 安全和隐私工具
- 论坛 💬 - 论坛和讨论社区

### 3. 智能图标匹配
- 系统会根据分类名称自动匹配合适的图标
- 支持中英文关键词匹配
- 为每个分类提供独特的颜色标识

### 4. 分组显示
- 在项目列表中按分类分组显示
- 每个分类显示项目数量
- 未分类项目单独显示

## 使用方法

### 1. 创建项目时设置分类

在创建新项目时，在项目表单中选择或输入分类：

1. 点击分类下拉选择器
2. 从预定义分类中选择，或点击"创建新分类..."
3. 输入自定义分类名称并确认

### 2. 编辑现有项目分类

1. 在项目列表中点击项目进入详情页
2. 点击编辑按钮
3. 在分类字段中修改分类
4. 保存更改

### 3. 查看按分类分组的项目

在项目页面，项目会自动按分类分组显示：
- 每个分类显示相应的图标和颜色
- 显示该分类下的项目数量
- "未分类"项目会单独显示在最后

## 数据库结构

### 新增字段
```sql
-- projects表新增字段
ALTER TABLE link_track.projects 
ADD COLUMN category VARCHAR(100);
```

### 新增函数
```sql
-- 获取用户项目分类统计
CREATE FUNCTION link_track.get_user_project_categories(p_user_id VARCHAR(255))
RETURNS TABLE(category VARCHAR(100), project_count BIGINT);
```

## API 接口

### 获取用户分类
```http
GET /api/projects/categories
```

返回用户的所有分类及每个分类的项目数量。

### 创建/更新项目
现有的项目 API 已更新支持 `category` 字段：

```http
POST /api/projects
PUT /api/projects/{id}
```

请求体示例：
```json
{
  "name": "我的项目",
  "domain": "example.com",
  "description": "项目描述",
  "category": "工具"
}
```

## 组件使用

### ProjectCategoryBadge 组件
```tsx
import { ProjectCategoryBadge } from "@/components/blocks/link-dashboard/project-category-badge";

<ProjectCategoryBadge 
  category="工具" 
  size="md" 
  showIcon={true} 
/>
```

### 获取分类配置
```tsx
import { getCategoryConfig } from "@/lib/project-categories";

const config = getCategoryConfig("工具");
const IconComponent = config.icon;
```

## 注意事项

1. **数据库迁移**：在使用前需要运行 `012_add_project_categories.sql` 迁移
2. **分类长度限制**：分类名称最长100个字符
3. **大小写敏感**：分类名称区分大小写
4. **图标匹配**：系统会智能匹配图标，自定义分类也会有合适的默认图标

## 技术实现

### 文件结构
```
nextjs/
├── lib/project-categories.ts           # 分类配置和工具函数
├── types/links.ts                      # 更新的类型定义
├── models/links.ts                     # 数据模型更新
├── app/api/projects/categories/        # 分类API
├── components/blocks/link-dashboard/
│   ├── project-form.tsx               # 更新的项目表单
│   ├── project-category-badge.tsx     # 分类徽章组件
│   └── index.tsx                      # 更新的主界面
└── data/migrations/
    └── 012_add_project_categories.sql # 数据库迁移
```

### 核心功能
- 自动图标和颜色匹配
- 智能分类建议
- 分组显示
- 响应式设计
- 国际化支持

通过这个分类功能，用户可以更好地组织和管理他们的项目，提高工作效率。 