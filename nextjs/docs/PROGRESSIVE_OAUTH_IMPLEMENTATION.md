# Progressive OAuth Implementation Guide

## Overview

This implementation provides a progressive OAuth permission system that separates basic authentication from service-specific permissions, significantly improving user experience and reducing Google verification requirements.

## Key Benefits

1. **Reduced User Friction**: Users can sign up with basic permissions only
2. **Context-Aware Permissions**: Analytics permissions requested only when needed
3. **Google Verification**: Eliminates need for immediate Google app verification
4. **Enhanced Security**: Granular permission management per service
5. **Better UX**: Clear understanding of why permissions are needed

## Architecture

### Database Schema

#### New Tables

1. **`user_oauth_tokens`**: Stores progressive OAuth tokens
2. **`oauth_states`**: Manages PKCE flow state for secure authorization
3. **Enhanced `user_configs`**: Tracks OAuth status and requirements

#### Token Types

- `basic`: Basic Google profile/email access
- `analytics`: Google Analytics readonly access
- `search_console`: Google Search Console readonly access
- `combined`: Both Analytics and Search Console access

### Service Layer

#### Core Services

1. **OAuthTokenManager** (`services/oauth-token-manager.ts`)
   - Manages token lifecycle
   - Handles PKCE flows
   - Provides token refresh functionality

2. **OAuthErrorHandler** (`services/oauth-error-handler.ts`)
   - Centralized error processing
   - Retry logic with exponential backoff
   - User-friendly error messages

3. **Enhanced Services**
   - `enhanced-google-analytics.ts`: Updated Analytics service
   - `enhanced-search-console.ts`: Updated Search Console service

### API Endpoints

- `GET /api/projects/[id]/oauth/status` - Check OAuth status
- `POST /api/projects/[id]/oauth/authorize` - Initiate OAuth flow
- `POST /api/projects/[id]/oauth/callback` - Handle OAuth callback
- `POST /api/projects/[id]/oauth/refresh` - Refresh tokens
- `DELETE /api/projects/[id]/oauth/revoke` - Revoke access
- `GET /api/projects/[id]/oauth/test` - Test connection

### Frontend Components

1. **ProgressiveOAuthConnector**: Service-specific connection UI
2. **AnalyticsConfigurationSection**: Main configuration interface
3. **OAuthStatusDashboard**: Status monitoring and management

### Hooks

1. **useOAuthStatus**: Monitor connection status and token expiration
2. **useOAuthError**: Handle OAuth errors with user-friendly messages

## Implementation Steps

### 1. Database Migration

Run the migration to create necessary tables:

```sql
-- Apply migration 008
-- See: data/migrations/008_progressive_oauth_tokens.sql
```

### 2. Update Environment Variables

Ensure your OAuth configuration is set up:

```env
AUTH_GOOGLE_ID=your_google_client_id
AUTH_GOOGLE_SECRET=your_google_client_secret
NEXTAUTH_URL=your_app_url
```

### 3. Update Authentication Config

The `auth/config.ts` file has been updated to use basic scopes only:

```typescript
// Basic OAuth scopes for initial login
scope: "openid email profile"
```

### 4. Integration Points

#### In Project Detail Pages

```tsx
import AnalyticsConfigurationSection from '@/components/blocks/analytics/AnalyticsConfigurationSection';

// In your project detail component
<AnalyticsConfigurationSection
  projectId={project.id}
  userId={user.uuid}
  project={project}
  onConfigurationChange={() => {
    // Handle configuration updates
  }}
/>
```

#### Status Monitoring

```tsx
import OAuthStatusDashboard from '@/components/blocks/analytics/OAuthStatusDashboard';

// For compact status display
<OAuthStatusDashboard
  projectId={project.id}
  userId={user.uuid}
  compact={true}
  autoRefresh={true}
/>
```

## Testing Guide

### 1. End-to-End OAuth Flow

#### Test Basic Login
1. Clear browser cookies/localStorage
2. Navigate to login page
3. Verify only basic permissions requested
4. Complete login flow
5. Verify user can access basic features

#### Test Progressive Analytics Connection
1. Navigate to project analytics configuration
2. Click "Connect Google Analytics"
3. Verify additional permissions requested
4. Complete OAuth flow
5. Verify analytics features are enabled

#### Test Error Scenarios
1. **Token Expiration**: Manually expire tokens in database, verify refresh logic
2. **Revoked Access**: Revoke access in Google account, verify error handling
3. **Network Issues**: Simulate network failures, verify retry logic
4. **Invalid State**: Tamper with OAuth state, verify security validation

### 2. API Endpoint Testing

#### Status Check
```bash
curl -X GET "/api/projects/{project_id}/oauth/status" \
  -H "Authorization: Bearer {session_token}"
```

#### Initiate OAuth
```bash
curl -X POST "/api/projects/{project_id}/oauth/authorize" \
  -H "Content-Type: application/json" \
  -d '{"tokenType": "analytics"}'
```

#### Test Connection
```bash
curl -X GET "/api/projects/{project_id}/oauth/test?tokenType=analytics" \
  -H "Authorization: Bearer {session_token}"
```

### 3. Component Testing

#### ProgressiveOAuthConnector
- Test connection states: idle, connecting, connected, error
- Verify status updates after successful connection
- Test error handling and user feedback
- Verify refresh and revoke functionality

#### AnalyticsConfigurationSection
- Test tab navigation and content
- Verify connection status synchronization
- Test configuration options when connected/disconnected

### 4. Security Testing

#### OAuth Flow Security
- Verify PKCE implementation (code_challenge/code_verifier)
- Test state parameter validation
- Verify redirect URI validation
- Test token encryption and storage

#### Permission Validation
- Verify scope validation for each service
- Test unauthorized access attempts
- Verify token refresh security

## Migration Strategy

### Phase 1: Immediate (Current Implementation)
1. Update `auth/config.ts` to use basic scopes only ✅
2. Deploy progressive OAuth system alongside existing ✅
3. Test with development users ✅

### Phase 2: Gradual Migration
1. Enable progressive OAuth for new users
2. Migrate existing users gradually
3. Monitor error rates and user feedback

### Phase 3: Complete Migration
1. Remove legacy session-based token storage
2. Migrate all users to new system
3. Update documentation and training

## Monitoring and Maintenance

### Key Metrics
- OAuth flow completion rates
- Token refresh success rates
- Error rates by type
- User conversion from basic to analytics permissions

### Maintenance Tasks
- Monitor token expiration and renewal
- Clean up expired OAuth states
- Review and update error handling
- Update permissions as Google APIs evolve

### Alerting
- High error rates in OAuth flows
- Unusual patterns in token usage
- Security-related events (invalid states, etc.)

## Troubleshooting

### Common Issues

#### "OAuth token has expired" Errors
- Check token refresh logic
- Verify refresh token availability
- Review expiration handling

#### "Insufficient scope" Errors
- Verify required scopes for operations
- Check token type matching
- Review permission mappings

#### Connection Test Failures
- Verify API credentials
- Check network connectivity
- Review rate limiting

### Debug Tools

#### Enable Debug Logging
```typescript
// Add to environment
DEBUG_OAUTH=true
```

#### Check Token Status
```sql
SELECT * FROM link_track.user_oauth_tokens 
WHERE user_id = 'user_uuid' AND project_id = 'project_uuid';
```

#### Monitor OAuth States
```sql
SELECT * FROM link_track.oauth_states 
WHERE created_at > NOW() - INTERVAL '1 hour';
```

## Security Considerations

### Token Storage
- All tokens encrypted at rest
- Secure transmission via HTTPS
- Limited scope per token type

### Access Control
- User-project scoped tokens
- Role-based access validation
- Audit logging for token operations

### Best Practices
- Regular token rotation
- Minimal permission principle
- Secure error handling (no sensitive data in logs)

## Future Enhancements

### Planned Features
1. **Multi-Account Support**: Connect multiple Google accounts
2. **Automatic Token Renewal**: Background refresh of expiring tokens
3. **Usage Analytics**: Track OAuth flow performance
4. **Advanced Permissions**: Fine-grained scope management

### API Integrations
1. **Microsoft Analytics**: Azure Application Insights
2. **Meta Business**: Facebook/Instagram analytics
3. **Twitter API**: Social media analytics
4. **LinkedIn Analytics**: Professional network insights

## Conclusion

This progressive OAuth implementation provides a robust foundation for secure, user-friendly analytics integrations while maintaining the flexibility to add new services and permissions as needed.

For questions or issues, refer to the troubleshooting section or consult the development team.