# Google Analytics & Search Console 集成

## 概述

这个集成允许用户直接使用他们自己的 Google 账号来访问 Google Analytics 和 Search Console 数据，无需额外的 OAuth 应用配置。

## 特点

- **用户级授权**: 每个用户使用自己的 Google 账号登录
- **自动权限检测**: 自动检测用户是否有 Analytics 和 Search Console 访问权限
- **零配置**: 用户无需配置额外的 API 密钥或服务账号
- **实时数据**: 直接从用户的 Google 账号获取实时数据

## 工作原理

### 1. Google 登录扩展权限
当用户登录时，系统会请求以下权限：
- `openid email profile` - 基本登录信息
- `https://www.googleapis.com/auth/analytics.readonly` - 只读 Analytics 访问
- `https://www.googleapis.com/auth/webmasters.readonly` - 只读 Search Console 访问

### 2. Session 中的令牌存储
用户的 Google 访问令牌被安全地存储在 NextAuth session 中：
```typescript
session.googleAccessToken
session.googleRefreshToken  
session.googleTokenExpiry
session.googleScope
```

### 3. API 路由
新的 API 路由直接使用用户的令牌：
- `GET /api/projects/[id]/google-analytics/test` - 测试连接和权限
- `GET /api/projects/[id]/google-analytics/properties` - 获取 GA 属性列表
- `POST /api/projects/[id]/google-analytics/data` - 获取 Analytics 数据

## 配置要求

### Google Cloud Console 设置
1. 使用现有的 Google OAuth 应用（AUTH_GOOGLE_ID 和 AUTH_GOOGLE_SECRET）
2. 启用以下 API：
   - Google Analytics Data API
   - Google Analytics Admin API
   - Google Search Console API

### 环境变量
不需要额外的环境变量，使用现有的：
```env
AUTH_GOOGLE_ID=your_existing_google_client_id
AUTH_GOOGLE_SECRET=your_existing_google_client_secret
```

## 用户体验

### 首次使用
1. 用户点击 "Login with Google"
2. Google 会要求授权 Analytics 和 Search Console 权限
3. 用户确认后，系统自动检测可用的服务
4. 显示用户的 Analytics 属性和 Search Console 站点

### 权限管理
- 如果用户没有 Analytics 或 Search Console 访问权限，系统会友好地提示
- 用户可以随时重新授权以更新权限
- 令牌过期时会提示用户重新登录

## 代码结构

### 核心服务
- `services/user-google-analytics.ts` - 使用用户令牌的 Analytics 服务
- `auth/config.ts` - 扩展的 NextAuth 配置

### API 路由
- `app/api/projects/[id]/google-analytics/` - 用户级 Analytics API

### 前端组件
- `components/blocks/analytics/user-google-analytics.tsx` - 用户友好的界面

## 安全考虑

1. **令牌安全**: 访问令牌只存储在服务器端 session 中
2. **权限最小化**: 只请求只读权限
3. **令牌过期**: 自动检测令牌过期并提示重新登录
4. **用户控制**: 用户完全控制自己的数据访问

## 与 Plausible 的对比

| 特性 | 本实现 | Plausible |
|------|--------|-----------|
| 用户授权 | 个人 Google 账号 | 个人 Google 账号 |
| 配置复杂度 | 零配置 | 零配置 |
| 数据访问 | 用户自己的数据 | 用户自己的数据 |
| 权限管理 | 自动检测 | 手动配置 |
| 多用户支持 | 天然支持 | 天然支持 |

## 优势

1. **用户友好**: 用户使用自己熟悉的 Google 账号
2. **无配置负担**: 管理员无需配置额外的服务账号
3. **权限透明**: 用户清楚知道授权了什么权限
4. **实时同步**: 直接访问用户的最新数据
5. **多租户**: 每个用户访问自己的数据，天然隔离

这种方法实现了类似 Plausible 的用户体验，同时保持了系统的简洁性和安全性。