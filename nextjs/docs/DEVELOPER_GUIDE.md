# MyBacklinks.app Developer Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Development Environment](#development-environment)
3. [Architecture Overview](#architecture-overview)
4. [Database Development](#database-development)
5. [API Development](#api-development)
6. [Frontend Development](#frontend-development)
7. [Testing](#testing)
8. [Deployment](#deployment)
9. [Contributing](#contributing)
10. [Best Practices](#best-practices)

---

## Getting Started

### Prerequisites

- **Node.js** 18.x or higher
- **pnpm** 8.x or higher
- **PostgreSQL** 14.x or higher with pgvector extension
- **Git** for version control

### Quick Setup

1. **Clone the repository:**
```bash
git clone https://github.com/mybacklinks/mybacklinks-app.git
cd mybacklinks-app
```

2. **Install dependencies:**
```bash
cd nextjs/
pnpm install
```

3. **Set up environment variables:**
```bash
cp .env.example .env.local
```

4. **Configure database:**
```bash
# Edit .env.local with your database credentials
DATABASE_URL="postgresql://user:password@localhost:5432/mybacklinks"
```

5. **Run database migrations:**
```bash
psql -f data/add-user-tiers.sql
```

6. **Start development server:**
```bash
pnpm dev
```

### Project Structure

```
nextjs/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── blocks/           # Feature-specific components
│   ├── ui/               # Base UI components
│   └── console/          # Dashboard components
├── lib/                   # Utility libraries
│   ├── hooks/            # Custom React hooks
│   ├── utils/            # Utility functions
│   ├── validation/       # Zod schemas
│   └── tier-middleware.ts # Tier system middleware
├── models/               # Database models
├── data/                 # Database schemas and migrations
├── i18n/                 # Internationalization files
├── types/                # TypeScript type definitions
└── docs/                 # Documentation
```

---

## Development Environment

### Local Development Setup

#### 1. Database Setup

**Install PostgreSQL with pgvector:**
```bash
# macOS
brew install postgresql@14
brew install pgvector

# Ubuntu
sudo apt-get install postgresql-14 postgresql-14-pgvector

# Start PostgreSQL
brew services start postgresql@14  # macOS
sudo systemctl start postgresql    # Ubuntu
```

**Create database and user:**
```sql
-- Connect to PostgreSQL
psql postgres

-- Create database
CREATE DATABASE mybacklinks;
CREATE USER mybacklinks_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE mybacklinks TO mybacklinks_user;

-- Connect to your database
\c mybacklinks

-- Create schema
CREATE SCHEMA IF NOT EXISTS link_track;
GRANT ALL ON SCHEMA link_track TO mybacklinks_user;

-- Install pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;
```

**Run migrations:**
```bash
cd nextjs/
psql -d mybacklinks -f data/add-user-tiers.sql
```

#### 2. Environment Configuration

Create `.env.local` file:
```bash
# Database
DATABASE_URL="postgresql://mybacklinks_user:your_password@localhost:5432/mybacklinks"
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# External APIs (optional for development)
AHREFS_API_KEY="your-ahrefs-key"
SEMRUSH_API_KEY="your-semrush-key"

# Development flags
NODE_ENV="development"
DEBUG_TIER_SYSTEM="true"
```

#### 3. Development Tools

**Install recommended VS Code extensions:**
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-eslint",
    "yzhang.markdown-all-in-one"
  ]
}
```

**VS Code settings:**
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "tailwindCSS.experimental.classRegex": [
    "cn\\(([^)]*)\\)"
  ]
}
```

### Development Commands

```bash
# Start development server
pnpm dev

# Run type checking
pnpm type-check

# Run linting
pnpm lint

# Build for production
pnpm build

# Start production server
pnpm start

# Run Cloudflare build
pnpm cf:build

# Deploy to Cloudflare
pnpm cf:deploy
```

---

## Architecture Overview

### System Architecture

```mermaid
graph TB
    subgraph "Frontend (Next.js)"
        A[App Router]
        B[React Components]
        C[SWR Caching]
        D[Tier Status Hook]
    end
    
    subgraph "Middleware Layer"
        E[Tier Middleware]
        F[Rate Limiting]
        G[Authentication]
        H[Validation]
    end
    
    subgraph "API Layer"
        I[Projects API]
        J[Links API]
        K[Analytics API]
        L[User API]
    end
    
    subgraph "Database"
        M[PostgreSQL]
        N[Supabase]
        O[pgvector]
    end
    
    subgraph "External Services"
        P[Ahrefs API]
        Q[Google APIs]
        R[Stripe]
    end
    
    A --> E
    B --> D
    C --> L
    E --> F
    F --> G
    G --> H
    H --> I
    I --> M
    K --> P
    K --> Q
    R --> L
```

### Key Design Patterns

#### 1. Tier-Based Access Control
```typescript
// Middleware pattern for API protection
export const handler = withTierValidation(
  { action: 'create_project' },
  async (request, context, userUuid) => {
    // Your handler logic here
  }
);
```

#### 2. React Hook Pattern
```typescript
// Custom hook for tier status
export function useTierStatus() {
  const { data, error, isLoading } = useSWR('/api/user/tier-info', fetcher);
  return { tierInfo: data, loading: isLoading, error };
}
```

#### 3. Repository Pattern
```typescript
// Database operations abstraction
export class ProjectRepository {
  async create(data: CreateProjectData): Promise<Project> {
    return retryDatabaseOperation(() => 
      this.supabase.from('projects').insert(data)
    );
  }
}
```

#### 4. Factory Pattern
```typescript
// Tier validation factory
export function createTierValidator(action: TierAction) {
  return (userUuid: string) => validateTierAction(userUuid, action);
}
```

---

## Database Development

### Schema Management

#### 1. Creating Migrations

Create new migration files in `data/` directory:
```sql
-- data/002_add_analytics_tables.sql
CREATE TABLE link_track.analytics_data (
    id SERIAL PRIMARY KEY,
    link_id UUID NOT NULL REFERENCES link_track.links(id),
    date DATE NOT NULL,
    clicks INTEGER DEFAULT 0,
    impressions INTEGER DEFAULT 0,
    ctr DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX idx_analytics_data_link_date ON link_track.analytics_data(link_id, date);
CREATE INDEX idx_analytics_data_date ON link_track.analytics_data(date);
```

#### 2. Database Functions

```sql
-- Function to get project statistics
CREATE OR REPLACE FUNCTION link_track.get_project_stats(p_project_id UUID)
RETURNS TABLE(
    total_links INTEGER,
    indexed_links INTEGER,
    avg_dr_score DECIMAL,
    total_traffic INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_links,
        COUNT(CASE WHEN is_indexed = true THEN 1 END)::INTEGER as indexed_links,
        COALESCE(AVG(dr_score), 0)::DECIMAL as avg_dr_score,
        COALESCE(SUM(traffic), 0)::INTEGER as total_traffic
    FROM link_track.links
    WHERE project_id = p_project_id;
END;
$$ LANGUAGE plpgsql;
```

#### 3. Model Development

```typescript
// models/analytics.ts
export interface AnalyticsData {
  id: string;
  link_id: string;
  date: string;
  clicks: number;
  impressions: number;
  ctr: number;
  created_at: string;
}

export async function getAnalyticsData(
  linkId: string,
  startDate: string,
  endDate: string
): Promise<AnalyticsData[]> {
  return retryDatabaseOperation(async () => {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('analytics_data')
      .select('*')
      .eq('link_id', linkId)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: true });
    
    if (error) throw new Error(error.message);
    return data || [];
  });
}
```

### Database Best Practices

#### 1. Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX CONCURRENTLY idx_users_tier_status 
ON link_track.users(tier, subscription_status);

CREATE INDEX CONCURRENTLY idx_links_user_project 
ON link_track.links(user_id, project_id);

CREATE INDEX CONCURRENTLY idx_tier_usage_audit_user_month 
ON link_track.tier_usage_audit(user_uuid, date_trunc('month', created_at));
```

#### 2. Query Optimization
```typescript
// Use proper query patterns
const getProjectsWithStats = async (userUuid: string) => {
  const { data, error } = await supabase
    .rpc('get_projects_with_stats', { p_user_id: userUuid });
  
  if (error) throw new Error(error.message);
  return data;
};
```

#### 3. Connection Management
```typescript
// Use connection pooling
const supabase = getSupabaseClient(); // Singleton pattern
```

---

## API Development

### Creating New API Endpoints

#### 1. Basic API Route Structure
```typescript
// app/api/example/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth';
import { validateTierAccess } from '@/lib/tier-middleware';

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Your logic here
    return NextResponse.json({ data: 'example' });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
```

#### 2. Tier-Protected API Route
```typescript
// app/api/protected-endpoint/route.ts
import { validateTierAccess, createTierErrorResponse } from '@/lib/tier-middleware';

export async function POST(request: NextRequest) {
  // Validate tier access
  const tierResult = await validateTierAccess(request, { 
    action: 'create_project' 
  });
  
  if (!tierResult.success) {
    return createTierErrorResponse(tierResult);
  }

  const userUuid = tierResult.userUuid!;
  
  // Your protected logic here
  return NextResponse.json({ success: true });
}
```

#### 3. Rate-Limited API Route
```typescript
// app/api/rate-limited/route.ts
import { withRateLimit, RATE_LIMITS } from '@/lib/rate-limit';

export const POST = withRateLimit(
  RATE_LIMITS.DR_QUERIES,
  async (request: NextRequest) => {
    // Your rate-limited logic here
    return NextResponse.json({ success: true });
  }
);
```

### Input Validation

#### 1. Request Body Validation
```typescript
import { z } from 'zod';

const CreateProjectSchema = z.object({
  name: z.string().min(1).max(100),
  domain: z.string().url(),
  description: z.string().optional()
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = CreateProjectSchema.parse(body);
    
    // Process validated data
    const project = await createProject(validatedData);
    
    return NextResponse.json({ project });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }
    throw error;
  }
}
```

#### 2. Query Parameter Validation
```typescript
const ListProjectsSchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(50),
  offset: z.coerce.number().min(0).default(0),
  search: z.string().optional()
});

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  const params = ListProjectsSchema.parse({
    limit: searchParams.get('limit'),
    offset: searchParams.get('offset'),
    search: searchParams.get('search')
  });
  
  // Use validated params
  const projects = await getProjects(params);
  
  return NextResponse.json({ projects });
}
```

### Error Handling

#### 1. Standardized Error Responses
```typescript
// lib/api-errors.ts
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: unknown): NextResponse {
  if (error instanceof ApiError) {
    return NextResponse.json({
      error: error.message,
      code: error.code
    }, { status: error.statusCode });
  }
  
  if (error instanceof z.ZodError) {
    return NextResponse.json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: error.errors
    }, { status: 400 });
  }
  
  console.error('Unexpected error:', error);
  return NextResponse.json({
    error: 'Internal server error',
    code: 'INTERNAL_ERROR'
  }, { status: 500 });
}
```

#### 2. Using Error Handler
```typescript
export async function POST(request: NextRequest) {
  try {
    // Your API logic
    const result = await processRequest(request);
    return NextResponse.json(result);
  } catch (error) {
    return handleApiError(error);
  }
}
```

---

## Frontend Development

### Component Development

#### 1. Component Structure
```tsx
// components/example/ExampleComponent.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ExampleComponentProps {
  title: string;
  data: any[];
  onAction?: (item: any) => void;
}

export function ExampleComponent({ title, data, onAction }: ExampleComponentProps) {
  const [loading, setLoading] = useState(false);
  
  const handleAction = async (item: any) => {
    setLoading(true);
    try {
      await onAction?.(item);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {data.map((item) => (
          <div key={item.id} className="flex items-center justify-between p-2">
            <span>{item.name}</span>
            <Button 
              onClick={() => handleAction(item)}
              disabled={loading}
            >
              Action
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
```

#### 2. Custom Hook Development
```typescript
// lib/hooks/useApi.ts
import { useState, useEffect } from 'react';

export function useApi<T>(url: string, options?: RequestInit) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(url, options);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [url]);
  
  return { data, loading, error };
}
```

#### 3. Tier-Aware Components
```tsx
// components/TierAwareButton.tsx
import { Button } from '@/components/ui/button';
import { Lock, Crown } from 'lucide-react';
import { useTierStatus } from '@/lib/hooks/useTierStatus';

interface TierAwareButtonProps {
  requiredTier: 'free' | 'paid';
  requiredPermission?: keyof ReturnType<typeof useTierStatus>;
  onClick: () => void;
  children: React.ReactNode;
}

export function TierAwareButton({ 
  requiredTier, 
  requiredPermission, 
  onClick, 
  children 
}: TierAwareButtonProps) {
  const tierStatus = useTierStatus();
  
  const hasAccess = requiredPermission 
    ? tierStatus[requiredPermission] 
    : tierStatus.tierInfo?.tier === requiredTier;
  
  if (!hasAccess) {
    return (
      <Button disabled variant="outline">
        <Lock className="h-4 w-4 mr-2" />
        {requiredTier === 'paid' ? 'Pro Only' : 'Upgrade Required'}
      </Button>
    );
  }
  
  return (
    <Button onClick={onClick}>
      {requiredTier === 'paid' && <Crown className="h-4 w-4 mr-2" />}
      {children}
    </Button>
  );
}
```

### State Management

#### 1. SWR Integration
```typescript
// lib/api/projects.ts
import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then(res => res.json());

export function useProjects() {
  const { data, error, mutate } = useSWR('/api/projects', fetcher);
  
  return {
    projects: data?.projects || [],
    loading: !error && !data,
    error,
    refetch: mutate
  };
}
```

#### 2. Optimistic Updates
```typescript
export function useProjectMutations() {
  const { mutate } = useSWR('/api/projects');
  
  const createProject = async (projectData: CreateProjectData) => {
    // Optimistic update
    mutate(
      (current) => ({
        ...current,
        projects: [...current.projects, { ...projectData, id: 'temp-id' }]
      }),
      false
    );
    
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      });
      
      if (!response.ok) throw new Error('Failed to create project');
      
      // Revalidate with server data
      mutate();
    } catch (error) {
      // Revert optimistic update on error
      mutate();
      throw error;
    }
  };
  
  return { createProject };
}
```

### Styling Guidelines

#### 1. Tailwind CSS Usage
```tsx
// Use semantic class names
<div className="bg-background text-foreground border border-border rounded-lg p-4">
  <h2 className="text-lg font-semibold mb-2">Title</h2>
  <p className="text-muted-foreground">Description</p>
</div>
```

#### 2. Component Variants
```tsx
// Use cva for component variants
import { cva } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3',
        lg: 'h-11 px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);
```

---

## Testing

### Unit Testing

#### 1. API Route Testing
```typescript
// __tests__/api/projects.test.ts
import { describe, it, expect, vi } from 'vitest';
import { GET, POST } from '@/app/api/projects/route';
import { NextRequest } from 'next/server';

// Mock dependencies
vi.mock('@/lib/auth', () => ({
  getAuthenticatedUser: vi.fn()
}));

vi.mock('@/models/projects', () => ({
  getProjectsByUser: vi.fn(),
  createProject: vi.fn()
}));

describe('/api/projects', () => {
  it('should return projects for authenticated user', async () => {
    const mockUser = { uuid: 'user-123' };
    const mockProjects = [{ id: 'proj-1', name: 'Test Project' }];
    
    vi.mocked(getAuthenticatedUser).mockResolvedValue(mockUser);
    vi.mocked(getProjectsByUser).mockResolvedValue(mockProjects);
    
    const request = new NextRequest('http://localhost/api/projects');
    const response = await GET(request);
    
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.projects).toEqual(mockProjects);
  });
  
  it('should require authentication', async () => {
    vi.mocked(getAuthenticatedUser).mockResolvedValue(null);
    
    const request = new NextRequest('http://localhost/api/projects');
    const response = await GET(request);
    
    expect(response.status).toBe(401);
  });
});
```

#### 2. Component Testing
```typescript
// __tests__/components/TierQuotaCard.test.tsx
import { render, screen } from '@testing-library/react';
import { TierQuotaCard } from '@/components/blocks/link-dashboard/tier-quota-card';
import { useTierStatus } from '@/lib/hooks/useTierStatus';

// Mock the hook
vi.mock('@/lib/hooks/useTierStatus');

describe('TierQuotaCard', () => {
  it('should display free tier information', () => {
    vi.mocked(useTierStatus).mockReturnValue({
      tierInfo: {
        tier: 'free',
        subscription_status: 'active',
        limits: {
          projects: { used: 3, limit: 5, canCreate: true }
        }
      },
      isPaidUser: false,
      loading: false,
      error: null
    });
    
    render(<TierQuotaCard />);
    
    expect(screen.getByText('Free Plan')).toBeInTheDocument();
    expect(screen.getByText('3 / 5')).toBeInTheDocument();
    expect(screen.getByText('Upgrade to Professional')).toBeInTheDocument();
  });
});
```

#### 3. Database Function Testing
```typescript
// __tests__/models/user-tier.test.ts
import { getUserUsageSummary, canCreateProject } from '@/models/user-tier';

describe('user-tier model', () => {
  it('should return user usage summary', async () => {
    const userUuid = 'user-123';
    const summary = await getUserUsageSummary(userUuid);
    
    expect(summary).toHaveProperty('tier');
    expect(summary).toHaveProperty('projects_count');
    expect(summary).toHaveProperty('projects_limit');
  });
  
  it('should validate project creation limits', async () => {
    const userUuid = 'user-123';
    const result = await canCreateProject(userUuid);
    
    expect(result).toHaveProperty('allowed');
    expect(typeof result.allowed).toBe('boolean');
  });
});
```

### Integration Testing

#### 1. API Integration Tests
```typescript
// __tests__/integration/projects-api.test.ts
import { describe, it, expect } from 'vitest';
import { testApiHandler } from 'next-test-api-route-handler';
import handler from '@/app/api/projects/route';

describe('/api/projects integration', () => {
  it('should create project with valid data', async () => {
    await testApiHandler({
      handler,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: 'Test Project',
            domain: 'example.com'
          })
        });
        
        expect(response.status).toBe(201);
        const data = await response.json();
        expect(data.project.name).toBe('Test Project');
      }
    });
  });
});
```

#### 2. E2E Testing with Playwright
```typescript
// e2e/project-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Project Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/auth/signin');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.click('[data-testid="signin-button"]');
    
    // Navigate to projects
    await page.goto('/my-links');
    await page.click('[data-testid="projects-tab"]');
  });
  
  test('should create new project', async ({ page }) => {
    await page.click('[data-testid="new-project-button"]');
    
    await page.fill('[data-testid="project-name"]', 'Test Project');
    await page.fill('[data-testid="project-domain"]', 'example.com');
    await page.click('[data-testid="create-project-button"]');
    
    await expect(page.locator('[data-testid="project-item"]')).toContainText('Test Project');
  });
  
  test('should show tier limit for free users', async ({ page }) => {
    // Create 5 projects (free tier limit)
    for (let i = 0; i < 5; i++) {
      await page.click('[data-testid="new-project-button"]');
      await page.fill('[data-testid="project-name"]', `Project ${i + 1}`);
      await page.fill('[data-testid="project-domain"]', `example${i + 1}.com`);
      await page.click('[data-testid="create-project-button"]');
    }
    
    // Try to create 6th project
    await page.click('[data-testid="new-project-button"]');
    await page.fill('[data-testid="project-name"]', 'Project 6');
    await page.fill('[data-testid="project-domain"]', 'example6.com');
    await page.click('[data-testid="create-project-button"]');
    
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Plan limit reached');
  });
});
```

### Test Configuration

#### 1. Vitest Configuration
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

#### 2. Test Setup
```typescript
// src/test/setup.ts
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

expect.extend(matchers);

afterEach(() => {
  cleanup();
});
```

---

## Deployment

### Development Deployment

#### 1. Local Development
```bash
# Start development server
pnpm dev

# Start with specific port
pnpm dev -- --port 3001

# Start with turbo mode
pnpm dev --turbo
```

#### 2. Preview Build
```bash
# Build and preview
pnpm build
pnpm start
```

### Production Deployment

#### 1. Cloudflare Pages
```bash
# Build for Cloudflare
pnpm cf:build

# Deploy to Cloudflare Pages
pnpm cf:deploy
```

#### 2. Environment Variables Setup
```bash
# Set production environment variables
wrangler secret put DATABASE_URL
wrangler secret put NEXTAUTH_SECRET
wrangler secret put GOOGLE_CLIENT_ID
wrangler secret put GOOGLE_CLIENT_SECRET
```

#### 3. Database Migration
```bash
# Run production migrations
psql -h production-host -U user -d database -f data/add-user-tiers.sql
```

### CI/CD Pipeline

#### 1. GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm test
      - run: pnpm build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm cf:deploy
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
```

---

## Contributing

### Development Workflow

1. **Fork the repository**
2. **Create feature branch:** `git checkout -b feature/your-feature`
3. **Make changes** following the coding standards
4. **Write tests** for new functionality
5. **Run tests:** `pnpm test`
6. **Create pull request** with detailed description

### Code Review Process

#### 1. Pull Request Requirements
- [ ] Tests pass
- [ ] Code follows style guidelines
- [ ] Documentation updated
- [ ] Tier system changes tested
- [ ] Performance impact considered

#### 2. Code Review Checklist
- [ ] Code quality and maintainability
- [ ] Security implications
- [ ] Performance impact
- [ ] Tier system compliance
- [ ] Error handling
- [ ] Documentation completeness

### Commit Guidelines

#### 1. Commit Message Format
```
type(scope): description

feat(tier): add usage tracking for DR queries
fix(auth): resolve NextAuth session issue
docs(api): update API documentation
```

#### 2. Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `perf`: Performance improvements

---

## Best Practices

### Code Quality

#### 1. TypeScript Best Practices
```typescript
// Use strict types
interface User {
  id: string;
  email: string;
  tier: 'free' | 'paid';
}

// Use type guards
function isValidUser(user: unknown): user is User {
  return typeof user === 'object' && 
         user !== null && 
         'id' in user && 
         'email' in user;
}

// Use discriminated unions
type ApiResponse<T> = 
  | { success: true; data: T }
  | { success: false; error: string };
```

#### 2. Error Handling
```typescript
// Use custom error types
class TierLimitError extends Error {
  constructor(message: string, public currentUsage: number, public limit: number) {
    super(message);
    this.name = 'TierLimitError';
  }
}

// Use Result pattern
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };
```

#### 3. Performance Optimization
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }: { data: any[] }) => {
  return (
    <div>
      {data.map(item => <Item key={item.id} item={item} />)}
    </div>
  );
});

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return data.reduce((acc, item) => acc + item.value, 0);
}, [data]);

// Use useCallback for stable references
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

### Security Guidelines

#### 1. Input Validation
```typescript
// Always validate API inputs
const schema = z.object({
  name: z.string().min(1).max(100),
  domain: z.string().url(),
  tier: z.enum(['free', 'paid'])
});

const validatedData = schema.parse(inputData);
```

#### 2. SQL Injection Prevention
```typescript
// Use parameterized queries
const { data } = await supabase
  .from('projects')
  .select('*')
  .eq('user_id', userUuid)  // Safe parameter
  .ilike('name', `%${searchTerm}%`);  // Safe parameter
```

#### 3. Authentication Security
```typescript
// Always verify authentication
const user = await getAuthenticatedUser();
if (!user?.uuid) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}
```

### Documentation Standards

#### 1. Code Documentation
```typescript
/**
 * Validates user tier access for a specific action
 * @param userUuid - The user's UUID
 * @param action - The action to validate
 * @returns Promise<TierValidationResult> - Validation result with access status
 * @throws {TierLimitError} When user exceeds tier limits
 */
export async function validateTierAccess(
  userUuid: string, 
  action: TierAction
): Promise<TierValidationResult> {
  // Implementation
}
```

#### 2. Component Documentation
```tsx
/**
 * TierQuotaCard - Displays user's tier information and usage quotas
 * 
 * @example
 * ```tsx
 * <TierQuotaCard />
 * ```
 */
export function TierQuotaCard() {
  // Component implementation
}
```

#### 3. API Documentation
```typescript
/**
 * @api {post} /api/projects Create Project
 * @apiName CreateProject
 * @apiGroup Projects
 * @apiVersion 1.0.0
 * 
 * @apiParam {String} name Project name
 * @apiParam {String} domain Project domain
 * @apiParam {String} [description] Project description
 * 
 * @apiSuccess {Object} project Created project object
 * @apiSuccess {String} project.id Project ID
 * @apiSuccess {String} project.name Project name
 * 
 * @apiError {String} error Error message
 * @apiError {String} code Error code
 */
```

---

This comprehensive developer guide provides everything needed to contribute effectively to the MyBacklinks.app project. Follow these guidelines to maintain code quality, security, and consistency across the codebase.