-- LinkTrackPro Database Installation Script
-- Updated to match the latest migration schema refactor
-- This script creates the complete database schema for a fresh installation

BEGIN;

-- ========================================
-- EXTENSIONS AND SCHEMA SETUP
-- ========================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the link_track schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS link_track;

-- Grant permissions on the schema
GRANT USAGE ON SCHEMA link_track TO service_role;
GRANT USAGE ON SCHEMA link_track TO authenticated;
GRANT CREATE ON SCHEMA link_track TO service_role;
GRANT CREATE ON SCHEMA link_track TO authenticated;

-- Grant default permissions for future tables in the schema
ALTER DEFAULT PRIVILEGES IN SCHEMA link_track GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA link_track GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO authenticated;

-- Grant default permissions for future sequences in the schema
ALTER DEFAULT PRIVILEGES IN SCHEMA link_track GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA link_track GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO authenticated;

-- Grant default permissions for future functions in the schema
ALTER DEFAULT PRIVILEGES IN SCHEMA link_track GRANT EXECUTE ON FUNCTIONS TO service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA link_track GRANT EXECUTE ON FUNCTIONS TO authenticated;

-- ========================================
-- USER MANAGEMENT TABLES
-- ========================================

DROP TABLE IF EXISTS link_track.users CASCADE;
CREATE TABLE link_track.users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    nickname VARCHAR(255),
    avatar_url VARCHAR(255),
    locale VARCHAR(50),
    signin_type VARCHAR(50),
    signin_ip VARCHAR(255),
    signin_provider VARCHAR(50),
    signin_openid VARCHAR(255),
    invited_by VARCHAR(255),
    invite_code VARCHAR(50) UNIQUE,
    invites_count INT DEFAULT 0,
    api_rate_limit INT DEFAULT 20, -- Default to 20 requests per hour
    api_requests_current INT DEFAULT 0,
    api_rate_reset TIMESTAMPTZ,
    UNIQUE (email, signin_provider)
);

DROP TABLE IF EXISTS link_track.orders CASCADE;
CREATE TABLE link_track.orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    user_uuid VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) NOT NULL DEFAULT '',
    amount INT NOT NULL,
    interval VARCHAR(50),
    expired_at TIMESTAMPTZ,
    status VARCHAR(50) NOT NULL,
    stripe_session_id VARCHAR(255),
    credits INT NOT NULL,
    currency VARCHAR(50),
    sub_id VARCHAR(255),
    sub_interval_count INT,
    sub_cycle_anchor INT,
    sub_period_end INT,
    sub_period_start INT,
    sub_times INT,
    product_id VARCHAR(255),
    product_name VARCHAR(255),
    valid_months INT,
    order_detail TEXT,
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT
);

DROP TABLE IF EXISTS link_track.apikeys CASCADE;
CREATE TABLE link_track.apikeys (
    id SERIAL PRIMARY KEY,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(100),
    user_uuid VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50),
    requests_count INT DEFAULT 0,
    requests_current_period INT DEFAULT 0,
    rate_limit_reset TIMESTAMPTZ
);

DROP TABLE IF EXISTS link_track.credits CASCADE;
CREATE TABLE link_track.credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,
    order_no VARCHAR(255),
    expired_at TIMESTAMPTZ
);

-- ========================================
-- CORE LINK TRACKING TABLES (NEW ARCHITECTURE)
-- ========================================

-- Projects table - simplified, removed redundant fields
DROP TABLE IF EXISTS link_track.projects CASCADE;
CREATE TABLE link_track.projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL, -- 项目的网站域名，使用 normalizeUrl 函数的 normalized 结果存储，确保去除www前缀并规范化
    description TEXT,
    total_links INTEGER NOT NULL DEFAULT 0, -- 总记录的外链数量，包含用户已提交但是可能没有收录的外链
    indexed_links INTEGER NOT NULL DEFAULT 0, -- 总外链数量，从搜索引擎索引到的外链
    info JSONB, -- 项目详细信息：包含网站图标、项目介绍、sitemap链接、robots.txt链接、域名注册信息等
    is_archived BOOLEAN NOT NULL DEFAULT FALSE, -- 是否存档
    archived_at TIMESTAMPTZ, -- 存档时间
    user_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Link resources table - user's link resources (decoupled from projects)
DROP TABLE IF EXISTS link_track.link_resources CASCADE;
CREATE TABLE link_track.link_resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL, -- 外链资源的完整URL，表示可以提交外链的网址，使用 normalizeUrl 函数归一化存储
    title VARCHAR(500) NOT NULL,
    link_type VARCHAR(50) NOT NULL DEFAULT 'free' CHECK (link_type IN ('free', 'paid')),
    price DECIMAL(10,2),
    currency VARCHAR(10) NOT NULL DEFAULT 'USD',
    source VARCHAR(255), -- guest post, directory, resource page, etc.
    acquisition_method VARCHAR(255), -- outreach, haro, partnership, etc.
    notes TEXT,
    user_id VARCHAR(255) NOT NULL,
    last_checked TIMESTAMPTZ, -- 表示上一次 dr_score 和 traffic 的更新时间
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- All links table - centralized domain statistics (replaces link_stats)  
-- 存储所有链接的最新统计数据，无论链接来源是 projects、link_resources 还是 discovered_links
DROP TABLE IF EXISTS link_track.all_links CASCADE;
CREATE TABLE link_track.all_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL UNIQUE, -- 规范化的域名（去除www等），从各种URL中提取的统一域名标识符
    dr_score INTEGER, -- 域名评级分数，由cron worker定期更新
    traffic INTEGER NOT NULL DEFAULT 0, -- 月访问量估算，由cron worker定期更新
    backlink_count INTEGER NOT NULL DEFAULT 0, -- 外链数量统计，由cron worker定期更新
    is_indexed BOOLEAN NOT NULL DEFAULT FALSE, -- 搜索引擎索引状态
    last_updated TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP -- 最后更新时间
);

-- All links history table - tracking changes over time
-- 存储 all_links 表中域名统计数据的历史变化，用于绘制时间变化曲线
DROP TABLE IF EXISTS link_track.all_links_history CASCADE;
CREATE TABLE link_track.all_links_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL, -- 规范化的域名，与 all_links 表保持一致
    dr_score INTEGER, -- 历史DR分数记录
    traffic INTEGER NOT NULL DEFAULT 0, -- 历史流量数据记录
    backlink_count INTEGER NOT NULL DEFAULT 0, -- 历史外链数量变化记录
    is_indexed BOOLEAN NOT NULL DEFAULT FALSE, -- 历史索引状态记录
    checked_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP -- 数据检查时间点
);

-- Public link resources table - public platforms where users can submit backlinks
-- 存储公开的外链资源平台信息，供用户浏览可提交外链的网站
DROP TABLE IF EXISTS link_track.public_link_resources CASCADE;
CREATE TABLE link_track.public_link_resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL UNIQUE, -- 规范化的域名，与 all_links 表关联获取DR和流量数据
    title VARCHAR(500) NOT NULL, -- 平台名称或标题
    website_url TEXT NOT NULL, -- 完整的网站URL
    submission_method VARCHAR(100) NOT NULL, -- 提交方式：email, form, contact, etc.
    submission_url TEXT, -- 提交页面的具体URL
    contact_email VARCHAR(255), -- 联系邮箱
    is_paid BOOLEAN NOT NULL DEFAULT FALSE, -- 是否付费
    price_range VARCHAR(50), -- 价格范围：如 "$50-100", "Free", etc.
    currency VARCHAR(10) DEFAULT 'USD', -- 货币单位
    category VARCHAR(100), -- 分类：directory, blog, news, resource page, etc.
    description TEXT, -- 平台描述
    requirements TEXT, -- 提交要求或条件
    response_time VARCHAR(50), -- 响应时间：如 "1-3 days", "1 week", etc.
    success_rate INTEGER, -- 成功率百分比 0-100
    last_verified TIMESTAMPTZ, -- 最后验证时间
    is_active BOOLEAN NOT NULL DEFAULT TRUE, -- 是否活跃可用
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Discovered links table - project-specific discovered links (cleaned up)
-- 存储某个项目域名的外部链接发现记录
DROP TABLE IF EXISTS link_track.discovered_links CASCADE;
CREATE TABLE link_track.discovered_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,  -- 外部网站的链接URL，表示指向本项目的外链来源网站，使用 normalizeUrl 函数归一化存储，可能包含路径
    title TEXT NOT NULL, -- 外链页面标题
    anchor_text TEXT NOT NULL, -- 锚文本
    link_type VARCHAR(20) NOT NULL CHECK (link_type IN ('dofollow', 'nofollow')), -- 链接类型
    discovered_at TIMESTAMPTZ NOT NULL, -- 发现时间
    source_url TEXT NOT NULL,   -- 本网站在其他网站被收录的具体链接，表示项目网站的哪个页面被外部网站收录，可能包含路径用于标识具体子页面
    is_active BOOLEAN DEFAULT true, -- 是否活跃
    status VARCHAR(20) NOT NULL DEFAULT 'NEW' CHECK (status IN ('NEW', 'SUBMITTED', 'INDEXED', 'ARCHIVED')), -- 处理状态
    project_id UUID NOT NULL REFERENCES link_track.projects(id) ON DELETE CASCADE, -- 关联的项目ID
    user_id TEXT NOT NULL, -- 用户ID
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(url, project_id) -- 确保同一项目下不会有重复的外链URL
);

-- ========================================
-- USER CONFIGURATION TABLES
-- ========================================

-- User configs table for storing user-specific configuration data
DROP TABLE IF EXISTS link_track.user_configs CASCADE;
CREATE TABLE link_track.user_configs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    project_id UUID NOT NULL REFERENCES link_track.projects(id) ON DELETE CASCADE,
    config_type VARCHAR(50) NOT NULL,
    config_name VARCHAR(255) NOT NULL,
    config_data JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    -- Unique constraint to ensure only one active config per user per project per type
    UNIQUE(user_id, project_id, config_type)
);

-- ========================================
-- CONTENT MANAGEMENT TABLES
-- ========================================

DROP TABLE IF EXISTS link_track.posts CASCADE;
CREATE TABLE link_track.posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50),
    cover_url VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50)
);

-- Items table
DROP TABLE IF EXISTS link_track.items CASCADE;
CREATE TABLE link_track.items(
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    brief VARCHAR(255) NOT NULL,
    item_avatar_url VARCHAR(255),
    user_avatar_url VARCHAR(512) DEFAULT '',
    website_url VARCHAR(255) NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_recommended BOOLEAN NOT NULL DEFAULT FALSE,
    is_official BOOLEAN NOT NULL DEFAULT FALSE,
    clicks INT NOT NULL DEFAULT 0,
    allow_public BOOLEAN NOT NULL DEFAULT FALSE,
    tags VARCHAR(255)[] DEFAULT '{}'::VARCHAR[],
    metadata JSONB DEFAULT '{}'::JSONB
);

-- Item localizations table
DROP TABLE IF EXISTS link_track.item_localizations CASCADE;
CREATE TABLE link_track.item_localizations (
    id SERIAL PRIMARY KEY,
    item_uuid VARCHAR NOT NULL REFERENCES link_track.items(uuid) ON DELETE CASCADE,
    language_code VARCHAR(50) NOT NULL,
    brief VARCHAR(255) NOT NULL,
    detail TEXT NOT NULL,
    processinfo TEXT,
    UNIQUE (item_uuid, language_code)
);

-- Submissions table
DROP TABLE IF EXISTS link_track.submissions CASCADE;
CREATE TABLE link_track.submissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    website_url VARCHAR(512) NOT NULL,
    item_avatar_url VARCHAR(512),
    user_avatar_url VARCHAR(512) DEFAULT '',
    email VARCHAR(255),
    subscribe_newsletter BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMPTZ,
    approved_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    status VARCHAR(50) DEFAULT 'pending',
    detail TEXT,
    preprocessInfo JSONB DEFAULT '{}'::JSONB
);

-- User cases table
DROP TABLE IF EXISTS link_track.user_cases CASCADE;
CREATE TABLE link_track.user_cases (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    url TEXT NOT NULL,
    details TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    ai_summary JSONB DEFAULT '{}'::JSONB,
    content JSONB DEFAULT '{}'::JSONB,
    related_items TEXT[],
    status VARCHAR(50) NOT NULL DEFAULT 'online',
    title VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    image_urls TEXT[],
    video_urls TEXT[]
);

-- ========================================
-- VECTOR SEARCH TABLES
-- ========================================

-- Embedding items table with vector embeddings support
DROP TABLE IF EXISTS link_track.embedding_items CASCADE;
CREATE TABLE link_track.embedding_items (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    item_uuid TEXT NOT NULL,
    language_code TEXT NOT NULL,
    brief_vector VECTOR(1024),
    processinfo_vector VECTOR(1024),
    update_time TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(item_uuid, language_code)
);

-- ========================================
-- EMAIL AND NOTIFICATION TABLES
-- ========================================

-- Email templates table
DROP TABLE IF EXISTS link_track.email_templates CASCADE;
CREATE TABLE link_track.email_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subject TEXT NOT NULL,
    body TEXT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email logs table
DROP TABLE IF EXISTS link_track.email_logs CASCADE;
CREATE TABLE link_track.email_logs (
    id SERIAL PRIMARY KEY,
    uuid UUID NOT NULL,
    template_id INTEGER REFERENCES link_track.email_templates(id),
    recipient VARCHAR(255) NOT NULL,
    subject TEXT NOT NULL,
    body TEXT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE
);

-- ========================================
-- AI TASKS AND PROCESSING TABLES
-- ========================================

-- AI tasks table
DROP TABLE IF EXISTS link_track.ai_tasks CASCADE;
CREATE TABLE link_track.ai_tasks (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(255) UNIQUE NOT NULL,
    user_uuid VARCHAR(64) NOT NULL,
    product_name VARCHAR(20) NOT NULL,
    credit_cost INTEGER NOT NULL DEFAULT 0,
    input_file_path VARCHAR(200) NOT NULL,
    output_image_path VARCHAR(200),
    output_text TEXT,
    output_options JSONB DEFAULT '{}'::JSONB,
    orderstatus VARCHAR(20) NOT NULL,
    fail_reason VARCHAR(500),
    create_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMPTZ,
    cost_time INTEGER NOT NULL DEFAULT 0,
    callback_url VARCHAR(200),
    FOREIGN KEY (user_uuid) REFERENCES link_track.users(uuid) ON DELETE CASCADE
);

-- ========================================
-- ADDITIONAL FEATURE TABLES
-- ========================================

-- Settings table
DROP TABLE IF EXISTS link_track.settings CASCADE;
CREATE TABLE link_track.settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Item tools table
DROP TABLE IF EXISTS link_track.item_tools CASCADE;
CREATE TABLE link_track.item_tools (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    allow_public BOOLEAN NOT NULL DEFAULT TRUE,
    type VARCHAR(10) NOT NULL CHECK (type IN ('sse', 'stdio', 'both')),
    tools JSONB NOT NULL DEFAULT '[]'::JSONB,
    usage JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uuid) REFERENCES link_track.items(uuid) ON DELETE CASCADE
);

-- Affiliate products table
DROP TABLE IF EXISTS link_track.affiliate_products CASCADE;
CREATE TABLE link_track.affiliate_products (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    link VARCHAR(255),
    tags VARCHAR(255)[] DEFAULT '{}'::VARCHAR[],
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    clicks INT NOT NULL DEFAULT 0
);

-- User liked items table
DROP TABLE IF EXISTS link_track.user_liked_items CASCADE;
CREATE TABLE link_track.user_liked_items (
    uuid VARCHAR(255) DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL REFERENCES link_track.users(uuid) ON DELETE CASCADE,
    item_uuid VARCHAR(255) NOT NULL REFERENCES link_track.items(uuid) ON DELETE CASCADE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_uuid, item_uuid)
);

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Indexes for projects table
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON link_track.projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_domain ON link_track.projects(domain);
CREATE INDEX IF NOT EXISTS idx_projects_is_archived ON link_track.projects(is_archived);
CREATE INDEX IF NOT EXISTS idx_projects_user_archived ON link_track.projects(user_id, is_archived);

-- Indexes for link_resources table
CREATE INDEX IF NOT EXISTS idx_link_resources_user_id ON link_track.link_resources(user_id);
CREATE INDEX IF NOT EXISTS idx_link_resources_url ON link_track.link_resources USING hash(url);
CREATE INDEX IF NOT EXISTS idx_link_resources_link_type ON link_track.link_resources(link_type);

-- Indexes for all_links table
CREATE INDEX IF NOT EXISTS idx_all_links_domain ON link_track.all_links(domain);
CREATE INDEX IF NOT EXISTS idx_all_links_dr_score ON link_track.all_links(dr_score);
CREATE INDEX IF NOT EXISTS idx_all_links_traffic ON link_track.all_links(traffic);
CREATE INDEX IF NOT EXISTS idx_all_links_last_updated ON link_track.all_links(last_updated);

-- Indexes for all_links_history table
CREATE INDEX IF NOT EXISTS idx_all_links_history_domain ON link_track.all_links_history(domain);
CREATE INDEX IF NOT EXISTS idx_all_links_history_checked_at ON link_track.all_links_history(checked_at);

-- Indexes for public_link_resources table
CREATE INDEX IF NOT EXISTS idx_public_link_resources_domain ON link_track.public_link_resources(domain);
CREATE INDEX IF NOT EXISTS idx_public_link_resources_category ON link_track.public_link_resources(category);
CREATE INDEX IF NOT EXISTS idx_public_link_resources_is_paid ON link_track.public_link_resources(is_paid);
CREATE INDEX IF NOT EXISTS idx_public_link_resources_is_active ON link_track.public_link_resources(is_active);
CREATE INDEX IF NOT EXISTS idx_public_link_resources_success_rate ON link_track.public_link_resources(success_rate);
CREATE INDEX IF NOT EXISTS idx_public_link_resources_last_verified ON link_track.public_link_resources(last_verified);

-- Indexes for discovered_links table
CREATE INDEX IF NOT EXISTS idx_discovered_links_project_id ON link_track.discovered_links(project_id);
CREATE INDEX IF NOT EXISTS idx_discovered_links_user_id ON link_track.discovered_links(user_id);
CREATE INDEX IF NOT EXISTS idx_discovered_links_discovered_at ON link_track.discovered_links(discovered_at);
CREATE INDEX IF NOT EXISTS idx_discovered_links_is_active ON link_track.discovered_links(is_active);
CREATE INDEX IF NOT EXISTS idx_discovered_links_url ON link_track.discovered_links USING hash(url);

-- Indexes for user_configs table
CREATE INDEX IF NOT EXISTS idx_user_configs_user_id ON link_track.user_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_configs_project_id ON link_track.user_configs(project_id);
CREATE INDEX IF NOT EXISTS idx_user_configs_type ON link_track.user_configs(config_type);
CREATE INDEX IF NOT EXISTS idx_user_configs_active ON link_track.user_configs(is_active);
CREATE INDEX IF NOT EXISTS idx_user_configs_user_project_type ON link_track.user_configs(user_id, project_id, config_type);
CREATE INDEX IF NOT EXISTS idx_user_configs_user_project_type_active ON link_track.user_configs(user_id, project_id, config_type, is_active);

-- Indexes for email logs
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON link_track.email_logs (status);
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON link_track.email_logs (recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_event_type ON link_track.email_logs (event_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_template_id ON link_track.email_logs (template_id);

-- Indexes for AI tasks
CREATE INDEX IF NOT EXISTS idx_ai_tasks_user_uuid ON link_track.ai_tasks(user_uuid);
CREATE INDEX IF NOT EXISTS idx_ai_tasks_orderstatus ON link_track.ai_tasks(orderstatus);
CREATE INDEX IF NOT EXISTS idx_ai_tasks_create_time ON link_track.ai_tasks(create_time);

-- ========================================
-- FOREIGN KEY CONSTRAINTS
-- ========================================

-- Add foreign key constraint between public_link_resources and all_links
-- This enables Supabase/PostgREST to properly join these tables
ALTER TABLE link_track.public_link_resources 
ADD CONSTRAINT fk_public_link_resources_domain 
FOREIGN KEY (domain) REFERENCES link_track.all_links(domain) 
ON UPDATE CASCADE ON DELETE RESTRICT;

-- ========================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ========================================

-- Create trigger function for updating updated_at columns
CREATE OR REPLACE FUNCTION link_track.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for projects
DROP TRIGGER IF EXISTS update_projects_updated_at ON link_track.projects;
CREATE TRIGGER update_projects_updated_at
    BEFORE UPDATE ON link_track.projects
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Triggers for link_resources
DROP TRIGGER IF EXISTS update_link_resources_updated_at ON link_track.link_resources;
CREATE TRIGGER update_link_resources_updated_at
    BEFORE UPDATE ON link_track.link_resources
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Triggers for discovered_links
DROP TRIGGER IF EXISTS update_discovered_links_updated_at ON link_track.discovered_links;
CREATE TRIGGER update_discovered_links_updated_at
    BEFORE UPDATE ON link_track.discovered_links
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Triggers for user_configs
DROP TRIGGER IF EXISTS update_user_configs_updated_at ON link_track.user_configs;
CREATE TRIGGER update_user_configs_updated_at
    BEFORE UPDATE ON link_track.user_configs
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Triggers for public_link_resources
DROP TRIGGER IF EXISTS update_public_link_resources_updated_at ON link_track.public_link_resources;
CREATE TRIGGER update_public_link_resources_updated_at
    BEFORE UPDATE ON link_track.public_link_resources
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Trigger for users
DROP TRIGGER IF EXISTS update_users_updated_at ON link_track.users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON link_track.users
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Trigger for AI tasks
CREATE OR REPLACE FUNCTION link_track.update_ai_tasks_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_ai_tasks_modtime ON link_track.ai_tasks;
CREATE TRIGGER update_ai_tasks_modtime
BEFORE UPDATE ON link_track.ai_tasks
FOR EACH ROW
EXECUTE FUNCTION link_track.update_ai_tasks_modified_column();

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Function to get domain stats from all_links
CREATE OR REPLACE FUNCTION link_track.get_domain_stats(p_domain VARCHAR(255))
RETURNS TABLE(dr_score INTEGER, traffic INTEGER, is_indexed BOOLEAN) AS $$
BEGIN
    RETURN QUERY
    SELECT al.dr_score, al.traffic, al.is_indexed
    FROM link_track.all_links al
    WHERE al.domain = p_domain;
END;
$$ LANGUAGE plpgsql;

-- Function to update domain stats in all_links
CREATE OR REPLACE FUNCTION link_track.update_domain_stats(
    p_domain VARCHAR(255),
    p_dr_score INTEGER DEFAULT NULL,
    p_traffic INTEGER DEFAULT 0,
    p_is_indexed BOOLEAN DEFAULT FALSE
)
RETURNS VOID AS $$
BEGIN
    -- Insert into history
    INSERT INTO link_track.all_links_history (domain, dr_score, traffic, is_indexed, checked_at)
    VALUES (p_domain, p_dr_score, p_traffic, p_is_indexed, NOW());
    
    -- Update or insert current stats
    INSERT INTO link_track.all_links (domain, dr_score, traffic, is_indexed, last_updated)
    VALUES (p_domain, p_dr_score, p_traffic, p_is_indexed, NOW())
    ON CONFLICT (domain) DO UPDATE SET
        dr_score = COALESCE(EXCLUDED.dr_score, link_track.all_links.dr_score),
        traffic = EXCLUDED.traffic,
        is_indexed = EXCLUDED.is_indexed,
        last_updated = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update domain stats with backlink count
CREATE OR REPLACE FUNCTION link_track.update_domain_stats_with_backlinks(
    p_domain VARCHAR(255),
    p_dr_score INTEGER DEFAULT NULL,
    p_traffic INTEGER DEFAULT 0,
    p_is_indexed BOOLEAN DEFAULT FALSE,
    p_backlink_count INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
    -- Insert into history
    INSERT INTO link_track.all_links_history (domain, dr_score, traffic, backlink_count, is_indexed, checked_at)
    VALUES (p_domain, p_dr_score, p_traffic, p_backlink_count, p_is_indexed, NOW());
    
    -- Update or insert current stats
    INSERT INTO link_track.all_links (domain, dr_score, traffic, backlink_count, is_indexed, last_updated)
    VALUES (p_domain, p_dr_score, p_traffic, p_backlink_count, p_is_indexed, NOW())
    ON CONFLICT (domain) DO UPDATE SET
        dr_score = COALESCE(EXCLUDED.dr_score, link_track.all_links.dr_score),
        traffic = EXCLUDED.traffic,
        backlink_count = EXCLUDED.backlink_count,
        is_indexed = EXCLUDED.is_indexed,
        last_updated = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update project stats
-- Drop any existing versions to avoid function overloading issues
DROP FUNCTION IF EXISTS link_track.update_project_stats(p_project_id INTEGER);
DROP FUNCTION IF EXISTS link_track.update_project_stats(INTEGER);
DROP FUNCTION IF EXISTS link_track.update_project_stats(project_id INTEGER);

CREATE OR REPLACE FUNCTION link_track.update_project_stats(p_project_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE link_track.projects
    SET 
        total_links = (
            SELECT COUNT(*) 
            FROM link_track.discovered_links 
            WHERE project_id = p_project_id
        ),
        indexed_links = (
            SELECT COUNT(*) 
            FROM link_track.discovered_links dl
            JOIN link_track.all_links al ON al.domain = (
                CASE WHEN dl.url LIKE 'http%' 
                THEN (regexp_match(dl.url, '^https?://([^/]+)'))[1]
                ELSE dl.url END
            )
            WHERE dl.project_id = p_project_id AND al.is_indexed = true
        ),
        updated_at = NOW()
    WHERE id = p_project_id;
END;
$$ LANGUAGE plpgsql;

-- Function to extract domain from URL
CREATE OR REPLACE FUNCTION link_track.extract_domain(p_url TEXT)
RETURNS VARCHAR(255) AS $$
BEGIN
    IF p_url LIKE 'http%' THEN
        RETURN (regexp_match(p_url, '^https?://([^/]+)'))[1];
    ELSE
        RETURN p_url;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to atomically increment clicks for an ITEM
CREATE OR REPLACE FUNCTION link_track.increment_item_clicks(item_uuid_param VARCHAR)
RETURNS void AS $$
BEGIN
  UPDATE link_track.items
  SET clicks = clicks + 1
  WHERE uuid = item_uuid_param;
END;
$$ LANGUAGE plpgsql;

-- Function to get random public items
CREATE OR REPLACE FUNCTION link_track.get_random_public_items(limit_count INTEGER)
RETURNS SETOF link_track.items AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM link_track.items
  WHERE allow_public = true
  ORDER BY RANDOM()
  LIMIT LEAST(limit_count, 50);
END;
$$ LANGUAGE plpgsql;

-- Function to get all item tags with counts
CREATE OR REPLACE FUNCTION link_track.get_all_item_tags()
RETURNS TABLE(tag VARCHAR, count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT t.tag, COUNT(t.tag) AS count
  FROM (
    SELECT unnest(tags) AS tag
    FROM link_track.items
    WHERE allow_public = true
  ) t
  GROUP BY t.tag
  ORDER BY count DESC, tag ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to get specific tag counts
CREATE OR REPLACE FUNCTION link_track.get_specific_tag_counts(tags_param VARCHAR[])
RETURNS TABLE(tag VARCHAR, count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT t.tag, COUNT(t.tag) AS count
  FROM (
    SELECT unnest(tags) AS tag
    FROM link_track.items
    WHERE allow_public = true
  ) t
  WHERE t.tag = ANY(tags_param)
  GROUP BY t.tag
  ORDER BY count DESC, tag ASC;
END;
$$ LANGUAGE plpgsql;

-- Vector search functions
CREATE OR REPLACE FUNCTION link_track.match_items_localized(
  query_embedding vector(1024),
  match_threshold float,
  match_count int,
  p_language_code text
)
RETURNS TABLE (
  uuid text,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    item.item_uuid as uuid,
    1 - (item.brief_vector <=> query_embedding) AS similarity
  FROM embedding_items item
  WHERE 
    item.language_code = p_language_code AND
    item.brief_vector IS NOT NULL AND
    1 - (item.brief_vector <=> query_embedding) > match_threshold
  ORDER BY item.brief_vector <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Function to count matching items
CREATE OR REPLACE FUNCTION link_track.count_matching_items_localized(
  query_embedding vector(1024),
  match_threshold float,
  p_language_code text
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  match_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO match_count
  FROM embedding_items item
  WHERE 
    item.language_code = p_language_code AND
    item.brief_vector IS NOT NULL AND
    1 - (item.brief_vector <=> query_embedding) > match_threshold;
  RETURN match_count;
END;
$$;

-- Function to create AI task with credit deduction
CREATE OR REPLACE FUNCTION link_track.create_ai_task_with_credit_deduction(
    p_user_uuid VARCHAR,
    p_product_name VARCHAR,
    p_credit_cost INT,
    p_input_file_path VARCHAR,
    p_output_options JSONB DEFAULT '{}'::JSONB,
    p_callback_url VARCHAR DEFAULT NULL
)
RETURNS link_track.ai_tasks
LANGUAGE plpgsql
AS $$
DECLARE
    v_current_balance INT;
    v_new_order_no VARCHAR;
    v_new_trans_no VARCHAR;
    v_created_task link_track.ai_tasks%ROWTYPE;
BEGIN
    v_new_order_no := uuid_generate_v4();
    v_new_trans_no := uuid_generate_v4();

    SELECT COALESCE(SUM(credits), 0)
    INTO v_current_balance
    FROM link_track.credits
    WHERE user_uuid = p_user_uuid;

    IF v_current_balance < p_credit_cost THEN
        RAISE EXCEPTION USING ERRCODE = 'P0001', MESSAGE = 'INSUFFICIENT_CREDITS';
    END IF;

    INSERT INTO link_track.credits (trans_no, user_uuid, trans_type, credits, order_no, created_at)
    VALUES (v_new_trans_no, p_user_uuid, 'AI_TASK_USAGE', -p_credit_cost, v_new_order_no, NOW());

    INSERT INTO link_track.ai_tasks (
        order_no, user_uuid, product_name, credit_cost, input_file_path,
        output_options, callback_url, orderstatus, create_time, update_time, cost_time
    )
    VALUES (
        v_new_order_no, p_user_uuid, p_product_name, p_credit_cost, p_input_file_path,
        p_output_options, p_callback_url, 'PENDING', NOW(), NOW(), 0
    )
    RETURNING * INTO v_created_task;

    RETURN v_created_task;

EXCEPTION
    WHEN SQLSTATE 'P0001' THEN
        RAISE EXCEPTION 'INSUFFICIENT_CREDITS';
    WHEN OTHERS THEN
        RAISE;
END;
$$;

-- Function to increment affiliate clicks
CREATE OR REPLACE FUNCTION link_track.increment_affiliate_clicks(product_id INT)
RETURNS VOID AS $$
BEGIN
    UPDATE link_track.affiliate_products
    SET clicks = clicks + 1
    WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get user credit balance
CREATE OR REPLACE FUNCTION link_track.get_user_credit_balance(p_user_uuid VARCHAR)
RETURNS INTEGER AS $$
DECLARE
    balance INTEGER;
BEGIN
    SELECT COALESCE(SUM(credits), 0)
    INTO balance
    FROM link_track.credits
    WHERE user_uuid = p_user_uuid
    AND (expired_at IS NULL OR expired_at > NOW());
    
    RETURN balance;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has sufficient credits
CREATE OR REPLACE FUNCTION link_track.has_sufficient_credits(
    p_user_uuid VARCHAR,
    p_required_credits INTEGER
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN link_track.get_user_credit_balance(p_user_uuid) >= p_required_credits;
END;
$$ LANGUAGE plpgsql;

-- User configuration functions
CREATE OR REPLACE FUNCTION link_track.get_user_config(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_config_type VARCHAR(50)
)
RETURNS link_track.user_configs AS $$
DECLARE
    result link_track.user_configs%ROWTYPE;
BEGIN
    SELECT * INTO result
    FROM link_track.user_configs
    WHERE user_id = p_user_id 
    AND project_id = p_project_id
    AND config_type = p_config_type 
    AND is_active = TRUE;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION link_track.save_user_config(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_config_type VARCHAR(50),
    p_config_name VARCHAR(255),
    p_config_data JSONB
)
RETURNS link_track.user_configs AS $$
DECLARE
    result link_track.user_configs%ROWTYPE;
BEGIN
    INSERT INTO link_track.user_configs (user_id, project_id, config_type, config_name, config_data, is_active, created_at, updated_at)
    VALUES (p_user_id, p_project_id, p_config_type, p_config_name, p_config_data, TRUE, NOW(), NOW())
    ON CONFLICT (user_id, project_id, config_type)
    DO UPDATE SET
        config_name = EXCLUDED.config_name,
        config_data = EXCLUDED.config_data,
        is_active = TRUE,
        updated_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION link_track.delete_user_config(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_config_type VARCHAR(50)
)
RETURNS BOOLEAN AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM link_track.user_configs
    WHERE user_id = p_user_id AND project_id = p_project_id AND config_type = p_config_type;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- VIEWS FOR EASIER QUERYING
-- ========================================

-- Analytics configurations view
CREATE OR REPLACE VIEW link_track.analytics_configs AS
SELECT 
    uc.user_id,
    uc.project_id,
    uc.config_type,
    uc.config_name,
    uc.config_data,
    uc.is_active,
    uc.created_at,
    uc.updated_at,
    p.name as project_name,
    p.domain as project_domain,
    CASE 
        WHEN uc.config_type = 'analytics_plausible' THEN 'Plausible'
        WHEN uc.config_type = 'analytics_google' THEN 'Google Analytics'
        WHEN uc.config_type = 'analytics_umami' THEN 'Umami'
        ELSE 'Unknown'
    END as provider_name
FROM link_track.user_configs uc
JOIN link_track.projects p ON uc.project_id = p.id
WHERE uc.config_type LIKE 'analytics_%'
AND uc.is_active = TRUE;

-- ========================================
-- CONSTRAINTS AND UNIQUE INDEXES
-- ========================================

-- Unique constraint for email templates
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_active_template_per_event 
ON link_track.email_templates (event_type) 
WHERE is_active = true;

-- ========================================
-- DEFAULT DATA INSERTIONS
-- ========================================

-- Insert default settings
INSERT INTO link_track.settings (key, value, updated_at)
VALUES ('invitation_credits', '5', CURRENT_TIMESTAMP)
ON CONFLICT (key) DO NOTHING;

-- Insert default email templates
INSERT INTO link_track.email_templates (name, subject, body, event_type, is_active)
VALUES
(
    'ITEM Submission Confirmation',
    'Your ITEM "{{ITEM_NAME}}" has been submitted successfully',
    '<div style="font-family: sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <h2 style="color: #333;">ITEM Submission Confirmation</h2>
        <p>Dear {{RECIPIENT_NAME}},</p>
        <p>Thank you for submitting your ITEM <strong>{{ITEM_NAME}}</strong> to our platform.</p>
        <p>Our team will review your submission and get back to you soon. You will receive another email once your ITEM is approved and published.</p>
        <p>ITEM Details:</p>
        <ul>
            <li><strong>Name:</strong> {{ITEM_NAME}}</li>
            <li><strong>Author:</strong> {{ITEM_AUTHOR}}</li>
            <li><strong>URL:</strong> <a href="{{ITEM_URL}}">{{ITEM_URL}}</a></li>
        </ul>
        <p>If you have any questions, please don''t hesitate to contact us.</p>
        <p>Thank you,<br>The ITEM Hub Team</p>
        <hr style="border: none; border-top: 1px solid #eaeaea; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">This is an automated message from <a href="{{SITE_URL}}">ITEM Hub</a>.</p>
    </div>',
    'item_submitted',
    true
),
(
    'ITEM Published Notification',
    'Your ITEM "{{ITEM_NAME}}" is now published on ITEM Hub',
    '<div style="font-family: sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <h2 style="color: #333;">Your ITEM is Now Published!</h2>
        <p>Dear {{RECIPIENT_NAME}},</p>
        <p>We''re excited to inform you that your ITEM <strong>{{ITEM_NAME}}</strong> has been reviewed and is now published on the ITEM Hub platform!</p>
        <p>You can view your published ITEM here: <a href="{{ITEM_LINK}}">{{ITEM_LINK}}</a></p>
        <p>ITEM Details:</p>
        <ul>
            <li><strong>Name:</strong> {{ITEM_NAME}}</li>
            <li><strong>Author:</strong> {{ITEM_AUTHOR}}</li>
            <li><strong>Original URL:</strong> <a href="{{ITEM_URL}}">{{ITEM_URL}}</a></li>
        </ul>
        <p>Thank you for contributing to the ITEM Hub community!</p>
        <p>Best regards,<br>The ITEM Hub Team</p>
        <hr style="border: none; border-top: 1px solid #eaeaea; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">This is an automated message from <a href="{{SITE_URL}}">ITEM Hub</a>.</p>
    </div>',
    'item_published',
    true
)
ON CONFLICT (event_type) WHERE is_active = true DO NOTHING;

-- ========================================
-- PERMISSIONS GRANTS
-- ========================================

-- Grant permissions for all tables
DO $$
DECLARE
    table_name TEXT;
BEGIN
    FOR table_name IN 
        SELECT schemaname||'.'||tablename 
        FROM pg_tables 
        WHERE schemaname = 'link_track'
    LOOP
        EXECUTE 'GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE ' || table_name || ' TO service_role';
        EXECUTE 'GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE ' || table_name || ' TO authenticated';
    END LOOP;
END $$;

-- Grant permissions for all sequences
DO $$ 
DECLARE 
    seq_name TEXT;
BEGIN
    FOR seq_name IN 
        SELECT schemaname||'.'||sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'link_track'
    LOOP
        EXECUTE 'GRANT USAGE, SELECT, UPDATE ON SEQUENCE ' || seq_name || ' TO service_role';
        EXECUTE 'GRANT USAGE, SELECT, UPDATE ON SEQUENCE ' || seq_name || ' TO authenticated';
    END LOOP;
END $$;

-- Grant permissions for all functions
DO $$
DECLARE
    func_name TEXT;
BEGIN
    FOR func_name IN 
        SELECT schemaname||'.'||proname||'('||pg_get_function_identity_arguments(p.oid)||')'
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'link_track'
    LOOP
        EXECUTE 'GRANT EXECUTE ON FUNCTION ' || func_name || ' TO service_role';
        EXECUTE 'GRANT EXECUTE ON FUNCTION ' || func_name || ' TO authenticated';
    END LOOP;
END $$;

-- Explicitly grant permissions for the new function
GRANT EXECUTE ON FUNCTION link_track.update_domain_stats_with_backlinks(VARCHAR, INTEGER, INTEGER, BOOLEAN, INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.update_domain_stats_with_backlinks(VARCHAR, INTEGER, INTEGER, BOOLEAN, INTEGER) TO authenticated;

-- Grant permissions for views
GRANT SELECT ON link_track.analytics_configs TO service_role;
GRANT SELECT ON link_track.analytics_configs TO authenticated;

COMMIT;

-- ========================================
-- FINAL VERIFICATION AND COMMENTS
-- ========================================

-- Add comments for documentation
COMMENT ON SCHEMA link_track IS 'LinkTrackPro main schema containing all application tables, functions, and configurations';
COMMENT ON TABLE link_track.user_configs IS 'Stores project-specific user configuration data including analytics integrations bound to specific projects';
COMMENT ON TABLE link_track.projects IS 'User projects for organizing and tracking discovered links. The domain field stores normalized project website domains using normalizeUrl.';
COMMENT ON TABLE link_track.link_resources IS 'User link resources - decoupled from projects for flexible management. The url field stores normalized external link resource URLs where users can submit backlinks.';
COMMENT ON TABLE link_track.all_links IS 'Centralized domain statistics for DR scores, traffic, and indexing status. All domains from projects, link_resources, and discovered_links are aggregated here.';
COMMENT ON TABLE link_track.all_links_history IS 'Historical tracking of domain statistics changes over time for chart visualization and trend analysis.';
COMMENT ON TABLE link_track.discovered_links IS 'Project-specific discovered links. The url field stores external websites linking to the project, and source_url stores the specific project page being linked to.';
COMMENT ON TABLE link_track.public_link_resources IS 'Public link resource platforms where users can submit backlinks. Domain stats (DR, traffic) are retrieved from all_links table by domain association.';

-- Final verification message
DO $$
BEGIN
    RAISE NOTICE 'LinkTrackPro database installation completed successfully!';
    RAISE NOTICE 'Schema: link_track';
    RAISE NOTICE 'New Architecture Tables: projects, link_resources, all_links, all_links_history, discovered_links';
    RAISE NOTICE 'Tables created: %, Functions created: %', 
        (SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'link_track'),
        (SELECT COUNT(*) FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = 'link_track');
    RAISE NOTICE 'Ready for production use with the latest refactored schema!';
END $$;