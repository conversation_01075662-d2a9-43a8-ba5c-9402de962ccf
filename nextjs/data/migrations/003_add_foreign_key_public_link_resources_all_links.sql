-- Migration to add foreign key relationship between public_link_resources and all_links
-- This enables Supabase/PostgREST to properly join these tables

-- First, ensure all domains in public_link_resources exist in all_links
INSERT INTO link_track.all_links (domain, traffic, backlink_count, is_indexed, last_updated)
SELECT DISTINCT plr.domain, 0, 0, false, NOW()
FROM link_track.public_link_resources plr
WHERE plr.domain NOT IN (SELECT domain FROM link_track.all_links)
ON CONFLICT (domain) DO NOTHING;

-- Add foreign key constraint
ALTER TABLE link_track.public_link_resources 
ADD CONSTRAINT fk_public_link_resources_domain 
FOREIGN KEY (domain) REFERENCES link_track.all_links(domain) 
ON UPDATE CASCADE ON DELETE RESTRICT;

-- Grant permissions (if needed)
GRANT SELECT ON link_track.all_links TO authenticated;
GRANT SELECT ON link_track.public_link_resources TO authenticated; 