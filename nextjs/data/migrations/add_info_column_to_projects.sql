-- Migration: Add info column to projects table
-- This should be run when database permissions allow schema changes

ALTER TABLE "link_track"."projects" 
ADD COLUMN IF NOT EXISTS info jsonb DEFAULT '{}'::jsonb;

-- Add a comment to explain what this column stores
COMMENT ON COLUMN "link_track"."projects".info IS 'JSON object storing additional project information like favicon, introduction, sitemaps, robotsTxt, and domainInfo'; 