-- Create Domain Management System
-- Migration: 004_create_domain_management.sql
-- Purpose: Create dedicated domain management table and related structures

BEGIN;

-- ========================================
-- DOMAIN MANAGEMENT TABLE
-- ========================================

-- Create domain management table
DROP TABLE IF EXISTS link_track.domain_management CASCADE;
CREATE TABLE link_track.domain_management (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL, -- Normalized domain name (without www, protocol)
    user_id VARCHAR(255) NOT NULL, -- Owner of the domain record
    
    -- Registration Information
    registrar VARCHAR(255), -- Domain registrar (GoDaddy, Namecheap, etc.)
    created_date DATE, -- Registration date
    expiry_date DATE, -- Expiration date
    registration_price DECIMAL(10,2), -- Initial registration cost
    renewal_price DECIMAL(10,2), -- Annual renewal cost
    currency VARCHAR(10) DEFAULT 'USD', -- Currency for pricing
    auto_renew BOOLEAN DEFAULT FALSE, -- Auto-renewal status
    
    -- Technical Information
    dns_provider VARCHAR(255), -- DNS provider (Cloudflare, AWS Route 53, etc.)
    name_servers TEXT[], -- Array of nameserver URLs
    status VARCHAR(50) DEFAULT 'active', -- Domain status (active, expired, pending, etc.)
    
    -- WHOIS Data Cache
    whois_data JSONB, -- Complete WHOIS response data
    whois_last_updated TIMESTAMPTZ, -- Last WHOIS update timestamp
    whois_cache_expires TIMESTAMPTZ, -- When to refresh WHOIS data (30 days)
    
    -- Monitoring & Alerts
    monitor_expiry BOOLEAN DEFAULT TRUE, -- Monitor for expiry alerts
    alert_days_before INTEGER DEFAULT 30, -- Days before expiry to alert
    last_alert_sent TIMESTAMPTZ, -- Last expiry alert timestamp
    
    -- Metadata
    notes TEXT, -- User notes about the domain
    tags VARCHAR(100)[] DEFAULT '{}', -- Domain tags for organization
    is_favorite BOOLEAN DEFAULT FALSE, -- User favorite status
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(domain, user_id) -- One domain record per user
);

-- ========================================
-- DOMAIN-PROJECT ASSOCIATIONS
-- ========================================

-- Create junction table for domain-project relationships
DROP TABLE IF EXISTS link_track.domain_project_associations CASCADE;
CREATE TABLE link_track.domain_project_associations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_id UUID NOT NULL REFERENCES link_track.domain_management(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES link_track.projects(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- For efficient querying
    is_primary BOOLEAN DEFAULT FALSE, -- Primary domain for the project
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(domain_id, project_id) -- One association per domain-project pair
);

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Domain management indexes
CREATE INDEX idx_domain_management_user_id ON link_track.domain_management(user_id);
CREATE INDEX idx_domain_management_domain ON link_track.domain_management(domain);
CREATE INDEX idx_domain_management_expiry_date ON link_track.domain_management(expiry_date);
CREATE INDEX idx_domain_management_status ON link_track.domain_management(status);
CREATE INDEX idx_domain_management_whois_expires ON link_track.domain_management(whois_cache_expires);
CREATE INDEX idx_domain_management_monitor_expiry ON link_track.domain_management(monitor_expiry, expiry_date);
CREATE INDEX idx_domain_management_user_domain ON link_track.domain_management(user_id, domain);

-- Domain-project association indexes
CREATE INDEX idx_domain_project_associations_domain_id ON link_track.domain_project_associations(domain_id);
CREATE INDEX idx_domain_project_associations_project_id ON link_track.domain_project_associations(project_id);
CREATE INDEX idx_domain_project_associations_user_id ON link_track.domain_project_associations(user_id);
CREATE INDEX idx_domain_project_associations_primary ON link_track.domain_project_associations(project_id, is_primary);

-- Partial unique index to ensure only one primary domain per project
CREATE UNIQUE INDEX idx_domain_project_associations_unique_primary 
ON link_track.domain_project_associations(project_id) 
WHERE is_primary = TRUE;

-- ========================================
-- TRIGGERS AND FUNCTIONS
-- ========================================

-- Trigger for updating updated_at column
DROP TRIGGER IF EXISTS update_domain_management_updated_at ON link_track.domain_management;
CREATE TRIGGER update_domain_management_updated_at
    BEFORE UPDATE ON link_track.domain_management
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Function to set WHOIS cache expiration
CREATE OR REPLACE FUNCTION link_track.set_whois_cache_expiration()
RETURNS TRIGGER AS $$
BEGIN
    -- Set cache to expire in 30 days when WHOIS data is updated
    IF NEW.whois_data IS NOT NULL AND (OLD.whois_data IS NULL OR NEW.whois_data != OLD.whois_data) THEN
        NEW.whois_last_updated = NOW();
        NEW.whois_cache_expires = NOW() + INTERVAL '30 days';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for WHOIS cache management
DROP TRIGGER IF EXISTS trigger_whois_cache_expiration ON link_track.domain_management;
CREATE TRIGGER trigger_whois_cache_expiration
    BEFORE INSERT OR UPDATE ON link_track.domain_management
    FOR EACH ROW
    EXECUTE FUNCTION link_track.set_whois_cache_expiration();

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Function to check if domain WHOIS needs refresh
CREATE OR REPLACE FUNCTION link_track.domain_needs_whois_refresh(p_domain_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    cache_expires TIMESTAMPTZ;
BEGIN
    SELECT whois_cache_expires INTO cache_expires
    FROM link_track.domain_management
    WHERE id = p_domain_id;
    
    -- Needs refresh if no cache or cache expired
    RETURN cache_expires IS NULL OR cache_expires <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to get domains expiring soon
CREATE OR REPLACE FUNCTION link_track.get_expiring_domains(p_user_id VARCHAR(255), p_days_ahead INTEGER DEFAULT 30)
RETURNS TABLE(
    id UUID,
    domain VARCHAR(255),
    expiry_date DATE,
    days_until_expiry INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dm.id,
        dm.domain,
        dm.expiry_date,
        (dm.expiry_date - CURRENT_DATE)::INTEGER as days_until_expiry
    FROM link_track.domain_management dm
    WHERE dm.user_id = p_user_id
    AND dm.monitor_expiry = TRUE
    AND dm.expiry_date IS NOT NULL
    AND dm.expiry_date <= CURRENT_DATE + p_days_ahead
    AND dm.expiry_date >= CURRENT_DATE
    ORDER BY dm.expiry_date ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to get domain statistics for a user
CREATE OR REPLACE FUNCTION link_track.get_user_domain_statistics(p_user_id VARCHAR(255))
RETURNS TABLE(
    total_domains INTEGER,
    active_domains INTEGER,
    expired_domains INTEGER,
    expiring_soon INTEGER,
    total_projects INTEGER,
    avg_renewal_price DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_domains,
        COUNT(CASE WHEN dm.status = 'active' AND (dm.expiry_date IS NULL OR dm.expiry_date > CURRENT_DATE) THEN 1 END)::INTEGER as active_domains,
        COUNT(CASE WHEN dm.expiry_date IS NOT NULL AND dm.expiry_date < CURRENT_DATE THEN 1 END)::INTEGER as expired_domains,
        COUNT(CASE WHEN dm.expiry_date IS NOT NULL AND dm.expiry_date BETWEEN CURRENT_DATE AND CURRENT_DATE + 30 THEN 1 END)::INTEGER as expiring_soon,
        (SELECT COUNT(DISTINCT dpa.project_id)::INTEGER FROM link_track.domain_project_associations dpa 
         JOIN link_track.domain_management dm2 ON dpa.domain_id = dm2.id 
         WHERE dm2.user_id = p_user_id) as total_projects,
        AVG(dm.renewal_price) as avg_renewal_price
    FROM link_track.domain_management dm
    WHERE dm.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- VIEWS FOR EASIER QUERYING
-- ========================================

-- Domain management with project information view
CREATE OR REPLACE VIEW link_track.domain_management_with_projects AS
SELECT 
    dm.*,
    COALESCE(
        json_agg(
            json_build_object(
                'project_id', p.id,
                'project_name', p.name,
                'project_domain', p.domain,
                'is_primary', dpa.is_primary
            )
        ) FILTER (WHERE p.id IS NOT NULL), 
        '[]'::json
    ) as associated_projects,
    COUNT(dpa.project_id) as project_count
FROM link_track.domain_management dm
LEFT JOIN link_track.domain_project_associations dpa ON dm.id = dpa.domain_id
LEFT JOIN link_track.projects p ON dpa.project_id = p.id
GROUP BY dm.id;

-- ========================================
-- PERMISSIONS
-- ========================================

-- Grant permissions on new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_management TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_management TO authenticated;

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_project_associations TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_project_associations TO authenticated;

-- Grant permissions on new functions
GRANT EXECUTE ON FUNCTION link_track.domain_needs_whois_refresh(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.domain_needs_whois_refresh(UUID) TO authenticated;

GRANT EXECUTE ON FUNCTION link_track.get_expiring_domains(VARCHAR, INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_expiring_domains(VARCHAR, INTEGER) TO authenticated;

GRANT EXECUTE ON FUNCTION link_track.get_user_domain_statistics(VARCHAR) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_user_domain_statistics(VARCHAR) TO authenticated;

-- Grant permissions on view
GRANT SELECT ON link_track.domain_management_with_projects TO service_role;
GRANT SELECT ON link_track.domain_management_with_projects TO authenticated;

-- ========================================
-- COMMENTS FOR DOCUMENTATION
-- ========================================

COMMENT ON TABLE link_track.domain_management IS 'Dedicated domain management table for tracking domain registration, expiry, pricing, and WHOIS data';
COMMENT ON TABLE link_track.domain_project_associations IS 'Junction table linking domains to projects with primary domain designation';

COMMENT ON COLUMN link_track.domain_management.domain IS 'Normalized domain name without www, protocol, or path';
COMMENT ON COLUMN link_track.domain_management.whois_data IS 'Cached WHOIS data in JSON format for 30-day cache period';
COMMENT ON COLUMN link_track.domain_management.whois_cache_expires IS 'Timestamp when WHOIS cache expires and needs refresh';
COMMENT ON COLUMN link_track.domain_management.monitor_expiry IS 'Whether to send expiry alerts for this domain';

COMMIT;

-- Final verification message
DO $$
BEGIN
    RAISE NOTICE 'Domain Management System created successfully!';
    RAISE NOTICE 'Tables: domain_management, domain_project_associations';
    RAISE NOTICE 'Features: WHOIS caching, expiry monitoring, project associations';
END $$;