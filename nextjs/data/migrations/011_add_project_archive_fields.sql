-- Add archive functionality to projects table
-- This migration adds is_archived and archived_at fields to support project archiving

BEGIN;

-- Add archive fields to projects table
ALTER TABLE link_track.projects 
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN NOT NULL DEFAULT FALSE;

ALTER TABLE link_track.projects 
ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ;

-- <PERSON>reate indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_projects_is_archived 
ON link_track.projects(is_archived);

CREATE INDEX IF NOT EXISTS idx_projects_user_archived 
ON link_track.projects(user_id, is_archived);

-- Add comment for documentation
COMMENT ON COLUMN link_track.projects.is_archived IS 'Whether the project is archived';
COMMENT ON COLUMN link_track.projects.archived_at IS 'Timestamp when the project was archived';

-- Create a function to archive/unarchive projects
CREATE OR REPLACE FUNCTION link_track.archive_project(
    p_project_id UUID,
    p_user_id VARCHAR(255),
    p_is_archived BOOLEAN
)
RETURNS TABLE(
    success BOOLEAN,
    message TEXT,
    project_data JSONB
) AS $$
DECLARE
    v_project_exists BOOLEAN;
    v_updated_project RECORD;
BEGIN
    -- Check if project exists and belongs to user
    SELECT EXISTS(
        SELECT 1 FROM link_track.projects 
        WHERE id = p_project_id AND user_id = p_user_id
    ) INTO v_project_exists;
    
    IF NOT v_project_exists THEN
        RETURN QUERY SELECT FALSE, 'Project not found or access denied'::TEXT, NULL::JSONB;
        RETURN;
    END IF;
    
    -- Update the project archive status
    UPDATE link_track.projects 
    SET 
        is_archived = p_is_archived,
        archived_at = CASE 
            WHEN p_is_archived THEN NOW() 
            ELSE NULL 
        END,
        updated_at = NOW()
    WHERE id = p_project_id AND user_id = p_user_id
    RETURNING * INTO v_updated_project;
    
    -- Return success with project data
    RETURN QUERY SELECT 
        TRUE,
        CASE 
            WHEN p_is_archived THEN 'Project archived successfully'
            ELSE 'Project restored successfully'
        END::TEXT,
        row_to_json(v_updated_project)::JSONB;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on the function
GRANT EXECUTE ON FUNCTION link_track.archive_project(UUID, VARCHAR(255), BOOLEAN) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.archive_project(UUID, VARCHAR(255), BOOLEAN) TO authenticated;

-- Create a view for active projects (commonly used query)
CREATE OR REPLACE VIEW link_track.active_projects AS
SELECT *
FROM link_track.projects
WHERE is_archived = FALSE OR is_archived IS NULL;

-- Grant permissions on the view
GRANT SELECT ON link_track.active_projects TO service_role;
GRANT SELECT ON link_track.active_projects TO authenticated;

-- Create a view for archived projects
CREATE OR REPLACE VIEW link_track.archived_projects AS
SELECT *
FROM link_track.projects
WHERE is_archived = TRUE;

-- Grant permissions on the view
GRANT SELECT ON link_track.archived_projects TO service_role;
GRANT SELECT ON link_track.archived_projects TO authenticated;

-- Add a function to get project statistics including archive status
CREATE OR REPLACE FUNCTION link_track.get_user_project_stats(p_user_id VARCHAR(255))
RETURNS TABLE(
    total_projects INTEGER,
    active_projects INTEGER,
    archived_projects INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_projects,
        COUNT(*) FILTER (WHERE is_archived = FALSE OR is_archived IS NULL)::INTEGER as active_projects,
        COUNT(*) FILTER (WHERE is_archived = TRUE)::INTEGER as archived_projects
    FROM link_track.projects
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on the stats function
GRANT EXECUTE ON FUNCTION link_track.get_user_project_stats(VARCHAR(255)) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_user_project_stats(VARCHAR(255)) TO authenticated;

DROP FUNCTION IF EXISTS link_track.get_user_usage_summary(VARCHAR);

CREATE OR REPLACE FUNCTION link_track.get_user_usage_summary(p_user_id VARCHAR(255))
RETURNS TABLE(
    tier link_track.user_tier,
    subscription_status VARCHAR(50),
    projects_count INTEGER,
    projects_limit INTEGER,
    domains_count INTEGER,
    domains_limit INTEGER,
    link_resources_count INTEGER,
    link_resources_limit INTEGER,
    monthly_dr_queries_used INTEGER,
    monthly_dr_queries_limit INTEGER,
    monthly_traffic_updates_used INTEGER,
    monthly_traffic_updates_limit INTEGER,
    usage_reset_date TIMESTAMPTZ
) AS $$
DECLARE
    user_rec RECORD;
    limits_rec RECORD;
BEGIN
    -- Get user info
    SELECT u.tier, u.subscription_status, u.monthly_dr_queries_used, 
           u.monthly_traffic_updates_used, u.monthly_usage_reset_date
    INTO user_rec
    FROM link_track.users u
    WHERE u.uuid = p_user_id;
    
    -- Handle case where user doesn't exist
    IF user_rec.tier IS NULL THEN
        RETURN;
    END IF;
    
    -- Get current counts (cast COUNT to integer to avoid bigint/integer mismatch)
    SELECT 
        COUNT(DISTINCT p.id)::INTEGER as projects_count,
        COUNT(DISTINCT p.domain)::INTEGER as domains_count,
        COUNT(DISTINCT lr.id)::INTEGER as link_resources_count
    INTO limits_rec
    FROM link_track.users u
    LEFT JOIN link_track.projects p ON p.user_id = u.uuid
    LEFT JOIN link_track.link_resources lr ON lr.user_id = u.uuid
    WHERE u.uuid = p_user_id;
    
    RETURN QUERY
    SELECT 
        user_rec.tier,
        user_rec.subscription_status,
        limits_rec.projects_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'projects'),
        limits_rec.domains_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'domains'),
        limits_rec.link_resources_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'link_resources'),
        user_rec.monthly_dr_queries_used,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'monthly_dr_queries'),
        user_rec.monthly_traffic_updates_used,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'monthly_traffic_updates'),
        user_rec.monthly_usage_reset_date;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the fix
COMMENT ON FUNCTION link_track.get_user_usage_summary(VARCHAR) IS 'Fixed type mismatch: COUNT() returns bigint, cast to integer to match return type';

COMMIT;

-- Final verification message
DO $$
BEGIN
    RAISE NOTICE 'Migration 011_add_project_archive_fields completed successfully!';
    RAISE NOTICE 'Added is_archived and archived_at columns to projects table';
    RAISE NOTICE 'Created indexes for better query performance';
    RAISE NOTICE 'Added archive_project() function for safe archiving operations';
    RAISE NOTICE 'Created active_projects and archived_projects views';
    RAISE NOTICE 'Updated get_user_usage_summary() to exclude archived projects from limits';
    RAISE NOTICE 'Added get_user_project_stats() function for project statistics';
END $$; 