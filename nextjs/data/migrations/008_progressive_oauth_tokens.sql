-- Migration 008: Progressive OAuth Token Management
-- Implement separate token storage for progressive OAuth permissions

BEGIN;

-- Create OAuth token types enum
CREATE TYPE link_track.oauth_token_type AS ENUM (
    'basic',           -- Basic Google profile/email access
    'analytics',       -- Google Analytics readonly
    'search_console',  -- Google Search Console readonly
    'combined'         -- Analytics + Search Console
);

-- Create OAuth scope enum
CREATE TYPE link_track.oauth_scope AS ENUM (
    'openid',
    'email', 
    'profile',
    'analytics.readonly',
    'webmasters.readonly'
);

-- Create user OAuth tokens table for progressive permissions
CREATE TABLE IF NOT EXISTS link_track.user_oauth_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    project_id UUID REFERENCES link_track.projects(id) ON DELETE CASCADE,
    token_type link_track.oauth_token_type NOT NULL,
    scope_requested TEXT[] NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMPTZ NOT NULL,
    token_data JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_refreshed_at TIMESTAMPTZ,
    -- Unique constraint per user per project per token type
    UNIQUE(user_id, project_id, token_type)
);

-- Add indexes for OAuth token queries
CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_user_id 
ON link_track.user_oauth_tokens(user_id);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_project_id 
ON link_track.user_oauth_tokens(project_id);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_type 
ON link_track.user_oauth_tokens(token_type);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_active 
ON link_track.user_oauth_tokens(is_active);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_expires 
ON link_track.user_oauth_tokens(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_user_project_type 
ON link_track.user_oauth_tokens(user_id, project_id, token_type);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_scope 
ON link_track.user_oauth_tokens USING GIN (scope_requested);

CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_data 
ON link_track.user_oauth_tokens USING GIN (token_data);

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_user_oauth_tokens_updated_at 
ON link_track.user_oauth_tokens;

CREATE TRIGGER update_user_oauth_tokens_updated_at
    BEFORE UPDATE ON link_track.user_oauth_tokens
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Enhance user_configs table for OAuth status tracking
ALTER TABLE link_track.user_configs 
ADD COLUMN IF NOT EXISTS required_oauth_scopes TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN IF NOT EXISTS oauth_status VARCHAR(50) DEFAULT 'pending';

-- Create OAuth state table for PKCE flow
CREATE TABLE IF NOT EXISTS link_track.oauth_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    project_id UUID NOT NULL REFERENCES link_track.projects(id) ON DELETE CASCADE,
    state_token VARCHAR(255) UNIQUE NOT NULL,
    code_verifier VARCHAR(255) NOT NULL,
    token_type link_track.oauth_token_type NOT NULL,
    scopes TEXT[] NOT NULL,
    redirect_url TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP + INTERVAL '10 minutes'),
    is_used BOOLEAN DEFAULT FALSE
);

-- Add index for OAuth state lookups
CREATE INDEX IF NOT EXISTS idx_oauth_states_state_token 
ON link_track.oauth_states(state_token);

CREATE INDEX IF NOT EXISTS idx_oauth_states_user_project 
ON link_track.oauth_states(user_id, project_id);

CREATE INDEX IF NOT EXISTS idx_oauth_states_expires 
ON link_track.oauth_states(expires_at);

-- Function to store OAuth tokens
CREATE OR REPLACE FUNCTION link_track.store_oauth_tokens(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_token_type link_track.oauth_token_type,
    p_scopes TEXT[],
    p_access_token TEXT,
    p_refresh_token TEXT DEFAULT NULL,
    p_expires_at TIMESTAMPTZ DEFAULT NULL,
    p_token_data JSONB DEFAULT '{}'::jsonb
)
RETURNS link_track.user_oauth_tokens AS $$
DECLARE
    result link_track.user_oauth_tokens%ROWTYPE;
BEGIN
    INSERT INTO link_track.user_oauth_tokens (
        user_id, 
        project_id, 
        token_type, 
        scope_requested, 
        access_token, 
        refresh_token, 
        expires_at,
        token_data,
        is_active, 
        created_at, 
        updated_at
    )
    VALUES (
        p_user_id, 
        p_project_id, 
        p_token_type, 
        p_scopes, 
        p_access_token, 
        p_refresh_token, 
        COALESCE(p_expires_at, CURRENT_TIMESTAMP + INTERVAL '1 hour'),
        p_token_data,
        TRUE, 
        NOW(), 
        NOW()
    )
    ON CONFLICT (user_id, project_id, token_type)
    DO UPDATE SET
        scope_requested = EXCLUDED.scope_requested,
        access_token = EXCLUDED.access_token,
        refresh_token = COALESCE(EXCLUDED.refresh_token, link_track.user_oauth_tokens.refresh_token),
        expires_at = EXCLUDED.expires_at,
        token_data = EXCLUDED.token_data,
        is_active = TRUE,
        updated_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to get valid OAuth tokens (not expired)
CREATE OR REPLACE FUNCTION link_track.get_valid_oauth_tokens(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_token_type link_track.oauth_token_type
)
RETURNS link_track.user_oauth_tokens AS $$
DECLARE
    result link_track.user_oauth_tokens%ROWTYPE;
BEGIN
    SELECT * INTO result
    FROM link_track.user_oauth_tokens
    WHERE user_id = p_user_id 
    AND project_id = p_project_id
    AND token_type = p_token_type
    AND is_active = TRUE
    AND expires_at > NOW();
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to refresh OAuth tokens
CREATE OR REPLACE FUNCTION link_track.refresh_oauth_tokens(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_token_type link_track.oauth_token_type,
    p_new_access_token TEXT,
    p_new_expires_at TIMESTAMPTZ
)
RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE link_track.user_oauth_tokens
    SET 
        access_token = p_new_access_token,
        expires_at = p_new_expires_at,
        updated_at = NOW(),
        last_refreshed_at = NOW()
    WHERE user_id = p_user_id 
    AND project_id = p_project_id 
    AND token_type = p_token_type
    AND is_active = TRUE;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to revoke OAuth tokens
CREATE OR REPLACE FUNCTION link_track.revoke_oauth_tokens(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_token_type link_track.oauth_token_type
)
RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE link_track.user_oauth_tokens
    SET 
        is_active = FALSE,
        updated_at = NOW()
    WHERE user_id = p_user_id 
    AND project_id = p_project_id 
    AND token_type = p_token_type;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to store OAuth state for PKCE flow
CREATE OR REPLACE FUNCTION link_track.store_oauth_state(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_state_token VARCHAR(255),
    p_code_verifier VARCHAR(255),
    p_token_type link_track.oauth_token_type,
    p_scopes TEXT[],
    p_redirect_url TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    state_id UUID;
BEGIN
    INSERT INTO link_track.oauth_states (
        user_id,
        project_id,
        state_token,
        code_verifier,
        token_type,
        scopes,
        redirect_url,
        created_at,
        expires_at
    )
    VALUES (
        p_user_id,
        p_project_id,
        p_state_token,
        p_code_verifier,
        p_token_type,
        p_scopes,
        p_redirect_url,
        NOW(),
        NOW() + INTERVAL '10 minutes'
    )
    RETURNING id INTO state_id;
    
    RETURN state_id;
END;
$$ LANGUAGE plpgsql;

-- Function to verify and consume OAuth state
CREATE OR REPLACE FUNCTION link_track.verify_oauth_state(
    p_state_token VARCHAR(255)
)
RETURNS link_track.oauth_states AS $$
DECLARE
    result link_track.oauth_states%ROWTYPE;
BEGIN
    UPDATE link_track.oauth_states
    SET is_used = TRUE
    WHERE state_token = p_state_token
    AND expires_at > NOW()
    AND is_used = FALSE
    RETURNING * INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired OAuth states
CREATE OR REPLACE FUNCTION link_track.cleanup_expired_oauth_states()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM link_track.oauth_states
    WHERE expires_at < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.user_oauth_tokens TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.user_oauth_tokens TO authenticated;

GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.oauth_states TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.oauth_states TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE link_track.user_oauth_tokens IS 'Progressive OAuth token storage for service-specific permissions';
COMMENT ON TABLE link_track.oauth_states IS 'OAuth PKCE state management for secure authorization flows';

COMMENT ON COLUMN link_track.user_oauth_tokens.token_type IS 'Type of OAuth token: basic, analytics, search_console, or combined';
COMMENT ON COLUMN link_track.user_oauth_tokens.scope_requested IS 'Array of OAuth scopes granted for this token';
COMMENT ON COLUMN link_track.user_oauth_tokens.token_data IS 'Additional metadata about the OAuth token';

COMMENT ON COLUMN link_track.oauth_states.code_verifier IS 'PKCE code verifier for secure OAuth flow';
COMMENT ON COLUMN link_track.oauth_states.state_token IS 'Unique state token to prevent CSRF attacks';

-- Migration completion notice
DO $$
BEGIN
    RAISE NOTICE 'Migration 008 completed: Progressive OAuth token management system';
    RAISE NOTICE 'Created: user_oauth_tokens table for token storage';
    RAISE NOTICE 'Created: oauth_states table for PKCE flow management';
    RAISE NOTICE 'Added: Helper functions for OAuth token lifecycle';
    RAISE NOTICE 'Enhanced: user_configs table with OAuth status tracking';
END $$;

COMMIT;