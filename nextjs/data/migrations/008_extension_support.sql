-- Migration 008: Extension Support
-- Add tables and fields for browser extension functionality
BEGIN;

-- ========================================
-- EXTENSION API KEYS TABLE
-- ========================================

-- Create table for extension API keys
CREATE TABLE IF NOT EXISTS link_track.extension_api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL REFERENCES link_track.users(uuid) ON DELETE CASCADE,
    key_hash VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL DEFAULT 'Extension API Key',
    last_used TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create indexes for extension_api_keys
CREATE INDEX IF NOT EXISTS idx_extension_api_keys_user_id ON link_track.extension_api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_extension_api_keys_key_hash ON link_track.extension_api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_extension_api_keys_active ON link_track.extension_api_keys(is_active);

-- ========================================
-- EXTENSION SUBMISSIONS TABLE
-- ========================================

-- Create table for tracking extension submissions
CREATE TABLE IF NOT EXISTS link_track.extension_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL REFERENCES link_track.users(uuid) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES link_track.projects(id) ON DELETE CASCADE,
    link_id UUID NOT NULL REFERENCES link_track.link_resources(id) ON DELETE CASCADE,
    target_url TEXT NOT NULL,
    submission_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'submitted', 'failed', 'completed')),
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for extension_submissions
CREATE INDEX IF NOT EXISTS idx_extension_submissions_user_id ON link_track.extension_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_extension_submissions_project_id ON link_track.extension_submissions(project_id);
CREATE INDEX IF NOT EXISTS idx_extension_submissions_link_id ON link_track.extension_submissions(link_id);
CREATE INDEX IF NOT EXISTS idx_extension_submissions_status ON link_track.extension_submissions(status);
CREATE INDEX IF NOT EXISTS idx_extension_submissions_created_at ON link_track.extension_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_extension_submissions_target_url ON link_track.extension_submissions USING hash(target_url);

-- ========================================
-- UPDATE DISCOVERED_LINKS TABLE
-- ========================================

-- Add extension-related fields to discovered_links
ALTER TABLE link_track.discovered_links 
ADD COLUMN IF NOT EXISTS extension_submitted BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS submission_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS submission_details JSONB DEFAULT '{}';

-- Create indexes for new discovered_links fields
CREATE INDEX IF NOT EXISTS idx_discovered_links_extension_submitted ON link_track.discovered_links(extension_submitted);
CREATE INDEX IF NOT EXISTS idx_discovered_links_submission_date ON link_track.discovered_links(submission_date);

-- ========================================
-- UPDATE LINK_RESOURCES TABLE
-- ========================================

-- Add source field to track where links came from
ALTER TABLE link_track.link_resources 
ADD COLUMN IF NOT EXISTS source VARCHAR(50) DEFAULT 'manual';

-- Create index for source field
CREATE INDEX IF NOT EXISTS idx_link_resources_source ON link_track.link_resources(source);

-- ========================================
-- EXTENSION FORM ANALYSIS CACHE TABLE
-- ========================================

-- Create table for caching form analysis results
CREATE TABLE IF NOT EXISTS link_track.extension_form_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url_hash VARCHAR(64) NOT NULL, -- SHA-256 hash of the target URL
    user_id VARCHAR(255) NOT NULL REFERENCES link_track.users(uuid) ON DELETE CASCADE,
    form_signature VARCHAR(255) NOT NULL, -- Simple signature of the form structure
    analysis_result JSONB NOT NULL,
    confidence_score INTEGER DEFAULT 0 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days')
);

-- Create indexes for extension_form_analysis
CREATE INDEX IF NOT EXISTS idx_extension_form_analysis_url_hash ON link_track.extension_form_analysis(url_hash);
CREATE INDEX IF NOT EXISTS idx_extension_form_analysis_user_id ON link_track.extension_form_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_extension_form_analysis_expires_at ON link_track.extension_form_analysis(expires_at);
CREATE INDEX IF NOT EXISTS idx_extension_form_analysis_signature ON link_track.extension_form_analysis(form_signature);

-- ========================================
-- EXTENSION USAGE STATISTICS TABLE
-- ========================================

-- Create table for tracking extension usage
CREATE TABLE IF NOT EXISTS link_track.extension_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL REFERENCES link_track.users(uuid) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'form_detect', 'form_fill', 'link_submit', 'link_add'
    target_url TEXT,
    success BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for extension_usage_stats
CREATE INDEX IF NOT EXISTS idx_extension_usage_stats_user_id ON link_track.extension_usage_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_extension_usage_stats_action ON link_track.extension_usage_stats(action);
CREATE INDEX IF NOT EXISTS idx_extension_usage_stats_created_at ON link_track.extension_usage_stats(created_at);
CREATE INDEX IF NOT EXISTS idx_extension_usage_stats_success ON link_track.extension_usage_stats(success);

-- ========================================
-- EXTENSION SETTINGS TABLE
-- ========================================

-- Create table for user extension settings
CREATE TABLE IF NOT EXISTS link_track.extension_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL REFERENCES link_track.users(uuid) ON DELETE CASCADE,
    auto_fill_enabled BOOLEAN DEFAULT true,
    ai_generation_enabled BOOLEAN DEFAULT true,
    default_project_id UUID REFERENCES link_track.projects(id) ON DELETE SET NULL,
    theme VARCHAR(20) DEFAULT 'light' CHECK (theme IN ('light', 'dark')),
    notifications_enabled BOOLEAN DEFAULT true,
    settings_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Create indexes for extension_settings
CREATE INDEX IF NOT EXISTS idx_extension_settings_user_id ON link_track.extension_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_extension_settings_default_project_id ON link_track.extension_settings(default_project_id);

-- ========================================
-- UPDATE TRIGGERS
-- ========================================

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION link_track.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers for new tables
CREATE TRIGGER update_extension_api_keys_updated_at 
    BEFORE UPDATE ON link_track.extension_api_keys 
    FOR EACH ROW EXECUTE FUNCTION link_track.update_updated_at_column();

CREATE TRIGGER update_extension_submissions_updated_at 
    BEFORE UPDATE ON link_track.extension_submissions 
    FOR EACH ROW EXECUTE FUNCTION link_track.update_updated_at_column();

CREATE TRIGGER update_extension_settings_updated_at 
    BEFORE UPDATE ON link_track.extension_settings 
    FOR EACH ROW EXECUTE FUNCTION link_track.update_updated_at_column();

-- ========================================
-- ROW LEVEL SECURITY (RLS)
-- ========================================

-- Enable RLS on new tables
ALTER TABLE link_track.extension_api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE link_track.extension_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE link_track.extension_form_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE link_track.extension_usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE link_track.extension_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for extension_api_keys
CREATE POLICY "Users can manage their own API keys" ON link_track.extension_api_keys
    FOR ALL USING (auth.uid()::text = user_id);

-- Create RLS policies for extension_submissions
CREATE POLICY "Users can manage their own submissions" ON link_track.extension_submissions
    FOR ALL USING (auth.uid()::text = user_id);

-- Create RLS policies for extension_form_analysis
CREATE POLICY "Users can access their own form analysis" ON link_track.extension_form_analysis
    FOR ALL USING (auth.uid()::text = user_id);

-- Create RLS policies for extension_usage_stats
CREATE POLICY "Users can access their own usage stats" ON link_track.extension_usage_stats
    FOR ALL USING (auth.uid()::text = user_id);

-- Create RLS policies for extension_settings
CREATE POLICY "Users can manage their own extension settings" ON link_track.extension_settings
    FOR ALL USING (auth.uid()::text = user_id);

-- ========================================
-- CLEANUP FUNCTION
-- ========================================

-- Function to clean up expired form analysis cache
CREATE OR REPLACE FUNCTION link_track.cleanup_expired_form_analysis()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM link_track.extension_form_analysis 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions on new tables
GRANT ALL ON link_track.extension_api_keys TO service_role;
GRANT ALL ON link_track.extension_submissions TO service_role;
GRANT ALL ON link_track.extension_form_analysis TO service_role;
GRANT ALL ON link_track.extension_usage_stats TO service_role;
GRANT ALL ON link_track.extension_settings TO service_role;

GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.extension_api_keys TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.extension_submissions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.extension_form_analysis TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.extension_usage_stats TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.extension_settings TO authenticated;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION link_track.cleanup_expired_form_analysis() TO service_role;

COMMIT;

-- ========================================
-- MIGRATION NOTES
-- ========================================

/*
This migration adds comprehensive support for the LinkTrackPro browser extension:

1. extension_api_keys: Stores hashed API keys for extension authentication
2. extension_submissions: Tracks all link submissions made through the extension
3. extension_form_analysis: Caches AI-powered form analysis results
4. extension_usage_stats: Tracks extension usage for analytics
5. extension_settings: Stores user preferences for the extension

Updated existing tables:
- discovered_links: Added extension submission tracking fields
- link_resources: Added source field to track origin of links

Security:
- All tables have RLS enabled with appropriate policies
- API keys are stored as hashes, never in plain text
- User data is isolated by user_id

Performance:
- Comprehensive indexing on frequently queried fields
- Form analysis cache expires automatically after 7 days
- Cleanup function provided for maintenance

To apply this migration:
1. Run this SQL script on your database
2. Update your API models to handle the new tables
3. Configure BACKEND_WORKER_API_KEY for AI integration
4. Test extension authentication and submission flows
*/