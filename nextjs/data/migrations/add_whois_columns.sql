-- Add WHOIS data columns to all_links table
-- Migration: add_whois_columns.sql
-- Date: 2024-01-15

-- Add WHOIS data storage column (JSON format)
ALTER TABLE link_track.all_links ADD COLUMN whois_data TEXT;

-- Add timestamp for WHOIS data updates
ALTER TABLE link_track.all_links ADD COLUMN whois_updated_at TIMESTAMP;

-- Add index for efficient WHOIS data queries
CREATE INDEX idx_all_links_whois_updated_at ON link_track.all_links(whois_updated_at);

-- Add index for domain lookups with WHOIS data
CREATE INDEX idx_all_links_domain_whois ON link_track.all_links(domain, whois_updated_at);

-- Update existing records to have NULL WHOIS data initially
UPDATE link_track.all_links SET whois_data = NULL, whois_updated_at = NULL WHERE whois_data IS NULL;

-- Add comments for documentation (PostgreSQL syntax)
COMMENT ON COLUMN link_track.all_links.whois_data IS 'JSON storage for WHOIS information including registrar, creation_date, expiration_date, name_servers, etc.';
COMMENT ON COLUMN link_track.all_links.whois_updated_at IS 'Timestamp when WHOIS data was last updated. Used for cache validation (30-day cache).'; 