-- Migration 007: Enhance user_configs table for OAuth token management
-- Add support for Google Analytics OAuth integration

BEGIN;

-- Add new columns to user_configs table for OAuth support
ALTER TABLE link_track.user_configs 
ADD COLUMN IF NOT EXISTS oauth_tokens J<PERSON>NB,
ADD COLUMN IF NOT EXISTS properties JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS permissions TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN IF NOT EXISTS last_sync_at TIMESTAMPTZ;

-- Add indexes for OAuth token queries
CREATE INDEX IF NOT EXISTS idx_user_configs_oauth_tokens 
ON link_track.user_configs USING GIN (oauth_tokens);

CREATE INDEX IF NOT EXISTS idx_user_configs_properties 
ON link_track.user_configs USING GIN (properties);

CREATE INDEX IF NOT EXISTS idx_user_configs_last_sync 
ON link_track.user_configs (last_sync_at);

-- Create table for Google Analytics properties
CREATE TABLE IF NOT EXISTS link_track.google_analytics_properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    project_id UUID NOT NULL REFERENCES link_track.projects(id) ON DELETE CASCADE,
    property_id VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    website_url VARCHAR(500),
    account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    time_zone VARCHAR(100),
    currency_code VARCHAR(10),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, property_id)
);

-- Add indexes for Google Analytics properties
CREATE INDEX IF NOT EXISTS idx_ga_properties_user_id 
ON link_track.google_analytics_properties(user_id);

CREATE INDEX IF NOT EXISTS idx_ga_properties_project_id 
ON link_track.google_analytics_properties(project_id);

CREATE INDEX IF NOT EXISTS idx_ga_properties_property_id 
ON link_track.google_analytics_properties(property_id);

CREATE INDEX IF NOT EXISTS idx_ga_properties_active 
ON link_track.google_analytics_properties(is_active);

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_google_analytics_properties_updated_at 
ON link_track.google_analytics_properties;

CREATE TRIGGER update_google_analytics_properties_updated_at
    BEFORE UPDATE ON link_track.google_analytics_properties
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Enhance analytics_data table for Google Analytics integration
ALTER TABLE link_track.analytics_data 
ADD COLUMN IF NOT EXISTS source_property_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS query_data JSONB,
ADD COLUMN IF NOT EXISTS position_data JSONB,
ADD COLUMN IF NOT EXISTS impression_data JSONB;

-- Add indexes for enhanced analytics data
CREATE INDEX IF NOT EXISTS idx_analytics_data_source_property 
ON link_track.analytics_data (source_property_id);

CREATE INDEX IF NOT EXISTS idx_analytics_data_query 
ON link_track.analytics_data USING GIN (query_data);

CREATE INDEX IF NOT EXISTS idx_analytics_data_position 
ON link_track.analytics_data USING GIN (position_data);

CREATE INDEX IF NOT EXISTS idx_analytics_data_impression 
ON link_track.analytics_data USING GIN (impression_data);

-- Create function to get Google Analytics configuration
CREATE OR REPLACE FUNCTION link_track.get_google_analytics_config(
    p_user_id VARCHAR(255),
    p_project_id UUID
)
RETURNS link_track.user_configs AS $$
DECLARE
    result link_track.user_configs%ROWTYPE;
BEGIN
    SELECT * INTO result
    FROM link_track.user_configs
    WHERE user_id = p_user_id 
    AND project_id = p_project_id
    AND config_type = 'google_analytics_oauth'
    AND is_active = TRUE;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create function to save Google Analytics configuration
CREATE OR REPLACE FUNCTION link_track.save_google_analytics_config(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_config_name VARCHAR(255),
    p_config_data JSONB,
    p_oauth_tokens JSONB DEFAULT NULL,
    p_properties JSONB DEFAULT '[]'::jsonb,
    p_permissions TEXT[] DEFAULT ARRAY[]::TEXT[]
)
RETURNS link_track.user_configs AS $$
DECLARE
    result link_track.user_configs%ROWTYPE;
BEGIN
    INSERT INTO link_track.user_configs (
        user_id, 
        project_id, 
        config_type, 
        config_name, 
        config_data, 
        oauth_tokens,
        properties,
        permissions,
        is_active, 
        created_at, 
        updated_at,
        last_sync_at
    )
    VALUES (
        p_user_id, 
        p_project_id, 
        'google_analytics_oauth', 
        p_config_name, 
        p_config_data,
        p_oauth_tokens,
        p_properties,
        p_permissions,
        TRUE, 
        NOW(), 
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id, project_id, config_type)
    DO UPDATE SET
        config_name = EXCLUDED.config_name,
        config_data = EXCLUDED.config_data,
        oauth_tokens = EXCLUDED.oauth_tokens,
        properties = EXCLUDED.properties,
        permissions = EXCLUDED.permissions,
        is_active = TRUE,
        updated_at = NOW(),
        last_sync_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create function to refresh OAuth tokens
CREATE OR REPLACE FUNCTION link_track.update_oauth_tokens(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_oauth_tokens JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE link_track.user_configs
    SET 
        oauth_tokens = p_oauth_tokens,
        updated_at = NOW(),
        last_sync_at = NOW()
    WHERE user_id = p_user_id 
    AND project_id = p_project_id 
    AND config_type = 'google_analytics_oauth'
    AND is_active = TRUE;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Create function to manage Google Analytics properties
CREATE OR REPLACE FUNCTION link_track.save_google_analytics_property(
    p_user_id VARCHAR(255),
    p_project_id UUID,
    p_property_id VARCHAR(255),
    p_display_name VARCHAR(255),
    p_website_url VARCHAR(500),
    p_account_id VARCHAR(255),
    p_account_name VARCHAR(255),
    p_time_zone VARCHAR(100) DEFAULT NULL,
    p_currency_code VARCHAR(10) DEFAULT NULL
)
RETURNS link_track.google_analytics_properties AS $$
DECLARE
    result link_track.google_analytics_properties%ROWTYPE;
BEGIN
    INSERT INTO link_track.google_analytics_properties (
        user_id,
        project_id,
        property_id,
        display_name,
        website_url,
        account_id,
        account_name,
        time_zone,
        currency_code,
        is_active,
        created_at,
        updated_at
    )
    VALUES (
        p_user_id,
        p_project_id,
        p_property_id,
        p_display_name,
        p_website_url,
        p_account_id,
        p_account_name,
        p_time_zone,
        p_currency_code,
        TRUE,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id, property_id)
    DO UPDATE SET
        project_id = EXCLUDED.project_id,
        display_name = EXCLUDED.display_name,
        website_url = EXCLUDED.website_url,
        account_id = EXCLUDED.account_id,
        account_name = EXCLUDED.account_name,
        time_zone = EXCLUDED.time_zone,
        currency_code = EXCLUDED.currency_code,
        is_active = TRUE,
        updated_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.google_analytics_properties TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON link_track.google_analytics_properties TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE link_track.google_analytics_properties IS 'Google Analytics property configurations linked to user projects';
COMMENT ON COLUMN link_track.user_configs.oauth_tokens IS 'Encrypted OAuth tokens for Google services integration';
COMMENT ON COLUMN link_track.user_configs.properties IS 'Array of configured Google Analytics properties';
COMMENT ON COLUMN link_track.user_configs.permissions IS 'Array of granted OAuth permissions/scopes';
COMMENT ON COLUMN link_track.user_configs.last_sync_at IS 'Timestamp of last successful data synchronization';

-- Migration completion notice
DO $$
BEGIN
    RAISE NOTICE 'Migration 007 completed: Enhanced user_configs for OAuth token management';
    RAISE NOTICE 'Added: OAuth tokens, properties, permissions columns';
    RAISE NOTICE 'Created: google_analytics_properties table';
    RAISE NOTICE 'Added: Helper functions for Google Analytics configuration';
END $$;

COMMIT;