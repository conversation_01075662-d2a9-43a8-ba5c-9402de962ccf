# Waitlist System Migration Guide

本文档介绍如何迁移和使用新的Waitlist系统。

## 📋 概述

Waitlist系统允许用户提交邮箱、姓名和留言，加入产品的等候名单。当有新用户提交时，系统会自动通过飞书发送通知。

## 🚀 快速开始

### 1. 运行数据库迁移

首先运行数据库迁移脚本来创建所需的表和函数：

```sql
-- 在你的PostgreSQL数据库中运行
\i nextjs/data/migrations/010_create_waitlist.sql
```

或者通过psql命令：

```bash
psql -d your_database_name -f nextjs/data/migrations/010_create_waitlist.sql
```

### 2. 配置环境变量

确保在 `.env` 文件中配置了飞书通知：

```bash
# 飞书通知配置
NOTIFICATION_FEISHU_ENABLED=true
NOTIFICATION_FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id
```

获取飞书Webhook URL的步骤：
1. 在飞书群聊中添加自定义机器人
2. 获取Webhook地址
3. 将地址配置到环境变量中

### 3. 验证功能

1. **测试API端点**：
   ```bash
   curl http://localhost:3000/api/waitlist/submit
   ```

2. **测试提交**：
   ```bash
   curl -X POST http://localhost:3000/api/waitlist/submit \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "name": "测试用户",
       "message": "我对这个产品很感兴趣！"
     }'
   ```

## 📊 数据库结构

### 主要表：link_track.waitlist

| 字段 | 类型 | 说明 |
|------|------|------|
| id | UUID | 主键 |
| email | VARCHAR(255) | 用户邮箱（必填，唯一） |
| name | VARCHAR(255) | 用户姓名（可选） |
| message | TEXT | 用户留言（可选） |
| source | VARCHAR(100) | 来源（默认：landing_page） |
| status | VARCHAR(50) | 状态：pending, contacted, converted, unsubscribed |
| user_agent | TEXT | 用户代理字符串 |
| ip_address | INET | 用户IP地址 |
| referrer | TEXT | 来源页面 |
| utm_source | VARCHAR(255) | UTM来源 |
| utm_medium | VARCHAR(255) | UTM媒介 |
| utm_campaign | VARCHAR(255) | UTM活动 |
| metadata | JSONB | 额外元数据 |
| created_at | TIMESTAMPTZ | 创建时间 |
| updated_at | TIMESTAMPTZ | 更新时间 |
| contacted_at | TIMESTAMPTZ | 联系时间 |
| converted_at | TIMESTAMPTZ | 转化时间 |

### 数据库函数

系统提供了以下数据库函数：

- `add_to_waitlist()` - 添加用户到waitlist
- `update_waitlist_status()` - 更新用户状态
- `get_waitlist_stats()` - 获取统计数据
- `get_waitlist_by_source()` - 按来源统计
- `get_recent_waitlist_signups()` - 获取最近注册

## 🔧 API端点

### 用户端点

#### POST /api/waitlist/submit
提交waitlist申请

**请求体**：
```json
{
  "email": "<EMAIL>",
  "name": "用户姓名",
  "message": "用户留言",
  "source": "landing_page",
  "utm_source": "google",
  "utm_medium": "cpc",
  "utm_campaign": "summer2024"
}
```

**响应**：
```json
{
  "success": true,
  "message": "Successfully added to waitlist",
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### 管理端点

#### GET /api/admin/waitlist
获取waitlist列表（需要管理员权限）

**查询参数**：
- `page` - 页码（默认：1）
- `limit` - 每页数量（默认：50）
- `status` - 状态筛选
- `email` - 邮箱搜索

#### PUT /api/admin/waitlist
更新waitlist状态

```json
{
  "id": "uuid",
  "status": "contacted"
}
```

#### DELETE /api/admin/waitlist?id=uuid
删除waitlist条目

#### GET /api/admin/waitlist/stats
获取waitlist统计数据

## 🎨 前端组件

### WaitList组件

位置：`nextjs/components/blocks/waitList/index.tsx`

这是一个现代化的表单组件，包含：
- 邮箱输入（必填）
- 姓名输入（可选）
- 留言文本框（可选）
- 提交按钮
- 成功状态显示
- 错误处理

组件特性：
- 响应式设计
- 暗色模式支持
- 表单验证
- 加载状态
- 成功反馈
- 错误提示

### 使用方式

在页面配置中添加waitlist section：

```json
{
  "name": "waitlist",
  "title": "Join Our Waitlist",
  "description": "Be the first to know when we launch!",
  "disabled": false
}
```

## 📱 通知系统

### 飞书通知格式

当用户提交waitlist时，会自动发送包含以下信息的飞书消息：

- 🎯 标题：新的Waitlist注册
- 📧 用户邮箱
- 👤 用户姓名
- 💬 用户留言
- 📍 注册来源
- 🕐 注册时间
- 🔗 UTM参数
- 🌐 IP地址和用户代理

### 自定义通知

你可以修改 `nextjs/app/api/waitlist/submit/route.ts` 中的通知内容：

```typescript
await notificationService.notify({
  title: '🎯 新的Waitlist注册',
  content: `有新用户加入了waitlist！\n\n**邮箱**: ${email}`,
  level: 'info',
  data: {
    // 自定义数据字段
  }
});
```

## 🔒 安全特性

1. **邮箱验证** - 客户端和服务端双重验证
2. **重复提交防护** - 同一邮箱只能提交一次
3. **管理员权限** - 管理端点需要管理员认证
4. **RLS策略** - 数据库级别的行安全策略
5. **输入清理** - 所有输入都会被清理和验证

## 📈 数据分析

### 可用的统计数据

- 总注册数
- 各状态数量（pending, contacted, converted, unsubscribed）
- 今日/本周/本月注册数
- 转化率
- 按来源分布
- 最近注册列表

### 获取统计数据

```typescript
import { waitlistModel } from '@/models/waitlist';

// 获取总体统计
const stats = await waitlistModel.getWaitlistStats();

// 按来源统计
const sourceStats = await waitlistModel.getWaitlistBySource();

// 最近注册
const recent = await waitlistModel.getRecentSignups(10);
```

## 🚨 故障排除

### 常见问题

1. **飞书通知不工作**
   - 检查 `NOTIFICATION_FEISHU_ENABLED` 环境变量
   - 验证 `NOTIFICATION_FEISHU_WEBHOOK_URL` 是否正确
   - 确认飞书机器人有权限发送消息

2. **数据库连接错误**
   - 检查 Supabase 配置
   - 确认数据库迁移已执行
   - 验证表权限设置

3. **重复邮箱错误**
   - 这是正常行为，邮箱必须唯一
   - 可以通过管理界面查看现有记录

4. **管理权限问题**
   - 检查 `ADMIN_EMAILS` 环境变量
   - 确认用户邮箱在管理员列表中

### 日志调试

在开发环境中，可以通过以下方式查看详细日志：

```bash
# 查看API日志
npm run dev

# 查看数据库查询
# 在Supabase Dashboard中查看Logs
```

## 🔄 升级和维护

### 定期维护

1. **清理过期数据**：定期清理超过一定时间的pending状态记录
2. **监控存储大小**：关注waitlist表的增长
3. **备份数据**：定期备份waitlist数据
4. **更新状态**：及时更新用户的contacted和converted状态

### 性能优化

- 考虑为高频查询添加索引
- 定期VACUUM和ANALYZE表
- 监控查询性能

## 📞 支持

如果你在迁移过程中遇到问题，请：

1. 检查控制台日志
2. 验证环境变量配置
3. 确认数据库迁移状态
4. 查看飞书机器人设置

---

**注意**：在生产环境中部署前，请务必测试所有功能，包括表单提交、通知发送和管理界面。 