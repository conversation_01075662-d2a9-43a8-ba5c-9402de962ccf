-- 数据库一键修复脚本：将 domain_management 表中的域名转换为顶级域名
-- Migration: 006_cleanup_subdomain_data.sql
-- Purpose: Convert domain_management table subdomain entries to top-level domains only

BEGIN;

-- ========================================
-- 创建域名标准化函数
-- ========================================

-- 创建函数：从域名中提取顶级域名
CREATE OR REPLACE FUNCTION link_track.extract_top_level_domain(input_domain TEXT)
RETURNS TEXT AS $$
DECLARE
    clean_domain TEXT;
    domain_parts TEXT[];
BEGIN
    -- 处理空值
    IF input_domain IS NULL OR input_domain = '' THEN
        RETURN NULL;
    END IF;
    
    -- 移除协议前缀和www
    clean_domain := LOWER(TRIM(input_domain));
    clean_domain := REGEXP_REPLACE(clean_domain, '^https?://', '', 'i');
    clean_domain := REGEXP_REPLACE(clean_domain, '^www\.', '', 'i');
    
    -- 移除路径、查询参数和锚点
    clean_domain := SPLIT_PART(clean_domain, '/', 1);
    clean_domain := SPLIT_PART(clean_domain, '?', 1);
    clean_domain := SPLIT_PART(clean_domain, '#', 1);
    
    -- 分割域名部分
    domain_parts := STRING_TO_ARRAY(clean_domain, '.');
    
    -- 验证是否为有效的顶级域名格式 (至少2部分，且最后部分至少2个字符)
    IF array_length(domain_parts, 1) < 2 THEN
        RETURN NULL;
    END IF;
    
    IF LENGTH(domain_parts[array_length(domain_parts, 1)]) < 2 THEN
        RETURN NULL;
    END IF;
    
    -- 提取最后两部分作为顶级域名
    IF array_length(domain_parts, 1) >= 2 THEN
        RETURN domain_parts[array_length(domain_parts, 1) - 1] || '.' || domain_parts[array_length(domain_parts, 1)];
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 跳过备份（按用户要求）
-- ========================================

-- 不创建备份表，直接修复

-- ========================================
-- 统计修复前的数据状况
-- ========================================

DO $$
DECLARE
    domains_total INTEGER;
    domains_invalid INTEGER;
    domains_with_subdomains INTEGER;
BEGIN
    -- 统计 domain_management 表
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'domain_management') THEN
        SELECT COUNT(*) INTO domains_total FROM link_track.domain_management;
        
        SELECT COUNT(*) INTO domains_invalid FROM link_track.domain_management 
        WHERE link_track.extract_top_level_domain(domain) IS NULL;
        
        SELECT COUNT(*) INTO domains_with_subdomains FROM link_track.domain_management 
        WHERE domain IS NOT NULL 
        AND domain != ''
        AND link_track.extract_top_level_domain(domain) IS NOT NULL
        AND domain != link_track.extract_top_level_domain(domain);
    ELSE
        domains_total := 0;
        domains_invalid := 0;
        domains_with_subdomains := 0;
    END IF;
    
    RAISE NOTICE '=== 修复前数据统计 ===';
    RAISE NOTICE 'Domain Management 总数: %', domains_total;
    RAISE NOTICE 'Domain Management 无效域名: %', domains_invalid;
    RAISE NOTICE 'Domain Management 需要转换的子域名: %', domains_with_subdomains;
END $$;

-- ========================================
-- 跳过 projects 表修复（保持子域名）
-- ========================================

-- Projects 表保持原样，允许子域名存在
-- 这里不进行任何修改

-- ========================================
-- 修复 domain_management 表中的域名
-- ========================================

-- 检查 domain_management 表是否存在
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'domain_management') THEN
        -- 更新域名管理表中的域名为顶级域名
        UPDATE link_track.domain_management 
        SET domain = link_track.extract_top_level_domain(domain)
        WHERE domain IS NOT NULL 
        AND domain != ''
        AND link_track.extract_top_level_domain(domain) IS NOT NULL
        AND domain != link_track.extract_top_level_domain(domain);
        
        -- 删除无法转换为有效顶级域名的记录
        DELETE FROM link_track.domain_management 
        WHERE domain IS NOT NULL 
        AND domain != ''
        AND link_track.extract_top_level_domain(domain) IS NULL;
        
        -- 处理重复的域名记录（保留最新的）
        DELETE FROM link_track.domain_management dm1 
        USING link_track.domain_management dm2 
        WHERE dm1.domain = dm2.domain 
        AND dm1.user_id = dm2.user_id 
        AND dm1.created_at < dm2.created_at;
        
        RAISE NOTICE '已修复 domain_management 表中的域名数据';
    END IF;
END $$;

-- ========================================
-- 跳过其他表的修复（保持原样）
-- ========================================

-- all_links 和 discovered_links 表保持原样
-- 只修复 domain_management 表

-- ========================================
-- 统计修复后的数据状况
-- ========================================

DO $$
DECLARE
    domains_remaining INTEGER;
    domains_remaining_invalid INTEGER;
    domains_converted INTEGER;
BEGIN
    -- 统计修复后的 domain_management
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'domain_management') THEN
        SELECT COUNT(*) INTO domains_remaining FROM link_track.domain_management;
        
        SELECT COUNT(*) INTO domains_remaining_invalid FROM link_track.domain_management 
        WHERE link_track.extract_top_level_domain(domain) IS NULL;
        
        SELECT COUNT(*) INTO domains_converted FROM link_track.domain_management 
        WHERE domain IS NOT NULL 
        AND domain != ''
        AND link_track.extract_top_level_domain(domain) IS NOT NULL;
    ELSE
        domains_remaining := 0;
        domains_remaining_invalid := 0;
        domains_converted := 0;
    END IF;
    
    RAISE NOTICE '=== 修复后数据统计 ===';
    RAISE NOTICE 'Domain Management 记录总数: %', domains_remaining;
    RAISE NOTICE 'Domain Management 有效顶级域名: %', domains_converted;
    RAISE NOTICE 'Domain Management 剩余无效域名: %', domains_remaining_invalid;
END $$;

-- ========================================
-- 清理临时函数
-- ========================================

-- 保留 extract_top_level_domain 函数供将来使用
-- 不删除任何函数

-- ========================================
-- 创建验证查询
-- ========================================

-- 这些查询可以在修复完成后运行以验证结果
COMMENT ON FUNCTION link_track.extract_top_level_domain(TEXT) IS '提取顶级域名的函数，用于域名标准化';

-- 验证查询示例（注释掉，可以手动运行）
/*
-- 检查仍有问题的 domain_management 记录
SELECT id, domain, link_track.extract_top_level_domain(domain) as extracted
FROM link_track.domain_management 
WHERE domain IS NOT NULL 
AND domain != ''
AND (
    link_track.extract_top_level_domain(domain) IS NULL 
    OR domain != link_track.extract_top_level_domain(domain)
);

-- 查看成功转换的域名示例
SELECT id, domain, link_track.extract_top_level_domain(domain) as extracted
FROM link_track.domain_management 
WHERE domain IS NOT NULL 
AND domain != ''
AND link_track.extract_top_level_domain(domain) IS NOT NULL
LIMIT 10;
*/

COMMIT;

-- ========================================
-- 执行完成提示
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Domain Management 域名标准化脚本执行完成！';
    RAISE NOTICE '========================================';
    RAISE NOTICE '1. 直接修复，未创建备份表';
    RAISE NOTICE '2. 已将 domain_management 表中的子域名转换为顶级域名';
    RAISE NOTICE '3. 已删除无效的域名记录';
    RAISE NOTICE '4. Projects 表保持原样（允许子域名）';
    RAISE NOTICE '5. 其他表保持原样，不受影响';
    RAISE NOTICE '6. 保留了 extract_top_level_domain 函数供将来使用';
    RAISE NOTICE '========================================';
    RAISE NOTICE '注意：未创建备份表，请确保数据安全';
    RAISE NOTICE '验证脚本位于文件末尾的注释中';
    RAISE NOTICE '========================================';
END $$;