-- Add user tier system and usage tracking to LinkTrackPro
-- This migration adds subscription tiers and usage limits for free vs paid users

BEGIN;

-- ========================================
-- USER TIER SYSTEM
-- ========================================

-- Add user tier enum
CREATE TYPE link_track.user_tier AS ENUM ('free', 'paid');

-- Add tier-related fields to users table
ALTER TABLE link_track.users 
ADD COLUMN IF NOT EXISTS tier link_track.user_tier DEFAULT 'free' NOT NULL,
ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) DEFAULT 'inactive',
ADD COLUMN IF NOT EXISTS subscription_start_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS subscription_end_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS subscription_plan VARCHAR(50),
ADD COLUMN IF NOT EXISTS monthly_dr_queries_used INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS monthly_traffic_updates_used INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS monthly_usage_reset_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP;

-- ========================================
-- USAGE TRACKING TABLE
-- ========================================

-- Create user usage tracking table
DROP TABLE IF EXISTS link_track.user_usage_history CASCADE;
CREATE TABLE link_track.user_usage_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    usage_type VARCHAR(50) NOT NULL, -- 'dr_query', 'traffic_update'
    usage_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    project_id UUID,
    api_endpoint VARCHAR(255),
    credits_consumed INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::JSONB,
    FOREIGN KEY (user_id) REFERENCES link_track.users(uuid) ON DELETE CASCADE
);

-- ========================================
-- TIER LIMITS CONFIGURATION
-- ========================================

-- Create tier limits table
DROP TABLE IF EXISTS link_track.tier_limits CASCADE;
CREATE TABLE link_track.tier_limits (
    id SERIAL PRIMARY KEY,
    tier link_track.user_tier NOT NULL,
    limit_type VARCHAR(50) NOT NULL, -- 'projects', 'domains', 'link_resources', 'monthly_dr_queries', 'monthly_traffic_updates'
    limit_value INTEGER NOT NULL, -- -1 for unlimited
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tier, limit_type)
);

-- Insert default tier limits
INSERT INTO link_track.tier_limits (tier, limit_type, limit_value) VALUES
-- Free tier limits
('free', 'projects', 5),
('free', 'domains', 10),
('free', 'link_resources', 1000),
('free', 'monthly_dr_queries', 0), -- No DR queries for free users
('free', 'monthly_traffic_updates', 0), -- No manual traffic updates for free users
-- Paid tier limits
('paid', 'projects', 1000),
('paid', 'domains', 1000),
('paid', 'link_resources', -1), -- Unlimited
('paid', 'monthly_dr_queries', 100),
('paid', 'monthly_traffic_updates', 100);

-- ========================================
-- FUNCTIONS FOR TIER MANAGEMENT
-- ========================================

-- Function to get user tier limits
CREATE OR REPLACE FUNCTION link_track.get_user_tier_limits(p_user_id VARCHAR(255))
RETURNS TABLE(limit_type VARCHAR(50), limit_value INTEGER) AS $$
DECLARE
    user_tier link_track.user_tier;
BEGIN
    -- Get user's tier
    SELECT tier INTO user_tier
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Return limits for this tier
    RETURN QUERY
    SELECT tl.limit_type, tl.limit_value
    FROM link_track.tier_limits tl
    WHERE tl.tier = user_tier;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can create more projects
CREATE OR REPLACE FUNCTION link_track.can_create_project(p_user_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    project_limit INTEGER;
    current_projects INTEGER;
BEGIN
    -- Get project limit for user's tier
    SELECT limit_value INTO project_limit
    FROM link_track.get_user_tier_limits(p_user_id)
    WHERE limit_type = 'projects';
    
    -- Count current projects
    SELECT COUNT(*) INTO current_projects
    FROM link_track.projects
    WHERE user_id = p_user_id;
    
    -- Check if can create more (unlimited = -1)
    RETURN (project_limit = -1) OR (current_projects < project_limit);
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can add more domains
CREATE OR REPLACE FUNCTION link_track.can_add_domain(p_user_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    domain_limit INTEGER;
    current_domains INTEGER;
BEGIN
    -- Get domain limit for user's tier
    SELECT limit_value INTO domain_limit
    FROM link_track.get_user_tier_limits(p_user_id)
    WHERE limit_type = 'domains';
    
    -- Count current unique domains from projects
    SELECT COUNT(DISTINCT domain) INTO current_domains
    FROM link_track.projects
    WHERE user_id = p_user_id;
    
    -- Check if can add more (unlimited = -1)
    RETURN (domain_limit = -1) OR (current_domains < domain_limit);
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can add more link resources
CREATE OR REPLACE FUNCTION link_track.can_add_link_resource(p_user_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    link_limit INTEGER;
    current_links INTEGER;
BEGIN
    -- Get link resource limit for user's tier
    SELECT limit_value INTO link_limit
    FROM link_track.get_user_tier_limits(p_user_id)
    WHERE limit_type = 'link_resources';
    
    -- Count current link resources
    SELECT COUNT(*) INTO current_links
    FROM link_track.link_resources
    WHERE user_id = p_user_id;
    
    -- Check if can add more (unlimited = -1)
    RETURN (link_limit = -1) OR (current_links < link_limit);
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can make DR queries
CREATE OR REPLACE FUNCTION link_track.can_make_dr_query(p_user_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    monthly_limit INTEGER;
    current_usage INTEGER;
    user_tier link_track.user_tier;
BEGIN
    -- Get user's tier
    SELECT tier INTO user_tier
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Free users can't make DR queries
    IF user_tier = 'free' THEN
        RETURN FALSE;
    END IF;
    
    -- Get monthly DR query limit for user's tier
    SELECT limit_value INTO monthly_limit
    FROM link_track.get_user_tier_limits(p_user_id)
    WHERE limit_type = 'monthly_dr_queries';
    
    -- Get current monthly usage
    SELECT monthly_dr_queries_used INTO current_usage
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Check if can make more queries (unlimited = -1)
    RETURN (monthly_limit = -1) OR (current_usage < monthly_limit);
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can make manual traffic updates
CREATE OR REPLACE FUNCTION link_track.can_make_traffic_update(p_user_id VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    monthly_limit INTEGER;
    current_usage INTEGER;
    user_tier link_track.user_tier;
BEGIN
    -- Get user's tier
    SELECT tier INTO user_tier
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Free users can't make manual traffic updates
    IF user_tier = 'free' THEN
        RETURN FALSE;
    END IF;
    
    -- Get monthly traffic update limit for user's tier
    SELECT limit_value INTO monthly_limit
    FROM link_track.get_user_tier_limits(p_user_id)
    WHERE limit_type = 'monthly_traffic_updates';
    
    -- Get current monthly usage
    SELECT monthly_traffic_updates_used INTO current_usage
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Check if can make more updates (unlimited = -1)
    RETURN (monthly_limit = -1) OR (current_usage < monthly_limit);
END;
$$ LANGUAGE plpgsql;

-- Function to record DR query usage
CREATE OR REPLACE FUNCTION link_track.record_dr_query_usage(
    p_user_id VARCHAR(255),
    p_project_id UUID DEFAULT NULL,
    p_api_endpoint VARCHAR(255) DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::JSONB
)
RETURNS VOID AS $$
BEGIN
    -- Update user's monthly usage counter
    UPDATE link_track.users
    SET monthly_dr_queries_used = monthly_dr_queries_used + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE uuid = p_user_id;
    
    -- Record in usage history
    INSERT INTO link_track.user_usage_history (user_id, usage_type, project_id, api_endpoint, metadata)
    VALUES (p_user_id, 'dr_query', p_project_id, p_api_endpoint, p_metadata);
END;
$$ LANGUAGE plpgsql;

-- Function to record traffic update usage
CREATE OR REPLACE FUNCTION link_track.record_traffic_update_usage(
    p_user_id VARCHAR(255),
    p_project_id UUID DEFAULT NULL,
    p_api_endpoint VARCHAR(255) DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::JSONB
)
RETURNS VOID AS $$
BEGIN
    -- Update user's monthly usage counter
    UPDATE link_track.users
    SET monthly_traffic_updates_used = monthly_traffic_updates_used + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE uuid = p_user_id;
    
    -- Record in usage history
    INSERT INTO link_track.user_usage_history (user_id, usage_type, project_id, api_endpoint, metadata)
    VALUES (p_user_id, 'traffic_update', p_project_id, p_api_endpoint, p_metadata);
END;
$$ LANGUAGE plpgsql;

-- Function to reset monthly usage counters
CREATE OR REPLACE FUNCTION link_track.reset_monthly_usage_counters()
RETURNS VOID AS $$
BEGIN
    UPDATE link_track.users
    SET monthly_dr_queries_used = 0,
        monthly_traffic_updates_used = 0,
        monthly_usage_reset_date = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE monthly_usage_reset_date < CURRENT_TIMESTAMP - INTERVAL '1 month';
END;
$$ LANGUAGE plpgsql;

-- Function to upgrade user to paid tier
CREATE OR REPLACE FUNCTION link_track.upgrade_user_to_paid(
    p_user_id VARCHAR(255),
    p_subscription_plan VARCHAR(50) DEFAULT 'monthly',
    p_subscription_start_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    p_subscription_end_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE link_track.users
    SET tier = 'paid',
        subscription_status = 'active',
        subscription_plan = p_subscription_plan,
        subscription_start_date = p_subscription_start_date,
        subscription_end_date = p_subscription_end_date,
        updated_at = CURRENT_TIMESTAMP
    WHERE uuid = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to downgrade user to free tier
CREATE OR REPLACE FUNCTION link_track.downgrade_user_to_free(p_user_id VARCHAR(255))
RETURNS VOID AS $$
BEGIN
    UPDATE link_track.users
    SET tier = 'free',
        subscription_status = 'inactive',
        subscription_plan = NULL,
        subscription_start_date = NULL,
        subscription_end_date = NULL,
        monthly_dr_queries_used = 0,
        monthly_traffic_updates_used = 0,
        updated_at = CURRENT_TIMESTAMP
    WHERE uuid = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's current usage and limits
CREATE OR REPLACE FUNCTION link_track.get_user_usage_summary(p_user_id VARCHAR(255))
RETURNS TABLE(
    tier link_track.user_tier,
    subscription_status VARCHAR(50),
    projects_count INTEGER,
    projects_limit INTEGER,
    domains_count INTEGER,
    domains_limit INTEGER,
    link_resources_count INTEGER,
    link_resources_limit INTEGER,
    monthly_dr_queries_used INTEGER,
    monthly_dr_queries_limit INTEGER,
    monthly_traffic_updates_used INTEGER,
    monthly_traffic_updates_limit INTEGER,
    usage_reset_date TIMESTAMPTZ
) AS $$
DECLARE
    user_rec RECORD;
    limits_rec RECORD;
BEGIN
    -- Get user info
    SELECT u.tier, u.subscription_status, u.monthly_dr_queries_used, 
           u.monthly_traffic_updates_used, u.monthly_usage_reset_date
    INTO user_rec
    FROM link_track.users u
    WHERE u.uuid = p_user_id;
    
    -- Get current counts
    SELECT 
        COUNT(DISTINCT p.id) as projects_count,
        COUNT(DISTINCT p.domain) as domains_count,
        COUNT(DISTINCT lr.id) as link_resources_count
    INTO limits_rec
    FROM link_track.users u
    LEFT JOIN link_track.projects p ON p.user_id = u.uuid
    LEFT JOIN link_track.link_resources lr ON lr.user_id = u.uuid
    WHERE u.uuid = p_user_id;
    
    RETURN QUERY
    SELECT 
        user_rec.tier,
        user_rec.subscription_status,
        limits_rec.projects_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'projects'),
        limits_rec.domains_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'domains'),
        limits_rec.link_resources_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'link_resources'),
        user_rec.monthly_dr_queries_used,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'monthly_dr_queries'),
        user_rec.monthly_traffic_updates_used,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'monthly_traffic_updates'),
        user_rec.monthly_usage_reset_date;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- TIER CHANGE AUDIT LOGGING
-- ========================================

-- Create tier change audit table
DROP TABLE IF EXISTS link_track.tier_change_audit CASCADE;
CREATE TABLE link_track.tier_change_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    old_tier link_track.user_tier,
    new_tier link_track.user_tier NOT NULL,
    changed_by VARCHAR(255),
    changed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    reason VARCHAR(500),
    metadata JSONB DEFAULT '{}'::JSONB,
    FOREIGN KEY (user_id) REFERENCES link_track.users(uuid) ON DELETE CASCADE
);

-- Function to log tier changes
CREATE OR REPLACE FUNCTION link_track.log_tier_change(
    p_user_id VARCHAR(255),
    p_old_tier link_track.user_tier,
    p_new_tier link_track.user_tier,
    p_changed_by VARCHAR(255) DEFAULT NULL,
    p_reason VARCHAR(500) DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::JSONB
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO link_track.tier_change_audit (user_id, old_tier, new_tier, changed_by, reason, metadata)
    VALUES (p_user_id, p_old_tier, p_new_tier, p_changed_by, p_reason, p_metadata);
END;
$$ LANGUAGE plpgsql;

-- Update upgrade function to include audit logging
CREATE OR REPLACE FUNCTION link_track.upgrade_user_to_paid(
    p_user_id VARCHAR(255),
    p_subscription_plan VARCHAR(50) DEFAULT 'monthly',
    p_subscription_start_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    p_subscription_end_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    current_tier link_track.user_tier;
BEGIN
    -- Get current tier
    SELECT tier INTO current_tier
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Update user tier
    UPDATE link_track.users
    SET tier = 'paid',
        subscription_status = 'active',
        subscription_plan = p_subscription_plan,
        subscription_start_date = p_subscription_start_date,
        subscription_end_date = p_subscription_end_date,
        updated_at = CURRENT_TIMESTAMP
    WHERE uuid = p_user_id;
    
    -- Log the tier change
    PERFORM link_track.log_tier_change(
        p_user_id, 
        current_tier, 
        'paid', 
        'system', 
        'User upgraded to paid tier',
        jsonb_build_object('subscription_plan', p_subscription_plan, 'auto_upgrade', true)
    );
END;
$$ LANGUAGE plpgsql;

-- Update downgrade function to include audit logging
CREATE OR REPLACE FUNCTION link_track.downgrade_user_to_free(p_user_id VARCHAR(255))
RETURNS VOID AS $$
DECLARE
    current_tier link_track.user_tier;
BEGIN
    -- Get current tier
    SELECT tier INTO current_tier
    FROM link_track.users
    WHERE uuid = p_user_id;
    
    -- Update user tier
    UPDATE link_track.users
    SET tier = 'free',
        subscription_status = 'inactive',
        subscription_plan = NULL,
        subscription_start_date = NULL,
        subscription_end_date = NULL,
        monthly_dr_queries_used = 0,
        monthly_traffic_updates_used = 0,
        updated_at = CURRENT_TIMESTAMP
    WHERE uuid = p_user_id;
    
    -- Log the tier change
    PERFORM link_track.log_tier_change(
        p_user_id, 
        current_tier, 
        'free', 
        'system', 
        'User downgraded to free tier',
        jsonb_build_object('reason', 'subscription_ended', 'auto_downgrade', true)
    );
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Indexes for tier and subscription fields
CREATE INDEX IF NOT EXISTS idx_users_tier ON link_track.users(tier);
CREATE INDEX IF NOT EXISTS idx_users_subscription_status ON link_track.users(subscription_status);
CREATE INDEX IF NOT EXISTS idx_users_subscription_end_date ON link_track.users(subscription_end_date);

-- Indexes for usage history
CREATE INDEX IF NOT EXISTS idx_user_usage_history_user_id ON link_track.user_usage_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_usage_history_usage_type ON link_track.user_usage_history(usage_type);
CREATE INDEX IF NOT EXISTS idx_user_usage_history_usage_date ON link_track.user_usage_history(usage_date);

-- Indexes for tier change audit
CREATE INDEX IF NOT EXISTS idx_tier_change_audit_user_id ON link_track.tier_change_audit(user_id);
CREATE INDEX IF NOT EXISTS idx_tier_change_audit_changed_at ON link_track.tier_change_audit(changed_at);
CREATE INDEX IF NOT EXISTS idx_tier_change_audit_new_tier ON link_track.tier_change_audit(new_tier);

-- ========================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ========================================

-- Trigger for user_usage_history updated_at
CREATE TRIGGER update_user_usage_history_updated_at
    BEFORE UPDATE ON link_track.user_usage_history
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- Trigger for tier_limits updated_at
CREATE TRIGGER update_tier_limits_updated_at
    BEFORE UPDATE ON link_track.tier_limits
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- ========================================
-- PERMISSIONS
-- ========================================

-- Grant permissions for new tables and functions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.user_usage_history TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.user_usage_history TO authenticated;

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.tier_limits TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.tier_limits TO authenticated;

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.tier_change_audit TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.tier_change_audit TO authenticated;

-- Grant permissions for new functions
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA link_track TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA link_track TO authenticated;

-- ========================================
-- COMMENTS
-- ========================================

COMMENT ON TYPE link_track.user_tier IS 'User subscription tier: free or paid';
COMMENT ON TABLE link_track.user_usage_history IS 'Tracks API usage history for DR queries and traffic updates';
COMMENT ON TABLE link_track.tier_limits IS 'Defines resource limits for each user tier';
COMMENT ON FUNCTION link_track.get_user_usage_summary(VARCHAR) IS 'Returns comprehensive usage summary for a user including current counts and limits';

COMMIT;

-- Final message
DO $$
BEGIN
    RAISE NOTICE 'User tier system migration completed successfully!';
    RAISE NOTICE 'Added: user_tier enum, tier fields to users table, tier_limits table, user_usage_history table';
    RAISE NOTICE 'Functions created for tier checking and usage tracking';
    RAISE NOTICE 'Default limits set: Free (5 projects, 10 domains, 1000 links), Paid (1000 projects, 1000 domains, unlimited links)';
END $$;