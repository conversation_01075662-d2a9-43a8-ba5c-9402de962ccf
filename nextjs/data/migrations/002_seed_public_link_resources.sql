-- Seed data for public_link_resources table
-- This file contains sample data for testing the public link resources feature

BEGIN;

-- Insert sample public link resources
INSERT INTO link_track.public_link_resources (
    domain, title, website_url, submission_method, submission_url, 
    contact_email, is_paid, price_range, currency, category, description, 
    requirements, response_time, success_rate, is_active
) VALUES 
-- High DR Free Resources
(
    'github.com',
    'GitHub - Developer Profiles',
    'https://github.com',
    'Profile Creation',
    'https://github.com/join',
    NULL,
    FALSE,
    NULL,
    'USD',
    'directory',
    'Create a developer profile with links to your projects and website.',
    'Must be a legitimate developer or organization',
    '1-2 days',
    95,
    TRUE
),
(
    'reddit.com',
    'Reddit - Community Discussions',
    'https://reddit.com',
    'Community Posts',
    'https://reddit.com/submit',
    NULL,
    FALSE,
    NULL,
    'USD',
    'forum',
    'Share your content in relevant subreddit communities.',
    'Follow subreddit rules, no spam, provide value',
    '1-3 days',
    70,
    TRUE
),
(
    'medium.com',
    'Medium - Publishing Platform',
    'https://medium.com',
    'Content Publishing',
    'https://medium.com/new-story',
    NULL,
    FALSE,
    NULL,
    'USD',
    'blog',
    'Publish high-quality articles with backlinks to your website.',
    'Original, well-written content with value to readers',
    '1 week',
    85,
    TRUE
),

-- High DR Paid Resources
(
    'techcrunch.com',
    'TechCrunch - Startup Press',
    'https://techcrunch.com',
    'Press Release',
    'https://techcrunch.com/pages/contact-us/',
    '<EMAIL>',
    TRUE,
    '$2000-5000',
    'USD',
    'news',
    'Premier technology news coverage for startups and established companies.',
    'Newsworthy story, significant funding, major product launch',
    '1-2 weeks',
    25,
    TRUE
),
(
    'forbes.com',
    'Forbes - Business Magazine',
    'https://forbes.com',
    'Email Pitch',
    NULL,
    '<EMAIL>',
    TRUE,
    '$3000-8000',
    'USD',
    'news',
    'Business and entrepreneurship coverage in Forbes magazine.',
    'C-level executives, significant business achievements',
    '2-4 weeks',
    15,
    TRUE
),

-- Medium DR Resources
(
    'producthunt.com',
    'Product Hunt - Product Discovery',
    'https://producthunt.com',
    'Product Submission',
    'https://producthunt.com/ship',
    NULL,
    FALSE,
    NULL,
    'USD',
    'startup-directory',
    'Launch your product to the Product Hunt community.',
    'Working product, good design, clear value proposition',
    '1-3 days',
    90,
    TRUE
),
(
    'betalist.com',
    'BetaList - Startup Directory',
    'https://betalist.com',
    'Submission Form',
    'https://betalist.com/submit',
    NULL,
    TRUE,
    '$49-99',
    'USD',
    'startup-directory',
    'Get featured in startup directory for early-stage companies.',
    'Early-stage startup, beta or pre-launch product',
    '3-5 days',
    80,
    TRUE
),

-- Tool Directories
(
    'alternativeto.net',
    'AlternativeTo - Software Alternatives',
    'https://alternativeto.net',
    'Software Listing',
    'https://alternativeto.net/software/register/',
    NULL,
    FALSE,
    NULL,
    'USD',
    'tool-directory',
    'List your software as an alternative to popular tools.',
    'Working software product, clear differentiation',
    '1 week',
    85,
    TRUE
),
(
    'capterra.com',
    'Capterra - Software Reviews',
    'https://capterra.com',
    'Vendor Registration',
    'https://vendors.capterra.com/',
    NULL,
    TRUE,
    '$200-500/month',
    'USD',
    'tool-directory',
    'List your business software on Capterra marketplace.',
    'B2B software, customer reviews, pricing information',
    '1-2 weeks',
    75,
    TRUE
),

-- Guest Post Opportunities
(
    'hackernoon.com',
    'HackerNoon - Tech Publishing',
    'https://hackernoon.com',
    'Guest Post Submission',
    'https://app.hackernoon.com/new',
    NULL,
    FALSE,
    NULL,
    'USD',
    'guest-post',
    'Publish technical articles and thought leadership content.',
    'Technical expertise, original insights, 1000+ words',
    '1-2 weeks',
    60,
    TRUE
);

-- Also ensure these domains exist in all_links table
INSERT INTO link_track.all_links (domain, dr_score, traffic, backlink_count, is_indexed, last_updated)
VALUES 
    ('github.com', 95, 45000000, 2500000, TRUE, NOW()),
    ('reddit.com', 91, 52000000, 1800000, TRUE, NOW()),
    ('medium.com', 85, 180000000, 890000, TRUE, NOW()),
    ('techcrunch.com', 92, 25000000, 450000, TRUE, NOW()),
    ('forbes.com', 94, 95000000, 780000, TRUE, NOW()),
    ('producthunt.com', 83, 8500000, 125000, TRUE, NOW()),
    ('betalist.com', 65, 450000, 15000, TRUE, NOW()),
    ('alternativeto.net', 76, 12000000, 85000, TRUE, NOW()),
    ('capterra.com', 81, 18000000, 180000, TRUE, NOW()),
    ('hackernoon.com', 72, 5500000, 75000, TRUE, NOW())
ON CONFLICT (domain) DO UPDATE SET
    dr_score = EXCLUDED.dr_score,
    traffic = EXCLUDED.traffic,
    backlink_count = EXCLUDED.backlink_count,
    is_indexed = EXCLUDED.is_indexed,
    last_updated = NOW();

COMMIT;

-- Verification queries
SELECT 
    plr.title,
    plr.domain,
    plr.category,
    plr.is_paid,
    al.dr_score,
    al.traffic
FROM link_track.public_link_resources plr
LEFT JOIN link_track.all_links al ON plr.domain = al.domain
ORDER BY al.dr_score DESC;