-- Migrate existing domain data to new domain management system
-- Migration: 005_migrate_domain_data.sql
-- Purpose: Transfer domain data from all_links table and project configs to domain_management

BEGIN;

-- ========================================
-- MIGRATE DOMAIN DATA FROM ALL_LINKS
-- ========================================

-- Note: The all_links table contains domain-level statistics without user associations
-- and does not contain whois_data or user_id columns. This section is commented out
-- as it was based on incorrect table structure assumptions.

-- If you have a different table that contains user-specific domain data with WHOIS information,
-- you can modify this section to migrate from the correct source table.

/*
-- Example migration from a hypothetical user_domains table:
INSERT INTO link_track.domain_management (
    domain, 
    user_id, 
    registrar, 
    created_date, 
    expiry_date, 
    name_servers, 
    status,
    whois_data, 
    whois_last_updated,
    whois_cache_expires,
    notes
)
SELECT DISTINCT ON (domain, user_id)
    domain,
    user_id,
    (whois_data::jsonb->>'registrar') as registrar,
    -- ... rest of the migration logic
FROM your_actual_source_table
WHERE domain IS NOT NULL 
AND user_id IS NOT NULL
AND whois_data IS NOT NULL
ON CONFLICT (domain, user_id) DO NOTHING;
*/

-- ========================================
-- MIGRATE PROJECT DOMAIN ASSOCIATIONS
-- ========================================

-- Note: This section was originally intended to create associations between 
-- existing domain records (from all_links migration) and projects.
-- Since all_links migration was removed, this will only work if domain records
-- already exist in domain_management table from other sources.

-- Create associations between existing domain records and projects
INSERT INTO link_track.domain_project_associations (
    domain_id,
    project_id,
    user_id,
    is_primary
)
SELECT DISTINCT
    dm.id as domain_id,
    p.id as project_id,
    p.user_id,
    true as is_primary -- Mark as primary since it's the main project domain
FROM link_track.projects p
JOIN link_track.domain_management dm ON dm.domain = p.domain AND dm.user_id = p.user_id
WHERE p.domain IS NOT NULL 
AND p.domain != ''
ON CONFLICT (domain_id, project_id) DO NOTHING;

-- Note: The unique constraint for primary domains will ensure only one primary per project
-- If multiple domains are associated with a project, only the first one will be marked as primary

-- ========================================
-- MIGRATE DOMAINS FROM PROJECT CONFIG
-- ========================================

-- Handle domains that exist in projects but not yet in domain_management
INSERT INTO link_track.domain_management (
    domain, 
    user_id, 
    status,
    notes
)
SELECT DISTINCT
    p.domain,
    p.user_id,
    'active' as status,
    'Migrated from project: ' || p.name as notes
FROM link_track.projects p
LEFT JOIN link_track.domain_management dm ON dm.domain = p.domain AND dm.user_id = p.user_id
WHERE p.domain IS NOT NULL 
AND p.domain != ''
AND dm.id IS NULL -- Only insert if not already exists
ON CONFLICT (domain, user_id) DO NOTHING;

-- Create associations for these newly added domains
INSERT INTO link_track.domain_project_associations (
    domain_id,
    project_id,
    user_id,
    is_primary
)
SELECT DISTINCT
    dm.id as domain_id,
    p.id as project_id,
    p.user_id,
    -- Only mark as primary if no other domain is already primary for this project
    NOT EXISTS (
        SELECT 1 FROM link_track.domain_project_associations existing 
        WHERE existing.project_id = p.id AND existing.is_primary = true
    ) as is_primary
FROM link_track.projects p
JOIN link_track.domain_management dm ON dm.domain = p.domain AND dm.user_id = p.user_id
LEFT JOIN link_track.domain_project_associations dpa ON dpa.domain_id = dm.id AND dpa.project_id = p.id
WHERE p.domain IS NOT NULL 
AND p.domain != ''
AND dpa.id IS NULL -- Only insert if association doesn't exist
ON CONFLICT (domain_id, project_id) DO NOTHING;

-- ========================================
-- MIGRATE USER CONFIG DOMAIN SETTINGS
-- ========================================

-- Check if there are any domain-related configurations in user_configs
-- This is a placeholder for any domain-specific settings that might exist
-- Update as needed based on actual config structure

-- Example: Migrate domain monitoring preferences if they exist
UPDATE link_track.domain_management dm
SET 
    monitor_expiry = true,
    alert_days_before = 30
WHERE dm.id IN (
    SELECT dm2.id
    FROM link_track.domain_management dm2
    JOIN link_track.user_configs uc ON uc.user_id = dm2.user_id
    WHERE uc.config_type LIKE '%domain%'
    AND uc.config_data IS NOT NULL
);

-- ========================================
-- UPDATE PROJECT CONFIG COLUMN
-- ========================================

-- Add column to projects table to indicate migration status
-- This allows for tracking which projects have been migrated
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'link_track' 
        AND table_name = 'projects' 
        AND column_name = 'domain_migrated'
    ) THEN
        ALTER TABLE link_track.projects ADD COLUMN domain_migrated BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Mark projects as migrated if they have domain associations
UPDATE link_track.projects p
SET domain_migrated = true
WHERE EXISTS (
    SELECT 1 
    FROM link_track.domain_project_associations dpa
    JOIN link_track.domain_management dm ON dpa.domain_id = dm.id
    WHERE dpa.project_id = p.id
    AND dm.domain = p.domain
);

-- ========================================
-- INFER DNS PROVIDERS FROM NAMESERVERS
-- ========================================

-- Update DNS providers based on nameserver patterns
UPDATE link_track.domain_management 
SET dns_provider = CASE
    WHEN name_servers IS NOT NULL AND array_length(name_servers, 1) > 0 THEN
        CASE 
            WHEN name_servers[1] ILIKE '%cloudflare%' THEN 'Cloudflare'
            WHEN name_servers[1] ILIKE '%amazonaws%' THEN 'AWS Route 53'
            WHEN name_servers[1] ILIKE '%google%' OR name_servers[1] ILIKE '%googledomains%' THEN 'Google Domains'
            WHEN name_servers[1] ILIKE '%godaddy%' THEN 'GoDaddy'
            WHEN name_servers[1] ILIKE '%namecheap%' THEN 'Namecheap'
            WHEN name_servers[1] ILIKE '%digitalocean%' THEN 'DigitalOcean'
            WHEN name_servers[1] ILIKE '%linode%' THEN 'Linode'
            WHEN name_servers[1] ILIKE '%vultr%' THEN 'Vultr'
            ELSE 'Other'
        END
    ELSE NULL
END
WHERE dns_provider IS NULL;

-- ========================================
-- CLEAN UP AND VERIFICATION
-- ========================================

-- Update statistics for verification
DO $$
DECLARE
    total_domains INTEGER;
    total_associations INTEGER;
    total_projects_migrated INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_domains FROM link_track.domain_management;
    SELECT COUNT(*) INTO total_associations FROM link_track.domain_project_associations;
    SELECT COUNT(*) INTO total_projects_migrated FROM link_track.projects WHERE domain_migrated = true;
    
    RAISE NOTICE 'Domain migration completed successfully!';
    RAISE NOTICE 'Total domains migrated: %', total_domains;
    RAISE NOTICE 'Total project associations created: %', total_associations;
    RAISE NOTICE 'Total projects with migrated domains: %', total_projects_migrated;
END $$;

-- Create indexes if they don't exist (in case migration runs before main schema)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_domain_management_user_domain') THEN
        CREATE INDEX idx_domain_management_user_domain ON link_track.domain_management(user_id, domain);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_domain_project_associations_user_id') THEN
        CREATE INDEX idx_domain_project_associations_user_id ON link_track.domain_project_associations(user_id);
    END IF;
END $$;

-- Grant permissions for any new data
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_management TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_management TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_project_associations TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.domain_project_associations TO authenticated;

COMMIT;

-- ========================================
-- POST-MIGRATION RECOMMENDATIONS
-- ========================================

-- This query can be run after migration to identify any potential issues
/*
-- Run this query to check migration results:

SELECT 
    'Domains without projects' as check_type,
    COUNT(*) as count
FROM link_track.domain_management dm
LEFT JOIN link_track.domain_project_associations dpa ON dm.id = dpa.domain_id
WHERE dpa.id IS NULL

UNION ALL

SELECT 
    'Projects without domain records' as check_type,
    COUNT(*) as count
FROM link_track.projects p
LEFT JOIN link_track.domain_management dm ON dm.domain = p.domain AND dm.user_id = p.user_id
WHERE p.domain IS NOT NULL 
AND p.domain != ''
AND dm.id IS NULL

UNION ALL

SELECT 
    'Domains with WHOIS data' as check_type,
    COUNT(*) as count
FROM link_track.domain_management
WHERE whois_data IS NOT NULL

UNION ALL

SELECT 
    'Domains needing WHOIS refresh' as check_type,
    COUNT(*) as count
FROM link_track.domain_management
WHERE whois_cache_expires IS NULL OR whois_cache_expires <= NOW();
*/