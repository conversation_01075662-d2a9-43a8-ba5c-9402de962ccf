-- Migration: Add category support to projects table
-- This migration adds category field to support project categorization

BEGIN;

-- Add category field to projects table
ALTER TABLE link_track.projects 
ADD COLUMN IF NOT EXISTS category VARCHAR(100);

-- Add index for better query performance on category
CREATE INDEX IF NOT EXISTS idx_projects_category 
ON link_track.projects(category);

-- Add index for category filtering with user
CREATE INDEX IF NOT EXISTS idx_projects_user_category 
ON link_track.projects(user_id, category);

-- Add comment for documentation
COMMENT ON COLUMN link_track.projects.category IS 'User-defined category for project organization (e.g., gaming, tools, blog, etc.)';

-- Create a function to get user categories (used for UI suggestions)
CREATE OR REPLACE FUNCTION link_track.get_user_project_categories(
    p_user_id VARCHAR(255)
)
RETURNS TABLE(
    category VARCHAR(100),
    project_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.category,
        COUNT(*) as project_count
    FROM link_track.projects p
    WHERE p.user_id = p_user_id 
        AND p.category IS NOT NULL 
        AND p.category != ''
    GROUP BY p.category
    ORDER BY project_count DESC, p.category ASC;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on the function
GRANT EXECUTE ON FUNCTION link_track.get_user_project_categories(VARCHAR(255)) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_user_project_categories(VARCHAR(255)) TO authenticated;

COMMIT; 