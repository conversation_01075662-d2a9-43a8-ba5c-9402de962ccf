-- Fix get_user_usage_summary function type mismatch
-- The COUNT() function returns bigint but the function expects integer
-- This causes the error: "structure of query does not match function result type"

BEGIN;

-- Drop and recreate the function with proper type casting
CREATE OR REPLACE FUNCTION link_track.get_user_usage_summary(p_user_id VARCHAR(255))
RETURNS TABLE(
    tier link_track.user_tier,
    subscription_status VARCHAR(50),
    projects_count INTEGER,
    projects_limit INTEGER,
    domains_count INTEGER,
    domains_limit INTEGER,
    link_resources_count INTEGER,
    link_resources_limit INTEGER,
    monthly_dr_queries_used INTEGER,
    monthly_dr_queries_limit INTEGER,
    monthly_traffic_updates_used INTEGER,
    monthly_traffic_updates_limit INTEGER,
    usage_reset_date TIMESTAMPTZ
) AS $$
DECLARE
    user_rec RECORD;
    limits_rec RECORD;
BEGIN
    -- Get user info
    SELECT u.tier, u.subscription_status, u.monthly_dr_queries_used, 
           u.monthly_traffic_updates_used, u.monthly_usage_reset_date
    INTO user_rec
    FROM link_track.users u
    WHERE u.uuid = p_user_id;
    
    -- Handle case where user doesn't exist
    IF user_rec.tier IS NULL THEN
        RETURN;
    END IF;
    
    -- Get current counts (cast COUNT to integer to avoid bigint/integer mismatch)
    SELECT 
        COUNT(DISTINCT p.id)::INTEGER as projects_count,
        COUNT(DISTINCT p.domain)::INTEGER as domains_count,
        COUNT(DISTINCT lr.id)::INTEGER as link_resources_count
    INTO limits_rec
    FROM link_track.users u
    LEFT JOIN link_track.projects p ON p.user_id = u.uuid
    LEFT JOIN link_track.link_resources lr ON lr.user_id = u.uuid
    WHERE u.uuid = p_user_id;
    
    RETURN QUERY
    SELECT 
        user_rec.tier,
        user_rec.subscription_status,
        limits_rec.projects_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'projects'),
        limits_rec.domains_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'domains'),
        limits_rec.link_resources_count,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'link_resources'),
        user_rec.monthly_dr_queries_used,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'monthly_dr_queries'),
        user_rec.monthly_traffic_updates_used,
        (SELECT tl.limit_value FROM link_track.tier_limits tl WHERE tl.tier = user_rec.tier AND tl.limit_type = 'monthly_traffic_updates'),
        user_rec.monthly_usage_reset_date;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the fix
COMMENT ON FUNCTION link_track.get_user_usage_summary(VARCHAR) IS 'Fixed type mismatch: COUNT() returns bigint, cast to integer to match return type';

COMMIT;

-- Test the function after applying the fix
DO $$
BEGIN
    RAISE NOTICE 'Migration 010 completed: Fixed get_user_usage_summary type mismatch';
    RAISE NOTICE 'The function now properly casts COUNT() results to INTEGER to avoid bigint/integer type conflicts';
END $$; 