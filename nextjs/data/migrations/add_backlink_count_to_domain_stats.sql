-- Migration to add support for backlink_count in domain stats updates
-- This function extends the existing update_domain_stats to include backlink_count

-- Function to update domain stats with backlink count
CREATE OR REPLACE FUNCTION link_track.update_domain_stats_with_backlinks(
    p_domain VARCHAR(255),
    p_dr_score INTEGER DEFAULT NULL,
    p_traffic INTEGER DEFAULT 0,
    p_is_indexed BOOLEAN DEFAULT FALSE,
    p_backlink_count INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
    -- Insert into history
    INSERT INTO link_track.all_links_history (domain, dr_score, traffic, backlink_count, is_indexed, checked_at)
    VALUES (p_domain, p_dr_score, p_traffic, p_backlink_count, p_is_indexed, NOW());
    
    -- Update or insert current stats
    INSERT INTO link_track.all_links (domain, dr_score, traffic, backlink_count, is_indexed, last_updated)
    VALUES (p_domain, p_dr_score, p_traffic, p_backlink_count, p_is_indexed, NOW())
    ON CONFLICT (domain) DO UPDATE SET
        dr_score = COALESCE(EXCLUDED.dr_score, link_track.all_links.dr_score),
        traffic = EXCLUDED.traffic,
        backlink_count = EXCLUDED.backlink_count,
        is_indexed = EXCLUDED.is_indexed,
        last_updated = NOW();
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION link_track.update_domain_stats_with_backlinks(VARCHAR, INTEGER, INTEGER, BOOLEAN, INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.update_domain_stats_with_backlinks(VARCHAR, INTEGER, INTEGER, BOOLEAN, INTEGER) TO authenticated; 