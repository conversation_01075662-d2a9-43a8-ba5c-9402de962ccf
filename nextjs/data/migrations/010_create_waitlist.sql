-- Migration 010: Create Waitlist System
-- Add waitlist table for collecting early user interest

BEGIN;

-- ========================================
-- WAITLIST TABLE
-- ========================================

-- Create waitlist table
CREATE TABLE IF NOT EXISTS link_track.waitlist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    message TEXT,
    source VARCHAR(100) DEFAULT 'landing_page', -- tracking where signup came from
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'contacted', 'converted', 'unsubscribed')),
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    contacted_at TIMESTAMPTZ,
    converted_at TIMESTAMPTZ,
    
    -- Constraints
    UNIQUE(email) -- Prevent duplicate emails
);

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Indexes for waitlist table
CREATE INDEX IF NOT EXISTS idx_waitlist_email ON link_track.waitlist(email);
CREATE INDEX IF NOT EXISTS idx_waitlist_status ON link_track.waitlist(status);
CREATE INDEX IF NOT EXISTS idx_waitlist_created_at ON link_track.waitlist(created_at);
CREATE INDEX IF NOT EXISTS idx_waitlist_source ON link_track.waitlist(source);
CREATE INDEX IF NOT EXISTS idx_waitlist_utm_campaign ON link_track.waitlist(utm_campaign);

-- Composite index for analytics queries
CREATE INDEX IF NOT EXISTS idx_waitlist_status_created_at ON link_track.waitlist(status, created_at);
CREATE INDEX IF NOT EXISTS idx_waitlist_source_status ON link_track.waitlist(source, status);

-- ========================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ========================================

-- Add updated_at trigger
DROP TRIGGER IF EXISTS update_waitlist_updated_at ON link_track.waitlist;
CREATE TRIGGER update_waitlist_updated_at
    BEFORE UPDATE ON link_track.waitlist
    FOR EACH ROW
    EXECUTE FUNCTION link_track.update_updated_at_column();

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Function to add user to waitlist
CREATE OR REPLACE FUNCTION link_track.add_to_waitlist(
    p_email VARCHAR(255),
    p_name VARCHAR(255) DEFAULT NULL,
    p_message TEXT DEFAULT NULL,
    p_source VARCHAR(100) DEFAULT 'landing_page',
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_referrer TEXT DEFAULT NULL,
    p_utm_source VARCHAR(255) DEFAULT NULL,
    p_utm_medium VARCHAR(255) DEFAULT NULL,
    p_utm_campaign VARCHAR(255) DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS link_track.waitlist AS $$
DECLARE
    result link_track.waitlist%ROWTYPE;
BEGIN
    INSERT INTO link_track.waitlist (
        email, name, message, source, user_agent, ip_address, 
        referrer, utm_source, utm_medium, utm_campaign, metadata,
        created_at, updated_at
    )
    VALUES (
        LOWER(TRIM(p_email)), p_name, p_message, p_source, p_user_agent, p_ip_address,
        p_referrer, p_utm_source, p_utm_medium, p_utm_campaign, p_metadata,
        NOW(), NOW()
    )
    ON CONFLICT (email) 
    DO UPDATE SET
        name = COALESCE(EXCLUDED.name, link_track.waitlist.name),
        message = COALESCE(EXCLUDED.message, link_track.waitlist.message),
        source = EXCLUDED.source,
        user_agent = COALESCE(EXCLUDED.user_agent, link_track.waitlist.user_agent),
        ip_address = COALESCE(EXCLUDED.ip_address, link_track.waitlist.ip_address),
        referrer = COALESCE(EXCLUDED.referrer, link_track.waitlist.referrer),
        utm_source = COALESCE(EXCLUDED.utm_source, link_track.waitlist.utm_source),
        utm_medium = COALESCE(EXCLUDED.utm_medium, link_track.waitlist.utm_medium),
        utm_campaign = COALESCE(EXCLUDED.utm_campaign, link_track.waitlist.utm_campaign),
        metadata = EXCLUDED.metadata,
        updated_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to update waitlist status
CREATE OR REPLACE FUNCTION link_track.update_waitlist_status(
    p_id UUID,
    p_status VARCHAR(50)
)
RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE link_track.waitlist
    SET 
        status = p_status,
        contacted_at = CASE WHEN p_status = 'contacted' THEN NOW() ELSE contacted_at END,
        converted_at = CASE WHEN p_status = 'converted' THEN NOW() ELSE converted_at END,
        updated_at = NOW()
    WHERE id = p_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to get waitlist statistics
CREATE OR REPLACE FUNCTION link_track.get_waitlist_stats()
RETURNS TABLE(
    total_signups INTEGER,
    pending INTEGER,
    contacted INTEGER,
    converted INTEGER,
    unsubscribed INTEGER,
    signups_today INTEGER,
    signups_this_week INTEGER,
    signups_this_month INTEGER,
    conversion_rate DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_signups,
        COUNT(CASE WHEN status = 'pending' THEN 1 END)::INTEGER as pending,
        COUNT(CASE WHEN status = 'contacted' THEN 1 END)::INTEGER as contacted,
        COUNT(CASE WHEN status = 'converted' THEN 1 END)::INTEGER as converted,
        COUNT(CASE WHEN status = 'unsubscribed' THEN 1 END)::INTEGER as unsubscribed,
        COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END)::INTEGER as signups_today,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END)::INTEGER as signups_this_week,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END)::INTEGER as signups_this_month,
        CASE 
            WHEN COUNT(CASE WHEN status IN ('contacted', 'converted') THEN 1 END) > 0 
            THEN ROUND(
                COUNT(CASE WHEN status = 'converted' THEN 1 END)::DECIMAL / 
                COUNT(CASE WHEN status IN ('contacted', 'converted') THEN 1 END)::DECIMAL * 100, 
                2
            )
            ELSE 0
        END as conversion_rate
    FROM link_track.waitlist;
END;
$$ LANGUAGE plpgsql;

-- Function to get waitlist signups by source
CREATE OR REPLACE FUNCTION link_track.get_waitlist_by_source()
RETURNS TABLE(
    source VARCHAR(100),
    count INTEGER,
    percentage DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH source_counts AS (
        SELECT 
            w.source,
            COUNT(*)::INTEGER as count,
            COUNT(*) * 100.0 / SUM(COUNT(*)) OVER () as percentage
        FROM link_track.waitlist w
        GROUP BY w.source
    )
    SELECT 
        sc.source,
        sc.count,
        ROUND(sc.percentage, 2) as percentage
    FROM source_counts sc
    ORDER BY sc.count DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get recent waitlist signups
CREATE OR REPLACE FUNCTION link_track.get_recent_waitlist_signups(p_limit INTEGER DEFAULT 10)
RETURNS TABLE(
    id UUID,
    email VARCHAR(255),
    name VARCHAR(255),
    message TEXT,
    source VARCHAR(100),
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        w.id,
        w.email,
        w.name,
        w.message,
        w.source,
        w.created_at
    FROM link_track.waitlist w
    ORDER BY w.created_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- ROW LEVEL SECURITY (RLS)
-- ========================================

-- Enable RLS
ALTER TABLE link_track.waitlist ENABLE ROW LEVEL SECURITY;

-- Policy for service role (full access)
CREATE POLICY "Service role can manage all waitlist entries" ON link_track.waitlist
    FOR ALL TO service_role USING (true);

-- Policy for authenticated users (read-only for admins)
CREATE POLICY "Authenticated users can read waitlist" ON link_track.waitlist
    FOR SELECT TO authenticated USING (true);

-- ========================================
-- PERMISSIONS
-- ========================================

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.waitlist TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE link_track.waitlist TO authenticated;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION link_track.add_to_waitlist(VARCHAR, VARCHAR, TEXT, VARCHAR, TEXT, INET, TEXT, VARCHAR, VARCHAR, VARCHAR, JSONB) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.add_to_waitlist(VARCHAR, VARCHAR, TEXT, VARCHAR, TEXT, INET, TEXT, VARCHAR, VARCHAR, VARCHAR, JSONB) TO authenticated;

GRANT EXECUTE ON FUNCTION link_track.update_waitlist_status(UUID, VARCHAR) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.update_waitlist_status(UUID, VARCHAR) TO authenticated;

GRANT EXECUTE ON FUNCTION link_track.get_waitlist_stats() TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_waitlist_stats() TO authenticated;

GRANT EXECUTE ON FUNCTION link_track.get_waitlist_by_source() TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_waitlist_by_source() TO authenticated;

GRANT EXECUTE ON FUNCTION link_track.get_recent_waitlist_signups(INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION link_track.get_recent_waitlist_signups(INTEGER) TO authenticated;

-- ========================================
-- COMMENTS FOR DOCUMENTATION
-- ========================================

COMMENT ON TABLE link_track.waitlist IS 'Stores waitlist signups with email, message, and tracking data for early user interest';
COMMENT ON COLUMN link_track.waitlist.email IS 'User email address, normalized to lowercase and trimmed';
COMMENT ON COLUMN link_track.waitlist.source IS 'Source of signup: landing_page, referral, social_media, etc.';
COMMENT ON COLUMN link_track.waitlist.status IS 'Current status: pending, contacted, converted, unsubscribed';
COMMENT ON COLUMN link_track.waitlist.metadata IS 'Additional tracking data in JSON format';

COMMENT ON FUNCTION link_track.add_to_waitlist(VARCHAR, VARCHAR, TEXT, VARCHAR, TEXT, INET, TEXT, VARCHAR, VARCHAR, VARCHAR, JSONB) IS 'Add user to waitlist with tracking data, handles duplicates';
COMMENT ON FUNCTION link_track.get_waitlist_stats() IS 'Get comprehensive waitlist statistics including conversion rates';

COMMIT;

-- Migration completion notice
DO $$
BEGIN
    RAISE NOTICE 'Migration 010 completed: Waitlist system created';
    RAISE NOTICE 'Added: waitlist table with email, name, message, and tracking fields';
    RAISE NOTICE 'Added: Helper functions for waitlist management and analytics';
    RAISE NOTICE 'Added: RLS policies for secure access control';
END $$; 