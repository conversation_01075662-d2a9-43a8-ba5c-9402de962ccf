export const dynamic = 'force-dynamic'
export const revalidate = 0 // revalidate at every request

import DashboardLayout from "@/components/dashboard/layout";
import Empty from "@/components/blocks/empty";
import { ReactNode } from "react";
import { Sidebar } from "@/types/blocks/sidebar";
import { getUserInfo } from "@/services/user";
import { redirect } from "next/navigation";
import { Toaster } from "@/components/ui/toaster";

export default async function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  // Check admin authentication
  const userInfo = await getUserInfo();
  if ((!userInfo || !userInfo.email) && process.env.NODE_ENV !== "development") {
    redirect("/auth/signin");
  }

  const adminEmails = process.env.ADMIN_EMAILS?.split(",");
  if (!adminEmails?.includes(userInfo?.email) && process.env.NODE_ENV !== "development") {
    return <Empty message="No access" />;
  }
  
  const sidebar: Sidebar = {
    brand: {
      title: "AiMCP",
      logo: {
        src: "/logo.png",
        alt: "AiMCP",
      },
      url: "/admin",
    },
    nav: {
      items: [
        {
          title: "Users",
          url: "/admin/users",
          icon: "RiUserLine",
        },
        {
          title: "Settings",
          url: "/admin/settings",
          icon: "RiSettingsLine",
        },
        {
          title: "Email Management",
          url: "/admin/emails",
          icon: "RiMailLine",
        },
        {
          title: "Orders",
          icon: "RiOrderPlayLine",
          is_expand: true,
          children: [
            {
              title: "Paid Orders",
              url: "/admin/paid-orders",
            },
          ],
        },
        {
          title: "Posts",
          url: "/admin/posts",
          icon: "RiArticleLine",
        },
        {
          title: "Image Hosting",
          url: "/admin/image-hosting",
          icon: "RiImage2Line",
        },
        {
          title: "Link Management",
          url: "/admin/links",
          icon: "RiLinksLine",
          is_expand: true,
          children: [
            {
              title: "Public Links",
              url: "/admin/public-link-resources",
            },
            {
              title: "Link Resources",
              url: "/admin/links/link-resources",
            },
            {
              title: "All Links",
              url: "/admin/links/all-links",
            },
            {
              title: "History",
              url: "/admin/links/history",
            },
            {
              title: "Discovered Links",
              url: "/admin/links/discovered",
            },
            {
              title: "Projects List",
              url: "/admin/links/projects-list",
            },
            {
              title: "User Configs",
              url: "/admin/links/user-configs",
            }
          ],
        },
      ],
    },
    social: {
      items: [
        {
          title: "Home",
          url: "/",
          target: "_blank",
          icon: "RiHomeLine",
        },
        {
          title: "Discord",
          url: "https://discord.gg/HQNnrzjZQS",
          target: "_blank",
          icon: "RiDiscordLine",
        }
      ],
    },
  };

  return (
    <>
      <DashboardLayout sidebar={sidebar}>{children}</DashboardLayout>
      <Toaster />
    </>
  );
}
