
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getUsers, findUserByUuid } from "@/models/user";
import moment from "moment";
import Image from "next/image";


export default async function () {
  const users = await getUsers(1, 50) || [];

  // Process data to include invitation information
  const processedData = await Promise.all(users.map(async user => {
    // Get inviter information if invited_by exists
    let inviterInfo = "";
    if (user.invited_by) {
      const inviter = await findUserByUuid(user.invited_by);
      if (inviter) {
        inviterInfo = `${inviter.nickname || inviter.email} (${inviter.uuid})`;
      } else {
        inviterInfo = user.invited_by;
      }
    }
    
    return {
      ...user,
      // Process avatar column
      avatar_display: (
        <Image 
          src={user.avatar_url} 
          alt={user.nickname || user.email}
          width={40}
          height={40}
          className="rounded-full"
        />
      ),
      // Process created_at column
      created_at_display: moment(user.created_at).format("YYYY-MM-DD HH:mm:ss"),
      // Process invitation columns
      invited_by_display: inviterInfo || "—",
      invites_count_display: user.invites_count || 0,
    };
  }));

  const columns: TableColumn[] = [
    { name: "uuid", title: "UUID" },
    { name: "email", title: "Email" },
    { name: "nickname", title: "Name" },
    {
      name: "avatar_display",
      title: "Avatar",
    },
    {
      name: "created_at_display",
      title: "Created At",
    },
    {
      name: "invites_count_display",
      title: "Invites",
    },
    {
      name: "invited_by_display",
      title: "Invited By",
    },
  ];

  const table: TableSlotType = {
    title: "All Users",
    columns,
    data: processedData,
  };

  return <TableSlot {...table} />;
}
