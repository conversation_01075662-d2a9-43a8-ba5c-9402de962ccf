
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getPaiedOrders } from "@/models/order";
import moment from "moment";


export default async function () {
  const orders = await getPaiedOrders(1, 50) || [];

  // 预处理数据以避免直接传递回调函数给客户端组件
  const processedData = orders.map(order => {
    return {
      ...order,
      // 预处理创建时间列
      created_at_display: moment(order.created_at).format("YYYY-MM-DD HH:mm:ss"),
    };
  });

  const columns: TableColumn[] = [
    { name: "order_no", title: "Order No" },
    { name: "paid_email", title: "Paid Email" },
    { name: "product_name", title: "Product Name" },
    { name: "amount", title: "Amount" },
    {
      name: "created_at_display",
      title: "Created At",
    },
  ];

  const table: TableSlotType = {
    title: "Paid Orders",
    columns,
    data: processedData,
  };

  return <TableSlot {...table} />;
}
