export const dynamic = 'force-dynamic';

import { ApikeyStatus, insertApikey } from "@/models/apikey";
import { Apikey } from "@/types/apikey";
import FormSlot from "@/components/dashboard/slots/form";
import { Form as FormSlotType } from "@/types/slots/form";
import { getIsoTimestr } from "@/lib/time";
import { getNonceStr } from "@/lib/hash";
import { findUserByUuid } from "@/models/user";
import { redirect } from "next/navigation";


export default async function CreateUserApiKey({ params }: { params: Promise<{ userId: string }> }) {
  const { userId } = await params;
  const user = await findUserByUuid(userId);
  
  if (!user) {
    redirect("/admin/api-keys");
  }

  const form: FormSlotType = {
    title: `Create API Key for ${user.nickname || user.email || userId}`,
    crumb: {
      items: [
        {
          title: "API Keys",
          url: "/admin/api-keys",
        },
        {
          title: user.nickname || user.email || userId,
          url: `/admin/api-keys/${userId}`,
        },
        {
          title: "Create API Key",
          is_active: true,
        },
      ],
    },
    fields: [
      {
        title: "Name",
        name: "title",
        type: "text",
        placeholder: "Enter a name for this API key",
        validation: {
          required: true,
        },
      },
    ],
    passby: {
      user_uuid: userId,
    },
    submit: {
      button: {
        title: "Create API Key",
      },
      handler: async (data: FormData, passby: any) => {
        "use server";

        const { user_uuid } = passby;
        if (!user_uuid) {
          throw new Error("Invalid user ID");
        }

        const title = data.get("title") as string;
        if (!title || !title.trim()) {
          throw new Error("Name is required");
        }

        const key = `sk-${getNonceStr(32)}`;

        const apikey: Apikey = {
          user_uuid,
          api_key: key,
          title,
          created_at: getIsoTimestr(),
          status: ApikeyStatus.Created,
        };

        try {
          await insertApikey(apikey);

          return {
            status: "success",
            message: "API key created successfully",
            redirect_url: `/admin/api-keys/${user_uuid}`,
          };
        } catch (e: any) {
          throw new Error(`Failed to create API key: ${e.message}`);
        }
      },
    },
  };

  return <FormSlot {...form} />;
} 
