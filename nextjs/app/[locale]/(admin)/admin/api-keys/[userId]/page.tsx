
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getUserApikeys, revokeApikey } from "@/models/apikey";
import { findUserByUuid } from "@/models/user";
import moment from "moment";
import Link from "next/link";
import { getUserInfo } from "@/services/user";
import { Apikey } from "@/types/apikey";


// Revoke API key server action
async function revokeApiKeyAction(apiKey: string) {
  "use server";
  await revokeApikey(apiKey);
}

export default async function UserApiKeysPage({ params }: { params: Promise<{ userId: string }> }) {
  const { userId } = await params;
  const apikeys = await getUserApikeys(userId) || [];
  const user = await findUserByUuid(userId);
  const currentUser = await getUserInfo();
  
  // Check if viewing own API keys
  const isOwnKeys = currentUser?.uuid === userId;
  
  // 预处理数据以避免直接传递回调函数给客户端组件
  const processedData = apikeys.map((apikey: Apikey) => {
    return {
      ...apikey,
      // 预处理 API Key 列
      api_key_display: `${apikey.api_key.slice(0, 8)}...${apikey.api_key.slice(-8)}`,
      
      // 预处理创建时间列
      created_at_display: moment(apikey.created_at).format("YYYY-MM-DD HH:mm:ss"),
      
      // 预处理操作列
      actions_display: (
        <form action={revokeApiKeyAction.bind(null, apikey.api_key)}>
          <button 
            type="submit"
            className="text-red-500 hover:text-red-700 font-medium"
          >
            Revoke
          </button>
        </form>
      ),
    };
  });
  
  const columns: TableColumn[] = [
    { name: "title", title: "Name" },
    { 
      name: "api_key_display", 
      title: "API Key",
      type: "copy",
    },
    {
      name: "created_at_display",
      title: "Created At",
    },
    {
      name: "actions_display",
      title: "Actions",
    },
  ];

  const table: TableSlotType = {
    title: isOwnKeys 
      ? "My API Keys" 
      : `API Keys for ${user?.nickname || user?.email || userId}`,
    crumb: {
      items: [
        {
          title: "API Keys",
          url: "/admin/api-keys",
        },
        {
          title: isOwnKeys 
            ? "My API Keys" 
            : user?.nickname || user?.email || userId,
          is_active: true,
        },
      ],
    },
    toolbar: {
      items: [
        {
          title: "Back to All API Keys",
          url: "/admin/api-keys",
          icon: "RiArrowLeftLine",
        },
        {
          title: isOwnKeys ? "Create New API Key" : `Create API Key for ${user?.nickname || user?.email || userId}`,
          url: isOwnKeys ? "/admin/api-keys/create" : `/admin/api-keys/${userId}/create`,
          icon: "RiAddLine",
        },
      ],
    },
    columns,
    data: processedData,
    empty_message: isOwnKeys 
      ? "You don't have any API keys yet" 
      : "No API keys found for this user",
  };

  return <TableSlot {...table} />;
} 
