
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getAllApikeys, getUserApikeys, revokeApikey } from "@/models/apikey";
import { Apikey } from "@/types/apikey";
import moment from "moment";
import { findUserByUuid } from "@/models/user";
import Link from "next/link";
import { getUserInfo } from "@/services/user";
import { ReactNode } from "react";


async function getEnhancedApikeys(): Promise<(Apikey & { user_email?: string; user_nickname?: string })[]> {
  const apikeys = await getAllApikeys();
  
  if (!apikeys) {
    return [];
  }

  // Enhance data with user information
  const enhancedData = await Promise.all(
    apikeys.map(async (apikey) => {
      const user = await findUserByUuid(apikey.user_uuid);
      return {
        ...apikey,
        user_email: user?.email,
        user_nickname: user?.nickname,
      };
    })
  );

  return enhancedData;
}

// Revoke API key server action
async function revokeApiKeyAction(apiKey: string) {
  "use server";
  await revokeApikey(apiKey);
}

export default async function AdminApiKeysPage() {
  const apikeys = await getEnhancedApikeys();
  const userInfo = await getUserInfo();
  
  // Mark admin's own API keys
  const enhancedApikeys = apikeys.map(key => {
    const isAdmin = userInfo?.uuid === key.user_uuid;
    
    // 预处理所有列的显示内容，避免使用回调函数
    return {
      ...key,
      is_admin: isAdmin,
      
      // 预处理 API Key 列
      api_key_display: `${key.api_key.slice(0, 8)}...${key.api_key.slice(-8)}`,
      
      // 预处理用户邮箱列
      user_email_display: (
        <Link 
          href={`/admin/api-keys/${key.user_uuid}`}
          className="text-blue-600 hover:underline"
        >
          {key.user_email || "Unknown"}
          {isAdmin && " (You)"}
        </Link>
      ),
      
      // 预处理用户名列
      user_nickname_display: (
        <>
          {key.user_nickname || "Unknown"}
          {isAdmin && " (You)"}
        </>
      ),
      
      // 预处理创建时间列
      created_at_display: moment(key.created_at).format("YYYY-MM-DD HH:mm:ss"),
      
      // 预处理操作列
      actions_display: (
        <div className="flex space-x-3">
          <Link 
            href={`/admin/api-keys/${key.user_uuid}`}
            className="text-blue-500 hover:text-blue-700 font-medium"
          >
            View All
          </Link>
          <form action={revokeApiKeyAction.bind(null, key.api_key)}>
            <button 
              type="submit"
              className="text-red-500 hover:text-red-700 font-medium"
            >
              Revoke
            </button>
          </form>
        </div>
      )
    };
  });

  // 使用预处理后的字段定义列
  const columns: TableColumn[] = [
    { name: "title", title: "Name" },
    { 
      name: "api_key_display", 
      title: "API Key",
      type: "copy",
    },
    { 
      name: "user_email_display", 
      title: "User Email",
    },
    { 
      name: "user_nickname_display", 
      title: "User Name",
    },
    {
      name: "created_at_display",
      title: "Created At",
    },
    {
      name: "actions_display",
      title: "Actions",
    },
  ];

  const table: TableSlotType = {
    title: "API Keys Management",
    tip: {
      title: "Manage all user API keys. Revoked keys cannot be recovered.",
    },
    toolbar: {
      items: [
        {
          title: "Create New API Key",
          url: "/admin/api-keys/create",
          icon: "RiAddLine",
        },
        {
          title: "My API Keys",
          url: userInfo?.uuid ? `/admin/api-keys/${userInfo.uuid}` : "/admin/api-keys",
          icon: "RiKey2Line",
        },
      ],
    },
    columns,
    data: enhancedApikeys,
    empty_message: "No API keys found",
  };

  return <TableSlot {...table} />;
} 
