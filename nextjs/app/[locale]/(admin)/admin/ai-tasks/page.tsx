
import { Table as TableSlotType } from "@/types/slots/table";
import TableSlot from "@/components/dashboard/slots/table";
import { getTranslations } from "next-intl/server";
import { getAllAiTasks } from "@/models/aiTask";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { formatDistance } from "date-fns";
import { Eye, FileText, Image, Search, Filter } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ReactNode } from "react";
import { formatDate } from "@/lib/utils";
// Define AiTask and AiTaskStatus types locally since the imported ones have issues
type AiTaskStatus = "PENDING" | "PROCESSING" | "SUCCEED" | "FAILED";

interface AiTask {
  id: string;
  order_no: string;
  user_uuid: string;
  product_name: string;
  credit_cost: number;
  orderstatus: AiTaskStatus;
  cost_time?: number;
  create_time: string | Date;
  update_time: string | Date;
  input_file_path?: string;
  output_image_path?: string;
  output_text?: string;
  fail_reason?: string;
  output_options?: Record<string, any>;
  callback_url?: string;
  users?: {
    email: string;
  };
}

export const metadata = {
  title: "AI Tasks Management",
};

// Search action to handle form submission
async function searchAction(formData: FormData) {
  "use server";
  const searchQuery = formData.get("search") as string;
  redirect(searchQuery ? `/admin/ai-tasks?search=${encodeURIComponent(searchQuery)}` : "/admin/ai-tasks");
}

// Filter actions
async function filterByStatus(formData: FormData) {
  "use server";
  const status = formData.get("status") as string;
  redirect(`/admin/ai-tasks?status=${encodeURIComponent(status)}`);
}

async function filterByUser(formData: FormData) {
  "use server";
  const user = formData.get("user") as string;
  redirect(`/admin/ai-tasks?user=${encodeURIComponent(user)}`);
}

async function clearFilters() {
  "use server";
  redirect("/admin/ai-tasks");
}

// Define a custom column type that allows ReactNode in title
interface CustomColumn {
  title: string | ReactNode;
  name: string;
  callback?: (item: any) => any;
  className?: string;
  type?: string;
}

interface PageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    status?: string;
    user?: string;
    search?: string;
  }>;
}

export default async function AdminAiTasksPage({ searchParams }: PageProps) {
  const t = await getTranslations();
  const params = await searchParams;
  
  // Parse pagination parameters
  const currentPage = parseInt(params.page || "1");
  const limit = parseInt(params.limit || "20");
  const offset = (currentPage - 1) * limit;
  const search = params.search || "";
  
  // Get tasks with filters and pagination
  const result = await getAllAiTasks({
    limit,
    offset,
    status: params.status,
    userId: params.user,
  });
  
  const tasks = result.data || [];
  const error = result.error;
  
  // Since we don't know if total is returned, estimate it based on tasks length
  // In a real implementation, getAllAiTasks should return a total count
  const total = tasks.length === limit ? (currentPage * limit) + 1 : currentPage * limit;
  
  // Calculate pagination info
  const totalPages = Math.ceil((total || 0) / limit);
  const hasNextPage = tasks.length === limit; // If we got a full page, assume there's more
  const hasPrevPage = currentPage > 1;
  
  // Build pagination URL with all current filters
  const buildPageUrl = (pageNum: number) => {
    const urlParams = new URLSearchParams();
    urlParams.set('page', pageNum.toString());
    urlParams.set('limit', limit.toString());
    if (params.status) urlParams.set('status', params.status);
    if (params.user) urlParams.set('user', params.user);
    if (search) urlParams.set('search', search);
    return `/admin/ai-tasks?${urlParams.toString()}`;
  };
  
  // Function to render status badge with appropriate color
  const renderStatusBadge = (status: AiTaskStatus) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline">Pending</Badge>;
      case "PROCESSING":
        return <Badge variant="secondary">Processing</Badge>;
      case "SUCCEED":
        return <Badge>Succeeded</Badge>;
      case "FAILED":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  // Filter options for status dropdown
  const statusOptions = [
    { label: "Pending", value: "PENDING" },
    { label: "Processing", value: "PROCESSING" },
    { label: "Succeeded", value: "SUCCEED" },
    { label: "Failed", value: "FAILED" },
  ];
  
  // Process data for the table
  const processedData = tasks.map((task: AiTask) => {
    return {
      ...task,
      orderNoDisplay: (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="font-mono text-xs">
                {task.order_no.substring(0, 8)}...
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{task.order_no}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
      userDisplay: (
        <span className="truncate max-w-[150px] inline-block">
          {task.users?.email || task.user_uuid.substring(0, 8)}
        </span>
      ),
      statusDisplay: renderStatusBadge(task.orderstatus),
      dateDisplay: (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div>{formatDate(task.create_time.toString())}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{new Date(task.create_time).toLocaleString()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
      resultTypeDisplay: task.output_text ? (
        <FileText className="h-4 w-4 text-muted-foreground" />
      ) : task.output_image_path ? (
        <Image className="h-4 w-4 text-muted-foreground" />
      ) : (
        "-"
      ),
      actionsDisplay: (
        <Button
          variant="ghost"
          size="sm"
          asChild
        >
          <Link href={`/admin/ai-tasks/${task.order_no}`}>
            <Eye className="h-4 w-4 mr-2" />
            View
          </Link>
        </Button>
      ),
    };
  });
  
  // Table configuration
  const table: TableSlotType = {
    title: "AI Tasks Management",
    description: "View and manage all AI tasks",
    toolbar: {
      items: [
        // Search form
        {
          type: "custom",
          element: (
            <form action={searchAction} className="flex items-center gap-2 mr-4">
              <input
                type="text"
                name="search"
                defaultValue={search}
                placeholder="Search tasks"
                className="input input-sm border rounded px-2 py-1 text-sm w-56"
                autoComplete="off"
              />
              <Button type="submit" size="sm" variant="outline" className="text-xs">
                <Search className="h-4 w-4 mr-1" />
                Search
              </Button>
            </form>
          ) as any,
        } as any,
        // Pagination controls
        ...(hasPrevPage ? [{
          title: "Previous",
          url: buildPageUrl(currentPage - 1),
          icon: "RiArrowLeftSLine",
        }] : []),
        {
          title: `${currentPage} / ${totalPages || 1}`,
          disabled: true,
        },
        ...(hasNextPage ? [{
          title: "Next",
          url: buildPageUrl(currentPage + 1),
          icon: "RiArrowRightSLine",
        }] : []),
      ],
    },
    // Add filters configuration
    filters: [
      {
        title: "Status Filter",
        items: [
          ...statusOptions.map(option => ({
            title: option.label,
            className: `px-3 py-1 text-xs rounded-md ${
              params.status === option.value 
                ? "bg-blue-600 text-white" 
                : "bg-gray-100 hover:bg-gray-200"
            }`,
            formAction: filterByStatus,
            formData: { status: option.value }
          })),
          ...(params.status || params.user || search ? [{
            title: "Clear Filters",
            className: "px-3 py-1 text-xs rounded-md bg-gray-100 hover:bg-gray-200",
            formAction: clearFilters,
            icon: "filter"
          }] : [])
        ]
      }
    ],
    columns: [
      {
        title: "Order No",
        name: "orderNoDisplay",
      },
      {
        title: "User",
        name: "userDisplay",
      },
      {
        title: "Product",
        name: "product_name",
      },
      {
        title: "Status",
        name: "statusDisplay",
      },
      {
        title: "Date",
        name: "dateDisplay",
      },
      {
        title: "Credits",
        name: "credit_cost",
      },
      {
        title: "Result Type",
        name: "resultTypeDisplay",
      },
      {
        title: "Actions",
        name: "actionsDisplay",
      },
    ] as CustomColumn[],
    data: processedData,
    empty_message: "No tasks found. Adjust your filters to see more results.",
    pagination: {
      currentPage,
      totalPages: totalPages || 1,
      onPageChange: async (page) => {
        // This won't actually execute since we're using URL-based navigation
        return;
      }
    }
  };

  return (
    <div className="container mx-auto">
      <TableSlot {...table} />
    </div>
  );
} 