
import { notFound } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import TaskDetail from "@/components/admin/TaskDetail";
import { getAiTaskByOrderNo } from "@/models/aiTask";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ order_no: string }>;
}) {
  const { order_no } = await params;
  return {
    title: `AI Task ${order_no}`,
  };
}

async function getTask(order_no: string) {
  const { data: task, error } = await getAiTaskByOrderNo(order_no);
  if (error || !task) {
    throw new Error(`Could not load AI task: ${error?.message}`);
  }
  return task;
}

export default async function AdminTaskDetailPage({
  params,
}: {
  params: Promise<{ order_no: string; locale: string }>;
}) {
  const { order_no } = await params;
  const task = await getTask(order_no);

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link
          href="/admin/ai-tasks"
          className={cn(buttonVariants({ variant: "outline" }))}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Task List
        </Link>
      </div>

      <TaskDetail task={task} />
    </div>
  );
} 