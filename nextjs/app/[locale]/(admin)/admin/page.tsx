"use client";


import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Shield, Package2, Wrench, Mail, Users, Settings, FileText, Database, BarChart3, Activity, TrendingUp } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface AdminStats {
  totalUsers: number;
  totalLinks: number;
  totalProjects: number;
  totalItems: number;
  pendingSubmissions: number;
  totalTraffic: number;
}

export default function AdminPage() {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalLinks: 0,
    totalProjects: 0,
    totalItems: 0,
    pendingSubmissions: 0,
    totalTraffic: 0
  });
  const router = useRouter();

  useEffect(() => {
    checkAdminStatus();
  }, []);

  const checkAdminStatus = async () => {
    try {
      const response = await fetch("/api/admin/auth");
      if (response.ok) {
        const data = await response.json();
        setIsAdmin(data.isAdmin);
        if (data.isAdmin) {
          fetchStats();
        }
      } else {
        setIsAdmin(false);
      }
    } catch (error) {
      console.error("Error checking admin status:", error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // 这里可以并行获取各种统计数据
      const [usersRes, linksRes, projectsRes] = await Promise.all([
        fetch("/api/admin/users").catch(() => null),
        fetch("/api/admin/links?limit=1").catch(() => null),
        fetch("/api/admin/projects?limit=1").catch(() => null)
      ]);

      const usersData = usersRes?.ok ? await usersRes.json() : { users: [] };
      const linksData = linksRes?.ok ? await linksRes.json() : { pagination: { total: 0 } };
      const projectsData = projectsRes?.ok ? await projectsRes.json() : { pagination: { total: 0 } };

      setStats({
        totalUsers: usersData.users?.length || 0,
        totalLinks: linksData.pagination?.total || 0,
        totalProjects: projectsData.pagination?.total || 0,
        totalItems: 0, // 可以从items API获取
        pendingSubmissions: 0, // 可以从submissions API获取
        totalTraffic: 0 // 可以从analytics API获取
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground text-sm">检查管理员权限...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader className="text-center">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <CardTitle className="text-lg sm:text-xl">访问受限</CardTitle>
              <CardDescription className="text-sm">
                您需要管理员权限才能访问此页面
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button onClick={() => router.back()} variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <div className="flex items-center gap-2 sm:gap-4 mb-2">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-0">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold truncate">管理员控制台</h1>
            <p className="text-muted-foreground text-sm sm:text-base hidden sm:block">系统管理和数据管理工具</p>
          </div>
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">总用户数</CardTitle>
            <Users className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 sm:pb-6">
            <div className="text-lg sm:text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">注册用户总数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">外链总数</CardTitle>
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 sm:pb-6">
            <div className="text-lg sm:text-2xl font-bold">{stats.totalLinks}</div>
            <p className="text-xs text-muted-foreground">管理的外链数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">项目总数</CardTitle>
            <Package2 className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 sm:pb-6">
            <div className="text-lg sm:text-2xl font-bold">{stats.totalProjects}</div>
            <p className="text-xs text-muted-foreground">用户创建的项目</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">系统活跃度</CardTitle>
            <Activity className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 sm:pb-6">
            <div className="text-lg sm:text-2xl font-bold">98%</div>
            <p className="text-xs text-muted-foreground">系统运行状态</p>
          </CardContent>
        </Card>
      </div>

      {/* Management Modules Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
        <Link href="/admin/items">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">Item 管理</h3>
              <Package2 className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理平台上的所有 Item，包括提交、审核和编辑
            </p>
          </div>
        </Link>
        
        <Link href="/admin/items/tools">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">Item 工具</h3>
              <Wrench className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理 Item 的 MCP 工具配置和文档
            </p>
          </div>
        </Link>

        <Link href="/admin/users">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">用户管理</h3>
              <Users className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理系统用户和权限设置
            </p>
          </div>
        </Link>

        <Link href="/admin/emails">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">邮件管理</h3>
              <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理邮件模板、日志和队列处理
            </p>
          </div>
        </Link>

        <Link href="/admin/links">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">外链管理</h3>
              <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理用户的外链资源和项目数据
            </p>
          </div>
        </Link>

        <Link href="/admin/api-keys">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">API 密钥</h3>
              <Database className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理用户 API 密钥和访问权限
            </p>
          </div>
        </Link>

        <Link href="/admin/posts">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">文章管理</h3>
              <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理博客文章和内容发布
            </p>
          </div>
        </Link>

        <Link href="/admin/settings">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">系统设置</h3>
              <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              配置系统范围的设置和参数
            </p>
          </div>
        </Link>

        <Link href="/admin/image-hosting">
          <div className="group rounded-lg border p-4 sm:p-6 hover:border-foreground transition-all">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h3 className="text-base sm:text-lg font-medium">图片托管</h3>
              <Database className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0" />
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">
              管理上传的图片和文件资源
            </p>
          </div>
        </Link>
      </div>
    </div>
  );
}
