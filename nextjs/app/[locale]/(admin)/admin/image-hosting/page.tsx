
import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { newStorage } from "@/lib/storage";
import moment from "moment";
import { ReactNode } from "react";
import Image from "next/image";
import { revalidatePath } from "next/cache";
import { toast } from "sonner";
import Empty from "@/components/blocks/empty";
// Server action to delete an image
async function deleteImageAction(key: string) {
  "use server";
  
  try {
    const storage = newStorage();
    await storage.deleteFile({ key });
    
    // Revalidate the page to refresh the image list
    revalidatePath('/admin/image-hosting');
  } catch (error) {
    console.error("Error deleting image:", error);
  }
}

// Get all images from the storage
async function getAllImages() {
  const storage = newStorage();
  try {
    const images = await storage.listFiles({});
    return images;
  } catch (error) {
    console.error("Error fetching images:", error);
    return [];
  }
}

export default async function AdminImageHostingPage() {

    if (process.env.STORAGE_BUCKET == "") {
        return <Empty message="No storage bucket configured" />;
    }

  const images = await getAllImages();
  
  // Enhance data for display
  const enhancedImages = images.map(image => {
    // Get file extension to determine if it's an image
    const key = image.key || '';
    const url = image.url || '';
    const fileExtension = key.split('.').pop()?.toLowerCase() || '';
    const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension);
    
    return {
      ...image,
      
      // Display image name
      name: key.split('/').pop() || key,
      
      // File size formatting
      size_display: formatFileSize(image.size || 0),
      
      // Last modified date formatting
      last_modified_display: moment(image.lastModified).format("YYYY-MM-DD HH:mm:ss"),
      
      // Image preview
      preview_display: isImage ? (
        <div className="w-16 h-16 relative">
          <Image 
            src={url}
            alt={key}
            fill
            className="object-cover rounded"
          />
        </div>
      ) : (
        <div className="w-16 h-16 flex items-center justify-center bg-gray-100 rounded">
          <span className="text-xs text-gray-500">{fileExtension}</span>
        </div>
      ),
      
      // Actions column
      actions_display: (
        <div className="flex space-x-3">
          <a 
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:text-blue-700 font-medium"
          >
            View
          </a>
          <button
            onClick={() => navigator.clipboard.writeText(url)}
            className="text-green-500 hover:text-green-700 font-medium"
          >
            Copy URL
          </button>
          <form action={deleteImageAction.bind(null, key)}>
            <button 
              type="submit"
              className="text-red-500 hover:text-red-700 font-medium"
            >
              Delete
            </button>
          </form>
        </div>
      )
    };
  });

  // Column definitions
  const columns: TableColumn[] = [
    { 
      name: "preview_display", 
      title: "Preview",
    },
    { 
      name: "name", 
      title: "File Name",
    },
    { 
      name: "url", 
      title: "URL",
      type: "copy",
    },
    { 
      name: "size_display", 
      title: "Size",
    },
    {
      name: "last_modified_display",
      title: "Last Modified",
    },
    {
      name: "actions_display",
      title: "Actions",
    },
  ];

  // Table configuration
  const table: TableSlotType = {
    title: "Image Hosting Management",
    tip: {
      title: "Manage uploaded images. Deleted images cannot be recovered.",
    },
    toolbar: {
      items: [
        {
          title: "Upload New Image",
          url: "/admin/image-hosting/upload",
          icon: "RiUploadLine",
        },
      ],
    },
    columns,
    data: enhancedImages,
    empty_message: "No images found",
  };

  return <TableSlot {...table} />;
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 