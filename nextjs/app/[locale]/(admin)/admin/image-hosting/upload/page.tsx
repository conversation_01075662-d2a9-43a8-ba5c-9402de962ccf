
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { newStorage } from "@/lib/storage";
import { redirect } from "next/navigation";

// Server action to handle file upload
async function uploadImage(formData: FormData) {
  "use server";

  const file = formData.get("file") as File;
  if (!file) {
    throw new Error("No file provided");
  }

  try {
    const buffer = Buffer.from(await file.arrayBuffer());
    const originalFilename = file.name;
    const contentType = file.type;
    
    // Generate a unique key for the file
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 10);
    const key = `uploads/${timestamp}-${randomString}-${originalFilename}`;
    
    // Upload the file
    const storage = newStorage();
    await storage.uploadFile({
      body: buffer,
      key,
      contentType,
    });
    
    // Redirect back to the image hosting page
    redirect("/admin/image-hosting");
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
}

export default function UploadImagePage() {
  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Upload New Image</CardTitle>
          <CardDescription>
            Upload images to your image hosting service. Supported formats: JPG, PNG, GIF, SVG, WEBP.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={uploadImage} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="file">Select Image</Label>
              <Input 
                id="file" 
                name="file" 
                type="file" 
                accept="image/jpeg, image/png, image/gif, image/svg+xml, image/webp" 
                required 
              />
              <p className="text-sm text-gray-500">
                Files up to 20MB are supported
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <Button type="submit">Upload Image</Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => window.location.href = "/admin/image-hosting"}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 