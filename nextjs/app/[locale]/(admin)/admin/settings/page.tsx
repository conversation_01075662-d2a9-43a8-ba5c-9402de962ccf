import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import SettingsForm from "@/components/admin/settings-form";
import { getSetting } from "@/models/settings";

export default async function SettingsPage() {
  const t = await getTranslations("admin");
  
  // Fetch the current invitation credits setting
  const invitationCredits = await getSetting("invitation_credits") || "5";
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">{t("settings.title")}</h1>
      
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("settings.invitation.title")}</CardTitle>
            <CardDescription>{t("settings.invitation.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <SettingsForm 
              settingKey="invitation_credits" 
              label={t("settings.invitation.credits_label")}
              description={t("settings.invitation.credits_description")}
              initialValue={invitationCredits}
              type="number"
              min={0}
            />
          </CardContent>
        </Card>
        
        {/* Add more setting sections here in the future */}
      </div>
    </div>
  );
} 