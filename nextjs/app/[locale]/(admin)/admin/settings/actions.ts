"use server";

import { upsertSetting } from "@/models/settings";
import { revalidatePath } from "next/cache";

export async function updateSetting(settingKey: string, value: string) {
  try {
    const result = await upsertSetting(settingKey, value);
    revalidatePath("/admin/settings");
    return { success: result };
  } catch (error) {
    console.error("Error updating setting:", error);
    return { success: false, error: "Failed to update setting" };
  }
} 