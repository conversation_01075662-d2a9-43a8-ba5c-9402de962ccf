"use client";


import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  RefreshCw, 
  Search,
  Trash2,
  Filter,
  Info,
  ArrowLeft,
  FolderOpen,
  Settings
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface AdminData {
  projects: any[];
  userConfigs: any[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export default function ProjectsOverviewPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [adminData, setAdminData] = useState<AdminData>({
    projects: [],
    userConfigs: [],
    pagination: { total: 0, limit: 50, offset: 0, hasMore: false }
  });
  const [activeTab, setActiveTab] = useState("projects");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    link_type: "all",
    user_id: ""
  });
  const [currentPage, setCurrentPage] = useState(0);

  // Fetch data based on active tab
  const fetchData = async (page: number = currentPage, tab: string = activeTab) => {
    setLoading(true);
    try {
      const limit = 50;
      const offset = page * limit;
      
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        table: tab,
        ...(filters.search && { search: filters.search }),
        status: filters.status,
        link_type: filters.link_type,
        ...(filters.user_id && filters.user_id !== "all" && { user_id: filters.user_id })
      });

      const response = await fetch(`/api/admin/link-management?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setAdminData(prev => ({
          ...prev,
          [tab]: data.data,
          pagination: data.pagination
        }));
      } else {
        toast.error(data.error || "Failed to fetch data");
      }
    } catch (error) {
      console.error("Error fetching admin data:", error);
      toast.error("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select items to delete");
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} items?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/link-management", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          itemIds: selectedItems,
          table: activeTab 
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setSelectedItems([]);
        fetchData(0, activeTab);
      } else {
        toast.error(result.error || "Batch delete failed");
      }
    } catch (error) {
      console.error("Error in batch delete:", error);
      toast.error("Batch delete failed");
    } finally {
      setLoading(false);
    }
  };

  // Handle selection changes
  const handleSelectAll = (checked: boolean) => {
    const currentData = adminData[activeTab as keyof AdminData] as any[];
    if (checked) {
      setSelectedItems(currentData.map(item => item.id || item.uuid || Math.random().toString()));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleSearch = () => {
    setCurrentPage(0);
    fetchData(0, activeTab);
  };

  const handleResetFilters = () => {
    setFilters({ search: "", status: "all", link_type: "all", user_id: "" });
    setCurrentPage(0);
    fetchData(0, activeTab);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchData(newPage, activeTab);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setCurrentPage(0);
    setSelectedItems([]);
    fetchData(0, tab);
  };

  useEffect(() => {
    fetchData(0, activeTab);
  }, []);

  const renderTableContent = () => {
    const currentData = adminData[activeTab as keyof AdminData] as any[];
    
    if (!currentData || currentData.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          暂无数据
        </div>
      );
    }

    const renderTableHeaders = () => {
      switch (activeTab) {
        case 'projects':
          return (
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === currentData.length && currentData.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>项目名称</TableHead>
              <TableHead>域名</TableHead>
              <TableHead>描述</TableHead>
              <TableHead>总链接数</TableHead>
              <TableHead>已索引链接</TableHead>
              <TableHead>用户ID</TableHead>
              <TableHead>创建时间</TableHead>
            </TableRow>
          );
        case 'userConfigs':
          return (
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === currentData.length && currentData.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>用户ID</TableHead>
              <TableHead>项目ID</TableHead>
              <TableHead>配置类型</TableHead>
              <TableHead>配置名称</TableHead>
              <TableHead>是否活跃</TableHead>
              <TableHead>创建时间</TableHead>
            </TableRow>
          );
        default:
          return null;
      }
    };

    const renderTableRows = () => {
      return currentData.map((item: any, index: number) => {
        const itemId = item.id || item.uuid || `${activeTab}-${index}`;
        
        switch (activeTab) {
          case 'projects':
            return (
              <TableRow key={itemId}>
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(itemId)}
                    onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                  />
                </TableCell>
                <TableCell className="font-medium">{item.name}</TableCell>
                <TableCell>{item.domain}</TableCell>
                <TableCell>{item.description || '-'}</TableCell>
                <TableCell>{item.total_links || 0}</TableCell>
                <TableCell>{item.indexed_links || 0}</TableCell>
                <TableCell className="font-mono text-sm">{item.user_id}</TableCell>
                <TableCell>{new Date(item.created_at).toLocaleDateString('zh-CN')}</TableCell>
              </TableRow>
            );
          case 'userConfigs':
            return (
              <TableRow key={itemId}>
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(itemId)}
                    onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                  />
                </TableCell>
                <TableCell className="font-mono text-sm">{item.user_id}</TableCell>
                <TableCell className="font-mono text-sm">{item.project_id}</TableCell>
                <TableCell>{item.config_type}</TableCell>
                <TableCell>{item.config_name}</TableCell>
                <TableCell>
                  {item.is_active ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">活跃</Badge>
                  ) : (
                    <Badge variant="secondary">非活跃</Badge>
                  )}
                </TableCell>
                <TableCell>{new Date(item.created_at).toLocaleDateString('zh-CN')}</TableCell>
              </TableRow>
            );
          default:
            return null;
        }
      });
    };

    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {renderTableHeaders()}
          </TableHeader>
          <TableBody>
            {renderTableRows()}
          </TableBody>
        </Table>
      </div>
    );
  };

  const projectPages = [
    {
      title: "项目列表",
      description: "管理用户创建的所有项目",
      icon: FolderOpen,
      href: "/admin/links/projects/list",
      count: adminData.projects.length
    },
    {
      title: "用户配置",
      description: "管理用户项目的配置信息",
      icon: Settings,
      href: "/admin/links/projects/configs",
      count: adminData.userConfigs.length
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-2">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">项目管理</h1>
            <p className="text-muted-foreground">管理用户项目和相关配置</p>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {projectPages.map((page, index) => {
          const IconComponent = page.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{page.title}</CardTitle>
                      <CardDescription className="mt-1">
                        {page.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{page.count}</div>
                    <div className="text-sm text-muted-foreground">条记录</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Link href={page.href}>
                  <Button className="w-full" variant="outline">
                    查看详情
                    <FolderOpen className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            项目数据概览
          </CardTitle>
          <CardDescription>
            项目管理系统的各个数据表统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{adminData.projects.length}</div>
              <div className="text-sm text-muted-foreground">项目总数</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{adminData.userConfigs.length}</div>
              <div className="text-sm text-muted-foreground">用户配置</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}