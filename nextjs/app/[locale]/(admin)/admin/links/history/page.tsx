"use client";


import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  RefreshCw, 
  History,
  Search,
  Trash2,
  Filter,
  Info,
  ArrowLeft,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface AdminData {
  allLinksHistory: any[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export default function LinksHistoryPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [adminData, setAdminData] = useState<AdminData>({
    allLinksHistory: [],
    pagination: { total: 0, limit: 50, offset: 0, hasMore: false }
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    link_type: "all",
    user_id: "",
    sortBy: "checked_at",
    sortOrder: "desc"
  });
  const [currentPage, setCurrentPage] = useState(0);

  // Fetch data
  const fetchData = async (page: number = currentPage) => {
    setLoading(true);
    try {
      const limit = 50;
      const offset = page * limit;
      
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        table: "allLinksHistory",
        ...(filters.search && { search: filters.search }),
        status: filters.status,
        link_type: filters.link_type,
        ...(filters.user_id && filters.user_id !== "all" && { user_id: filters.user_id }),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      });

      const response = await fetch(`/api/admin/link-management?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setAdminData(prev => ({
          ...prev,
          allLinksHistory: data.data,
          pagination: data.pagination
        }));
      } else {
        toast.error(data.error || "Failed to fetch data");
      }
    } catch (error) {
      console.error("Error fetching admin data:", error);
      toast.error("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select items to delete");
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} items?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/link-management", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          itemIds: selectedItems,
          table: "allLinksHistory"
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setSelectedItems([]);
        fetchData(0);
      } else {
        toast.error(result.error || "Batch delete failed");
      }
    } catch (error) {
      console.error("Error in batch delete:", error);
      toast.error("Batch delete failed");
    } finally {
      setLoading(false);
    }
  };

  // Handle selection changes
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(adminData.allLinksHistory.map(item => item.id || item.uuid || Math.random().toString()));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleSearch = () => {
    setCurrentPage(0);
    fetchData(0);
  };

  const handleResetFilters = () => {
    setFilters({ search: "", status: "all", link_type: "all", user_id: "", sortBy: "checked_at", sortOrder: "desc" });
    setCurrentPage(0);
    fetchData(0);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchData(newPage);
  };

  const handleSort = (column: string) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === "asc" ? "desc" : "asc";
    setFilters(prev => ({ ...prev, sortBy: column, sortOrder: newSortOrder }));
    setCurrentPage(0);
    fetchData(0);
  };

  const getSortIcon = (column: string) => {
    if (filters.sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return filters.sortOrder === "asc" ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  useEffect(() => {
    fetchData(0);
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-2">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">链接历史记录</h1>
            <p className="text-muted-foreground">查看链接统计数据的历史变化</p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                链接历史记录
              </CardTitle>
              <CardDescription>
                追踪域名DR分数、流量和索引状态的历史变化
              </CardDescription>
            </div>
            <Button onClick={() => fetchData(0)} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索域名..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>

            <Button onClick={handleResetFilters} variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              重置
            </Button>
          </div>

          {/* Batch Actions */}
          {selectedItems.length > 0 && (
            <Alert className="mb-4">
              <Info className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>已选择 {selectedItems.length} 个项目</span>
                <Button onClick={handleBatchDelete} variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedItems.length === adminData.allLinksHistory.length && adminData.allLinksHistory.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      onClick={() => handleSort("domain")}
                      className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                      域名 {getSortIcon("domain")}
                    </Button>
                  </TableHead>
                  <TableHead>DR分数</TableHead>
                  <TableHead>流量</TableHead>
                  <TableHead>外链数量</TableHead>
                  <TableHead>是否已索引</TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      onClick={() => handleSort("checked_at")}
                      className="h-auto p-0 font-semibold hover:bg-transparent"
                    >
                      检查时间 {getSortIcon("checked_at")}
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {adminData.allLinksHistory.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  adminData.allLinksHistory.map((item: any, index: number) => {
                    const itemId = item.id || item.uuid || `history-${index}`;
                    return (
                      <TableRow key={itemId}>
                        <TableCell>
                          <Checkbox
                            checked={selectedItems.includes(itemId)}
                            onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{item.domain}</TableCell>
                        <TableCell>
                          <span className="font-mono">{item.dr_score || '-'}</span>
                        </TableCell>
                        <TableCell>{item.traffic || 0}</TableCell>
                        <TableCell>{item.backlink_count || 0}</TableCell>
                        <TableCell>
                          {item.is_indexed ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">已索引</Badge>
                          ) : (
                            <Badge variant="secondary">未索引</Badge>
                          )}
                        </TableCell>
                        <TableCell>{new Date(item.checked_at).toLocaleDateString('zh-CN')}</TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              显示 {adminData.pagination.offset + 1} - {Math.min(adminData.pagination.offset + adminData.pagination.limit, adminData.pagination.total)} 
              ，共 {adminData.pagination.total} 条
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
                variant="outline"
                size="sm"
              >
                上一页
              </Button>
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!adminData.pagination.hasMore}
                variant="outline"
                size="sm"
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}