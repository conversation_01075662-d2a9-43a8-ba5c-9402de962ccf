"use client";


import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  RefreshCw, 
  ExternalLink,
  Search,
  Trash2,
  Filter,
  Info,
  ArrowLeft,
  Globe,
  History,
  Eye,
  Settings
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface AdminData {
  linkResources: any[];
  allLinks: any[];
  allLinksHistory: any[];
  discoveredLinks: any[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export default function ExternalLinksOverviewPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [adminData, setAdminData] = useState<AdminData>({
    linkResources: [],
    allLinks: [],
    allLinksHistory: [],
    discoveredLinks: [],
    pagination: { total: 0, limit: 50, offset: 0, hasMore: false }
  });
  const [activeTab, setActiveTab] = useState("linkResources");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    link_type: "all",
    user_id: ""
  });
  const [currentPage, setCurrentPage] = useState(0);

  // Fetch data based on active tab
  const fetchData = async (page: number = currentPage, tab: string = activeTab) => {
    setLoading(true);
    try {
      const limit = 50;
      const offset = page * limit;
      
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        table: tab,
        ...(filters.search && { search: filters.search }),
        status: filters.status,
        link_type: filters.link_type,
        ...(filters.user_id && filters.user_id !== "all" && { user_id: filters.user_id })
      });

      const response = await fetch(`/api/admin/link-management?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setAdminData(prev => ({
          ...prev,
          [tab]: data.data,
          pagination: data.pagination
        }));
      } else {
        toast.error(data.error || "Failed to fetch data");
      }
    } catch (error) {
      console.error("Error fetching admin data:", error);
      toast.error("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select items to delete");
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} items?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/link-management", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          itemIds: selectedItems,
          table: activeTab 
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setSelectedItems([]);
        fetchData(0, activeTab);
      } else {
        toast.error(result.error || "Batch delete failed");
      }
    } catch (error) {
      console.error("Error in batch delete:", error);
      toast.error("Batch delete failed");
    } finally {
      setLoading(false);
    }
  };

  // Handle selection changes
  const handleSelectAll = (checked: boolean) => {
    const currentData = adminData[activeTab as keyof AdminData] as any[];
    if (checked) {
      setSelectedItems(currentData.map(item => item.id || item.uuid || Math.random().toString()));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleSearch = () => {
    setCurrentPage(0);
    fetchData(0, activeTab);
  };

  const handleResetFilters = () => {
    setFilters({ search: "", status: "all", link_type: "all", user_id: "" });
    setCurrentPage(0);
    fetchData(0, activeTab);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchData(newPage, activeTab);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setCurrentPage(0);
    setSelectedItems([]);
    fetchData(0, tab);
  };

  useEffect(() => {
    fetchData(0, activeTab);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "blocked":
        return <Badge variant="destructive">Blocked</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "免费":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">免费</Badge>;
      case "付费":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700">付费</Badge>;
      case "free":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">Free</Badge>;
      case "paid":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700">Paid</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const renderTableContent = () => {
    const currentData = adminData[activeTab as keyof AdminData] as any[];
    
    if (!currentData || currentData.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          暂无数据
        </div>
      );
    }

    const renderTableHeaders = () => {
      switch (activeTab) {
        case 'linkResources':
          return (
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === currentData.length && currentData.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>URL</TableHead>
              <TableHead>标题</TableHead>
              <TableHead>链接类型</TableHead>
              <TableHead>价格</TableHead>
              <TableHead>来源</TableHead>
              <TableHead>获取方式</TableHead>
              <TableHead>用户ID</TableHead>
              <TableHead>创建时间</TableHead>
            </TableRow>
          );
        case 'allLinks':
          return (
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === currentData.length && currentData.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>域名</TableHead>
              <TableHead>DR分数</TableHead>
              <TableHead>流量</TableHead>
              <TableHead>外链数量</TableHead>
              <TableHead>是否已索引</TableHead>
              <TableHead>最后更新</TableHead>
            </TableRow>
          );
        case 'allLinksHistory':
          return (
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === currentData.length && currentData.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>域名</TableHead>
              <TableHead>DR分数</TableHead>
              <TableHead>流量</TableHead>
              <TableHead>外链数量</TableHead>
              <TableHead>是否已索引</TableHead>
              <TableHead>检查时间</TableHead>
            </TableRow>
          );
        case 'discoveredLinks':
          return (
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedItems.length === currentData.length && currentData.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>URL</TableHead>
              <TableHead>标题</TableHead>
              <TableHead>锚文本</TableHead>
              <TableHead>链接类型</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>项目ID</TableHead>
              <TableHead>发现时间</TableHead>
            </TableRow>
          );
        default:
          return null;
      }
    };

    const renderTableRows = () => {
      return currentData.map((item: any, index: number) => {
        const itemId = item.id || item.uuid || `${activeTab}-${index}`;
        
        switch (activeTab) {
          case 'linkResources':
            return (
              <TableRow key={itemId}>
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(itemId)}
                    onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                  />
                </TableCell>
                <TableCell>
                  <a href={item.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {item.url.length > 50 ? `${item.url.substring(0, 50)}...` : item.url}
                  </a>
                </TableCell>
                <TableCell className="font-medium">{item.title}</TableCell>
                <TableCell>{getTypeBadge(item.link_type)}</TableCell>
                <TableCell>{item.price ? `${item.currency} ${item.price}` : '-'}</TableCell>
                <TableCell>{item.source || '-'}</TableCell>
                <TableCell>{item.acquisition_method || '-'}</TableCell>
                <TableCell className="font-mono text-sm">{item.user_id}</TableCell>
                <TableCell>{new Date(item.created_at).toLocaleDateString('zh-CN')}</TableCell>
              </TableRow>
            );
          case 'allLinks':
            return (
              <TableRow key={itemId}>
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(itemId)}
                    onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                  />
                </TableCell>
                <TableCell className="font-medium">{item.domain}</TableCell>
                <TableCell>
                  <span className="font-mono">{item.dr_score || '-'}</span>
                </TableCell>
                <TableCell>{item.traffic || 0}</TableCell>
                <TableCell>{item.backlink_count || 0}</TableCell>
                <TableCell>
                  {item.is_indexed ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">已索引</Badge>
                  ) : (
                    <Badge variant="secondary">未索引</Badge>
                  )}
                </TableCell>
                <TableCell>{new Date(item.last_updated).toLocaleDateString('zh-CN')}</TableCell>
              </TableRow>
            );
          case 'allLinksHistory':
            return (
              <TableRow key={itemId}>
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(itemId)}
                    onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                  />
                </TableCell>
                <TableCell className="font-medium">{item.domain}</TableCell>
                <TableCell>
                  <span className="font-mono">{item.dr_score || '-'}</span>
                </TableCell>
                <TableCell>{item.traffic || 0}</TableCell>
                <TableCell>{item.backlink_count || 0}</TableCell>
                <TableCell>
                  {item.is_indexed ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">已索引</Badge>
                  ) : (
                    <Badge variant="secondary">未索引</Badge>
                  )}
                </TableCell>
                <TableCell>{new Date(item.checked_at).toLocaleDateString('zh-CN')}</TableCell>
              </TableRow>
            );
          case 'discoveredLinks':
            return (
              <TableRow key={itemId}>
                <TableCell>
                  <Checkbox
                    checked={selectedItems.includes(itemId)}
                    onCheckedChange={(checked) => handleSelectItem(itemId, checked as boolean)}
                  />
                </TableCell>
                <TableCell>
                  <a href={item.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {item.url.length > 50 ? `${item.url.substring(0, 50)}...` : item.url}
                  </a>
                </TableCell>
                <TableCell className="font-medium">{item.title}</TableCell>
                <TableCell>{item.anchor_text}</TableCell>
                <TableCell>
                  {item.link_type === 'dofollow' ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">DoFollow</Badge>
                  ) : (
                    <Badge variant="secondary">NoFollow</Badge>
                  )}
                </TableCell>
                <TableCell>{getStatusBadge(item.status)}</TableCell>
                <TableCell className="font-mono text-sm">{item.project_id}</TableCell>
                <TableCell>{new Date(item.discovered_at).toLocaleDateString('zh-CN')}</TableCell>
              </TableRow>
            );
          default:
            return null;
        }
      });
    };

    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {renderTableHeaders()}
          </TableHeader>
          <TableBody>
            {renderTableRows()}
          </TableBody>
        </Table>
      </div>
    );
  };

  const externalLinksPages = [
    {
      title: "链接资源",
      description: "管理用户的链接资源数据",
      icon: ExternalLink,
      href: "/admin/links/external-links/link-resources",
      count: adminData.linkResources.length
    },
    {
      title: "所有链接",
      description: "查看域名的DR分数、流量和索引状态",
      icon: Globe,
      href: "/admin/links/external-links/all-links",
      count: adminData.allLinks.length
    },
    {
      title: "历史记录",
      description: "追踪链接统计数据的历史变化",
      icon: History,
      href: "/admin/links/external-links/history",
      count: adminData.allLinksHistory.length
    },
    {
      title: "发现的链接",
      description: "管理项目中发现的外链数据",
      icon: Eye,
      href: "/admin/links/external-links/discovered",
      count: adminData.discoveredLinks.length
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-2">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">外链管理</h1>
            <p className="text-muted-foreground">管理外链资源、域名统计和发现的链接</p>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {externalLinksPages.map((page, index) => {
          const IconComponent = page.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{page.title}</CardTitle>
                      <CardDescription className="mt-1">
                        {page.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{page.count}</div>
                    <div className="text-sm text-muted-foreground">条记录</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Link href={page.href}>
                  <Button className="w-full" variant="outline">
                    查看详情
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="h-5 w-5" />
            外链数据概览
          </CardTitle>
          <CardDescription>
            外链管理系统的各个数据表统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{adminData.linkResources.length}</div>
              <div className="text-sm text-muted-foreground">链接资源</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{adminData.allLinks.length}</div>
              <div className="text-sm text-muted-foreground">所有链接</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{adminData.allLinksHistory.length}</div>
              <div className="text-sm text-muted-foreground">历史记录</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{adminData.discoveredLinks.length}</div>
              <div className="text-sm text-muted-foreground">发现链接</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}