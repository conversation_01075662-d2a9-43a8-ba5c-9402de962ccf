"use client";


import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  ArrowLeft,
  FolderOpen,
  ExternalLink,
  ChevronRight,
  Database,
  BarChart3
} from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function LinkManagementOverviewPage() {
  const router = useRouter();

  const linkSections = [
    {
      title: "外链管理",
      description: "管理外链资源、域名统计和发现的链接",
      icon: ExternalLink,
      href: "/admin/links/external-links",
      items: [
        "链接资源管理",
        "所有链接统计", 
        "历史记录跟踪",
        "发现链接数据"
      ]
    },
    {
      title: "项目管理",
      description: "管理用户项目和相关配置",
      icon: FolderOpen,
      href: "/admin/links/projects",
      items: [
        "项目信息管理",
        "用户配置数据",
        "项目统计信息",
        "配置状态监控"
      ]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-2">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">链接管理</h1>
            <p className="text-muted-foreground">管理系统中的所有链接相关数据</p>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {linkSections.map((section, index) => {
          const IconComponent = section.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{section.title}</CardTitle>
                      <CardDescription className="mt-1">
                        {section.description}
                      </CardDescription>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 mb-4">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                      {item}
                    </li>
                  ))}
                </ul>
                <Link href={section.href}>
                  <Button className="w-full" variant="outline">
                    进入管理
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            系统概览
          </CardTitle>
          <CardDescription>
            链接管理系统的核心数据表统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-primary">6</div>
              <div className="text-sm text-muted-foreground">数据表</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">Projects</div>
              <div className="text-sm text-muted-foreground">项目管理</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">Resources</div>
              <div className="text-sm text-muted-foreground">链接资源</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">All Links</div>
              <div className="text-sm text-muted-foreground">链接统计</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">History</div>
              <div className="text-sm text-muted-foreground">历史记录</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-teal-600">Configs</div>
              <div className="text-sm text-muted-foreground">用户配置</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}