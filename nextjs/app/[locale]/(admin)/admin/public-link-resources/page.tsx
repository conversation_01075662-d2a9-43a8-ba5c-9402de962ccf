
import { Suspense } from 'react';
import { PublicLinkResourcesManagement } from '@/components/admin/public-link-resources/public-link-resources-management';

export default function PublicLinkResourcesPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Public Link Resources Management</h1>
        <p className="text-muted-foreground">
          Manage public platforms where users can submit backlinks
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <PublicLinkResourcesManagement />
      </Suspense>
    </div>
  );
}