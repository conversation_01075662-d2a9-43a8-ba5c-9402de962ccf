"use client";


import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  RefreshCw, 
  Package2,
  Search,
  Trash2,
  Filter,
  Info,
  ArrowLeft,
  ExternalLink
} from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Project } from "@/types/links";

interface AdminData {
  projects: (Project & { 
    user_email?: string; 
    links_count?: number;
    total_traffic?: number;
  })[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export default function ProjectsManagementPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [adminData, setAdminData] = useState<AdminData>({
    projects: [],
    pagination: { total: 0, limit: 50, offset: 0, hasMore: false }
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    search: "",
    user_id: ""
  });
  const [currentPage, setCurrentPage] = useState(0);

  // Fetch projects data
  const fetchData = async (page: number = currentPage) => {
    setLoading(true);
    try {
      const limit = 50;
      const offset = page * limit;
      
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.user_id && filters.user_id !== "all" && { user_id: filters.user_id })
      });

      const response = await fetch(`/api/admin/projects?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setAdminData(prev => ({
          ...prev,
          projects: data.projects,
          pagination: data.pagination
        }));
      } else {
        toast.error(data.error || "Failed to fetch projects");
      }
    } catch (error) {
      console.error("Error fetching admin data:", error);
      toast.error("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select items to delete");
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} projects? This will also delete all associated links.`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/projects", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ projectIds: selectedItems }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setSelectedItems([]);
        fetchData();
      } else {
        toast.error(result.error || "Batch delete failed");
      }
    } catch (error) {
      console.error("Error in batch delete:", error);
      toast.error("Batch delete failed");
    } finally {
      setLoading(false);
    }
  };

  // Handle selection changes
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(adminData.projects.map(project => project.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleSearch = () => {
    setCurrentPage(0);
    fetchData(0);
  };

  const handleResetFilters = () => {
    setFilters({ search: "", user_id: "" });
    setCurrentPage(0);
    fetchData(0);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchData(newPage);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <div className="flex items-center gap-2 sm:gap-4 mb-2">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-0">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold truncate">项目管理</h1>
            <p className="text-muted-foreground text-sm sm:text-base hidden sm:block">管理用户创建的所有项目</p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="min-w-0 flex-1">
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <Package2 className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                <span className="truncate">项目管理</span>
              </CardTitle>
              <CardDescription className="text-sm mt-1 hidden sm:block">
                管理所有用户创建的项目和相关数据
              </CardDescription>
            </div>
            <Button onClick={() => fetchData()} disabled={loading} size="sm" className="self-start sm:self-auto">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">刷新</span>
              <span className="sm:hidden">刷新</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="space-y-4 mb-6">
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索项目名称或描述..."
                    value={filters.search}
                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                    className="pl-10"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleSearch} disabled={loading} className="flex-1 sm:flex-none">
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>

                <Button onClick={handleResetFilters} variant="outline" className="flex-1 sm:flex-none">
                  <Filter className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>
            </div>
          </div>

          {/* Batch Actions */}
          {selectedItems.length > 0 && (
            <Alert className="mb-4">
              <Info className="h-4 w-4" />
              <AlertDescription className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                <span className="text-sm">已选择 {selectedItems.length} 个项目</span>
                <Button onClick={handleBatchDelete} variant="destructive" size="sm" className="self-start sm:self-auto">
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Mobile Card View */}
          <div className="block lg:hidden space-y-4">
            {/* Mobile Select All */}
            {adminData.projects.length > 0 && (
              <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedItems.length === adminData.projects.length && adminData.projects.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm text-muted-foreground">
                    {selectedItems.length === adminData.projects.length ? '取消全选' : '全选'}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground">
                  共 {adminData.projects.length} 个项目
                </span>
              </div>
            )}
            
            {adminData.projects.map((project) => (
              <Card key={project.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    {/* Header with checkbox and title */}
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={selectedItems.includes(project.id)}
                        onCheckedChange={(checked) => handleSelectItem(project.id, checked as boolean)}
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-foreground truncate">
                          {project.name}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {project.user_email || "未知用户"}
                        </p>
                        {project.description && (
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                            {project.description}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          window.open(`/projects/${project.id}`, '_blank');
                        }}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between pt-2 border-t">
                      <div className="flex items-center gap-4">
                        <Badge variant="outline" className="text-xs">
                          {project.links_count || 0} 个外链
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          流量: {project.total_traffic?.toLocaleString() || 0}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {project.created_at ? new Date(project.created_at).toLocaleDateString('zh-CN') : "-"}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden lg:block rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedItems.length === adminData.projects.length && adminData.projects.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>项目名称</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>外链数量</TableHead>
                  <TableHead>总流量</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {adminData.projects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedItems.includes(project.id)}
                        onCheckedChange={(checked) => handleSelectItem(project.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{project.name}</TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate" title={project.description || ""}>
                        {project.description || "无描述"}
                      </div>
                    </TableCell>
                    <TableCell>{project.user_email || "未知用户"}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {project.links_count || 0} 个外链
                      </Badge>
                    </TableCell>
                    <TableCell>{project.total_traffic?.toLocaleString() || 0}</TableCell>
                    <TableCell>
                      {project.created_at ? new Date(project.created_at).toLocaleDateString('zh-CN') : "-"}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          window.open(`/projects/${project.id}`, '_blank');
                        }}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6">
            <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
              显示 {adminData.pagination.offset + 1} - {Math.min(adminData.pagination.offset + adminData.pagination.limit, adminData.pagination.total)} 
              ，共 {adminData.pagination.total} 条
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none"
              >
                上一页
              </Button>
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!adminData.pagination.hasMore}
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none"
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 