
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Items } from "@/types/items";
import { UserCaseType } from "@/types/usercase";
import { searchItems } from "@/models/items";
import { insertUserCase } from "@/models/usercase";
import { v4 as uuidv4 } from "uuid";
import { UserCaseForm } from "@/components/blocks/usercase/user-case-form";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { extractMediaContent } from "@/services/media";


// 提取媒体内容的服务端操作
async function extractMedia(formData: FormData) {
  "use server";
  
  const url = formData.get("url") as string;
  const type = formData.get("type") as UserCaseType;
  
  if (!url || !type) {
    return { success: false, error: "URL and type are required" };
  }
  
  try {
    // 直接调用函数处理，而不是通过API
    const result = await extractMediaContent(url, type);
    return result;
  } catch (error) {
    console.error("Error extracting media:", error);
    return { success: false, error: "Failed to extract media content" };
  }
}

// 搜索Items的服务端操作
async function searchItemsAction(formData: FormData) {
  "use server";
  
  const query = formData.get("query") as string;
  
  if (!query) {
    return { success: false, error: "Search query is required" };
  }
  
  try {
    const { data, count, error } = await searchItems(query, "en", 1, 50);
    
    if (error) {
      throw error;
    }
    
    console.log("Search results:", data);
    
    // 确保返回的数据格式与客户端组件预期的一致
    const formattedData = data?.map((item: any) => {
      // 统一格式，确保返回的对象包含uuid字段
      const uuid = item.uuid || item.item_uuid;
      return {
        ...item,
        uuid: uuid,
        name: item.name || `Item ${uuid}`,
        brief: item.brief || ""
      };
    }) || [];
    
    return { 
      success: true, 
      data: formattedData, 
      count 
    };
  } catch (error) {
    console.error("Error searching Items:", error);
    return { success: false, error: "Failed to search Items" };
  }
}

// 创建用户案例的服务端操作
async function createUserCase(formData: FormData) {
  "use server";
  
  try {
    const type = formData.get("type") as UserCaseType;
    const url = formData.get("url") as string;
    const title = formData.get("title") as string;
    const author_name = formData.get("author_name") as string;
    const author_avatar_url = formData.get("author_avatar_url") as string;
    const aiSummary = formData.get("ai_summary") as string;
    const contentText = formData.get("content") as string;
    const selectedItemsJson = formData.get("selected_items") as string;
    const imageUrlsJson = formData.get("image_urls") as string;
    const videoUrlsJson = formData.get("video_urls") as string;
    const detailsJson = formData.get("details") as string;
    const locale = formData.get("locale") as string || "en";
    
    // 解析JSON数据
    const selectedItems = JSON.parse(selectedItemsJson || "[]");
    const imageUrls = JSON.parse(imageUrlsJson || "[]");
    const videoUrls = JSON.parse(videoUrlsJson || "[]");
    const details = JSON.parse(detailsJson || "{}");
    
    // 验证必填字段
    if (!url || !type) {
      return {
        success: false,
        error: "URL and type are required"
      };
    }
    
    // 准备多语言对象
    const aiSummaryObj: Record<string, string> = {
      [locale]: aiSummary
    };
    
    const contentObj: Record<string, string> = {
      [locale]: contentText
    };
    
    // 准备用户案例数据
    const userCaseData = {
      uuid: getUuid(),
      type,
      url,
      title,
      author_name,
      author_avatar_url,
      details,
      related_items: selectedItems,
      ai_summary: aiSummaryObj,
      content: contentObj,
      image_urls: imageUrls,
      video_urls: videoUrls,
      status: "created",
      created_at: getIsoTimestr(),
      updated_at: getIsoTimestr()
    };
    
    // 插入用户案例
    await insertUserCase(userCaseData);
    
    // 重新验证路径
    revalidatePath("/admin/user-cases");
    
    // 返回成功结果，避免在服务端组件中使用redirect
    return {
      success: true,
      message: "User case created successfully",
      redirectTo: "/admin/user-cases"
    };
  } catch (error) {
    console.error("Error creating user case:", error);
    return {
      success: false,
      error: "Failed to create user case, error: " + error
    };
  }
}

export default async function CreateUserCasePage() {
  // 获取Items列表用于下拉选择
  const { data: itemsData } = await searchItems("", "en", 1, 100);
  
  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Create User Case</h1>
        <Link href="/admin/user-cases">
          <Button variant="outline">Back to List</Button>
        </Link>
      </div>
      
      <UserCaseForm 
        items={itemsData || []}
        extractMediaAction={extractMedia}
        searchItemsAction={searchItemsAction}
        createUserCaseAction={createUserCase}
      />
    </div>
  );
} 
