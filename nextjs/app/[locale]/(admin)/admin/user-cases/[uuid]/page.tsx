
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { UserCaseCard } from "@/components/blocks/usercase/usercase-card";
import { UserCase } from "@/types/usercase";
import { Items } from "@/types/items";
import Link from "next/link";
import { findUserCaseByUuid, deleteUserCase } from "@/models/usercase";
import { getItemByUuid } from "@/models/items";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";


// 删除用户案例的服务器操作
async function deleteUserCaseAction(formData: FormData) {
  "use server";
  
  const uuid = formData.get("uuid") as string;
  
  if (!uuid) {
    throw new Error("UUID is required");
  }
  
  try {
    await deleteUserCase(uuid);
    
    // 重新验证路径并重定向
    revalidatePath("/admin/user-cases");
    redirect("/admin/user-cases");
  } catch (error) {
    console.error("Error deleting user case:", error);
    throw new Error(`Failed to delete user case: ${error}`);
  }
}

export default async function UserCaseDetailPage({ params }: { params: Promise<{ uuid: string, locale: string }> }) {
  const { uuid, locale } = await params;
  
  // 获取用户案例数据
  const userCase = await findUserCaseByUuid(uuid);
  
  if (!userCase) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center py-20">
          <div className="text-center">
            <div className="mb-4 text-4xl">🔍</div>
            <h2 className="text-xl font-medium">User case not found</h2>
            <p className="mt-2 text-gray-500">The user case you're looking for doesn't exist or you don't have permission to view it.</p>
            <div className="mt-6">
              <Link href="/admin/user-cases">
                <Button>Back to List</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // 获取相关的Items
  let relatedItems: Items[] = [];
  if (userCase.related_items && userCase.related_items.length > 0) {
    const itemsPromises = userCase.related_items.map(async (itemUuid: string) => {
      try {
        const { data: item } = await getItemByUuid(itemUuid);
        return item;
      } catch (error) {
        console.error(`Error fetching Item ${itemUuid}:`, error);
        return null;
      }
    });
    
    const itemsResults = await Promise.all(itemsPromises);
    relatedItems = itemsResults.filter(Boolean) as Items[];
  }
  
  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">
          {userCase.title || (userCase.type === 'twitter' ? 'Twitter Post' : userCase.type === 'jike' ? 'Jike Post' : 'YouTube Video')}
        </h1>
        <div className="flex space-x-3">
          <Link href="/admin/user-cases">
            <Button variant="outline">Back to List</Button>
          </Link>
          <Link href={`/admin/user-cases/${uuid}/edit`}>
            <Button variant="outline">Edit</Button>
          </Link>
          <form action={deleteUserCaseAction}>
            <input type="hidden" name="uuid" value={uuid} />
            <Button variant="destructive" type="submit">Delete</Button>
          </form>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Content Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-4">
                <UserCaseCard userCase={userCase} className="w-full max-w-lg" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>User Case Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <h3 className="mb-2 font-medium">Basic Information</h3>
                    <dl className="space-y-2">
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">Type:</dt>
                        <dd className="flex-1 capitalize">{userCase.type}</dd>
                      </div>
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">Author:</dt>
                        <dd className="flex-1">{userCase.author_name || 'Unknown'}</dd>
                      </div>
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">Created:</dt>
                        <dd className="flex-1">{userCase.created_at ? new Date(userCase.created_at).toLocaleString() : 'Unknown'}</dd>
                      </div>
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">Status:</dt>
                        <dd className="flex-1 capitalize">{userCase.status || 'online'}</dd>
                      </div>
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">URL:</dt>
                        <dd className="flex-1 break-all">
                          <a href={userCase.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                            {userCase.url}
                          </a>
                        </dd>
                      </div>
                    </dl>
                  </div>
                  
                  <div>
                    <h3 className="mb-2 font-medium">Media</h3>
                    <dl className="space-y-2">
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">Images:</dt>
                        <dd className="flex-1">{userCase.image_urls?.length || 0}</dd>
                      </div>
                      <div className="flex flex-wrap">
                        <dt className="w-32 font-medium text-gray-500">Videos:</dt>
                        <dd className="flex-1">{userCase.video_urls?.length || 0}</dd>
                      </div>
                    </dl>
                    
                    {userCase.image_urls && userCase.image_urls.length > 0 && (
                      <div className="mt-4">
                        <details>
                          <summary className="cursor-pointer text-sm text-blue-500 hover:text-blue-700">
                            Show image URLs
                          </summary>
                          <div className="mt-2 max-h-40 overflow-y-auto rounded border p-2">
                            {userCase.image_urls.map((url, idx) => (
                              <div key={idx} className="mb-1 text-xs">
                                <a
                                  href={url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="hover:underline"
                                >
                                  {url}
                                </a>
                              </div>
                            ))}
                          </div>
                        </details>
                      </div>
                    )}
                    
                    {userCase.video_urls && userCase.video_urls.length > 0 && (
                      <div className="mt-4">
                        <details>
                          <summary className="cursor-pointer text-sm text-blue-500 hover:text-blue-700">
                            Show video URLs
                          </summary>
                          <div className="mt-2 max-h-40 overflow-y-auto rounded border p-2">
                            {userCase.video_urls.map((url, idx) => (
                              <div key={idx} className="mb-1 text-xs">
                                <a
                                  href={url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="hover:underline"
                                >
                                  {url}
                                </a>
                              </div>
                            ))}
                          </div>
                        </details>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <h3 className="mb-2 font-medium">Localized Content</h3>
                  <div className="space-y-4">
                    {userCase.ai_summary && Object.keys(userCase.ai_summary).length > 0 && (
                      <div>
                        <div className="mb-1 font-medium text-gray-500">AI Summary:</div>
                        <div className="rounded-md border p-3">
                          {Object.entries(userCase.ai_summary).map(([lang, summary]) => (
                            <div key={lang}>
                              <span className="mr-2 font-medium">{lang}:</span>
                              <span>{summary}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {userCase.content && Object.keys(userCase.content).length > 0 && (
                      <div>
                        <div className="mb-1 font-medium text-gray-500">Content:</div>
                        <div className="rounded-md border p-3">
                          {Object.entries(userCase.content).map(([lang, content]) => (
                            <div key={lang}>
                              <span className="mr-2 font-medium">{lang}:</span>
                              <span>{content}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Related Items</CardTitle>
            </CardHeader>
            <CardContent>
              {relatedItems.length > 0 ? (
                <div className="space-y-3">
                  {relatedItems.map((item) => (
                    <Link key={item.uuid} href={`/admin/items/${item.uuid}`}>
                      <div className="flex cursor-pointer items-center gap-3 rounded-lg border p-3 transition-colors hover:bg-gray-50">
                        {item.item_avatar_url && (
                          <img 
                            src={item.item_avatar_url} 
                            alt={item.name} 
                            className="h-10 w-10 rounded-md object-cover" 
                          />
                        )}
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-sm text-gray-500">
                            {item.author_name || "Unknown author"}
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="py-4 text-center text-gray-500">
                  No related Items found
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 
