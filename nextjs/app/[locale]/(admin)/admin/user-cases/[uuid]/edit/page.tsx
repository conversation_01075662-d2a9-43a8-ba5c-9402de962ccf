
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { findUserCaseByUuid, updateUserCase } from "@/models/usercase";
import { Items } from "@/types/items";
import { UserCase } from "@/types/usercase";
import Link from "next/link";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { EditUserCaseForm } from "@/components/blocks/usercase/edit-user-case-form";


// 更新用户案例的服务器操作
async function updateUserCaseAction(formData: FormData) {
  "use server";
  
  const uuid = formData.get("uuid") as string;
  const title = formData.get("title") as string;
  const status = formData.get("status") as string;
  const aiSummary = formData.get("ai_summary") as string;
  const contentText = formData.get("content") as string;
  const selectedItemsJson = formData.get("selected_items") as string;
  const locale = formData.get("locale") as string || "en";
  
  if (!uuid) {
    throw new Error("UUID is required");
  }
  
  try {
    // 获取当前用户案例数据
    const currentUserCase = await findUserCaseByUuid(uuid);
    
    if (!currentUserCase) {
      throw new Error("User case not found");
    }
    
    // 解析JSON数据
    const selectedItems = JSON.parse(selectedItemsJson || "[]");
    
    // 更新多语言对象
    const updatedAiSummary = {
      ...currentUserCase.ai_summary,
      [locale]: aiSummary
    };
    
    const updatedContent = {
      ...currentUserCase.content,
      [locale]: contentText
    };
    
    // 准备更新数据
    const updateData: Partial<UserCase> = {
      title,
      status,
      related_items: selectedItems,
      ai_summary: updatedAiSummary,
      content: updatedContent,
      updated_at: new Date().toISOString()
    };
    
    // 更新用户案例
    await updateUserCase(uuid, updateData);
    
    // 重新验证路径并重定向
    revalidatePath("/admin/user-cases");
    revalidatePath(`/admin/user-cases/${uuid}`);
    
    // 返回成功信息和重定向URL
    return {
      success: true,
      message: "User case updated successfully",
      redirectTo: `/admin/user-cases/${uuid}`
    };
  } catch (error) {
    console.error("Error updating user case:", error);
    throw new Error(`Failed to update user case: ${error}`);
  }
}

export default async function EditUserCasePage({ params }: { params: Promise<{ uuid: string, locale: string }> }) {
  const { uuid, locale } = await params;
  
  // 获取用户案例数据
  const userCase = await findUserCaseByUuid(uuid);
  
  if (!userCase) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center py-20">
          <div className="text-center">
            <div className="mb-4 text-4xl">🔍</div>
            <h2 className="text-xl font-medium">User case not found</h2>
            <p className="mt-2 text-gray-500">The user case you're looking for doesn't exist or you don't have permission to view it.</p>
            <div className="mt-6">
              <Link href="/admin/user-cases">
                <Button>Back to List</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // 获取所有Items用于选择
  const items = [] as Items[];
  
  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Edit User Case</h1>
        <div className="flex space-x-3">
          <Link href={`/admin/user-cases/${uuid}`}>
            <Button variant="outline">Cancel</Button>
          </Link>
        </div>
      </div>
      
      <EditUserCaseForm 
        userCase={userCase}
        items={items}
        locale={locale}
        updateUserCaseAction={updateUserCaseAction}
      />
    </div>
  );
} 
