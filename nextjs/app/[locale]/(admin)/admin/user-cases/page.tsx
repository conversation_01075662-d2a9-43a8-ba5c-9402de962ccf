
import { UserCase } from "@/types/usercase";
import { UserCasesTable } from "@/components/blocks/usercase/user-cases-table";
import { getAllUserCases } from "@/models/usercase";


export default async function UserCasesPage() {
  // 直接从数据库获取用户案例
  const { data: userCases } = await getAllUserCases();

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-8 text-3xl font-bold">User Cases Management</h1>
      <UserCasesTable userCases={userCases} loading={false} />
    </div>
  );
} 
