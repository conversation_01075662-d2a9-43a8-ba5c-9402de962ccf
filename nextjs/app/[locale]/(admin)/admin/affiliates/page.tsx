
import { getAllAffiliateProducts } from '@/models/affiliate';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, ExternalLink, BarChart3, Check, X } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { format } from 'date-fns';

export default async function AffiliateManagementPage() {
  const { data: affiliateProducts, error } = await getAllAffiliateProducts();
  
  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Affiliate Products Management</h1>
          <p className="text-muted-foreground">Manage promotional affiliate products displayed across the platform</p>
        </div>
        
        <Link href="/admin/affiliates/new">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Affiliate Product
          </Button>
        </Link>
      </div>
      
      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Active Products</TabsTrigger>
          <TabsTrigger value="inactive">Inactive Products</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active" className="space-y-4">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md text-red-600 dark:text-red-400">
              Error loading affiliate products: {error.message}
            </div>
          )}
          
          {!error && affiliateProducts.filter(p => p.isActive).length === 0 && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md text-yellow-600 dark:text-yellow-400">
              No active affiliate products found. Create a new product to get started.
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {affiliateProducts
              .filter(product => product.isActive)
              .map(product => (
                <AffiliateProductCard 
                  key={product.id} 
                  product={product} 
                />
              ))
            }
          </div>
        </TabsContent>
        
        <TabsContent value="inactive" className="space-y-4">
          {!error && affiliateProducts.filter(p => !p.isActive).length === 0 && (
            <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-md text-gray-600 dark:text-gray-400">
              No inactive affiliate products found.
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {affiliateProducts
              .filter(product => !product.isActive)
              .map(product => (
                <AffiliateProductCard 
                  key={product.id} 
                  product={product} 
                />
              ))
            }
          </div>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Affiliate Products Performance</CardTitle>
              <CardDescription>Click-through metrics for all affiliate products</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative w-full h-[300px] bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center">
                  <BarChart3 className="h-16 w-16 text-slate-300 dark:text-slate-600" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <p className="text-sm text-muted-foreground">Analytics visualization coming soon</p>
                  </div>
                </div>
                
                <div className="border rounded-md">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium text-sm">Product</th>
                        <th className="text-center p-3 font-medium text-sm">Status</th>
                        <th className="text-center p-3 font-medium text-sm">Clicks</th>
                        <th className="text-right p-3 font-medium text-sm">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {affiliateProducts.map(product => (
                        <tr key={product.id} className="border-b">
                          <td className="p-3 flex items-center">
                            <div className="relative w-8 h-8 mr-2 rounded overflow-hidden">
                              <Image 
                                src={product.imageUrl} 
                                alt={product.title}
                                fill
                                className="object-cover"
                              />
                            </div>
                            <span className="text-sm">{product.title}</span>
                          </td>
                          <td className="p-3 text-center">
                            <div className="flex items-center justify-center">
                              {product.isActive ? (
                                <div className="flex items-center text-green-600">
                                  <Check className="w-4 h-4 mr-1" />
                                  <span className="text-xs">Active</span>
                                </div>
                              ) : (
                                <div className="flex items-center text-gray-400">
                                  <X className="w-4 h-4 mr-1" />
                                  <span className="text-xs">Inactive</span>
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="p-3 text-center">{product.clicks || 0}</td>
                          <td className="p-3 text-right">
                            <Link href={`/admin/affiliates/${product.id}`}>
                              <Button variant="outline" size="sm">View</Button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function AffiliateProductCard({ product }: { product: any }) {
  return (
    <Card>
      <div className="relative">
        <div className="relative h-40 w-full bg-slate-100 dark:bg-slate-800 rounded-t-lg overflow-hidden">
          <Image 
            src={product.imageUrl} 
            alt={product.title}
            fill
            className="object-cover"
          />
        </div>
        
        {product.tags && product.tags.length > 0 && (
          <div className="absolute top-2 left-2 flex flex-wrap gap-1">
            {product.tags.map((tag: string) => (
              <Badge key={tag} variant="secondary" className="bg-black/70 text-white">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>
      
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center justify-between">
          {product.title}
          {product.isActive ? (
            <Badge variant="default" className="bg-green-600">Active</Badge>
          ) : (
            <Badge variant="secondary">Inactive</Badge>
          )}
        </CardTitle>
        <CardDescription>
          <span className="flex items-center">
            <ExternalLink className="w-3 h-3 mr-1" />
            {new URL(product.link).hostname}
          </span>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-2">
        <p className="text-sm text-muted-foreground line-clamp-2">{product.description}</p>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2">
          <div>Created: {format(new Date(product.createdAt), 'MMM d, yyyy')}</div>
          <div>{product.clicks || 0} clicks</div>
        </div>
        
        <div className="flex gap-2 pt-2">
          <Link href={`/admin/affiliates/${product.id}`} className="flex-1">
            <Button variant="outline" size="sm" className="w-full">
              Edit
            </Button>
          </Link>
          <Link href={product.link} target="_blank" rel="noopener noreferrer">
            <Button variant="secondary" size="sm">
              <ExternalLink className="w-3 h-3 mr-1" />
              Visit
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
} 