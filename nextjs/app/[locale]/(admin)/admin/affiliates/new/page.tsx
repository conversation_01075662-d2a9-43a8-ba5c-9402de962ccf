'use client';


import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { createAffiliateProduct } from '@/models/affiliate';
import { toast } from 'sonner';
import { ChevronLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { v4 as uuidv4 } from 'uuid';

export default function NewAffiliatePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image_url: '',
    link: '',
    tags: '',
    is_active: true
  });
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Parse tags from comma-separated string to array
      const tags = formData.tags
        ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        : [];
      
      // Create product with UUID
      const { data, error } = await createAffiliateProduct({
        uuid: uuidv4(),
        title: formData.title,
        description: formData.description,
        image_url: formData.image_url,
        link: formData.link,
        tags,
        is_active: formData.is_active
      });
      
      if (error) throw error;
      
      toast.success('Affiliate product created successfully');
      router.push('/admin/affiliates');
    } catch (error: any) {
      toast.error('Failed to create product: ' + (error.message || 'Unknown error'));
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="p-8">
      <div className="mb-6">
        <Link href="/admin/affiliates" className="text-primary hover:underline flex items-center">
          <ChevronLeft className="w-4 h-4 mr-1" />
          Back to Affiliate Products
        </Link>
      </div>
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>New Affiliate Product</CardTitle>
        </CardHeader>
        
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                placeholder="Enter product title"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Enter product description"
                rows={3}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="imageUrl">Image URL</Label>
              <Input
                id="image_url"
                name="image_url"
                value={formData.image_url}
                onChange={handleChange}
                placeholder="https://example.com/image.jpg"
                type="url"
                required
              />
              <p className="text-xs text-muted-foreground">
                Provide a direct link to an image (recommended size: 800x600px)
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="link">Affiliate Link</Label>
              <Input
                id="link"
                name="link"
                value={formData.link}
                onChange={handleChange}
                placeholder="https://affiliate.example.com/?ref=your-id"
                type="url"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tags">Tags (comma-separated)</Label>
              <Input
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleChange}
                placeholder="ai, tools, productivity"
              />
              <p className="text-xs text-muted-foreground">
                Optional. Separate tags with commas (e.g. "ai, tools, productivity")
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={handleSwitchChange}
              />
              <Label htmlFor="is_active">Active</Label>
              <p className="text-xs text-muted-foreground ml-2">
                Product will be visible on the site when active
              </p>
            </div>
          </CardContent>
          
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.push('/admin/affiliates')}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Product
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
} 