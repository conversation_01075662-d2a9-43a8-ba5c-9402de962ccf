
import Dropdown from "@/components/blocks/table/dropdown";
import { NavItem } from "@/types/blocks/base";
import { Post } from "@/types/post";
import { Table as TableSlotType } from "@/types/slots/table";
import { getAllPosts, PostStatus, updatePost } from "@/models/post";
import { locales, localeNames } from "@/i18n/locale";
import moment from "moment";
import { getTranslations } from "next-intl/server";
import TableSlot from "@/components/dashboard/slots/table";
import { ReactNode } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import Link from "next/link";
import { Loader2 } from "lucide-react";

// Server actions
async function filterByStatus(formData: FormData) {
  "use server";
  const status = formData.get("status") as string;
  const locale = formData.get("locale") as string;
  
  let url = "/admin/posts";
  const params = new URLSearchParams();
  
  if (status) {
    params.set("status", status);
  }
  
  if (locale) {
    params.set("locale", locale);
  }
  
  const queryString = params.toString();
  if (queryString) {
    url += `?${queryString}`;
  }
  
  redirect(url);
}

async function filterByLocale(formData: FormData) {
  "use server";
  const locale = formData.get("locale") as string;
  const status = formData.get("status") as string;
  
  let url = "/admin/posts";
  const params = new URLSearchParams();
  
  if (status) {
    params.set("status", status);
  }
  
  if (locale) {
    params.set("locale", locale);
  }
  
  const queryString = params.toString();
  if (queryString) {
    url += `?${queryString}`;
  }
  
  redirect(url);
}

// Define custom column type
interface CustomColumn {
  title: string | ReactNode;
  name: string;
  className?: string;
}

// Enhanced type for the filter items
interface EnhancedFilterItem extends NavItem {
  status?: string | null;
  locale?: string | null;
  className?: string;
}

export default async function PostsPage({
  searchParams,
}: {
  searchParams: Promise<{ status?: string; locale?: string; page?: string; limit?: string }>;
}) {
  const posts = await getAllPosts();
  const t = await getTranslations("admin.posts");
  const params = await searchParams;
  
  const status = params.status || null;
  const locale = params.locale || null;
  
  // 筛选数据
  const filteredPosts = posts.filter(post => {
    // 跳过无效记录
    if (!post) return false;
    
    // 状态筛选
    if (status && String(post.status).toLowerCase() !== String(status).toLowerCase()) {
      return false;
    }
    
    // 语言筛选
    if (locale && String(post.locale).toLowerCase() !== String(locale).toLowerCase()) {
      return false;
    }
    
    return true;
  });
  
  // Calculate status counts
  // Use PostStatus enum for status values
  const statusCounts = {
    all: posts.length || 0,
    [PostStatus.Online]: posts.filter(post => post.status === PostStatus.Online).length || 0,
    [PostStatus.Offline]: posts.filter(post => post.status === PostStatus.Offline).length || 0,
    [PostStatus.Created]: posts.filter(post => post.status === PostStatus.Created).length || 0,
  };
  
  // Calculate locale counts from available locales
  const localeCounts: Record<string, number> = {
    all: posts.length || 0,
  };
  
  locales.forEach(loc => {
    localeCounts[loc] = posts.filter(post => post.locale === loc).length || 0;
  });

  // Prepare toolbar status filter items using PostStatus enum
  const statusFilterItems: EnhancedFilterItem[] = [
    {
      title: `${t("filter.all")} (${statusCounts.all})`,
      url: "/admin/posts",
      icon: "RiListUnordered",
      is_active: !status,
      status: null,
    },
    {
      title: `${t("status.online")} (${statusCounts[PostStatus.Online]})`,
      url: `/admin/posts?status=${PostStatus.Online}`,
      icon: "RiCheckLine",
      is_active: status === PostStatus.Online,
      status: PostStatus.Online,
    },
    {
      title: `${t("status.offline")} (${statusCounts[PostStatus.Offline]})`,
      url: `/admin/posts?status=${PostStatus.Offline}`,
      icon: "RiDraftLine",
      is_active: status === PostStatus.Offline,
      status: PostStatus.Offline,
    },
    {
      title: `${t("status.created")} (${statusCounts[PostStatus.Created]})`,
      url: `/admin/posts?status=${PostStatus.Created}`,
      icon: "RiFile2Line",
      is_active: status === PostStatus.Created,
      status: PostStatus.Created,
    },
  ];
  
  // Prepare toolbar locale filter items from locales array
  const localeFilterItems: EnhancedFilterItem[] = [
    {
      title: `${t("filter.allLocales")} (${localeCounts.all})`,
      url: locale ? `/admin/posts${status ? `?status=${status}` : ''}` : "/admin/posts",
      icon: "RiGlobalLine",
      is_active: !locale,
      locale: null,
    },
    ...locales.map(loc => ({
      title: `${localeNames[loc] || loc.toUpperCase()} (${localeCounts[loc] || 0})`,
      url: `/admin/posts?${status ? `status=${status}&` : ''}locale=${loc}`,
      icon: "RiTranslate2",
      is_active: locale === loc,
      locale: loc,
    })),
  ];

  // Enhance filter items with styling
  const enhancedStatusFilterItems = statusFilterItems.map(item => ({
    ...item,
    className: cn(
      "px-3 py-2 rounded-md text-sm font-medium transition-colors",
      item.is_active 
        ? "bg-blue-500 text-white hover:bg-blue-600" 
        : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
    )
  }));
  
  const enhancedLocaleFilterItems = localeFilterItems.map(item => ({
    ...item,
    className: cn(
      "px-3 py-2 rounded-md text-sm font-medium transition-colors",
      item.is_active 
        ? "bg-blue-500 text-white hover:bg-blue-600" 
        : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
    )
  }));

  // Prepare data with pre-rendered UI components
  const serializedPosts = filteredPosts.map(post => {
    const formattedDate = post.created_at ? moment(post.created_at).format("YYYY-MM-DD HH:mm:ss") : "";
    
    // Pre-render dropdown for post actions
    const actionsDisplay = (
      <Dropdown items={[
        {
          title: "Edit",
          icon: "RiEditLine",
          url: `/admin/posts/${post.uuid}/edit`,
        },
        {
          title: "View",
          icon: "RiEyeLine",
          url: `/${post.locale}/posts/${post.slug}`,
          target: "_blank",
        }
      ]} />
    );
    
    // Return the post with additional display fields
    return {
      ...post,
      formattedDate,
      actionsDisplay
    };
  });

  // Create table configuration
  const tableConfig: TableSlotType = {
    title: t("title"),
    toolbar: {
      items: [
        {
          title: "Add Post",
          icon: "RiAddLine",
          url: "/admin/posts/add",
          className: "ml-auto bg-green-500 text-white hover:bg-green-600"
        },
      ],
      component: (
        <div className="flex flex-col space-y-4">
          {/* Nothing else needed here */}
        </div>
      )
    },
    filters: [
      {
        title: "Status",
        items: enhancedStatusFilterItems.map(item => ({
          ...item,
          onClick: undefined, // Remove onClick handlers as we're using forms instead
          formAction: filterByStatus,
          formData: {
            status: item.status,
            locale: params.locale || ''
          }
        }))
      },
      {
        title: "Language",
        items: enhancedLocaleFilterItems.map(item => ({
          ...item,
          onClick: undefined, // Remove onClick handlers as we're using forms instead
          formAction: filterByLocale,
          formData: {
            locale: item.locale,
            status: params.status || ''
          }
        }))
      }
    ],
    columns: [
      {
        name: "title",
        title: "Title",
      },
      {
        name: "description",
        title: "Description",
      },
      {
        name: "slug",
        title: "Slug",
      },
      {
        name: "locale",
        title: "Locale",
      },
      {
        name: "status",
        title: "Status",
      },
      {
        name: "formattedDate",
        title: "Created At",
      },
      {
        name: "actionsDisplay",
        title: "Actions",
      },
    ],
    data: serializedPosts,
    empty_message: filteredPosts.length === 0 ? t("empty") : undefined,
  };

  return <TableSlot {...tableConfig} />;
}
