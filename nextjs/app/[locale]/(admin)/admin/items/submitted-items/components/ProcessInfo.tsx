"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Info } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface ProcessInfoProps {
  name: string;
  preprocessinfo: Record<string, any> | null;
}

export default function ProcessInfo({ name, preprocessinfo }: ProcessInfoProps) {
  if (!preprocessinfo) {
    return "-";
  }
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8 text-blue-600">
          <Info className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Preprocessed Info for {name}</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          <div className="mt-2 space-y-4">
            {Object.entries(preprocessinfo).map(([key, value]) => (
              <div key={key} className="border p-3 rounded-md">
                <h3 className="font-semibold capitalize">{key}</h3>
                <div className="mt-1 text-sm">
                  {typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
                </div>
              </div>
            ))}
          </div>
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
} 