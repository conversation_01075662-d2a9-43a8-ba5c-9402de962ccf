"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

interface LocationInfoProps {
  name: string;
  itemUuid: string | null;
  locationData: {
    enContent: string | null;
    enProcessinfo: string | null;
    availableLanguages: string[];
  } | null;
}

export default function LocationInfo({ name, itemUuid, locationData }: LocationInfoProps) {
  if (!itemUuid || !locationData) {
    return "-";
  }
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8 text-indigo-600">
          <MapPin className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Location Info for {name}</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          <div className="mt-2 space-y-4">
            <div className="border p-3 rounded-md">
              <h3 className="font-semibold">English Content</h3>
              <div className="mt-1 text-sm">
                {locationData.enContent || "No English content available"}
              </div>
            </div>
            
            {locationData.enProcessinfo && (
              <div className="border p-3 rounded-md">
                <h3 className="font-semibold">Process Info</h3>
                <div className="mt-1 text-sm whitespace-pre-wrap">
                  {locationData.enProcessinfo}
                </div>
              </div>
            )}
            
            <div className="border p-3 rounded-md">
              <h3 className="font-semibold">Available Translations</h3>
              <div className="mt-2 flex flex-wrap gap-2">
                {locationData.availableLanguages.length > 0 ? (
                  locationData.availableLanguages.map((lang) => (
                    <Badge key={lang} variant="outline" className="capitalize">
                      {lang}
                    </Badge>
                  ))
                ) : (
                  <span className="text-sm text-gray-500">No translations available</span>
                )}
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
} 