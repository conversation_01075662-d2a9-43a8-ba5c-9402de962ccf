"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Check, X, Loader2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";

interface ActionButtonsProps {
  id: number;
  status: string;
  name: string;
  rejectedText: string;
  viewText: string;
}

export default function ActionButtons({
  id,
  status,
  name,
  rejectedText,
  viewText
}: ActionButtonsProps) {
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(status);
  const { toast } = useToast();

  // Handle approve submission
  const handleApprove = async () => {
    if (isApproving || isRejecting) return;

    setIsApproving(true);
    try {
      const response = await fetch(`/api/admin/submissions/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include' // 确保包含cookies
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(errorData.error || 'Failed to approve submission');
      }

      setCurrentStatus('approved');
      toast({
        title: "Success",
        description: `Submission ${id} has been approved.`,
        variant: "default",
      });

      // Refresh the page after a short delay to show updated data
      setTimeout(() => {
        window.location.reload();
      }, 1500);

    } catch (error) {
      console.error('Error approving submission:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to approve submission',
        variant: "destructive",
      });
    } finally {
      setIsApproving(false);
    }
  };

  // Handle reject submission
  const handleReject = async () => {
    if (isApproving || isRejecting) return;

    setIsRejecting(true);
    try {
      const response = await fetch(`/api/admin/submissions/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include' // 确保包含cookies
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(errorData.error || 'Failed to reject submission');
      }

      setCurrentStatus('rejected');
      toast({
        title: "Success",
        description: `Submission ${id} has been rejected.`,
        variant: "default",
      });

      // Refresh the page after a short delay to show updated data
      setTimeout(() => {
        window.location.reload();
      }, 1500);

    } catch (error) {
      console.error('Error rejecting submission:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to reject submission',
        variant: "destructive",
      });
    } finally {
      setIsRejecting(false);
    }
  };

  // 对于processed状态，显示批准和拒绝按钮
  if (currentStatus === 'processed') {
    return (
      <div className="flex space-x-2">
        <Button
          onClick={handleApprove}
          disabled={isApproving || isRejecting}
          size="icon"
          variant="outline"
          className="h-8 w-8 text-green-600 hover:text-green-700 border-green-600 hover:border-green-700"
        >
          {isApproving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Check className="h-4 w-4" />}
        </Button>
        <Button
          onClick={handleReject}
          disabled={isApproving || isRejecting}
          size="icon"
          variant="outline"
          className="h-8 w-8 text-red-600 hover:text-red-700 border-red-600 hover:border-red-700"
        >
          {isRejecting ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
        </Button>
      </div>
    );
  }

  // 对于pending状态，只显示拒绝按钮
  if (currentStatus === 'pending') {
    return (
      <div className="flex space-x-2">
        <Button
          onClick={handleReject}
          disabled={isRejecting}
          size="icon"
          variant="outline"
          className="h-8 w-8 text-red-600 hover:text-red-700 border-red-600 hover:border-red-700"
        >
          {isRejecting ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
        </Button>
      </div>
    );
  }

  // 对于rejected状态，只显示状态标签，没有操作按钮
  if (currentStatus === 'rejected') {
    return (
      <Badge variant="destructive">
        {rejectedText}
      </Badge>
    );
  }

  if (currentStatus === 'approved') {
    return (
      <Badge variant="default" className="bg-green-600 hover:bg-green-700">
        Approved
      </Badge>
    );
  }

  return (
    <Button
      size="sm"
      variant="outline"
      className="text-blue-600"
      asChild
    >
      <Link href={`/search?q=${name}`}>
        {viewText}
      </Link>
    </Button>
  );
}