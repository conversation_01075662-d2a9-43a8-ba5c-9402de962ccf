"use client";

import { Submission } from "@/types/items";
import moment from "moment";
import { ExternalLink, Clock, Mail, User, Info } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { NavItem } from "@/types/blocks/base";
import { cn } from "@/lib/utils";
import { lazy } from "react";
import { formatDate } from "@/lib/utils";
// 延迟加载组件以减少初始加载体积
const ActionButtons = lazy(() => import('./Actions'));
const ProcessInfo = lazy(() => import('./ProcessInfo'));

interface StatusCounts {
  all: number;
  pending: number;
  processed: number;
  approved: number;
  rejected: number;
}

interface SubmissionsListProps {
  submissions: Submission[];
  statusCounts: StatusCounts;
  currentStatus: string | null;
  currentPage: number;
  totalPages: number;
  filterAction: (formData: FormData) => Promise<void>;
  changePageAction: (formData: FormData) => Promise<void>;
}

export default function SubmissionsList({
  submissions,
  statusCounts,
  currentStatus,
  currentPage,
  totalPages,
  filterAction,
  changePageAction
}: SubmissionsListProps) {

  // Prepare toolbar filter items
  interface EnhancedToolbarItem extends NavItem {
    status: string | null;
    className?: string;
  }

  const toolbarItems: EnhancedToolbarItem[] = [
    {
      title: `All (${statusCounts.all})`,
      url: "/admin/items/submitted-items",
      icon: "RiListUnordered",
      is_active: !currentStatus,
      status: null,
    },
    {
      title: `Pending (${statusCounts.pending})`,
      url: "/admin/items/submitted-items?status=pending",
      icon: "RiTimeLine",
      is_active: currentStatus === "pending",
      status: "pending",
    },
    {
      title: `Processed (${statusCounts.processed})`,
      url: "/admin/items/submitted-items?status=processed",
      icon: "RiCheckLine",
      is_active: currentStatus === "processed",
      status: "processed",
    },
    {
      title: `Approved (${statusCounts.approved})`,
      url: "/admin/items/submitted-items?status=approved",
      icon: "RiCheckLine",
      is_active: currentStatus === "approved",
      status: "approved",
    },
    {
      title: `Rejected (${statusCounts.rejected})`,
      url: "/admin/items/submitted-items?status=rejected",
      icon: "RiCloseLine",
      is_active: currentStatus === "rejected",
      status: "rejected",
    },
  ];

  // Enhance toolbar items with proper styling
  const enhancedToolbarItems = toolbarItems.map(item => ({
    ...item,
    className: cn(
      "px-3 py-2 rounded-md text-sm font-medium transition-colors",
      item.is_active
        ? "bg-blue-500 text-white hover:bg-blue-600"
        : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
    ),
    onClick: undefined, // Remove onClick as it's not needed for server components
  }));

  // Prepare serialized submissions with pre-rendered UI elements
  const serializedSubmissions = submissions.map(submission => {
    const formattedCreatedAt = moment(submission.created_at).fromNow();
    const fullCreatedAtDate = formatDate(submission.created_at);
    const badgeVariant = submission.status === 'approved' ? 'default' :
                      submission.status === 'rejected' ? 'destructive' :
                      submission.status === 'processed' ? 'secondary' : 'default';

    // Pre-render UI elements
    const nameDisplay = (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="max-w-[180px] truncate">
              {submission.name}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{submission.name}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

    const authorDisplay = (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="max-w-[120px] truncate">
              {submission.author_name}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{submission.author_name}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

    const websiteDisplay = (
      <a
        href={submission.website_url}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-500 hover:underline"
        title={submission.website_url}
      >
        <ExternalLink className="h-4 w-4" />
      </a>
    );

    const emailDisplay = submission.email ? (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="max-w-[120px] truncate">
              {submission.email}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{submission.email}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    ) : "-";

    const statusDisplay = (
      <Badge variant={badgeVariant as any}>
        {submission.status}
      </Badge>
    );

    const dateDisplay = (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>{formattedCreatedAt}</div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{fullCreatedAtDate}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );

    // Using the separated components
    const processInfoDisplay = (
      <ProcessInfo
        name={submission.name}
        preprocessinfo={submission.preprocessinfo}
      />
    );

    const actionsDisplay = (
      <ActionButtons
        id={submission.id}
        status={submission.status}
        name={submission.name}
        rejectedText="Rejected"
        viewText="View"
      />
    );

    return {
      ...submission,
      nameDisplay,
      authorDisplay,
      websiteDisplay,
      emailDisplay,
      statusDisplay,
      dateDisplay,
      processInfoDisplay,
      actionsDisplay,
    };
  });

  // Table configuration
  const tableConfig: TableSlotType = {
    title: "Item Submissions",
    tip: {
      title: "Manage Item Submissions from users",
    },
    toolbar: {
      items: [],
      // Render filter buttons
      component: (
        <div className="flex flex-wrap gap-2">
          {toolbarItems.map((item, index) => (
            <form key={index} action={filterAction}>
              <input type="hidden" name="status" value={item.status || ""} />
              <Button
                type="submit"
                className={item.className}
                variant="ghost"
              >
                {item.title}
              </Button>
            </form>
          ))}
        </div>
      )
    },
    filters: [
      {
        title: "Status",
        items: enhancedToolbarItems
      }
    ],
    columns: [
      {
        title: "Name",
        name: "nameDisplay",
      },
      {
        title: <User className="h-4 w-4" />,
        name: "authorDisplay",
      },
      {
        title: <ExternalLink className="h-4 w-4" />,
        name: "websiteDisplay",
      },
      {
        title: <Mail className="h-4 w-4" />,
        name: "emailDisplay",
      },
      {
        title: <Info className="h-4 w-4" />,
        name: "processInfoDisplay",
      },
      {
        title: "Status",
        name: "statusDisplay",
      },
      {
        title: <Clock className="h-4 w-4" />,
        name: "dateDisplay",
      },
      {
        title: "Actions",
        name: "actionsDisplay",
      },
    ],
    data: serializedSubmissions,
    empty_message: "No submissions found",
    pagination: {
      currentPage,
      totalPages,
      onPageChange: async (page: number) => {
        const formData = new FormData();
        formData.append('page', page.toString());
        if (currentStatus) formData.append('status', currentStatus);
        await changePageAction(formData);
      },
    }
  };

  return <TableSlot {...tableConfig} />;
}