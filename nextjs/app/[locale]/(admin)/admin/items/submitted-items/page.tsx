
import { getSubmissions, getSubmissionStatusCounts } from "@/models/submission";
import type { Submission } from "@/types/items";
import { getUserInfo } from "@/services/user";
import { redirect } from "next/navigation";
import Empty from "@/components/blocks/empty";
import { Badge } from "@/components/ui/badge";
import ActionButtons from "./components/Actions";
import ProcessInfo from "./components/ProcessInfo";
import LocationInfo from "./components/LocationInfo";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { TableColumn } from "@/types/blocks/table";
import { filterByStatus, changePage, getItemLocationByUuid } from "./actions";
import Link from "next/link";

export default async function SubmittedItemsPage({
  searchParams,
}: {
  searchParams: Promise<{ status?: string; page?: string; limit?: string }>;
}) {
  const params = await searchParams;
  // 获取查询参数
  const status = params.status || null;
  const page = Number(params.page) || 1;
  const limit = Number(params.limit) || 20;

  // 获取状态计数
  const statusCounts = await getSubmissionStatusCounts();

  // 获取提交数据，直接在数据库中过滤
  const { data = [], count = 0 } = await getSubmissions(limit, page, status);

  // 使用已经过滤的数据
  const filteredData = data || [];

  // 计算分页信息
  const totalItems = count || 0;
  const totalPages = Math.ceil(totalItems / limit);

  // 预处理数据
  const processedData = await Promise.all(filteredData.map(async (submission: Submission) => {
    // 创建状态显示
    const statusDisplay = (
      <Badge variant={
        submission.status === "approved" ? "default" :
        submission.status === "rejected" ? "destructive" :
        submission.status === "processed" ? "outline" : "secondary"
      }
      className={submission.status === "approved" ? "bg-green-600 hover:bg-green-700" : undefined}
      >
        {submission.status}
      </Badge>
    );

    // 创建日期显示
    const dateDisplay = new Date(submission.created_at).toLocaleDateString();

    // 创建链接显示
    const linkDisplay = submission.website_url ? (
      <Link
        href={submission.website_url}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:underline"
      >
        {submission.website_url.length > 30
          ? submission.website_url.substring(0, 30) + '...'
          : submission.website_url
        }
      </Link>
    ) : "-";

    // 创建预处理信息显示
    const processInfoDisplay = (
      <ProcessInfo
        name={submission.name}
        preprocessinfo={submission.preprocessinfo || null}
      />
    );
    
    // Get Item UUID from preprocessinfo if available
    const itemUuid = submission.preprocessinfo?.uuid || null;
    
    // Fetch location data if we have a UUID
    let locationData = null;
    if (itemUuid) {
      const locationResult = await getItemLocationByUuid(itemUuid);
      if (locationResult.data) {
        // Find English content
        const enLocalization = locationResult.data.find(loc => loc.language_code === 'en');
        // Get list of available languages - filter out any undefined values
        const availableLanguages = locationResult.data
          .map(loc => loc.language_code)
          .filter((lang): lang is string => lang !== undefined);
        
        locationData = {
          enContent: enLocalization?.brief || null,
          enProcessinfo: enLocalization?.processinfo || null,
          availableLanguages
        };
      }
    }
    
    // Create location info display
    const locationInfoDisplay = (
      <LocationInfo
        name={submission.name}
        itemUuid={itemUuid}
        locationData={locationData}
      />
    );

    // 创建操作显示
    const actionsDisplay = (
      <ActionButtons
        id={submission.id}
        status={submission.status}
        name={submission.name}
        rejectedText="Rejected"
        viewText="View"
      />
    );

    return {
      ...submission,
      id_display: `#${submission.id}`,
      status_display: statusDisplay,
      created_at_display: dateDisplay,
      link_display: linkDisplay,
      process_info_display: processInfoDisplay,
      location_info_display: locationInfoDisplay,
      actions_display: actionsDisplay,
    };
  }));

  // 定义表格列
  const columns: TableColumn[] = [
    { name: "id_display", title: "ID" },
    { name: "name", title: "Name" },
    { name: "email", title: "Submitter" },
    { name: "created_at_display", title: "Submitted" },
    { name: "link_display", title: "Link" },
    { name: "status_display", title: "Status" },
    { name: "process_info_display", title: "Process Info" },
    { name: "location_info_display", title: "Location" },
    { name: "actions_display", title: "Actions" },
  ];

  // 创建过滤器项
  const filterItems = [
    {
      title: `All (${statusCounts.all})`,
      className: !status ? "bg-primary text-primary-foreground px-3 py-1 rounded-md" : "px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",
      formAction: filterByStatus,
      formData: { status: "" },
    },
    {
      title: `Pending (${statusCounts.pending})`,
      className: status === "pending" ? "bg-primary text-primary-foreground px-3 py-1 rounded-md" : "px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",
      formAction: filterByStatus,
      formData: { status: "pending" },
    },
    {
      title: `Processed (${statusCounts.processed})`,
      className: status === "processed" ? "bg-primary text-primary-foreground px-3 py-1 rounded-md" : "px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",
      formAction: filterByStatus,
      formData: { status: "processed" },
    },
    {
      title: `Approved (${statusCounts.approved})`,
      className: status === "approved" ? "bg-primary text-primary-foreground px-3 py-1 rounded-md" : "px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",
      formAction: filterByStatus,
      formData: { status: "approved" },
    },
    {
      title: `Rejected (${statusCounts.rejected})`,
      className: status === "rejected" ? "bg-primary text-primary-foreground px-3 py-1 rounded-md" : "px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700",
      formAction: filterByStatus,
      formData: { status: "rejected" },
    },
  ];

  // 创建分页工具栏项
  const paginationItems = [];

  // 上一页按钮
  if (page > 1) {
    paginationItems.push({
      title: "Previous",
      formAction: changePage,
      formData: { page: (page - 1).toString(), status: status || "" },
    });
  }

  // 页码指示器
  paginationItems.push({
    title: `Page ${page} of ${totalPages}`,
    disabled: true,
  });

  // 下一页按钮
  if (page < totalPages) {
    paginationItems.push({
      title: "Next",
      formAction: changePage,
      formData: { page: (page + 1).toString(), status: status || "" },
    });
  }

  // 表格配置
  const tableConfig: TableSlotType = {
    title: "Item Submissions",
    tip: {
      title: "Manage Item Submissions from users",
    },
    toolbar: {
      items: paginationItems,
    },
    filters: [
      {
        title: "Status",
        items: filterItems,
      },
    ],
    columns,
    data: processedData,
    empty_message: "No submissions found",
  };

  return <TableSlot {...tableConfig} />;
}
