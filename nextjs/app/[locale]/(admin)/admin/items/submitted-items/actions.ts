"use server";

import { redirect } from "next/navigation";
import { getItemLocalizationsByUuid } from "@/models/items";

// 定义服务器操作
export async function filterByStatus(formData: FormData) {
  const status = formData.get("status") as string;
  redirect(status ? `/admin/items/submitted-items?status=${status}` : "/admin/items/submitted-items");
}

export async function changePage(formData: FormData) {
  const page = formData.get("page") as string;
  const status = formData.get("status") as string;
  const url = status 
    ? `/admin/items/submitted-items?status=${status}&page=${page}` 
    : `/admin/items/submitted-items?page=${page}`;
  redirect(url);
}

// Fetch Item localization data by UUID using the model
export async function getItemLocationByUuid(uuid: string) {
  return await getItemLocalizationsByUuid(uuid);
}