
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Globe, FileText, Inbox, Activity, TrendingUp, Eye, Users, BarChart3 } from "lucide-react";
import { getItemStats } from "@/models/items";

// Get recent traffic data - approximation in case umami is not available
async function getRecentTraffic() {
  try {
    // Attempt to fetch from umami API if configured
    // This is a placeholder - in a real implementation, you would fetch actual umami data
    return {
      todayVisitors: 150,
      weeklyVisitors: 850,
      todayPageviews: 320,
      weeklyPageviews: 1640,
      topPages: [
        { path: '/', views: 230 },
        { path: '/items', views: 180 },
        { path: '/submit', views: 95 },
      ]
    };
  } catch (error) {
    console.error('Error fetching traffic data:', error);
    // Return some fallback data
    return {
      todayVisitors: 0,
      weeklyVisitors: 0,
      todayPageviews: 0,
      weeklyPageviews: 0,
      topPages: []
    };
  }
}

export default async function ItemsManagementIndex() {
  const t = await getTranslations("admin.items.index");
  
  // Fetch Item statistics and traffic data
  const stats = await getItemStats();
  const traffic = await getRecentTraffic();
  
  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
        <p className="text-muted-foreground mt-2">
          {t("description")}
        </p>
      </div>
      
      <Tabs defaultValue="overview" className="mb-8">
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            {t("tabs.overview")}
          </TabsTrigger>
          <TabsTrigger value="traffic">
            <Activity className="h-4 w-4 mr-2" />
            {t("tabs.traffic")}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("stats.total_items")}
                </CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalItems || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.publicItems || 0} {t("stats.public")}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("stats.submissions")}
                </CardTitle>
                <Inbox className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalSubmissions || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.pendingSubmissions || 0} {t("stats.pending")}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("stats.localizations")}
                </CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalLocalizations || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {t("stats.translations")}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("stats.total_clicks")}
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalClicks.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {t("stats.engagement")}
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="traffic" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("traffic.today_visitors")}
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{traffic.todayVisitors.toLocaleString()}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("traffic.today_pageviews")}
                </CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{traffic.todayPageviews.toLocaleString()}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("traffic.weekly_visitors")}
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{traffic.weeklyVisitors.toLocaleString()}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("traffic.weekly_pageviews")}
                </CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{traffic.weeklyPageviews.toLocaleString()}</div>
              </CardContent>
            </Card>
          </div>
          
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>{t("traffic.top_pages")}</CardTitle>
              <CardDescription>{t("traffic.top_pages_desc")}</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {traffic.topPages.map((page, i) => (
                  <li key={i} className="flex items-center justify-between pb-2 border-b">
                    <span className="text-sm font-medium">{page.path}</span>
                    <span className="text-sm text-muted-foreground">{page.views} {t("traffic.views")}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-3">
        <Card className="col-span-1 lg:col-span-1">
          <CardHeader>
            <CardTitle>{t("actions.submissions")}</CardTitle>
            <CardDescription>{t("actions.submissions_desc")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <a 
                href="/admin/items/submitted-items" 
                className="block py-2 px-4 bg-primary text-primary-foreground rounded-md text-center font-medium"
              >
                {t("actions.go_to_submissions")}
              </a>
              {(stats.pendingSubmissions || 0) > 0 && (
                <div className="text-sm font-medium text-center text-amber-600 mt-2">
                  {stats.pendingSubmissions || 0} {t("actions.pending_review")}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1 lg:col-span-1">
          <CardHeader>
            <CardTitle>{t("actions.manage")}</CardTitle>
            <CardDescription>{t("actions.manage_desc")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <a 
                href="/admin/items/total-items" 
                className="block py-2 px-4 bg-primary text-primary-foreground rounded-md text-center font-medium"
              >
                {t("actions.go_to_items")}
              </a>
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1 lg:col-span-1">
          <CardHeader>
            <CardTitle>{t("actions.tags")}</CardTitle>
            <CardDescription>{t("actions.tags_desc")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <a 
                href="/admin/items/tags-items" 
                className="block py-2 px-4 bg-primary text-primary-foreground rounded-md text-center font-medium"
              >
                {t("actions.go_to_tags")}
              </a>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1 lg:col-span-1">
          <CardHeader>
            <CardTitle>{t("actions.tools")}</CardTitle>
            <CardDescription>{t("actions.tools_desc")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <a 
                href="/admin/items/tools" 
                className="block py-2 px-4 bg-primary text-primary-foreground rounded-md text-center font-medium"
              >
                {t("actions.go_to_tools")}
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 
