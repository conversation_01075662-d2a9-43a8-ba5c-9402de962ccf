
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Wrench, Code, Database, PlusCircle, Globe, PencilLine } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Link from "next/link";
import { getItemToolsList } from "@/models/itemTools";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// 创建一个简单的 DataTable 组件作为占位符
function DataTable({ columns, data, pagination = false, search = false }) {
  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id}>{column.header}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {columns.map((column) => (
                  <TableCell key={column.id}>
                    {column.cell ? column.cell(row) : row[column.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default async function ItemToolsManagement() {
  const t = await getTranslations("admin.items.tools");
  
  // Fetch Item tools data
  const { data: itemTools, error } = await getItemToolsList();
  
  // Define columns for the data table
  const columns = [
    {
      id: "item_uuid",
      header: t("table.item_uuid"),
      cell: (row) => <span className="font-mono text-xs">{row.uuid}</span>,
    },
    {
      id: "type",
      header: t("table.type"),
      cell: (row) => (
        <Badge variant={
          row.type === "sse" ? "default" :
          row.type === "stdio" ? "secondary" : "outline"
        }>
          {row.type}
        </Badge>
      ),
    },
    {
      id: "tools_count",
      header: t("table.tools_count"),
      cell: (row) => row.tools?.length || 0,
    },
    {
      id: "updated_at",
      header: t("table.updated_at"),
      cell: (row) => {
        const date = row.updated_at ? new Date(row.updated_at) : null;
        return date ? date.toLocaleDateString() : "-";
      },
    },
    {
      id: "actions",
      header: t("table.actions"),
      cell: (row) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/mcps/tools/${row.uuid}`} passHref>
            <Button variant="outline" size="sm">
              <PencilLine className="h-4 w-4 mr-1" />
              {t("actions.edit")}
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
        <p className="text-muted-foreground mt-2">
          {t("description")}
        </p>
      </div>
      
      <Tabs defaultValue="tools" className="mb-8">
        <TabsList>
          <TabsTrigger value="tools">
            <Wrench className="h-4 w-4 mr-2" />
            {t("tabs.tools")}
          </TabsTrigger>
          <TabsTrigger value="translations">
            <Globe className="h-4 w-4 mr-2" />
            {t("tabs.translations")}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="tools" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t("tools.title")}</CardTitle>
                  <CardDescription>{t("tools.description")}</CardDescription>
                </div>
                <Link href="/admin/mcps/tools/create" passHref>
                  <Button>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    {t("tools.add_new")}
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {error ? (
                <Alert variant="destructive">
                  <AlertTitle>{t("error.title")}</AlertTitle>
                  <AlertDescription>{t("error.description")}</AlertDescription>
                </Alert>
              ) : itemTools && itemTools.length > 0 ? (
                <DataTable
                  columns={columns}
                  data={itemTools}
                  pagination
                  search
                />
              ) : (
                <div className="text-center py-8">
                  <Database className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">{t("tools.no_data_title")}</h3>
                  <p className="text-muted-foreground mt-1">{t("tools.no_data_description")}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="translations" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("translations.title")}</CardTitle>
              <CardDescription>{t("translations.description")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="border rounded-md p-6">
                  <h3 className="text-lg font-medium mb-4">{t("translations.how_it_works_title")}</h3>
                  <div className="space-y-4">
                    <p>{t("translations.how_it_works_desc1")}</p>
                    <div className="bg-slate-100 dark:bg-slate-800 p-4 rounded-md">
                      <pre className="text-xs whitespace-pre-wrap"><code>{`// Example of i18n structured fields
{
  "name": "tool_name",
  "description": "Default description",
  "i18n_description": { 
    "en": "English description", 
    "zh": "中文描述"
  }
}`}</code></pre>
                    </div>
                    <p>{t("translations.how_it_works_desc2")}</p>
                  </div>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>{t("translations.supported_languages_title")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        <li className="flex items-center">
                          <Badge variant="outline" className="mr-2">en</Badge>
                          <span>English</span>
                        </li>
                        <li className="flex items-center">
                          <Badge variant="outline" className="mr-2">zh</Badge>
                          <span>中文 (Chinese)</span>
                        </li>
                        {/* Add more languages as needed */}
                      </ul>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>{t("translations.translation_tips_title")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 list-disc pl-5">
                        <li>{t("translations.tip1")}</li>
                        <li>{t("translations.tip2")}</li>
                        <li>{t("translations.tip3")}</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 