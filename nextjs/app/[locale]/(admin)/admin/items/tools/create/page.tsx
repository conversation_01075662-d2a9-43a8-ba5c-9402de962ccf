
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Save, Wrench } from "lucide-react";
import Link from "next/link";
import { ItemToolEdit } from "@/components/admin/tools/ItemToolEdit";

export default async function CreateToolPage() {
  const t = await getTranslations("admin.mcps.tools");
  
  // Create an empty MCP tool template
  const emptyItemTool = {
    uuid: '',  // Will be generated when saved
    type: 'both' as 'both' | 'sse' | 'stdio',
    tools: [],
    allow_public: false,
    updated_at: new Date().toISOString()
  };
  
  return (
    <div className="container py-10">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link href="/admin/mcps/tools" passHref>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("actions.back")}
            </Button>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight">{t("edit.title")}</h1>
        <p className="text-muted-foreground mt-2">
          {t("edit.description")}
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{t("edit.tools_title")}</CardTitle>
          <CardDescription>{t("edit.tools_description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <ItemToolEdit itemTools={emptyItemTool} isCreating={true} />
        </CardContent>
      </Card>
    </div>
  );
} 