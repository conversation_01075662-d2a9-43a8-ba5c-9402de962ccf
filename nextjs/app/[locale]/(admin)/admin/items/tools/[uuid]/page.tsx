
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ArrowLeft, Delete, Globe, Save, Wrench } from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getItemToolsByUuid, deleteItemTools } from "@/models/itemTools";
import { ItemToolEdit } from "@/components/admin/tools/ItemToolEdit";
import { ToolsTranslationEditor } from "@/components/admin/tools/ToolsTranslationEditor";
import { DeleteItemToolAction } from "@/components/admin/tools/DeleteItemToolAction";

interface ToolEditPageProps {
  params: Promise<{
    uuid: string;
  }>;
}

export default async function ToolEditPage({ params }: ToolEditPageProps) {
  const t = await getTranslations("admin.items.tools");
  const { uuid } = await params;
  
  // Fetch the MCP tool data
  const { data: itemTools, error } = await getItemToolsByUuid(uuid);
  
  // If there's an error or no data, show not found
  if (error || !itemTools) {
    notFound();
  }
  
  return (
    <div className="container py-10">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link href="/admin/items/tools" passHref>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("actions.back")}
            </Button>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight">{t("edit.title")}</h1>
        <p className="text-muted-foreground mt-2">
          {t("edit.description")}
        </p>
        <div className="mt-2 flex items-center">
          <code className="bg-muted p-1 text-sm rounded font-mono">{uuid}</code>
        </div>
      </div>
      
      <Tabs defaultValue="tools" className="mb-8">
        <TabsList>
          <TabsTrigger value="tools">
            <Wrench className="h-4 w-4 mr-2" />
            {t("tabs.tools")}
          </TabsTrigger>
          <TabsTrigger value="translations">
            <Globe className="h-4 w-4 mr-2" />
            {t("tabs.translations")}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="tools" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("edit.tools_title")}</CardTitle>
              <CardDescription>{t("edit.tools_description")}</CardDescription>
            </CardHeader>
            <CardContent>
              <ItemToolEdit itemTools={itemTools} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="translations" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("edit.translations_title")}</CardTitle>
              <CardDescription>{t("edit.translations_description")}</CardDescription>
            </CardHeader>
            <CardContent>
              <ToolsTranslationEditor itemTools={itemTools} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <Card className="bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="text-red-600 dark:text-red-400">
            {t("edit.danger_zone")}
          </CardTitle>
          <CardDescription className="text-red-600/80 dark:text-red-400/80">
            {t("edit.danger_description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeleteItemToolAction uuid={uuid} />
        </CardContent>
      </Card>
    </div>
  );
} 