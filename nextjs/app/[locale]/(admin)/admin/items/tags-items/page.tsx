
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tag, TagIcon, Search, Trash2, ChevronDown, ChevronUp } from "lucide-react";
import { getAllTags, updateItemTags } from "@/models/items";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";


// Server action to delete a tag
async function deleteTag(formData: FormData) {
  "use server";
  
  const tagName = formData.get("tagName") as string;
  
  if (!tagName) {
    console.error("Missing tag name");
    return;
  }
  
  try {
    console.log(`Starting delete process for tag: "${tagName}"`);
    
    // Find Items that reference this tag in their 'tags' array field
    console.log(`Finding Items with tag "${tagName}" in their tags array`);
    const { getItemsByTagName } = await import("@/models/items");
    const { data: itemsWithTag, error: itemsQueryError } = await getItemsByTagName(tagName);
    
    if (itemsQueryError) {
      console.error(`Error finding Items with this tag:`, itemsQueryError);
      return;
    }
    
    // Update Items to remove the tag from their tags array
    if (itemsWithTag && itemsWithTag.length > 0) {
      console.log(`Found ${itemsWithTag.length} Items with tag: "${tagName}" in their tags array`);
      
      for (const item of itemsWithTag) {
        if (item.tags && Array.isArray(item.tags)) {
          console.log(`Updating Item ID: ${item.id}, removing tag from tags array`);
          const updatedTags = item.tags.filter(t => t !== tagName);
          
          try {
            const { error: updateError } = await updateItemTags(item.id, updatedTags);
              
            if (updateError) {
              console.error(`Error updating tags for Item ID ${item.id}:`, updateError);
            } else {
              console.log(`Successfully updated tags array for Item ID: ${item.id}`);
            }
          } catch (updateErr) {
            console.error(`Exception during Item tags array update for ID ${item.id}:`, updateErr);
          }
        }
      }
      
      console.log(`Successfully removed tag: "${tagName}" from all Items`);
    } else {
      console.log(`No Items found with tag: "${tagName}"`);
    }
    
    // Success - revalidate the page
    try {
      revalidatePath("/admin/items/tags-items");
      console.log(`Revalidated path: /admin/items/tags-items`);
    } catch (revalidateErr) {
      console.error(`Error during revalidation:`, revalidateErr);
    }
    
  } catch (error) {
    console.error("Unhandled error in deleteTag action:", error);
  }
}

// Server action to filter tags by name
async function searchTags(formData: FormData) {
  "use server";
  const searchTerm = formData.get("searchTerm") as string;
  redirect(`/admin/items/tags-items?search=${encodeURIComponent(searchTerm)}`);
}

// Server action to confirm and delete a tag
async function confirmAndDeleteTag(formData: FormData) {
  "use server";
  
  // We're using this action just to handle the form submission
  // The actual deletion happens in the deleteTag action
  return deleteTag(formData);
}

// Interface for tags with grouping
interface GroupedTags {
  [group: string]: {
    tag: string;
    count: number;
  }[];
}

export default async function TagsManagementPage({
  searchParams,
}: {
  searchParams: Promise<{ search?: string; expanded?: string }>;
}) {
  const params = await searchParams;
  const t = await getTranslations("admin.items.tags");
  const searchTerm = params.search || "";
  
  // Parse expanded groups from URL
  const expandedParam = params.expanded || 'all';
  const expandedGroups = expandedParam === 'all' 
    ? [] // 'all' means everything is expanded
    : expandedParam.split(',');
  
  // Fetch tags from the database
  const { data: tagsData = [], error } = await getAllTags();
  
  if (error) {
    console.error("Error fetching tags:", error);
  }
  
  // Filter tags based on search term if provided
  const filteredTags = searchTerm && tagsData
    ? tagsData.filter(tag => 
        tag.tag.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : tagsData || [];
  
  // Group tags in A/B format
  const groupedTags: GroupedTags = filteredTags.reduce((groups: GroupedTags, tag) => {
    const tagName = tag.tag;
    let group = 'Uncategorized';
    let displayTag = tagName;
    
    // Check if tag is in A/B format
    if (tagName.includes('/')) {
      const [prefix, suffix] = tagName.split('/', 2);
      group = prefix;
      displayTag = suffix;
    }
    
    if (!groups[group]) {
      groups[group] = [];
    }
    
    groups[group].push({
      tag: tagName,
      count: tag.count
    });
    
    return groups;
  }, {});
  
  // Sort groups by name, but keep Uncategorized at the top
  const sortedGroups = Object.keys(groupedTags).sort((a, b) => {
    if (a === 'Uncategorized') return -1;
    if (b === 'Uncategorized') return 1;
    return a.localeCompare(b);
  });
  
  // Toggle group expansion
  const toggleGroup = (group: string) => {
    const isExpanded = expandedParam === 'all' || expandedGroups.includes(group);
    let newExpanded;
    
    if (expandedParam === 'all') {
      // Collapse only this group, keep others expanded
      const allGroups = Object.keys(groupedTags);
      newExpanded = allGroups.filter(g => g !== group).join(',');
    } else if (isExpanded) {
      // Remove this group from expanded list
      newExpanded = expandedGroups.filter(g => g !== group).join(',');
    } else {
      // Add this group to expanded list
      newExpanded = [...expandedGroups, group].join(',');
    }
    
    if (newExpanded === '') {
      // If nothing is expanded, use 'none'
      newExpanded = 'none';
    } else if (Object.keys(groupedTags).length === newExpanded.split(',').length) {
      // If all groups are expanded, use 'all'
      newExpanded = 'all';
    }
    
    return `/admin/items/tags-items${searchTerm ? `?search=${encodeURIComponent(searchTerm)}&` : '?'}expanded=${newExpanded}`;
  };
  
  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
        <p className="text-muted-foreground mt-2">
          {t("description")}
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TagIcon className="h-5 w-5 mr-2" />
            {t("tag_groups.title")}
          </CardTitle>
          <CardDescription>{t("tags_list.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search form */}
          <form action={searchTags} className="mb-6">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <input
                type="search"
                name="searchTerm"
                placeholder="Search tags..."
                className="pl-8 h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                defaultValue={searchTerm}
              />
              <Button type="submit" variant="ghost" size="sm" className="absolute right-1 top-1">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </form>
          
          {/* Tag groups */}
          <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
            {sortedGroups.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {sortedGroups.map(group => {
                  const isExpanded = expandedParam === 'all' || expandedGroups.includes(group);
                  const tagsInGroup = groupedTags[group];
                  const tagCount = tagsInGroup.length;
                  const totalItems = tagsInGroup.reduce((sum, tag) => sum + tag.count, 0);
                  
                  return (
                    <div key={group} className="mb-4">
                      <div className="border rounded-lg p-4">
                        <a
                          href={toggleGroup(group)}
                          className="flex items-center justify-between w-full text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 mb-3 group transition-colors"
                        >
                          <span className="font-medium capitalize flex items-center">
                            {group}
                            <span className="ml-2 text-xs text-gray-500">
                              ({tagCount} tags, {totalItems} Items)
                            </span>
                          </span>
                          {isExpanded ? (
                            <ChevronUp className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                          ) : (
                            <ChevronDown className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                          )}
                        </a>
                        
                        {isExpanded && (
                          <div className="space-y-2 mt-2">
                            {tagsInGroup.map(tagItem => {
                              const displayTag = tagItem.tag.includes('/') 
                                ? tagItem.tag.split('/')[1] 
                                : tagItem.tag;
                              
                              return (
                                <div key={tagItem.tag} className="flex items-center justify-between py-1 border-b border-gray-100 last:border-0">
                                  <div className="text-sm">
                                    <span>{displayTag}</span>
                                    <span className="text-xs text-gray-500 ml-2">
                                      ({tagItem.count})
                                    </span>
                                  </div>
                                  <form action={confirmAndDeleteTag}>
                                    <input type="hidden" name="tagName" value={tagItem.tag} />
                                    <Button 
                                      type="submit"
                                      variant="ghost" 
                                      size="icon"
                                      className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-100"
                                      title={`Delete tag "${tagItem.tag}" (used by ${tagItem.count} Items)`}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </form>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-muted-foreground">
                  {searchTerm ? `No tags found matching "${searchTerm}"` : "No tags available"}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 
