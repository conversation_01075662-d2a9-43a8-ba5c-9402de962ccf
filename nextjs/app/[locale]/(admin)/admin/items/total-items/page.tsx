
import { Table as TableSlotType } from "@/types/slots/table";
import TableSlot from "@/components/dashboard/slots/table";
import { getTranslations } from "next-intl/server";
import { Items } from "@/types/items";
import moment from "moment";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { updateItems, getAllItems, getItemLocalizationsByUuid, archiveItem, getItemByUuid } from "@/models/items"; // Added archiveItem
import { findSubmissionByWebsiteUrl, rejectSubmission } from "@/models/submission"; // Added submission functions
import { getItemToolsByUuid } from "@/models/itemTools"; // Import getItemToolsByUuid function
import { revalidatePath } from "next/cache";
import { NavItem } from "@/types/blocks/base";
import { Calendar, ExternalLink, Info, Tag, Check, X, ThumbsUp, Clock, Database, Star, Search, Filter, Trash2, Wrench } from "lucide-react"; // Added Wrench icon
import { ReactNode } from "react";
import { redirect } from "next/navigation";
import EditButton from "@/components/blocks/edit";
import { Dialog, DialogTrigger, DialogContent, DialogTitle, DialogHeader, DialogFooter, DialogClose } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Added AlertDialog components
import { formatDate } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Toggle field server actions
async function toggleField(formData: FormData) {
  "use server";
  const uuid = formData.get("uuid") as string;
  const field = formData.get("field") as string;
  const currentValue = formData.get("current") === "true";
  
  if (!uuid || !field) {
    console.error("Missing required parameters");
    return;
  }
  
  const validFields = ["is_recommended", "is_official", "allow_public"];
  if (!validFields.includes(field)) {
    console.error("Invalid field name");
    return;
  }
  
  try {
    const updateData: Record<string, any> = {
      uuid: uuid
    };
    updateData[field] = !currentValue;
    const result = await updateItems(updateData as Partial<Items>);
    if (result.error) {
      console.error(`Error toggling ${field}:`, result.error);
    }
    revalidatePath("/admin/items/total-items");
  } catch (error) {
    console.error("Error in toggle action:", error);
  }
}

// Search action to handle form submission
async function searchAction(formData: FormData) {
  "use server";
  const searchQuery = formData.get("search") as string;
  redirect(searchQuery ? `/admin/items/total-items?search=${encodeURIComponent(searchQuery)}` : "/admin/items/total-items");
}

// Filter actions
async function filterByRecommended() {
  "use server";
  redirect("/admin/items/total-items?filter=recommended");
}

async function filterByOfficial() {
  "use server";
  redirect("/admin/items/total-items?filter=official");
}

async function filterByPublic() {
  "use server";
  redirect("/admin/items/total-items?filter=public");
}

async function filterByNotPublic() {
  "use server";
  redirect("/admin/items/total-items?filter=not_public");
}

async function clearFilters() {
  "use server";
  redirect("/admin/items/total-items");
}

// Edit Item action
async function handleEditItem(updatedItem: Partial<Items>) {
  "use server";
  
  if (!updatedItem.uuid) {
    console.error("Item UUID is required for update");
    return;
  }
  
  try {
    const result = await updateItems(updatedItem);
    if (result.error) {
      console.error(`Error updating Item:`, result.error);
      return;
    }
    
    revalidatePath("/admin/items/total-items");
    return { success: true };
  } catch (error) {
    console.error("Error in edit action:", error);
    return { success: false, error: "Failed to update Item" };
  }
}

// Archive Item and reject corresponding submission if found
async function handleDeleteItem(formData: FormData) {
  "use server";

  const uuid = formData.get("uuid") as string;
  const website_url = formData.get("website_url") as string;

  if (!uuid || !website_url) {
    console.error("Item UUID and Website URL are required for deletion");
    throw new Error("Item UUID and Website URL are required for deletion");
  }

  try {
    // 1. Archive the Item
    const archiveResult = await archiveItem(uuid);
    if (archiveResult.error) {
      console.error(`Error archiving Item ${uuid}:`, archiveResult.error);
      throw new Error("Failed to archive Item");
    }
    console.log(`Item ${uuid} archived successfully.`);

    // 2. Find the corresponding submission by website_url
    const submission = await findSubmissionByWebsiteUrl(website_url);

    if (submission && submission.id) {
      console.log(`Found submission ${submission.id} for website ${website_url}. Attempting to reject.`);
      // 3. Reject the submission if found
      const rejectResult = await rejectSubmission(submission.id);
      if (rejectResult.error) {
        // Log error but don't fail the whole operation if archiving succeeded
        console.error(`Error rejecting submission ${submission.id} for Item ${uuid}:`, rejectResult.error);
      }
    }

    // 4. Revalidate the path to refresh the table
    revalidatePath("/admin/items/total-items");
  } catch (error) {
    console.error(`Error in delete action for Item ${uuid}:`, error);
  }
}

// Define a custom column type that allows ReactNode in title
interface CustomColumn {
  title: string | ReactNode;
  name: string;
  callback?: (item: any) => any;
  className?: string;
  type?: string;
}

export default async function TotalItemsPage({
  searchParams,
}: {
  searchParams: Promise<{ 
    page?: string;
    limit?: string;
    search?: string;
    filter?: string;
    localization?: string;
  }>;
}) {
  const params = await searchParams;
  const t = await getTranslations("admin.items.total");
  const page = Number(params.page) || 1;
  const limit = Number(params.limit) || 20;
  const search = params.search || "";
  const filter = params.filter || "";
  const localization = params.localization || "en";

  // Set up filters based on search params
  const filterOptions: {
    is_recommended?: boolean;
    is_official?: boolean;
    allow_public?: boolean;
    search?: string;
    localization?: string;
  } = {};
  
  if (search) {
    filterOptions.search = search;
  }
  
  if (filter === "recommended") {
    filterOptions.is_recommended = true;
  }
  if (filter === "official") {
    filterOptions.is_official = true;
  }
  if (filter === "public") {
    filterOptions.allow_public = true;
  }
  if (filter === "not_public") {
    filterOptions.allow_public = false;
  }
  
  if (filter === "localization") {
    filterOptions.localization = localization;
  }
  
  // Fetch all Items with pagination and filtering
  const { data = [], error, count = 0 } = await getAllItems({
    limit,
    page,
    filter: filterOptions
  });
  
  // Calculate pagination info
  const totalPages = Math.ceil((count || 0) / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;
  
  // Build pagination URL with all current filters
  const buildPageUrl = (pageNum: number) => {
    const params = new URLSearchParams();
    params.set('page', pageNum.toString());
    params.set('limit', limit.toString());
    if (search) params.set('search', search);
    if (filter) params.set('filter', filter);
    if (localization) params.set('localization', localization);
    return `/admin/items/total-items?${params.toString()}`;
  };
  
  // 预处理数据以避免直接传递回调函数给客户端组件
  const processedData = await Promise.all(
    (data || []).map(async item => {
      // 获取所有本地化内容
      const { data: localizationsRaw } = await getItemLocalizationsByUuid(item.uuid);
      const localizations = localizationsRaw || [];
      // 英文内容
      const enLoc = localizations.find(loc => loc.language_code === "en") || {};
      // 生成多语言弹窗内容
      const renderLangDialog = (field: "brief" | "processinfo") => (
        <Dialog>
          <DialogTrigger asChild>
            <div className="max-w-[140px] truncate cursor-pointer text-blue-600 hover:underline">
              {typeof enLoc[field] === "string" && enLoc[field]
                ? enLoc[field]
                : (field === "brief" ? item.brief : "-")}
            </div>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>{field === "brief" ? t("columns.brief") : t("columns.processinfo")}</DialogTitle>
            <div className="space-y-2 max-h-[400px] overflow-y-auto">
              {localizations.length === 0 ? (
                field === "brief" && item.brief
                  ? <div className="whitespace-pre-line text-sm">{item.brief}</div>
                  : <div className="text-gray-400">No data</div>
              ) : (
                localizations.map(loc => (
                  <div key={loc.language_code} className="border-b pb-2 mb-2">
                    <div className="font-mono text-xs text-gray-500 mb-1">{loc.language_code}</div>
                    <div className="whitespace-pre-line text-sm">
                      {typeof loc[field] === "string" && loc[field] ? loc[field] : <span className="text-gray-400">-</span>}
                    </div>
                  </div>
                ))
              )}
            </div>
          </DialogContent>
        </Dialog>
      );
      // Fetch Item tools data
      const { data: toolsData } = await getItemToolsByUuid(item.uuid);
      
      return {
        ...item,
        nameDisplay: (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[180px] truncate">
                  {item.name}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{item.name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ),
        briefDisplay: renderLangDialog("brief"),
        processinfoDisplay: renderLangDialog("processinfo"),
        websiteDisplay: (
          <a
            href={item.website_url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline"
            title={item.website_url}
          >
            <ExternalLink className="h-4 w-4" />
          </a>
        ),
        dateDisplay: (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>{moment(item.created_at).fromNow()}</div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{formatDate(item.created_at)}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ),
        recommendedDisplay: (
          <form action={toggleField}>
            <input type="hidden" name="uuid" value={item.uuid} />
            <input type="hidden" name="field" value="is_recommended" />
            <input type="hidden" name="current" value={item.is_recommended.toString()} />
            <Button 
              type="submit"
              variant={item.is_recommended ? "default" : "outline"}
              size="icon"
              className={`h-8 w-8 ${item.is_recommended ? "bg-green-600 hover:bg-green-700" : "text-gray-500"}`}
            >
              {item.is_recommended ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
            </Button>
          </form>
        ),
        officialDisplay: (
          <form action={toggleField}>
            <input type="hidden" name="uuid" value={item.uuid} />
            <input type="hidden" name="field" value="is_official" />
            <input type="hidden" name="current" value={item.is_official.toString()} />
            <Button 
              type="submit"
              variant={item.is_official ? "default" : "outline"}
              size="icon"
              className={`h-8 w-8 ${item.is_official ? "bg-blue-600 hover:bg-blue-700" : "text-gray-500"}`}
            >
              {item.is_official ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
            </Button>
          </form>
        ),
        publicDisplay: (
          <form action={toggleField}>
            <input type="hidden" name="uuid" value={item.uuid} />
            <input type="hidden" name="field" value="allow_public" />
            <input type="hidden" name="current" value={item.allow_public.toString()} />
            <Button 
              type="submit"
              variant={item.allow_public ? "default" : "outline"}
              size="icon"
              className={`h-8 w-8 ${item.allow_public ? "bg-purple-600 hover:bg-purple-700" : "text-gray-500"}`}
            >
              {item.allow_public ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
            </Button>
          </form>
        ),
        clicksDisplay: item.clicks,
        tagsDisplay: (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex flex-wrap gap-1 max-w-[120px] overflow-hidden">
                  {item.tags && item.tags.length > 0 ? (
                    <>
                      <Badge variant="outline" className="text-xs">
                        {item.tags[0]}
                      </Badge>
                      {item.tags.length > 1 && (
                        <Badge variant="outline" className="text-xs">
                          +{item.tags.length - 1}
                        </Badge>
                      )}
                    </>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="flex flex-wrap gap-1 max-w-[220px]">
                  {item.tags && item.tags.map((tag, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ),
        toolsDisplay: (
          <Dialog>
            <DialogTrigger asChild>
              <Button 
                variant="ghost" 
                className="flex items-center space-x-1 text-blue-600 hover:text-blue-800"
                size="sm"
              >
                <Wrench className="h-4 w-4" />
                <span>{toolsData?.tools?.length || 0}</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="w-[90vw] max-w-[800px] max-h-[80vh] overflow-hidden flex flex-col">
              <DialogHeader className="flex flex-row items-center justify-between">
                <DialogTitle className="flex items-center gap-2">
                  <Wrench className="h-5 w-5" />
                  Tools for {item.name}
                </DialogTitle>
                <DialogClose className="h-8 w-8 rounded-full hover:bg-gray-200 flex items-center justify-center">
                  <X className="h-4 w-4" />
                </DialogClose>
              </DialogHeader>
              
              <div className="overflow-y-auto pr-2" style={{ maxHeight: "calc(80vh - 120px)" }}>
                {toolsData && toolsData.tools && toolsData.tools.length > 0 ? (
                  <Tabs defaultValue="tools" className="w-full">
                    <TabsList className="mb-4">
                      <TabsTrigger value="tools">Tools List</TabsTrigger>
                      <TabsTrigger value="usage">Usage JSON</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="tools" className="space-y-4">
                      {toolsData.tools.map((tool, idx) => (
                        <div key={idx} className="border p-3 rounded-md">
                          <h3 className="text-md font-semibold mb-1">{tool.name}</h3>
                          <p className="text-xs text-gray-600 mb-2">{tool.description}</p>
                          
                          {tool.parameters && tool.parameters.length > 0 && (
                            <div className="mt-2">
                              <h4 className="text-xs font-medium mb-1">Parameters:</h4>
                              <div className="space-y-1">
                                {tool.parameters.map((param, paramIdx) => (
                                  <div key={paramIdx} className="bg-gray-50 p-2 rounded-sm">
                                    <div className="flex justify-between">
                                      <span className="font-mono text-xs">{param.name}</span>
                                      <span className="text-xs bg-gray-200 px-1 rounded text-gray-700">
                                        {param.type}
                                        {param.required ? " (required)" : ""}
                                      </span>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">{param.description}</p>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </TabsContent>
                    
                    <TabsContent value="usage" className="space-y-4">
                      <div className="bg-gray-50 p-3 rounded-md">
                        <h3 className="text-sm font-medium mb-2">Tool Type: <span className="font-normal">{toolsData.type || "unspecified"}</span></h3>
                        
                        {toolsData.usage?.sse && (
                          <div className="mt-3">
                            <h4 className="text-xs font-medium mb-1">SSE Usage:</h4>
                            <div className="bg-gray-900 text-gray-100 p-2 rounded text-xs overflow-x-auto max-h-[150px] overflow-y-auto">
                              <pre className="whitespace-pre-wrap break-words">
                                {toolsData.usage.sse.code_example}
                              </pre>
                            </div>
                            {toolsData.usage.sse.description && (
                              <p className="text-xs mt-1 text-gray-600">{toolsData.usage.sse.description}</p>
                            )}
                          </div>
                        )}
                        
                        {toolsData.usage?.stdio && (
                          <div className="mt-3">
                            <h4 className="text-xs font-medium mb-1">STDIO Usage:</h4>
                            <div className="bg-gray-900 text-gray-100 p-2 rounded text-xs overflow-x-auto max-h-[150px] overflow-y-auto">
                              <pre className="whitespace-pre-wrap break-words">
                                {toolsData.usage.stdio.code_example}
                              </pre>
                            </div>
                            {toolsData.usage.stdio.description && (
                              <p className="text-xs mt-1 text-gray-600">{toolsData.usage.stdio.description}</p>
                            )}
                          </div>
                        )}
                        
                        {!toolsData.usage?.sse && !toolsData.usage?.stdio && (
                          <p className="text-sm text-gray-500">No usage examples available.</p>
                        )}
                      </div>
                      
                      <div className="bg-gray-50 p-3 rounded-md">
                        <h3 className="text-sm font-medium mb-1">Raw JSON:</h3>
                        <div className="bg-gray-900 text-gray-100 p-2 rounded text-xs overflow-x-auto max-h-[200px] overflow-y-auto">
                          <pre className="whitespace-pre-wrap break-words">
                            {JSON.stringify(toolsData, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                ) : (
                  <div className="py-6 text-center text-gray-500">
                    <Wrench className="h-10 w-10 mx-auto opacity-20 mb-3" />
                    <p>No tools available for this Item</p>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        ),
        actionsDisplay: (
          <EditButton item={item} onSave={handleEditItem} />
        ),
        actionDelete: (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="icon" className="h-8 w-8">
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action will archive the Item '{item.name}' and reject its corresponding submission (if found). This cannot be easily undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <form action={handleDeleteItem}>
                  <input type="hidden" name="uuid" value={item.uuid} />
                  <input type="hidden" name="website_url" value={item.website_url} />
                  <AlertDialogAction type="submit">
                    Yes, Archive & Reject
                  </AlertDialogAction>
                </form>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )
      };
    })
  );

  // Table configuration
  const table: TableSlotType = {
    title: t("title"),
    tip: {
      title: t("tip"),
    },
    toolbar: {
      items: [
        // Search form (add before filter buttons)
        {
          type: "custom",
          element: (
            <form action={searchAction} className="flex items-center gap-2 mr-4">
              <input
                type="text"
                name="search"
                defaultValue={search}
                placeholder="Search by name or UUID"
                className="input input-sm border rounded px-2 py-1 text-sm w-56"
                autoComplete="off"
              />
              <Button type="submit" size="sm" variant="outline" className="text-xs">
                <Search className="h-4 w-4 mr-1" />
                Search
              </Button>
            </form>
          ) as any,
        } as any,
        {
          title: t("back_to_submissions"),
          url: "/admin/items/submitted-items",
          icon: "RiArrowLeftLine",
        } as NavItem,
        // Pagination controls
        ...(hasPrevPage ? [{
          title: t("prev_page"),
          url: buildPageUrl(page - 1),
          icon: "RiArrowLeftSLine",
        } as NavItem] : []),
        {
          title: `${page} / ${totalPages}`,
          disabled: true,
        } as NavItem,
        ...(hasNextPage ? [{
          title: t("next_page"),
          url: buildPageUrl(page + 1),
          icon: "RiArrowRightSLine",
        } as NavItem] : []),
      ],
    },
    // Add filters configuration
    filters: [
      {
        title: "Filters",
        items: [
          {
            title: "Recommended",
            className: `px-3 py-1 text-xs rounded-md ${filter === "recommended" ? "bg-blue-600 text-white" : "bg-gray-100 hover:bg-gray-200"}`,
            formAction: filterByRecommended,
            icon: "star"
          },
          {
            title: "Official",
            className: `px-3 py-1 text-xs rounded-md ${filter === "official" ? "bg-blue-600 text-white" : "bg-gray-100 hover:bg-gray-200"}`,
            formAction: filterByOfficial,
            icon: "thumbs-up"
          },
          {
            title: "Public",
            className: `px-3 py-1 text-xs rounded-md ${filter === "public" ? "bg-blue-600 text-white" : "bg-gray-100 hover:bg-gray-200"}`,
            formAction: filterByPublic,
            icon: "database"
          },
          {
            title: "Not Public",
            className: `px-3 py-1 text-xs rounded-md ${filter === "not_public" ? "bg-blue-600 text-white" : "bg-gray-100 hover:bg-gray-200"}`,
            formAction: filterByNotPublic,
            icon: "database"
          },
          ...(search || filter ? [{
            title: "Clear All",
            className: "px-3 py-1 text-xs rounded-md bg-gray-100 hover:bg-gray-200",
            formAction: clearFilters,
            icon: "filter"
          }] : [])
        ]
      }
    ],
    columns: [
      {
        title: t("columns.name"),
        name: "nameDisplay",
      },
      {
        title: t("brief"),
        name: "briefDisplay",
      },
      {
        title: t("processinfo"),
        name: "processinfoDisplay",
      },
      {
        title: <Wrench className="h-4 w-4" />,
        name: "toolsDisplay",
      },
      {
        title: <ExternalLink className="h-4 w-4" />,
        name: "websiteDisplay",
      },
      {
        title: <Calendar className="h-4 w-4" />,
        name: "dateDisplay",
      },
      {
        title: <Star className="h-4 w-4" />,
        name: "recommendedDisplay",
      },
      {
        title: <ThumbsUp className="h-4 w-4" />,
        name: "officialDisplay",
      },
      {
        title: <Database className="h-4 w-4" />,
        name: "publicDisplay",
      },
      {
        title: <Clock className="h-4 w-4" />,
        name: "clicksDisplay",
      },
      {
        title: <Tag className="h-4 w-4" />,
        name: "tagsDisplay",
      },
      {
        title: t("columns.actions"),
        name: "actionsDisplay",
      },
      {
        title: "Delete",
        name: "actionDelete"
      }
    ] as CustomColumn[],
    data: processedData,
    empty_message: t("empty"),
  };

  return <TableSlot {...table} />;
} 
