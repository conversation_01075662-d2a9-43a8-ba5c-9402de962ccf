
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Refresh<PERSON>w, CheckCircle, XCircle, Info } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { getPendingEmails } from "@/models/emailNotification";
import { processPendingEmails } from "@/lib/emailService";
import ProcessEmailsForm from "@/components/admin/emails/ProcessEmailsForm";

export const dynamic = 'force-dynamic';

export default async function ProcessEmailsPage() {
  const t = await getTranslations("admin.emails.process");
  
  // Get count of pending emails
  const { data: pendingEmails, error } = await getPendingEmails(50);
  const pendingCount = pendingEmails?.length || 0;
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/emails">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t("title")}</h1>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Status Card */}
        <Card>
          <CardHeader>
            <CardTitle>{t("status.title")}</CardTitle>
            <CardDescription>{t("status.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            {error ? (
              <div className="flex items-center gap-2 text-red-500">
                <XCircle className="h-5 w-5" />
                <p>{t("status.error")}</p>
              </div>
            ) : pendingCount > 0 ? (
              <div className="flex items-center gap-2 text-amber-500">
                <Info className="h-5 w-5" />
                <p>{t("status.pending", { count: pendingCount })}</p>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-green-500">
                <CheckCircle className="h-5 w-5" />
                <p>{t("status.no_pending")}</p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="outline" asChild>
              <Link href="/admin/emails/logs?status=pending">
                {t("status.view_pending")}
              </Link>
            </Button>
          </CardFooter>
        </Card>
        
        {/* Process Emails Card */}
        <Card>
          <CardHeader>
            <CardTitle>{t("process.title")}</CardTitle>
            <CardDescription>{t("process.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <ProcessEmailsForm />
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 