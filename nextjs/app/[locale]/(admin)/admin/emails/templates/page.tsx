
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, ArrowLeft, FileText, Eye, Edit, Power, PowerOff, Trash } from "lucide-react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { getEmailTemplates } from "@/models/emailNotification";
import { EmailTemplate } from "@/models/emailNotification";

export const dynamic = 'force-dynamic';

export default async function EmailTemplatesPage() {
  const t = await getTranslations("admin.emails.templates");
  
  // Fetch email templates from the API
  const { data: rawTemplates = [], error } = await getEmailTemplates();
  const templates = rawTemplates || []; // Ensure templates is always an array

  const templateTypes = [
    { id: 'item_submitted', label: t("types.item_submitted") },
    { id: 'item_published', label: t("types.item_published") },
    { id: 'item_rejected', label: t("types.item_rejected") },
  ];

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/emails">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t("title")}</h1>
        </div>
        <Button asChild>
          <Link href="/admin/emails/templates/create">
            <PlusCircle className="h-4 w-4 mr-2" />
            {t("create_button")}
          </Link>
        </Button>
      </div>

      {error ? (
        <Card className="p-6">
          <p className="text-red-500">{t("error_loading")}</p>
          <pre className="mt-2 p-2 bg-gray-100 rounded-md text-sm overflow-auto">{JSON.stringify(error, null, 2)}</pre>
        </Card>
      ) : (
        <Tabs defaultValue="all">
          <TabsList className="mb-6">
            <TabsTrigger value="all">{t("tabs.all")}</TabsTrigger>
            {templateTypes.map((type) => (
              <TabsTrigger key={type.id} value={type.id}>
                {type.label}
              </TabsTrigger>
            ))}
            <TabsTrigger value="active">{t("tabs.active")}</TabsTrigger>
            <TabsTrigger value="inactive">{t("tabs.inactive")}</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <TemplatesTable templates={templates} t={t} />
          </TabsContent>

          {templateTypes.map((type) => (
            <TabsContent key={type.id} value={type.id} className="space-y-4">
              <TemplatesTable 
                templates={templates.filter(template => template.event_type === type.id)} 
                t={t} 
              />
            </TabsContent>
          ))}

          <TabsContent value="active" className="space-y-4">
            <TemplatesTable 
              templates={templates.filter(template => template.is_active)} 
              t={t} 
            />
          </TabsContent>

          <TabsContent value="inactive" className="space-y-4">
            <TemplatesTable 
              templates={templates.filter(template => !template.is_active)} 
              t={t} 
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

function TemplatesTable({ templates, t }: { templates: EmailTemplate[], t: any }) {
  return templates.length === 0 ? (
    <div className="text-center py-10 text-muted-foreground">
      {t("no_templates")}
    </div>
  ) : (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("table.id")}</TableHead>
            <TableHead>{t("table.name")}</TableHead>
            <TableHead>{t("table.event_type")}</TableHead>
            <TableHead>{t("table.subject")}</TableHead>
            <TableHead>{t("table.status")}</TableHead>
            <TableHead>{t("table.updated_at")}</TableHead>
            <TableHead className="text-right">{t("table.actions")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {templates.map((template) => (
            <TableRow key={template.id}>
              <TableCell className="font-mono">{template.id}</TableCell>
              <TableCell className="font-medium">{template.name}</TableCell>
              <TableCell>
                <Badge variant={getEventTypeBadgeVariant(template.event_type)}>
                  {t(`types.${template.event_type}`)}
                </Badge>
              </TableCell>
              <TableCell className="max-w-[200px] truncate">{template.subject}</TableCell>
              <TableCell>
                {template.is_active ? (
                  <Badge variant="default" className="bg-green-600 hover:bg-green-700">{t("status.active")}</Badge>
                ) : (
                  <Badge variant="secondary">{t("status.inactive")}</Badge>
                )}
              </TableCell>
              <TableCell>{format(new Date(template.updated_at), 'yyyy-MM-dd HH:mm')}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="icon" asChild>
                    <Link href={`/admin/emails/templates/${template.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button variant="outline" size="icon" asChild>
                    <Link href={`/admin/emails/templates/${template.id}/edit`}>
                      <Edit className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button 
                    variant={template.is_active ? "outline" : "default"} 
                    size="icon"
                    asChild
                  >
                    <Link href={`/admin/emails/templates/${template.id}/toggle`}>
                      {template.is_active ? (
                        <PowerOff className="h-4 w-4" />
                      ) : (
                        <Power className="h-4 w-4" />
                      )}
                    </Link>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

function getEventTypeBadgeVariant(eventType: string) {
  switch (eventType) {
    case 'item_submitted':
      return 'default';
    case 'item_published':
      return 'default';
    case 'item_rejected':
      return 'destructive';
    default:
      return 'secondary';
  }
} 