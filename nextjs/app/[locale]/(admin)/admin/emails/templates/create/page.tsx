
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { Card } from "@/components/ui/card";
import TemplateEditForm from "@/components/admin/emails/TemplateEditForm";

export const dynamic = 'force-dynamic';

export default async function CreateTemplatePage() {
  const t = await getTranslations("admin.emails.templates.create");
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/emails/templates">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t("title")}</h1>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <Card className="p-6">
          <TemplateEditForm />
        </Card>
        
        <Card className="p-6">
          <h2 className="text-lg font-medium mb-4">{t("variable_help.title")}</h2>
          <p className="mb-4">{t("variable_help.description")}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-md font-medium mb-2">{t("variable_help.item_variables")}</h3>
              <ul className="space-y-1 text-sm">
                <li className="font-mono">{'{{Item_NAME}}'} - {t("variable_help.item_name")}</li>
                <li className="font-mono">{'{{Item_AUTHOR}}'} - {t("variable_help.item_author")}</li>
                <li className="font-mono">{'{{Item_URL}}'} - {t("variable_help.item_url")}</li>
                <li className="font-mono">{'{{Item_LINK}}'} - {t("variable_help.item_link")}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-md font-medium mb-2">{t("variable_help.recipient_variables")}</h3>
              <ul className="space-y-1 text-sm">
                <li className="font-mono">{'{{RECIPIENT_NAME}}'} - {t("variable_help.recipient_name")}</li>
                <li className="font-mono">{'{{RECIPIENT_EMAIL}}'} - {t("variable_help.recipient_email")}</li>
                <li className="font-mono">{'{{SITE_URL}}'} - {t("variable_help.site_url")}</li>
                <li className="font-mono">{'{{DATE}}'} - {t("variable_help.date")}</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 