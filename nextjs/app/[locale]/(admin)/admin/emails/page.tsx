
import { getTranslations } from "next-intl/server";
import { VariantProps } from "class-variance-authority";
import Link from "next/link";
import { Button, ButtonProps } from "@/components/ui/button";
import { Mail, FileText, ClipboardList, SendHorizonal } from "lucide-react";
import { Card, CardDescription, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card";

export const dynamic = 'force-dynamic';

export default async function EmailsPage() {
  const t = await getTranslations("admin.emails");

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{t("title")}</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Templates card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t("templates.title")}
            </CardTitle>
            <CardDescription>{t("templates.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{t("templates.content")}</p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href="/admin/emails/templates">{t("templates.view_button")}</Link>
            </Button>
          </CardFooter>
        </Card>
        
        {/* Logs card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ClipboardList className="h-5 w-5" />
              {t("logs.title")}
            </CardTitle>
            <CardDescription>{t("logs.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{t("logs.content")}</p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href="/admin/emails/logs">{t("logs.view_button")}</Link>
            </Button>
          </CardFooter>
        </Card>
        
        {/* Process queue card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SendHorizonal className="h-5 w-5" />
              {t("process.title")}
            </CardTitle>
            <CardDescription>{t("process.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{t("process.content")}</p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href="/admin/emails/process">{t("process.action_button")}</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
} 