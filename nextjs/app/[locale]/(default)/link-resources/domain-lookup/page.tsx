
import DomainLookup from "@/components/blocks/domain-lookup";
import { getTranslations } from "next-intl/server";

export default async function DomainLookupPage() {
  const t = await getTranslations();

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">Domain Lookup</h1>
        <p className="text-muted-foreground">Check domain registration status, expiration dates, and registrar information</p>
      </div>
      <DomainLookup />
    </div>
  );
}