
import { Suspense } from 'react';
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import { PublicLinkResourcesList } from '@/components/blocks/link-resources/public-link-resources-list';

type Props = {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export async function generateMetadata({ params, searchParams }: Props): Promise<Metadata> {
  const { locale } = await params;
  const searchParamsResolved = await searchParams;
  const t = await getTranslations({ locale, namespace: 'linkResources' });
  
  const page = searchParamsResolved.page ? parseInt(searchParamsResolved.page as string) : 1;
  const category = searchParamsResolved.category as string;
  const isPaid = searchParamsResolved.paid;
  
  let title = t('seo.title');
  let description = t('seo.description');
  
  // Dynamic title and description based on filters
  if (category) {
    title = `${t(`categories.${category}`)} - ${t('seo.title')}`;
    description = `${t('seo.categoryDescription', { category: t(`categories.${category}`) })}`;
  }
  
  if (isPaid === 'true') {
    title = `${t('paid')} ${title}`;
    description = `${t('seo.paidDescription')} ${description}`;
  } else if (isPaid === 'false') {
    title = `${t('free')} ${title}`;
    description = `${t('seo.freeDescription')} ${description}`;
  }
  
  if (page > 1) {
    title = `${title} - ${t('seo.page')} ${page}`;
  }

  // Ensure description is within valid length (150-160 characters recommended)
  if (description.length > 160) {
    description = description.substring(0, 157) + '...';
  }

  // Generate canonical URL
  const baseUrl = 'https://mybacklinks.app';
  let canonicalUrl = `${baseUrl}/${locale}/link-resources`;
  
  // Add query parameters to canonical URL if they exist
  const urlParams = new URLSearchParams();
  if (category) urlParams.append('category', category);
  if (isPaid !== undefined) urlParams.append('paid', Array.isArray(isPaid) ? isPaid[0] : isPaid);
  if (page > 1) urlParams.append('page', page.toString());
  
  if (urlParams.toString()) {
    canonicalUrl += `?${urlParams.toString()}`;
  }

  return {
    title,
    description,
    keywords: t('seo.keywords'),
    authors: [{ name: 'MyBackLinks', url: 'https://mybacklinks.app' }],
    creator: 'MyBackLinks',
    publisher: 'MyBackLinks',
    applicationName: 'MyBackLinks',
    generator: 'Next.js',
    referrer: 'origin-when-cross-origin',
    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title,
      description,
      type: 'website',
      locale: locale,
      siteName: 'MyBackLinks',
      url: canonicalUrl,
      images: [
        {
          url: `${baseUrl}/og-image.png`,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      site: '@mybacklinks.app',
      creator: '@hhkkmon',
      images: [`${baseUrl}/og-image.png`],
    },
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `${baseUrl}/en/link-resources`,
        'zh': `${baseUrl}/zh/link-resources`,
        'x-default': `${baseUrl}/en/link-resources`
      }
    },
    other: {
      'DC.title': title,
      'DC.description': description,
      'DC.language': locale,
      'DC.creator': 'MyBackLinks',
      'DC.publisher': 'MyBackLinks',
      'DC.type': 'Dataset',
      'DC.format': 'text/html',
      'DC.rights': 'Creative Commons Attribution 4.0',
      'citation_title': title,
      'citation_abstract_html_url': canonicalUrl,
      'citation_language': locale,
      'citation_keywords': t('seo.keywords'),
      'article:author': 'MyBackLinks',
      'article:publisher': 'MyBackLinks',
      'article:section': 'SEO Tools',
      'article:tag': 'link building, SEO, backlinks, directory',
      'og:updated_time': new Date().toISOString(),
    },
    verification: {
      google: 'your-google-site-verification',
    },
  };
}

export default async function LinkResourcesPage({ params, searchParams }: Props) {
  const { locale } = await params;
  const searchParamsResolved = await searchParams;
  const t = await getTranslations({ locale, namespace: 'linkResources' });

  const category = searchParamsResolved.category as string;
  const isPaid = searchParamsResolved.paid === 'true' ? true : searchParamsResolved.paid === 'false' ? false : undefined;
  const sort = searchParamsResolved.sort as string || 'dr_desc';
  const search = searchParamsResolved.search as string;
  const page = searchParamsResolved.page ? parseInt(searchParamsResolved.page as string) : 1;

  // Enhanced JSON-LD structured data for AI search engines
  const jsonLd = {
    '@context': 'https://schema.org',
    '@graph': [
      {
        '@type': 'WebPage',
        '@id': `https://mybacklinks.app/${locale}/link-resources`,
        name: t('seo.title'),
        description: t('seo.description'),
        url: `https://mybacklinks.app/${locale}/link-resources`,
        inLanguage: locale,
        isPartOf: {
          '@id': 'https://mybacklinks.app/#website'
        },
        about: {
          '@id': 'https://mybacklinks.app/#organization'
        },
        dateModified: new Date().toISOString(),
        datePublished: '2025-01-01T00:00:00Z',
        keywords: t('seo.keywords'),
        mainEntity: {
          '@id': `https://mybacklinks.app/${locale}/link-resources#directory`
        },
        breadcrumb: {
          '@id': `https://mybacklinks.app/${locale}/link-resources#breadcrumb`
        }
      },
      {
        '@type': 'WebSite',
        '@id': 'https://mybacklinks.app/#website',
        name: 'MyBackLinks',
        description: 'Comprehensive link building and SEO analytics platform',
        url: 'https://mybacklinks.app',
        inLanguage: ['en', 'zh', 'ja', 'ko', 'es', 'fr', 'de', 'ru'],
        publisher: {
          '@id': 'https://mybacklinks.app/#organization'
        },
        potentialAction: {
          '@type': 'SearchAction',
          target: `https://mybacklinks.app/${locale}/link-resources?search={search_term_string}`,
          'query-input': 'required name=search_term_string'
        }
      },
      {
        '@type': 'Organization',
        '@id': 'https://mybacklinks.app/#organization',
        name: 'MyBackLinks',
        description: 'Leading provider of link building resources and SEO analytics tools',
        url: 'https://mybacklinks.app',
        logo: {
          '@type': 'ImageObject',
          url: 'https://mybacklinks.app/logo.png',
          width: 600,
          height: 60
        },
        sameAs: [
          'https://twitter.com/hhkkmon',
        ],
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer service',
          email: '<EMAIL>'
        }
      },
      {
        '@type': 'BreadcrumbList',
        '@id': `https://mybacklinks.app/${locale}/link-resources#breadcrumb`,
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: t('common.home'),
            item: `https://mybacklinks.app/${locale}`
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: t('title'),
            item: `https://mybacklinks.app/${locale}/link-resources`
          }
        ]
      },
      {
        '@type': 'Dataset',
        '@id': `https://mybacklinks.app/${locale}/link-resources#directory`,
        name: t('title'),
        description: t('seo.description'),
        creator: {
          '@id': 'https://mybacklinks.app/#organization'
        },
        publisher: {
          '@id': 'https://mybacklinks.app/#organization'
        },
        dateModified: new Date().toISOString(),
        license: 'https://creativecommons.org/licenses/by/4.0/',
        keywords: t('seo.keywords'),
        distribution: {
          '@type': 'DataDownload',
          contentUrl: `https://mybacklinks.app/api/public-link-resources`,
          encodingFormat: 'application/json'
        },
        measurementTechnique: 'Manual verification and community feedback',
        variableMeasured: [
          {
            '@type': 'PropertyValue',
            name: 'Domain Rating',
            description: 'Ahrefs domain rating score from 0-100',
            minValue: 20,
            maxValue: 95
          },
          {
            '@type': 'PropertyValue', 
            name: 'Success Rate',
            description: 'Percentage of successful submissions',
            minValue: 0,
            maxValue: 100,
            unitText: 'PERCENT'
          },
          {
            '@type': 'PropertyValue',
            name: 'Traffic Volume',
            description: 'Monthly organic traffic volume',
            minValue: 1000,
            maxValue: 10000000,
            unitText: 'visits per month'
          }
        ],
        size: '50+ verified platforms',
        temporalCoverage: '2024/..',
        spatialCoverage: 'Worldwide'
      },
      {
        '@type': 'Service',
        '@id': `https://mybacklinks.app/#service`,
        name: 'Link Building Resource Directory',
        description: 'Comprehensive database of verified link building opportunities',
        provider: {
          '@id': 'https://mybacklinks.app/#organization'
        },
        areaServed: 'Worldwide',
        availableLanguage: ['en', 'zh', 'ja', 'ko', 'es', 'fr', 'de', 'ru'],
        serviceType: 'SEO Tools and Resources',
        audience: {
          '@type': 'BusinessAudience',
          audienceType: ['SEO Professionals', 'Digital Marketing Agencies', 'Content Marketers', 'Small Businesses']
        },
        offers: [
          {
            '@type': 'Offer',
            name: 'Free Link Building Resources',
            description: 'Access to free submission opportunities',
            price: '0',
            priceCurrency: 'USD'
          },
          {
            '@type': 'Offer', 
            name: 'Premium Link Building Database',
            description: 'Complete access to verified high-authority platforms',
            price: '99',
            priceCurrency: 'USD',
            priceSpecification: {
              '@type': 'UnitPriceSpecification',
              price: '99',
              priceCurrency: 'USD',
              billingIncrement: 'P1M'
            }
          }
        ]
      }
    ]
  };

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className="md:max-w-10xl mx-auto px-4 py-8">
        {/* Breadcrumb Navigation */}
        <nav aria-label="Breadcrumb" className="mb-6">
          <ol className="flex items-center space-x-2 text-sm text-muted-foreground">
            <li>
              <a href={`/${locale}`} className="hover:text-primary transition-colors">
                {t('common.home')}
              </a>
            </li>
            <li>/</li>
            <li aria-current="page" className="text-foreground font-medium">
              {t('title')}
            </li>
          </ol>
        </nav>

        {/* Main Header with enhanced semantic markup */}
        <header className="mb-8 text-center" itemScope itemType="https://schema.org/Dataset">
          <h1 className="text-4xl font-bold mb-4" itemProp="name">
            {t('title')}
          </h1>
          <p className="text-muted-foreground text-lg max-w-3xl mx-auto" itemProp="description">
            {t('description')}
          </p>
          
          {/* Hidden metadata for AI crawlers */}
          <div style={{display: 'none'}} aria-hidden="true">
            <span itemProp="keywords">{t('seo.keywords')}</span>
            <span itemProp="creator" itemScope itemType="https://schema.org/Organization">
              <span itemProp="name">MyBackLinks</span>
            </span>
            <span itemProp="publisher" itemScope itemType="https://schema.org/Organization">
              <span itemProp="name">MyBackLinks</span>
            </span>
            <time itemProp="dateModified" dateTime={new Date().toISOString()}>
              {new Date().toLocaleDateString()}
            </time>
          </div>
        </header>

        {/* Statistics Section */}
        <section className="mb-12 bg-muted/30 rounded-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-primary mb-2">50+</div>
              <div className="text-sm text-muted-foreground">{t('stats.totalResources')}</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">10+</div>
              <div className="text-sm text-muted-foreground">{t('stats.categories')}</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary mb-2">85%</div>
              <div className="text-sm text-muted-foreground">{t('stats.highAuthority')}</div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8">{t('features.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-6 bg-card rounded-lg border">
              <div className="text-2xl mb-3">🔍</div>
              <h3 className="font-semibold mb-2">{t('features.highQuality')}</h3>
              <p className="text-sm text-muted-foreground">{t('features.highQualityDesc')}</p>
            </div>
            <div className="text-center p-6 bg-card rounded-lg border">
              <div className="text-2xl mb-3">✅</div>
              <h3 className="font-semibold mb-2">{t('features.verified')}</h3>
              <p className="text-sm text-muted-foreground">{t('features.verifiedDesc')}</p>
            </div>
            <div className="text-center p-6 bg-card rounded-lg border">
              <div className="text-2xl mb-3">🛡️</div>
              <h3 className="font-semibold mb-2">{t('features.qualityAssured')}</h3>
              <p className="text-sm text-muted-foreground">{t('features.qualityAssuredDesc')}</p>
            </div>
            <div className="text-center p-6 bg-card rounded-lg border">
              <div className="text-2xl mb-3">🔄</div>
              <h3 className="font-semibold mb-2">{t('features.updated')}</h3>
              <p className="text-sm text-muted-foreground">{t('features.updatedDesc')}</p>
            </div>
          </div>
        </section>

        {/* Main Content - Link Resources List */}
        <section>
          <Suspense fallback={<div className="text-center py-8">{t('loading')}</div>}>
            <PublicLinkResourcesList
              category={category}
              isPaid={isPaid}
              sort={sort}
              search={search}
              locale={locale}
              compact={true}
              page={page}
            />
          </Suspense>
        </section>

        {/* FAQ Section - Removed duplicate FAQPage itemType to avoid schema duplication */}
        <section className="mt-16" id="link-building-faq">
          <h2 className="text-3xl font-bold mb-8 text-center">{t('faq.title')}</h2>
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-semibold mb-3">{t('faq.q1')}</h3>
              <p className="text-muted-foreground">{t('faq.a1')}</p>
            </div>
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-semibold mb-3">{t('faq.q2')}</h3>
              <p className="text-muted-foreground">{t('faq.a2')}</p>
            </div>
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-semibold mb-3">{t('faq.q3')}</h3>
              <p className="text-muted-foreground">{t('faq.a3')}</p>
            </div>
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-semibold mb-3">{t('faq.q4')}</h3>
              <p className="text-muted-foreground">{t('faq.a4')}</p>
            </div>
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-semibold mb-3">{t('faq.q5')}</h3>
              <p className="text-muted-foreground">{t('faq.a5')}</p>
            </div>
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-semibold mb-3">{t('faq.q6')}</h3>
              <p className="text-muted-foreground">{t('faq.a6')}</p>
            </div>
          </div>
        </section>

        {/* Additional SEO Content */}
        <section className="mt-16 bg-muted/30 rounded-lg p-8">
          <h2 className="text-2xl font-semibold mb-6">{t('seo.additionalTitle')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold mb-3">{t('seo.howItWorks')}</h3>
              <p className="text-muted-foreground mb-4">{t('seo.howItWorksDesc')}</p>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• {t('seo.step1')}</li>
                <li>• {t('seo.step2')}</li>
                <li>• {t('seo.step3')}</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-3">{t('seo.benefits')}</h3>
              <p className="text-muted-foreground mb-4">{t('seo.benefitsDesc')}</p>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• {t('seo.benefit1')}</li>
                <li>• {t('seo.benefit2')}</li>
                <li>• {t('seo.benefit3')}</li>
                <li>• {t('seo.benefit4')}</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}