
import { getUserInfo, getUserInviteLink } from "@/services/user";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { getInvitationCredits } from "@/models/settings";
import { UsersIcon } from "lucide-react";
import InviteActions from "@/components/invite/invite-actions";

export default async function InvitePage() {
  const t = await getTranslations();
  const user = await getUserInfo();
  const inviteLink = await getUserInviteLink();
  const invitationCredits = await getInvitationCredits();
  
  if (!user) return null;
  
  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-8">
        {t('invitation.title')}
      </h1>
      
      <div className="grid md:grid-cols-3 gap-6 mb-10">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">{t('invitation.your_code')}</CardTitle>
            <CardDescription>{t('invitation.share_code')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user.invite_code}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">{t('invitation.credits_per_invite')}</CardTitle>
            <CardDescription>{t('invitation.earn_credits')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invitationCredits}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">{t('invitation.invites_count')}</CardTitle>
            <CardDescription>{t('invitation.successful_invites')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user.invites_count || 0}</div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{t('invitation.share_invite_link')}</CardTitle>
          <CardDescription>{t('invitation.share_description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <InviteActions inviteLink={inviteLink || ''} />
        </CardContent>
        <CardFooter className="bg-muted/50 border-t px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <UsersIcon className="h-4 w-4" />
            <span>
              {t('invitation.how_it_works')}
            </span>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
} 