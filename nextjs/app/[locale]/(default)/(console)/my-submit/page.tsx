
import Empty from "@/components/blocks/empty";
import TableSlot from "@/components/console/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getTranslations } from "next-intl/server";

// Import the correct function and user service
import { getUserSubmissionsByEmail } from "@/models/submission";
import { getUserInfo } from "@/services/user"; // Need user email
import { Submission } from "@/types/items"; // Import the type
import { formatDate } from "@/lib/utils";

export default async function MySubmissionsPage() {
  const t = await getTranslations();

  // Fetch user info to get email
  const userInfo = await getUserInfo();
  console.log("userInfo", userInfo);
  if (!userInfo || !userInfo.email) {
    return <Empty message={t("user.no_auth_or_email")} />; // More specific message
  }
  const userEmail = userInfo.email;

  // Fetch user's submissions using email
  const { data, error, count } = await getUserSubmissionsByEmail(userEmail);

  if (error) {
    console.error("Error fetching user submissions:", error);
    return <Empty message={t("errors.failed_to_fetch_submissions")} />; // Error message
  }

  const table: TableSlotType = {
    title: t("my_submit.title"),
    tip: {
      title: t("my_submit.tip"),
    },
    toolbar: {
      items: [
        {
          title: t("my_submit.submit_new_item"),
          url: "/submit",
          icon: "RiAddLine",
        },
      ],
    },
    columns: [
      {
        title: t("my_submit.table.name"),
        name: "name",
      },
      {
        title: t("my_submit.table.website_url"),
        name: "website_url",
        type: "element", // Render as a link
        callback: (item: Submission) => (
          <a
            href={item.website_url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline truncate"
            title={item.website_url} // Show full URL on hover
          >
            {item.website_url.length > 40 ? item.website_url.slice(0, 40) + "..." : item.website_url}
          </a>
        ),
      },
      {
        title: t("my_submit.table.status"),
        name: "status",
        // Optional: Add styling based on status
        callback: (item: Submission) => {
            let color = "text-gray-600"; // Default for pending
            if (item.status === 'approved') color = "text-green-600";
            if (item.status === 'rejected') color = "text-red-600";
            return <span className={color}>{item.status}</span>;
        }
      },
      {
        title: t("my_submit.table.submitted_at"), // Changed from created_at for clarity
        name: "created_at",
        callback: (item: Submission) => formatDate(item.created_at),
      },
      {
        title: t("my_submit.table.processed_at"),
        name: "processed_at",
        callback: (item: Submission) => formatDate(item.processed_at),
      },
      {
        title: t("my_submit.table.approved_at"),
        name: "approved_at",
        callback: (item: Submission) => formatDate(item.approved_at),
      },
      {
        title: t("my_submit.table.rejected_at"),
        name: "rejected_at",
        callback: (item: Submission) => formatDate(item.rejected_at),
      },
    ],
    data,
    empty_message: t("my_submit.no_submissions"), // Updated translation key
  };

  return <TableSlot {...table} />;
}
