"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { deleteUserItem } from "@/models/userItems";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface DeleteFavoriteButtonProps {
  uuid: string;
  user_uuid: string;
}

export default function DeleteFavoriteButton({ uuid, user_uuid }: DeleteFavoriteButtonProps) {
  const t = useTranslations();
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    if (confirm(t("favorites.confirm_delete"))) {
      try {
        setIsDeleting(true);
        const { error } = await deleteUserItem(user_uuid, uuid);
        
        if (error) {
          console.error("Error deleting favorite:", error);
          toast.error(t("common.error_occurred"));
          return;
        }
        
        toast.success(t("favorites.deleted"));
        router.refresh(); // Refresh the page to update the list
      } catch (error) {
        console.error("Error:", error);
        toast.error(t("common.error_occurred"));
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleDelete}
      disabled={isDeleting}
      className="text-red-500 hover:text-red-700 hover:bg-red-50"
    >
      <Trash2 className="w-4 h-4 mr-2" />
      {t("common.delete")}
    </Button>
  );
} 