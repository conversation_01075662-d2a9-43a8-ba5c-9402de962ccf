
import { Metadata } from "next";
import { getUserItems } from "@/models/userItems";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, Trash2 } from "lucide-react";
import Link from "next/link";
import { getTranslations } from "next-intl/server";
import DeleteFavoriteButton from "./DeleteFavoriteButton";
import { getUserUuid } from "@/services/user";

export const metadata: Metadata = {
  title: "My Favorite Items",
  description: "Manage your favorite Items",
};

export default async function MyFavoritesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();
  const user_uuid = await getUserUuid();
  const { data: userItems, error } = await getUserItems(user_uuid);

  if (error) {
    return (
      <div className="container py-10">
        <h1 className="text-2xl font-bold mb-6">{t("favorites.title")}</h1>
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded">
          <p>{t("common.error_occurred")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">{t("favorites.title")}</h1>
      </div>

      {userItems && userItems.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {userItems.map((item) => (
            <Card key={item.uuid} className="flex flex-col h-full">
              <CardHeader>
                <CardTitle className="truncate">{item.item_name}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {item.item_brief}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-sm text-muted-foreground">
                  {item.description || t("favorites.no_description")}
                </p>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button asChild variant="outline" size="sm">
                  <Link href={`/${locale}/g/${item.item_uuid}`}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    {t("common.view")}
                  </Link>
                </Button>
                <DeleteFavoriteButton uuid={item.uuid} user_uuid={user_uuid} />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="bg-muted rounded-md p-10 text-center">
          <h3 className="text-lg font-semibold mb-2">{t("favorites.empty_title")}</h3>
          <p className="text-muted-foreground mb-6">
            {t("favorites.empty_description")}
          </p>
          <Button asChild>
            <Link href={`/${locale}`}>{t("favorites.browse_items")}</Link>
          </Button>
        </div>
      )}
    </div>
  );
} 