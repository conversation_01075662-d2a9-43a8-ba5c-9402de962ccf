
import { getTranslations } from "next-intl/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getUserAiTasksByUUID } from "@/models/aiTask";
import { formatDistance } from "date-fns";
import { AiTask } from "@/types/aiTask";
import Link from "next/link";
import { Eye, FileText, Image } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getUserUuid } from "@/services/user";
export const metadata = {
  title: "My AI Tasks",
};

export default async function MyTasksPage() {
  const t = await getTranslations("editor");
  const user_uuid = await getUserUuid();
  const { data: tasks, error } = await getUserAiTasksByUUID(user_uuid);

  // Function to render status badge with appropriate color
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline">{t("task_pending")}</Badge>;
      case "PROCESSING":
        return <Badge variant="secondary">{t("task_processing")}</Badge>;
      case "SUCCEED":
        return <Badge variant="outline">{t("task_succeeded")}</Badge>;
      case "FAILED":
        return <Badge variant="destructive">{t("task_failed_status")}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Function to get formatted date
  const getFormattedDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return formatDistance(date, new Date(), { addSuffix: true });
  };

  return (
    <div className="container py-6">
      <Card>
        <CardHeader>
          <CardTitle>{t("my_tasks")}</CardTitle>
          <CardDescription>{t("my_tasks_description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8 text-destructive">
              <p>{error.message}</p>
            </div>
          ) : tasks && tasks.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("task_product")}</TableHead>
                  <TableHead>{t("task_status")}</TableHead>
                  <TableHead>{t("task_date")}</TableHead>
                  <TableHead>{t("task_credit_cost")}</TableHead>
                  <TableHead>{t("task_result")}</TableHead>
                  <TableHead className="text-right">{t("task_view")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tasks.map((task: AiTask) => (
                  <TableRow key={task.order_no}>
                    <TableCell className="font-medium">{task.product_name}</TableCell>
                    <TableCell>{renderStatusBadge(task.orderstatus)}</TableCell>
                    <TableCell>{getFormattedDate(task.create_time.toString())}</TableCell>
                    <TableCell>{task.credit_cost}</TableCell>
                    <TableCell>
                      {task.output_text ? (
                        <FileText className="h-4 w-4 text-muted-foreground" />
                      ) : task.output_image_path ? (
                        <Image className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {task.orderstatus === "SUCCEED" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          asChild
                        >
                          <Link href={`/my-tasks/${task.order_no}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            {t("view_task_result")}
                          </Link>
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">{t("no_tasks")}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 