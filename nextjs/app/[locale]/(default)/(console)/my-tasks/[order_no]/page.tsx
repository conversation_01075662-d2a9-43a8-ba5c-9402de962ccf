
import { getTranslations } from "next-intl/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getAiTaskByOrderNo } from "@/models/aiTask";
import { notFound } from "next/navigation";
import { format } from "date-fns";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { formatDate } from "@/lib/utils";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ order_no: string }>;
}) {
  const { order_no } = await params;
  return {
    title: `Task ${order_no}`,
  };
}

export default async function TaskDetailPage({
  params,
}: {
  params: Promise<{ order_no: string }>;
}) {
  const { order_no } = await params;
  const t = await getTranslations("editor");
  const { data: task, error } = await getAiTaskByOrderNo(order_no);

  if (error || !task) {
    notFound();
  }

  return (
    <div className="container py-6">
      <div className="mb-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/my-tasks">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("my_tasks")}
          </Link>
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{task.product_name}</CardTitle>
          <CardDescription>
            {t("task_date")}: {formatDate(task.create_time.toString())}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-1">{t("task_status")}</h4>
              <p>{task.orderstatus}</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">{t("task_credit_cost")}</h4>
              <p>{task.credit_cost}</p>
            </div>
            {task.update_time && (
              <div>
                <h4 className="font-medium mb-1">{t("completed_at")}</h4>
                <p>{formatDate(task.update_time.toString())}</p>
              </div>
            )}
            {task.cost_time > 0 && (
              <div>
                <h4 className="font-medium mb-1">{t("processing_time")}</h4>
                <p>{task.cost_time}ms</p>
              </div>
            )}
            {task.fail_reason && (
              <div className="col-span-2">
                <h4 className="font-medium mb-1">{t("error_reason")}</h4>
                <p className="text-destructive">{task.fail_reason}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {task.orderstatus === "SUCCEED" && (
        <Card>
          <CardHeader>
            <CardTitle>{t("results_title")}</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue={task.output_text ? "text" : task.output_image_path ? "image" : "json"}>
              <TabsList className="mb-4">
                <TabsTrigger value="text" disabled={!task.output_text}>
                  {t("tab_text")}
                </TabsTrigger>
                <TabsTrigger value="image" disabled={!task.output_image_path}>
                  {t("tab_image")}
                </TabsTrigger>
                <TabsTrigger value="json" disabled={!task.output_options}>
                  {t("tab_json")}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="text">
                {task.output_text ? (
                  <Textarea
                    value={task.output_text}
                    readOnly
                    className="min-h-[300px]"
                  />
                ) : (
                  <p className="text-center py-8 text-muted-foreground">
                    {t("no_text_result")}
                  </p>
                )}
              </TabsContent>

              <TabsContent value="image">
                {task.output_image_path ? (
                  <div className="flex justify-center">
                    <img
                      src={task.output_image_path}
                      alt={t("result_image")}
                      className="max-h-[500px] object-contain border p-2 rounded"
                    />
                  </div>
                ) : (
                  <p className="text-center py-8 text-muted-foreground">
                    {t("no_image_result")}
                  </p>
                )}
              </TabsContent>

              <TabsContent value="json">
                {task.output_options ? (
                  <pre className="p-4 bg-muted rounded-md overflow-auto max-h-[300px]">
                    {JSON.stringify(task.output_options, null, 2)}
                  </pre>
                ) : (
                  <p className="text-center py-8 text-muted-foreground">
                    {t("no_json_result")}
                  </p>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              onClick={() => window.print()}
              className="ml-auto"
            >
              {t("print_result")}
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
} 