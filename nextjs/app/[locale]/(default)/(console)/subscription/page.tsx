import { getUserUuid } from "@/services/user";
import { getUserTierInfo } from "@/lib/tier-middleware";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import { getLandingPage } from "@/services/page";
import SubscriptionManagement from "@/components/console/subscription-management";
import Pricing from "@/components/blocks/pricing";

export default async function SubscriptionPage({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();
  const user_uuid = await getUserUuid();

  const callbackUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/subscription`;
  if (!user_uuid) {
    redirect(`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`);
  }

  // Get user's tier info and usage
  const tierInfo = await getUserTierInfo(user_uuid);
  
  // Get pricing data from landing page for free users
  const landingPage = await getLandingPage(locale);

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">{t("subscription.title")}</h1>
        <p className="text-muted-foreground">{t("subscription.description")}</p>
      </div>
      
      <SubscriptionManagement 
        tierInfo={tierInfo}
        translations={{
          current_plan: t("subscription.current_plan"),
          usage_summary: t("subscription.usage_summary"),
          upgrade_plan: t("subscription.upgrade_plan"),
          manage_billing: t("subscription.manage_billing"),
          features: t("subscription.features"),
          limits: t("subscription.limits"),
          projects: t("subscription.projects"),
          domains: t("subscription.domains"),
          link_resources: t("subscription.link_resources"),
          dr_queries: t("subscription.dr_queries"),
          traffic_updates: t("subscription.traffic_updates"),
          free_tier: t("subscription.free_tier"),
          professional_tier: t("subscription.professional_tier"),
          unlimited: t("subscription.unlimited"),
          per_month: t("subscription.per_month"),
          upgrade_now: t("subscription.upgrade_now"),
          contact_support: t("subscription.contact_support")
        }}
      />
      
      {/* Show pricing component for free tier users */}
      {tierInfo?.tier === 'free' && landingPage.pricing && (
        <div className="mt-12 pt-8 border-t">
          <Pricing pricing={landingPage.pricing} />
        </div>
      )}
    </div>
  );
}