
import { Metadata } from 'next';
import SubmitForm from '@/components/blocks/submit/SubmitForm';
import { getTranslations } from 'next-intl/server';
import { AffiliateSidebar } from '@/components/blocks/affiliate';
import { Suspense } from 'react';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'SubmitPage' });
  
  return {
    title: t('title'),
    description: t('description'),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/submit`,
    },
  };
}

export default async function SubmitPage({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'SubmitPage' });
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
        <a href={`/${locale}`} className="hover:text-foreground transition-colors">
          {t('home')}
        </a>
        <span>/</span>
        <span className="text-foreground font-medium">{t('submit')}</span>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <h1 className="text-3xl font-bold mb-2">{t('heading')}</h1>
          <p className="text-muted-foreground mb-8">{t('description')}</p>
          
          <SubmitForm />
          
          {/* Advertisement */}
          {process.env.NODE_ENV === 'production' && (
            <div className="mt-8" dangerouslySetInnerHTML={{
              __html: `
            <script type="text/javascript">
              atOptions = {
                'key' : 'a3c45315f84826c9b944c89d13982636',
                'format' : 'iframe',
                'height' : 90,
                'width' : 728,
                'params' : {}
              };
            </script>
            <script type="text/javascript" src="//www.highperformanceformat.com/a3c45315f84826c9b944c89d13982636/invoke.js"></script>
              `
            }} />
          )}
        </div>
        
        {/* Affiliate sidebar */}
        <div className="space-y-6">
          <Suspense fallback={<div className="h-48"></div>}>
            <AffiliateSidebar limit={3} />
          </Suspense>
        </div>
      </div>
    </div>
  );
} 
