
import { Metadata } from 'next';
import SearchResults from '@/components/blocks/search/SearchResults';
import { getTranslations } from 'next-intl/server';


export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'SearchPage' });
  
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
  let canonicalUrl = `${baseUrl}/${locale}/search`;
  
  return {
    title: t('title'),
    description: t('description'),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function SearchPage({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'SearchPage' });
  
  return (
    <div className="bg-background dark:bg-background">
      <div className="max-w-10xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4 pt-8">
          <a href={`/${locale}`} className="hover:text-foreground transition-colors">
            {t('home')}
          </a>
          <span>/</span>
          <span className="text-foreground font-medium">{t('search')}</span>
        </div>
        <SearchResults locale={locale} />
      </div>
    </div>
  );
} 
