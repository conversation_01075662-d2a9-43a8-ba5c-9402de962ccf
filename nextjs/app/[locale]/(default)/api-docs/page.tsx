import { ApiDocsClient } from "@/components/blocks/api-docs";


export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/api-docs`;

  return {
    title: "MCP Hub API Docs",
    description: "MCP Hub API Docs, integrate MCP Hub into your applications.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function ApiDocsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  return <ApiDocsClient />;
} 