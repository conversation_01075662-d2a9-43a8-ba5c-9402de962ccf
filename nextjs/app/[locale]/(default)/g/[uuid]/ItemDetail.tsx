// 服务器组件 - 包含所有SEO关键内容
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Items, ItemLocalization } from '@/types/items';
import { ItemTool, Tool, ToolParameter } from '@/types/ItemTools';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Clock,
  Calendar,
  RefreshCw,
  Star,
  GitFork,
  Eye,
  View,
  ExternalLink,
  Wrench,
  Info
} from 'lucide-react';
import ItemCard from '@/components/blocks/gallery/Card';
import { SimpleMarkdown } from '@/lib/simple-markdown';
import { AffiliateSidebar } from '@/components/blocks/affiliate';
import { Suspense } from 'react';
import { getTranslations } from 'next-intl/server';
import UsageList from './ToolsList';
import ToolsList from './UsageList';
import GithubCard from '@/components/blocks/cards/github-card';

// 这个组件为服务器组件，包含所有可搜索引擎索引的内容
interface ItemDetailServerProps {
    item: Items & { localization?: Partial<ItemLocalization> };
    recommendations: Items[];
    locale: string;
    formattedCreatedDate: string;
    formattedUpdatedDate: string;
    formattedRelativeDates: {
        [key: string]: string;
    };
    itemTool?: ItemTool | null;
}

export default async function ItemDetailServer({
    item,
    recommendations,
    locale,
    formattedCreatedDate,
    formattedUpdatedDate,
    formattedRelativeDates,
    itemTool
}: ItemDetailServerProps) {
    // 服务器端获取翻译
    const t = await getTranslations();
    const metadata = item.metadata || {};
    const brief = item.localization?.brief || item.brief || "";
    const processinfo = item.localization?.processinfo || "";

    return (
        <div className="container mx-auto px-4 py-8 max-w-10xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* --- Left Column --- */}
            <div className="lg:col-span-2 space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Avatar className="h-14 w-14">
                <AvatarImage src={item.user_avatar_url || undefined} alt={item.author_name || ''} />
                <AvatarFallback>{item.author_name?.charAt(0).toUpperCase() || 'A'}</AvatarFallback>
                </Avatar>
                <div>
                <h1 className="text-3xl font-bold">{item.name}</h1>
                <div className="text-sm text-muted-foreground">
                    By {item.author_name}
                    {item.website_url && (
                    <>
                        <span className="mx-2">·</span>
                        <a href={item.website_url + '?utm_source=' + (process.env.NEXT_PUBLIC_UTM_SOURCE)} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline flex items-center gap-1 inline-flex">
                        Visit Source <ExternalLink className="w-3 h-3" />
                        </a>
                    </>
                    )}
                </div>
                </div>
            </div>

            {/* Brief description */}
            {brief && (
                <div className="text-lg text-muted-foreground">
                {brief}
                </div>
            )}

            {/* Tags & Status */}
            <div className="flex flex-wrap items-center gap-2">
                {item.is_official && <Badge variant="default" className="bg-green-600 text-white">Official</Badge>}
                {item.tags && item.tags.map((tag: string) => (
                <Link
                    key={tag}
                    href={`/${locale}/?tags=${encodeURIComponent(tag)}`}
                    className="inline-block"
                >
                    <Badge variant="secondary" className="cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600">
                    {tag}
                    </Badge>
                </Link>
                ))}
            </div>

            {/* Dates & Clicks with icons */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>{formattedCreatedDate}</span>
                </div>
                <div className="flex items-center gap-1">
                <RefreshCw className="w-4 h-4" />
                <span>{formattedUpdatedDate}</span>
                </div>
                <div className="flex items-center gap-1">
                <View className="w-4 h-4" />
                <span>{item.clicks ?? 'N/A'} Clicks</span>
                </div>
            </div>

            {/* Process Info (Markdown) with Tabs */}
            {(processinfo || itemTool) && (
                <Card>
                <CardContent>
                    <Tabs defaultValue="introduction">
                        <TabsList className="mb-4">
                            <TabsTrigger value="introduction">
                                <div className="flex items-center gap-1">
                                    <Info className="w-4 h-4" />
                                    <span>{t("tools.introduction")}</span>
                                </div>
                            </TabsTrigger>
                            <TabsTrigger value="tools" disabled={!itemTool || itemTool.tools.length === 0}>
                                <div className="flex items-center gap-1">
                                    <Wrench className="w-4 h-4" />
                                    <span>{t("tools.title")}</span>
                                </div>
                            </TabsTrigger>
                            <TabsTrigger value="usage" disabled={!itemTool || !itemTool.usage || Object.keys(itemTool.usage).length === 0}>
                                <div className="flex items-center gap-1">
                                    <ExternalLink className="w-4 h-4" />
                                    <span>{t("tools.usage_examples")}</span>
                                </div>
                            </TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="introduction" className="p-0">
                            {processinfo ? (
                                <div className="p-5 bg-white dark:bg-gray-950 rounded-lg shadow-sm border border-gray-100 dark:border-gray-800">
                                    <SimpleMarkdown markdown={processinfo} />
                                </div>
                            ) : (
                                <div className="text-muted-foreground bg-muted/30 rounded-lg p-6 text-center">
                                    <p className="text-lg">{t("tools.no_introduction")}</p>
                                </div>
                            )}
                        </TabsContent>
                        
                        <TabsContent value="tools" className="p-0">
                            {itemTool && itemTool.tools.length > 0 ? (
                                <UsageList itemTool={itemTool} t={t} />
                            ) : (
                                <div className="text-muted-foreground bg-muted/30 rounded-lg p-6 text-center">
                                    <p className="text-lg">{t("tools.no_tools")}</p>
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="usage" className="p-0">
                            {/* Usage Examples */}
                            {itemTool && itemTool.usage && Object.keys(itemTool.usage).length > 0 ? (
                                <>
                                    <ToolsList itemTool={itemTool} t={t} />
                                </>
                            ) : (
                                <div className="text-muted-foreground bg-muted/30 rounded-lg p-6 text-center">
                                    <p className="text-lg">{t("tools.no_usage_examples")}</p>
                                </div>
                            )}
                        </TabsContent>
                    </Tabs>
                </CardContent>
                </Card>
            )}
            </div>

            {/* --- Right Column (Metadata) --- */}
            <div className="space-y-4">

            {/* Repository Info Card */}
            <Card>
                <GithubCard metadata={metadata} formattedRelativeDates={formattedRelativeDates} />
            </Card>

            {/* Affiliate Products */}
            <Suspense fallback={<div className="h-24"></div>}>
                <AffiliateSidebar limit={2} />
            </Suspense>

            {/* Advertisement */}
            {process.env.NODE_ENV === 'production' && (
                <div className="mt-4" dangerouslySetInnerHTML={{
                    __html: `
                    <script type="text/javascript">
                        atOptions = {
                            'key' : '2119d7815b7e2ce0254ae97a694f31ca',
                            'format' : 'iframe',
                            'height' : 50,
                            'width' : 320,
                            'params' : {}
                        };
                    </script>
                    <script type="text/javascript" src="//www.highperformanceformat.com/2119d7815b7e2ce0254ae97a694f31ca/invoke.js"></script>
                    `
                }} />
            )}

            </div>
        </div>

        {/* --- Recommendations --- */}
        {recommendations.length > 0 && (
            <div className="mt-12 pt-8 border-t">
            <h2 className="text-2xl font-semibold mb-4">Recommended Items</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {recommendations.map((rec) => (
                    <ItemCard key={rec.uuid} item={rec} />
                ))}
                </div>
            </div>
        )}
        </div>
    );
} 