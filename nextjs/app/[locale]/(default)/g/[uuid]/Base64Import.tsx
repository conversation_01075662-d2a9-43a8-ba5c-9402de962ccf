'use client';

import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useCallback } from "react";
import type { MouseEvent } from "react";

interface Base64ImportProps {
    codeExample: string;
    platform: string; // Changed from t function to string label
    icon: string;
}

const URL_MAPS: Record<string, string> = {
    cherrystudio: "cherrystudio://mcp/install?server=",
}

export default function Base64Import({ codeExample, platform = "cherrystudio", icon = "Download" }: Base64ImportProps) {
    const handleInstallClick = useCallback((e: MouseEvent<HTMLButtonElement>) => {
        if (typeof window !== 'undefined' && Object.keys(URL_MAPS).includes(platform) && codeExample) {
            try {
                // UTF-8 safe Base64 encoding
                const base64Encoded = btoa(unescape(encodeURIComponent(codeExample)));
                let installUrl = URL_MAPS[platform] + base64Encoded;
                window.open(installUrl, '_blank');
            } catch (e) {
                console.error("Failed to encode or open install link:", e);
                // Optionally, show an error message to the user
            }
        }
    }, [codeExample, platform]);

    return (
        <Button
            variant="outline"
            size="sm"
            onClick={handleInstallClick}
            disabled={!codeExample}
            className="ml-auto"
        >
            <Download className="w-3 h-3 mr-1" />
            Install {/* Use the passed string label */}
        </Button>
    );
}