"use client";

import React, { useEffect, useRef } from 'react';
import { FavoriteButton } from '@/components/ui/FavoriteButton';
import { createPortal } from 'react-dom';

interface FavoriteInteractionsProps {
  itemUuid: string;
  user_uuid?: string;
}

export default function FavoriteInteractions({ itemUuid, user_uuid }: FavoriteInteractionsProps) {
  const favButtonMounted = useRef(false);

  useEffect(() => {
    // 确保只有在 user_uuid 存在时才渲染收藏按钮
    if (user_uuid && !favButtonMounted.current) {
      const container = document.getElementById('favorite-button-container');
      if (container) {
        favButtonMounted.current = true;
      }
    }
  }, [user_uuid]);

  // 找不到挂载点或无用户ID时不渲染任何内容
  if (!user_uuid) {
    return null;
  }

  return (
    <>
      {typeof document !== 'undefined' && createPortal(
        <FavoriteButton itemUuid={itemUuid} user_uuid={user_uuid} />,
        document.getElementById('favorite-button-container') || document.createElement('div')
      )}
    </>
  );
} 