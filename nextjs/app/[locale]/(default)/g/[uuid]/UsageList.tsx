import { ItemTool } from "@/types/ItemTools";
import { ExternalLink } from "lucide-react"; // Removed Download icon import
import Base64Import from "./Base64Import"; // Import the new client component

export default function UsageList({ itemTool, t }: { itemTool: ItemTool, t: (key: string) => string }) {
    const usage = itemTool.usage;
    if (!usage) {
        return null;
    }

    return (
        <div className="mt-8 border-t border-gray-100 dark:border-gray-800 pt-6">
            <h3 className="text-lg font-medium mb-4 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2 text-blue-500" />
                {t("tools.usage_examples")}
            </h3>

            <div className="space-y-6">

                {/* STDIO Usage */}
                {usage.stdio && (
                    <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900 shadow-sm">
                        <h4 className="text-md font-semibold mb-3 pb-2 border-b flex items-center justify-between">
                            <div className="flex items-center">
                                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-0.5 rounded mr-2 text-xs">STDIO</span>
                                {t("tools.stdio")}
                            </div>
                            {/* Use the Base64Import client component, passing the translated label */}
                            {usage.stdio.code_example && (
                                <Base64Import
                                    codeExample={usage.stdio.code_example}
                                    platform={"cherrystudio"} // Pass the translated string
                                    icon="" // Pass an empty string for the button label
                                />
                            )}
                        </h4>
                        {usage.stdio.description && (
                            <p className="text-sm text-muted-foreground mb-3">
                                {usage.stdio.description}
                            </p>
                        )}
                        {usage.stdio.code_example && (
                            <div className="relative">
                                <pre className="bg-gray-800 text-gray-100 p-4 rounded-md overflow-x-auto text-xs mt-3">
                                    <code>{usage.stdio.code_example}</code>
                                </pre>
                            </div>
                        )}
                    </div>
                )}

                {/* SSE Usage */}
                {usage.sse && (
                    <div className="border rounded-lg p-4 mb-4 bg-gray-50 dark:bg-gray-900 shadow-sm">
                        <h4 className="text-md font-semibold mb-3 pb-2 border-b flex items-center">
                            <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-0.5 rounded mr-2 text-xs">SSE</span>
                            {t("tools.sse")}
                        </h4>
                        {usage.sse.description && (
                            <p className="text-sm text-muted-foreground mb-3">
                                {usage.sse.description}
                            </p>
                        )}
                        {usage.sse.code_example && (
                            <div className="relative">
                                <pre className="bg-gray-800 text-gray-100 p-4 rounded-md overflow-x-auto text-xs mt-3">
                                    <code>{usage.sse.code_example}</code>
                                </pre>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}