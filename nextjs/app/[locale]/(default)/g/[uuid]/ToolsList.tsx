import { Wrench } from "lucide-react";
import { ItemTool, Tool, Tool<PERSON>arameter } from "@/types/ItemTools";

interface ToolsListProps {
    itemTool: ItemTool;
    t: (key: string) => string;
}

export default function ToolsList({ itemTool, t }: ToolsListProps) {

    return (
        <div className="space-y-6 p-5 bg-white dark:bg-gray-950 rounded-lg shadow-sm border border-gray-100 dark:border-gray-800">
            {/* Item Type */}
            <div className="pb-4 border-b border-gray-100 dark:border-gray-800">
                <h3 className="text-lg font-medium mb-2 flex items-center">
                    <Wrench className="w-4 h-4 mr-2 text-blue-500" />
                    {t("tools.item_type")}
                </h3>
                {/* <Badge variant="outline" className="capitalize bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                    {itemTool.type == 'both' ? t("sse") && t("stdio")  : t(`tools.${itemTool.type}`)}
                </Badge>
                <div className="text-sm text-muted-foreground mt-2 pl-6 border-l-2 border-blue-200 dark:border-blue-800">
                    {itemTool.type === 'sse' && t("tools.supports_sse")}
                    {itemTool.type === 'stdio' && t("tools.supports_stdio")}
                    {itemTool.type === 'both' && t("tools.supports_both")}
                </div> */}
            </div>

            <div>
                <h3 className="text-lg font-medium mb-4 flex items-center">
                    <Wrench className="w-4 h-4 mr-2 text-blue-500" />
                    {t("tools.available_tools")}
                </h3>
                <div className="grid grid-cols-1 gap-4">
                    {itemTool.tools.map((tool: Tool, index: number) => (
                        <div key={index} className="border rounded-md p-4 mb-4 bg-gray-50 dark:bg-gray-900 shadow-sm hover:shadow-md transition-shadow">
                            <h4 className="text-md font-semibold border-b pb-2 mb-3 flex items-center">
                                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-xs">{index + 1}</span>
                                {tool.name}
                            </h4>
                            <p className="text-sm text-muted-foreground mb-3">{tool.description}</p>
                            
                            {/* Parameters */}
                            {tool.parameters && tool.parameters.length > 0 && (
                                <div className="mt-4 bg-white dark:bg-gray-950 rounded-md p-3 border border-gray-200 dark:border-gray-800">
                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                        <span className="text-xs bg-gray-200 dark:bg-gray-800 px-2 py-0.5 rounded mr-2">{tool.parameters.length}</span>
                                        {t("tools.parameters")}:
                                    </h5>
                                    <div className="overflow-x-auto">
                                        <table className="w-full text-sm">
                                            <thead className="bg-muted text-xs uppercase">
                                                <tr>
                                                    <th className="text-left p-2 rounded-tl-md">{t("tools.name")}</th>
                                                    <th className="text-left p-2">{t("tools.type")}</th>
                                                    <th className="text-left p-2">{t("tools.required")}</th>
                                                    <th className="text-left p-2 rounded-tr-md">{t("tools.description")}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {tool.parameters.map((param: ToolParameter, pIndex: number) => (
                                                    <tr key={pIndex} className={`border-b ${pIndex % 2 === 0 ? 'bg-gray-50 dark:bg-gray-900/50' : ''}`}>
                                                        <td className="p-2 font-mono text-xs">{param.name}</td>
                                                        <td className="p-2">
                                                            <span className="bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded text-xs">{param.type}</span>
                                                        </td>
                                                        <td className="p-2">
                                                            {param.required ? 
                                                                <span className="text-green-600 dark:text-green-400 text-xs">{t("tools.yes")}</span> : 
                                                                <span className="text-gray-400 dark:text-gray-500 text-xs">{t("tools.no")}</span>
                                                            }
                                                        </td>
                                                        <td className="p-2 text-xs">{param.description || '-'}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}