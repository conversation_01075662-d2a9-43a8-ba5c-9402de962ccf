import { notFound } from 'next/navigation';
import { getItemByUuid, getRandomPublicItems } from '@/models/items';
import { incrementItemClicks } from '@/services/items';
import { Items, ItemLocalization } from '@/types/items';
import { Metadata } from 'next';
import React from 'react';
import { getItemToolsByUuid } from '@/models/itemTools';
import { getUserUuid } from '@/services/user';
import ItemDetailServer from './ItemDetail';
import FavoriteInteractionsProps from './FavoriteInteractions';
import { formatDate, formatRelativeDate } from '@/lib/utils';


interface ItemDetailPageProps {
  params: Promise<{
    uuid: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: ItemDetailPageProps): Promise<Metadata> {
  const { uuid, locale } = await params;

  try {
    // --- Direct Data Fetching for Metadata ---
    const { data: item, error } = await getItemByUuid(uuid, locale || 'en');

    if (error || !item) {
        console.error('Error fetching Item for metadata:', error?.message || 'Item not found');
       return {
         title: 'Item Not Found',
         description: 'The requested Item could not be found.'
       };
     }

    const brief = item.localization?.brief || item.brief || ""; // Use item.brief as fallback
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL; // Use site URL for metadata
    
    // Determine canonical URL - always use the English version as canonical
    let canonicalUrl = `${baseUrl}/${locale}/g/${uuid}`;

    return {
      title: `${item.name} - Item Hub`,
      description: brief,
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: `${item.name} - Item Hub`,
        description: brief,
        type: 'article',
        url: `${baseUrl}/${locale}/g/${uuid}`,
        // Ensure user_avatar_url exists and is correct field name
        images: item.user_avatar_url ? [{ url: item.user_avatar_url }] : undefined,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Item Detail - Item Hub',
      description: 'View Item details',
    };
  }
}

// --- Server Component ---
export default async function ItemDetailPage({ params }: ItemDetailPageProps) {
  const { uuid, locale } = await params;

  // --- Data Fetching ---
  let item: (Items & { localization?: Partial<ItemLocalization> }) | null = null;
  let recommendations: Items[] = [];
  let fetchError: string | null = null;
  let user_uuid: string | undefined;

  try {
    // --- Direct Data Fetching ---
    const { data: itemData, error: ItemError } = await getItemByUuid(uuid, locale || 'en');

    // Get user_uuid for favorites functionality
    try {
      user_uuid = await getUserUuid();
    } catch (userError) {
      console.error("Error fetching user UUID:", userError);
      // Continue without user_uuid, favorites functionality will be disabled
    }

    if (ItemError) {
      // Handle potential errors from getItemByUuid
      // Check for specific error codes if needed (e.g., not found)
        if (ItemError.message.includes('Not found') || ItemError.code === 'PGRST116') { // Example check
          notFound();
        }
      throw new Error(`Failed to fetch Item details: ${ItemError.message}`);
    }

    if (!itemData) {
        notFound(); // Trigger 404 page if Item not found
    }
    item = itemData; // Assign fetched data

    // Increment Item clicks/views when page is opened
    if (process.env.NODE_ENV !== 'development' && uuid) {
      try {
        await incrementItemClicks(uuid);
      } catch (error) {
        console.error('Failed to increment Item views:', error);
      }
    }

    const { data: recommendationsData, error: recommendationsError } = await getRandomPublicItems(9);

    if (recommendationsError) {
      console.error("Error fetching recommendations:", recommendationsError);
    }

    recommendations = recommendationsData || [];
    // Filter out the current Item from recommendations if it's in there
    recommendations = recommendations.filter(rec => rec.uuid !== uuid);

  } catch (err: any) {
    console.error("Error fetching Item data server-side:", err);
    // If the error came from getItemByUuid and wasn't a 'not found' handled above
    if (!(err instanceof Error && err.message.includes('Failed to fetch Item details'))) {
        fetchError = err.message || 'An unknown error occurred while fetching data.';
    }
    // If it's a notFound() error triggered above, Next.js handles it.
    // Otherwise, if item is still null, let the error display logic handle it.
  }

  // --- Render Logic ---
  if (fetchError && !item) { // Show error only if primary data failed and we don't have Item
    return <div className="container mx-auto px-4 py-8 text-red-600">Error: {fetchError}</div>;
  }

  if (!item) {
    // This case should ideally be handled by notFound() earlier.
    // If reached, it implies an unexpected state.
      notFound();
  }

  // Fetch Item tools data
  const itemTool = await getItemToolsByUuid(uuid);

  // Format date for display on server
  const formattedCreatedDate = formatDate(item.created_at, locale);
  const formattedUpdatedDate = formatRelativeDate(item.updated_at);
  
  // Pre-format all dates that might be used with formatRelativeDate
  const formattedRelativeDates: {[key: string]: string} = {};
  if (item.metadata?.updated_at) {
    formattedRelativeDates[item.metadata.updated_at] = formatRelativeDate(item.metadata.updated_at);
  }

  return (
    <>
      {/* Server-rendered component for main content (SEO-important) */}
      <ItemDetailServer 
        item={item}
        recommendations={recommendations}
        locale={locale}
        formattedCreatedDate={formattedCreatedDate}
        formattedUpdatedDate={formattedUpdatedDate}
        formattedRelativeDates={formattedRelativeDates}
        itemTool={itemTool.data}
      />

      {/* Client-side component for interactive elements */}
      <FavoriteInteractionsProps
        itemUuid={item.uuid}
        user_uuid={user_uuid}
      />
    </>
  );
}
