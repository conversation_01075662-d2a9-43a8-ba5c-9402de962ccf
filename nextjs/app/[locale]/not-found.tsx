import Link from 'next/link';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { getLocale } from 'next-intl/server';

export default async function NotFound() {
  const locale = await getLocale();
  const t = await getTranslations('NotFound');

  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-4 bg-white dark:bg-slate-950 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute w-4 h-4 text-blue-400 animate-bounce" style={{ top: '15%', right: '15%' }}>✨</div>
      <div className="absolute w-4 h-4 text-purple-400 animate-bounce" style={{ top: '25%', left: '20%' }}>✨</div>
      <div className="absolute w-4 h-4 text-green-400 animate-bounce" style={{ bottom: '20%', right: '25%' }}>✨</div>
      <div className="absolute w-4 h-4 text-yellow-400 animate-bounce" style={{ bottom: '30%', left: '15%' }}>✨</div>

      <div className="text-center relative z-10">
        {/* 404 number */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <span className="text-[120px] md:text-[200px] font-bold text-primary">4</span>
          <div className="flex flex-col items-center justify-center">
            <div className="w-16 h-16 md:w-24 md:h-24 bg-secondary rounded-full flex items-center justify-center mb-2">
              <div className="text-2xl md:text-4xl">🤔</div>
            </div>
            <div className="w-16 md:w-24 h-2 bg-accent"></div>
          </div>
          <span className="text-[120px] md:text-[200px] font-bold text-primary">4</span>
        </div>

        {/* Error message */}
        <h1 className="text-2xl md:text-4xl font-bold text-slate-900 dark:text-slate-100 mb-4">{t('title')}</h1>
        <p className="text-slate-600 dark:text-slate-400 mb-8">{t('description')}</p>

        {/* Button */}
        <Link 
          href={`/${locale}`} 
          className="inline-block bg-primary text-white px-8 py-4 rounded-lg hover:bg-primary/90 transition-all duration-300 hover:scale-105 hover:shadow-lg"
        >
          {t('goHome')}
        </Link>
      </div>

      {/* Copyright */}
      <div className="absolute bottom-8 text-slate-400 dark:text-slate-600 text-sm">
        © {new Date().getFullYear()} AiMCP. All rights reserved
      </div>
    </div>
  );
} 