'use client';

import React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { X<PERSON>ircle, AlertTriangle, RefreshCw, Home, Clock, Wifi } from 'lucide-react';
import OAuthErrorDisplay from '@/components/auth/oauth-error-display';

const OAuthErrorPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const error = searchParams.get('error');
  const description = searchParams.get('description');
  const requiresReauth = searchParams.get('requiresReauth') === 'true';

  const getErrorInfo = () => {
    switch (error) {
      case 'oauth_timeout':
      case 'connection_timeout':
        return {
          title: 'Connection Timeout',
          message: 'The connection to the authentication service timed out. This might be due to a slow network connection.',
          icon: Clock,
          severity: 'default' as const,
          canRetry: true,
          isTimeout: true
        };
      
      case 'network_error':
        return {
          title: 'Network Error',
          message: 'Unable to connect to the authentication service. Please check your internet connection.',
          icon: Wifi,
          severity: 'default' as const,
          canRetry: true,
          isTimeout: false
        };
      
      case 'access_denied':
        return {
          title: 'Authorization Denied',
          message: 'You declined to grant the necessary permissions. To use this feature, please try again and accept the required permissions.',
          icon: XCircle,
          severity: 'default' as const,
          canRetry: true
        };
      
      case 'invalid_request':
        return {
          title: 'Invalid Request',
          message: description || 'The authorization request was invalid. Please try again.',
          icon: AlertTriangle,
          severity: 'destructive' as const,
          canRetry: true
        };
      
      case 'invalid_grant':
        return {
          title: 'Authorization Expired',
          message: 'Your authorization session has expired. Please start the connection process again.',
          icon: XCircle,
          severity: 'destructive' as const,
          canRetry: true
        };
      
      case 'token_expired':
        return {
          title: 'Token Expired',
          message: 'Your access token has expired. Please reconnect to refresh your permissions.',
          icon: RefreshCw,
          severity: 'default' as const,
          canRetry: true
        };
      
      case 'token_revoked':
        return {
          title: 'Access Revoked',
          message: 'Your access has been revoked. Please reconnect to restore functionality.',
          icon: XCircle,
          severity: 'destructive' as const,
          canRetry: true
        };
      
      case 'insufficient_scope':
        return {
          title: 'Insufficient Permissions',
          message: 'Additional permissions are required for this feature. Please reconnect with the necessary permissions.',
          icon: AlertTriangle,
          severity: 'default' as const,
          canRetry: true
        };
      
      case 'server_error':
        return {
          title: 'Server Error',
          message: description || 'An unexpected server error occurred. Please try again later.',
          icon: XCircle,
          severity: 'destructive' as const,
          canRetry: true
        };
      
      default:
        return {
          title: 'Connection Failed',
          message: description || 'An unexpected error occurred during the connection process.',
          icon: XCircle,
          severity: 'destructive' as const,
          canRetry: true
        };
    }
  };

  const errorInfo = getErrorInfo();
  const ErrorIcon = errorInfo.icon;

  const handleRetry = () => {
    // Go back to the previous page or projects list
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/projects');
    }
  };

  const handleGoHome = () => {
    router.push('/projects');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md p-8">
        <div className="text-center">
          <ErrorIcon className={`w-16 h-16 mx-auto mb-6 ${
            errorInfo.severity === 'destructive' ? 'text-red-600' : 'text-yellow-600'
          }`} />
          
          <h1 className={`text-2xl font-bold mb-4 ${
            errorInfo.severity === 'destructive' 
              ? 'text-red-800 dark:text-red-400' 
              : 'text-yellow-800 dark:text-yellow-400'
          }`}>
            {errorInfo.title}
          </h1>
          
          <p className="text-muted-foreground mb-6">
            {errorInfo.message}
          </p>
          
          <Alert variant={errorInfo.severity} className="mb-6 text-left">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>What happened?</AlertTitle>
            <AlertDescription>
              {(error === 'oauth_timeout' || error === 'connection_timeout') && 
                "The connection to Google's servers took too long to complete. This is often caused by slow internet connections or temporary server issues."
              }
              {error === 'network_error' && 
                "Your device couldn't establish a connection to the authentication service. Please check your internet connection and try again."
              }
              {error === 'access_denied' && 
                "You need to grant the requested permissions for LinkTrackPro to access your Google data."
              }
              {error === 'invalid_request' && 
                "The authorization request was malformed or missing required parameters."
              }
              {error === 'invalid_grant' && 
                "The authorization code or refresh token is invalid or has expired."
              }
              {(error === 'token_expired' || error === 'token_revoked') && 
                "Your previously granted access is no longer valid and needs to be renewed."
              }
              {error === 'insufficient_scope' && 
                "The current permissions don't include access to the requested features."
              }
              {error === 'server_error' && 
                "There was a problem on our servers. This is usually temporary."
              }
              {!error && 
                "An unexpected error occurred during the OAuth flow."
              }
            </AlertDescription>
          </Alert>
          
          <div className="flex flex-col space-y-3">
            {errorInfo.canRetry && (
              <Button onClick={handleRetry} className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            )}
            
            <Button onClick={handleGoHome} variant="outline" className="w-full">
              <Home className="w-4 h-4 mr-2" />
              Go to Projects
            </Button>
          </div>
          
          {(error === 'oauth_timeout' || error === 'connection_timeout') && (
            <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-950/20 rounded-lg">
              <h3 className="text-sm font-semibold text-amber-800 dark:text-amber-200 mb-2">
                Troubleshooting Tips:
              </h3>
              <ul className="text-xs text-amber-700 dark:text-amber-300 space-y-1">
                <li>• Check your internet connection speed</li>
                <li>• Try switching to a different network (WiFi/mobile data)</li>
                <li>• Wait a few minutes and try again</li>
                <li>• Disable VPN if you're using one</li>
                <li>• Clear your browser cache and cookies</li>
              </ul>
            </div>
          )}

          {requiresReauth && (
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <strong>Note:</strong> You'll need to go through the authorization process again 
                to restore access to your Google data.
              </p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default OAuthErrorPage;