'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Loader2 } from 'lucide-react';

const OAuthSuccessPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const tokenType = searchParams.get('tokenType');
  const projectId = searchParams.get('projectId');

  const serviceName = tokenType === 'analytics' ? 'Google Analytics' : 
                     tokenType === 'search_console' ? 'Google Search Console' :
                     tokenType === 'combined' ? 'Google Analytics & Search Console' :
                     'Google Service';

  useEffect(() => {
    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      handleRedirect();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleRedirect = () => {
    setIsRedirecting(true);
    
    if (projectId) {
      // Redirect to project page with analytics tab and success indicator
      const projectUrl = `/projects/${projectId}?tab=analytics&oauth_success=true&token_type=${tokenType}`;
      router.push(projectUrl);
    } else {
      // Fallback to projects list
      router.push('/projects');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md p-8">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 mx-auto mb-6 text-green-600" />
          
          <h1 className="text-2xl font-bold mb-4 text-green-800 dark:text-green-400">
            Connection Successful!
          </h1>
          
          <p className="text-muted-foreground mb-6">
            {serviceName} has been successfully connected to your project. 
            You can now access advanced analytics and insights.
          </p>
          
          <div className="space-y-4">
            <div className="bg-green-50 dark:bg-green-950/20 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                What's Next?
              </h3>
              <ul className="text-sm text-green-700 dark:text-green-300 space-y-1 text-left">
                <li>• Configure your analytics settings</li>
                <li>• Set up data collection preferences</li>
                <li>• Start tracking your website performance</li>
              </ul>
            </div>
            
            <div className="flex items-center justify-center space-x-3">
              {isRedirecting ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Redirecting...</span>
                </>
              ) : (
                <>
                  <span className="text-sm text-muted-foreground">
                    Redirecting in 3 seconds...
                  </span>
                  <Button 
                    onClick={handleRedirect}
                    size="sm"
                    variant="outline"
                  >
                    Go Now
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default OAuthSuccessPage;