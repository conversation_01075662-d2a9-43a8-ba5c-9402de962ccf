import React from 'react';

export default function TermsOfService() {
  return (
    <div className="container mx-auto px-4 py-8 prose dark:prose-invert max-w-4xl">
      <h1>Terms of Service</h1>
      <p>Last updated: April 2025</p>
      
      <h2>Acceptance of Terms</h2>
      <p>By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.</p>
      
      <h2>Description of Services</h2>
      <p>MCP Hub provides a platform for discovering and sharing Model Context Protocol tools and services.</p>
      
      <h2>Privacy Policy</h2>
      <p>Your use of MCP Hub is also governed by our Privacy Policy.</p>
      
      <h2>Intellectual Property</h2>
      <p>All content on this site is the property of MCP Hub or its users and protected by intellectual property laws.</p>
      
      <h2>Limitation of Liability</h2>
      <p>MCP Hub shall not be liable for any indirect, incidental, special, consequential or punitive damages.</p>
      
      <h2>Changes to Terms</h2>
      <p>We reserve the right to modify these terms at any time. Please review them periodically.</p>
      
      <h2>Contact Information</h2>
      <p>If you have any questions about these Terms, please contact us.</p>
    </div>
  );
} 