import React from 'react';

export default function PrivacyPolicy() {
  return (
    <div className="container mx-auto px-4 py-8 prose dark:prose-invert max-w-4xl">
      <h1>Privacy Policy</h1>
      <p>Last updated: April 2025</p>
      
      <h2>Information Collection</h2>
      <p>We collect information that you provide directly to us when using our services.</p>
      
      <h2>Use of Information</h2>
      <p>We use the information we collect to provide, maintain, and improve our services.</p>
      
      <h2>Cookies</h2>
      <p>We use cookies and similar technologies to collect information about how you use our services.</p>
      
      <h2>Information Sharing</h2>
      <p>We do not share your personal information with third parties except as described in this policy.</p>
      
      <h2>Security</h2>
      <p>We take reasonable measures to help protect your personal information.</p>
      
      <h2>Your Rights</h2>
      <p>You have the right to access, correct, or delete your personal information.</p>
      
      <h2>Changes to Policy</h2>
      <p>We may update this privacy policy from time to time.</p>
      
      <h2>Contact Us</h2>
      <p>If you have questions about this Privacy Policy, please contact us.</p>
    </div>
  );
} 