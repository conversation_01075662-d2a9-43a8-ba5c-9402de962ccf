# Ping API Documentation

This API endpoint serves as a simple test endpoint, potentially for checking authentication and credit deduction.

## Ping Test

*   **Method:** `POST`
*   **Path:** `/api/ping`
*   **Authentication:** User Required (must be signed in)
*   **Description:** Receives a message, checks user authentication, deducts a predefined amount of credits for the 'Ping' action, and responds with a confirmation message.
*   **Request Body:** `application/json`
    ```json
    {
      "message": "string (required) - Any message string."
    }
    ```
*   **Responses:**
    *   `200 OK`: Request successful.
        ```json
        {
          "code": 0, // Indicates success
          "message": "Success",
          "data": {
            "pong": "string (e.g., 'received message: your_message_here')"
          }
        }
        ```
    *   `200 OK` (with error): Request failed due to invalid parameters, authentication issues, or errors during credit deduction.
        ```json
        {
          "code": -1, // Indicates error
          "message": "string (Error description, e.g., 'invalid params', 'no auth', 'test failed')"
        }
        ```
    *   `500 Internal Server Error`: Unhandled server error during processing.

**Details:**

*   This endpoint requires the user to be authenticated.
*   It expects a `message` field in the JSON request body.
*   Upon successful authentication and validation, it attempts to decrease the user's credits by a fixed amount associated with the `CreditsTransType.Ping` type.
*   It returns a `pong` message confirming receipt of the original message.
