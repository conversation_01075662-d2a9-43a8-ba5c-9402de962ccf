import { NextRequest, NextResponse } from "next/server";
import { oauthToken<PERSON>anager, OAuthError } from "@/services/oauth-token-manager";

// Global OAuth callback handler for progressive OAuth flows
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Handle OAuth errors from Google
    if (error) {
      const errorMessage = errorDescription || error;
      console.error('OAuth error from Google:', errorMessage);
      
      // Redirect to error page with error details
      const errorUrl = new URL('/oauth/error', request.url);
      errorUrl.searchParams.set('error', error);
      errorUrl.searchParams.set('description', errorMessage);
      
      return NextResponse.redirect(errorUrl);
    }

    // Validate required parameters
    if (!code || !state) {
      console.error('Missing required OAuth parameters:', { code: !!code, state: !!state });
      
      const errorUrl = new URL('/oauth/error', request.url);
      errorUrl.searchParams.set('error', 'invalid_request');
      errorUrl.searchParams.set('description', 'Missing authorization code or state parameter');
      
      return NextResponse.redirect(errorUrl);
    }

    try {
      // Exchange code for tokens using the token manager
      const tokenSet = await oauthTokenManager.exchangeCodeForTokens(code, state);
      
      // Redirect to success page with token details
      const successUrl = new URL('/oauth/success', request.url);
      successUrl.searchParams.set('tokenType', tokenSet.tokenType);
      successUrl.searchParams.set('projectId', tokenSet.projectId);
      
      return NextResponse.redirect(successUrl);
    } catch (exchangeError) {
      console.error('Token exchange error:', exchangeError);
      
      if (exchangeError instanceof OAuthError) {
        const errorUrl = new URL('/oauth/error', request.url);
        errorUrl.searchParams.set('error', exchangeError.type);
        errorUrl.searchParams.set('description', exchangeError.message);
        errorUrl.searchParams.set('requiresReauth', exchangeError.requiresReauth.toString());
        
        return NextResponse.redirect(errorUrl);
      }
      
      throw exchangeError;
    }
  } catch (error) {
    console.error('OAuth callback error:', error);
    
    // Redirect to generic error page
    const errorUrl = new URL('/oauth/error', request.url);
    errorUrl.searchParams.set('error', 'server_error');
    errorUrl.searchParams.set('description', 'An unexpected error occurred during OAuth callback');
    
    return NextResponse.redirect(errorUrl);
  }
}