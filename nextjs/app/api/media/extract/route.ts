
import { NextResponse } from 'next/server'
// Removed: import { extractMediaContent } from '@/services/media'

export async function POST(request: Request) {
  try {
    const { url, type } = await request.json()
    const backendWorkerUrl = process.env.BACKEND_WORKER_URL;
    const backendWorkerAPI = process.env.BACKEND_WORKER_API_KEY;

    if (!backendWorkerUrl || !backendWorkerAPI) {
      console.error('BACKEND_WORKER_URL or BACKEND_WORKER_API_KEY environment variable is not set.');
      return NextResponse.json({ error: 'Backend worker service is not configured.' }, { status: 500 });
    }

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      )
    }

    // Call the backend worker's unified meta endpoint
    const response = await fetch(`${backendWorkerUrl}/meta/extract`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${backendWorkerAPI}`,
      },
      body: JSON.stringify({ url, type }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse backend error response' }));
      console.error(`Backend worker error (${response.status}):`, errorData);
      return NextResponse.json(
        { error: errorData.error || `Backend worker failed with status ${response.status}` },
        { status: response.status }
      );
    }

    const result = await response.json();
    
    return NextResponse.json(result, { status: 200 })
  } catch (error: any) {
    console.error('Error calling backend worker for metadata extraction:', error)
    
    return NextResponse.json(
      { error: error.message || 'Failed to extract media content' },
      { status: 500 }
    )
  }
} 
