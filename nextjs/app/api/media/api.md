# Media API Documentation

This document outlines the API endpoints available under the `/api/media` path for interacting with external media content like Twitter/X posts, YouTube videos, and Jike posts.

## Extract Media Content

*   **Method:** `POST`
*   **Path:** `/api/media/extract`
*   **Authentication:** None
*   **Description:** Extracts content and metadata from a social media URL (Twitter/X, YouTube, or Jike).
*   **Request Body:** `application/json`
    ```json
    {
      "url": "string (required) - URL of the media content to extract",
      "type": "string (optional) - Type of media (twitter, x, youtube, jike). If not provided, will be detected from URL."
    }
    ```
*   **Responses:**
    *   `200 OK`: Returns extracted media content.
        ```json
        {
          "success": true,
          "data": {
            "type": "string (twitter|x|youtube|jike)",
            "title": "string (optional)",
            "description": "string (optional)",
            "authorName": "string (optional)",
            "authorAvatar": "string (optional)",
            "content": "string (optional)",
            "mediaUrl": "string (optional)",
            "id": "string - platform-specific ID"
          }
        }
        ```
    *   `400 Bad Request`: Missing URL or invalid request.
        ```json
        {
          "error": "URL is required"
        }
        ```
    *   `500 Internal Server Error`: Failed to extract content.
        ```json
        {
          "success": false,
          "error": "string - error message"
        }
        ``` 