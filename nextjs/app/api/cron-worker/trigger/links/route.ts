import { NextRequest, NextResponse } from "next/server";
import { isAdminAuthenticated, unauthorizedResponse } from "@/lib/adminAuth";


export async function POST(request: NextRequest) {
  try {
    // Check if user is admin for this premium feature
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse("This feature is restricted to administrators during beta period");
    }

    // Get the cron worker URL from environment variables
    const cronWorkerUrl = process.env.BACKEND_WORKER_URL || 'http://localhost:8787';
    
    // Forward the request to the cron worker
    const response = await fetch(`${cronWorkerUrl}/trigger/links`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY || ''}`,
      },
    });

    if (response.ok) {
      // First try to get response as text to handle non-JSON responses
      const responseText = await response.text();
      let data;
      
      try {
        // Try to parse as JSON first
        data = JSON.parse(responseText);
      } catch (jsonError) {
        // If it's not valid JSON, treat it as a plain text message
        console.log('Worker returned non-JSON response:', responseText);
        data = { message: responseText };
      }
      
      return NextResponse.json({
        success: true,
        message: 'External links discovery triggered successfully',
        data
      });
    } else {
      const error = await response.text();
      return NextResponse.json({
        error: 'Failed to trigger external links discovery',
        details: error
      }, { status: response.status });
    }
  } catch (error) {
    console.error('Error triggering external links discovery:', error);
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Failed to trigger external links discovery'
    }, { status: 500 });
  }
} 