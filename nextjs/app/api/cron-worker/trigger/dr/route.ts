import { NextRequest, NextResponse } from "next/server";
import { validateTierAccess, createTierErrorResponse, createTierSuccessResponse } from "@/lib/tier-middleware";


export async function POST(request: NextRequest) {
  try {
    // Validate tier access for DR queries
    const tierResult = await validateTierAccess(request, { 
      action: 'dr_query',
      recordUsage: true,
      apiEndpoint: '/api/cron-worker/trigger/dr',
      metadata: { action: 'batch_dr_update' }
    });
    
    if (!tierResult.success) {
      return createTierErrorResponse(tierResult);
    }

    const userUuid = tierResult.userUuid!;

    // Get the cron worker URL from environment variables
    const cronWorkerUrl = process.env.BACKEND_WORKER_URL || 'http://localhost:8787';
    
    // Forward the request to the cron worker
    const response = await fetch(`${cronWorkerUrl}/trigger/dr`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY || ''}`,
      },
    });

    if (response.ok) {
      // First try to get response as text to handle non-JSON responses
      const responseText = await response.text();
      let data;
      
      try {
        // Try to parse as JSON first
        data = JSON.parse(responseText);
      } catch (jsonError) {
        // If it's not valid JSON, treat it as a plain text message
        console.log('Worker returned non-JSON response:', responseText);
        data = { message: responseText };
      }
      
      return await createTierSuccessResponse({
        success: true,
        message: 'DR update triggered successfully',
        data
      }, userUuid);
    } else {
      const error = await response.text();
      return NextResponse.json({
        error: 'Failed to trigger DR update',
        details: error
      }, { status: response.status });
    }
  } catch (error) {
    console.error('Error triggering DR update:', error);
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Failed to trigger DR update'
    }, { status: 500 });
  }
} 