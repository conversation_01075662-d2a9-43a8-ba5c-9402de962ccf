import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getDomainStats, getDomainStatsHistory, updateDomainStats } from "@/models/links";
import { validateTierAccess, createTierErrorResponse, createTierSuccessResponse } from "@/lib/tier-middleware";


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { domain: domainParam } = await params;
    const domain = decodeURIComponent(domainParam);
    const { searchParams } = new URL(request.url);
    const includeHistory = searchParams.get("includeHistory") === "true";
    const historyLimit = parseInt(searchParams.get("historyLimit") || "30");

    // Get current domain stats
    const { data: domainStats, error } = await getDomainStats(domain);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    let response: any = { domainStats };

    // Include history if requested
    if (includeHistory) {
      const { data: history, error: historyError } = await getDomainStatsHistory(domain, historyLimit);
      
      if (historyError) {
        console.warn("Failed to fetch domain stats history:", historyError);
        response.history = [];
      } else {
        response.history = history || [];
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching domain stats:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  try {
    const { domain: domainParam } = await params;
    const domain = decodeURIComponent(domainParam);
    
    // Validate tier access for manual traffic/DR updates
    const tierResult = await validateTierAccess(request, { 
      action: 'traffic_update',
      recordUsage: true,
      apiEndpoint: `/api/domain-stats/${domain}`,
      metadata: { domain, action: 'manual_domain_stats_update' }
    });
    
    if (!tierResult.success) {
      return createTierErrorResponse(tierResult);
    }

    const userUuid = tierResult.userUuid!;
    const body = await request.json();
    
    const { dr_score, traffic, is_indexed } = body;

    const { error } = await updateDomainStats(
      domain,
      dr_score,
      traffic || 0,
      is_indexed || false
    );

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Return updated stats
    const { data: updatedStats, error: fetchError } = await getDomainStats(domain);
    
    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    return await createTierSuccessResponse({ 
      success: true,
      domainStats: updatedStats,
      message: "Domain stats updated successfully"
    }, userUuid);
  } catch (error) {
    console.error("Error updating domain stats:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}