import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { 
  createLinkResource, 
  getLinkResourcesByUser, 
  getLinkResourcesWithStats,
  importLinkResources 
} from "@/models/links";
import { LinkResource, LinkImportDataNew } from "@/types/links";


export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const withStats = searchParams.get("withStats") === "true";

    let result;
    if (withStats) {
      result = await getLinkResourcesWithStats(user.uuid, limit, offset);
    } else {
      result = await getLinkResourcesByUser(user.uuid, limit, offset);
    }

    const { data: linkResources, error, count } = result;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      linkResources, 
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    });
  } catch (error) {
    console.error("Error fetching link resources:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    // Handle bulk import (CSV format from import dialog)
    if (body.bulk && Array.isArray(body.linkResources)) {
      // Validate each link has required fields (same logic as import-dialog.tsx)
      const validatedLinks: LinkImportDataNew[] = [];
      const validationErrors: string[] = [];

      for (let i = 0; i < body.linkResources.length; i++) {
        const link = body.linkResources[i];
        
        if (!link.url || !link.title) {
          validationErrors.push(`Row ${i + 1}: URL and title are required`);
          continue;
        }

        // Validate URL format
        try {
          new URL(link.url);
        } catch {
          validationErrors.push(`Row ${i + 1}: Invalid URL format`);
          continue;
        }

        validatedLinks.push({
          url: link.url,
          title: link.title,
          link_type: link.link_type || 'free',
          price: link.price ? parseFloat(link.price) : undefined,
          source: link.source,
          acquisition_method: link.acquisition_method,
          notes: link.notes
        });
      }

      if (validationErrors.length > 0) {
        return NextResponse.json({ 
          error: "Validation errors",
          details: validationErrors
        }, { status: 400 });
      }

      const { success, failed, errors } = await importLinkResources(
        user.uuid, 
        validatedLinks
      );

      return NextResponse.json({ 
        success, 
        failed, 
        errors,
        message: `Imported ${success} link resources, ${failed} failed`
      }, { status: 201 });
    }

    // Handle single link resource creation
    const linkResourceData: Omit<LinkResource, 'id' | 'created_at' | 'updated_at'> = {
      ...body,
      user_id: user.uuid,
      currency: body.currency || 'USD'
    };

    const { data: linkResource, error } = await createLinkResource(linkResourceData);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ linkResource }, { status: 201 });
  } catch (error) {
    console.error("Error creating link resource:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}