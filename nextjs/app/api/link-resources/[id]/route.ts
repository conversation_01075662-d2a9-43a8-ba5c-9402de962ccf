import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { updateLinkResource, deleteLinkResource } from "@/models/links";


export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const linkResourceId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(linkResourceId)) {
      return NextResponse.json({ error: "Invalid link resource ID format" }, { status: 400 });
    }

    const body = await request.json();
    
    // Ensure user can only update their own link resources
    const updateData = {
      ...body,
      user_id: user.uuid // Override to prevent privilege escalation
    };
    
    const { data: linkResource, error } = await updateLinkResource(linkResourceId, updateData);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!linkResource) {
      return NextResponse.json({ error: "Link resource not found" }, { status: 404 });
    }

    return NextResponse.json({ linkResource });
  } catch (error) {
    console.error("Error updating link resource:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const linkResourceId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(linkResourceId)) {
      return NextResponse.json({ error: "Invalid link resource ID format" }, { status: 400 });
    }

    const body = await request.json();
    
    // For PATCH, we only update the provided fields
    const { data: linkResource, error } = await updateLinkResource(linkResourceId, body);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!linkResource) {
      return NextResponse.json({ error: "Link resource not found" }, { status: 404 });
    }

    return NextResponse.json({ linkResource });
  } catch (error) {
    console.error("Error patching link resource:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const linkResourceId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(linkResourceId)) {
      return NextResponse.json({ error: "Invalid link resource ID format" }, { status: 400 });
    }

    const { error } = await deleteLinkResource(linkResourceId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting link resource:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}