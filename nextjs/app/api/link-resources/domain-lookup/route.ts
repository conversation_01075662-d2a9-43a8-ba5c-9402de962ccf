import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from "@/lib/auth";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const domain = searchParams.get('domain');

  if (!domain) {
    return NextResponse.json({ error: 'Domain parameter is required' }, { status: 400 });
  }

  try {
    // Authentication check
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use the backend cron-worker WHOIS service
    const cronWorkerUrl = process.env.BACKEND_WORKER_URL;
    
    if (!cronWorkerUrl) {
      return NextResponse.json({ error: 'WHOIS service not configured' }, { status: 500 });
    }

    // Call the cron-worker WHOIS API
    const response = await fetch(`${cronWorkerUrl}/api/whois?domain=${encodeURIComponent(domain)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add authorization header if needed
        'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY || ''}`,
      },
    });

    if (!response.ok) {
      throw new Error(`WHOIS service error: ${response.status}`);
    }

    const whoisData = await response.json();
    
    // Transform the cron-worker response to match the expected format
    const result = {
      domain: whoisData.domain,
      registrar: whoisData.registrar || 'Unknown',
      createdDate: whoisData.creation_date,
      expiryDate: whoisData.expiration_date,
      updatedDate: null, // Not provided by the whois service
      status: [], // Not provided by the whois service
      nameServers: whoisData.name_servers || [],
      contact: {
        registrant: null, // Not provided by the whois service
        admin: null,
        tech: null,
      },
      // Additional metadata from the whois service
      timestamp: whoisData.timestamp,
      source: whoisData.source,
      cached: whoisData.cached || false,
      dnssec: whoisData.dnssec,
      emails: whoisData.emails
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('WHOIS lookup error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch domain information' },
      { status: 500 }
    );
  }
}