# Stripe Notify API Documentation (Webhook)

This API endpoint acts as a webhook receiver for events sent by <PERSON><PERSON>. It is crucial for handling asynchronous payment confirmations and other Stripe-related events.

## Stripe Webhook Handler

*   **Method:** `POST`
*   **Path:** `/api/stripe-notify`
*   **Authentication:** Stripe Webhook Signature Verification
*   **Description:** Listens for incoming webhook events from Stripe. It verifies the authenticity of the event using the `stripe-signature` header and the configured webhook secret. Currently, it specifically handles the `checkout.session.completed` event to process successful payments.
*   **Request Headers:**
    *   `stripe-signature`: string (required) - Provided by <PERSON><PERSON> to verify the event's authenticity.
*   **Request Body:** Raw request body (text) containing the Stripe event JSON payload.
*   **Handled Events:**
    *   `checkout.session.completed`: Triggered when a customer successfully completes a Stripe Checkout session. The handler calls `handleOrderSession` with the session object to update the corresponding order status in the database, potentially grant credits, or activate subscriptions.
    *   Other events are logged but not explicitly handled.
*   **Responses:**
    *   `200 OK`: Event received and processed successfully (or ignored if not handled).
        ```json
        {
          "code": 0,
          "message": "Success"
        }
        ```
    *   `500 Internal Server Error`: Failed to process the event due to invalid signature, missing configuration, or errors during event handling.
        ```json
        {
          "error": "string (Error description, e.g., 'handle stripe notify failed: invalid stripe config', 'handle stripe notify failed: invalid notify data', or other processing errors)"
        }
        ```

**Important:**

*   This endpoint should be configured in your Stripe dashboard's webhook settings.
*   The `STRIPE_PRIVATE_KEY` and `STRIPE_WEBHOOK_SECRET` environment variables must be correctly set for this endpoint to function.
*   It's critical to return a `200 OK` status quickly to Stripe to acknowledge receipt of the event, even if further processing happens asynchronously. Failure to respond promptly can lead to Stripe retrying the webhook delivery.
