# Checkout API Documentation

This API endpoint handles the creation of a Stripe checkout session for purchasing products (like credits or subscriptions).

## Create Checkout Session

*   **Method:** `POST`
*   **Path:** `/api/checkout`
*   **Authentication:** User Required (must be signed in)
*   **Request Body:** `application/json`
    ```json
    {
      "credits": "number (required) - Number of credits being purchased (if applicable)",
      "currency": "string (required) - Currency code (e.g., 'usd', 'cny')",
      "amount": "number (required) - Amount in the smallest currency unit (e.g., cents for USD)",
      "interval": "string (required) - Billing interval ('year', 'month', or 'one-time')",
      "product_id": "string (required) - Identifier for the product being purchased",
      "product_name": "string (required) - Display name of the product",
      "valid_months": "number (required) - Duration the purchase is valid for (e.g., 1 for month, 12 for year)",
      "cancel_url": "string (optional) - URL to redirect to if the user cancels the checkout"
    }
    ```
*   **Responses:**
    *   `200 OK`: Checkout session created successfully.
        ```json
        {
          "code": 0, // Indicates success
          "message": "Success",
          "data": {
            "public_key": "string (Stripe public key)",
            "order_no": "string (Generated order number)",
            "session_id": "string (Stripe checkout session ID)"
          }
        }
        ```
    *   `200 OK` (with error): Request failed due to invalid parameters, authentication issues, or server errors.
        ```json
        {
          "code": -1, // Indicates error
          "message": "string (Error description, e.g., 'invalid params', 'no auth, please sign-in', 'checkout failed: ...')"
        }
        ```
    *   `500 Internal Server Error`: Unhandled server error during processing.

**Details:**

*   This endpoint first validates the incoming request parameters and user authentication status.
*   It creates an internal order record in the database.
*   It then communicates with the Stripe API to create a checkout session based on the provided product details.
*   The `interval` determines if it's a one-time payment (`"one-time"`) or a recurring subscription (`"month"` or `"year"`).
*   Supported payment methods may vary based on the `currency` (e.g., includes WeChat Pay and Alipay for 'cny').
*   On success, it returns the necessary information (`public_key`, `session_id`) for the frontend to redirect the user to the Stripe checkout page.
