import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { createProject, getProjectsByUser } from "@/models/links";
import { Project } from "@/types/links";
import { getCanonicalDomain, extractTopLevelDomain, validateDomainFormat } from "@/utils/url-normalization";
import { validateTierAccess, createTierErrorResponse, createTierSuccessResponse } from "@/lib/tier-middleware";
import { DomainModel } from "@/models/domain";


export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: projects, error } = await getProjectsByUser(user.uuid);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ projects });
  } catch (error) {
    console.error("Error fetching projects:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // First check basic authentication to ensure user is logged in
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Then validate tier access for project creation
    const tierResult = await validateTierAccess(request, { action: 'create_project' });
    
    if (!tierResult.success) {
      return createTierErrorResponse(tierResult);
    }

    const userUuid = tierResult.userUuid!;
    const body = await request.json();
    
    // Extract and validate domain - only allow top-level domains
    let domain = body.domain;
    if (!domain) {
      return NextResponse.json({ error: "Domain is required" }, { status: 400 });
    }

    const projectData: Omit<Project, 'id' | 'created_at' | 'updated_at'> = {
      ...body,
      domain,
      user_id: userUuid,
      total_links: 0,
      indexed_links: 0
    };

    const { data: project, error } = await createProject(projectData);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Auto-create domain record for the project's top-level domain
    try {
      const topLevelDomain = extractTopLevelDomain(domain);
      
      if (topLevelDomain && validateDomainFormat(topLevelDomain)) {
        // Check if domain already exists for this user
        const existingDomains = await DomainModel.getUserDomains(userUuid);
        const existingDomain = existingDomains.find(d => d.domain === topLevelDomain);
        
        if (existingDomain) {
          // Domain exists, just associate with project
          await DomainModel.associateDomainWithProject(userUuid, existingDomain.id, project.id, true);
          console.log(`Associated project ${project.id} with existing domain ${topLevelDomain}`);
        } else {
          // Create new domain record
          const domainData = await DomainModel.createOrUpdateDomain(userUuid, {
            domain: topLevelDomain,
            notes: `Auto-created from project: ${body.name || domain}`
          });
          
          // Associate with project
          await DomainModel.associateDomainWithProject(userUuid, domainData.id, project.id, true);
          console.log(`Created and associated new domain ${topLevelDomain} with project ${project.id}`);
          
          // Fetch WHOIS data in background (don't await)
          if (process.env.BACKEND_WORKER_URL) {
            fetch(`${process.env.BACKEND_WORKER_URL}/api/domain/whois?domain=${encodeURIComponent(topLevelDomain)}`)
              .then(async (whoisResponse) => {
                if (whoisResponse.ok) {
                  const fetchedWhoisData = await whoisResponse.json();
                  // Update domain with WHOIS data asynchronously
                  await DomainModel.updateDomainWhois(userUuid, topLevelDomain, fetchedWhoisData);
                }
              })
              .catch(error => {
                console.warn('Background WHOIS fetch failed:', error);
              });
          }
        }
      } else {
        console.warn(`Invalid top-level domain extracted from ${domain}: ${topLevelDomain}`);
      }
    } catch (domainError) {
      // Don't fail project creation if domain creation fails
      console.error("Error auto-creating domain for project:", domainError);
    }

    return await createTierSuccessResponse({ project }, userUuid);
  } catch (error) {
    console.error("Error creating project:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 