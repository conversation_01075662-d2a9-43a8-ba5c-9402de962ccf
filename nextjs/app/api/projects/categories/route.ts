import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getUserProjectCategories } from "@/models/links";

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: categories, error } = await getUserProjectCategories(user.uuid);

    if (error) {
      console.error("Error fetching project categories:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      categories: categories || [],
      total: categories?.length || 0
    });
  } catch (error) {
    console.error("Error in project categories API:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 