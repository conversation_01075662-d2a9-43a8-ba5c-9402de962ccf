import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getProjectById, updateProject } from "@/models/links";


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    console.log(`Fetching project info for project: ${project_id}, user: ${user.uuid}`);

    const { data: project, error } = await getProjectById(project_id, user.uuid);

    if (error) {
      console.error("Error fetching project:", error);
      return NextResponse.json(
        { error: "Project not found", details: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      info: project?.info || {},
      message: "Project info fetched successfully"
    });

  } catch (error) {
    console.error("Error in project info GET API:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    const body = await request.json();

    // Validate the request body
    const { favicon, introduction, sitemaps, robotsTxt, domainInfo } = body;

    const projectInfo = {
      ...(favicon && { favicon }),
      ...(introduction && { introduction }),
      ...(sitemaps && { sitemaps }),
      ...(robotsTxt && { robotsTxt }),
      ...(domainInfo && { domainInfo })
    };

    // First verify that the project belongs to the user
    const { data: existingProject, error: fetchError } = await getProjectById(
      project_id, 
      user.uuid
    );

    if (fetchError || !existingProject) {
      return NextResponse.json(
        { error: "Project not found or access denied" },
        { status: 404 }
      );
    }

    // Update project info
    const { data: updatedProject, error } = await updateProject(project_id, {
      info: projectInfo
    });

    if (error) {
      console.error("Error updating project info:", error);
      return NextResponse.json(
        { error: "Failed to update project info", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Project info updated successfully",
      project: updatedProject
    });

  } catch (error) {
    console.error("Error in project info PUT API:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}