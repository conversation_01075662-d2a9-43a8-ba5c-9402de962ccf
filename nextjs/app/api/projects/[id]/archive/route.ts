import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { updateProject, getProjectById } from "@/models/links";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { is_archived } = body;

    if (typeof is_archived !== 'boolean') {
      return NextResponse.json({ error: "is_archived must be a boolean" }, { status: 400 });
    }

    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    // First, check if the project exists and belongs to the user
    const { data: existingProject, error: fetchError } = await getProjectById(project_id, user.uuid);

    if (fetchError || !existingProject) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Update the project's archive status
    const updateData: any = { 
      is_archived,
    };

    if (is_archived) {
      updateData.archived_at = new Date().toISOString();
    } else {
      updateData.archived_at = null;
    }

    const { data: updatedProject, error: updateError } = await updateProject(project_id, updateData);

    if (updateError) {
      console.error("Error updating project archive status:", updateError);
      return NextResponse.json({ error: "Failed to update project archive status" }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      project: updatedProject,
      message: is_archived ? "Project archived successfully" : "Project restored successfully"
    });

  } catch (error) {
    console.error("Error archiving project:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 