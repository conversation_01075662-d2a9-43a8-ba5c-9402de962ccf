import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { updateProjectStats, getProjectWithStats } from "@/models/links";


export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    // Update project stats
    const { error } = await updateProjectStats(project_id);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Return updated project with stats
    const { data: updatedProject, error: fetchError } = await getProjectWithStats(project_id, user.uuid);
    
    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    if (!updatedProject) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true,
      project: updatedProject,
      message: "Project stats updated successfully"
    });
  } catch (error) {
    console.error("Error updating project stats:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}