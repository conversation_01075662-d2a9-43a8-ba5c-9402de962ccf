import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getUserConfig } from "@/models/user-configs";
import { getSupabaseClient } from "@/models/db";

interface AnalyticsConfig {
  provider: 'google' | 'plausible' | 'umami';
  api_key: string;
  website_id: string;
  base_url?: string;
  domain: string;
}

interface PageData {
  path: string;
  views: number;
  visitors: number;
  bounce_rate: number;
  percentage?: number;
}

// 获取项目的分析配置
async function getProjectAnalyticsConfig(projectId: string, provider: string): Promise<AnalyticsConfig | null> {
  try {
    // 首先获取项目信息，从中获取项目所有者的用户ID
    const supabase = getSupabaseClient();
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('user_id')
      .eq('uuid', projectId)
      .single();
    
    if (projectError || !project) {
      console.error('Project not found:', projectError?.message);
      return null;
    }

    // 首先尝试获取项目特定的analytics配置
    const { data: projectConfig, error: projectConfigError } = await getUserConfig(
      project.user_id,
      projectId,
      `analytics_${provider}`
    );
    
    if (projectConfig) {
      return {
        provider: provider as 'google' | 'plausible' | 'umami',
        api_key: projectConfig.configData.api_key,
        website_id: projectConfig.configData.website_id,
        base_url: projectConfig.configData.base_url,
        domain: projectConfig.configData.domain
      };
    }

    // 如果没有项目特定配置，尝试获取全局用户配置
    const { data: userConfig, error: userConfigError } = await getUserConfig(
      project.user_id,
      '', // 空字符串表示全局配置
      `analytics_${provider}`
    );
    
    if (userConfig) {
      return {
        provider: provider as 'google' | 'plausible' | 'umami',
        api_key: userConfig.configData.api_key,
        website_id: userConfig.configData.website_id,
        base_url: userConfig.configData.base_url,
        domain: userConfig.configData.domain
      };
    }

    return null;
  } catch (error) {
    console.error(`Error getting ${provider} analytics config:`, error);
    return null;
  }
}

// 从Plausible获取页面数据
async function fetchPlausiblePageData(config: AnalyticsConfig, timeRange: string, type: 'total' | 'recent' = 'total'): Promise<PageData[]> {
  try {
    const baseUrl = config.base_url || 'https://plausible.io';
    
    // 根据类型设置时间范围
    let period = timeRange;
    if (type === 'recent') {
      period = '7d'; // 近期增量固定7天
    }
    
    console.log(`Fetching Plausible page data for ${type}, period: ${period}`);
    
    // 使用 Plausible API v1 的 breakdown 端点获取页面数据
    const response = await fetch(`${baseUrl}/api/v1/stats/breakdown?site_id=${encodeURIComponent(config.website_id)}&period=${period}&property=event:page&metrics=visitors,pageviews,bounce_rate&limit=10`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.api_key}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const responseText = await response.text();
      console.error(`Plausible API error response: ${responseText}`);
      throw new Error(`Plausible API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Plausible API response:', data);
    
    if (!data.results || !Array.isArray(data.results)) {
      console.warn('Plausible API returned no results or invalid format');
      return [];
    }
    
    // 计算总页面浏览量用于百分比计算
    const totalPageviews = data.results.reduce((sum: number, item: any) => sum + (item.pageviews || 0), 0) || 1;
    
    return data.results.map((item: any) => ({
      path: item.page || '/',
      views: item.pageviews || 0,
      visitors: item.visitors || 0,
      bounce_rate: item.bounce_rate || 0,
      percentage: ((item.pageviews || 0) / totalPageviews * 100)
    }));

  } catch (error) {
    console.error('Error fetching Plausible page data:', error);
    return [];
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = id;
    const searchParams = request.nextUrl.searchParams;
    const timeRange = searchParams.get('timeRange') || '30d';
    const provider = searchParams.get('provider') || 'plausible';
    
    // 目前只支持Plausible
    if (provider !== 'plausible') {
      return NextResponse.json(
        { error: 'Currently only Plausible is supported for internal links data' },
        { status: 400 }
      );
    }

    // 获取项目的分析配置
    const integrationConfig = await getProjectAnalyticsConfig(projectId, provider);
    
    if (!integrationConfig) {
      return NextResponse.json(
        { 
          error: `No active ${provider} integration found for this project`,
          message: `Please configure ${provider} analytics integration first in the user settings.`,
          code: 'NO_INTEGRATION_CONFIGURED'
        },
        { status: 404 }
      );
    }

    // 获取总量top10和近期增量top10
    const [totalTop10, recentTop10] = await Promise.all([
      fetchPlausiblePageData(integrationConfig, timeRange, 'total'),
      fetchPlausiblePageData(integrationConfig, '7d', 'recent')
    ]);

    return NextResponse.json({
      success: true,
      data: {
        totalTop10,
        recentTop10,
        timeRange,
        provider,
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("Error fetching internal links data:", error);
    return NextResponse.json(
      { error: "Failed to fetch internal links data" },
      { status: 500 }
    );
  }
} 