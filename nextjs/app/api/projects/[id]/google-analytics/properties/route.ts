import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedSession } from '@/lib/auth';
import { userGoogleAnalyticsService } from '@/services/user-google-analytics';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const session = await getAuthenticatedSession();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 检查用户是否有Google访问令牌
    if (!session.googleAccessToken) {
      return NextResponse.json(
        { 
          error: 'Google Analytics not connected',
          message: 'Please re-login with Google to grant Analytics permissions'
        },
        { status: 400 }
      );
    }

    const tokens = {
      accessToken: session.googleAccessToken,
      refreshToken: session.googleRefreshToken,
      expiresAt: session.googleTokenExpiry,
      scope: session.googleScope
    };

    // 获取用户的Google Analytics属性
    const properties = await userGoogleAnalyticsService.getUserGoogleProperties(tokens);

    return NextResponse.json({
      success: true,
      data: {
        properties,
        hasPermissions: true
      }
    });

  } catch (error) {
    console.error('Error fetching Google Analytics properties:', error);
    
    if (error instanceof Error && error.message.includes('expired')) {
      return NextResponse.json(
        { 
          error: 'Token expired',
          message: 'Please re-login with Google to refresh your permissions'
        },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to fetch properties',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}