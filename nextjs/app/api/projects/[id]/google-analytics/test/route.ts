import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedSession } from '@/lib/auth';
import { userGoogleAnalyticsService } from '@/services/user-google-analytics';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const session = await getAuthenticatedSession();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 检查用户是否有Google访问令牌
    if (!session.googleAccessToken) {
      return NextResponse.json(
        { 
          error: 'Google not connected',
          message: 'Please login with Google to access Analytics and Search Console',
          connected: false,
          hasAnalytics: false,
          hasSearchConsole: false
        }
      );
    }

    const tokens = {
      accessToken: session.googleAccessToken,
      refreshToken: session.googleRefreshToken,
      expiresAt: session.googleTokenExpiry,
      scope: session.googleScope
    };

    // 测试连接和权限
    const connectionTest = await userGoogleAnalyticsService.testUserConnection(tokens);

    if (!connectionTest.isValid) {
      return NextResponse.json({
        error: connectionTest.error || 'Connection failed',
        connected: false,
        hasAnalytics: false,
        hasSearchConsole: false
      });
    }

    return NextResponse.json({
      success: true,
      connected: true,
      hasAnalytics: connectionTest.hasAnalytics,
      hasSearchConsole: connectionTest.hasSearchConsole,
      scopes: session.googleScope?.split(' ') || [],
      tokenExpiry: session.googleTokenExpiry,
      message: `Connected with ${connectionTest.hasAnalytics ? 'Analytics' : ''}${connectionTest.hasAnalytics && connectionTest.hasSearchConsole ? ' and ' : ''}${connectionTest.hasSearchConsole ? 'Search Console' : ''} access`
    });

  } catch (error) {
    console.error('Error testing Google connection:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to test connection',
        details: error instanceof Error ? error.message : 'Unknown error',
        connected: false,
        hasAnalytics: false,
        hasSearchConsole: false
      },
      { status: 500 }
    );
  }
}