import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedSession } from '@/lib/auth';
import { userGoogleAnalyticsService } from '@/services/user-google-analytics';

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const session = await getAuthenticatedSession();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 检查用户是否有Google访问令牌
    if (!session.googleAccessToken) {
      return NextResponse.json(
        { 
          error: 'Google Analytics not connected',
          message: 'Please re-login with Google to grant Analytics permissions'
        },
        { status: 400 }
      );
    }

    const { propertyId, startDate, endDate, dimensions, metrics } = await request.json();
    
    if (!propertyId) {
      return NextResponse.json(
        { error: 'Property ID is required' },
        { status: 400 }
      );
    }

    const tokens = {
      accessToken: session.googleAccessToken,
      refreshToken: session.googleRefreshToken,
      expiresAt: session.googleTokenExpiry,
      scope: session.googleScope
    };

    // 获取Analytics数据
    const analyticsData = await userGoogleAnalyticsService.getUserAnalyticsData(
      tokens,
      propertyId,
      {
        startDate: startDate || '30daysAgo',
        endDate: endDate || 'today'
      },
      dimensions || ['date'],
      metrics || ['activeUsers', 'sessions', 'screenPageViews']
    );

    return NextResponse.json({
      success: true,
      data: {
        analytics: analyticsData,
        propertyId,
        dateRange: {
          startDate: startDate || '30daysAgo',
          endDate: endDate || 'today'
        }
      }
    });

  } catch (error) {
    console.error('Error fetching Google Analytics data:', error);
    
    if (error instanceof Error && error.message.includes('expired')) {
      return NextResponse.json(
        { 
          error: 'Token expired',
          message: 'Please re-login with Google to refresh your permissions'
        },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to fetch analytics data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}