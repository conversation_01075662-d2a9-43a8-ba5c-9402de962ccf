import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { analyticsService } from "@/services/analytics";


// POST - Get websites list from analytics provider
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { provider, api_key, base_url } = body;

    console.log('Fetching websites for provider:', provider, 'with base_url:', base_url);

    if (!provider || !api_key) {
      return NextResponse.json({ 
        error: "Provider and API key are required" 
      }, { status: 400 });
    }

    // Validate provider
    if (!['plausible', 'umami'].includes(provider)) {
      return NextResponse.json({ 
        error: "Invalid provider. Supported: plausible, umami" 
      }, { status: 400 });
    }

    // For Plausible, base_url is optional (defaults to plausible.io)
    // For Umami, base_url is required
    if (provider === 'umami' && !base_url) {
      return NextResponse.json({ 
        error: "Base URL is required for Umami" 
      }, { status: 400 });
    }

    // Get websites list
    console.log('Calling analyticsService.getWebsites with config:', {
      provider,
      api_key: api_key ? '***hidden***' : 'missing',
      base_url,
      website_id: '',
      domain: ''
    });

    const result = await analyticsService.getWebsites({
      provider,
      api_key,
      website_id: '', // Not needed for listing websites
      base_url,
      domain: '' // Not needed for listing websites
    });

    console.log('getWebsites result:', { websites: result.websites?.length || 0, error: result.error });

    if (result.error) {
      return NextResponse.json({ 
        success: false,
        error: result.error
      }, { status: 400 });
    }

    return NextResponse.json({ 
      success: true,
      websites: result.websites
    });
  } catch (error) {
    console.error("Error fetching websites list:", error);
    return NextResponse.json({ 
      success: false,
      error: "Internal server error" 
    }, { status: 500 });
  }
}