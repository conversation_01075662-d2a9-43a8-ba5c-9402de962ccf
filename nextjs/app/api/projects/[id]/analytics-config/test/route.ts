import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { analyticsService } from "@/services/analytics";


// POST - Test analytics configuration
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      console.log("Analytics config test: Unauthorized user");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = id;
    console.log(`Analytics config test for project: ${projectId}`);
    
    let body;
    try {
      body = await request.json();
    } catch (e) {
      console.error("Analytics config test: Invalid JSON body", e);
      return NextResponse.json({ 
        error: "Invalid JSON in request body" 
      }, { status: 400 });
    }

    const { provider, api_key, website_id, base_url, domain } = body;
    console.log(`Analytics config test: provider=${provider}, domain=${domain}, has_api_key=${!!api_key}, website_id=${website_id}`);

    // Detailed validation with specific error messages
    if (!provider) {
      console.log("Analytics config test: Missing provider");
      return NextResponse.json({ 
        error: "Provider is required" 
      }, { status: 400 });
    }

    if (!api_key) {
      console.log("Analytics config test: Missing API key");
      return NextResponse.json({ 
        error: "API key is required" 
      }, { status: 400 });
    }

    if (!website_id) {
      console.log("Analytics config test: Missing website ID");
      return NextResponse.json({ 
        error: "Website ID is required" 
      }, { status: 400 });
    }

    if (!domain) {
      console.log("Analytics config test: Missing domain");
      return NextResponse.json({ 
        error: "Domain is required" 
      }, { status: 400 });
    }

    // Validate provider
    if (!['google', 'plausible', 'umami'].includes(provider)) {
      console.log(`Analytics config test: Invalid provider: ${provider}`);
      return NextResponse.json({ 
        error: "Invalid provider. Supported: google, plausible, umami" 
      }, { status: 400 });
    }

    // For Umami, base_url is required
    if (provider === 'umami' && !base_url) {
      console.log("Analytics config test: Missing base_url for Umami");
      return NextResponse.json({ 
        error: "Base URL is required for Umami analytics" 
      }, { status: 400 });
    }

    console.log(`Analytics config test: Starting connection test for ${provider}`);

    // Test the configuration with timeout
    const testPromise = analyticsService.testConnection({
      provider,
      api_key,
      website_id,
      base_url,
      domain
    });

    // Add timeout to prevent hanging requests
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Connection test timed out')), 30000); // 30 second timeout
    });

    let testResult;
    try {
      testResult = await Promise.race([testPromise, timeoutPromise]);
      console.log(`Analytics config test result for ${provider}:`, { isValid: testResult.isValid, hasError: !!testResult.error });
    } catch (timeoutError) {
      console.error(`Analytics config test timeout for ${provider}:`, timeoutError);
      return NextResponse.json({ 
        success: false,
        error: "Connection test timed out",
        details: "The analytics service took too long to respond. Please check your configuration and try again."
      }, { status: 400 });
    }
    
    if (!testResult.isValid) {
      console.log(`Analytics config test failed for ${provider}:`, testResult.error);
      return NextResponse.json({ 
        success: false,
        error: "Configuration test failed",
        details: testResult.error
      }, { status: 400 });
    }

    console.log(`Analytics config test successful for ${provider}`);
    return NextResponse.json({ 
      success: true,
      message: "Configuration test successful",
      details: testResult.data || "Connection established successfully"
    });
  } catch (error: any) {
    console.error("Error testing analytics config:", error);
    return NextResponse.json({ 
      success: false,
      error: "Internal server error",
      details: error.message || "Unknown error occurred"
    }, { status: 500 });
  }
}