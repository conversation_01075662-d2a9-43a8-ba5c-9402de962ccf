import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getProjectById, getLinksByProject, batchCreateDiscoveredLinks } from "@/models/links";
import { DiscoveredLink } from "@/types/links";
import { getCanonicalDomain } from "@/utils/url-normalization";


export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = user.uuid;
    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    // Get project details
    const { data: project, error: projectError } = await getProjectById(project_id, userId);
    if (projectError || !project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Get all links from the project
    const { data: links, error: linksError } = await getLinksByProject(project_id, userId);
    if (linksError) {
      return NextResponse.json({ error: "Failed to fetch project links" }, { status: 500 });
    }

    if (!links || links.length === 0) {
      return NextResponse.json({
        message: "No links found in project",
        newLinks: 0,
        updated: 0,
        failed: 0,
        errors: []
      });
    }

    // Use canonical domain for proper normalization (consistent with other parts of the system)
    const projectNormalizedDomain = getCanonicalDomain(project.domain) || project.domain;
    console.log(`Project domain: ${project.domain}, normalized: ${projectNormalizedDomain}`);
    console.log(`Found ${links.length} links in project`);

    // Convert all links to discovered links format
    // These represent external platforms where the user can publish/share their product
    // All links from the "Link Resources" collection are potential promotional opportunities
    const discoveredLinksData = links
      .filter(link => {
        // Include all valid links as potential promotional platforms
        // No status filtering since status field is deprecated
        const hasValidUrl = link.url && link.url.trim() !== '';
        console.log(`Link: ${link.url}, title: ${link.title}, hasValidUrl: ${hasValidUrl}`);
        return hasValidUrl;
      })
      .map(link => {
        // Extract canonical domain for consistency
        const linkDomain = getCanonicalDomain(link.url) || 'unknown';

        // Create discovered link entry representing a promotional opportunity
        return {
          url: link.url, // The platform URL where user can publish
          title: link.title || `Promotional opportunity on ${linkDomain}`,
          anchor_text: link.title || `Promote on ${linkDomain}`,
          link_type: 'dofollow' as const, // Default assumption
          discovered_at: link.created_at,
          source_url: link.url, // The platform URL
          is_active: true, // Mark as active promotional opportunity
          status: 'NEW' as const,
          project_id,
          user_id: userId
        };
      });

    console.log(`Found ${discoveredLinksData.length} promotional platforms from ${links.length} total links`);

    if (discoveredLinksData.length === 0) {
      const linkInfo = links.map(link => {
        return `${link.url} -> Title: ${link.title || 'No title'}`;
      });

      return NextResponse.json({
        message: "No valid promotional platforms found to sync",
        note: `Found ${links.length} links but none have valid URLs`,
        debug: {
          totalLinks: links.length,
          linkInfo: linkInfo
        },
        newLinks: 0,
        updated: 0,
        failed: 0,
        errors: []
      });
    }

    // Batch create/update discovered links with deduplication
    const { success, failed, errors } = await batchCreateDiscoveredLinks(
      discoveredLinksData,
      project_id,
      userId
    );

    return NextResponse.json({
      message: `Promotional platforms sync completed`,
      newLinks: success,
      updated: 0, // The batchCreateDiscoveredLinks handles updates internally
      failed,
      errors: errors.slice(0, 5), // Limit error messages
      note: success > 0 ? 
        `Successfully synced ${success} promotional platforms from Link Resources to project's external link opportunities` :
        "No new promotional platforms were added (they may already exist)"
    });

  } catch (error) {
    console.error("Error in POST /api/projects/[id]/sync-external-links:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 