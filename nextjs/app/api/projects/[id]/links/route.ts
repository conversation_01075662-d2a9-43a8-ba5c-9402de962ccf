import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getLinksByProject } from "@/models/links";


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    const { data: links, error } = await getLinksByProject(project_id, user.uuid);

    if (error) {
      console.error("Error fetching project links:", error);
      return NextResponse.json({ error: "Failed to fetch links" }, { status: 500 });
    }

    return NextResponse.json({ links });

  } catch (error) {
    console.error("Error in GET /api/projects/[id]/links:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 