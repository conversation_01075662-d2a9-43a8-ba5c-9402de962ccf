import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getProjectStats, updateProjectSummaryStats, createProjectStats, getDomainFullStats, getProjectByIdForAnalytics } from "@/models/links";


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    // Get project stats history
    const { data: stats, error } = await getProjectStats(project_id);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ stats: stats || [] });
  } catch (error) {
    console.error("Error fetching project stats:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'refresh') {
      // Update project summary stats first
      // const { error: updateError } = await updateProjectSummaryStats(project_id);
      
      // if (updateError) {
      //   return NextResponse.json({ error: updateError.message }, { status: 500 });
      // }

      // Get current domain stats and record new stats entry
      // const { data: project, error: projectFetchError } = await getProjectByIdForAnalytics(project_id);
      
      // if (projectFetchError || !project) {
      //   return NextResponse.json({ error: "Project not found" }, { status: 404 });
      // }

      // // Get current domain stats from all_links table
      // const { data: domainStats } = await getDomainFullStats(project.domain);
      
      // // Use actual data from database, fallback to defaults if no data available
      // const currentDrScore = domainStats?.dr_score || 0;
      // const currentTraffic = domainStats?.traffic || 0;
      // const currentBacklinkCount = domainStats?.backlink_count || 0;
      // const currentIsIndexed = domainStats?.is_indexed || false;

      // const { error: recordError } = await createProjectStats({
      //   project_id,
      //   dr_score: currentDrScore,
      //   traffic: currentTraffic,
      //   backlink_count: currentBacklinkCount,
      //   total_links: currentBacklinkCount, // Use backlink_count as total_links
      //   indexed_links: currentIsIndexed ? currentBacklinkCount : 0,
      //   is_indexed: currentIsIndexed,
      //   checked_at: new Date().toISOString()
      // });
      
      // if (recordError) {
      //   return NextResponse.json({ error: recordError.message }, { status: 500 });
      // }

      // Get updated stats
      const { data: stats, error: fetchError } = await getProjectStats(project_id);
      
      if (fetchError) {
        return NextResponse.json({ error: fetchError.message }, { status: 500 });
      }

      console.log("stats for project", project_id, stats);

      return NextResponse.json({ 
        success: true, 
        message: 'Project stats updated successfully',
        stats: stats || []
      });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error updating project stats:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 