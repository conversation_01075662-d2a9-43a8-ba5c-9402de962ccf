import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { 
  saveGoogleSearchConsoleConfig, 
  getUserGoogleSearchConsoleConfig,
  deleteUserConfig
} from "@/models/user-configs";
import { googleSearchConsoleServiceEdge } from "@/services/google-search-console-edge";
import { GoogleSearchConsoleConfig } from "@/models/user-configs";



// GET - Get project's Google Search Console configuration
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = id;
    const { data: config, error } = await getUserGoogleSearchConsoleConfig(
      user.uuid,
      projectId
    );
    
    if (error) {
      return NextResponse.json({ error: "Failed to get configuration" }, { status: 500 });
    }

    // Don't return the actual private key data, just metadata
    const response = config ? {
      id: config.id,
      configName: config.configName,
      isActive: config.isActive,
      created_at: config.created_at,
      updated_at: config.updated_at,
      clientEmail: config.configData?.client_email || null,
      project_id: config.configData?.project_id || null
    } : null;

    return NextResponse.json({ config: response });
  } catch (error) {
    console.error("Error getting Google Search Console config:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST - Save/Update Google Search Console configuration for project
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = id;
    const body = await request.json();
    const { configName, configData, testConnection } = body;

    if (!configName || !configData) {
      return NextResponse.json({ 
        error: "Configuration name and data are required" 
      }, { status: 400 });
    }

    // Validate the configuration structure
    const requiredFields = [
      'type', 'project_id', 'private_key_id', 'private_key', 
      'client_email', 'client_id', 'auth_uri', 'token_uri'
    ];

    for (const field of requiredFields) {
      if (!configData[field]) {
        return NextResponse.json({ 
          error: `Missing required field: ${field}` 
        }, { status: 400 });
      }
    }

    // Test the configuration if requested
    if (testConnection) {
      const testResult = await googleSearchConsoleServiceEdge.testConfiguration(configData);
      if (!testResult.success) {
        return NextResponse.json({ 
          error: "Configuration test failed",
          details: testResult.message
        }, { status: 400 });
      }
    }

    // Save the configuration
    const { data: savedConfig, error } = await saveGoogleSearchConsoleConfig(
      user.uuid,
      projectId,
      configName,
      configData
    );

    if (error) {
      return NextResponse.json({ 
        error: "Failed to save configuration" 
      }, { status: 500 });
    }

    // Return response without sensitive data
    const response = {
      id: savedConfig?.id,
      configName: savedConfig?.configName,
      isActive: savedConfig?.isActive,
      created_at: savedConfig?.created_at,
      updated_at: savedConfig?.updated_at,
      clientEmail: configData.client_email,
      project_id: configData.project_id
    };

    return NextResponse.json({ 
      message: "Configuration saved successfully",
      config: response
    });
  } catch (error) {
    console.error("Error saving Google Search Console config:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE - Delete Google Search Console configuration for project
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const projectId = id;
    const { error } = await deleteUserConfig(
      user.uuid,
      projectId,
      'google_search_console'
    );
    
    if (error) {
      return NextResponse.json({ 
        error: "Failed to delete configuration" 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      message: "Configuration deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting Google Search Console config:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}