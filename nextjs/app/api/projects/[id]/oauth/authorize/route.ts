import { NextRequest, NextResponse } from "next/server";
import { getNextAuthSession } from "@/lib/auth-utils";
import { oauthTokenManager, OAuthTokenType } from "@/services/oauth-token-manager";
import { z } from "zod";

const authorizeSchema = z.object({
  tokenType: z.enum(['analytics', 'search_console', 'combined']),
  scopes: z.array(z.string()).optional(),
  redirectUrl: z.string().url().optional()
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getNextAuthSession();
    if (!session?.user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: projectId } = await params;
    const userId = session.user.uuid;
    
    const body = await request.json();
    const { tokenType, redirectUrl } = authorizeSchema.parse(body);

    // Check if user already has valid tokens for this service
    try {
      const existingTokens = await oauthTokenManager.getStoredTokenSet(
        userId, 
        projectId, 
        tokenType as OAuthTokenType
      );
      
      if (existingTokens && existingTokens.expiresAt > Date.now() + (5 * 60 * 1000)) {
        return NextResponse.json(
          { error: "OAuth tokens already exist and are valid" },
          { status: 400 }
        );
      }
    } catch (error) {
      // No existing tokens, proceed with authorization
    }

    // Generate authorization URL
    const { authUrl, state } = await oauthTokenManager.initiateProgressiveAuth(
      userId,
      projectId,
      tokenType as OAuthTokenType,
      redirectUrl
    );

    return NextResponse.json({
      authUrl,
      state,
      tokenType
    });
  } catch (error) {
    console.error("Error initiating OAuth flow:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to initiate OAuth flow" },
      { status: 500 }
    );
  }
}