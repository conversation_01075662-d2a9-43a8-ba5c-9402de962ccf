import { NextRequest, NextResponse } from "next/server";
import { getNextAuthSession } from "@/lib/auth-utils";
import { oauthTokenManager, OAuthError } from "@/services/oauth-token-manager";
import { z } from "zod";

const callbackSchema = z.object({
  code: z.string().min(1, "Authorization code is required"),
  state: z.string().min(1, "State parameter is required"),
  tokenType: z.string().optional()
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getNextAuthSession();
    if (!session?.user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: projectId } = await params;
    const userId = session.user.uuid;
    
    const body = await request.json();
    const { code, state } = callbackSchema.parse(body);

    // Exchange authorization code for tokens
    const tokenSet = await oauthTokenManager.exchangeCodeForTokens(code, state);

    // Verify the token set belongs to the correct user and project
    if (tokenSet.userId !== userId || tokenSet.projectId !== projectId) {
      return NextResponse.json(
        { error: "Token verification failed" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      tokenType: tokenSet.tokenType,
      scopes: tokenSet.scopes,
      expiresAt: tokenSet.expiresAt
    });
  } catch (error) {
    console.error("Error handling OAuth callback:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof OAuthError) {
      return NextResponse.json(
        { 
          error: error.message,
          type: error.type,
          requiresReauth: error.requiresReauth
        },
        { status: error.requiresReauth ? 401 : 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to complete OAuth flow" },
      { status: 500 }
    );
  }
}