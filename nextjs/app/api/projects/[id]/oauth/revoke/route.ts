import { NextRequest, NextResponse } from "next/server";
import { getNextAuthSession } from "@/lib/auth-utils";
import { oauthTokenManager, OAuthTokenType } from "@/services/oauth-token-manager";
import { z } from "zod";

const revokeSchema = z.object({
  tokenType: z.enum(['basic', 'analytics', 'search_console', 'combined'])
});

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getNextAuthSession();
    if (!session?.user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: projectId } = await params;
    const userId = session.user.uuid;
    
    const body = await request.json();
    const { tokenType } = revokeSchema.parse(body);

    // Revoke the tokens
    await oauthTokenManager.revokeTokenSet(
      userId,
      projectId,
      tokenType as OAuthTokenType
    );

    return NextResponse.json({
      success: true,
      message: `${tokenType} tokens revoked successfully`
    });
  } catch (error) {
    console.error("Error revoking OAuth tokens:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to revoke OAuth tokens" },
      { status: 500 }
    );
  }
}