import { NextRequest, NextResponse } from "next/server";
import { getNextAuthSession } from "@/lib/auth-utils";
import { oauthTokenManager, OAuthError } from "@/services/oauth-token-manager";
import { google } from 'googleapis';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getNextAuthSession();
    if (!session?.user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: projectId } = await params;
    const userId = session.user.uuid;
    
    const { searchParams } = new URL(request.url);
    const tokenType = searchParams.get('tokenType');

    if (!tokenType || !['analytics', 'search_console'].includes(tokenType)) {
      return NextResponse.json(
        { error: "Invalid or missing tokenType parameter" },
        { status: 400 }
      );
    }

    try {
      // Get valid token set for the service
      const tokenSet = await oauthTokenManager.getValidTokenSet(
        userId,
        projectId,
        tokenType as 'analytics' | 'search_console'
      );

      // Create OAuth client
      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({
        access_token: tokenSet.accessToken,
        refresh_token: tokenSet.refreshToken,
        expiry_date: tokenSet.expiresAt
      });

      let hasAnalytics = false;
      let hasSearchConsole = false;
      let error: string | undefined;

      // Test Analytics access
      if (tokenType === 'analytics' || tokenSet.tokenType === 'combined') {
        try {
          const analyticsAdmin = google.analyticsadmin({ version: 'v1alpha', auth: oauth2Client });
          await analyticsAdmin.accounts.list();
          hasAnalytics = true;
        } catch (analyticsError: any) {
          console.warn('No Google Analytics access:', analyticsError.message);
          if (tokenType === 'analytics') {
            error = `Analytics access failed: ${analyticsError.message}`;
          }
        }
      }

      // Test Search Console access
      if (tokenType === 'search_console' || tokenSet.tokenType === 'combined') {
        try {
          const searchconsole = google.searchconsole({ version: 'v1', auth: oauth2Client });
          await searchconsole.sites.list();
          hasSearchConsole = true;
        } catch (searchError: any) {
          console.warn('No Google Search Console access:', searchError.message);
          if (tokenType === 'search_console') {
            error = `Search Console access failed: ${searchError.message}`;
          }
        }
      }

      const isValid = (tokenType === 'analytics' && hasAnalytics) || 
                     (tokenType === 'search_console' && hasSearchConsole) ||
                     (tokenSet.tokenType === 'combined' && (hasAnalytics || hasSearchConsole));

      return NextResponse.json({
        isValid,
        hasAnalytics,
        hasSearchConsole,
        tokenType: tokenSet.tokenType,
        scopes: tokenSet.scopes,
        expiresAt: tokenSet.expiresAt,
        error
      });
    } catch (tokenError) {
      if (tokenError instanceof OAuthError) {
        return NextResponse.json({
          isValid: false,
          hasAnalytics: false,
          hasSearchConsole: false,
          error: tokenError.message,
          type: tokenError.type,
          requiresReauth: tokenError.requiresReauth
        });
      }
      throw tokenError;
    }
  } catch (error) {
    console.error("Error testing OAuth connection:", error);
    
    return NextResponse.json({
      isValid: false,
      hasAnalytics: false,
      hasSearchConsole: false,
      error: "Failed to test OAuth connection"
    }, { status: 500 });
  }
}