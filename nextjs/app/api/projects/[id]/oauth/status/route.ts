import { NextRequest, NextResponse } from "next/server";
import { getNextAuthSession } from "@/lib/auth-utils";
import { oauthTokenManager } from "@/services/oauth-token-manager";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getNextAuthSession();
    if (!session?.user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: projectId } = await params;
    const userId = session.user.uuid;

    // Get OAuth status for all token types
    const status = await oauthTokenManager.getOAuthStatus(userId, projectId);

    return NextResponse.json(status);
  } catch (error) {
    console.error("Error checking OAuth status:", error);
    return NextResponse.json(
      { error: "Failed to check OAuth status" },
      { status: 500 }
    );
  }
}