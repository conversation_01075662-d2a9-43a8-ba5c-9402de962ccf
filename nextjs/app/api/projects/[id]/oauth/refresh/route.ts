import { NextRequest, NextResponse } from "next/server";
import { getNextAuthSession } from "@/lib/auth-utils";
import { oauthTokenManager, OAuthTokenType, OAuthError } from "@/services/oauth-token-manager";
import { z } from "zod";

const refreshSchema = z.object({
  tokenType: z.enum(['basic', 'analytics', 'search_console', 'combined'])
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getNextAuthSession();
    if (!session?.user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: projectId } = await params;
    const userId = session.user.uuid;
    
    const body = await request.json();
    const { tokenType } = refreshSchema.parse(body);

    // Get the existing token set
    const existingTokenSet = await oauthTokenManager.getStoredTokenSet(
      userId,
      projectId,
      tokenType as OAuthTokenType
    );

    if (!existingTokenSet) {
      return NextResponse.json(
        { error: "No tokens found for refresh" },
        { status: 404 }
      );
    }

    // Refresh the tokens
    const refreshedTokenSet = await oauthTokenManager.refreshTokens(existingTokenSet);

    return NextResponse.json({
      success: true,
      accessToken: refreshedTokenSet.accessToken,
      expiresAt: refreshedTokenSet.expiresAt,
      lastRefreshed: refreshedTokenSet.lastRefreshedAt
    });
  } catch (error) {
    console.error("Error refreshing OAuth tokens:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof OAuthError) {
      return NextResponse.json(
        { 
          error: error.message,
          type: error.type,
          requiresReauth: error.requiresReauth
        },
        { status: error.requiresReauth ? 401 : 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to refresh OAuth tokens" },
      { status: 500 }
    );
  }
}