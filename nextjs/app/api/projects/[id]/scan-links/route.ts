import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { isAdminAuthenticated, unauthorizedResponse } from "@/lib/adminAuth";
import { getProjectById } from "@/models/links";


export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is admin for this premium feature
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse("This feature is restricted to administrators during beta period");
    }

    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = user.uuid;
    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    // Get project details to verify ownership
    const { data: project, error: projectError } = await getProjectById(project_id, userId);
    if (process.env.NODE_ENV == "production"  && (projectError || !project)) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Call the worker's /api/external-links API endpoint for this specific domain
    const workerUrl = process.env.BACKEND_WORKER_URL;
    if (!workerUrl) {
      return NextResponse.json({ 
        error: "Worker service not configured",
        message: "Unable to trigger link discovery. BACKEND_WORKER_URL not found in environment."
      }, { status: 500 });
    }

    try {
      const apiUrl = `${workerUrl}/api/external-links?domain=${encodeURIComponent(project.domain)}`;
      
      // Prepare authorization headers if needed
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };
      
      // Add authentication for production environment
      const workerAuth = process.env.BACKEND_WORKER_API_KEY;
      if (workerAuth) {
        headers['Authorization'] = `Bearer ${workerAuth}`;
      }

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Worker API error: ${response.status} - ${errorText}`);
        return NextResponse.json({ 
          error: "Link discovery failed",
          message: `Worker service returned ${response.status}: ${errorText}`
        }, { status: 500 });
      }

      const result = await response.json();
      console.log(`Worker response for ${project.domain}:`, result);

      // Parse the worker response
      if (result.success && result.data) {
        const discoveredLinks = result.data.discovered_links || [];
        const totalLinks = result.data.total_links || 0;
        
        return NextResponse.json({
          message: "Link discovery completed successfully",
          domain: project.domain,
          newLinks: discoveredLinks.length,
          totalExternalLinks: totalLinks,
          discoveredLinks: discoveredLinks.slice(0, 10), // Show first 10 links as preview
          source: result.data.source || 'unknown',
          timestamp: result.data.timestamp,
          duration: result.duration
        });
      } else {
        return NextResponse.json({
          message: "Link discovery completed with issues",
          domain: project.domain,
          error: result.data?.error || "Unknown error",
          newLinks: 0,
          totalExternalLinks: 0
        });
      }

    } catch (error: any) {
      console.error("Error calling worker API:", error);
      return NextResponse.json({ 
        error: "Failed to trigger link discovery",
        message: error.message || "Unable to communicate with worker service"
      }, { status: 500 });
    }

  } catch (error) {
    console.error("Error in POST /api/projects/[id]/scan-links:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 