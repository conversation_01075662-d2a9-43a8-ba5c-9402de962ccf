import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { 
  getDiscoveredLinkById,
  archiveDiscoveredLink,
  updateDiscoveredLink 
} from "@/models/links";


export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; linkId: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, linkId } = await params;
    const projectId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(projectId) || !uuidRegex.test(linkId)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 });
    }

    // First verify that the discovered link belongs to the user and project
    const { data: discoveredLink, error: fetchError } = await getDiscoveredLinkById(
      linkId,
      projectId,
      user.uuid
    );

    if (fetchError || !discoveredLink) {
      return NextResponse.json(
        { error: "Discovered link not found or access denied" },
        { status: 404 }
      );
    }

    // Archive the discovered link
    const { error: updateError } = await archiveDiscoveredLink(linkId);

    if (updateError) {
      console.error("Error archiving discovered link:", updateError);
      return NextResponse.json(
        { error: "Failed to archive discovered link", details: updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Discovered link archived successfully"
    });

  } catch (error) {
    console.error("Error in discovered link delete API:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; linkId: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, linkId } = await params;
    const projectId = id;
    const body = await request.json();
    const { 
      status, 
      referral_traffic, 
      analytics_source, 
      traffic_period, 
      last_traffic_update,
      traffic_contribution_percentage 
    } = body;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(projectId) || !uuidRegex.test(linkId)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 });
    }

    // First verify that the discovered link belongs to the user and project
    const { data: discoveredLink, error: fetchError } = await getDiscoveredLinkById(
      linkId,
      projectId,
      user.uuid
    );

    if (fetchError || !discoveredLink) {
      return NextResponse.json(
        { error: "Discovered link not found or access denied" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    // Handle status update
    if (status !== undefined) {
      const validStatuses = ['NEW', 'SUBMITTED', 'INDEXED', 'ARCHIVED'];
      if (!validStatuses.includes(status)) {
        return NextResponse.json({ error: "Invalid status value" }, { status: 400 });
      }
      updateData.status = status;
      updateData.is_active = status !== 'ARCHIVED';
    }

    // Handle traffic contribution data update
    if (referral_traffic !== undefined) {
      updateData.referral_traffic = referral_traffic;
    }
    if (analytics_source !== undefined) {
      const validSources = ['plausible', 'google-analytics', 'umami', 'manual'];
      if (!validSources.includes(analytics_source)) {
        return NextResponse.json({ error: "Invalid analytics source" }, { status: 400 });
      }
      updateData.analytics_source = analytics_source;
    }
    if (traffic_period !== undefined) {
      updateData.traffic_period = traffic_period;
    }
    if (last_traffic_update !== undefined) {
      updateData.last_traffic_update = last_traffic_update;
    }
    if (traffic_contribution_percentage !== undefined) {
      updateData.traffic_contribution_percentage = traffic_contribution_percentage;
    }

    // Update the discovered link using model function
    const { error: updateError } = await updateDiscoveredLink(linkId, updateData);

    if (updateError) {
      console.error("Error updating discovered link:", updateError);
      return NextResponse.json(
        { error: "Failed to update discovered link", details: updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Discovered link updated successfully",
      updated_fields: Object.keys(updateData).filter(key => key !== 'updated_at')
    });

  } catch (error) {
    console.error("Error in discovered link update API:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}