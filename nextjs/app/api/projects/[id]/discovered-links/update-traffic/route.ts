import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { isAdminAuthenticated, unauthorizedResponse } from "@/lib/adminAuth";
import { getProjectById } from "@/models/links";
import { batchUpdateProjectTrafficContribution } from "@/services/analytics-traffic";


export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is admin for this premium feature
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse("This feature is restricted to administrators during beta period");
    }

    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = user.uuid;
    const { id } = await params;
    const projectId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(projectId)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    // Get request body
    const body = await request.json();
    const { analytics_config, period = 'last_30_days' } = body;

    if (!analytics_config) {
      return NextResponse.json({ 
        error: "Analytics configuration is required",
        message: "Please provide analytics_config with platform, domain, and API credentials"
      }, { status: 400 });
    }

    const { platform, domain, api_key, property_id, website_id } = analytics_config;

    if (!platform || !domain) {
      return NextResponse.json({
        error: "Invalid analytics configuration",
        message: "Platform and domain are required"
      }, { status: 400 });
    }

    if (!api_key && platform !== 'manual') {
      return NextResponse.json({
        error: "API key is required",
        message: `API key is required for ${platform} analytics`
      }, { status: 400 });
    }

    // Get project details
    const { data: project, error: projectError } = await getProjectById(projectId, userId);
    if (projectError || !project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Prepare analytics configuration
    const analyticsConfig = {
      platform: platform as 'plausible' | 'google-analytics' | 'umami',
      apiKey: api_key,
      propertyId: property_id,
      websiteId: website_id,
      domain: domain,
    };

    // Validate platform-specific requirements
    if (platform === 'google-analytics' && !property_id) {
      return NextResponse.json({
        error: "Property ID is required for Google Analytics",
        message: "Please provide property_id for Google Analytics integration"
      }, { status: 400 });
    }

    if (platform === 'umami' && !website_id) {
      return NextResponse.json({
        error: "Website ID is required for Umami",
        message: "Please provide website_id for Umami integration"
      }, { status: 400 });
    }

    // Update traffic contribution for all discovered links
    const results = await batchUpdateProjectTrafficContribution(
      projectId,
      analyticsConfig,
      period
    );

    if (results.errors.length > 0) {
      console.error("Traffic update errors:", results.errors);
    }

    return NextResponse.json({
      message: "Traffic contribution update completed",
      results: {
        updated: results.updated,
        failed: results.failed,
        total: results.updated + results.failed,
        period: period,
        platform: platform,
        domain: domain
      },
      errors: results.errors.slice(0, 5), // Limit error messages
      note: results.updated > 0 ? 
        `Successfully updated traffic data for ${results.updated} discovered links from ${platform}` :
        `No traffic data was updated. Please check your analytics configuration and API credentials.`
    });

  } catch (error) {
    console.error("Error in POST /api/projects/[id]/discovered-links/update-traffic:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      message: "Failed to update traffic contribution data"
    }, { status: 500 });
  }
}