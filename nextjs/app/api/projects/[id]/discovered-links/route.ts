import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { 
  getDiscoveredLinksWithStats, 
  getDiscoveredLinkById,
  convertDiscoveredLinkToResource 
} from "@/models/links";


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    console.log(`Fetching discovered links for project: ${project_id}, user: ${user.uuid}`);

    const { data: links, error } = await getDiscoveredLinksWithStats(project_id, user.uuid);

    if (error) {
      console.error("Error fetching discovered links:", error);
      return NextResponse.json(
        { error: "Failed to fetch discovered links", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      links: links || [],
      message: "Discovered links fetched successfully"
    });

  } catch (error) {
    console.error("Error in discovered-links API:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const project_id = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(project_id)) {
      return NextResponse.json({ error: "Invalid project ID format" }, { status: 400 });
    }

    const body = await request.json();
    const { discoveredLinkId, linkResourceData } = body;

    if (!discoveredLinkId) {
      return NextResponse.json({ error: "Discovered link ID is required" }, { status: 400 });
    }

    if (!uuidRegex.test(discoveredLinkId)) {
      return NextResponse.json({ error: "Invalid discovered link ID format" }, { status: 400 });
    }

    // First verify that the discovered link belongs to the user and project
    const { data: discoveredLink, error: fetchError } = await getDiscoveredLinkById(
      discoveredLinkId, 
      project_id, 
      user.uuid
    );

    if (fetchError || !discoveredLink) {
      return NextResponse.json(
        { error: "Discovered link not found or access denied" },
        { status: 404 }
      );
    }

    // Convert discovered link to link resource
    const { data: linkResource, error: createError } = await convertDiscoveredLinkToResource(
      discoveredLinkId,
      linkResourceData
    );

    if (createError) {
      console.error("Error creating link resource:", createError);
      return NextResponse.json(
        { error: "Failed to create link resource", details: createError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      linkResource,
      message: "Successfully converted discovered link to link resource"
    });

  } catch (error) {
    console.error("Error in discovered-links POST API:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
} 