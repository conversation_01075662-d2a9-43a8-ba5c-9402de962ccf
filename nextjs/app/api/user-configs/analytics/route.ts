import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { 
  saveUserConfig, 
  getUserConfig,
  deleteUserConfig
} from "@/models/user-configs";
import { analyticsService } from "@/services/analytics";


interface AnalyticsConfig {
  provider: 'google' | 'plausible' | 'umami';
  api_key: string;
  website_id: string;
  base_url?: string;
  domain: string;
}

// GET - Get user's analytics configurations
export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const provider = url.searchParams.get('provider');

    if (provider) {
      // Get specific provider config (using empty project_id for global user configs)
      const { data: config, error } = await getUserConfig(
        user.uuid, 
        '', // project_id - empty for global user configs
        `analytics_${provider}`
      );
      
      if (error) {
        return NextResponse.json({ error: "Failed to get configuration" }, { status: 500 });
      }

      // Don't return sensitive data like API keys
      const response = config ? {
        id: config.id,
        provider: provider,
        configName: config.configName,
        isActive: config.isActive,
        created_at: config.created_at,
        updated_at: config.updated_at,
        website_id: config.configData?.website_id || null,
        base_url: config.configData?.base_url || null,
        domain: config.configData?.domain || null
      } : null;

      console.log('response', response);

      return NextResponse.json({ config: response });
    } else {
      // Get all analytics configs
      const configs = [];
      const providers = ['google', 'plausible', 'umami'];
      
      for (const provider of providers) {
        const { data: config } = await getUserConfig(
          user.uuid, 
          '', // project_id - empty for global user configs
          `analytics_${provider}`
        );
        
        if (config) {
          configs.push({
            id: config.id,
            provider: provider,
            configName: config.configName,
            isActive: config.isActive,
            created_at: config.created_at,
            updated_at: config.updated_at,
            website_id: config.configData?.website_id || null,
            base_url: config.configData?.base_url || null,
            domain: config.configData?.domain || null
          });
        }
      }

      return NextResponse.json({ configs });
    }
  } catch (error) {
    console.error("Error getting analytics config:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST - Save/Update analytics configuration
export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { provider, configName, configData, testConnection } = body;

    if (!provider || !configName || !configData) {
      return NextResponse.json({ 
        error: "Provider, configuration name and data are required" 
      }, { status: 400 });
    }

    // Validate provider
    if (!['google', 'plausible', 'umami'].includes(provider)) {
      return NextResponse.json({ 
        error: "Invalid provider. Supported: google, plausible, umami" 
      }, { status: 400 });
    }

    // Validate required fields
    const requiredFields = ['api_key', 'website_id', 'domain'];
    for (const field of requiredFields) {
      if (!configData[field]) {
        return NextResponse.json({ 
          error: `Missing required field: ${field}` 
        }, { status: 400 });
      }
    }

    // Test the configuration if requested
    if (testConnection) {
      const testResult = await analyticsService.testConnection({
        provider,
        ...configData
      });
      
      if (!testResult.isValid) {
        return NextResponse.json({ 
          error: "Configuration test failed",
          details: testResult.error
        }, { status: 400 });
      }
    }

    // Save the configuration
    const { data: savedConfig, error } = await saveUserConfig(
      user.uuid,
      '', // project_id - empty for global user configs
      `analytics_${provider}`,
      configName,
      configData
    );

    if (error) {
      return NextResponse.json({ 
        error: "Failed to save configuration" 
      }, { status: 500 });
    }

    // Return response without sensitive data
    const response = {
      id: savedConfig?.id,
      provider: provider,
      configName: savedConfig?.configName,
      isActive: savedConfig?.isActive,
      created_at: savedConfig?.created_at,
      updated_at: savedConfig?.updated_at,
      website_id: configData.website_id,
      base_url: configData.base_url,
      domain: configData.domain
    };

    return NextResponse.json({ 
      message: "Configuration saved successfully",
      config: response
    });
  } catch (error) {
    console.error("Error saving analytics config:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE - Delete analytics configuration
export async function DELETE(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const provider = url.searchParams.get('provider');

    if (!provider) {
      return NextResponse.json({ 
        error: "Provider parameter is required" 
      }, { status: 400 });
    }

    const { error } = await deleteUserConfig(
      user.uuid, 
      '', // project_id - empty for global user configs
      `analytics_${provider}`
    );
    
    if (error) {
      return NextResponse.json({ 
        error: "Failed to delete configuration" 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      message: "Configuration deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting analytics config:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 