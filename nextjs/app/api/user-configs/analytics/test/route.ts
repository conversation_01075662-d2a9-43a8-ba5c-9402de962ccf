import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { analyticsService } from "@/services/analytics";


// POST - Test analytics configuration
export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { provider, api_key, website_id, base_url, domain } = body;

    if (!provider || !api_key || !website_id || !domain) {
      return NextResponse.json({ 
        error: "Provider, API key, website ID and domain are required" 
      }, { status: 400 });
    }

    // Validate provider
    if (!['google', 'plausible', 'umami'].includes(provider)) {
      return NextResponse.json({ 
        error: "Invalid provider. Supported: google, plausible, umami" 
      }, { status: 400 });
    }

    // Test the connection
    const testResult = await analyticsService.testConnection({
      provider,
      api_key,
      website_id,
      base_url,
      domain
    });

    if (testResult.isValid) {
      return NextResponse.json({ 
        success: true,
        message: "Connection test successful",
        data: testResult.data
      });
    } else {
      return NextResponse.json({ 
        success: false,
        error: testResult.error
      }, { status: 400 });
    }
  } catch (error) {
    console.error("Error testing analytics connection:", error);
    return NextResponse.json({ 
      success: false,
      error: "Internal server error" 
    }, { status: 500 });
  }
} 