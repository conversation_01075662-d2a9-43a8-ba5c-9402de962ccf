import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getSupabaseClient } from "@/models/db";

// GET - Get user's all Plausible configurations across all projects
export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all user configs with plausible provider directly from database
    const client = getSupabaseClient();
    const { data: allConfigs, error } = await client
      .from("user_configs")
      .select("*")
      .eq('user_id', user.uuid)
      .eq('config_type', 'analytics_plausible')
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching user configs:', error);
      return NextResponse.json({ error: "Failed to fetch configurations" }, { status: 500 });
    }

    // Transform configs and include config data for auto-fill
    const transformedConfigs = (allConfigs || []).map(config => ({
      id: config.id,
      configName: config.config_name,
      created_at: config.created_at,
      updated_at: config.updated_at,
      projectId: config.project_id,
      // Include sensitive data for auto-fill (only for authenticated user)
      api_key: config.config_data?.api_key || '',
      website_id: config.config_data?.website_id || '',
      base_url: config.config_data?.base_url || '',
      domain: config.config_data?.domain || ''
    }));

    // Deduplicate configs based on api_key and base_url (endpoint)
    const uniqueConfigsMap = new Map();
    
    transformedConfigs.forEach(config => {
      // Create a unique key based on api_key and base_url (normalize empty base_url to consistent value)
      const normalizedBaseUrl = config.base_url || 'https://plausible.io';
      const uniqueKey = `${config.api_key}|${normalizedBaseUrl}`;
      
      // If this combination doesn't exist or if current config is newer, use it
      if (!uniqueConfigsMap.has(uniqueKey) || 
          new Date(config.updated_at) > new Date(uniqueConfigsMap.get(uniqueKey).updated_at)) {
        
        // For merged configs, combine information from multiple sources
        const existingConfig = uniqueConfigsMap.get(uniqueKey);
        const mergedConfig = {
          ...config,
          // If merging, create a combined name that shows it's from multiple projects
          configName: existingConfig ? 
            `${config.configName} (多项目配置)` : 
            config.configName,
          // Keep track of all project IDs this config is used in
          projectIds: existingConfig ? 
            [...(existingConfig.projectIds || [existingConfig.projectId]), config.projectId].filter(Boolean) :
            [config.projectId].filter(Boolean),
          // Use the most recent website_id and domain
          website_id: config.website_id || existingConfig?.website_id || '',
          domain: config.domain || existingConfig?.domain || ''
        };
        
        uniqueConfigsMap.set(uniqueKey, mergedConfig);
      }
    });

    // Convert map back to array and sort by update time (newest first)
    const deduplicatedConfigs = Array.from(uniqueConfigsMap.values())
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());

    return NextResponse.json({ 
      success: true,
      configs: deduplicatedConfigs 
    });
  } catch (error) {
    console.error("Error getting user Plausible configs:", error);
    return NextResponse.json({ 
      success: false,
      error: "Internal server error" 
    }, { status: 500 });
  }
} 