import { NextRequest, NextResponse } from 'next/server';


export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const domain = searchParams.get('domain');

  if (!domain) {
    return NextResponse.json({ error: 'Domain parameter is required' }, { status: 400 });
  }

  try {
    // Clean domain name (remove protocol, www, paths)
    const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];

    // Use MyBackLinks cron-worker WHOIS API
    const cronWorkerUrl = process.env.BACKEND_WORKER_URL || 'https://backend.mybacklinks.app';
    const authToken = process.env.BACKEND_WORKER_API_KEY;

    if (!authToken) {
      console.error('Cron worker authentication not configured');
      return NextResponse.json({ error: 'Cron worker authentication not configured' }, { status: 500 });
    }

    // Call the cron-worker WHOIS API
    const response = await fetch(
      `${cronWorkerUrl}/api/whois?domain=${encodeURIComponent(cleanDomain)}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Cron worker API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Handle cron-worker returning success: false gracefully
    if (!data.success) {
      console.log(`WHOIS API unavailable for ${cleanDomain}: ${data.error || 'Service unavailable'}`);
      
      // Return a structured response indicating service unavailable
      const result = {
        domain: cleanDomain,
        registrar: null,
        createdDate: null,
        expiryDate: null,
        updatedDate: null,
        status: [],
        nameServers: [],
        dnssec: 'unknown',
        emails: null,
        source: 'unavailable',
        cached: false,
        timestamp: new Date().toISOString(),
        domainAge: null,
        expirationWarning: null,
        serviceUnavailable: true,
        serviceError: data.error || 'WHOIS service temporarily unavailable'
      };

      return NextResponse.json(result);
    }

    // Transform the cron-worker response to match the expected format
    const whoisData = data.data;
    
    const result = {
      domain: cleanDomain,
      registrar: whoisData.registrar || 'Unknown',
      createdDate: whoisData.creation_date,
      expiryDate: whoisData.expiration_date,
      updatedDate: null, // Not provided by API Layer
      status: [], // Not provided by API Layer in the same format
      nameServers: Array.isArray(whoisData.name_servers) ? whoisData.name_servers : [],
      dnssec: whoisData.dnssec || 'unknown',
      emails: whoisData.emails || '',
      source: whoisData.source || 'api-layer',
      cached: whoisData.cached || false,
      timestamp: whoisData.timestamp,
      // Additional fields from API Layer
      domainAge: whoisData.creation_date ? calculateDomainAge(whoisData.creation_date) : null,
      expirationWarning: whoisData.expiration_date ? checkExpirationWarning(whoisData.expiration_date) : null
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('WHOIS lookup error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch domain information' },
      { status: 500 }
    );
  }
}

/**
 * Calculate domain age from creation date
 */
function calculateDomainAge(creationDate: string) {
  if (!creationDate) return null;
  
  try {
    const created = new Date(creationDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffYears = Math.floor(diffDays / 365);
    const remainingDays = diffDays % 365;
    
    return {
      totalDays: diffDays,
      years: diffYears,
      remainingDays: remainingDays,
      formatted: diffYears > 0 ? `${diffYears} years, ${remainingDays} days` : `${diffDays} days`
    };
  } catch (error) {
    return null;
  }
}

/**
 * Check if domain is expiring soon
 */
function checkExpirationWarning(expirationDate: string, warningDays: number = 90) {
  if (!expirationDate) return null;
  
  try {
    const expires = new Date(expirationDate);
    const now = new Date();
    const diffTime = expires.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return {
      daysUntilExpiration: diffDays,
      isExpired: diffDays < 0,
      isExpiringSoon: diffDays > 0 && diffDays <= warningDays,
      warningThreshold: warningDays,
      status: diffDays < 0 ? 'expired' : 
              diffDays <= warningDays ? 'expiring_soon' : 'active'
    };
  } catch (error) {
    return null;
  }
}