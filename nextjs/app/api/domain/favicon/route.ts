import { NextRequest, NextResponse } from 'next/server';


export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const domain = searchParams.get('domain');

  if (!domain) {
    return NextResponse.json({ error: 'Domain parameter is required' }, { status: 400 });
  }

  try {
    // Clean domain name (remove protocol, www, paths)
    const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
    
    // Multiple favicon sources to try
    const faviconSources = [
      `https://www.google.com/s2/favicons?domain=${cleanDomain}&sz=64`,
      `https://favicon.im/${cleanDomain}?larger=true`,
      `https://api.faviconkit.com/${cleanDomain}/64`,
      `https://${cleanDomain}/favicon.ico`,
      `https://www.${cleanDomain}/favicon.ico`,
      `https://${cleanDomain}/apple-touch-icon.png`,
      `https://${cleanDomain}/favicon.png`
    ];

    // Try each favicon source
    for (const faviconUrl of faviconSources) {
      try {
        const response = await fetch(faviconUrl, {
          method: 'HEAD'
        });

        if (response.ok && response.headers.get('content-type')?.includes('image')) {
          return NextResponse.json({ 
            favicon: faviconUrl,
            source: faviconUrl.includes('google.com') ? 'google' : 
                   faviconUrl.includes('favicon.im') ? 'favicon.im' :
                   faviconUrl.includes('faviconkit.com') ? 'faviconkit' : 'direct'
          });
        }
      } catch (error) {
        // Continue to next source
        continue;
      }
    }

    // If all sources fail, return Google's favicon service as fallback
    const fallbackFavicon = `https://www.google.com/s2/favicons?domain=${cleanDomain}&sz=64`;
    
    return NextResponse.json({ 
      favicon: fallbackFavicon,
      source: 'google-fallback'
    });

  } catch (error) {
    console.error('Favicon fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch favicon' },
      { status: 500 }
    );
  }
}