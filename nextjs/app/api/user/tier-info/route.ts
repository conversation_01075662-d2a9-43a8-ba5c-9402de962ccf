import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getUserTierInfo } from "@/lib/tier-middleware";

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's tier info and usage
    const tierInfo = await getUserTierInfo(user.uuid);

    if (!tierInfo) {
      return NextResponse.json({ error: "Unable to fetch tier information" }, { status: 500 });
    }

    return NextResponse.json({ tierInfo });
  } catch (error) {
    console.error("Error fetching tier info:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}