import { NextRequest, NextResponse } from "next/server";
import { getUserByApi<PERSON>ey } from "@/models/user";
import { getProjectById } from "@/models/links";
import { z } from "zod";

const generateContentSchema = z.object({
  project_id: z.string().uuid("Invalid project ID"),
  form_type: z.string().min(1, "Form type is required"),
  fields: z.array(z.string()).min(1, "At least one field is required")
});

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    const body = await request.json();
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Validate request body
    const validation = generateContentSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { project_id, form_type, fields } = validation.data;
    
    // Get project details
    const { data: project, error: projectError } = await getProjectById(project_id, user.uuid);
    
    if (projectError || !project) {
      return NextResponse.json(
        { success: false, message: "Project not found or access denied" },
        { status: 404 }
      );
    }
    
    // Generate fallback content
    const generatedContent = generateFallbackContent(project, form_type, fields);
    
    return NextResponse.json({
      success: true,
      content: generatedContent,
      source: "fallback"
    });
  } catch (error) {
    console.error("Extension content generation error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Fallback content generation
function generateFallbackContent(
  project: any,
  formType: string,
  fields: string[]
): { [key: string]: string } {
  const content: { [key: string]: string } = {};
  
  const projectDescription = project.description || `${project.name} - A valuable resource from ${project.domain}`;
  const domain = project.domain;
  const projectName = project.name;
  
  fields.forEach(field => {
    const fieldLower = field.toLowerCase();
    
    // Generate content based on field patterns
    if (fieldLower.includes('description') || fieldLower.includes('summary') || fieldLower.includes('content')) {
      content[field] = generateDescription(projectName, domain, projectDescription, formType);
    } else if (fieldLower.includes('title') || fieldLower.includes('name')) {
      content[field] = generateTitle(projectName, domain, formType);
    } else if (fieldLower.includes('category') || fieldLower.includes('tag')) {
      content[field] = generateCategory(domain, projectDescription);
    } else if (fieldLower.includes('comment') || fieldLower.includes('note') || fieldLower.includes('message')) {
      content[field] = generateComment(projectName, domain, formType);
    } else if (fieldLower.includes('keyword') || fieldLower.includes('key_word')) {
      content[field] = generateKeywords(projectDescription, domain);
    } else if (fieldLower.includes('reason') || fieldLower.includes('why')) {
      content[field] = generateReason(projectName, domain, formType);
    }
  });
  
  return content;
}

function generateDescription(projectName: string, domain: string, description: string, formType: string): string {
  const templates = [
    `${projectName} offers valuable resources and insights. This high-quality website provides comprehensive information that would be a great addition to your directory.`,
    `I'd like to suggest ${projectName} (${domain}) as a valuable resource for your platform.`,
    `${projectName} is an excellent resource that provides valuable information and tools.`
  ];
  
  return templates[Math.floor(Math.random() * templates.length)];
}

function generateTitle(projectName: string, domain: string, formType: string): string {
  const templates = [
    projectName,
    `${projectName} - Quality Resource`,
    `${projectName} | ${domain}`,
    `Valuable Resource: ${projectName}`
  ];
  
  return templates[Math.floor(Math.random() * templates.length)];
}

function generateCategory(domain: string, description: string): string {
  return 'general';
}

function generateComment(projectName: string, domain: string, formType: string): string {
  const templates = [
    `Found this excellent resource at ${domain}. ${projectName} offers valuable content.`,
    `Hi! I wanted to share ${projectName} with you - it's a high-quality resource.`,
    `Discovered this great website: ${projectName}. The content quality is excellent.`
  ];
  
  return templates[Math.floor(Math.random() * templates.length)];
}

function generateKeywords(description: string, domain: string): string {
  return "quality, resource, valuable";
}

function generateReason(projectName: string, domain: string, formType: string): string {
  return `${projectName} provides high-quality, valuable content that would benefit your users.`;
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}