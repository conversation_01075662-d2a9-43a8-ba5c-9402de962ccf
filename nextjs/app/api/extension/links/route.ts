import { NextRequest, NextResponse } from "next/server";
import { getUserByApiKey } from "@/models/user";
import { getLinkResourcesByUser, createLinkResource } from "@/models/links";
import { z } from "zod";

const createLinkSchema = z.object({
  url: z.string().url("Invalid URL format"),
  title: z.string().min(1, "Title is required"),
  link_type: z.enum(["free", "paid"]).default("free"),
  price: z.number().optional(),
  source: z.string().optional(),
  project_id: z.string().optional()
});

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get("project_id");
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Get user's link resources
    const { data: links, error: linksError } = await getLinkResourcesByUser(user.uuid);
    
    if (linksError || !links) {
      return NextResponse.json(
        { success: false, message: "Failed to fetch links" },
        { status: 500 }
      );
    }
    
    // Note: LinkResource is decoupled from projects, so no project-based filtering
    let filteredLinks = links;
    
    // Format links for extension
    const formattedLinks = filteredLinks.map(link => ({
      id: link.id,
      url: link.url,
      title: link.title,
      link_type: link.link_type as "free" | "paid",
      price: link.price,
      source: link.source,
      user_id: link.user_id,
      created_at: link.created_at
    }));
    
    return NextResponse.json({
      success: true,
      links: formattedLinks
    });
  } catch (error) {
    console.error("Extension links GET error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    const body = await request.json();
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Validate request body
    const validation = createLinkSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const linkData = validation.data;
    
    // Create link resource (excluding project_id since LinkResource is project-decoupled)
    const { data: newLink, error: createError } = await createLinkResource({
      url: linkData.url,
      title: linkData.title,
      link_type: linkData.link_type,
      price: linkData.price,
      currency: 'USD', // Default currency
      source: linkData.source || "extension",
      user_id: user.uuid
    });
    
    if (createError || !newLink) {
      return NextResponse.json(
        { success: false, message: "Failed to create link" },
        { status: 500 }
      );
    }
    
    // Format response
    const formattedLink = {
      id: newLink.id,
      url: newLink.url,
      title: newLink.title,
      link_type: newLink.link_type as "free" | "paid",
      price: newLink.price,
      source: newLink.source,
      user_id: newLink.user_id,
      created_at: newLink.created_at
    };
    
    return NextResponse.json({
      success: true,
      link: formattedLink
    });
  } catch (error) {
    console.error("Extension links POST error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}