import { NextRequest, NextResponse } from "next/server";
import { getUserByApi<PERSON><PERSON> } from "@/models/user";
import { z } from "zod";

const analyzeFormSchema = z.object({
  form_html: z.string().min(1, "Form HTML is required"),
  target_url: z.string().url("Invalid target URL")
});

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    const body = await request.json();
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Validate request body
    const validation = analyzeFormSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { form_html, target_url } = validation.data;
    
    // Call cron-worker for AI analysis
    const cronWorkerUrl = process.env.BACKEND_WORKER_URL || "";
    const cronWorkerApiKey = process.env.BACKEND_WORKER_API_KEY;
    
    if (!cronWorkerApiKey) {
      console.error("BACKEND_WORKER_API_KEY not configured");
      return NextResponse.json(
        { success: false, message: "AI analysis service not available" },
        { status: 503 }
      );
    }
    
    const analysisResponse = await fetch(`${cronWorkerUrl}/api/ai/analyze-form`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${cronWorkerApiKey}`,
        "X-User-ID": user.uuid
      },
      body: JSON.stringify({
        form_html,
        target_url,
        user_id: user.uuid
      })
    });
    
    if (!analysisResponse.ok) {
      console.error("Cron worker analysis failed:", analysisResponse.status, analysisResponse.statusText);
      
      // Fallback to basic analysis
      const fallbackAnalysis = performBasicFormAnalysis(form_html, target_url);
      
      return NextResponse.json({
        success: true,
        analysis: fallbackAnalysis,
        source: "fallback"
      });
    }
    
    const analysisResult = await analysisResponse.json();
    
    if (analysisResult.success) {
      return NextResponse.json({
        success: true,
        analysis: analysisResult.analysis,
        source: "ai"
      });
    } else {
      // Fallback to basic analysis
      const fallbackAnalysis = performBasicFormAnalysis(form_html, target_url);
      
      return NextResponse.json({
        success: true,
        analysis: fallbackAnalysis,
        source: "fallback"
      });
    }
  } catch (error) {
    console.error("Extension form analysis error:", error);
    
    // Fallback to basic analysis on error
    try {
      const body = await request.json();
      const fallbackAnalysis = performBasicFormAnalysis(body.form_html, body.target_url);
      
      return NextResponse.json({
        success: true,
        analysis: fallbackAnalysis,
        source: "fallback"
      });
    } catch {
      return NextResponse.json(
        { success: false, message: "Internal server error" },
        { status: 500 }
      );
    }
  }
}

// Fallback form analysis when AI service is unavailable
function performBasicFormAnalysis(formHtml: string, targetUrl: string) {
  const fields = [];
  const fieldPatterns = [
    {
      pattern: /(input|textarea|select)[^>]*name=["']([^"']+)["'][^>]*>/gi,
      type: 'basic'
    }
  ];
  
  // Extract field information using regex
  for (const fieldPattern of fieldPatterns) {
    let match;
    while ((match = fieldPattern.pattern.exec(formHtml)) !== null) {
      const [fullMatch, element, name] = match;
      
      // Determine field type
      const typeMatch = fullMatch.match(/type=["']([^"']+)["']/i);
      const type = typeMatch ? typeMatch[1] : (element.toLowerCase() === 'textarea' ? 'textarea' : 'text');
      
      // Extract label information
      const labelMatch = fullMatch.match(/placeholder=["']([^"']+)["']/i);
      const label = labelMatch ? labelMatch[1] : name;
      
      // Check if required
      const required = fullMatch.includes('required');
      
      // Generate suggestions based on field name/label
      const suggestions = generateFieldSuggestions(name, label, type);
      
      fields.push({
        name,
        type,
        label,
        required,
        suggestions
      });
    }
  }
  
  // Determine form type
  const formType = determineFormType(formHtml, targetUrl);
  
  // Calculate confidence
  const confidence = calculateAnalysisConfidence(fields, formType, formHtml);
  
  return {
    fields,
    formType,
    confidence
  };
}

function generateFieldSuggestions(name: string, label: string, type: string): string[] {
  const fieldInfo = (name + ' ' + label).toLowerCase();
  const suggestions = [];
  
  if (fieldInfo.includes('url') || fieldInfo.includes('link') || fieldInfo.includes('website')) {
    suggestions.push('link_url', 'website_url', 'target_url');
  }
  
  if (fieldInfo.includes('title') || fieldInfo.includes('name')) {
    suggestions.push('link_title', 'site_name', 'resource_title');
  }
  
  if (fieldInfo.includes('description') || type === 'textarea') {
    suggestions.push('description', 'summary', 'content');
  }
  
  if (fieldInfo.includes('email')) {
    suggestions.push('email', 'contact_email');
  }
  
  if (fieldInfo.includes('category') || fieldInfo.includes('tag')) {
    suggestions.push('category', 'tags', 'topic');
  }
  
  return suggestions;
}

function determineFormType(formHtml: string, targetUrl: string): string {
  const content = (formHtml + ' ' + targetUrl).toLowerCase();
  
  if (content.includes('submit') && (content.includes('link') || content.includes('url'))) {
    return 'link_submission';
  }
  
  if (content.includes('directory') || content.includes('listing')) {
    return 'directory_submission';
  }
  
  if (content.includes('contact') || content.includes('message')) {
    return 'contact_form';
  }
  
  if (content.includes('register') || content.includes('signup')) {
    return 'registration_form';
  }
  
  return 'generic_form';
}

function calculateAnalysisConfidence(fields: any[], formType: string, formHtml: string): number {
  let confidence = 0;
  
  // Base confidence from field count
  confidence += Math.min(fields.length * 10, 40);
  
  // Boost for relevant field types
  fields.forEach(field => {
    const fieldInfo = (field.name + ' ' + field.label).toLowerCase();
    if (fieldInfo.includes('url') || fieldInfo.includes('link')) {
      confidence += 20;
    }
    if (fieldInfo.includes('title') || fieldInfo.includes('name')) {
      confidence += 10;
    }
    if (field.type === 'textarea') {
      confidence += 10;
    }
  });
  
  // Boost for form type detection
  if (formType === 'link_submission') {
    confidence += 20;
  } else if (formType === 'directory_submission') {
    confidence += 15;
  }
  
  return Math.min(confidence, 100);
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}