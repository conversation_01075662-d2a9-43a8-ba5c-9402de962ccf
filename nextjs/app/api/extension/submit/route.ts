import { NextRequest, NextResponse } from "next/server";
import { getUserByApiKey } from "@/models/user";
import { 
  createExtensionSubmission,
  validateProjectOwnership,
  validateLinkResourceOwnership
} from "@/models/extension-submission";
import { z } from "zod";

const submitLinkSchema = z.object({
  project_id: z.string().uuid("Invalid project ID"),
  link_id: z.string().uuid("Invalid link ID"),
  target_url: z.string().url("Invalid target URL"),
  form_data: z.record(z.any()).optional()
});

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    const body = await request.json();
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Validate request body
    const validation = submitLinkSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { project_id, link_id, target_url, form_data } = validation.data;
    
    // Verify user owns the project and link
    const { isValid: projectValid, error: projectError } = await validateProjectOwnership(project_id, user.uuid);
    
    if (projectError || !projectValid) {
      return NextResponse.json(
        { success: false, message: "Project not found or access denied" },
        { status: 404 }
      );
    }
    
    const { isValid: linkValid, error: linkError } = await validateLinkResourceOwnership(link_id, user.uuid);
    
    if (linkError || !linkValid) {
      return NextResponse.json(
        { success: false, message: "Link not found or access denied" },
        { status: 404 }
      );
    }
    
    // Create submission record
    const { data: submission, error: submissionError } = await createExtensionSubmission(
      {
        project_id,
        link_id,
        target_url,
        form_data
      },
      user.uuid
    );
    
    if (submissionError || !submission) {
      console.error('Failed to create submission:', submissionError);
      return NextResponse.json(
        { success: false, message: "Failed to create submission record" },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      submission: {
        id: submission.id,
        project_id: submission.project_id,
        link_id: submission.link_id,
        target_url: submission.target_url,
        status: submission.status,
        created_at: submission.created_at
      }
    });
  } catch (error) {
    console.error("Extension submit error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}