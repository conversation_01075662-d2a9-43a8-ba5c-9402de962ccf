import { NextRequest, NextResponse } from "next/server";
import { getUserByApiKey } from "@/models/user";
import { 
  getExtensionSubmissionById,
  updateExtensionSubmissionStatus,
  validateSubmissionOwnership
} from "@/models/extension-submission";
import { z } from "zod";

const updateStatusSchema = z.object({
  status: z.enum(["pending", "submitted", "failed", "completed"])
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authHeader = request.headers.get("authorization");
    const body = await request.json();
    const resolvedParams = await params;
    const submissionId = resolvedParams.id;
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Validate request body
    const validation = updateStatusSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { status } = validation.data;
    
    // Verify user owns the submission
    const { isValid, error: ownershipError } = await validateSubmissionOwnership(submissionId, user.uuid);
    
    if (ownershipError || !isValid) {
      return NextResponse.json(
        { success: false, message: "Submission not found or access denied" },
        { status: 404 }
      );
    }
    
    // Update submission status
    const { data: updatedSubmission, error: updateError } = await updateExtensionSubmissionStatus(
      submissionId,
      status,
      user.uuid
    );
    
    if (updateError || !updatedSubmission) {
      console.error('Failed to update submission:', updateError);
      return NextResponse.json(
        { success: false, message: "Failed to update submission" },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      submission: {
        id: updatedSubmission.id,
        status: updatedSubmission.status,
        updated_at: updatedSubmission.updated_at
      }
    });
  } catch (error) {
    console.error("Extension submission update error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authHeader = request.headers.get("authorization");
    const resolvedParams = await params;
    const submissionId = resolvedParams.id;
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Get submission details
    const { data: submission, error } = await getExtensionSubmissionById(submissionId, user.uuid);
    
    if (error || !submission) {
      return NextResponse.json(
        { success: false, message: "Submission not found or access denied" },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      submission: {
        id: submission.id,
        project_id: submission.project_id,
        link_id: submission.link_id,
        target_url: submission.target_url,
        submission_data: submission.submission_data,
        status: submission.status,
        created_at: submission.created_at,
        updated_at: submission.updated_at,
        project: submission.projects,
        link: submission.link_resources
      }
    });
  } catch (error) {
    console.error("Extension submission get error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, PATCH, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}