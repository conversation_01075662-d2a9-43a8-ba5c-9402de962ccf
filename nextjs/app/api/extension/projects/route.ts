import { NextRequest, NextResponse } from "next/server";
import { getUserByApi<PERSON>ey } from "@/models/user";
import { getProjectsByUser } from "@/models/links";

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    // Validate API key and get user
    const user = await getUserByApiKey(apiKey);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Get user's projects
    const { data: projects, error: projectsError } = await getProjectsByUser(user.uuid);
    
    if (projectsError || !projects) {
      return NextResponse.json(
        { success: false, message: "Failed to fetch projects" },
        { status: 500 }
      );
    }
    
    // Format projects for extension
    const formattedProjects = projects.map(project => ({
      id: project.id,
      name: project.name,
      domain: project.domain,
      description: project.description || '',
      user_id: project.user_id,
      created_at: project.created_at
    }));
    
    return NextResponse.json({
      success: true,
      projects: formattedProjects
    });
  } catch (error) {
    console.error("Extension projects error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}