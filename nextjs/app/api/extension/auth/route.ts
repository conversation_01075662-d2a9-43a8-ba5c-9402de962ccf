import { NextRequest, NextResponse } from "next/server";
import { getUserByApiKey } from "@/models/user";

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    const body = await request.json();
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization header required" },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.replace("Bearer ", "");
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: "API key required" },
        { status: 401 }
      );
    }
    
    if (body.action === "validate") {
      // Validate API key and return user info
      const user = await getUserByApiKey(apiKey);
      
      if (!user) {
        return NextResponse.json(
          { success: false, message: "Invalid API key" },
          { status: 401 }
        );
      }
      
      return NextResponse.json({
        success: true,
        user: {
          uuid: user.uuid,
          email: user.email,
          nickname: user.nickname || user.email,
          avatar_url: user.avatar_url
        }
      });
    }
    
    return NextResponse.json(
      { success: false, message: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Extension auth error:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}