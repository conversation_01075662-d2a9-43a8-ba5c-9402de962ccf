import { NextRequest, NextResponse } from 'next/server';
import { getUserInfo } from '@/services/user';
import { fetchPlausibleTrafficContribution, fetchGoogleAnalyticsTrafficContribution, fetchUmamiTrafficContribution } from '@/services/analytics-traffic';


export async function GET(request: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const domain = searchParams.get('domain');
    const projectId = searchParams.get('projectId');

    if (!domain && !projectId) {
      return NextResponse.json({ error: 'Domain or projectId parameter is required' }, { status: 400 });
    }

    const result = await getAITrafficData(domain, projectId, String(userInfo.id));
    
    return NextResponse.json(result);

  } catch (error) {
    console.error('AI traffic analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch AI traffic data' },
      { status: 500 }
    );
  }
}

async function getAITrafficData(domain: string | null, projectId: string | null, userId: string) {
  try {
    // Define AI traffic sources to look for in referrer data - Updated classification
    const aiTrafficSources = {
      'chatgpt.com': 'chatgpt_user',
      'chat.openai.com': 'chatgpt_user', 
      'claude.ai': 'claude_user',
      'bard.google.com': 'bard',
      'copilot.microsoft.com': 'copilot',
      'perplexity.ai': 'perplexity_user',
      'poe.com': 'other',
      'character.ai': 'other',
      // Add Claude SearchBot specific patterns
      'claude-search': 'claude_searchbot',
      'anthropic': 'claude_searchbot'
    };

    const aiTrafficData = {
      total: 0,
      sources: {
        chatgpt_user: 0,
        claude_user: 0,
        claude_searchbot: 0,
        perplexity_user: 0,
        other: 0
      },
      trends: {
        daily: [],
        weekly: [],
        monthly: []
      },
      lastUpdated: new Date().toISOString()
    };

    try {
      // Get project analytics configurations
      let analyticsConfigs = [];
      if (projectId) {
        const configResponse = await fetch(`http://localhost:3000/api/projects/${projectId}/analytics-config`);
        if (configResponse.ok) {
          const configData = await configResponse.json();
          analyticsConfigs = configData.configs || [];
        }
      }

      // If no configs found, return empty data
      if (analyticsConfigs.length === 0) {
        console.log('No analytics configurations found for project');
        return aiTrafficData;
      }

      // Process each analytics configuration
      for (const config of analyticsConfigs) {
        const { provider, api_key, website_id, base_url } = config;
        
        // For each AI source, get traffic data
        for (const [sourceDomain, sourceCategory] of Object.entries(aiTrafficSources)) {
          try {
            let trafficData = null;

            switch (provider) {
              case 'plausible':
                trafficData = await fetchPlausibleTrafficContribution(
                  domain || config.domain,
                  sourceDomain,
                  'last_30_days',
                  api_key
                );
                break;

              case 'google':
                trafficData = await fetchGoogleAnalyticsTrafficContribution(
                  website_id,
                  sourceDomain,
                  'last_30_days',
                  api_key
                );
                break;

              case 'umami':
                trafficData = await fetchUmamiTrafficContribution(
                  website_id,
                  sourceDomain,
                  'last_30_days',
                  api_key,
                  base_url || 'https://analytics.umami.is'
                );
                break;

              default:
                continue;
            }

            // Add traffic data to the appropriate category
            if (trafficData && trafficData.referral_traffic > 0) {
              aiTrafficData.sources[sourceCategory as keyof typeof aiTrafficData.sources] += trafficData.referral_traffic;
              aiTrafficData.total += trafficData.referral_traffic;
            }

          } catch (sourceError) {
            console.error(`Error fetching traffic for ${sourceDomain}:`, sourceError);
            // Continue processing other sources
          }
        }
      }

      return aiTrafficData;

    } catch (analyticsError) {
      console.error('Analytics API error:', analyticsError);
      return aiTrafficData;
    }

  } catch (error) {
    console.error('Error fetching AI traffic data:', error);
    throw error;
  }
}