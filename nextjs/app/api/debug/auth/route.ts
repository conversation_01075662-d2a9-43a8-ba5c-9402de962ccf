import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { getUserInfo, getUserEmail, getUserUuid } from "@/services/user";
import { getNextAuthSession } from "@/lib/auth-utils";


export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Debug Auth API called");
    
    // Test direct session (using try-catch to handle potential headers() error)
    let session;
    try {
      session = await getNextAuthSession();
      console.log("📱 Direct session:", session);
    } catch (sessionError) {
      const errorMessage = sessionError instanceof Error ? sessionError.message : String(sessionError);
      if (errorMessage.includes("headers().get") && errorMessage.includes("should be awaited")) {
        console.log("🚀 NextAuth headers() compatibility issue detected in debug API");
      } else {
        console.log("❌ Session error:", sessionError);
      }
      session = null;
    }

    // Test enhanced auth function
    let authenticatedUser;
    try {
      authenticatedUser = await getAuthenticatedUser();
      console.log("🔐 Authenticated user:", authenticatedUser);
    } catch (authError) {
      console.log("❌ Auth error:", authError);
    }

    // Test user service functions
    let userUuid;
    try {
      userUuid = await getUserUuid();
      console.log("🆔 User UUID:", userUuid);
    } catch (uuidError) {
      console.log("❌ UUID error:", uuidError);
    }

    let userEmail;
    try {
      userEmail = await getUserEmail();
      console.log("📧 User email:", userEmail);
    } catch (emailError) {
      console.log("❌ Email error:", emailError);
    }

    let userInfo;
    try {
      userInfo = await getUserInfo();
      console.log("👤 User info:", userInfo);
    } catch (userInfoError) {
      console.log("❌ User info error:", userInfoError);
    }

    return NextResponse.json({
      success: true,
      debug: {
        environment: process.env.NODE_ENV,
        session: session,
        authenticatedUser: authenticatedUser,
        userUuid: userUuid,
        userEmail: userEmail,
        userInfo: userInfo,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("❌ Debug auth API error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}