# MCPs API Documentation

This document outlines the public API endpoints available under the `/api/items` path for interacting with Model Context Protocols (MCPs).

## List Public MCPs

*   **Method:** `GET`
*   **Path:** `/api/items/public`
*   **Authentication:** None
*   **Description:** Retrieves a paginated list of all publicly available MCPs.
*   **Query Parameters:**
    *   `page`: number (optional, default: 1) - Page number for pagination.
    *   `limit`: number (optional, default: 50, max: 100) - Number of MCPs per page.
    *   `is_official`: boolean (optional) - Filter for official MCPs only.
    *   `is_recommended`: boolean (optional) - Filter for recommended MCPs only.
*   **Responses:**
    *   `200 OK`: Returns a list of public MCP objects with pagination info in headers (`X-Total-Count`, `X-Page`, `X-Per-Page`).
        ```json
        {
          "data": [ /* array of public MCP objects */ ],
          "count": number // total number of public MCPs
        }
        ```
    *   `400 Bad Request`: Invalid page or limit parameter.
    *   `500 Internal Server Error`: Failed to fetch public MCPs.

## Get MCP Details by UUID

*   **Method:** `GET`
*   **Path:** `/api/items/{uuid}`
*   **Authentication:** None
*   **Description:** Retrieves the details of a specific public MCP by its UUID. Can optionally request localized content.
*   **Path Parameters:**
    *   `uuid`: string (required) - The UUID of the MCP to retrieve.
*   **Query Parameters:**
    *   `lang`: string (optional, default: 'en') - The desired language code for localized fields (e.g., 'brief', 'detail'). If the language doesn't exist for the MCP, it falls back to the default.
*   **Responses:**
    *   `200 OK`: Returns the MCP object.
        ```json
        {
          "id": "string",
          "uuid": "string",
          "name": "string",
          "item_avatar_url": "string",
          "user_avatar_url": "string",
          "website_url": "string",
          "author_name": "string",
          "is_recommended": "bool",
          "is_official": "bool",
          "allow_public": "bool",
          "clicks": "number",
          "tags": "string[]",
          "localization": {
            "brief": "string",
            "processinfo": "string"
          }
        }
        ```
    *   `400 Bad Request`: Missing or invalid MCP UUID format.
    *   `404 Not Found`: MCP with the specified UUID not found.
    *   `500 Internal Server Error`: Failed to fetch MCP details.

## Increment MCP Click Count

*   **Method:** `POST`
*   **Path:** `/api/items/{uuid}/click`
*   **Authentication:** None
*   **Description:** Increments the click counter for a specific MCP. This is typically called when a user clicks on an MCP link.
*   **Path Parameters:**
    *   `uuid`: string (required) - The UUID of the MCP to increment clicks for.
*   **Responses:**
    *   `204 No Content`: Click count incremented successfully.
    *   `400 Bad Request`: Missing or invalid MCP UUID format.
    *   `500 Internal Server Error`: Failed to increment clicks.

## Prefetch MCP Submission Info

*   **Method:** `GET`
*   **Path:** `/api/items/prefetch`
*   **Authentication:** None
*   **Description:** Fetches preliminary information about a potential MCP submission from a given GitHub repository URL. Useful for pre-filling submission forms.
*   **Query Parameters:**
    *   `url`: string (required) - The full URL of the GitHub repository (e.g., `https://github.com/owner/repo`).
*   **Responses:**
    *   `200 OK`: Returns prefetched information from the GitHub repository.
        ```json
        {
          "name": "string", // Repository name
          "author_name": "string", // Repository owner
          "website_url": "string", // Original GitHub URL
          "user_avatar_url": "string", // GitHub owner's avatar URL
          "detail": "string", // README content in markdown format
          "preprocessinfo": {
            "brief": "string", // Brief description extracted from GitHub
            "tags": "string[]", // Array of tags (empty by default, would be populated by AI processing)
            "metadata": {
              "forks": number,
              "stars": number,
              "watchers": number,
              "updated_at": "string" // Last update date from GitHub
            }
          },
          "error": null // or error description if prefetch failed partially
        }
        ```
    *   `400 Bad Request`: Missing or invalid `url` query parameter.
    *   `500 Internal Server Error`: Failed to prefetch information due to server or GitHub API issues.

## Get Random Public MCPs

*   **Method:** `GET`
*   **Path:** `/api/items/random`
*   **Authentication:** None
*   **Description:** Retrieves a list of random publicly available MCPs.
*   **Query Parameters:**
    *   `limit`: number (optional, default: 10, max: 50) - The maximum number of random MCPs to return.
*   **Responses:**
    *   `200 OK`: Returns an array of random public MCP objects.
        ```json
        [ /* array of random public MCP objects */ ]
        ```
    *   `400 Bad Request`: Invalid limit parameter.
    *   `500 Internal Server Error`: Failed to fetch random MCPs.

## Submit New MCP

*   **Method:** `POST`
*   **Path:** `/api/items/submit`
*   **Authentication:** None
*   **Description:** Allows users to submit a new MCP for review and potential inclusion in the public list.
*   **Request Body:** `application/json`
    ```json
    {
      "name": "string (required)",
      "author_name": "string (required)",
      "website_url": "string (required) - Typically the GitHub repo URL",
      "item_avatar_url": "string (optional)",
      "user_avatar_url": "string (optional)",
      "email": "string (optional) - Submitter's email",
      "subscribe_newsletter": "boolean (optional, default: true)",
      "detail": "string (optional) - Detailed description in markdown format",
      "preprocessinfo": "object (optional) - Additional preprocessed information from the prefetch endpoint"
    }
    ```
*   **Responses:**
    *   `201 Created`: Submission received successfully. Returns the created submission object.
        ```json
        { /* MCP submission object with status 'pending' */ }
        ```
    *   `400 Bad Request`: Missing required fields, invalid JSON, or validation error (e.g., duplicate URL).
    *   `500 Internal Server Error`: Failed to process the submission.

## Get Tags or MCPs by Tags

*   **Method:** `GET`
*   **Path:** `/api/items/tags`
*   **Authentication:** None
*   **Description:**
    *   If called without query parameters, returns a list of all unique tags used across public MCPs, along with their counts.
    *   If called with the `tags` query parameter, returns a list of public MCPs matching *all* specified tags.
*   **Query Parameters:**
    *   `tags`: string[] (optional) - Comma-separated list of tags to filter by (e.g., `tags=tool&tags=github&tags=ai`). If omitted, returns all unique tags.
    *   `limit`: number (optional, default: 50, max: 100) - Maximum number of MCPs to return when filtering by tags. Ignored if `tags` is omitted.
    *   `is_official`: boolean (optional) - Filter for official MCPs only. Only used when `tags` parameter is provided.
    *   `is_recommended`: boolean (optional) - Filter for recommended MCPs only. Only used when `tags` parameter is provided.
*   **Responses:**
    *   **Without `tags` parameter:**
        *   `200 OK`: Returns an array of tag objects.
            ```json
            [
              { "tag": "string", "count": number },
              ...
            ]
            ```
        *   `500 Internal Server Error`: Failed to fetch tags.
    *   **With `tags` parameter:**
        *   `200 OK`: Returns an array of public MCP objects matching the tags.
            ```json
            [ /* array of public MCP objects matching tags */ ]
            ```
        *   `400 Bad Request`: Invalid `tags` or `limit` parameter.
        *   `500 Internal Server Error`: Failed to fetch MCPs by tags.

## Search MCPs

*   **Method:** `GET`
*   **Path:** `/api/items/search`
*   **Authentication:** None
*   **Description:** Searches for MCPs based on a query string. The search is performed across MCP names, descriptions, and localized content.
*   **Query Parameters:**
    *   `q`: string (required) - The search query.
    *   `lang`: string (optional, default: 'en') - The language code for localized content to search in.
    *   `page`: number (optional, default: 1) - Page number for pagination.
    *   `limit`: number (optional, default: 50, max: 100) - Number of MCPs per page.
*   **Responses:**
    *   `200 OK`: Returns a list of MCPs matching the search query with pagination info in headers.
        ```json
        {
          "data": [ /* array of MCP objects matching search query */ ],
          "count": number // total number of matching MCPs
        }
        ```
    *   `400 Bad Request`: Missing search query or invalid parameters.
    *   `500 Internal Server Error`: Failed to search MCPs.

## Get MCPs Count

*   **Method:** `GET`
*   **Path:** `/api/items/count`
*   **Authentication:** None
*   **Description:** Returns the total count of public MCPs.
*   **Responses:**
    *   `200 OK`: Returns the count of public MCPs.
        ```json
        {
          "count": number // total number of public MCPs
        }
        ```
    *   `500 Internal Server Error`: Failed to count MCPs.

## Get Tag Counts

*   **Method:** `GET`
*   **Path:** `/api/items/tags/count`
*   **Authentication:** None
*   **Description:** Returns the count of MCPs for each tag or a specified list of tags.
*   **Query Parameters:**
    *   `tags`: string[] (optional) - List of tags to get counts for. If omitted, returns counts for all tags.
*   **Responses:**
    *   `200 OK`: Returns an array of tag count objects.
        ```json
        [
          { "tag": "string", "count": number },
          ...
        ]
        ```
    *   `500 Internal Server Error`: Failed to fetch tag counts.

## Embedding-Based Semantic Search

*   **Method:** `POST`
*   **Path:** `/api/items/embedding-search`
*   **Authentication:** None
*   **Description:** Performs a semantic search using vector embeddings. This allows for finding semantically related content even when the exact keywords don't match.
*   **Request Body:** `application/json`
    ```json
    {
      "text": "string (required) - The search text to find semantically similar content",
      "language": "string (optional, default: 'en') - The language code for search ('en' or 'zh')",
      "threshold": "number (optional, default: 0.75) - Similarity threshold (0-1), higher values mean more similar results",
      "limit": "number (optional, default: 10) - Maximum number of results to return"
    }
    ```
*   **Responses:**
    *   `200 OK`: Returns embedding search results ordered by similarity.
        ```json
        {
          "data": [
            {
              "id": "number",
              "url": "string",
              "title": "string",
              "brief": "string",
              "logo": "string",
              "visitstotalcount": "number",
              "bouncerate": "number",
              "pagespervisit": "number",
              "visitsavgdurationformatted": "string",
              "similarity": "number" // Similarity score (0-1)
            },
            ...
          ],
          "count": "number", // Total number of matching results
          "language": "string" // The language used for search
        }
        ```
    *   `400 Bad Request`: Missing required fields, invalid language parameter.
    *   `500 Internal Server Error`: Failed to generate embeddings or perform search.
