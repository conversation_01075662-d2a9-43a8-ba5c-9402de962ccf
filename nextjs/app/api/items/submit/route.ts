
import { NextRequest, NextResponse } from 'next/server';
import { userSubmitItems } from '@/models/submission';
import { Submission } from '@/types/items';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth'; // Using response helpers
import { notificationService } from '@/lib/notification';
import { sendItemSubmittedEmail } from '@/lib/emailService';

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

// POST /api/items/submit
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Basic validation for required fields
    if (!body.name || !body.author_name || !body.website_url) {
      return badRequestResponse('Missing required fields: name, author_name, website_url');
    }

    // Construct submission data, allowing only specific fields
    console.log("recv item submit: body", body);
    const submissionData: Partial<Submission> = {
        name: body.name,
        author_name: body.author_name,
        website_url: body.website_url,
        item_avatar_url: body.item_avatar_url || null,
        user_avatar_url: body.user_avatar_url || `https://github.com/${body.author_name}.png`, // Use GitHub username for avatar if none provided
        email: body.email || null,
        subscribe_newsletter: body.subscribe_newsletter === undefined ? true : !!body.subscribe_newsletter,
        detail: body.detail || null,
        // Include preprocessinfo if it exists in the body
        preprocessinfo: body.preprocessinfo || null,
    };

    const { data, error } = await userSubmitItems(submissionData);

    if (error) {
        // Check if it's the specific validation error from the model
        if (error.code === '400') {
            // Send error notification
            await notificationService.notify({
                title: 'Item Submission Failed',
                content: `An Item submission failed with validation error`,
                level: 'error',
                data: {
                    'Error': error.message,
                    'Item Name': body.name,
                    'Author': body.author_name,
                    'Repository': body.website_url
                }
            });
            
            return badRequestResponse(error.message);
        }
        
        // Send error notification for non-validation errors
        await notificationService.notify({
            title: 'Item Submission Failed',
            content: `An Item submission failed with server error`,
            level: 'error',
            data: {
                'Error': error.message || 'Unknown error',
                'Item Name': body.name,
                'Author': body.author_name,
                'Repository': body.website_url
            }
        });
        
        return serverErrorResponse('Failed to submit Item', error);
    }

    // Send notification about the new submission
    await notificationService.notify({
      title: 'New Item Submission',
      content: `A new Item has been submitted: **${body.name}**`,
      level: 'success',
      data: {
        'Item Name': body.name,
        'Author': body.author_name, 
        'Repository': body.website_url,
        'Email': body.email || 'Not provided'
      }
    });

    // Send email to the submitter if email is provided
    if (body.email) {
      try {
        await sendItemSubmittedEmail(body.email, submissionData);
      } catch (emailError) {
        console.error('Failed to send submission confirmation email:', emailError);
        // We don't want to fail the submission if email sending fails
      }
    }

    // Return the created submission data with a 201 status
    return NextResponse.json(data, { status: 201 });

  } catch (err: any) {
     if (err instanceof SyntaxError) {
        return badRequestResponse('Invalid JSON format in request body.');
     }
    return serverErrorResponse('An unexpected error occurred', err);
  }
}
