
import { NextRequest, NextResponse } from 'next/server';
import { getAllTags, getPublicItemsByTags } from '@/models/items';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth'; // Using response helpers

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

// GET /api/items/tags -> returns all unique tags with counts
// GET /api/items/tags?tags=tag1,tag2&limit=10 -> returns Items matching tags
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tagsParam = searchParams.getAll('tags'); // Get all tags (allows multiple)
    const limitParam = searchParams.get('limit');
    const isOfficialParam = searchParams.get('is_official');
    const isRecommendedParam = searchParams.get('is_recommended');
    const languageParam = searchParams.get('lang') || 'en'; // Default to English if not specified
    
    // If no tags are provided, return all unique tags
    if (tagsParam.length === 0) {
      const { data, error } = await getAllTags();
      
      if (error) {
        return serverErrorResponse('Failed to fetch tags', error);
      }
      
      return NextResponse.json(data || []);
    }
    
    // Otherwise, return Items that match the provided tags
    const limit = limitParam ? parseInt(limitParam, 10) : 50;
    
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return badRequestResponse('Invalid limit value (must be between 1 and 100).');
    }
    
    // Parse boolean filters
    const filters: { is_official?: boolean; is_recommended?: boolean } = {};
    
    if (isOfficialParam !== null) {
      filters.is_official = isOfficialParam === 'true';
    }
    
    if (isRecommendedParam !== null) {
      filters.is_recommended = isRecommendedParam === 'true';
    }
    
    const { data, error, count } = await getPublicItemsByTags(tagsParam, limit, filters, languageParam);
    
    if (error) {
      return serverErrorResponse('Failed to fetch Items by tags', error);
    }
    
    // Add pagination info to headers
    const response = NextResponse.json(data || []);
    if (count !== null) {
      response.headers.set('X-Total-Count', count.toString());
    }
    response.headers.set('X-Language', languageParam);
    
    return response;
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}

