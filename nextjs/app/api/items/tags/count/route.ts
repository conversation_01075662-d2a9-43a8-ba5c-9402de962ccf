
import { NextRequest, NextResponse } from 'next/server';
import { getTagCounts } from '@/models/items';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tags = searchParams.getAll('tags');
    
    const { data, error } = await getTagCounts(tags.length > 0 ? tags : undefined);
    
    if (error) {
      return serverErrorResponse('Failed to fetch tag counts', error);
    }
    
    return NextResponse.json(data);
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
} 
