
import { NextRequest, NextResponse } from 'next/server';
import { getRandomPublicItems } from '@/models/items';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth'; // Using response helpers

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');

    const limit = limitParam ? parseInt(limitParam, 10) : 10; // Default to 10

    if (isNaN(limit) || limit < 1 || limit > 50) { // Add a max limit consistent with model
      return badRequestResponse('Invalid limit value (must be between 1 and 50).');
    }

    const { data, error } = await getRandomPublicItems(limit);

    if (error) {
      return serverErrorResponse('Failed to fetch random public Items', error);
    }

    return NextResponse.json(data || []); // Return empty array if data is null
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}
