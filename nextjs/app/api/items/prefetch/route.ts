
import { NextRequest, NextResponse } from 'next/server';
// Removed: import { prefetchSubmissionInfo } from '@/models/Submission';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth'; // Using response helpers

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

// POST /api/items/prefetch (now uses POST to align with /meta/extract)
export async function POST(request: NextRequest) { // Changed from GET to POST
  try {
    // const { searchParams } = new URL(request.url); // No longer needed for GET params
    // const githubUrl = searchParams.get('url'); // Get URL from POST body instead
    const { url: githubUrl } = await request.json(); // Get URL from POST body
    const backendWorkerUrl = process.env.BACKEND_WORKER_URL;
    const backendWorkerAPI = process.env.BACKEND_WORKER_API_KEY;

    if (!backendWorkerUrl || !backendWorkerAPI) {
      console.error('BACKEND_WORKER_URL or BACKEND_WORKER_API_KEY environment variable is not set.');
      return serverErrorResponse('Backend worker service is not configured.');
    }

    if (!githubUrl) {
      return badRequestResponse('Missing required query parameter: url');
    }

    // Basic URL validation (optional but recommended)
    try {
        new URL(githubUrl); // Check if it's a valid URL structure
        if (!githubUrl.startsWith('https://github.com/')) {
             return badRequestResponse('Invalid GitHub URL provided.');
        }
    } catch (_) {
        return badRequestResponse('Invalid URL format provided.');
    }


    // Call the backend worker's unified meta endpoint
    const response = await fetch(`${backendWorkerUrl}/meta/extract`, {
      method: 'POST', // Changed from GET
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${backendWorkerAPI}`,
        // Add any necessary headers, e.g., Authorization if needed later
      },
      body: JSON.stringify({ url: githubUrl, type: 'github' }), // Send URL and specify type
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse backend error response' }));
      console.error(`Backend worker error (${response.status}) for prefetch:`, errorData);
      // Map backend status code or return a generic server error
      const statusCode = response.status >= 400 && response.status < 500 ? response.status : 500;
      return NextResponse.json(
        { error: errorData.error || `Backend worker failed prefetch with status ${response.status}` },
        { status: statusCode }
      );
    }

    const result = await response.json();

    // Return the prefetched data from the worker
    return NextResponse.json(result);

  } catch (err: any) {
    console.error('Error calling backend worker for prefetch:', err);
    return serverErrorResponse('An unexpected error occurred while calling backend worker', err);
  }
}
