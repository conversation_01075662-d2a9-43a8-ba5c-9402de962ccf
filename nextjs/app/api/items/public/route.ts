
import { NextRequest, NextResponse } from 'next/server';
import { getAllPublicItems } from '@/models/items';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth'; // Using response helpers

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const pageParam = searchParams.get('page');
    const limitParam = searchParams.get('limit');
    const languageParam = searchParams.get('lang') || 'en'; // Default to English if not specified

    const page = pageParam ? parseInt(pageParam, 10) : 1;
    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    if (isNaN(page) || page < 1) {
      return badRequestResponse('Invalid page number.');
    }
    if (isNaN(limit) || limit < 1 || limit > 100) { // Add a max limit
      return badRequestResponse('Invalid limit value (must be between 1 and 100).');
    }

    const { data, error, count } = await getAllPublicItems(limit, page, languageParam);

    if (error) {
      return serverErrorResponse('Failed to fetch public Items', error);
    }

    console.log('getAllPublicItems count=', count);

    // Add pagination info to headers or response body
    const response = NextResponse.json({ data, count });
    response.headers.set('X-Total-Count', count?.toString() || '0');
    response.headers.set('X-Page', page.toString());
    response.headers.set('X-Per-Page', limit.toString());
    response.headers.set('X-Language', languageParam);

    return response;
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}
