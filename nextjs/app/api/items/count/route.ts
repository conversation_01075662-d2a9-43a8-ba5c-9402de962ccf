
import { NextRequest, NextResponse } from 'next/server';
import { countPublicItems } from '@/models/items';
import { serverErrorResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

export async function GET(request: NextRequest) {
  try {
    const { count, error } = await countPublicItems();
    
    if (error) {
      return serverErrorResponse('Failed to count Items', error);
    }
    
    return NextResponse.json({ count });
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
} 
