import { NextRequest, NextResponse } from 'next/server';
// Removed AI SDK imports as embedding generation is moved
// import { createOpenAI } from '@ai-sdk/openai';
// import { embed } from 'ai';
import { searchItemsByEmbedding, getItemDetailsByUuids } from '@/models/items';
export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  try {
    console.log('⭐️ Embedding search API called');
    
    console.log('⭐️ Environment:', process.env.NODE_ENV);
    
    const backendWorkerUrl = process.env.BACKEND_WORKER_URL;
    const backendWorkerAPI = process.env.BACKEND_WORKER_API_KEY;
    console.log('⭐️ Backend worker config:', backendWorkerUrl ? 'URL is set' : 'URL is not set', backendWorkerAPI ? 'API key is set' : 'API key is not set');

    // Parse the request body first
    let requestData;
    try {
      requestData = await req.json();
      console.log('⭐️ Request data parsed successfully');
    } catch (error) {
      console.error('⭐️ Failed to parse request JSON:', error);
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }
    
    const { text, language = 'en', threshold = 0.75, limit = 10 } = requestData;
    console.log(`⭐️ Request params: language=${language}, threshold=${threshold}, limit=${limit}, text length=${text?.length || 0}`);

    if (!backendWorkerUrl || !backendWorkerAPI) {
      console.error('⭐️ BACKEND_WORKER_URL environment variable or BACKEND_WORKER_API_KEY is not set.');
      // Return a proper JSON response instead of an error
      return NextResponse.json({ 
        error: 'Backend worker service is not configured.', 
        details: 'Missing environment variables: BACKEND_WORKER_URL and/or BACKEND_WORKER_API_KEY',
        status: 'configuration_error'
      }, { status: 500 });
    }

    if (!text || typeof text !== 'string') {
      console.error('⭐️ Invalid text parameter:', text);
      return NextResponse.json(
        { error: 'Search text is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate language parameter
    if (language !== 'en' && language !== 'zh') {
      console.error('⭐️ Invalid language parameter:', language);
      return NextResponse.json(
        { error: 'Language must be either "en" or "zh"' },
        { status: 400 }
      );
    }

    // Call the backend worker to generate embedding
    let embedding: number[];
    try {
      console.log(`⭐️ Calling backend worker at ${backendWorkerUrl}/embedding/generate`);
      
      const embeddingRequestData = {
        text,
        language,
        // Don't include model parameter as we'll use the worker's default
      };
      
      console.log('⭐️ Embedding request data:', JSON.stringify(embeddingRequestData));
      
      const embeddingResponse = await fetch(`${backendWorkerUrl}/embedding/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${backendWorkerAPI}`
        },
        body: JSON.stringify(embeddingRequestData),
      });

      console.log(`⭐️ Backend worker response status: ${embeddingResponse.status}`);
      
      // Handle non-OK responses
      if (!embeddingResponse.ok) {
        // Try to parse JSON error response
        let errorData;
        try {
          errorData = await embeddingResponse.json();
        } catch (e) {
          // If can't parse JSON, use text response
          const textResponse = await embeddingResponse.text().catch(() => 'Failed to get response text');
          errorData = { error: 'Failed to parse backend error response', rawResponse: textResponse };
        }
        
        console.error(`⭐️ Backend worker error (${embeddingResponse.status}) for embedding generation:`, errorData);
        
        return NextResponse.json(
          { 
            error: errorData.error || `Backend worker failed embedding generation with status ${embeddingResponse.status}`,
            message: errorData.message || 'No additional error details',
            details: errorData.details || '',
            status: embeddingResponse.status
          },
          { status: embeddingResponse.status }
        );
      }
      
      // Parse successful response
      const embeddingResult = await embeddingResponse.json();
      console.log(`⭐️ Embedding result received:`, embeddingResult ? 'Data received' : 'No data');
      
      if (!embeddingResult.embedding) {
        console.error('⭐️ Embedding not found in backend worker response:', embeddingResult);
        throw new Error('Embedding not found in backend worker response');
      }
      
      embedding = embeddingResult.embedding;
      console.log(`⭐️ Embedding vector length: ${embedding.length}`);

    } catch (error: any) {
      console.error('⭐️ Error calling backend worker for embedding generation:', error);
      return NextResponse.json({ 
        error: 'Failed to generate embedding via backend worker', 
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }, { status: 500 });
    }

    // Use the embedding received from the worker to search for similar Items
    console.log(`⭐️ Searching for Items with similar embedding`);
    const { matchingUuids, count, error } = await searchItemsByEmbedding(
      embedding,
      language,
      threshold,
      limit
    );

    if (error) {
      console.error('⭐️ Error searching with embeddings:', error);
      return NextResponse.json(
        { error: 'Failed to search with embeddings', details: error },
        { status: 500 }
      );
    }

    console.log(`⭐️ Found ${count || matchingUuids.length} matching Items`);
    // Get complete Item data for matching UUIDs
    const validResults = await getItemDetailsByUuids(matchingUuids, language);
    console.log(`⭐️ Retrieved ${validResults.length} valid Item results`);

    return NextResponse.json({
      data: validResults,
      count: count || matchingUuids.length,
      language,
    });
  } catch (error: any) {
    console.error('⭐️ Unhandled error in embedding search:', error);
    return NextResponse.json(
      { 
        error: 'Error processing embedding search', 
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined 
      },
      { status: 500 }
    );
  }
} 
