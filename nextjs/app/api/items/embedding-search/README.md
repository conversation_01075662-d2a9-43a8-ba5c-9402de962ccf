# Embedding Search API

This API endpoint provides vector similarity search for Items (Master Content Pieces) based on text embedding.

## Configuration

To use this API, you need to set the following environment variables:

```
BACKEND_WORKER_URL=https://your-worker-domain.workers.dev
BACKEND_WORKER_API_KEY=your-worker-api-key
```

You can create a `.env.local` file in the nextjs directory to set these variables for local development.

## Development Mode

When running in development mode (`NODE_ENV=development`), if the required environment variables are not set, the API will automatically use a mock implementation that returns sample data. This is useful for development and testing without requiring a configured backend worker.

To use the real implementation in development, ensure the environment variables are properly set.

## API Usage

**Endpoint:** `/api/items/embedding-search`
**Method:** `POST`
**Content-Type:** `application/json`

**Request Body:**
```json
{
  "text": "your search text", // Required
  "language": "en", // Optional, default: "en", valid values: "en" or "zh" 
  "threshold": 0.75, // Optional, minimum similarity threshold (0-1)
  "limit": 10 // Optional, maximum number of results to return
}
```

**Response:**
```json
{
  "data": [], // Array of matching Items with details
  "count": 0, // Total number of matches
  "language": "en" // Language used for the search
}
```

## Error Handling

The API will return appropriate error responses in these cases:

- **400 Bad Request**: Invalid input parameters
- **500 Internal Server Error**: Server-side errors, including missing configuration

Error responses include:
```json
{
  "error": "Error message",
  "details": "Additional error details", // When available
  "message": "Error message from exception", // When available
  "stack": "Stack trace" // Only in development mode
}
```

## Testing

You can test the API using the provided `api-test.js` script:

```bash
cd nextjs
node api-test.js
```

Make sure you have your local development server running before testing:

```bash
npm run dev
# or
pnpm dev
```

## Troubleshooting

- If you receive a "Backend worker service is not configured" error, check your environment variables.
- If you're in development mode without environment variables set, the API will use mock data.
- If you receive a "Failed to generate embedding via backend worker" error, make sure your worker is running and accessible.
- The embedding generation is handled by the backend worker, which must be properly deployed and configured. 