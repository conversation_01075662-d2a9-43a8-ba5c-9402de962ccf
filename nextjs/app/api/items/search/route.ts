
import { NextRequest, NextResponse } from 'next/server';
import { searchItems } from '@/models/items';
import { badRequestResponse, serverErrorResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const lang = searchParams.get('lang') || 'en';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(searchParams.get('limit') || '50', 10), 100);
    
    if (!query) {
      return badRequestResponse('Search query is required');
    }

    const { data, count, error } = await searchItems(query, lang, page, limit);
    
    if (error) {
      return serverErrorResponse('Failed to search Items', error);
    }
    
    return NextResponse.json({ 
      data, 
      count 
    }, {
      headers: {
        'X-Total-Count': (count ?? 0).toString(),
        'X-Page': page.toString(),
        'X-Per-Page': limit.toString()
      }
    });
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
} 
