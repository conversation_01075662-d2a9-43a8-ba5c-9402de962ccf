
import { NextRequest, NextResponse } from 'next/server';
import { incrementClicks } from '@/models/items';
import { badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth'; // Using response helpers

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

interface Params {
  params: Promise<{ uuid: string }>;
}

// POST method to increment clicks
export async function POST(request: NextRequest, { params }: Params) {
  try {
    const { uuid } = await params;

    if (!uuid) {
      return badRequestResponse('Item UUID is required.');
    }

    // Basic UUID validation (simple check)
    if (uuid.length !== 36) {
        return badRequestResponse('Invalid Item UUID format.');
    }

    const { error } = await incrementClicks(uuid);

    if (error) {
        // Check if the error indicates the Item wasn't found (this depends on the RPC function's behavior)
        // If the RPC doesn't throw a specific error for not found, we might not be able to return 404 easily.
        // Assuming a generic server error for now if increment fails.
        return serverErrorResponse(`Failed to increment clicks for Item UUID ${uuid}`, error);
    }

    // Return 204 No Content on success, as there's no body to return
    return new NextResponse(null, { status: 204 });

  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}
