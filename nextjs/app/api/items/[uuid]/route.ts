
import { NextRequest, NextResponse } from 'next/server';
import { getItemByUuid } from '@/models/items';
import { badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth'; // Using response helpers

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

interface Params {
  params: Promise<{ uuid: string }>;
}

export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { uuid } = await params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en'; // Default to English

    if (!uuid) {
      return badRequestResponse('Item UUID is required.');
    }

    // Basic UUID validation (simple check)
    if (uuid.length !== 36) {
        return badRequestResponse('Invalid Item UUID format.');
    }

    const { data, error } = await getItemByUuid(uuid, lang);

    if (error) {
      // Don't treat 'Not Found' as a server error
      if (error.code === 'PGRST116') {
        return notFoundResponse(`Item with UUID ${uuid} not found.`);
      }
      return serverErrorResponse(`Failed to fetch Item with UUID ${uuid}`, error);
    }

    if (!data) {
        return notFoundResponse(`Item with UUID ${uuid} not found.`);
    }

    return NextResponse.json(data);
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}
