import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import {
  getProjectForUser,
  getDiscoveredLinkByUrl,
  createDiscoveredLink,
  updateDiscoveredLinkFromImport,
  updateDomainStatsViaRPC,
  extractDomainFromUrl
} from "@/models/links";


export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { projectId, format, links } = body;

    if (!projectId || !format || !links || !Array.isArray(links)) {
      return NextResponse.json(
        { error: "Missing required fields: projectId, format, or links array" },
        { status: 400 }
      );
    }

    // Verify user has access to the project
    const { data: project, error: projectError } = await getProjectForUser(
      projectId,
      user.uuid
    );

    if (projectError || !project) {
      return NextResponse.json(
        { error: "Project not found or access denied" },
        { status: 404 }
      );
    }

    console.log(`Processing ${links.length} pre-parsed links`);

    let newLinks = 0;
    let updatedLinks = 0;
    const errors: string[] = [];

    for (let i = 0; i < links.length; i++) {
      try {
        const parsedLink = links[i];
        
        // Validate required fields
        if (!parsedLink.url || !parsedLink.source_url) {
          errors.push(`Link ${i + 1}: Missing required URL fields`);
          continue;
        }

        // Check if link already exists in discovered_links (check by URL first)
        const { data: existingLink, error: checkError } = await getDiscoveredLinkByUrl(
          projectId,
          parsedLink.url
        );

        // Handle the case where no link is found (which is expected and not an error)
        if (checkError && checkError.code !== 'PGRST116') {
          errors.push(`Link ${i + 1}: Database error - ${checkError.message}`);
          continue;
        }

        if (existingLink) {
          // Update existing link with new data
          const { error: updateError } = await updateDiscoveredLinkFromImport(
            existingLink.id,
            {
              title: parsedLink.title || existingLink.title,
              anchor_text: parsedLink.anchor_text || existingLink.anchor_text,
              status: 'SUBMITTED' // CSV imported links are considered submitted
            }
          );

          if (updateError) {
            errors.push(`Link ${i + 1}: Update error - ${updateError.message}`);
            continue;
          }
          updatedLinks++;
        } else {
          // Validate link_type to match database constraint (dofollow/nofollow)
          const linkType: 'dofollow' | 'nofollow' = (parsedLink.is_nofollow || parsedLink.link_type === 'nofollow') 
            ? 'nofollow' 
            : 'dofollow';
          
          // Create new discovered link
          const { error: insertError } = await createDiscoveredLink({
            project_id: projectId,
            user_id: user.uuid,
            url: parsedLink.url,
            title: parsedLink.title || 'Untitled',
            source_url: parsedLink.source_url,
            anchor_text: parsedLink.anchor_text || '',
            link_type: linkType,
            is_active: true,
            status: 'SUBMITTED', // CSV imported links are considered submitted
            discovered_at: new Date().toISOString()
          });

          if (insertError) {
            errors.push(`Link ${i + 1}: Insert error - ${insertError.message}`);
            continue;
          }
          newLinks++;
        }
        
        // Always update domain statistics in all_links table for both new and existing links
        const domain = extractDomainFromUrl(parsedLink.url);
        if (domain) {
          const { error: statsError } = await updateDomainStatsViaRPC(
            domain,
            parsedLink.dr_score || null,
            parsedLink.traffic || 0,
            false // Default to false, will be updated by cron worker
          );
          
          if (statsError) {
            console.warn(`Failed to update domain stats for ${domain}:`, statsError.message);
          }
        }
      } catch (error) {
        errors.push(`Link ${i + 1}: ${error}`);
      }
    }

    console.log(`Import completed: ${newLinks} new, ${updatedLinks} updated, ${errors.length} errors`);
    console.log('First few errors:', errors.slice(0, 5));
    
    return NextResponse.json({
      success: true,
      newLinks,
      updatedLinks,
      totalProcessed: links.length,
      errors: errors.length > 0 ? errors : undefined,
      message: `Import completed. Processed ${links.length} links: ${newLinks} new links, ${updatedLinks} updated links.`,
      debug: {
        firstError: errors[0],
        sampleLinkData: links[0] ? {
          hasUrl: !!links[0].url,
          hasSourceUrl: !!links[0].source_url,
          hasTitle: !!links[0].title,
          keys: Object.keys(links[0])
        } : null
      }
    });

  } catch (error) {
    console.error("CSV import error:", error);
    return NextResponse.json(
      { error: "Failed to process CSV import" },
      { status: 500 }
    );
  }
}