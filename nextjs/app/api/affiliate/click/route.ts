
import { NextResponse } from 'next/server';
import { incrementAffiliateClicks } from '@/models/affiliate';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { id } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Product ID is required' }, 
        { status: 400 }
      );
    }
    
    const { data, error } = await incrementAffiliateClicks(id);
    
    if (error) {
      console.error('API Error tracking affiliate click:', error);
      return NextResponse.json(
        { error: error.message || 'Database error' }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in affiliate click API:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to track click' }, 
      { status: 500 }
    );
  }
}