# Auth API Documentation

This API route handles user authentication using NextAuth.js. It automatically provides several standard endpoints for managing user sessions and authentication flows.

The specific behavior and available providers (e.g., GitHub, Google, Credentials) depend on the configuration defined in the `@/auth` module (`web-AiMCP/nextjs/auth/index.ts` or similar).

## Standard NextAuth.js Endpoints

The following endpoints are typically exposed under `/api/auth/`:

*   **`GET /api/auth/providers`**
    *   **Description:** Returns a list of configured authentication providers. Useful for dynamically generating login buttons.
    *   **Response:** `200 OK` - JSON object mapping provider IDs to their configuration details.

*   **`POST /api/auth/signin`**
    *   **Description:** Initiates the sign-in process. Can be used to redirect to a provider's login page or handle credential submissions.
    *   **Request:** Depends on the provider (e.g., form data for credentials, may redirect).
    *   **Response:** Typically redirects the user or returns session information upon success.

*   **`GET /api/auth/signin`**
    *   **Description:** Often displays a default sign-in page listing configured providers, unless a custom page is specified.
    *   **Response:** HTML page or redirect.

*   **`POST /api/auth/signin/{provider}`**
    *   **Description:** Initiates the sign-in flow for a specific OAuth provider (e.g., `/api/auth/signin/github`).
    *   **Response:** Redirects the user to the provider's authentication page.

*   **`POST /api/auth/signout`**
    *   **Description:** Logs the user out.
    *   **Request Body:** Typically requires a CSRF token.
    *   **Response:** Redirects the user (often to the homepage) after clearing the session.

*   **`GET /api/auth/signout`**
    *   **Description:** Often displays a confirmation page for signing out.
    *   **Response:** HTML page or redirect.

*   **`GET /api/auth/session`**
    *   **Description:** Returns the current user's session status and data (if logged in).
    *   **Response:** `200 OK` - JSON object containing session details (e.g., user info, expiry) or `null` if not authenticated.

*   **`GET /api/auth/csrf`**
    *   **Description:** Returns a CSRF token, necessary for POST requests like sign-in/sign-out when using email or credentials providers.
    *   **Response:** `200 OK` - JSON object containing the CSRF token: `{ "csrfToken": "..." }`.

*   **`GET /api/auth/callback/{provider}`**
    *   **Description:** The callback URL where OAuth providers redirect the user after successful authentication (e.g., `/api/auth/callback/github`). Handled internally by NextAuth.js.

*   **`GET /api/auth/error`**
    *   **Description:** Displays an error page if something goes wrong during authentication.
    *   **Query Parameters:** Can include `error` indicating the type of error.
    *   **Response:** HTML error page.

**Note:** The exact behavior, request parameters, and response formats might vary slightly based on the NextAuth.js version and specific configuration. Refer to the official NextAuth.js documentation for detailed information.
