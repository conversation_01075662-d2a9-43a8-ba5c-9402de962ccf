import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check if all required environment variables are present
    const requiredEnvVars = {
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
      AUTH_SECRET: process.env.AUTH_SECRET,
      AUTH_GOOGLE_ID: process.env.AUTH_GOOGLE_ID,
      AUTH_GOOGLE_SECRET: process.env.AUTH_GOOGLE_SECRET,
      NEXT_PUBLIC_AUTH_GOOGLE_ENABLED: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED,
    };

    const missingVars = Object.entries(requiredEnvVars)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missingVars.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Missing environment variables',
        missing: missingVars,
      }, { status: 500 });
    }

    // Test Google OAuth endpoints are reachable
    const googleWellKnown = 'https://accounts.google.com/.well-known/openid_configuration';
    
    try {
      const response = await fetch(googleWellKnown, {
        method: 'GET',
        headers: {
          'User-Agent': 'NextAuth.js',
        },
      });

      if (!response.ok) {
        throw new Error(`Google well-known endpoint returned ${response.status}`);
      }

      const googleConfig = await response.json();

      return NextResponse.json({
        success: true,
        message: 'Authentication configuration is valid',
        config: {
          nextAuthUrl: process.env.NEXTAUTH_URL,
          googleClientId: process.env.AUTH_GOOGLE_ID,
          googleEnabled: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === 'true',
          googleEndpoints: {
            authorization: googleConfig.authorization_endpoint,
            token: googleConfig.token_endpoint,
            userinfo: googleConfig.userinfo_endpoint,
          },
        },
      });
    } catch (fetchError) {
      return NextResponse.json({
        success: false,
        error: 'Cannot reach Google OAuth endpoints',
        details: fetchError instanceof Error ? fetchError.message : 'Unknown error',
      }, { status: 500 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Authentication test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 