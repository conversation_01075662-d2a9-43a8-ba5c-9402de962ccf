import { NextRequest, NextResponse } from "next/server";
import { isAdminAuthenticated } from "@/lib/adminAuth";
import { getAdminTableData, deleteAdminTableItems } from "@/models/admin";


// Sample data generator for development/testing
function generateSampleData(table: string, limit: number) {
  const now = new Date().toISOString();
  const sampleData = [];

  for (let i = 0; i < Math.min(limit, 10); i++) {
    switch (table) {
      case "projects":
        sampleData.push({
          id: `project-${i}`,
          name: `示例项目 ${i + 1}`,
          domain: `example${i + 1}.com`,
          description: `这是第${i + 1}个示例项目的描述`,
          total_links: Math.floor(Math.random() * 100),
          indexed_links: Math.floor(Math.random() * 50),
          user_id: `user-${i + 1}`,
          created_at: now,
          updated_at: now
        });
        break;
      case "linkResources":
        sampleData.push({
          id: `resource-${i}`,
          url: `https://example${i + 1}.com/resource`,
          title: `链接资源 ${i + 1}`,
          link_type: i % 2 === 0 ? "free" : "paid",
          price: i % 2 === 0 ? null : (i + 1) * 10,
          currency: "USD",
          source: `来源 ${i + 1}`,
          acquisition_method: `获取方式 ${i + 1}`,
          user_id: `user-${i + 1}`,
          created_at: now,
          updated_at: now
        });
        break;
      case "allLinks":
        sampleData.push({
          id: `link-${i}`,
          domain: `domain${i + 1}.com`,
          dr_score: Math.floor(Math.random() * 100),
          traffic: Math.floor(Math.random() * 10000),
          backlink_count: Math.floor(Math.random() * 500),
          is_indexed: i % 2 === 0,
          last_updated: now
        });
        break;
      case "allLinksHistory":
        sampleData.push({
          id: `history-${i}`,
          domain: `domain${i + 1}.com`,
          dr_score: Math.floor(Math.random() * 100),
          traffic: Math.floor(Math.random() * 10000),
          backlink_count: Math.floor(Math.random() * 500),
          is_indexed: i % 2 === 0,
          checked_at: now
        });
        break;
      case "discoveredLinks":
        sampleData.push({
          id: `discovered-${i}`,
          url: `https://discovered${i + 1}.com/page`,
          title: `发现的链接 ${i + 1}`,
          anchor_text: `锚文本 ${i + 1}`,
          link_type: i % 2 === 0 ? "dofollow" : "nofollow",
          status: ["NEW", "SUBMITTED", "INDEXED", "ARCHIVED"][i % 4],
          project_id: `project-${i + 1}`,
          user_id: `user-${i + 1}`,
          discovered_at: now,
          created_at: now,
          updated_at: now
        });
        break;
      case "userConfigs":
        sampleData.push({
          id: i + 1,
          user_id: `user-${i + 1}`,
          project_id: `project-${i + 1}`,
          config_type: ["analytics_google", "analytics_plausible", "analytics_umami"][i % 3],
          config_name: `配置 ${i + 1}`,
          config_data: { example: "data" },
          is_active: i % 2 === 0,
          created_at: now,
          updated_at: now
        });
        break;
    }
  }
  
  return sampleData;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const table = searchParams.get("table") || "projects";
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const search = searchParams.get("search") || "";


    const { data, error, count } = await getAdminTableData({
      table,
      limit,
      offset,
      search
    });

    // if (error) {
    //   console.error("Database error:", error);
      
    //   // Return sample data for development/testing
    //   const sampleData = generateSampleData(table, limit);
    //   return NextResponse.json({
    //     data: sampleData,
    //     pagination: {
    //       total: sampleData.length,
    //       limit,
    //       offset,
    //       hasMore: false
    //     }
    //   });
    // }

    const total = count || 0;
    const hasMore = offset + limit < total;

    return NextResponse.json({
      data: data || [],
      pagination: {
        total,
        limit,
        offset,
        hasMore
      }
    });

  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { itemIds, table } = await request.json();

    if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
      return NextResponse.json(
        { error: "No items selected for deletion" },
        { status: 400 }
      );
    }

    const { success, failed, errors } = await deleteAdminTableItems(table, itemIds);

    if (failed > 0) {
      console.error("Delete errors:", errors);
      return NextResponse.json({
        message: `Partial success: ${success} deleted, ${failed} failed`,
        errors
      }, { status: 207 }); // Multi-Status
    }

    return NextResponse.json({
      message: `Successfully deleted ${success} items`
    });

  } catch (error) {
    console.error("Delete API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}