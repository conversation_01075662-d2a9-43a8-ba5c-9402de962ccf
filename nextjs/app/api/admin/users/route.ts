import { NextRequest, NextResponse } from "next/server";
import { getAllUsersWithStats } from "@/models/links";
import { isAdminAuthenticated } from "@/lib/adminAuth";


export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const { data: users, error } = await getAllUsersWithStats();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ users });
  } catch (error) {
    console.error("Error fetching admin users:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 