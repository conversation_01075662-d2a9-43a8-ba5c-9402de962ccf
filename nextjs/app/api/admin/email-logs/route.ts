
import { NextRequest, NextResponse } from 'next/server';
import { getEmailLogs } from '@/models/emailNotification';
import { isAdminAuthenticated, unauthorizedResponse, serverErrorResponse } from '@/lib/adminAuth';
import { processPendingEmails as processPendingEmailsService } from '@/lib/emailService';

export const dynamic = 'force-dynamic';

// GET /api/admin/email-logs - Get all email logs with pagination and optional status filter
export async function GET(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status') as 'pending' | 'sent' | 'failed' | undefined;

    const { data, error, count } = await getEmailLogs(limit, page, status);

    if (error) {
      return serverErrorResponse('Failed to fetch email logs', error);
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: count ? Math.ceil(count / limit) : 0
      }
    });
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}

// POST /api/admin/email-logs/process - Process pending emails
export async function POST(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const result = await processPendingEmailsService(10); // Process 10 emails at a time

    return NextResponse.json({
      message: `Processed ${result.processed} emails: ${result.success} succeeded, ${result.failed} failed.`,
      ...result
    });
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred while processing emails', err);
  }
} 