# Admin API Documentation

This document outlines the API endpoints available under the `/api/admin` path. All endpoints require administrator authentication.

## Item Management (`/api/admin/items`)

### Add New Item

*   **Method:** `POST`
*   **Path:** `/api/admin/items`
*   **Authentication:** Admin Required
*   **Request Body:** `application/json`
    ```json
    {
      "uuid": "string (optional, auto-generated if omitted)",
      "name": "string (required)",
      "brief": "string (required)",
      "item_avatar_url": "string (optional)",
      "user_avatar_url": "string (optional)",
      "website_url": "string (required)",
      "author_name": "string (required)",
      "tags": ["string"] (optional, default: []),
      "metadata": { "key": "value" } (optional, default: {}),
      "is_recommended": "boolean (optional, default: false)",
      "is_official": "boolean (optional, default: false)",
      "allow_public": "boolean (optional, default: true)",
      "clicks": "number (optional, default: 0)",
      "localizations": [
        {
          "language_code": "string (required)",
          "brief": "string (required)",
          "detail": "string (required)",
          "processinfo": "string (optional)"
        }
      ] (optional, default: [])
    }
    ```
*   **Responses:**
    *   `201 Created`: Item added successfully. Returns the created Item object.
    *   `400 Bad Request`: Missing required fields or invalid JSON format.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `500 Internal Server Error`: Failed to add Item due to a server error.

### Update Existing Item

*   **Method:** `PUT`
*   **Path:** `/api/admin/items/{uuid}`
*   **Authentication:** Admin Required
*   **Path Parameters:**
    *   `uuid`: string (required) - The UUID of the Item to update.
*   **Request Body:** `application/json` (Include only fields to update)
    ```json
    {
      "name": "string",
      "brief": "string",
      "item_avatar_url": "string",
      "user_avatar_url": "string",
      "website_url": "string",
      "author_name": "string",
      "tags": ["string"],
      "metadata": { "key": "value" },
      "is_recommended": "boolean",
      "is_official": "boolean",
      "allow_public": "boolean",
      "localizations": [ // Upserts localizations based on language_code
        {
          "language_code": "string (required)",
          "brief": "string (required)",
          "detail": "string (required)",
          "processinfo": "string (optional)"
        }
      ]
    }
    ```
*   **Responses:**
    *   `200 OK`: Item updated successfully. Returns `{ "message": "Item updated successfully." }`.
    *   `207 Multi-Status`: Item updated, but some localizations failed. Returns `{ "message": "Item updated, but some localizations failed.", "errors": [...] }`.
    *   `400 Bad Request`: Invalid UUID or invalid JSON format.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `404 Not Found`: Item with the specified UUID not found.
    *   `500 Internal Server Error`: Failed to update Item due to a server error.

### Archive (Delete) Item

*   **Method:** `DELETE`
*   **Path:** `/api/admin/items/{uuid}`
*   **Authentication:** Admin Required
*   **Path Parameters:**
    *   `uuid`: string (required) - The UUID of the Item to archive.
*   **Responses:**
    *   `204 No Content`: Item archived successfully.
    *   `400 Bad Request`: Invalid UUID.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `500 Internal Server Error`: Failed to archive Item due to a server error.

### Delete Tag

*   **Method:** `DELETE`
*   **Path:** `/api/admin/items/tags/{id}`
*   **Authentication:** Admin Required
*   **Description:** Deletes a tag by its ID and removes it from all Items that have this tag.
*   **Path Parameters:**
    *   `id`: number (required) - The ID of the tag to delete.
*   **Responses:**
    *   `200 OK`: Tag deleted successfully. Returns information about the deleted tag and affected Items.
        ```json
        {
          "success": true,
          "message": "Tag \"tag_name\" has been deleted and removed from X Items"
        }
        ```
    *   `400 Bad Request`: Missing or invalid tag ID.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `404 Not Found`: Tag with specified ID not found.
    *   `500 Internal Server Error`: Failed to delete tag.

## Submission Management (`/api/admin/submissions`)

### List Submissions

*   **Method:** `GET`
*   **Path:** `/api/admin/submissions`
*   **Authentication:** Admin Required
*   **Query Parameters:**
    *   `page`: number (optional, default: 1) - Page number for pagination.
    *   `limit`: number (optional, default: 50, max: 100) - Number of submissions per page.
*   **Responses:**
    *   `200 OK`: Returns a list of submissions with pagination info in headers (`X-Total-Count`, `X-Page`, `X-Per-Page`).
      ```json
      {
        "data": [ /* array of submission objects */ ],
        "count": number // total number of submissions
      }
      ```
    *   `400 Bad Request`: Invalid page or limit parameter.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `500 Internal Server Error`: Failed to fetch submissions.

### Approve Submission

*   **Method:** `POST`
*   **Path:** `/api/admin/submissions/{id}/approve`
*   **Authentication:** Admin Required
*   **Path Parameters:**
    *   `id`: number (required) - The ID of the submission to approve.
*   **Responses:**
    *   `201 Created`: Submission approved successfully, and a new Item was created. Returns the newly created Item object.
    *   `400 Bad Request`: Invalid submission ID or submission already processed.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `404 Not Found`: Submission with the specified ID not found.
    *   `500 Internal Server Error`: Failed to approve submission.

### Reject Submission

*   **Method:** `POST`
*   **Path:** `/api/admin/submissions/{id}/reject`
*   **Authentication:** Admin Required
*   **Path Parameters:**
    *   `id`: number (required) - The ID of the submission to reject.
*   **Responses:**
    *   `204 No Content`: Submission rejected successfully.
    *   `400 Bad Request`: Invalid submission ID or submission already processed.
    *   `401 Unauthorized`: Admin authentication failed.
    *   `404 Not Found`: Submission with the specified ID not found.
    *   `500 Internal Server Error`: Failed to reject submission.

## Post Management (`/api/admin/posts`)
