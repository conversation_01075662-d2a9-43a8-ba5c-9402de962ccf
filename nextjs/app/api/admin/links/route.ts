import { NextRequest, NextResponse } from "next/server";
import { getUserInfo } from "@/services/user";
import { getAllLinkResourcesForAdmin, deleteLinkResourcesInBatch } from "@/models/links";


export async function GET(request: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check admin permissions
    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "all";
    const link_type = searchParams.get("link_type") || "all";
    const user_id = searchParams.get("user_id") || "";

    const filters = {
      search: search || undefined,
      status: status !== "all" ? status : undefined,
      link_type: link_type !== "all" ? link_type : undefined,
      user_id: user_id && user_id !== "all" ? user_id : undefined,
    };

    const { data: links, error, count } = await getAllLinkResourcesForAdmin(limit, offset, filters);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      links: links || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit,
      },
    });
  } catch (error) {
    console.error("Error fetching admin links:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check admin permissions
    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { linkIds } = await request.json();
    
    if (!linkIds || !Array.isArray(linkIds) || linkIds.length === 0) {
      return NextResponse.json(
        { error: "Invalid link IDs" },
        { status: 400 }
      );
    }

    const result = await deleteLinkResourcesInBatch(linkIds);

    return NextResponse.json({
      message: `Batch delete completed. ${result.success} successful, ${result.failed} failed.`,
      success: result.success,
      failed: result.failed,
      errors: result.errors
    });
  } catch (error) {
    console.error("Error deleting links:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 