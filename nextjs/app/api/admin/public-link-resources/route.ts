import { NextRequest, NextResponse } from 'next/server';
import { isAdminAuthenticated } from '@/lib/adminAuth';
import { 
  getPublicLinkResourcesForAdmin,
  createPublicLinkResource,
  CreatePublicLinkResourceData
} from '@/models/public-link-resources';
import { z } from 'zod';

const publicLinkResourceSchema = z.object({
  domain: z.string().min(1),
  title: z.string().min(1),
  website_url: z.string().url(),
  submission_method: z.string().min(1),
  submission_url: z.string().url().optional(),
  contact_email: z.string().email().optional(),
  is_paid: z.boolean(),
  price_range: z.string().optional(),
  currency: z.string().default('USD'),
  category: z.string().optional(),
  description: z.string().optional(),
  requirements: z.string().optional(),
  response_time: z.string().optional(),
  success_rate: z.number().min(0).max(100).optional(),
});

// GET - List all public link resources (admin view)
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get public link resources for admin
    const { data, error } = await getPublicLinkResourcesForAdmin(limit, offset);

    if (error || !data) {
      console.error('Failed to fetch public link resources:', error);
      return NextResponse.json({ error: 'Failed to fetch resources' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Create new public link resource
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = publicLinkResourceSchema.parse(body);

    // Create public link resource using model function
    const { data, error } = await createPublicLinkResource(validatedData as CreatePublicLinkResourceData);

    if (error || !data) {
      console.error('Failed to create public link resource:', error);
      return NextResponse.json({ error: 'Failed to create resource' }, { status: 500 });
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid data', details: error.errors }, { status: 400 });
    }
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}