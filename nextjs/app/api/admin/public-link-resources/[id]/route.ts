
import { NextRequest, NextResponse } from 'next/server';
import { isAdminAuthenticated } from '@/lib/adminAuth';
import { 
  getPublicLinkResourceById,
  updatePublicLinkResource,
  deletePublicLinkResource
} from '@/models/public-link-resources';
import { z } from 'zod';

const publicLinkResourceUpdateSchema = z.object({
  domain: z.string().min(1).optional(),
  title: z.string().min(1).optional(),
  website_url: z.string().url().optional(),
  submission_method: z.string().min(1).optional(),
  submission_url: z.string().url().optional(),
  contact_email: z.string().email().optional(),
  is_paid: z.boolean().optional(),
  price_range: z.string().optional(),
  currency: z.string().optional(),
  category: z.string().optional(),
  description: z.string().optional(),
  requirements: z.string().optional(),
  response_time: z.string().optional(),
  success_rate: z.number().min(0).max(100).optional(),
  is_active: z.boolean().optional(),
});

async function checkAdminAuth(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  
  if (!isAdmin) {
    return { error: 'Unauthorized', status: 401 };
  }

  return { success: true };
}

// GET - Get single public link resource
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await checkAdminAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id } = await params;
    
    // Get public link resource with stats
    const { data, error } = await getPublicLinkResourceById(id);

    if (error || !data) {
      console.error('Failed to fetch public link resource:', error);
      return NextResponse.json({ error: 'Resource not found' }, { status: 404 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT - Update public link resource
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await checkAdminAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = publicLinkResourceUpdateSchema.parse(body);

    // Update public link resource using model function
    const { data, error } = await updatePublicLinkResource(id, validatedData);

    if (error || !data) {
      console.error('Failed to update public link resource:', error);
      return NextResponse.json({ error: 'Failed to update resource' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid data', details: error.errors }, { status: 400 });
    }
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE - Delete public link resource
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await checkAdminAuth(request);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id } = await params;
    
    // Delete public link resource using model function
    const { success, error } = await deletePublicLinkResource(id);

    if (error || !success) {
      console.error('Failed to delete public link resource:', error);
      return NextResponse.json({ error: 'Failed to delete resource' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}