import { NextRequest, NextResponse } from 'next/server';
import { isAdminAuthenticated } from '@/lib/adminAuth';
import { bulkInsertPublicLinkResources } from '@/models/public-link-resources';
import { BulkInsertRequest, ImportResult, PublicLinkResourceData } from '@/types/import';

/**
 * Bulk insert public link resources
 * POST /api/admin/import/public-links/bulk-insert
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user has admin access
    const isAdmin = await isAdminAuthenticated(request);
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: BulkInsertRequest = await request.json();
    const { data, configuration } = body;

    if (!data || !Array.isArray(data) || data.length === 0) {
      return NextResponse.json({ error: 'No data provided' }, { status: 400 });
    }

    // Use model function to handle bulk insert
    const result = await bulkInsertPublicLinkResources(data, configuration);

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in bulk insert:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}