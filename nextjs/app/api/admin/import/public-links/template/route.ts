import { NextRequest, NextResponse } from 'next/server';
import { CSV_TEMPLATE_FIELDS } from '@/types/import';

/**
 * Download CSV template for public link resources import
 * GET /api/admin/import/public-links/template
 */
export async function GET(request: NextRequest) {
  try {
    // Create CSV header
    const headers = CSV_TEMPLATE_FIELDS.map(field => field.key).join(',');
    
    // Create sample data rows
    const sampleData = [
      {
        domain: 'example.com',
        title: 'Example Directory',
        website_url: 'https://example.com',
        submission_method: 'online_form',
        submission_url: 'https://example.com/submit',
        contact_email: '<EMAIL>',
        is_paid: 'false',
        price_range: '',
        currency: 'USD',
        category: 'Directory',
        description: 'Sample directory description',
        requirements: 'Basic requirements',
        response_time: '1-2 days',
        success_rate: '0.8'
      },
      {
        domain: 'paid-example.com',
        title: 'Premium Link Directory',
        website_url: 'https://paid-example.com',
        submission_method: 'email',
        submission_url: '',
        contact_email: '<EMAIL>',
        is_paid: 'true',
        price_range: '$50-100',
        currency: 'USD',
        category: 'Premium Directory',
        description: 'High-quality paid directory',
        requirements: 'Premium requirements',
        response_time: 'Same day',
        success_rate: '0.95'
      }
    ];

    // Convert sample data to CSV rows
    const csvRows = sampleData.map(row => 
      CSV_TEMPLATE_FIELDS.map(field => {
        const value = row[field.key] || '';
        // Escape values with commas or quotes
        return value.includes(',') || value.includes('"') 
          ? `"${value.replace(/"/g, '""')}"` 
          : value;
      }).join(',')
    );

    // Combine header and data
    const csvContent = [headers, ...csvRows].join('\n');

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="public_link_resources_template.csv"',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Error generating CSV template:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSV template' },
      { status: 500 }
    );
  }
}