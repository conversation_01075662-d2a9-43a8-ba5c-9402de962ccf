
import { NextRequest, NextResponse } from 'next/server';
import { updateItems, archiveItem, updateItemLocalizations, getItemByUuid } from '@/models/items';
import { getSubmissionEmailByItem } from '@/models/submission';
import { Items, ItemLocalization } from '@/types/items';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth';
import { sendItemPublishedEmail } from '@/lib/emailService';
import { notificationService } from '@/lib/notification';

export const dynamic = 'force-dynamic';

interface Params {
  params: Promise<{ uuid: string }>;
}

// PUT /api/admin/items/[uuid] - Update an existing Item
export async function PUT(request: NextRequest, { params }: Params) {
  const { uuid } = await params;
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }
  if (!uuid || uuid.length !== 36) {
    return badRequestResponse('Invalid or missing Item UUID.');
  }

  try {
    const body = await request.json();

    // Separate localizations from other Item fields
    const { localizations, ...itemUpdateData } = body;

    // Update core Item data (excluding fields managed elsewhere like clicks, created_at)
    const updateData: Partial<Items> = {
        uuid: uuid, // Ensure uuid is included for the model function
        name: itemUpdateData.name,
        brief: itemUpdateData.brief,
        item_avatar_url: itemUpdateData.item_avatar_url,
        user_avatar_url: itemUpdateData.user_avatar_url,
        website_url: itemUpdateData.website_url, // Allow updating website_url if needed
        author_name: itemUpdateData.author_name,
        tags: itemUpdateData.tags,
        metadata: itemUpdateData.metadata,
        is_recommended: itemUpdateData.is_recommended,
        is_official: itemUpdateData.is_official,
        allow_public: itemUpdateData.allow_public,
        // Do not allow updating 'clicks' or 'created_at' via PUT
    };

    // Filter out undefined values to avoid overwriting existing fields with null
    const filteredUpdateData = Object.entries(updateData).reduce((acc, [key, value]) => {
        if (value !== undefined) {
            // Use a more flexible type for the accumulator temporarily
            (acc as any)[key] = value;
        }
        return acc;
    }, {} as Partial<Items>);


    // Update the main Item record
    let isNewlyPublic = false;
    if (Object.keys(filteredUpdateData).length > 1) { // Check if there's more than just the uuid
        const { 
            data: updatedItem, 
            error: updateError,
            isNewlyPublic: newPublishStatus 
        } = await updateItems(filteredUpdateData);
        
        if (updateError) {
            // Check if it's a PostgrestError before accessing code
            if (updateError && 'code' in updateError && updateError.code === 'PGRST116') {
                return notFoundResponse(`Item with UUID ${uuid} not found.`);
            }
            return serverErrorResponse(`Failed to update Item ${uuid}`, updateError);
        }
        if (!updatedItem) {
             return notFoundResponse(`Item with UUID ${uuid} not found after update attempt.`);
        }
        
        isNewlyPublic = !!newPublishStatus;
    }


    // Update localizations if provided (using upsert)
    let localizationErrors: any[] = [];
    if (Array.isArray(localizations)) {
        for (const loc of localizations) {
            if (loc.language_code && loc.brief && loc.detail) {
                const { error: locError } = await updateItemLocalizations(
                    uuid,
                    loc.language_code,
                    loc.brief,
                    loc.detail,
                    loc.processinfo || ''
                );
                if (locError) {
                    localizationErrors.push({ lang: loc.language_code, error: locError.message });
                }
            } else {
                 localizationErrors.push({ lang: loc.language_code || 'unknown', error: 'Missing required fields (language_code, brief, detail)' });
            }
        }
    }

    // If the Item was just made public, send a notification email
    if (isNewlyPublic) {
        try {
            // Get complete Item data for email context
            const { data: itemData } = await getItemByUuid(uuid);

            if (itemData) {
                // Check if there is an email associated with this Item (from submission)
                const { data: submissionData } = await getSubmissionEmailByItem(
                    itemData.name,
                    itemData.author_name
                );

                if (submissionData && submissionData.email) {
                    // Send email notification
                    await sendItemPublishedEmail(submissionData.email, itemData);
                    
                    // Log success in admin notification
                    await notificationService.notify({
                        title: 'Item Publication Email Sent',
                        content: `Email notification for Item "${itemData.name}" publication sent to ${submissionData.email}`,
                        level: 'success',
                        data: {
                            'Item': itemData.name,
                            'Author': itemData.author_name,
                            'Email': submissionData.email,
                        }
                    });
                }
            }
        } catch (emailError) {
            console.error('Error sending Item publication email:', emailError);
            // Continue with the update even if email sending fails
        }
    }

    if (localizationErrors.length > 0) {
        // Return partial success with localization errors
        console.warn(`Errors updating localizations for Item ${uuid}:`, localizationErrors);
        return NextResponse.json({ message: `Item updated, but some localizations failed.`, errors: localizationErrors }, { status: 207 }); // Multi-Status
    }

    return NextResponse.json({ message: "Item updated successfully." }); // Simpler response


  } catch (err: any) {
     if (err instanceof SyntaxError) {
        return badRequestResponse('Invalid JSON format in request body.');
     }
    return serverErrorResponse('An unexpected error occurred during update', err);
  }
}

// DELETE /api/admin/items/[uuid] - Archive (delete) an Item
export async function DELETE(request: NextRequest, { params }: Params) {
  const { uuid } = await params;
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }
  if (!uuid || uuid.length !== 36) {
    return badRequestResponse('Invalid or missing Item UUID.');
  }

  try {
    const { error } = await archiveItem(uuid);

    if (error) {
        // Check if the error indicates not found (depends on model implementation)
        // Assuming generic error for now
        return serverErrorResponse(`Failed to archive Item ${uuid}`, error);
    }

    return new NextResponse(null, { status: 204 }); // 204 No Content

  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred during archive', err);
  }
}
