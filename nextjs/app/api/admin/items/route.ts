
import { NextRequest, NextResponse } from 'next/server';
import { addItems, updateItems, archiveItem } from '@/models/items';
import { Items } from '@/types/items';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

// POST /api/admin/items - Add a new Item
export async function POST(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const body = await request.json();

    // Validate required fields for adding Item
    if (!body.name || !body.brief || !body.website_url || !body.author_name) {
      return badRequestResponse('Missing required fields: name, brief, website_url, author_name');
    }

    // Prepare Item data, including optional localizations
    const itemData: Partial<Items> & { localizations?: any[] } = {
        id: body.id,
        uuid: body.uuid, // Allow providing UUID, otherwise model generates one
        name: body.name,
        brief: body.brief,
        item_avatar_url: body.avatar_url || null,
        website_url: body.website_url,
        author_name: body.author_name,
        tags: body.tags || [],
        metadata: body.metadata || {},
        is_recommended: body.is_recommended === undefined ? false : !!body.is_recommended,
        is_official: body.is_official === undefined ? false : !!body.is_official,
        allow_public: body.allow_public === undefined ? true : !!body.allow_public,
        clicks: body.clicks || 0, // Allow setting initial clicks if needed
        localizations: body.localizations || [], // Expecting array of localization objects
    };

    const { data, error } = await addItems(itemData);

    if (error) {
        return serverErrorResponse('Failed to add Item', error);
    }

    return NextResponse.json(data, { status: 201 }); // 201 Created

  } catch (err: any) {
     if (err instanceof SyntaxError) {
        return badRequestResponse('Invalid JSON format in request body.');
     }
    return serverErrorResponse('An unexpected error occurred', err);
  }
}

// Note: GET (list all), PUT, DELETE will be in separate files ([uuid]/route.ts)
