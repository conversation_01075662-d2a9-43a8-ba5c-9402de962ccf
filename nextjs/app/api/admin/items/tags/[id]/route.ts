
import { NextRequest, NextResponse } from 'next/server';
import { getTagById, getItemsWithTag, removeTagFromItems, updateItemTags, deleteTag, getItemByUuid } from '@/models/items';
import { badRequestResponse, serverErrorResponse, unauthorizedResponse } from '@/lib/adminAuth';
import { cookies } from 'next/headers';

// DELETE /api/admin/items/tags/[id] - Delete a tag by ID (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    // Admin check using cookies
    const cookieStore = await cookies();
    const adminSessionToken = cookieStore.get('admin-session')?.value;
    
    if (!adminSessionToken) {
      return unauthorizedResponse('Admin access required to delete tags');
    }
    
    if (!id) {
      return badRequestResponse('Tag ID is required');
    }
    
    const tagId = parseInt(id, 10);
    
    if (isNaN(tagId)) {
      return badRequestResponse('Invalid tag ID format');
    }
    
    // Get the tag information to verify it exists
    const { data: tagData, error: tagError } = await getTagById(tagId);
    
    if (tagError) {
      if (tagError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Tag not found' }, { status: 404 });
      }
      return serverErrorResponse('Error fetching tag', tagError);
    }
    
    // Begin a transaction to ensure all operations succeed or fail together
    
    // 1. Get all Items that have this tag
    const { data: itemsWithTag, error: itemsQueryError } = await getItemsWithTag(tagId);
    
    if (itemsQueryError) {
      return serverErrorResponse('Error finding Items with this tag', itemsQueryError);
    }
    
    const itemIds = itemsWithTag.map(item => item.item_id);
    
    // 2. Delete all associations between this tag and Items
    const { error: deleteAssociationsError } = await removeTagFromItems(tagId);
    
    if (deleteAssociationsError) {
      return serverErrorResponse('Error removing tag from Items', deleteAssociationsError);
    }
    
    // 3. For each Item that had this tag, update its 'tags' array field
    if (itemIds.length > 0) {
      // Update each Item to remove the tag from its tags array
      for (const itemId of itemIds) {
        // Get the item to access its current tags
        const { data: item, error: itemError } = await getItemByUuid(itemId.toString());
        
        if (itemError) {
          return serverErrorResponse(`Error fetching Item ID ${itemId}`, itemError);
        }
        
        if (item && item.tags && Array.isArray(item.tags)) {
          const updatedTags = item.tags.filter(t => t !== tagData.name);
          
          const { error: updateError } = await updateItemTags(item.id, updatedTags);
            
          if (updateError) {
            return serverErrorResponse(`Error updating tags for Item ID ${item.id}`, updateError);
          }
        }
      }
    }
    
    // 4. Finally, delete the tag itself
    const { error: deleteTagError } = await deleteTag(tagId);
    
    if (deleteTagError) {
      return serverErrorResponse('Error deleting tag', deleteTagError);
    }
    
    return NextResponse.json({
      success: true,
      message: `Tag "${tagData.name}" has been deleted and removed from ${itemIds.length} Items`,
    });
    
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
} 
