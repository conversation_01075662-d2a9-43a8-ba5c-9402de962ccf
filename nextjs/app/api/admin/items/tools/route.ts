
import { NextRequest, NextResponse } from "next/server";
import { getItemToolsList, getItemToolsByUuid, saveItemTools, deleteItemTools } from "@/models/itemTools";
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse } from '@/lib/adminAuth';
import { ItemTool } from "@/types/ItemTools";

// GET /api/admin/mcps/tools - Get all Item tools or a specific one by UUID
export async function GET(req: NextRequest) {
  const isAdmin = await isAdminAuthenticated(req);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const url = new URL(req.url);
    const uuid = url.searchParams.get('uuid');

    if (uuid) {
      // Get tools for a specific MCP
      const { data, error } = await getItemToolsByUuid(uuid);

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      if (!data) {
        return NextResponse.json({ data: null }, { status: 200 });
      }

      return NextResponse.json({ data });
    } else {
      // Get all Item tools
      const { data, error } = await getItemToolsList();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json({ data: data || [] });
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to get Item tools" },
      { status: 500 }
    );
  }
}

// POST /api/admin/mcps/tools - Create new Item tools
export async function POST(req: NextRequest) {
  const isAdmin = await isAdminAuthenticated(req);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const itemTools = await req.json() as ItemTool;

    if (!itemTools.uuid) {
      return badRequestResponse('UUID is required');
    }

    // Set initial timestamps
    const now = new Date().toISOString();
    itemTools.created_at = now;
    itemTools.updated_at = now;

    const { data, error } = await saveItemTools(itemTools);

    if (error) {
      return serverErrorResponse('Failed to create Item tools', error);
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    if (error instanceof SyntaxError) {
      return badRequestResponse('Invalid JSON format in request body.');
    }
    return serverErrorResponse('An unexpected error occurred', error);
  }
}

// PUT /api/admin/mcps/tools - Update existing Item tools
export async function PUT(req: NextRequest) {
  const isAdmin = await isAdminAuthenticated(req);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const itemTools = await req.json() as ItemTool;

    if (!itemTools.uuid) {
      return badRequestResponse('UUID is required');
    }

    // Check if the tools exist
    const { data: existingTools, error: fetchError } = await getItemToolsByUuid(itemTools.uuid);
    
    if (fetchError) {
      return serverErrorResponse('Failed to fetch existing Item tools', fetchError);
    }

    if (!existingTools) {
      return NextResponse.json({ error: "Item tools not found" }, { status: 404 });
    }

    // Update timestamp
    itemTools.updated_at = new Date().toISOString();
    
    // Maintain the original created_at
    itemTools.created_at = existingTools.created_at;

    const { data, error } = await saveItemTools(itemTools);

    if (error) {
      return serverErrorResponse('Failed to update Item tools', error);
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    if (error instanceof SyntaxError) {
      return badRequestResponse('Invalid JSON format in request body.');
    }
    return serverErrorResponse('An unexpected error occurred', error);
  }
}

// DELETE /api/admin/mcps/tools - Delete Item tools
export async function DELETE(req: NextRequest) {
  const isAdmin = await isAdminAuthenticated(req);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const { uuid } = await req.json();
    const { success, error } = await deleteItemTools(uuid);

    if (error) {
      return serverErrorResponse('Failed to delete Item tools', error);
    }

    return NextResponse.json({ success });
  } catch (error: any) {
    return serverErrorResponse('Failed to delete Item tools', error);
  }
}