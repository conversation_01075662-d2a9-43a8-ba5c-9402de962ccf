import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { updateProjectSummaryStats } from "@/models/links";

import { isAdminAuthenticated } from "@/lib/adminAuth";

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const { projectId, type } = body;

    if (!projectId) {
      return NextResponse.json({ error: "Project ID is required" }, { status: 400 });
    }

    let result = {};

    switch (type) {
      case 'project_stats':
        // Update project summary statistics
        const { error: statsError } = await updateProjectSummaryStats(projectId);
        if (statsError) {
          return NextResponse.json({ error: statsError.message }, { status: 500 });
        }
        result = { message: "Project statistics updated successfully" };
        break;

      case 'dr_scores':
        // In a real implementation, this would call external APIs to update DR scores
        // For now, we'll just return a placeholder response
        result = { 
          message: "DR score update initiated. This would normally call Ahrefs API to update domain ratings.",
          note: "Implement integration with Ahrefs API or similar service"
        };
        break;

      case 'traffic_data':
        // In a real implementation, this would call Google Analytics or Search Console APIs
        result = { 
          message: "Traffic data update initiated. This would normally call Google Analytics API.",
          note: "Implement integration with Google Analytics API or Search Console API"
        };
        break;

      case 'indexing_status':
        // In a real implementation, this would call Google Search Console API
        result = { 
          message: "Indexing status update initiated. This would normally call Google Search Console API.",
          note: "Implement integration with Google Search Console API"
        };
        break;

      case 'full_sync':
        // Update project stats first
        await updateProjectSummaryStats(projectId);
        
        result = { 
          message: "Full synchronization initiated. In production, this would update DR scores, traffic data, and indexing status.",
          completed: ["Project statistics updated"],
          pending: ["DR scores", "Traffic data", "Indexing status"],
          note: "Implement integrations with external APIs for complete functionality"
        };
        break;

      default:
        return NextResponse.json({ error: "Invalid sync type" }, { status: 400 });
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error("Error in admin sync:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 