import { NextRequest, NextResponse } from 'next/server';
import { waitlistModel } from '@/models/waitlist';
import { isAdminAuthenticated } from '@/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get comprehensive stats
    const [stats, bySource, recentSignups] = await Promise.all([
      waitlistModel.getWaitlistStats(),
      waitlistModel.getWaitlistBySource(),
      waitlistModel.getRecentSignups(10)
    ]);

    return NextResponse.json({
      success: true,
      data: {
        stats,
        bySource,
        recentSignups
      }
    });

  } catch (error: any) {
    console.error('Waitlist stats error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch waitlist statistics' },
      { status: 500 }
    );
  }
} 