import { NextRequest, NextResponse } from 'next/server';
import { waitlistModel } from '@/models/waitlist';
import { isAdminAuthenticated } from '@/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status') as any || undefined;
    const email = searchParams.get('email') || undefined;

    // Search by email if provided
    if (email) {
      const entry = await waitlistModel.searchByEmail(email);
      return NextResponse.json({
        success: true,
        data: entry ? [entry] : [],
        count: entry ? 1 : 0
      });
    }

    // Get waitlist entries with pagination
    const { data, count } = await waitlistModel.getWaitlistEntries(page, limit, status);

    return NextResponse.json({
      success: true,
      data,
      count,
      page,
      limit,
      totalPages: Math.ceil(count / limit)
    });

  } catch (error: any) {
    console.error('Admin waitlist GET error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch waitlist data' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, status } = body;

    if (!id || !status) {
      return NextResponse.json(
        { error: 'ID and status are required' },
        { status: 400 }
      );
    }

    // Valid statuses
    const validStatuses = ['pending', 'contacted', 'converted', 'unsubscribed'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    const success = await waitlistModel.updateStatus(id, status);

    if (!success) {
      return NextResponse.json(
        { error: 'Waitlist entry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Status updated successfully'
    });

  } catch (error: any) {
    console.error('Admin waitlist PUT error:', error);
    return NextResponse.json(
      { error: 'Failed to update waitlist status' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    const success = await waitlistModel.deleteEntry(id);

    if (!success) {
      return NextResponse.json(
        { error: 'Waitlist entry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Waitlist entry deleted successfully'
    });

  } catch (error: any) {
    console.error('Admin waitlist DELETE error:', error);
    return NextResponse.json(
      { error: 'Failed to delete waitlist entry' },
      { status: 500 }
    );
  }
} 