
import { NextRequest, NextResponse } from 'next/server';
import { updateSubmissionStatusAndPreprocess, getSubmissionById } from '@/models/submission';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

interface Params {
  params: Promise<{ id: string }>;
}

// PUT /api/admin/submissions/[id]/processed - Update a submission status to processed
export async function POST(request: NextRequest, { params }: Params) {
  const { id } = await params;
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  const submissionIdStr = id;
  const submissionId = parseInt(submissionIdStr, 10);

  if (isNaN(submissionId) || submissionId <= 0) {
    return badRequestResponse('Invalid submission ID.');
  }

  try {
    // Get preprocessinfo from request body if provided
    const body = await request.json().catch(() => ({}));
    const { preprocessinfo } = body;

    // Check if submission exists
    const { data: submission, error: fetchError } = await getSubmissionById(submissionId);
      
    if (fetchError || !submission) {
      return notFoundResponse(`Submission with ID ${submissionId} not found.`);
    }

    // Update submission with the processed status and preprocessinfo if provided
    const { error } = await updateSubmissionStatusAndPreprocess(submissionId, 'processed', preprocessinfo);
    
    if (error) {
      if (error.message?.includes("Submission status is already")) {
        return badRequestResponse(error.message);
      }
      return serverErrorResponse(`Failed to update submission ${submissionId} to processed`, error);
    }

    return NextResponse.json({ 
      message: `Submission ${submissionId} updated successfully to processed.` 
    });

  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred during update', err);
  }
} 