
import { NextRequest, NextResponse } from 'next/server';
import { rejectSubmission } from '@/models/submission';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

interface Params {
  params: Promise<{ id: string }>;
}

// POST /api/admin/submissions/[id]/reject - Reject a submission
export async function POST(request: NextRequest, { params }: Params) {
  const { id } = await params;
  // 检查是否是管理员
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    console.log("Admin authentication failed in /api/admin/submissions/[id]/reject");
    return unauthorizedResponse();
  }

  const submissionIdStr = id;
  const submissionId = parseInt(submissionIdStr, 10);

  if (isNaN(submissionId) || submissionId <= 0) {
    return badRequestResponse('Invalid submission ID.');
  }

  try {
    const { error } = await rejectSubmission(submissionId);

    if (error) {
        // Handle specific errors from the model function
        if (error.message.includes("Submission not found")) {
            return notFoundResponse(`Submission with ID ${submissionId} not found.`);
        }
         if (error.message.includes("Submission status is already")) {
            return badRequestResponse(error.message);
        }
        return serverErrorResponse(`Failed to reject submission ${submissionId}`, error);
    }

    // Return 204 No Content on success
    return new NextResponse(null, { status: 204 });

  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred during rejection', err);
  }
}
