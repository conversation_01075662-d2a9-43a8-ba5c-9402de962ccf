
import { NextRequest, NextResponse } from 'next/server';
import { getSubmissions } from '@/models/submission';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

// GET /api/admin/submissions - List all submissions (paginated)
export async function GET(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const { searchParams } = new URL(request.url);
    const pageParam = searchParams.get('page');
    const limitParam = searchParams.get('limit');

    const page = pageParam ? parseInt(pageParam, 10) : 1;
    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    if (isNaN(page) || page < 1) {
      return badRequestResponse('Invalid page number.');
    }
    if (isNaN(limit) || limit < 1 || limit > 100) { // Add a max limit
      return badRequestResponse('Invalid limit value (must be between 1 and 100).');
    }

    const { data, error, count } = await getSubmissions(limit, page);

    if (error) {
      return serverErrorResponse('Failed to fetch Item Submissions', error);
    }

    // Add pagination info
    const response = NextResponse.json({ data, count });
    response.headers.set('X-Total-Count', count?.toString() || '0');
    response.headers.set('X-Page', page.toString());
    response.headers.set('X-Per-Page', limit.toString());

    return response;
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}

// Note: POST (approve/reject) will be in separate files ([id]/approve|reject/route.ts)
