import { NextRequest, NextResponse } from "next/server";
import { isAdminAuthenticated } from "@/lib/adminAuth";


export async function GET(request: NextRequest) {
  try {
    // 检查用户是否为管理员
    const isAdmin = await isAdminAuthenticated(request);
    
    return NextResponse.json({
      isAdmin,
      message: isAdmin ? "Admin access granted" : "Regular user access"
    });
  } catch (error) {
    console.error("Error checking admin status:", error);
    return NextResponse.json({
      isAdmin: false,
      message: "Error checking admin status"
    }, { status: 500 });
  }
} 