# User Cases API Documentation

This document outlines the API endpoints available under the `/api/admin/user-cases` path for managing user cases showcasing Item usage from social media platforms.

## Get All User Cases

*   **Method:** `GET`
*   **Path:** `/api/admin/user-cases`
*   **Authentication:** Admin access required
*   **Description:** Retrieves a paginated list of all user cases.
*   **Query Parameters:**
    *   `page`: number (optional, default: 1) - Page number for pagination.
    *   `limit`: number (optional, default: 50) - Number of user cases per page.
*   **Responses:**
    *   `200 OK`: Returns a list of user case objects with total count in header.
        ```json
        [
          {
            "uuid": "string",
            "type": "twitter|x|jike|youtube",
            "url": "string",
            "title": "string (optional)",
            "author_name": "string (optional)",
            "author_avatar_url": "string (optional)",
            "details": "object (platform-specific data)",
            "created_at": "string (ISO date)",
            "updated_at": "string (ISO date)",
            "ai_summary": "object (Record<string, string> mapping locale to summary)",
            "content": "object (Record<string, string> mapping locale to content)",
            "related_items": "string[] (array of Item UUIDs)",
            "status": "string",
          },
          // ...
        ]
        ```
    *   `401 Unauthorized`: User is not authenticated or not an admin.
    *   `500 Internal Server Error`: Failed to fetch user cases.

## Create User Case

*   **Method:** `POST`
*   **Path:** `/api/admin/user-cases`
*   **Authentication:** Admin access required
*   **Description:** Creates a new user case.
*   **Request Body:** `application/json`
    ```json
    {
      "type": "string (required) - twitter|x|jike|youtube",
      "url": "string (required) - Original post URL",
      "title": "string (optional)",
      "author_name": "string (optional)",
      "author_avatar_url": "string (optional)",
      "details": "object (optional) - Platform-specific extracted data",
      "ai_summary": "object (optional) - Record<string, string> mapping locale to summary",
      "content": "object (optional) - Record<string, string> mapping locale to content",
      "related_items": "string[] (optional) - Array of related Item UUIDs",
      "locale": "string (optional, default: 'en') - Primary locale for this user case"
    }
    ```
*   **Responses:**
    *   `201 Created`: Returns the created user case object.
        ```json
        {
          "uuid": "string",
          "type": "string",
          "url": "string",
          // ... other user case fields
        }
        ```
    *   `400 Bad Request`: Missing required fields or invalid input.
    *   `401 Unauthorized`: User is not authenticated or not an admin.
    *   `500 Internal Server Error`: Failed to create user case.

## Update User Case

*   **Method:** `PUT`
*   **Path:** `/api/admin/user-cases`
*   **Authentication:** Admin access required
*   **Description:** Updates an existing user case.
*   **Request Body:** `application/json`
    ```json
    {
      "uuid": "string (required) - UUID of the user case to update",
      // Any fields to update, same structure as create endpoint
    }
    ```
*   **Responses:**
    *   `200 OK`: Returns the updated user case object.
        ```json
        {
          "uuid": "string",
          "type": "string",
          "url": "string",
          // ... other user case fields
        }
        ```
    *   `400 Bad Request`: Missing UUID or invalid input.
    *   `401 Unauthorized`: User is not authenticated or not an admin.
    *   `500 Internal Server Error`: Failed to update user case.

## Delete User Case

*   **Method:** `DELETE`
*   **Path:** `/api/admin/user-cases`
*   **Authentication:** Admin access required
*   **Description:** Marks a user case as deleted (soft delete).
*   **Query Parameters:**
    *   `uuid`: string (required) - UUID of the user case to delete.
*   **Responses:**
    *   `200 OK`: Returns success message.
        ```json
        {
          "success": true
        }
        ```
    *   `400 Bad Request`: Missing UUID.
    *   `401 Unauthorized`: User is not authenticated or not an admin.
    *   `500 Internal Server Error`: Failed to delete user case. 