
import { NextRequest, NextResponse } from "next/server";
import { findUserCaseByUuid, deleteUserCase } from "@/models/usercase";
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

interface Params {
  params: Promise<{ uuid: string }>;
}

// GET a single user case by UUID (admin only)
export async function GET(request: NextRequest, { params }: Params) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse();
    }

    const { uuid } = await params;
    if (!uuid) {
      return badRequestResponse("UUID is required");
    }

    // Find user case by UUID
    const userCase = await findUserCaseByUuid(uuid);
    
    if (!userCase) {
      return notFoundResponse(`User case with UUID ${uuid} not found`);
    }

    return NextResponse.json(userCase);
  } catch (error: any) {
    console.error("Error getting user case:", error);
    return serverErrorResponse(error.message || "Failed to get user case");
  }
} 

export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse();
    }

    const { uuid } = await params;
    if (!uuid) {
      return badRequestResponse("UUID is required");
    }

    // Delete user case by UUID
    await deleteUserCase(uuid);

    return NextResponse.json({ success: true, message: "User case deleted successfully" });
  } catch (error: any) {
    console.error("Error deleting user case:", error);
    return serverErrorResponse(error.message || "Failed to delete user case");
  }
}
