
import { NextRequest, NextResponse } from "next/server";
import { insertUserCase, getAllUserCases, findUserCaseByUuid, deleteUserCase, updateUserCase } from "@/models/usercase";
import { UserCase } from "@/types/usercase";
import { v4 as uuidv4 } from "uuid";
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse } from '@/lib/adminAuth';

// GET all user cases or a single user case by UUID (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse();
    }

    // Parse query parameters
    const url = new URL(request.url);
    const uuid = url.searchParams.get("uuid");
    
    // If UUID is provided, get single user case
    if (uuid) {
      const userCase = await findUserCaseByUuid(uuid);
      
      if (!userCase) {
        return NextResponse.json({ error: `User case with UUID ${uuid} not found` }, { status: 404 });
      }
      
      return NextResponse.json(userCase);
    }
    
    // Otherwise, get all user cases with pagination
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "50");

    // Get all user cases
    const { data, count } = await getAllUserCases(page, limit);

    return NextResponse.json(data, {
      headers: {
        "X-Total-Count": count.toString(),
      },
    });
  } catch (error: any) {
    console.error("Error getting user cases:", error);
    return serverErrorResponse(error.message || "Failed to get user cases");
  }
}

// POST create a new user case (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse();
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.url || !body.type) {
      return badRequestResponse("URL and type are required");
    }

    // Ensure ai_summary and content are objects
    let aiSummary = body.ai_summary || {};
    if (typeof aiSummary === 'string') {
      // Convert string to object with default locale
      aiSummary = { en: aiSummary };
    }

    let content = body.content || {};
    if (typeof content === 'string') {
      // Convert string to object with default locale
      content = { en: content };
    }

    // Extract image and video URLs from details or direct fields
    const imageUrls = body.image_urls || 
      (body.details && body.details.imageUrls ? body.details.imageUrls : []);
    
    const videoUrls = body.video_urls || 
      (body.details && body.details.videoUrls ? body.details.videoUrls : []);

    // Prepare user case data with type assertion to bypass TypeScript limitation
    const userCase = {
      uuid: uuidv4(),
      type: body.type,
      url: body.url,
      title: body.title,
      author_name: body.author_name,
      author_avatar_url: body.author_avatar_url,
      details: body.details || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ai_summary: aiSummary,
      content: content,
      related_items: body.related_items || [],
      status: "online",
      locale: body.locale || "en",
      image_urls: imageUrls,
      video_urls: videoUrls
    } as UserCase;

    // Insert user case
    const result = await insertUserCase(userCase);

    return NextResponse.json(result, { status: 201 });
  } catch (error: any) {
    console.error("Error creating user case:", error);
    return serverErrorResponse(error.message || "Failed to create user case");
  }
}

// PUT update a user case (admin only)
export async function PUT(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse();
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.uuid) {
      return badRequestResponse("UUID is required");
    }

    // Prepare user case data
    const userCase: Partial<UserCase> = {
      ...body,
      updated_at: new Date().toISOString(),
    };

    // Update user case
    const result = await updateUserCase(body.uuid, userCase);

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("Error updating user case:", error);
    return serverErrorResponse(error.message || "Failed to update user case");
  }
}

// DELETE a user case (admin only)
export async function DELETE(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await isAdminAuthenticated(request);
    if (!isAdmin) {
      return unauthorizedResponse();
    }

    // Parse query parameters
    const url = new URL(request.url);
    const uuid = url.searchParams.get("uuid");

    if (!uuid) {
      return badRequestResponse("UUID is required");
    }

    // Delete user case
    await deleteUserCase(uuid);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error deleting user case:", error);
    return serverErrorResponse(error.message || "Failed to delete user case");
  }
} 
