
import { NextRequest, NextResponse } from 'next/server';
import { getEmailTemplates, upsertEmailTemplate } from '@/models/emailNotification';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

// GET /api/admin/email-templates - Get all email templates
export async function GET(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const { data, error } = await getEmailTemplates();

    if (error) {
      return serverErrorResponse('Failed to fetch email templates', error);
    }

    return NextResponse.json(data);
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}

// POST /api/admin/email-templates - Create or update an email template
export async function POST(request: NextRequest) {
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  try {
    const body = await request.json();

    // Basic validation for required fields
    if (!body.name || !body.subject || !body.body || !body.event_type) {
      return badRequestResponse('Missing required fields: name, subject, body, event_type');
    }

    // Validate event_type
    const validEventTypes = ['item_submitted', 'item_published', 'item_rejected'];
    if (!validEventTypes.includes(body.event_type)) {
      return badRequestResponse(`Invalid event_type. Must be one of: ${validEventTypes.join(', ')}`);
    }

    const templateData = {
      id: body.id, // Will be undefined for new templates
      name: body.name,
      subject: body.subject,
      body: body.body,
      event_type: body.event_type,
      is_active: body.is_active === undefined ? true : !!body.is_active,
    };

    const { data, error } = await upsertEmailTemplate(templateData);

    if (error) {
      return serverErrorResponse('Failed to save email template', error);
    }

    return NextResponse.json(data, { status: body.id ? 200 : 201 });
  } catch (err: any) {
    if (err instanceof SyntaxError) {
      return badRequestResponse('Invalid JSON format in request body.');
    }
    return serverErrorResponse('An unexpected error occurred', err);
  }
} 