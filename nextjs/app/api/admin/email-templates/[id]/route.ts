
import { NextRequest, NextResponse } from 'next/server';
import { getEmailTemplateById, upsertEmailTemplate } from '@/models/emailNotification';
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse } from '@/lib/adminAuth';

export const dynamic = 'force-dynamic';

interface Params {
  params: Promise<{ id: string }>;
}

// GET /api/admin/email-templates/[id] - Get a specific email template
export async function GET(request: NextRequest, { params }: Params) {
  const { id: idParam } = await params;
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  const id = parseInt(idParam);
  if (isNaN(id)) {
    return badRequestResponse('Invalid template ID');
  }

  try {
    const { data, error } = await getEmailTemplateById(id);

    if (error) {
      return serverErrorResponse('Failed to fetch email template', error);
    }

    if (!data) {
      return notFoundResponse(`Email template with ID ${id} not found`);
    }

    return NextResponse.json(data);
  } catch (err: any) {
    return serverErrorResponse('An unexpected error occurred', err);
  }
}

// PUT /api/admin/email-templates/[id] - Update a specific email template
export async function PUT(request: NextRequest, { params }: Params) {
  const { id: idParam } = await params;
  const isAdmin = await isAdminAuthenticated(request);
  if (!isAdmin) {
    return unauthorizedResponse();
  }

  const id = parseInt(idParam);
  if (isNaN(id)) {
    return badRequestResponse('Invalid template ID');
  }

  try {
    const body = await request.json();

    // Basic validation for required fields
    if (!body.name || !body.subject || !body.body || !body.event_type) {
      return badRequestResponse('Missing required fields: name, subject, body, event_type');
    }

    // Validate event_type
    const validEventTypes = ['item_submitted', 'item_published', 'item_rejected'];
    if (!validEventTypes.includes(body.event_type)) {
      return badRequestResponse(`Invalid event_type. Must be one of: ${validEventTypes.join(', ')}`);
    }

    const templateData = {
      id,
      name: body.name,
      subject: body.subject,
      body: body.body,
      event_type: body.event_type,
      is_active: body.is_active === undefined ? true : !!body.is_active,
    };

    const { data, error } = await upsertEmailTemplate(templateData);

    if (error) {
      return serverErrorResponse('Failed to update email template', error);
    }

    if (!data) {
      return notFoundResponse(`Email template with ID ${id} not found`);
    }

    return NextResponse.json(data);
  } catch (err: any) {
    if (err instanceof SyntaxError) {
      return badRequestResponse('Invalid JSON format in request body.');
    }
    return serverErrorResponse('An unexpected error occurred', err);
  }
} 