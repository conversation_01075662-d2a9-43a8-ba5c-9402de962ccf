import { NextRequest, NextResponse } from "next/server";
import { getUserInfo } from "@/services/user";
import { getAllProjectsForAdmin, deleteProjectsInBatch } from "@/models/links";


export async function GET(request: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check admin permissions
    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const search = searchParams.get("search") || "";
    const user_id = searchParams.get("user_id") || "";

    const filters = {
      search: search || undefined,
      user_id: user_id && user_id !== "all" ? user_id : undefined,
    };

    const { data: projects, error, count } = await getAllProjectsForAdmin(limit, offset, filters);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      projects: projects || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit,
      },
    });
  } catch (error) {
    console.error("Error fetching admin projects:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const userInfo = await getUserInfo();
    if (!userInfo?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check admin permissions
    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { projectIds } = await request.json();
    
    if (!projectIds || !Array.isArray(projectIds) || projectIds.length === 0) {
      return NextResponse.json(
        { error: "Invalid project IDs" },
        { status: 400 }
      );
    }

    const result = await deleteProjectsInBatch(projectIds);

    return NextResponse.json({
      message: `Batch delete completed. ${result.success} successful, ${result.failed} failed.`,
      success: result.success,
      failed: result.failed,
      errors: result.errors
    });
  } catch (error) {
    console.error("Error deleting projects:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 