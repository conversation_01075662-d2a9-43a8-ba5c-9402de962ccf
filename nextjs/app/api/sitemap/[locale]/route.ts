
import { NextResponse } from 'next/server'
import { getAllPublicItems } from '@/models/items'
import { getPublicUserCasesByLocale } from '@/models/usercase'
import { locales } from '@/i18n/locale'
import { Items } from '@/types/items'
import { UserCase } from '@/types/usercase'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ locale: string }> }
) {
  const { locale } = await params;
  if (!locales.includes(locale as any)) {
    return new NextResponse('Not found', { status: 404 });
  }

  // Get base URL from request headers with fallback to environment variable
  const host = request.headers.get('host') || process.env.NEXT_PUBLIC_WEB_URL;
  const protocol = host.includes('localhost') ? 'http' : 'https';
  const baseUrl = `${protocol}://${host}`;
  const localePath = locale === 'en' ? '' : `/${locale}`;

  const urls = [
    {
      loc: `${baseUrl}${localePath}`,
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: '1.0'
    },
    {
      loc: `${baseUrl}${localePath}/posts`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: '0.9'
    },
    {
      loc: `${baseUrl}${localePath}/link-resources`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: '0.9'
    }
  ];

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('')}
</urlset>`;

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600',
    },
  })
} 
