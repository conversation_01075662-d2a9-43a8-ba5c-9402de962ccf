import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { batchCreateDiscoveredLinks, updateDomainStatsViaRPC, extractDomainFromUrl, getProjectById } from "@/models/links";
import { DiscoveredLink } from "@/types/links";
import { normalizeUrl, normalizeSourceUrl, extractTopLevelDomain } from "@/utils/url-normalization";


interface SemrushLinkData {
  page_ascore: number;
  source_title: string;
  source_url: string;
  target_url: string;
  anchor: string;
  external_links: number;
  internal_links: number;
  nofollow: boolean;
  sponsored: boolean;
  ugc: boolean;
  text: boolean;
  frame: boolean;
  form: boolean;
  image: boolean;
  sitewide: boolean;
  first_seen: string;
  last_seen: string;
  new_link: boolean;
  lost_link: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { projectId, links } = body;

    if (!projectId) {
      return NextResponse.json({ error: "Project ID is required" }, { status: 400 });
    }

    if (!Array.isArray(links) || links.length === 0) {
      return NextResponse.json({ error: "Links array is required and cannot be empty" }, { status: 400 });
    }

    // 验证用户是否有权限访问该项目
    const { data: project, error: projectError } = await getProjectById(projectId, user.uuid);
    if (projectError || !project) {
      return NextResponse.json({ error: "Project not found or access denied" }, { status: 404 });
    }

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[],
      domainStatsUpdated: 0,
      domainStatsErrors: [] as string[]
    };

    // 处理链接数据
    const discoveredLinksData: Omit<DiscoveredLink, 'id' | 'created_at' | 'updated_at'>[] = [];
    const domainStatsMap = new Map<string, { dr_score: number; backlink_count: number; traffic: number }>();

    // 处理每个链接
    for (let i = 0; i < links.length; i++) {
      const linkData: SemrushLinkData = links[i];
      
      try {
        // 验证必需字段
        if (!linkData.source_url || !linkData.target_url) {
          results.failed++;
          results.errors.push(`Row ${i + 1}: Missing required URL fields`);
          continue;
        }

        // 根据用户描述，url 对应 Source url，source_url 对应 Target url
        const discoveredUrl = linkData.source_url; // 外部网站URL (发现的外链来源)
        const projectUrl = linkData.target_url;    // 项目网站URL (被链接的目标)
        
        // 验证目标URL是否属于该项目域名 - 只允许顶级域名
        const projectDomain = extractTopLevelDomain(project.domain);
        const targetDomain = extractTopLevelDomain(projectUrl);
        
        if (!projectDomain || !targetDomain) {
          results.failed++;
          results.errors.push(`Row ${i + 1}: Invalid domain format. Only top-level domains are allowed.`);
          continue;
        }
        
        if (projectDomain !== targetDomain) {
          results.failed++;
          results.errors.push(`Row ${i + 1}: Target URL domain (${targetDomain}) doesn't match project domain (${projectDomain})`);
          continue;
        }

        // 准备 discovered_links 数据
        const discoveredLinkToCreate: Omit<DiscoveredLink, 'id' | 'created_at' | 'updated_at'> = {
          url: discoveredUrl, // 外部网站URL
          title: linkData.source_title || '',
          anchor_text: linkData.anchor || '',
          link_type: linkData.nofollow ? 'nofollow' : 'dofollow',
          discovered_at: linkData.first_seen ? new Date(linkData.first_seen).toISOString() : new Date().toISOString(),
          source_url: projectUrl, // 项目网站被链接的具体页面
          is_active: !linkData.lost_link, // 如果是 lost_link 则设为 false
          status: 'SUBMITTED', // Semrush 导入的链接视为已提交
          project_id: projectId,
          user_id: user.uuid
        };

        discoveredLinksData.push(discoveredLinkToCreate);

        // 收集域名统计数据
        const sourceDomain = extractDomainFromUrl(discoveredUrl);
        if (sourceDomain) {
          if (!domainStatsMap.has(sourceDomain)) {
            domainStatsMap.set(sourceDomain, {
              dr_score: linkData.page_ascore || 0,
              backlink_count: 0,
              traffic: linkData.external_links || 0
            });
          }
          // 累计该域名的外链数量
          const stats = domainStatsMap.get(sourceDomain)!;
          stats.backlink_count += 1;
          // 使用最高的 DR 分数
          stats.dr_score = Math.max(stats.dr_score, linkData.page_ascore || 0);
          stats.traffic = Math.max(stats.traffic, linkData.external_links || 0);
        }

      } catch (error) {
        results.failed++;
        results.errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (discoveredLinksData.length === 0) {
      return NextResponse.json({
        message: "No valid links to import",
        newLinks: 0,
        failedLinks: results.failed,
        errors: results.errors
      }, { status: 400 });
    }

    // 批量创建 discovered links
    const { success, failed, errors } = await batchCreateDiscoveredLinks(
      discoveredLinksData,
      projectId,
      user.uuid
    );

    results.successful = success;
    results.failed += failed;
    results.errors.push(...errors);

    // 更新域名统计数据到 all_links 表
    for (const [domain, stats] of domainStatsMap.entries()) {
      try {
        const { error: statsError } = await updateDomainStatsViaRPC(
          domain,
          stats.dr_score,
          stats.traffic,
          false, // 索引状态默认为 false，由 cron worker 更新
          stats.backlink_count
        );
        
        if (statsError) {
          results.domainStatsErrors.push(`Failed to update stats for ${domain}: ${statsError.message}`);
        } else {
          results.domainStatsUpdated++;
        }
      } catch (error) {
        results.domainStatsErrors.push(`Error updating stats for ${domain}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: `Semrush import completed. ${results.successful} links imported, ${results.domainStatsUpdated} domain stats updated.`,
      newLinks: results.successful,
      failedLinks: results.failed,
      domainStatsUpdated: results.domainStatsUpdated,
      errors: results.errors,
      domainStatsErrors: results.domainStatsErrors,
      debug: {
        totalProcessed: links.length,
        validLinksFound: discoveredLinksData.length,
        uniqueDomainsFound: domainStatsMap.size
      }
    }, { status: 200 });

  } catch (error) {
    console.error("Error in Semrush import:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 