import { NextRequest, NextResponse } from "next/server";
import { isAdminAuthenticated, unauthorizedResponse } from "@/lib/adminAuth";
import { getDiscoveredLinkForIndexCheck } from "@/models/links";
import { getCanonicalDomain } from "@/utils/url-normalization";


// POST - Check if project URL is indexed in discovered link's domain (Admin Only)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('POST /api/discovered-links/[id]/check-index - Route accessed');
    
    const { id } = await params;
    console.log('Route parameters:', { id });
    
    // Check admin authentication with proper error handling
    let isAdmin = false;
    try {
      isAdmin = await isAdminAuthenticated(request);
      console.log('Admin authentication result:', isAdmin);
    } catch (authError) {
      console.error('Admin authentication error:', authError);
      return NextResponse.json({
        error: 'Authentication service error',
        details: authError instanceof Error ? authError.message : 'Unknown error'
      }, { status: 500 });
    }
    
    if (!isAdmin) {
      console.log('Admin access denied');
      return unauthorizedResponse("Admin access required for index checking");
    }
    
    // Fetch the discovered link to get project_id and source URL
    console.log('Fetching discovered link with ID:', id);
    const { data: discoveredLink, error: linkError } = await getDiscoveredLinkForIndexCheck(id);
    
    console.log('Discovered link query result:', { 
      discoveredLink: {
        ...discoveredLink,
        source_url_length: discoveredLink?.source_url?.length || 0,
        source_url_type: typeof discoveredLink?.source_url
      }, 
      linkError 
    });
    
    if (linkError || !discoveredLink) {
      console.error('Discovered link not found:', linkError);
      return NextResponse.json({ 
        error: "Discovered link not found",
        details: linkError?.message || 'Link ID not found in database'
      }, { status: 404 });
    }
    
    // Validate source_url before processing
    if (!discoveredLink.source_url || discoveredLink.source_url.trim() === '') {
      console.error('Invalid or empty source_url:', discoveredLink.source_url);
      return NextResponse.json({ 
        error: "Invalid source URL",
        details: 'The discovered link has an empty or invalid source URL'
      }, { status: 400 });
    }

    let sourceDomain: string;
    try {
      // Extract and normalize domain from source_url using canonical domain extraction
      sourceDomain = getCanonicalDomain(discoveredLink.source_url);
      if (!sourceDomain) {
        throw new Error('Failed to extract domain');
      }
    } catch (urlError) {
      console.error('Invalid URL format:', discoveredLink.source_url, urlError);
      return NextResponse.json({ 
        error: "Invalid URL format",
        details: `The source URL "${discoveredLink.source_url}" is not a valid URL format`
      }, { status: 400 });
    }

    const projectDomain = discoveredLink.source_url;
    
    // Synchronously check index status via the cron worker
    const cronWorkerUrl = process.env.BACKEND_WORKER_URL || 'http://localhost:8787';
    
    console.log(`Admin ${request.headers.get('x-forwarded-for') || 'unknown'} requesting domain index check: ${projectDomain} on ${sourceDomain}`);
    
    const response = await fetch(`${cronWorkerUrl}/api/indexing?projectUrl=${encodeURIComponent(projectDomain)}&sourceDomain=${encodeURIComponent(sourceDomain)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.BACKEND_WORKER_API_KEY || ''}`,
      },
      // Set timeout to 30 seconds for synchronous processing
      signal: AbortSignal.timeout(30000)
    });

    if (response.ok) {
      // First try to get response as text to handle non-JSON responses
      const responseText = await response.text();
      let data;
      
      try {
        // Try to parse as JSON first
        data = JSON.parse(responseText);
      } catch (jsonError) {
        // If it's not valid JSON, treat it as a plain text message
        console.log('Worker returned non-JSON response:', responseText);
        data = { message: responseText };
      }
      
      console.log(`Domain index check completed for ${projectDomain} on ${sourceDomain}:`, data);
      
      return NextResponse.json({
        success: true,
        message: '域名收录检查已完成',
        data: {
          linkId: id,
          projectDomain: projectDomain,
          sourceDomain: sourceDomain,
          indexingResult: data,
          isIndexed: data.isIndexed || false,
          indexedPages: data.indexedPages || 0
        }
      });
    } else {
      const error = await response.text();
      console.error(`Domain index check failed for ${projectDomain} on ${sourceDomain}:`, error);
      return NextResponse.json({
        error: '域名收录检查失败',
        details: error
      }, { status: response.status });
    }
  } catch (error) {
    console.error('Error checking index status:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    
    // Handle timeout specifically
    if (error instanceof Error && error.name === 'TimeoutError') {
      return NextResponse.json({
        error: '域名收录检查超时',
        message: '请求处理时间过长，请稍后重试',
        details: error.message
      }, { status: 408 });
    }
    
    return NextResponse.json({
      error: '系统内部错误',
      message: '域名收录检查失败，请联系管理员',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}