import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { batchCreateDiscoveredLinks } from "@/models/links";
import { DiscoveredLink } from "@/types/links";
import { normalizeUrl, normalizeSourceUrl } from "@/utils/url-normalization";


export async function POST(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { projectId, links } = body;

    if (!projectId) {
      return NextResponse.json({ error: "Project ID is required" }, { status: 400 });
    }

    if (!Array.isArray(links) || links.length === 0) {
      return NextResponse.json({ error: "Links array is required and cannot be empty" }, { status: 400 });
    }

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Prepare discovered links data
    const discoveredLinksData: Omit<DiscoveredLink, 'id' | 'created_at' | 'updated_at'>[] = [];

    // Process links in batches to avoid overwhelming the database
    for (let i = 0; i < links.length; i++) {
      const linkData = links[i];
      
      try {
        // Validate and normalize URLs
        if (!linkData.url) {
          results.failed++;
          results.errors.push(`Row ${i + 1}: URL is required`);
          continue;
        }

        if (!linkData.source_url) {
          results.failed++;
          results.errors.push(`Row ${i + 1}: Source URL is required`);
          continue;
        }
        
        try {
          normalizeUrl(linkData.url);
          normalizeSourceUrl(linkData.source_url);
          // URL normalization successful, continue
        } catch (error) {
          results.failed++;
          results.errors.push(`Row ${i + 1}: Invalid URL format - ${linkData.url} or ${linkData.source_url}`);
          continue;
        }
        
        const discoveredLinkToCreate: Omit<DiscoveredLink, 'id' | 'created_at' | 'updated_at'> = {
          url: linkData.url, // Will be normalized in batchCreateDiscoveredLinks function
          title: linkData.title || linkData.source_title || '',
          anchor_text: linkData.anchor_text || linkData.title || '',
          link_type: linkData.is_nofollow === true ? 'nofollow' : 'dofollow',
          discovered_at: linkData.first_seen ? new Date(linkData.first_seen).toISOString() : new Date().toISOString(),
          source_url: normalizeSourceUrl(linkData.source_url), // Normalize source URL to domain-only format
          is_active: true,
          status: 'NEW',
          project_id: projectId,
          user_id: user.uuid
        };

        discoveredLinksData.push(discoveredLinkToCreate);
      } catch (error) {
        results.failed++;
        results.errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (discoveredLinksData.length === 0) {
      return NextResponse.json({
        message: "No valid links to import",
        newLinks: 0,
        failedLinks: results.failed,
        errors: results.errors
      }, { status: 400 });
    }

    // Batch create discovered links
    const { success, failed, errors } = await batchCreateDiscoveredLinks(
      discoveredLinksData,
      projectId,
      user.uuid
    );

    results.successful = success;
    results.failed += failed;
    results.errors.push(...errors);

    return NextResponse.json({
      message: `Batch import completed. ${results.successful} successful, ${results.failed} failed.`,
      newLinks: results.successful,
      failedLinks: results.failed,
      errors: results.errors
    }, { status: 200 });

  } catch (error) {
    console.error("Error in batch discovered links import:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}