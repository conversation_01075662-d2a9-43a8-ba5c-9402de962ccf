import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { createLinkResource, getLinkResourcesByUser } from "@/models/links";
import { Link } from "@/types/links";
import { normalizeUrl } from "@/utils/url-normalization";
import { validateTierAccess, createTierErrorResponse, createTierSuccessResponse } from "@/lib/tier-middleware";


export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    const { data: links, error, count } = await getLinkResourcesByUser(user.uuid, limit, offset);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      links, 
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    });
  } catch (error) {
    console.error("Error fetching links:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Validate tier access for link resource creation
    const tierResult = await validateTierAccess(request, { action: 'add_link_resource' });
    
    if (!tierResult.success) {
      return createTierErrorResponse(tierResult);
    }

    const userUuid = tierResult.userUuid!;
    const body = await request.json();
    
    // Validate and normalize URL
    if (!body.url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }
    
    try {
      const normalizedUrl = normalizeUrl(body.url);
      // URL normalization successful, continue with normalized URL
    } catch (error) {
      return NextResponse.json({ error: "Invalid URL format" }, { status: 400 });
    }
    
    const linkData: Omit<Link, 'id' | 'created_at' | 'updated_at'> = {
      ...body,
      user_id: userUuid,
      traffic: body.traffic || 0,
      is_indexed: body.is_indexed || false,
      status: body.status || 'pending',
      currency: body.currency || 'USD'
    };

    const { data: link, error } = await createLinkResource(linkData);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return await createTierSuccessResponse({ link }, userUuid);
  } catch (error) {
    console.error("Error creating link:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 