import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { 
  updateLinkResource, 
  deleteLinkResource, 
  getLinkResourceById,
  updateDomainStatsInAllLinks 
} from "@/models/links";
import { getCanonicalDomain } from "@/utils/url-normalization";


export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const linkId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(linkId)) {
      return NextResponse.json({ error: "Invalid link ID format" }, { status: 400 });
    }

    const body = await request.json();
    
    // First, verify the link belongs to the current user
    const { data: currentLink, error: linkError } = await getLinkResourceById(linkId, user.uuid);
    
    if (linkError || !currentLink) {
      return NextResponse.json({ error: "Link not found or access denied" }, { status: 404 });
    }
    
    // Separate link_resources data from domain stats data
    const { dr_score, traffic, is_indexed, ...linkResourceData } = body;
    
    // Update link_resources table
    const { data: link, error } = await updateLinkResource(linkId, linkResourceData);
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    // Update all_links table if domain stats are provided
    if (dr_score !== undefined || traffic !== undefined || is_indexed !== undefined) {
      const url = body.url || currentLink.url;
      const domain = getCanonicalDomain(url);
      
      if (domain) {
        const { error: statsError } = await updateDomainStatsInAllLinks(domain, {
          dr_score,
          traffic,
          is_indexed
        });
          
        if (statsError) {
          console.error("Error updating domain stats:", statsError);
          // Don't fail the request if domain stats update fails
        }
      }
    }

    if (!link) {
      return NextResponse.json({ error: "Link not found" }, { status: 404 });
    }

    return NextResponse.json({ link });
  } catch (error) {
    console.error("Error updating link:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const linkId = id;
    
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(linkId)) {
      return NextResponse.json({ error: "Invalid link ID format" }, { status: 400 });
    }

    // First, verify the link belongs to the current user
    const { data: currentLink, error: linkError } = await getLinkResourceById(linkId, user.uuid);
    
    if (linkError || !currentLink) {
      return NextResponse.json({ error: "Link not found or access denied" }, { status: 404 });
    }

    const { error } = await deleteLinkResource(linkId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting link:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 