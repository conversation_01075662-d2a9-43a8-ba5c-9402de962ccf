import { NextRequest, NextResponse } from 'next/server';
import { searchMcps, getMcpByUuid } from '@/models/mcps';
import { verifyApiKeyAndRateLimit } from '@/models/user';
import { getApiKey } from '@/services/apikey';

export const dynamic = 'force-dynamic';

// MCP Protocol Types
interface MCPRequest {
  jsonrpc: string;
  id: string | number;
  method: string;
  params?: any;
}

interface MCPResponse {
  jsonrpc: string;
  id: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

interface MCPToolCall {
  name: string;
  arguments: Record<string, any>;
}

// MCP Tool Definitions
const TOOLS = [
  {
    name: 'search_mcp',
    description: 'Search for MCPs in the MCP Hub database using keywords. Returns a list of matching MCPs with basic information.',
    inputSchema: {
      type: 'object',
      properties: {
        keywords: {
          type: 'string',
          description: 'Keywords to search for in MCP names, descriptions, and metadata'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return (default: 50)',
          default: 50
        }
      },
      required: ['keywords']
    }
  },
  {
    name: 'get_mcp_detail',
    description: 'Get detailed information about a specific MCP using its UUID.',
    inputSchema: {
      type: 'object',
      properties: {
        mcp_id: {
          type: 'string',
          description: 'The UUID of the MCP to retrieve details for'
        }
      },
      required: ['mcp_id']
    }
  }
];

// Authentication helper
async function authenticateApiKey(apiKey: string | null) {
  if (!apiKey) {
    throw new Error('API key is required. Include it in the Authorization header.');
  }

  try {
    const { isValid, message } = await verifyApiKeyAndRateLimit(apiKey);
    if (!isValid && process.env.NODE_ENV !== 'development') {
      throw new Error(`Invalid API key: ${message}`);
    }
    return true;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('API Key service is not available.');
  }
}

// Handle MCP Request
async function handleMCPRequest(request: MCPRequest): Promise<MCPResponse> {
  try {
    switch (request.method) {
      case 'initialize':
        return {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {
                listChanged: true
              }
            },
            serverInfo: {
              name: 'mcp-hub-search',
              version: '1.0.0',
              description: 'MCP Hub search and discovery server'
            }
          }
        };

      case 'tools/list':
        return {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            tools: TOOLS
          }
        };

      case 'tools/call':
        const { name, arguments: args } = request.params as MCPToolCall;
        
        let result;
        switch (name) {
          case 'search_mcp':
            result = await handleSearchMcp(args);
            break;
          case 'get_mcp_detail':
            result = await handleGetMcpDetail(args);
            break;
          default:
            throw new Error(`Unknown tool: ${name}`);
        }

        return {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            content: [
              {
                type: 'text',
                text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
              }
            ]
          }
        };

      default:
        throw new Error(`Unknown method: ${request.method}`);
    }
  } catch (error) {
    return {
      jsonrpc: '2.0',
      id: request.id,
      error: {
        code: -32603,
        message: error instanceof Error ? error.message : 'Internal error',
        data: error
      }
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get API key from request
    const apiKey = getApiKey(request);
    
    // Authenticate API key
    await authenticateApiKey(apiKey);
    
    // Parse MCP request
    const mcpRequest = await request.json() as MCPRequest;
    
    // Handle MCP request
    const response = await handleMCPRequest(mcpRequest);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('MCP request error:', error);
    return NextResponse.json({
      jsonrpc: '2.0',
      id: null,
      error: {
        code: -32603,
        message: error instanceof Error ? error.message : 'Internal error'
      }
    }, { status: 500 });
  }
}

async function handleSearchMcp(params: any) {
  const { keywords, limit = 50 } = params;
  
  if (!keywords || typeof keywords !== 'string') {
    throw new Error('Keywords parameter is required and must be a string');
  }

  try {
    // Perform the search using the existing MCP search function
    const { data, count, error } = await searchMcps(keywords, 'en');
    
    if (error) {
      throw new Error('Failed to search MCPs');
    }
    
    // Simplify the response to only include essential information
    const simplifiedData = data?.slice(0, limit).map((item: any) => ({
      uuid: item.mcp_uuid,
      name: item.name,
      brief: item.brief,
      clicks: item.clicks || 0
    })) || [];
    
    return {
      success: true,
      data: simplifiedData,
      count: count,
      total_results: data?.length || 0,
      keywords: keywords
    };
  } catch (error) {
    console.error('Search MCP error:', error);
    throw error;
  }
}

async function handleGetMcpDetail(params: any) {
  const { mcp_id } = params;
  
  if (!mcp_id || typeof mcp_id !== 'string') {
    throw new Error('mcp_id parameter is required and must be a string');
  }

  try {
    // Get the MCP information
    const { data: mcp, error } = await getMcpByUuid(mcp_id.trim(), 'en');
    
    if (error || !mcp) {
      throw new Error('MCP not found or failed to get MCP information');
    }
    
    // Return detailed MCP information
    return {
      success: true,
      data: {
        id: mcp.id,
        uuid: mcp.uuid,
        name: mcp.name,
        brief: mcp.brief,
        website_url: mcp.website_url || '',
        author_name: mcp.author_name || '',
        created_at: mcp.created_at,
        updated_at: mcp.updated_at,
        is_recommended: mcp.is_recommended,
        is_official: mcp.is_official,
        clicks: mcp.clicks,
        tags: mcp.tags || [],
        metadata: mcp.metadata || {},
        mcp_avatar_url: mcp.mcp_avatar_url || '',
        user_avatar_url: mcp.user_avatar_url || ''
      }
    };
  } catch (error) {
    console.error('Get MCP detail error:', error);
    throw error;
  }
}

// GET endpoint for Server-Sent Events (SSE)
export async function GET(request: NextRequest) {
  try {
    // Get API key from request
    const apiKey = getApiKey(request);
    
    // Authenticate API key
    await authenticateApiKey(apiKey);

    // Create SSE response
    const stream = new ReadableStream({
      start(controller) {
        // Send initial server info
        const serverInfo = {
          jsonrpc: '2.0',
          method: 'notifications/initialized',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {
                listChanged: true
              }
            },
            serverInfo: {
              name: 'mcp-hub-search',
              version: '1.0.0',
              description: 'MCP Hub search and discovery server'
            }
          }
        };
        
        controller.enqueue(`data: ${JSON.stringify(serverInfo)}\n\n`);
        
        // Keep connection alive with periodic pings
        const interval = setInterval(() => {
          try {
            controller.enqueue(`data: ${JSON.stringify({ type: 'ping' })}\n\n`);
          } catch (error) {
            clearInterval(interval);
          }
        }, 30000);

        // Handle cleanup
        return () => {
          clearInterval(interval);
        };
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key'
      }
    });
  } catch (error) {
    console.error('SSE connection error:', error);
    return new Response(error instanceof Error ? error.message : 'Authentication failed', { 
      status: 401 
    });
  }
}

// OPTIONS for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key'
    }
  });
} 