# Item (Model Context Protocol) API

This directory contains the implementation of a Model Context Protocol (Item) server using Streamable HTTP transport.

## What is Item?

Model Context Protocol (Item) is a standardized protocol for AI model interaction, enabling interoperability between different AI tools, servers, and clients. It provides a consistent way for applications to request information and invoke tools from AI models.

## Implementation

Our Item API implementation uses the Streamable HTTP transport method, which provides bi-directional communication between clients and servers. Note that we've implemented a custom version of StreamableHttpServerTransport due to compatibility issues with the SDK.

## Installation Options

### HTTP-based Item Server

The HTTP-based Item server is already deployed and available at:
```
https://item.aiitem.info/api/open/v1/streamable
```

### Stdio-based Item Server

You can also use the stdio-based Item server, which can be installed and run using either `npx` or `uvx`:

```bash
# Using npx
npx @aiitem/tools

# Using uvx
uvx @aiitem/tools
```

Both methods require an API key to authenticate with Item Hub.

### Authentication

The Item API requires authentication with a valid API key. The API key must be included in the `Authorization` header as a Bearer token:

```
Authorization: Bearer YOUR_API_KEY
```

You can obtain an API key from the [API Keys page](https://www.aiitem.info/dashboard/api-keys). The API has a rate limit of 20 requests per hour.

For the stdio-based Item server, set the API key as an environment variable:

```bash
export Item_HUB_API_KEY=YOUR_API_KEY
npx @aiitem/tools
```

### Endpoints

- **Connection Endpoint:** `/api/open/v1/streamable` (HTTP GET)
  - Requires API key in the Authorization header
  - Establishes a streaming connection to the Item server
  - Returns a stream that stays open for the duration of the session

- **Message Endpoint:** `/api/open/v1/streamable?sessionId={sessionId}` (HTTP POST)
  - Requires API key in the Authorization header
  - Used by the client to send JSON-RPC messages to the server
  - Requires a valid `sessionId` from an established connection

### Test Mode

The Item API includes a special test mode that simplifies testing without requiring handling of streaming connections:

- **Test Mode Connection:** `/api/open/v1/streamable?test=true` (HTTP GET)
  - Requires API key in the Authorization header
  - Returns a JSON response with a session ID instead of a stream
  - Example response: `{ "success": true, "sessionId": "abc-123", "message": "Test mode enabled..." }`

- **Test Mode Messages:** `/api/open/v1/streamable?sessionId={sessionId}&test=true` (HTTP POST)
  - Requires API key in the Authorization header
  - Returns structured JSON responses instead of streaming data
  - Useful for testing and debugging Item clients

### Available Tools

The server currently provides the following tools:

1. **search_item_hub**
   - Description: Search for Items on the Item Hub
   - Parameters: `keywords` (string) - Keywords to search for Items

2. **get_item_info**
   - Description: Get detailed information about a specific Item
   - Parameters: `id` (string) - Item identifier

## How to Use

### Using a Item Client

```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHttpClientTransport } from '@modelcontextprotocol/sdk/client/streamable-http.js';

const run = async () => {
  const apiKey = "YOUR_API_KEY"; // Required for authentication
  const client = new Client({ name: 'item-test-client' });
  const transport = new StreamableHttpClientTransport(
    'https://item.aiitem.info/api/open/v1/streamable',
    {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    }
  );
  
  try {
    // Connect to the server
    await client.connect(transport);
    
    // List available tools
    const toolsResult = await client.listTools();
    const tools = toolsResult.tools;
    
    // Call a tool
    const result = await client.callTool({
      name: 'search_item_hub',
      arguments: { keywords: 'example' },
    });
    
    // Process the result
    console.log(result.content);
  } finally {
    // Close the connection
    await client.close();
  }
};

run().catch(console.error);
```

## Integration with Claude and Other Item-compatible Assistants

There are multiple ways to integrate Item Hub with AI assistants and tools that support Item.

### 1. Claude Desktop

To use the Item server with Claude Desktop:

1. Locate your Claude Desktop configuration file:
   - Windows: `%APPDATA%\claude\config.json`
   - macOS: `~/Library/Application Support/claude/config.json` or `~/.config/claude/config.json`
   - Linux: `~/.config/claude/config.json`

2. Add the Item Hub configuration:

```json
{
  "itemServers": {
    "aiitem": {
      "command": "npx",
      "args": ["@aiitem/tools"],
      "environment": {
        "Item_HUB_API_KEY": "YOUR_API_KEY"
      }
    }
  }
}
```

3. Restart Claude Desktop to apply the changes.
4. In your conversation, you can now access Item Hub tools by typing "@aiitem".

### 2. Cline

For Cline and other command-line tools:

1. Create a configuration file named `servers.json` in your project directory:

```json
{
  "servers": [
    {
      "name": "aiitem-tools",
      "command": ["npx", "@aiitem/tools"],
      "environment": {
        "Item_HUB_API_KEY": "YOUR_API_KEY"
      }
    }
  ]
}
```

2. Launch Cline with reference to this configuration:

```bash
cline --item-servers-config ./servers.json
```

### 3. For Tools Supporting Remote Item Servers

Some newer Item clients support direct HTTP connections. Configure them using:

```json
{
  "itemServers": {
    "aiitem-http": {
      "url": "https://item.aiitem.info/api/open/v1/streamable",
      "headers": {
        "Authorization": "Bearer YOUR_API_KEY"
      }
    }
  }
}
```

### 4. For Tools Using File-based Configuration (Cursor, etc.)

1. Create a file named `aiitem-config.json`:

```json
{
  "itemServers": {
    "aiitem": {
      "command": "npx",
      "args": ["@aiitem/tools"],
      "environment": {
        "Item_HUB_API_KEY": "YOUR_API_KEY"
      }
    }
  }
}
```

2. Reference this file in your tool's settings or launch with the appropriate configuration parameter.

### Testing

You can test the Item implementation using the provided TypeScript test script:

```bash
# Run the Next.js development server first
pnpm dev

# Set your API key as an environment variable
export Item_API_KEY=your_api_key_here

# In a separate terminal, run the test script (normal mode)
pnpm item:test

# Test with debug output
pnpm item:test:debug

# Test in simplified test mode (no streaming)
pnpm item:test:simple
```

The test script will connect to your local Item server and demonstrate the available tools.

For testing with the production URL:

```bash
pnpm item:test https://item.aiitem.info/api/open/v1/streamable
```

## Note on Implementation

Our implementation uses a custom StreamableHttpServerTransport class to handle streaming connections between clients and the server. This was necessary due to compatibility issues with the Item SDK's default transport implementation.

The test mode implementation provides a simplified way to test the Item API without dealing with streaming connections, which can be useful for testing and debugging clients.

## Reference

- [Model Context Protocol Specification](https://modelcontextprotocol.io/)
- [Item SDK Documentation](https://github.com/ModelContextProtocol/sdk) 