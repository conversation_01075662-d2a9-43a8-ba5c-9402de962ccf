import { NextRequest, NextResponse } from 'next/server';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  ItemError
} from '@modelcontextprotocol/sdk/types.js';
import { v4 as uuidv4 } from 'uuid';
// Import Item functions from models/items
import {
  getItemByUuid,
  searchItems
} from '@/models/items';
import { verifyApiKeyAndRateLimit } from '@/models/user';
import { getApiKey } from '@/services/apikey';

export const dynamic = 'force-dynamic';

/**
 * Custom implementation of StreamableHttpServerTransport
 * This is used since the import from SDK is not working properly
 */
class StreamableHttpServerTransport {
  sessionId: string;
  readable: ReadableStream;
  private readableController: ReadableStreamDefaultController | null = null;

  onclose: (() => void) | null = null;
  onerror: ((error: Error) => void) | null = null;

  constructor() {
    this.sessionId = uuidv4();
    this.readable = new ReadableStream({
      start: (controller) => {
        this.readableController = controller;
      },
      cancel: (reason) => {
        console.log(`Stream cancelled: ${reason}`);
        this.close();
      }
    });
  }

  // Implement the required Transport interface methods
  async start(): Promise<void> {
    // Nothing to do on start for HTTP transport
  }

  async stop(): Promise<void> {
    this.close();
  }

  async send(data: any): Promise<void> {
    if (!this.readableController) {
      console.error('Readable controller not initialized');
      return;
    }
    
    try {
      // Encode the message and send it
      const encoder = new TextEncoder();
      const message = JSON.stringify(data);
      const encodedMessage = encoder.encode(message);
      
      this.readableController.enqueue(encodedMessage);
    } catch (error) {
      console.error('Error sending data:', error);
      if (this.onerror) {
        this.onerror(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  async handlePostMessage(message: any, writable: WritableStream): Promise<void> {
    const writer = writable.getWriter();

    try {
      // Process the message on the server
      // In a real implementation, this would be handled by the Item protocol
      
      // Write the response to the writable stream
      const encoder = new TextEncoder();
      const response = JSON.stringify({ result: "success", data: message });
      const encodedResponse = encoder.encode(response);
      
      await writer.write(encodedResponse);
    } catch (error) {
      console.error('Error processing message:', error);
      const encoder = new TextEncoder();
      const errorResponse = JSON.stringify({ error: "Failed to process message" });
      const encodedError = encoder.encode(errorResponse);
      
      await writer.write(encodedError);
    } finally {
      await writer.close();
    }
  }

  async close(): Promise<void> {
    try {
      if (this.readableController) {
        this.readableController.close();
        this.readableController = null;
      }
      
      if (this.onclose) {
        this.onclose();
      }
    } catch (error) {
      console.error('Error closing transport:', error);
    }
  }
}

// Map to store session transports by ID
const sessionTransports: Record<string, StreamableHttpServerTransport> = {};

// Create Item server instance
const server = new Server(
  {
    name: "mybacklinks-server",
    version: "1.0.0"
  },
  {
    capabilities: {
      tools: {},
    }
  }
);

export async function GET(request: NextRequest) {
  console.log("Item connection request received");
  
  // Verify API key
  const api_key = getApiKey(request);
  const { isValid, message } = await verifyApiKeyAndRateLimit(api_key);
  
  if (!isValid) {
    return NextResponse.json({ 
      error: 'Unauthorized. ' + message 
    }, { 
      status: 401,
      headers: {
        'WWW-Authenticate': 'Bearer'
      }
    });
  }
  
  // Create a new session ID or use the one provided in query parameters
  const url = new URL(request.url);
  let sessionId = url.searchParams.get('sessionId');
  
  // For simple testing, if 'test' mode is enabled, return JSON with session info
  const testMode = url.searchParams.get('test') === 'true';
  if (testMode) {
    // If in test mode, don't create a real streaming connection
    const testSessionId = sessionId || uuidv4();
    console.log(`Test mode: returning session ID ${testSessionId}`);
    
    // Create a mock transport for test mode
    const mockTransport = new StreamableHttpServerTransport();
    mockTransport.sessionId = testSessionId;
    
    // Store in session store so subsequent calls can use it
    sessionTransports[testSessionId] = mockTransport;
    
    // Connect it to the server
    try {
      await server.connect(mockTransport);
    } catch (error) {
      console.error("Error establishing test mode Item connection:", error);
    }
    
    return NextResponse.json({
      success: true,
      sessionId: testSessionId,
      message: "Test mode enabled. Use this sessionId for subsequent calls."
    });
  }
  
  try {
    // Normal streaming mode
    // Create and configure response for streaming
    const responseHeaders = new Headers();
    responseHeaders.set('Content-Type', 'application/json');
    responseHeaders.set('Cache-Control', 'no-cache');
    responseHeaders.set('Connection', 'keep-alive');
    
    // Create streaming transport
    const transport = new StreamableHttpServerTransport();
    sessionId = sessionId || transport.sessionId;
    
    // Add session ID to headers
    responseHeaders.set('X-Session-ID', sessionId);
    
    // Store transport reference
    sessionTransports[sessionId] = transport;
    
    // Log session creation
    console.log(`New Item transport created with session ID: ${sessionId}`);
    
    // Connect to the server
    await server.connect(transport);
    
    // Set up cleanup when client disconnects
    request.signal.addEventListener('abort', () => {
      console.log(`Client disconnected, cleaning up session: ${sessionId}`);
      if (sessionTransports[sessionId]) {
        sessionTransports[sessionId].close();
        delete sessionTransports[sessionId];
      }
    });
    
    // Return the JSON response with sessionId for immediate client connection
    return NextResponse.json({
      success: true,
      sessionId: sessionId,
      message: "Connection established. Use this sessionId for subsequent calls."
    }, {
      headers: responseHeaders
    });
  } catch (error) {
    console.error("Error establishing Item connection:", error);
    if (sessionId && sessionTransports[sessionId]) {
      sessionTransports[sessionId].close();
      delete sessionTransports[sessionId];
    }
    return NextResponse.json({ 
      error: "Failed to establish Item connection",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const sessionId = request.nextUrl.searchParams.get('sessionId');
  
  if (!sessionId) {
    return NextResponse.json({ error: "Missing sessionId parameter" }, { status: 400 });
  }
  
  // Verify API key for POST requests as well
  const api_key = getApiKey(request);
  const { isValid, message } = await verifyApiKeyAndRateLimit(api_key);
  
  if (!isValid) {
    return NextResponse.json({ 
      error: 'Unauthorized. ' + message 
    }, { 
      status: 401,
      headers: {
        'WWW-Authenticate': 'Bearer'
      }
    });
  }
  
  const transport = sessionTransports[sessionId];
  
  if (!transport) {
    return NextResponse.json({ error: "Invalid or expired session" }, { status: 404 });
  }
  
  try {
    const body = await request.json();
    
    // 测试模式特殊处理
    const isTestMode = request.nextUrl.searchParams.get('test') === 'true';
    if (isTestMode) {
      // 为测试模式提供模拟响应
      if (body.method === 'listTools') {
        return NextResponse.json({
          jsonrpc: '2.0',
          result: {
            tools: [
              {
                name: 'search_items',
                description: 'Search for Items on the Item Hub',
                inputSchema: {
                  type: 'object',
                  properties: {
                    keywords: {
                      type: 'string',
                      description: 'Keywords to search for Items',
                    },
                  },
                  required: ['keywords'],
                },
              },
              {
                name: 'get_item_info',
                description: 'Get detailed information about a specific Item',
                inputSchema: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      description: 'Item identifier',
                    },
                  },
                  required: ['id'],
                },
              },
            ],
          },
          id: body.id,
        });
      } else if (body.method === 'callTool') {
        const toolName = body.params?.name;
        const args = body.params?.arguments;
        
        if (toolName === 'search_items') {
          return NextResponse.json({
            jsonrpc: '2.0',
            result: {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    results: [
                      { id: "item1", name: "Example Item 1", description: "Sample Item for demonstration" },
                      { id: "item2", name: "Example Item 2", description: "Another sample Item" }
                    ]
                  }, null, 2),
                },
              ],
            },
            id: body.id,
          });
        } else if (toolName === 'get_item_info') {
          return NextResponse.json({
            jsonrpc: '2.0',
            result: {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    id: args?.id || 'unknown',
                    name: `Example Item ${args?.id || 'unknown'}`,
                    description: "Detailed information about this Item",
                    version: "1.0.0",
                    author: "Item Hub Team",
                    website: "https://mybacklinks.app"
                  }, null, 2),
                },
              ],
            },
            id: body.id,
          });
        } else {
          return NextResponse.json({
            jsonrpc: '2.0',
            error: {
              code: -32601,
              message: `Unknown tool: ${toolName}`
            },
            id: body.id,
          }, { status: 400 });
        }
      } else {
        return NextResponse.json({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: `Method not found: ${body.method}`
          },
          id: body.id,
        }, { status: 400 });
      }
    }
    
    // 正常非测试模式处理
    try {
      // Process the request directly
      if (body.method === 'listTools') {
        // Return the tools configured in setupToolHandlers
        return NextResponse.json({
          jsonrpc: '2.0',
          result: {
            tools: [
              {
                name: 'search_items',
                description: 'Search for Items on the Item Hub',
                inputSchema: {
                  type: 'object',
                  properties: {
                    keywords: {
                      type: 'string',
                      description: 'Keywords to search for Items',
                    },
                  },
                  required: ['keywords'],
                },
              },
              {
                name: 'get_item_info',
                description: 'Get detailed information about a specific Item',
                inputSchema: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      description: 'Item identifier',
                    },
                  },
                  required: ['id'],
                },
              },
            ],
          },
          id: body.id,
        });
      } else if (body.method === 'callTool') {
        const toolName = body.params?.name;
        const args = body.params?.arguments;
        
        if (!toolName) {
          return NextResponse.json({
            jsonrpc: '2.0',
            error: {
              code: -32602,
              message: 'Missing tool name'
            },
            id: body.id,
          }, { status: 400 });
        }
        
        // Process the tool call based on the tool name, using real data
        if (toolName === 'search_items') {
          // Validate arguments
          if (typeof args !== 'object' || args === null || typeof args.keywords !== 'string') {
            return NextResponse.json({
              jsonrpc: '2.0',
              error: {
                code: -32602,
                message: 'Invalid search arguments. Requires "keywords" (string).'
              },
              id: body.id,
            }, { status: 400 });
          }
          
          // Search Items with real data
          try {
            const { data, error, count } = await searchItems(args.keywords, "en", 1, 10);
            
            if (error) {
              throw new Error(`Error searching Items: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
            }
            
            return NextResponse.json({
              jsonrpc: '2.0',
              result: {
                content: [
                  {
                    type: 'text',
                    text: JSON.stringify({
                      results: data || [],
                      count: count || 0,
                      query: args.keywords
                    }, null, 2),
                  },
                ],
              },
              id: body.id,
            });
          } catch (error) {
            return NextResponse.json({
              jsonrpc: '2.0',
              error: {
                code: -32000,
                message: `Error searching Items: ${error instanceof Error ? error.message : String(error)}`
              },
              id: body.id,
            }, { status: 500 });
          }
        } else if (toolName === 'get_item_info') {
          // Validate arguments
          if (typeof args !== 'object' || args === null || typeof args.id !== 'string') {
            return NextResponse.json({
              jsonrpc: '2.0',
              error: {
                code: -32602,
                message: 'Invalid arguments. Requires "id" (string).'
              },
              id: body.id,
            }, { status: 400 });
          }
          
          // Get Item data with real API
          try {
            const { data, error } = await getItemByUuid(args.id, "en");
            
            if (error) {
              throw new Error(`Error fetching Item: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
            }
            
            if (!data) {
              return NextResponse.json({
                jsonrpc: '2.0',
                error: {
                  code: -32000,
                  message: `Item with id "${args.id}" not found`
                },
                id: body.id,
              }, { status: 404 });
            }
            
            return NextResponse.json({
              jsonrpc: '2.0',
              result: {
                content: [
                  {
                    type: 'text',
                    text: JSON.stringify(data, null, 2),
                  },
                ],
              },
              id: body.id,
            });
          } catch (error) {
            return NextResponse.json({
              jsonrpc: '2.0',
              error: {
                code: -32000,
                message: `Error retrieving Item info: ${error instanceof Error ? error.message : String(error)}`
              },
              id: body.id,
            }, { status: 500 });
          }
        } else {
          return NextResponse.json({
            jsonrpc: '2.0',
            error: {
              code: -32601,
              message: `Unknown tool: ${toolName}`
            },
            id: body.id,
          }, { status: 400 });
        }
      } else {
        return NextResponse.json({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: `Method not found: ${body.method}`
          },
          id: body.id,
        }, { status: 400 });
      }
    } catch (error) {
      console.error("Error processing Item request:", error);
      return NextResponse.json({
        jsonrpc: '2.0',
        error: {
          code: -32000,
          message: error instanceof Error ? error.message : String(error)
        },
        id: body.id || 'unknown',
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error processing Item message:", error);
    return NextResponse.json({ 
      jsonrpc: '2.0',
      error: {
        code: -32700,
        message: "Invalid JSON received"
      },
      id: null
    }, { status: 400 });
  }
}
