import { NextRequest, NextResponse } from 'next/server';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  ItemError
} from '@modelcontextprotocol/sdk/types.js';
import { v4 as uuidv4 } from 'uuid';
// Import Item functions from models/items
import {
  getItemByUuid,
  searchItems
} from '@/models/items';
import { verifyApiKeyAndRateLimit } from '@/models/user';
import { getApiKey } from '@/services/apikey';
import { toolsList } from './toolsList';

export const dynamic = 'force-dynamic';

/**
 * Custom implementation of SSEServerTransport
 * This enables Server-Sent Events (SSE) communication for Item API
 */
class SSEServerTransport {
  sessionId: string;
  private encoder = new TextEncoder();
  private controller: ReadableStreamDefaultController | null = null;
  private stream: ReadableStream | null = null;

  onclose: (() => void) | null = null;
  onerror: ((error: Error) => void) | null = null;

  constructor() {
    this.sessionId = uuidv4();
    this.createStream();
  }

  private createStream() {
    this.stream = new ReadableStream({
      start: (controller) => {
        this.controller = controller;
      },
      cancel: (reason) => {
        console.log(`SSE stream cancelled: ${reason}`);
        this.close();
      }
    });
  }

  // Get the SSE stream
  getStream(): ReadableStream | null {
    return this.stream;
  }

  // Implement the required Transport interface methods
  async start(): Promise<void> {
    // Nothing to do on start for SSE transport
  }

  async stop(): Promise<void> {
    this.close();
  }

  async send(data: any): Promise<void> {
    if (!this.controller) {
      console.error('SSE stream controller not initialized');
      return;
    }
    
    try {
      // Format message as SSE
      const jsonMessage = JSON.stringify(data);
      // In SSE, each message needs to be prefixed with "data: " and end with two newlines
      const sseMessage = `data: ${jsonMessage}\n\n`;
      const encodedMessage = this.encoder.encode(sseMessage);
      
      this.controller.enqueue(encodedMessage);
    } catch (error) {
      console.error('Error sending SSE data:', error);
      if (this.onerror) {
        this.onerror(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  async close(): Promise<void> {
    try {
      if (this.controller) {
        this.controller.close();
        this.controller = null;
      }
      
      if (this.onclose) {
        this.onclose();
      }
    } catch (error) {
      console.error('Error closing SSE transport:', error);
    }
  }
}

// Map to store session transports by ID
const sessionTransports: Record<string, SSEServerTransport> = {};

// Create Item server instance
const server = new Server(
  {
    name: "mybacklinks-sse-server",
    version: "1.0.0"
  },
  {
    capabilities: {
      tools: {},
    }
  }
);

/**
 * Get API key from request (either from Authorization header or api_key query parameter)
 * @param request NextRequest object
 * @returns API key string or undefined
 */
function getApiKeyFromRequest(request: NextRequest): string | undefined {
  // First check header
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Then check URL parameter
  const url = new URL(request.url);
  const apiKeyParam = url.searchParams.get('api_key');
  if (apiKeyParam) {
    return apiKeyParam;
  }
  
  return undefined;
}

export async function GET(request: NextRequest) {
  console.log("Item SSE connection request received");
  
  // Get test mode flag early
  const url = new URL(request.url);
  
  // Verify API key - using our enhanced function that checks both header and URL param
  const api_key = getApiKeyFromRequest(request);
  
  // In test mode, bypass rate limiting
  let isValid = false;
  let message = '';
  
  // Normal mode - full API key and rate limit verification
  const result = await verifyApiKeyAndRateLimit(api_key);
  isValid = result.isValid;
  message = result.message;
  
  if (!isValid) {
    console.log(`Authentication failed: ${message}`);
    return NextResponse.json({ 
      error: 'Unauthorized. ' + message 
    }, { 
      status: 401,
      headers: {
        'WWW-Authenticate': 'Bearer'
      }
    });
  }
  
  // Get session ID or create new one
  let sessionId = url.searchParams.get('sessionId');
  const action = url.searchParams.get('action') || 'connect';
  
  // Log request details for debugging
  console.log(`SSE request: action=${action}, api_key present=${!!api_key}, sessionId=${sessionId || 'new'}`);
  
  // If action is 'connect', establish a new SSE connection
  if (action === 'connect') {
    try {
      // Create SSE transport
      const transport = new SSEServerTransport();
      sessionId = sessionId || transport.sessionId;
      
      // Store transport reference
      sessionTransports[sessionId] = transport;
      
      // Log session creation
      console.log(`New Item SSE transport created with session ID: ${sessionId}`);
      
      // Connect to the server
      await server.connect(transport);
      
      // Set up cleanup when client disconnects
      request.signal.addEventListener('abort', () => {
        console.log(`Client disconnected, cleaning up SSE session: ${sessionId}`);
        if (sessionTransports[sessionId]) {
          sessionTransports[sessionId].close();
          delete sessionTransports[sessionId];
        }
      });
      
      // Prepare SSE response headers
      const responseHeaders = new Headers();
      responseHeaders.set('Content-Type', 'text/event-stream');
      responseHeaders.set('Cache-Control', 'no-cache');
      responseHeaders.set('Connection', 'keep-alive');
      responseHeaders.set('X-Session-ID', sessionId);
      
      // Send initial connection event
      const stream = transport.getStream();
      if (!stream) {
        throw new Error("Failed to create SSE stream");
      }
      
      console.log(`SSE connection established successfully: sessionId=${sessionId}`);
      
      // Send an initial welcome message with the session ID
      await transport.send({
        type: 'connection_established',
        sessionId: sessionId,
        message: 'SSE connection established'
      });
      
      // Return SSE stream
      return new Response(stream, {
        headers: responseHeaders
      });
    } catch (error) {
      console.error("Error establishing Item SSE connection:", error);
      if (sessionId && sessionTransports[sessionId]) {
        sessionTransports[sessionId].close();
        delete sessionTransports[sessionId];
      }
      return NextResponse.json({ 
        error: "Failed to establish Item SSE connection",
        message: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  } 
  // If action is 'info', return session info without establishing connection
  else if (action === 'info') {
    if (!sessionId) {
      return NextResponse.json({ error: "Missing sessionId parameter for info action" }, { status: 400 });
    }
    
    const transport = sessionTransports[sessionId];
    if (!transport) {
      return NextResponse.json({ error: "Invalid or expired session" }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      sessionId: sessionId,
      active: true,
      message: "SSE connection is active"
    });
  }
  else {
    return NextResponse.json({ error: "Invalid action parameter. Use 'connect' or 'info'." }, { status: 400 });
  }
}

export async function POST(request: NextRequest) {
  const sessionId = request.nextUrl.searchParams.get('sessionId');
  
  if (!sessionId) {
    return NextResponse.json({ error: "Missing sessionId parameter" }, { status: 400 });
  }
  
  // Verify API key for POST requests as well, using our enhanced function
  const api_key = getApiKeyFromRequest(request);
  
  // In test mode, bypass rate limiting
  let isValid = false;
  let message = '';
  
  const result = await verifyApiKeyAndRateLimit(api_key);
  isValid = result.isValid;
  message = result.message;
  
  if (!isValid) {
    console.log(`Authentication failed for POST: ${message}`);
    return NextResponse.json({ 
      error: 'Unauthorized. ' + message 
    }, { 
      status: 401,
      headers: {
        'WWW-Authenticate': 'Bearer'
      }
    });
  }
  
  const transport = sessionTransports[sessionId];
  
  if (!transport) {
    console.log(`Session lookup failed: ${sessionId} not found in session store. Available sessions: ${Object.keys(sessionTransports).join(', ') || 'none'}`);
    return NextResponse.json({ error: "Invalid or expired session" }, { status: 404 });
  }
  
  try {
    const body = await request.json();
    
    // Process the request directly through transport
    if (body.method === 'listTools') {
      // Send response via SSE stream
      await transport.send({
        jsonrpc: '2.0',
        result: {
          tools: toolsList
        },
        id: body.id,
      });
    }
    else if (body.method === 'callTool') {
      const toolName = body.params?.name;
      const args = body.params?.arguments;
      
      if (!toolName) {
        await transport.send({
          jsonrpc: '2.0',
          error: {
            code: -32602,
            message: 'Missing tool name'
          },
          id: body.id,
        });
        return NextResponse.json({
          success: false,
          message: "Missing tool name"
        });
      }
        
      // Process the tool call based on the tool name, using real data
      if (toolName === 'search_items') {
        // Validate arguments
        if (typeof args !== 'object' || args === null || typeof args.keywords !== 'string') {
          await transport.send({
            jsonrpc: '2.0',
            error: {
              code: -32602,
              message: 'Invalid search arguments. Requires "keywords" (string).'
            },
            id: body.id,
          });
          return NextResponse.json({
            success: false,
            message: "Invalid search arguments"
          });
        }
          
        // Search Items with real data
        try {
          const { data, error, count } = await searchItems(args.keywords, "en", 1, 10);
          
          if (error) {
            throw new Error(`Error searching Items: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
          }
            
          await transport.send({
            jsonrpc: '2.0',
            result: {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    results: data || [],
                    count: count || 0,
                    query: args.keywords
                  }, null, 2),
                },
              ],
            },
            id: body.id,
          });
        } catch (error) {
          await transport.send({
            jsonrpc: '2.0',
            error: {
              code: -32000,
              message: `Error searching Items: ${error instanceof Error ? error.message : String(error)}`
            },
            id: body.id,
          });
          return NextResponse.json({
            success: false,
            message: `Error searching Items: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      } else if (toolName === 'get_item_info') {
        // Validate arguments
        if (typeof args !== 'object' || args === null || typeof args.id !== 'string') {
          await transport.send({
            jsonrpc: '2.0',
            error: {
              code: -32602,
              message: 'Invalid arguments. Requires "id" (string).'
            },
            id: body.id,
          });
          return NextResponse.json({
            success: false,
            message: "Invalid arguments"
          });
        }
          
        // Get Item data with real API
        try {
          const { data, error } = await getItemByUuid(args.id, "en");
          
          if (error) {
            throw new Error(`Error fetching Item: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
          }
          
          if (!data) {
            await transport.send({
              jsonrpc: '2.0',
              error: {
                code: -32000,
                message: `Item with id "${args.id}" not found`
              },
              id: body.id,
            });
            return NextResponse.json({
              success: false,
              message: `Item with id "${args.id}" not found`
            });
          }
          
          await transport.send({
            jsonrpc: '2.0',
            result: {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(data, null, 2),
                },
              ],
            },
            id: body.id,
          });
        } catch (error) {
          await transport.send({
            jsonrpc: '2.0',
            error: {
              code: -32000,
              message: `Error retrieving Item info: ${error instanceof Error ? error.message : String(error)}`
            },
            id: body.id,
          });
          return NextResponse.json({
            success: false,
            message: `Error retrieving Item info: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      } else {
        await transport.send({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: `Unknown tool: ${toolName}`
          },
          id: body.id,
        });
        return NextResponse.json({
          success: false,
          message: `Unknown tool: ${toolName}`
        });
      }
    } else {
      await transport.send({
        jsonrpc: '2.0',
        error: {
          code: -32601,
          message: `Method not found: ${body.method}`
        },
        id: body.id,
      });
      return NextResponse.json({
        success: false,
        message: `Method not found: ${body.method}`
      });
    }
      
    // Return acknowledgment response
    return NextResponse.json({
      success: true,
      message: "Message processed successfully"
    });
  } catch (error) {
    console.error("Error processing Item request:", error);
    await transport.send({
      jsonrpc: '2.0',
      error: {
        code: -32000,
        message: error instanceof Error ? error.message : String(error)
      },
      id: 'unknown',
    });
    return NextResponse.json({
      success: false,
      message: `Error processing request: ${error instanceof Error ? error.message : String(error)}`
    });
  }
}