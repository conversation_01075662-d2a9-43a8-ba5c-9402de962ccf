# Item (Model Context Protocol) API with SSE Transport

This directory contains the implementation of a Model Context Protocol (Item) server using Server-Sent Events (SSE) transport.

## What is Item with SSE?

Model Context Protocol (Item) is a standardized protocol for AI model interaction, enabling interoperability between different AI tools, servers, and clients. This implementation uses Server-Sent Events (SSE) as the transport mechanism, which provides real-time, one-way communication from server to client.

SSE (Server-Sent Events) is a standard for establishing a persistent connection between a client and server, allowing the server to push updates to the client in real time. Unlike WebSockets, SSE offers a simpler, one-way communication pattern that's ideal for scenarios where servers need to stream updates to clients.

## Implementation

Our Item SSE API implementation uses the standard SSE protocol to push Item responses back to the client. Unlike the StreamableHttp transport, the SSE approach:

1. Maintains a single persistent connection for receiving server messages
2. Uses a separate HTTP POST endpoint for sending client requests
3. Formats messages according to the SSE standard (prefixed with `data:` and terminated with double newlines)
4. Provides native browser compatibility in many clients

## Endpoint Structure

### SSE Connection Endpoint

- **URL:** `/api/open/v1/sse`
- **Method:** `GET`
- **Query Parameters:**
  - `action` - Operation to perform (default: "connect")
    - `connect` - Establish a new SSE connection
    - `info` - Get information about an existing session
  - `sessionId` - (Optional) Session identifier for reconnection
  - `test` - (Optional) Enable test mode (`true`/`false`)
- **Authentication:** Bearer token in Authorization header

### Message Endpoint

- **URL:** `/api/open/v1/sse`
- **Method:** `POST`
- **Query Parameters:**
  - `sessionId` - Session identifier from an established connection
  - `test` - (Optional) Enable test mode (`true`/`false`)
- **Authentication:** Bearer token in Authorization header
- **Body:** JSON-RPC 2.0 formatted request

## Usage

### 1. Establish SSE Connection

First, establish a Server-Sent Events connection:

```javascript
const apiKey = "YOUR_API_KEY";
const eventSource = new EventSource("/api/open/v1/sse?action=connect", {
  headers: {
    "Authorization": `Bearer ${apiKey}`
  }
});

// Listen for SSE messages
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log("Received SSE message:", data);
};

// Handle connection error
eventSource.onerror = (error) => {
  console.error("SSE connection error:", error);
  eventSource.close();
};

// Extract session ID from headers or initial message
const sessionId = /* get from X-Session-ID header or initial message */;
```

### 2. Send Requests

After establishing the SSE connection, send requests to the server using a separate HTTP POST endpoint:

```javascript
// Example: List available tools
async function listTools() {
  const response = await fetch(`/api/open/v1/sse?sessionId=${sessionId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      jsonrpc: "2.0",
      method: "listTools",
      params: {},
      id: "list-tools-request"
    })
  });
  
  // This response is just an acknowledgment, the actual tools list
  // will be sent via the SSE connection
  return await response.json();
}

// Example: Call a tool
async function searchItems(keywords) {
  const response = await fetch(`/api/open/v1/sse?sessionId=${sessionId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      jsonrpc: "2.0",
      method: "callTool",
      params: {
        name: "search_items",
        arguments: { keywords }
      },
      id: "search-request"
    })
  });
  
  // This response is just an acknowledgment, the actual search results
  // will be sent via the SSE connection
  return await response.json();
}
```

## Available Tools

The SSE endpoint provides the same tools as the StreamableHttp implementation:

1. **search_items**
   - Description: Search for Items on the Item Hub
   - Parameters: `keywords` (string) - Keywords to search for Items

2. **get_mcp_info**
   - Description: Get detailed information about a specific Item
   - Parameters: `id` (string) - Item identifier

## Authentication

The SSE API requires authentication with a valid API key. The API key must be included in the `Authorization` header as a Bearer token:

```
Authorization: Bearer YOUR_API_KEY
```

You can obtain an API key from the [API Keys page](https://mybacklinks.app/dashboard/api-keys). The API has a rate limit of 20 requests per hour.

## Test Mode

The SSE API includes a test mode for simplified testing without dealing with SSE connections:

- **Test Mode Connection:** `/api/open/v1/sse?test=true`
  - Returns a JSON response with a session ID instead of an SSE stream
  - Example response: `{ "success": true, "sessionId": "abc-123", "message": "Test mode enabled..." }`

- **Test Mode Messages:** `/api/open/v1/sse?sessionId={sessionId}&test=true` (POST)
  - Processes the request and returns a success acknowledgment
  - The actual response data will still be sent via the SSE connection

## Differences from StreamableHttp

The key differences between the SSE implementation and the StreamableHttp implementation are:

1. **Connection Model:** SSE uses a single persistent HTTP connection for server-to-client messages, with separate HTTP requests for client-to-server messages. StreamableHttp uses a bidirectional streaming connection.

2. **Message Format:** SSE uses the standard SSE message format (prefixed with `data:` and terminated with double newlines). StreamableHttp uses raw JSON.

3. **Browser Support:** SSE has native support in most browsers via the `EventSource` API. StreamableHttp typically requires a custom client implementation.

4. **Error Handling:** SSE includes built-in reconnection handling in many clients. StreamableHttp requires custom reconnection logic.

## Integration with Item-compatible Assistants

When using the SSE Item endpoint with AI assistants like Claude, you'll need an adapter that bridges the SSE protocol to the assistant's expected Item interface. For detailed integration instructions, refer to the documentation of your specific Item client.

## Reference

- [Model Context Protocol Specification](https://modelcontextprotocol.io/)
- [MDN Server-Sent Events Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events) 