import { NextRequest, NextResponse } from 'next/server';
import { searchItems } from '@/models/items';
import { verifyApiKeyAndRateLimit } from '@/models/user';
import { getApiKey } from '@/services/apikey';

export const dynamic = 'force-dynamic';

// Rate limit constants
const RATE_LIMIT_REQUESTS = 20; // Maximum requests per hour

export async function GET(request: NextRequest) {
  try {
    // Get API key from request
    const apiKey = getApiKey(request);
    
    // Verify API key and check rate limits using the function from user model
    const { isValid, message } = await verifyApiKeyAndRateLimit(apiKey);
    if (!isValid) {
      return NextResponse.json({ 
        error: 'Unauthorized. ' + message 
      }, { status: 401 });
    }

    // Extract search parameters
    const { searchParams } = new URL(request.url);
    const keywords = searchParams.get('keywords');
    
    if (!keywords) {
      return NextResponse.json({ 
        error: 'Search keywords is required' 
      }, { status: 400 });
    }

    // Perform the search using the existing Item search function
    // Always use 'en' as the language
    const { data, count, error } = await searchItems(keywords, 'en');
    
    if (error) {
      return NextResponse.json({ 
        error: 'Failed to search Items' 
      }, { status: 500 });
    }
    
    // Simplify the response to only include 'en' brief and website_url
    const simplifiedData = data?.map((item: any) => ({
      name: item.name,
      brief: item.brief,
      website_url: item.website_url || ''
    })) || [];
    
    // Return the simplified search results
    return NextResponse.json({ 
      data: simplifiedData,
      count
    });
  } catch (err: any) {
    console.error('Error in open search API:', err);
    return NextResponse.json({ 
      error: 'An unexpected error occurred',
      message: err.message 
    }, { status: 500 });
  }
} 