import { NextRequest, NextResponse } from "next/server";
import { requireAuth, unauthorizedResponse } from "@/lib/auth";


// Import the integrations data from the main route
// In production, this would be handled by a database
let integrations = [
  {
    id: 1,
    project_id: 1,
    provider: "umami",
    website_id: "abc123-def456-ghi789",
    api_key: "umami_api_key_123",
    base_url: "https://analytics.example.com",
    name: "Main Website Analytics",
    status: "active",
    created_at: "2024-01-15T10:00:00Z"
  },
  {
    id: 2,
    project_id: 2,
    provider: "plausible",
    website_id: "example.com",
    api_key: "plausible_api_key_456",
    base_url: "",
    name: "Blog Analytics",
    status: "active", 
    created_at: "2024-01-20T15:30:00Z"
  }
];

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAuth();
    if (user instanceof NextResponse) {
      return user; // Return unauthorized response
    }

    const { id } = await params;
    const integrationId = parseInt(id);
    const body = await request.json();
    const { project_id, provider, website_id, api_key, base_url, name } = body;

    // Find the integration
    const integrationIndex = integrations.findIndex(i => i.id === integrationId);
    
    if (integrationIndex === -1) {
      return NextResponse.json(
        { error: "Integration not found" },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!project_id || !provider || !website_id || !name) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if another integration exists for this project and provider (excluding current one)
    const existingIntegration = integrations.find(
      i => i.project_id === project_id && i.provider === provider && i.id !== integrationId
    );

    if (existingIntegration) {
      return NextResponse.json(
        { error: "Integration already exists for this project and provider" },
        { status: 409 }
      );
    }

    // Update the integration
    const updatedIntegration = {
      ...integrations[integrationIndex],
      project_id: parseInt(project_id),
      provider,
      website_id,
      api_key: api_key || "",
      base_url: base_url || "",
      name
    };

    integrations[integrationIndex] = updatedIntegration;

    return NextResponse.json({
      success: true,
      integration: updatedIntegration
    });
  } catch (error) {
    console.error("Error updating integration:", error);
    return NextResponse.json(
      { error: "Failed to update integration" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAuth();
    if (user instanceof NextResponse) {
      return user; // Return unauthorized response
    }

    const { id } = await params;
    const integrationId = parseInt(id);

    // Find the integration
    const integrationIndex = integrations.findIndex(i => i.id === integrationId);
    
    if (integrationIndex === -1) {
      return NextResponse.json(
        { error: "Integration not found" },
        { status: 404 }
      );
    }

    // Remove the integration
    integrations.splice(integrationIndex, 1);

    return NextResponse.json({
      success: true,
      message: "Integration deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting integration:", error);
    return NextResponse.json(
      { error: "Failed to delete integration" },
      { status: 500 }
    );
  }
} 