import { NextRequest, NextResponse } from "next/server";
import { requireAuth, unauthorizedResponse } from "@/lib/auth";


// Mock data for demo purposes - in production, this would be stored in a database
let integrations = [
  {
    id: 1,
    project_id: 1,
    provider: "umami",
    website_id: "abc123-def456-ghi789",
    api_key: "umami_api_key_123",
    base_url: "https://analytics.example.com",
    name: "Main Website Analytics",
    status: "active",
    created_at: "2024-01-15T10:00:00Z"
  },
  {
    id: 2,
    project_id: 2,
    provider: "plausible",
    website_id: "example.com",
    api_key: "plausible_api_key_456",
    base_url: "",
    name: "Blog Analytics",
    status: "active", 
    created_at: "2024-01-20T15:30:00Z"
  }
];

export async function GET() {
  try {
    const user = await requireAuth();
    if (user instanceof NextResponse) {
      return user; // Return unauthorized response
    }

    return NextResponse.json({
      success: true,
      integrations
    });
  } catch (error) {
    console.error("Error fetching integrations:", error);
    return NextResponse.json(
      { error: "Failed to fetch integrations" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    if (user instanceof NextResponse) {
      return user; // Return unauthorized response
    }

    const body = await request.json();
    const { project_id, provider, website_id, api_key, base_url, name } = body;

    // Validate required fields
    if (!project_id || !provider || !website_id || !name) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if integration already exists for this project and provider
    const existingIntegration = integrations.find(
      i => i.project_id === project_id && i.provider === provider
    );

    if (existingIntegration) {
      return NextResponse.json(
        { error: "Integration already exists for this project and provider" },
        { status: 409 }
      );
    }

    // Create new integration
    const newIntegration = {
      id: Math.max(...integrations.map(i => i.id), 0) + 1,
      project_id: parseInt(project_id),
      provider,
      website_id,
      api_key: api_key || "",
      base_url: base_url || "",
      name,
      status: "active",
      created_at: new Date().toISOString()
    };

    integrations.push(newIntegration);

    return NextResponse.json({
      success: true,
      integration: newIntegration
    });
  } catch (error) {
    console.error("Error creating integration:", error);
    return NextResponse.json(
      { error: "Failed to create integration" },
      { status: 500 }
    );
  }
} 