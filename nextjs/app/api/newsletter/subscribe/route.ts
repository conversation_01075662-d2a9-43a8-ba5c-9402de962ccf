
import { NextResponse } from 'next/server';
import { subscribeToNewsletter } from '@/services/newsletter';

export async function POST(request: Request) {
  try {
    const { email, source = 'direct' } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    try {
      const result = await subscribeToNewsletter(email, source);
      
      return NextResponse.json(
        { success: true, message: result.message },
        { status: 200 }
      );
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      return NextResponse.json(
        { error: 'Failed to subscribe to newsletter' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Newsletter API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
} 
