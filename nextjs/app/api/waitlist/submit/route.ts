import { NextRequest, NextResponse } from 'next/server';
import { waitlistModel } from '@/models/waitlist';
import { notificationService } from '@/lib/notification';
import { headers } from 'next/headers';
import type { WaitlistSubmission } from '@/types/waitlist';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.email || typeof body.email !== 'string') {
      return NextResponse.json(
        { error: 'Email is required and must be a valid string' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    // Extract UTM parameters from URL if provided
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Prepare submission data
    const submissionData: WaitlistSubmission = {
      email: body.email.trim().toLowerCase(),
      name: body.name?.trim() || undefined,
      message: body.message?.trim() || undefined,
      source: body.source || 'landing_page',
      utm_source: body.utm_source || searchParams.get('utm_source') || undefined,
      utm_medium: body.utm_medium || searchParams.get('utm_medium') || undefined,
      utm_campaign: body.utm_campaign || searchParams.get('utm_campaign') || undefined,
    };

    // Get request metadata
    const headersList = await headers();
    const userAgent = headersList.get('user-agent') || undefined;
    const forwardedFor = headersList.get('x-forwarded-for');
    const realIp = headersList.get('x-real-ip');
    const cfConnectingIp = headersList.get('cf-connecting-ip');
    const referer = headersList.get('referer') || undefined;
    
    // Determine IP address (prioritize cf-connecting-ip for Cloudflare, then x-real-ip, then x-forwarded-for)
    const ipAddress = cfConnectingIp || realIp || (forwardedFor ? forwardedFor.split(',')[0].trim() : undefined);

    const metadata = {
      user_agent: userAgent,
      ip_address: ipAddress,
      referrer: referer,
    };

    // Add to waitlist
    const waitlistEntry = await waitlistModel.addToWaitlist(submissionData, metadata);

    // Send notification to admin
    try {
      await notificationService.notify({
        title: '🎯 新的Waitlist注册',
        content: `有新用户加入了waitlist！\n\n**邮箱**: ${waitlistEntry.email}\n**姓名**: ${waitlistEntry.name || '未提供'}\n**留言**: ${waitlistEntry.message || '无留言'}\n**来源**: ${waitlistEntry.source}`,
        level: 'info',
        data: {
          '用户邮箱': waitlistEntry.email,
          '用户姓名': waitlistEntry.name || '未提供',
          '注册来源': waitlistEntry.source,
          '注册时间': new Date(waitlistEntry.created_at).toLocaleString('zh-CN'),
          'UTM来源': waitlistEntry.utm_source || '无',
          'UTM媒介': waitlistEntry.utm_medium || '无',
          'UTM活动': waitlistEntry.utm_campaign || '无',
          'IP地址': ipAddress || '未知',
          '用户代理': userAgent ? userAgent.substring(0, 100) + '...' : '未知'
        }
      });
    } catch (notificationError) {
      console.error('Failed to send waitlist notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully added to waitlist',
      data: {
        id: waitlistEntry.id,
        email: waitlistEntry.email,
        created_at: waitlistEntry.created_at
      }
    });

  } catch (error: any) {
    console.error('Waitlist submission error:', error);
    
    // Handle duplicate email error
    if (error.message?.includes('duplicate') || error.code === '23505') {
      return NextResponse.json(
        { error: 'This email is already on our waitlist' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add to waitlist. Please try again.' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  // Simple endpoint to check if API is working
  return NextResponse.json({
    message: 'Waitlist API is working',
    timestamp: new Date().toISOString()
  });
} 