# AI Tasks API Documentation

This document describes the API endpoints for managing AI tasks (e.g., image generation, OCR processing).

**Base Path:** `/api/ai-task`

**Authentication:** Most endpoints require user authentication via session cookie. The callback endpoint requires specific security measures (e.g., API key, IP check).

---

## 1. Create AI Task

*   **Endpoint:** `POST /api/ai-task`
*   **Description:** Creates a new AI task for the authenticated user. Requires credit deduction logic to be implemented separately before calling the model function.
*   **Authentication:** Required (User Session)
*   **Request Body:** `application/json`
    ```json
    {
      "product_name": "string (required)", // Name of the AI product/service used
      "credit_cost": "integer (required)", // Credits to be consumed for this task
      "input_file_path": "string (required)", // Path or identifier for the input file/data
      "output_options": "object (optional)", // Specific options for the AI task (e.g., resolution, style)
      "callback_url": "string (optional)" // URL to notify upon task completion/failure
    }
    ```
*   **Responses:**
    *   `201 Created`: Task created successfully. Returns the created task object.
        ```json
        {
          "id": 1,
          "order_no": "unique-order-uuid",
          "user_uuid": "user-uuid",
          "product_name": "image-generation",
          "credit_cost": 10,
          "input_file_path": "/path/to/input.jpg",
          "output_image_path": null,
          "output_text": null,
          "output_options": { "style": "photorealistic" },
          "orderstatus": "PENDING",
          "fail_reason": null,
          "create_time": "2023-10-27T10:00:00.000Z",
          "update_time": null,
          "cost_time": 0,
          "callback_url": "https://client.example.com/notify"
        }
        ```
    *   `400 Bad Request`: Invalid request body or missing required fields.
        ```json
        { "error": "Missing required fields: ..." }
        ```
    *   `401 Unauthorized`: User not authenticated.
        ```json
        { "error": "Unauthorized" }
        ```
    *   `500 Internal Server Error`: Failed to create task.
        ```json
        { "error": "Failed to create AI task: ..." }
        ```

---

## 2. List User's AI Tasks

*   **Endpoint:** `GET /api/ai-task/list`
*   **Description:** Retrieves a paginated list of AI tasks for the authenticated user.
*   **Authentication:** Required (User Session)
*   **Query Parameters:**
    *   `page` (integer, optional, default: 1): Page number for pagination.
    *   `limit` (integer, optional, default: 10, max: 100): Number of tasks per page.
*   **Responses:**
    *   `200 OK`: List of tasks retrieved successfully. Pagination info in headers (`X-Total-Count`, `X-Page`, `X-Per-Page`).
        ```json
        {
          "data": [
            // Array of AiTask objects (see structure in Create response)
          ],
          "count": 55 // Total number of tasks for the user
        }
        ```
    *   `400 Bad Request`: Invalid pagination parameters.
        ```json
        { "error": "Invalid pagination parameters..." }
        ```
    *   `401 Unauthorized`: User not authenticated.
        ```json
        { "error": "Unauthorized" }
        ```
    *   `500 Internal Server Error`: Failed to fetch tasks.
        ```json
        { "error": "Failed to fetch AI tasks: ..." }
        ```

---

## 3. Get Specific AI Task

*   **Endpoint:** `GET /api/ai-task/{order_no}`
*   **Description:** Retrieves details of a specific AI task by its `order_no`.
*   **Authentication:** Required (User Session)
*   **Path Parameters:**
    *   `order_no` (string, required): The unique order number of the task.
*   **Responses:**
    *   `200 OK`: Task details retrieved successfully. Returns the task object directly.
        ```json
        {
          // Single AiTask object (see structure in Create response)
        }
        ```
    *   `401 Unauthorized`: User not authenticated.
        ```json
        { "error": "Unauthorized" }
        ```
    *   `403 Forbidden`: User does not own this task.
        ```json
        { "error": "You do not have permission to access this task." }
        ```
    *   `404 Not Found`: Task with the given `order_no` not found.
        ```json
        { "error": "AI Task not found." }
        ```
    *   `500 Internal Server Error`: Failed to fetch task.
        ```json
        { "error": "Failed to fetch AI task: ..." }
        ```

---

## 4. Update AI Task (Partial)

*   **Endpoint:** `PATCH /api/ai-task/{order_no}`
*   **Description:** Partially updates a specific AI task. Primarily intended for user-initiated actions like cancellation, though most status updates should come via the callback.
*   **Authentication:** Required (User Session)
*   **Path Parameters:**
    *   `order_no` (string, required): The unique order number of the task.
*   **Request Body:** `application/json` - Contains fields to update.
    ```json
    {
      "orderstatus": "CANCELLED" // Example: User cancels a pending task
      // Other fields from UpdateAiTaskInput might be allowed depending on logic
    }
    ```
*   **Responses:**
    *   `200 OK`: Task updated successfully. Returns the updated task object.
        ```json
        {
          // Updated AiTask object
        }
        ```
    *   `400 Bad Request`: Invalid request body or disallowed update.
        ```json
        { "error": "No update data provided." }
        ```
    *   `401 Unauthorized`: User not authenticated.
        ```json
        { "error": "Unauthorized" }
        ```
    *   `403 Forbidden`: User does not own this task.
        ```json
        { "error": "You do not have permission to modify this task." }
        ```
    *   `404 Not Found`: Task with the given `order_no` not found (either initially or during update).
        ```json
        { "error": "AI Task not found." }
        // or { "error": "AI Task not found during update." }
        ```
    *   `500 Internal Server Error`: Failed to update task.
        ```json
        { "error": "Failed to update AI task: ..." }
        ```

---

## 5. Delete AI Task

*   **Endpoint:** `DELETE /api/ai-task/{order_no}`
*   **Description:** Deletes a specific AI task. May be restricted based on task status (e.g., cannot delete if processing).
*   **Authentication:** Required (User Session)
*   **Path Parameters:**
    *   `order_no` (string, required): The unique order number of the task.
*   **Responses:**
    *   `204 No Content`: Task deleted successfully.
    *   `400 Bad Request`: Deletion not allowed (e.g., task is processing).
        ```json
        { "error": "Cannot delete a task that is currently processing." } // Example
        ```
    *   `401 Unauthorized`: User not authenticated.
        ```json
        { "error": "Unauthorized" }
        ```
    *   `403 Forbidden`: User does not own this task.
        ```json
        { "error": "You do not have permission to delete this task." }
        ```
    *   `404 Not Found`: Task with the given `order_no` not found.
        ```json
        { "error": "AI Task not found." }
        // or { "error": "AI Task not found during deletion." }
        ```
    *   `500 Internal Server Error`: Failed to delete task.
        ```json
        { "error": "Failed to delete AI task: ..." }
        ```

---

## 6. AI Task Callback

*   **Endpoint:** `POST /api/ai-task/callback`
*   **Description:** Endpoint for the external AI processing worker to send status updates and results back to the application. The worker must provide the correct secret key in the `X-Callback-API-Key` header.
*   **Authentication:** Required (Shared Secret via `X-Callback-API-Key` header, using `AI_WORKER_CALLBACK_SECRET` environment variable on the server). IP Whitelisting is also recommended.
*   **Request Body:** `application/json`
    ```json
    {
      "order_no": "string (required)",
      "orderstatus": "string (required, e.g., 'PROCESSING', 'SUCCEED', 'FAILED')",
      "output_image_path": "string (optional)",
      "output_text": "string (optional)",
      "output_options": "object (optional)",
      "fail_reason": "string (optional, required if status is 'FAILED')",
      "cost_time": "integer (optional)" // Worker might calculate and send this
    }
    ```
*   **Responses:**
    *   `200 OK`: Callback received and processed successfully.
        ```json
        {
          "message": "Callback processed successfully."
          // Or: { "message": "Callback processed, but task {order_no} not found." }
        }
        ```
    *   `400 Bad Request`: Invalid request body or missing required fields.
        ```json
        { "error": "Missing required fields..." }
        // or { "error": "Invalid JSON format..." }
        ```
    *   `401 Unauthorized`: Missing or incorrect `X-Callback-API-Key` header.
        ```json
        { "error": "Invalid callback credentials." }
        ```
    *   `404 Not Found`: Task referenced by `order_no` not found (if strict checking is enabled, otherwise returns 200 OK with message).
        ```json
        { "error": "AI Task with order_no ... not found." }
        ```
    *   `500 Internal Server Error`: Failed to process callback or server configuration error (e.g., secret not set).
        ```json
        { "error": "Failed to process callback: ..." }
        // or { "error": "An unexpected error occurred..." }
        // or { "error": "Callback configuration error." }
        ```