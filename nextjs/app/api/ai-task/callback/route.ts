import { NextRequest, NextResponse } from 'next/server';
import { handleAiTaskCallback } from '@/models/aiTask'; // Adjust path
import { AiTaskCallbackPayload } from '@/types/aiTask';
// Remove Resp import
// import { Resp } from '@/lib/resp'; // Adjust path

// POST /api/ai-tasks/callback - Endpoint for AI worker to send updates
export async function POST(req: NextRequest) {
  // Implement security check using a shared secret
  const apiKey = req.headers.get('X-Callback-API-Key'); // Or another agreed-upon header/method
  if (!process.env.AI_WORKER_CALLBACK_SECRET) {
      console.error('CRITICAL: AI_WORKER_CALLBACK_SECRET is not set in environment variables.');
      // Return a generic error to avoid leaking information
      return NextResponse.json({ error: 'Callback configuration error.' }, { status: 500 });
  }
  if (apiKey !== process.env.AI_WORKER_CALLBACK_SECRET) {
    console.warn('Unauthorized callback attempt received.');
    return NextResponse.json({ error: 'Invalid callback credentials.' }, { status: 401 });
  }

  try {
    const payload = await req.json() as AiTaskCallbackPayload;

    // Validate payload
    if (!payload.order_no || !payload.orderstatus) {
      return NextResponse.json(
          { error: 'Missing required fields in callback payload: order_no, orderstatus' },
          { status: 400 }
      );
    }

    // Add more validation based on AiTaskCallbackPayload type if needed

    console.log(`Received callback for order ${payload.order_no} with status ${payload.orderstatus}`);

    const { data: updatedTask, error: callbackError } = await handleAiTaskCallback(payload);

    if (callbackError) {
        console.error(`Error processing callback for order ${payload.order_no}:`, callbackError);
        // Check if it was a 'not found' error
        const errorMessage = callbackError.message || '';
        // Use specific Supabase error code if available, e.g., PGRST116 for not found
        if (errorMessage.includes('not found') || (callbackError as any).code === 'PGRST116') {
             console.warn(`Callback received for unknown order_no: ${payload.order_no}`);
             // Return success to the worker to prevent retries for non-existent orders
             // Return success (200 OK) to the worker to prevent retries, but indicate the issue in the message.
             return NextResponse.json({ message: `Callback processed, but task ${payload.order_no} not found.` }, { status: 200 });
             // Or return 404 if strict checking is required:
             // return NextResponse.json({ error: `AI Task with order_no ${payload.order_no} not found.` }, { status: 404 });
        }
        // For other errors, return internal server error
        return NextResponse.json({ error: `Failed to process callback: ${errorMessage}` }, { status: 500 });
    }

    // If error is null, updatedTask should contain the updated task data
    if (!updatedTask) {
        // This case indicates an issue in the model function logic if error is null
        console.error(`Callback for order ${payload.order_no} processed without error but returned null data.`);
        return NextResponse.json({ error: 'Callback processed but failed to retrieve updated task data.' }, { status: 500 });
   }

   // Removed the client callback notification logic as per feedback.
   // The callback_url in the DB is for the AI worker, not this API.

   console.log(`Successfully processed callback for order ${payload.order_no}`);
   // Return simple success message
    return NextResponse.json({ message: 'Callback processed successfully.' }, { status: 200 });

  } catch (error) { // Catch unexpected errors (e.g., JSON parsing)
    console.error('Unexpected error processing AI task callback:', error);
    const message = error instanceof Error ? error.message : 'An unexpected error occurred.';
     if (error instanceof SyntaxError) {
        return NextResponse.json({ error: 'Invalid JSON format in callback payload.' }, { status: 400 });
    }
    // Use a more generic error message for unexpected issues
    return NextResponse.json({ error: `An unexpected error occurred: ${message}` }, { status: 500 });
  }
}