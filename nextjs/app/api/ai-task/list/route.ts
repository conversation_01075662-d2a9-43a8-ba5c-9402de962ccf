
import { NextRequest, NextResponse } from 'next/server';
// Assuming getUserUuid exists and provides the necessary user info
import { getUserUuid, getUserEmail } from "@/services/user";
import { getAiTasksByUserUuid } from '@/models/aiTask'; // Adjust path

// GET /api/ai-tasks/list - Get AI Tasks for the logged-in user (paginated)
export async function GET(req: NextRequest) {
  // Use the provided auth pattern
  const user_uuid = await getUserUuid();
  if (!user_uuid) {
    return NextResponse.json({ error: 'Unauthorized: No user UUID found.' }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const page = parseInt(searchParams.get('page') || '1', 10);
  const limit = parseInt(searchParams.get('limit') || '10', 10);

  if (isNaN(page) || page < 1 || isNaN(limit) || limit < 1 || limit > 100) {
      return NextResponse.json(
          { error: 'Invalid pagination parameters. Page must be >= 1, Limit must be between 1 and 100.' },
          { status: 400 }
      );
  }

  try {
    // Use the obtained user_uuid
    const { data: result, error: fetchError } = await getAiTasksByUserUuid(user_uuid, page, limit);

    if (fetchError) {
        console.error(`Error fetching AI tasks for user ${user_uuid} via API:`, fetchError);
        return NextResponse.json({ error: `Failed to fetch AI tasks: ${fetchError.message}` }, { status: 500 });
    }

    // Handle case where data might be null (though model function should return { tasks: [], total: 0 })
    if (!result) {
         console.error('getAiTasksByUserUuid returned null data without error');
         return NextResponse.json({ error: 'Failed to retrieve task data.' }, { status: 500 });
    }

    // Return data in the { data: [...], count: number } format, similar to items/search
    // Add pagination headers like items/public
    const headers = new Headers();
    headers.set('X-Total-Count', result.total.toString());
    headers.set('X-Page', page.toString());
    headers.set('X-Per-Page', limit.toString());
    // Consider adding Link header for pagination: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link

    return NextResponse.json(
        { data: result.tasks, count: result.total },
        { status: 200, headers: headers }
    );

  } catch (error) { // Catch unexpected errors
    console.error('Unexpected error in GET /api/ai-tasks/list:', error);
    const message = error instanceof Error ? error.message : 'An unexpected error occurred.';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}