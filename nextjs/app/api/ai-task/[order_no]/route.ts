
import { NextRequest, NextResponse } from 'next/server';
// Assuming these functions exist and provide the necessary user info/checks
import { getUserUuid, getUserEmail } from "@/services/user";
import { isAdminAuthenticated, unauthorizedResponse, badRequestResponse, serverErrorResponse, notFoundResponse, forbiddenResponse } from '@/lib/adminAuth'; // Import admin checks and responses
import { getAiTaskByOrderNo, updateAiTaskByOrderNo, deleteAiTaskByOrderNo } from '@/models/aiTask';
import { UpdateAiTaskInput } from '@/types/aiTask';

// GET /api/ai-tasks/{order_no} - Get a specific AI Task (User or Admin)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ order_no: string }> }
) {
  // Check if user is logged in OR is an admin
  const user_uuid = await getUserUuid();
  const isAdmin = await isAdminAuthenticated(request);

  if (!user_uuid && !isAdmin) {
    return unauthorizedResponse(); // Use adminAuth response helper
  }

  const { order_no } = await params;

  try {
    const { data: task, error: fetchError } = await getAiTaskByOrderNo(order_no);

    if (fetchError) {
        console.error(`Error fetching AI task ${order_no} via API:`, fetchError);
        return NextResponse.json({ error: `Failed to fetch AI task: ${fetchError.message}` }, { status: 500 });
    }

    if (!task) {
      return NextResponse.json({ error: 'AI Task not found.' }, { status: 404 });
    }

    // If not admin, ensure the user requesting the task is the owner
    if (!isAdmin && task.user_uuid !== user_uuid) {
      return forbiddenResponse(); // Use adminAuth helper
    }

    // Return task data directly on success
    return NextResponse.json(task, { status: 200 });

  } catch (error) { // Catch unexpected errors
    console.error(`Unexpected error in GET /api/ai-tasks/${order_no}:`, error);
    const message = error instanceof Error ? error.message : 'An unexpected error occurred.';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}

// PATCH /api/ai-tasks/{order_no} - Update a specific AI Task (User or Admin)
// Note: Allows user to update their own tasks (potentially limited fields), or admin to update any task.
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ order_no: string }> }
) {
    // Check if user is logged in OR is an admin
    const user_uuid = await getUserUuid();
    const isAdmin = await isAdminAuthenticated(request);

    if (!user_uuid && !isAdmin) {
        return unauthorizedResponse();
    }

    const { order_no } = await params;

    try {
        const body = await request.json(); // Use request from function signature
        const updateData: UpdateAiTaskInput = body;

        // Validate input data (add more specific validation)
        if (Object.keys(updateData).length === 0) {
            return NextResponse.json({ error: 'No update data provided.' }, { status: 400 });
        }
        // Potentially restrict which fields can be updated via PATCH by user
        // e.g., allow updating 'orderstatus' to 'CANCELLED' only if current status is 'PENDING'

        // First check if task exists and belongs to user
        const { data: existingTask, error: fetchError } = await getAiTaskByOrderNo(order_no);

        if (fetchError) {
            console.error(`Error fetching task ${order_no} before update:`, fetchError);
            return serverErrorResponse(`Failed to fetch task before update: ${fetchError.message}`, fetchError); // Use adminAuth helper
        }
        if (!existingTask) {
            return notFoundResponse('AI Task not found.'); // Use adminAuth helper
        }
        // If not admin, check ownership
        if (!isAdmin && existingTask.user_uuid !== user_uuid) {
            return forbiddenResponse(); // Use adminAuth helper
        }

        // Add logic here to check if the requested update is allowed based on current status
        // For example:
        // if (updateData.orderstatus === 'CANCELLED' && existingTask.orderstatus !== 'PENDING') {
        //     return Resp.invalidRequest('Task cannot be cancelled as it is already processing.');
        // }

        // Perform the update
        const { data: updatedTask, error: updateError } = await updateAiTaskByOrderNo(order_no, updateData);

        if (updateError) {
             console.error(`Error updating AI task ${order_no} via API:`, updateError);
             // Check if it was a 'not found' error during update
             const errorMessage = updateError.message || '';
             if (errorMessage.includes('not found') || (updateError as any).code === 'PGRST116') {
                 return notFoundResponse('AI Task not found during update.'); // Use adminAuth helper
             }
             return serverErrorResponse(`Failed to update AI task: ${errorMessage}`, updateError); // Use adminAuth helper
        }

        // updatedTask should not be null if updateError is null based on model logic
        if (!updatedTask) {
             console.error(`Update for task ${order_no} succeeded but returned null data.`);
             // Use serverErrorResponse for consistency
             return serverErrorResponse('Update completed but failed to retrieve updated data.');
        }

        // Return updated task data directly
        return NextResponse.json(updatedTask, { status: 200 });

    } catch (error) { // Catch unexpected errors (e.g., JSON parsing)
        console.error(`Unexpected error in PATCH /api/ai-tasks/${order_no}:`, error);
        if (error instanceof SyntaxError) {
            return badRequestResponse('Invalid JSON format.'); // Use adminAuth helper
        }
        return serverErrorResponse('An unexpected error occurred.', error); // Use adminAuth helper
    }
}


// DELETE /api/ai-tasks/{order_no} - Delete a specific AI Task (User or Admin)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ order_no: string }> }
) {
  // Check if user is logged in OR is an admin
  const user_uuid = await getUserUuid();
  const isAdmin = await isAdminAuthenticated(request);

  if (!user_uuid && !isAdmin) {
    return unauthorizedResponse();
  }

  const { order_no } = await params;

  try {
    // First check if task exists and belongs to user
    const { data: task, error: fetchError } = await getAiTaskByOrderNo(order_no);

     if (fetchError) {
        console.error(`Error fetching task ${order_no} before delete:`, fetchError);
        return serverErrorResponse(`Failed to fetch task before delete: ${fetchError.message}`, fetchError);
    }
    if (!task) {
      return notFoundResponse('AI Task not found.');
    }
    // If not admin, check ownership
    if (!isAdmin && task.user_uuid !== user_uuid) {
      return forbiddenResponse();
    }

    // Optional: Add logic to prevent deletion based on status
    // if (['PROCESSING'].includes(task.orderstatus)) {
    //     return Resp.invalidRequest('Cannot delete a task that is currently processing.');
    // }

    // Perform the deletion
    const { error: deleteError } = await deleteAiTaskByOrderNo(order_no);

    if (deleteError) {
       console.error(`Error deleting AI task ${order_no} via API:`, deleteError);
       // Check if it was a 'not found' error during delete
       const errorMessage = deleteError.message || '';
       if (errorMessage.includes('not found') || (deleteError as any).code === 'PGRST116') {
           return notFoundResponse('AI Task not found during deletion.');
       }
       return serverErrorResponse(`Failed to delete AI task: ${errorMessage}`, deleteError);
    }

    // Return no content on successful deletion
    return new NextResponse(null, { status: 204 });

  } catch (error) { // Catch unexpected errors
    console.error(`Unexpected error in DELETE /api/ai-tasks/${order_no}:`, error);
    return serverErrorResponse('An unexpected error occurred.', error);
  }
}