
import { NextRequest, NextResponse } from 'next/server';
// Assuming these functions exist and provide the necessary user info
import { getUserUuid, getUserEmail } from "@/services/user";
import { findUserByUuid } from '@/models/user'; // Assuming user model exists
import { createAiTask } from '@/models/aiTask';
import { CreateAiTaskInput } from '@/types/aiTask';

// POST /api/ai-tasks - Create a new AI Task
export async function POST(req: NextRequest) {
  // Use the provided auth pattern
  const user_uuid = await getUserUuid();
  if (!user_uuid) {
    return NextResponse.json({ error: 'Unauthorized: No user UUID found.' }, { status: 401 });
  }

  try {
    const body = await req.json();

    // Basic validation (add more robust validation as needed)
    const { product_name, credit_cost, input_file_path, callback_url, output_options } = body;
    if (!product_name || typeof credit_cost !== 'number' || !input_file_path) {
      return NextResponse.json(
          { error: 'Missing required fields: product_name, credit_cost, input_file_path' },
          { status: 400 }
      );
    }

    // Optional: Verify email exists for the user if required by business logic
    // let user_email = await getUserEmail();
    // if (!user_email) {
    //   const { data: user } = await findUserByUuid(user_uuid); // Assuming findUserByUuid returns { data, error }
    //   if (user) {
    //     user_email = user.email;
    //   }
    // }
    // if (!user_email) {
    //   return NextResponse.json({ error: 'Invalid user: Email not found.' }, { status: 400 });
    // }

    const taskData: Omit<CreateAiTaskInput, 'order_no'> & { user_uuid: string } = {
      user_uuid: user_uuid, // Use the obtained user_uuid
      product_name,
      credit_cost,
      input_file_path,
      callback_url, // Optional
      output_options, // Optional
    };

    // TODO: Add credit deduction logic here before creating the task
    // Example: check if user has enough credits, then deduct

    const { data: newTask, error: createTaskError } = await createAiTask(taskData);

    if (createTaskError) {
        console.error('Error creating AI task via API:', createTaskError);
        // Check for the specific insufficient credits error
        if (createTaskError.message === 'INSUFFICIENT_CREDITS') {
            return NextResponse.json({ error: 'Insufficient credits to perform this task.' }, { status: 400 }); // 400 Bad Request is appropriate
        }
        // Handle other errors as internal server errors
        return NextResponse.json({ error: `Failed to create AI task: ${createTaskError.message}` }, { status: 500 });
    }

    // newTask should not be null if error is null based on createAiTask implementation
    // Return the created task object directly on success, status 201 Created
    return NextResponse.json(newTask, { status: 201 });

  } catch (error) { // Catch unexpected errors (e.g., JSON parsing)
    console.error('Unexpected error in POST /api/ai-tasks:', error);
    const message = error instanceof Error ? error.message : 'An unexpected error occurred.';
    if (error instanceof SyntaxError) {
        return NextResponse.json({ error: 'Invalid JSON format.' }, { status: 400 });
    }
    return NextResponse.json({ error: message }, { status: 500 });
  }
}

// Removed GET handler - moved to /list/route.ts