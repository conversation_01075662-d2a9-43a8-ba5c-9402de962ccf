import { NextRequest, NextResponse } from 'next/server';
import { getPublicLinkResourcesWithStats } from '@/models/public-link-resources';
import { PublicLinkResourceWithStats } from '@/types/links';


export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const isPaid = searchParams.get('isPaid');
    const sort = searchParams.get('sort') || 'dr_desc';
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get public link resources with stats using model function
    const { data: paginatedResources, error: resourcesError } = await getPublicLinkResourcesWithStats(
      {
        category: category || undefined,
        isPaid: isPaid !== null ? isPaid === 'true' : undefined,
        search: search || undefined,
        isActive: true
      },
      sort,
      limit,
      offset
    );

    if (resourcesError) {
      console.error('Failed to fetch public link resources:', resourcesError);
      return NextResponse.json({ error: 'Failed to fetch resources' }, { status: 500 });
    }

    return NextResponse.json(paginatedResources || []);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}