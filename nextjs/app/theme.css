:root {
  /* Light theme - clean and minimalist */
  --background: 0 0% 100%;
  --foreground: 220 13% 18%;
  --card: 0 0% 100%;
  --card-foreground: 220 13% 18%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 13% 18%;

  /* Subdued primary colors - neutral slate */
  --primary: 220 13% 18%;
  --primary-foreground: 0 0% 98%;
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 13% 18%;

  /* Light theme contrast - subtle grays */
  --muted: 220 14% 96%;
  --muted-foreground: 220 9% 46%;
  --accent: 220 14% 96%;
  --accent-foreground: 220 13% 18%;

  /* Clean borders - minimal contrast */
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 220 13% 18%;

  /* Consistent destructive colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;

  /* Reduced radius for cleaner lines */
  --radius: 0.5rem;

  /* Minimal gradients - subtle and clean */
  --gradient-primary: linear-gradient(135deg, hsl(220 13% 18%) 0%, hsl(220 13% 25%) 100%);
  --gradient-card: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(220 14% 98%) 100%);
  --gradient-border: linear-gradient(135deg, hsl(220 13% 91%) 0%, hsl(220 13% 85%) 100%);

  /* Subtle shadows instead of glows */
  --glow-primary: 0 1px 3px hsl(220 13% 18% / 0.1);
  --glow-accent: 0 1px 2px hsl(220 13% 18% / 0.05);
  --glow-success: 0 1px 2px hsl(142 69% 58% / 0.1);

  /* Enhanced chart and data visualization colors - muted and accessible */
  --chart-primary: 71 84% 56%;        /* Muted sage green */
  --chart-secondary: 200 98% 39%;     /* Muted steel blue */
  --chart-tertiary: 25 95% 53%;       /* Muted amber */
  --chart-quaternary: 262 83% 58%;    /* Muted lavender */
  --chart-quinary: 346 77% 49%;       /* Muted rose */

  /* Data visualization accent colors */
  --data-positive: 142 69% 58%;       /* Muted green for positive trends */
  --data-negative: 0 84% 60%;         /* Muted red for negative trends */
  --data-neutral: 220 9% 46%;         /* Neutral gray */
  --data-warning: 38 92% 50%;         /* Muted orange for warnings */
  --data-info: 199 89% 48%;           /* Muted cyan for info */

  /* AI traffic source colors - subdued and distinct */
  --ai-chatgpt: 142 69% 58%;          /* Muted emerald */
  --ai-claude: 262 83% 58%;           /* Muted purple */
  --ai-claude-search: 220 98% 39%;    /* Muted indigo */
  --ai-perplexity: 199 89% 48%;       /* Muted blue */
  --ai-other: 220 9% 46%;             /* Neutral gray */

  /* Dashboard component colors */
  --dashboard-accent-1: 71 84% 56%;   /* Sage green for primary metrics */
  --dashboard-accent-2: 200 98% 39%;  /* Steel blue for secondary metrics */
  --dashboard-accent-3: 25 95% 53%;   /* Amber for traffic/performance */
  
  /* Responsive spacing variables */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Responsive container widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

.dark {
  /* Dark theme - clean and minimalist */
  --background: 220 13% 9%;
  --foreground: 220 14% 96%;
  --card: 220 13% 12%;
  --card-foreground: 220 14% 96%;
  --popover: 220 13% 9%;
  --popover-foreground: 220 14% 96%;

  /* Subdued primary colors - consistent with light theme */
  --primary: 220 14% 96%;
  --primary-foreground: 220 13% 9%;
  --secondary: 220 13% 18%;
  --secondary-foreground: 220 14% 96%;

  /* Enhanced contrast for dark theme */
  --muted: 220 13% 18%;
  --muted-foreground: 220 9% 64%;
  --accent: 220 13% 18%;
  --accent-foreground: 220 14% 96%;

  /* Clean borders for dark theme */
  --border: 220 13% 24%;
  --input: 220 13% 24%;
  --ring: 220 14% 96%;

  /* Consistent destructive colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;

  /* Reduced radius for cleaner lines */
  --radius: 0.5rem;

  /* Minimal gradients for dark theme */
  --gradient-primary: linear-gradient(135deg, hsl(220 14% 96%) 0%, hsl(220 14% 88%) 100%);
  --gradient-card: linear-gradient(135deg, hsl(220 13% 12%) 0%, hsl(220 13% 15%) 100%);
  --gradient-border: linear-gradient(135deg, hsl(220 13% 24%) 0%, hsl(220 13% 30%) 100%);

  /* Subtle shadows for dark theme */
  --glow-primary: 0 1px 3px hsl(220 14% 96% / 0.1);
  --glow-accent: 0 1px 2px hsl(220 14% 96% / 0.05);
  --glow-success: 0 1px 2px hsl(142 69% 58% / 0.1);

  /* Enhanced chart and data visualization colors for dark theme */
  --chart-primary: 71 84% 56%;        /* Muted sage green */
  --chart-secondary: 200 98% 39%;     /* Muted steel blue */
  --chart-tertiary: 25 95% 53%;       /* Muted amber */
  --chart-quaternary: 262 83% 58%;    /* Muted lavender */
  --chart-quinary: 346 77% 49%;       /* Muted rose */

  /* Data visualization accent colors for dark theme */
  --data-positive: 142 69% 58%;       /* Muted green for positive trends */
  --data-negative: 0 84% 60%;         /* Muted red for negative trends */
  --data-neutral: 220 14% 96%;        /* Light gray for dark theme */
  --data-warning: 38 92% 50%;         /* Muted orange for warnings */
  --data-info: 199 89% 48%;           /* Muted cyan for info */

  /* AI traffic source colors for dark theme */
  --ai-chatgpt: 142 69% 58%;          /* Muted emerald */
  --ai-claude: 262 83% 58%;           /* Muted purple */
  --ai-claude-search: 220 98% 39%;    /* Muted indigo */
  --ai-perplexity: 199 89% 48%;       /* Muted blue */
  --ai-other: 220 14% 96%;            /* Light gray for dark theme */

  /* Dashboard component colors for dark theme */
  --dashboard-accent-1: 71 84% 56%;   /* Sage green for primary metrics */
  --dashboard-accent-2: 200 98% 39%;  /* Steel blue for secondary metrics */
  --dashboard-accent-3: 25 95% 53%;   /* Amber for traffic/performance */
}

/* Global creative enhancements */
* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Dashboard-specific utility classes */
.chart-color-primary {
  color: hsl(var(--chart-primary));
}

.chart-color-secondary {
  color: hsl(var(--chart-secondary));
}

.chart-color-tertiary {
  color: hsl(var(--chart-tertiary));
}

.dashboard-accent-bg {
  background-color: hsl(var(--dashboard-accent-1) / 0.1);
}

.data-positive-color {
  color: hsl(var(--data-positive));
}

.data-negative-color {
  color: hsl(var(--data-negative));
}

/* Clean scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Clean button styling */
.btn-neon {
  background: hsl(var(--primary));
  border: 1px solid hsl(var(--border));
  transition: all 0.2s ease;
}

.btn-neon:hover {
  background: hsl(var(--primary) / 0.9);
  border-color: hsl(var(--primary));
  box-shadow: var(--glow-primary);
}

/* Clean card effect */
.card-glow {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: var(--glow-accent);
}

/* Enhanced form borders */
.form-enhanced {
  border: 2px solid hsl(var(--border));
  box-shadow: 0 1px 3px hsl(var(--border) / 0.15);
  transition: all 0.2s ease;
}

.form-enhanced:focus-within,
.form-enhanced:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2), 0 2px 4px hsl(var(--border) / 0.1);
}

.form-enhanced:hover:not(:focus):not(:focus-within) {
  border-color: hsl(var(--border) / 0.8);
  box-shadow: 0 2px 4px hsl(var(--border) / 0.12);
}

/* Animated gradient borders */
.gradient-border {
  position: relative;
  background: hsl(var(--card));
  border-radius: var(--radius);
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--gradient-border);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  -webkit-mask-composite: xor;
}

/* Pulsing animations */
@keyframes pulse-neon {
  0%, 100% {
    box-shadow: var(--glow-primary);
  }
  50% {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.6);
  }
}

/* Responsive layout utilities for better space utilization */
.layout-optimized {
  width: 100%;
  max-width: none;
  margin: 0 auto;
}

.sidebar-responsive {
  width: 14rem; /* 224px - w-56 */
}

@media (min-width: 1280px) {
  .sidebar-responsive {
    width: 16rem; /* 256px - w-64 */
  }
}

@media (min-width: 1536px) {
  .sidebar-responsive {
    width: 18rem; /* 288px - w-72 */
  }
}

.content-responsive {
  padding: var(--spacing-sm);
}

@media (min-width: 640px) {
  .content-responsive {
    padding: var(--spacing-md);
  }
}

@media (min-width: 1024px) {
  .content-responsive {
    padding: var(--spacing-md);
  }
}

@media (min-width: 1280px) {
  .content-responsive {
    padding: var(--spacing-lg);
  }
}

/* Optimized card layouts */
.card-grid-responsive {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

@media (min-width: 640px) {
  .card-grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .card-grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .card-grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Flexible container for better space usage */
.container-fluid {
  width: 100%;
  max-width: none;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (min-width: 1024px) {
  .container-fluid {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }
}

/* Responsive text sizing */
.text-responsive {
  font-size: 0.875rem; /* 14px */
}

@media (min-width: 1280px) {
  .text-responsive {
    font-size: 1rem; /* 16px */
  }
}

.heading-responsive {
  font-size: 1.25rem; /* 20px */
}

@media (min-width: 1280px) {
  .heading-responsive {
    font-size: 1.5rem; /* 24px */
  }
}

/* Mobile-first responsive utilities */
.mobile-stack {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

@media (min-width: 640px) {
  .mobile-stack {
    flex-direction: row;
    align-items: center;
  }
}

/* Optimized spacing for different screen sizes */
.spacing-responsive {
  margin-bottom: var(--spacing-md);
}

@media (min-width: 1024px) {
  .spacing-responsive {
    margin-bottom: var(--spacing-lg);
  }
}

.animate-pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite;
}

/* Matrix-style text effect */
.text-matrix {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}
