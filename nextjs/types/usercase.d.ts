export interface UserCase {
  uuid?: string;
  type: 'twitter' | 'x' | 'jike' | 'youtube';
  url: string;
  details?: any;
  created_at?: string;
  updated_at?: string;
  ai_summary?: Record<string, string>;
  content?: Record<string, string>;
  related_items?: string[];
  status?: string;
  title?: string;
  author_name?: string;
  author_avatar_url?: string;
  image_urls?: string[];
  video_urls?: string[];
}

export type UserCaseType = 'twitter' | 'x' | 'jike' | 'youtube'; 