// Waitlist related types

export interface WaitlistEntry {
  id: string;
  email: string;
  name?: string;
  message?: string;
  source: string;
  status: 'pending' | 'contacted' | 'converted' | 'unsubscribed';
  user_agent?: string;
  ip_address?: string;
  referrer?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  contacted_at?: string;
  converted_at?: string;
}

export interface WaitlistSubmission {
  email: string;
  name?: string;
  message?: string;
  source?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

export interface WaitlistStats {
  total_signups: number;
  pending: number;
  contacted: number;
  converted: number;
  unsubscribed: number;
  signups_today: number;
  signups_this_week: number;
  signups_this_month: number;
  conversion_rate: number;
}

export interface WaitlistBySource {
  source: string;
  count: number;
  percentage: number;
}

export interface RecentWaitlistSignup {
  id: string;
  email: string;
  name?: string;
  message?: string;
  source: string;
  created_at: string;
} 