import { Items, ItemLocalization } from './items';

export interface EmbeddingItem {
  id: number;
  item_uuid: string;
  language_code: string;
  brief_vector?: number[];
  processinfo_vector?: number[];
  update_time: Date;
}

export interface EmbeddingItem extends Omit<Items, 'id'> {
  doc_embedding_en?: number[];
}

export interface EmbeddingItemLocalization extends Omit<ItemLocalization, 'id'> {
  doc_embedding?: number[];
}

export interface EmbeddingItemWithSimilarity extends EmbeddingItem {
  similarity?: number;
}

export interface EmbeddingItemLocalizationWithSimilarity extends EmbeddingItemLocalization {
  similarity?: number;
  item?: Partial<Items>;
}

export interface VectorSearchParams {
  text: string;
  language?: string;
  threshold?: number;
  limit?: number;
}

export interface VectorSearchResult {
  data: EmbeddingItemWithSimilarity[] | EmbeddingItemLocalizationWithSimilarity[];
  count: number;
  language: string;
}