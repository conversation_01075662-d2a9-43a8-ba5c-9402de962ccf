import { Project } from "./links";

export interface Integration {
  id: string;
  project_id: string;
  provider: 'umami' | 'plausible' | 'google-analytics';
  website_id: string;
  api_key?: string;
  base_url?: string;
  name: string;
  status: 'active' | 'inactive' | 'error';
  created_at: string;
  updated_at?: string;
  project?: Project;
}

export interface IntegrationFormData {
  project_id: string;
  provider: string;
  website_id: string;
  api_key?: string;
  base_url?: string;
  name: string;
}

export interface IntegrationResponse {
  success: boolean;
  integration?: Integration;
  integrations?: Integration[];
  error?: string;
  message?: string;
} 