export interface DomainInfo {
  id: string;
  domain: string;
  registrar?: string;
  createdDate?: string;
  expiryDate?: string;
  status?: string;
  nameServers?: string[];
  registrationPrice?: number;
  renewalPrice?: number;
  currency?: string;
  dnsProvider?: string;
  autoRenew?: boolean;
  projectCount: number;
  associatedProjects?: ProjectAssociation[];
  lastUpdated?: string;
  isActive: boolean;
  notes?: string;
  tags?: string[];
  isFavorite?: boolean;
  monitorExpiry?: boolean;
  alertDaysBefore?: number;
}

export interface ProjectAssociation {
  project_id: string;
  project_name: string;
  project_domain: string;
  is_primary: boolean;
}

export interface DomainWhoisData {
  registrar?: string;
  createdDate?: string;
  expiryDate?: string;
  status?: string;
  nameServers?: string[];
  updatedDate?: string;
}

export interface DomainFilters {
  status?: 'all' | 'active' | 'expired' | 'expiring';
  registrar?: string;
  dnsProvider?: string;
  sortBy?: 'domain' | 'expiryDate' | 'createdDate' | 'projectCount' | 'renewalPrice';
  sortOrder?: 'asc' | 'desc';
  tags?: string[];
  isFavorite?: boolean;
}

export interface DomainStats {
  totalDomains: number;
  activeDomains: number;
  expiredDomains: number;
  expiringDomains: number;
  totalProjects: number;
} 