// LinkResource - represents user's link resources (decoupled from projects)
export interface LinkResource {
  id: string;
  /** 外链资源的完整URL，表示可以提交外链的网址，使用 normalizeUrl 函数归一化存储 */
  url: string;
  title: string;
  link_type: 'free' | 'paid';
  price?: number;
  currency: string;
  source?: string; // guest post, directory, resource page, etc.
  acquisition_method?: string; // outreach, haro, partnership, etc.
  notes?: string;
  user_id: string;
  last_checked?: string; // 表示上一次 dr_score 和 traffic 的更新时间
  created_at: string;
  updated_at: string;
}

// Helper interface for link resources with domain stats
export interface LinkResourceWithStats extends LinkResource {
  domain: string;
  dr_score?: number;
  traffic: number;
  is_indexed: boolean;
  status?: string; // For backward compatibility with components
}

export interface ProjectInfo {
  favicon?: string; // 网站图标URL
  introduction?: string; // 项目介绍
  sitemaps?: string[]; // sitemap.xml链接数组
  robotsTxt?: string; // robots.txt链接
  domainInfo?: {
    registrar?: string;
    createdDate?: string;
    expiryDate?: string;
    status?: string[];
    nameServers?: string[];
  };
}

export interface Project {
  id: string;
  name: string;
  /** 项目的网站域名，使用 normalizeUrl 函数的 normalized 结果存储，确保去除www前缀并规范化 */
  domain: string;
  description?: string;
  category?: string; // 用户自定义分类，如：游戏、工具、博客等
  total_links: number; // 总记录的外链数量，包含用户已提交但是可能没有收录的外链
  indexed_links: number; // 总外链数量，从搜索引擎索引到的外链
  info?: ProjectInfo; // 项目详细信息：包含网站图标、项目介绍、sitemap链接、robots.txt链接、域名注册信息等
  is_archived?: boolean; // 是否存档
  archived_at?: string; // 存档时间
  user_id: string;
  created_at: string;
  updated_at: string;
}

// Helper interface for projects with domain stats from all_links table
export interface ProjectWithStats extends Project {
  dr_score?: number;
  traffic: number;
  is_indexed: boolean;
}

// 项目分类统计信息
export interface ProjectCategoryStats {
  category: string;
  project_count: number;
}

// 项目分类相关的实用类型
export interface ProjectCategory {
  name: string;
  icon: string;
  color: string;
}

// Replaces LinkStats - central domain statistics table
// 存储所有链接的最新统计数据，无论链接来源是 projects、link_resources 还是 discovered_links
export interface AllLinks {
  id: string;
  /** 规范化的域名（去除www等），从各种URL中提取的统一域名标识符 */
  domain: string;
  dr_score?: number;
  traffic: number;
  backlink_count: number;
  is_indexed: boolean;
  last_updated: string;
}

// Historical tracking of domain statistics
export interface AllLinksHistory {
  id: string;
  domain: string;
  dr_score?: number;
  traffic: number;
  backlink_count: number;
  is_indexed: boolean;
  checked_at: string;
}

export interface LinkImportData {
  url: string;
  title: string;
  link_type?: 'free' | 'paid';
  price?: number;
  source?: string;
  acquisition_method?: string;
  notes?: string;
}

export interface DiscoveredLink {
  id: string;
  /** 外部网站的链接URL，表示指向本项目的外链来源网站，使用 normalizeUrl 函数归一化存储，可能包含路径 */
  url: string;
  title: string;
  anchor_text: string;
  link_type: 'dofollow' | 'nofollow';
  discovered_at: string;
  /** 本网站在其他网站被收录的具体链接，表示项目网站的哪个页面被外部网站收录，可能包含路径用于标识具体子页面 */
  source_url: string;
  is_active: boolean;
  status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED';
  project_id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  // Traffic contribution data from analytics platforms
  referral_traffic?: number; // Traffic from this specific link source
  analytics_source?: 'plausible' | 'google-analytics' | 'umami' | 'manual'; // Which analytics platform provided the data
  traffic_period?: string; // Time period for traffic data (e.g., 'last_30_days', 'last_7_days')
  last_traffic_update?: string; // When traffic data was last updated
}

// Helper interface for discovered links with domain stats
export interface DiscoveredLinkWithStats extends DiscoveredLink {
  domain: string;
  dr_score?: number;
  traffic: number; // Total domain traffic (website's own traffic)
  is_indexed: boolean;
  // Additional analytics data
  traffic_contribution_percentage?: number; // Percentage of total traffic from this referral
}

// Public link resources - public platforms where users can submit backlinks
export interface PublicLinkResource {
  id: string;
  /** 规范化的域名，与 all_links 表关联获取DR和流量数据 */
  domain: string;
  title: string;
  website_url: string;
  submission_method: string;
  submission_url?: string;
  contact_email?: string;
  is_paid: boolean;
  price_range?: string;
  currency: string;
  category?: string;
  description?: string;
  requirements?: string;
  response_time?: string;
  success_rate?: number;
  last_verified?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Helper interface for public link resources with domain stats
export interface PublicLinkResourceWithStats extends PublicLinkResource {
  dr_score?: number;
  traffic: number;
  backlink_count: number;
  is_indexed: boolean;
}

// Utility types for common operations
export interface DomainStats {
  domain: string;
  dr_score?: number;
  traffic: number;
  is_indexed: boolean;
}

export interface LinkImportDataNew {
  url: string;
  title: string;
  link_type?: 'free' | 'paid';
  price?: number;
  source?: string;
  acquisition_method?: string;
  notes?: string;
}

// Backward compatibility aliases (deprecated - use new names)
/** @deprecated Use LinkResource instead */
export type Link = LinkResourceWithStats;

/** @deprecated Use AllLinks instead */
export type LinkStats = AllLinksHistory;

/** @deprecated ProjectStats table has been removed - use ProjectWithStats instead */
export interface ProjectStats {
  id: string;
  project_id: string;
  dr_score?: number;
  traffic: number;
  backlink_count: number;
  total_links: number;
  indexed_links: number;
  checked_at: string;
} 