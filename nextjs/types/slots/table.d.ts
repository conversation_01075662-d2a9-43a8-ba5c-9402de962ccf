import { TableColumn } from "@/types/blocks/table";
import { Slot } from "@/types/slots/base";
import { Button as ButtonType } from "@/types/blocks/base";

export interface FilterGroup {
  title?: string;
  items: (ButtonType & { 
    onClick?: () => void;
    formAction?: (formData: FormData) => Promise<void> | void;
    formData?: Record<string, string | number | null | undefined>;
  })[];
}

export interface PaginationConfig {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => Promise<void> | void;
}

export interface Table extends Slot {
  columns?: TableColumn[];
  empty_message?: string;
  filters?: FilterGroup[];
  pagination?: PaginationConfig;
}
