export interface User {
  id?: number;
  uuid?: string;
  email: string;
  created_at?: string;
  nickname: string;
  avatar_url: string;
  locale?: string;
  signin_type?: string;
  signin_ip?: string;
  signin_provider?: string;
  signin_openid?: string;
  credits?: UserCredits;
  invited_by?: string;
  invite_code?: string;
  invites_count?: number;
  api_rate_limit?: number; // Maximum number of API requests per hour
  api_requests_current?: number; // Number of API requests made in current period
  api_rate_reset?: string; // Timestamp when the rate limit resets
}

export interface UserCredits {
  one_time_credits?: number;
  monthly_credits?: number;
  total_credits?: number;
  used_credits?: number;
  left_credits: number;
  free_credits?: number;
  is_recharged?: boolean;
  is_pro?: boolean;
}
