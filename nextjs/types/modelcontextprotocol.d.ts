/**
 * Type definitions for Model Context Protocol SDK
 */

declare module '@modelcontextprotocol/sdk/client/index.js' {
  export class Client {
    constructor(options: { name: string });
    connect(transport: any): Promise<void>;
    close(): Promise<void>;
    listTools(): Promise<{ tools: Tool[] }>;
    callTool(options: { name: string; arguments: Record<string, any> }): Promise<ToolResult>;
  }

  export interface Tool {
    name: string;
    description?: string;
    inputSchema?: any;
  }

  export interface ToolResult {
    content: Array<ContentItem>;
    isError?: boolean;
  }

  export interface ContentItem {
    type: string;
    [key: string]: any;
  }
}

declare module '@modelcontextprotocol/sdk/client/streamable-http.js' {
  export class StreamableHttpClientTransport {
    constructor(baseUrl: string);
    onclose: (() => void) | null;
    onerror: ((error: Error) => void) | null;
  }
}

declare module '@modelcontextprotocol/sdk/server/index.js' {
  export class Server {
    constructor(info: { name: string; version: string }, options: { capabilities: any });
    connect(transport: any): Promise<void>;
    close(): Promise<void>;
    onerror: ((error: Error) => void) | null;
  }
}

declare module '@modelcontextprotocol/sdk/types.js' {
  export const CallToolRequestSchema: any;
  export const ListToolsRequestSchema: any;
  
  export enum ErrorCode {
    ParseError = -32700,
    InvalidRequest = -32600,
    MethodNotFound = -32601,
    InvalidParams = -32602,
    InternalError = -32603,
  }
  
  export class ItemError extends Error {
    constructor(code: ErrorCode, message: string);
    code: ErrorCode;
  }
} 