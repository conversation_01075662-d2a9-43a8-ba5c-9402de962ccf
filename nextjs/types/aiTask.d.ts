// Define the possible statuses for an AI task
export type AiTaskStatus = 'PENDING' | 'PROCESSING' | 'SUCCEED' | 'FAILED';

// Define the structure for AI Task output options (flexible JSON)
export type AiTaskOutputOptions = Record<string, any>;

// Define the main AiTask type based on the database schema
export interface AiTask {
  id: number; // SERIAL PRIMARY KEY
  order_no: string; // VARCHAR(255) UNIQUE NOT NULL
  user_uuid: string; // VARCHAR(64) NOT NULL
  product_name: string; // VARCHAR(20) NOT NULL
  credit_cost: number; // INTEGER NOT NULL DEFAULT 0
  input_file_path: string; // VARCHAR(200) NOT NULL
  output_image_path?: string | null; // VARCHAR(200)
  output_text?: string | null; // TEXT
  output_options?: AiTaskOutputOptions | null; // JSONB DEFAULT '{}'::JSONB
  orderstatus: AiTaskStatus; // VARCHAR(20) NOT NULL
  fail_reason?: string | null; // VARCHAR(500)
  create_time: Date; // TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
  update_time?: Date | null; // TIMESTAMPTZ
  cost_time: number; // INTEGER NOT NULL DEFAULT 0
  callback_url?: string | null; // VARCHAR(200)
}

// Type for creating a new AI Task (omitting fields generated by DB or updated later)
export type CreateAiTaskInput = Omit<AiTask, 'id' | 'create_time' | 'update_time' | 'cost_time' | 'orderstatus' | 'fail_reason' | 'output_image_path' | 'output_text'> & {
  // Optional fields during creation can be added if needed
};

// Type for updating an AI Task (making fields optional for partial updates)
export type UpdateAiTaskInput = Partial<Pick<AiTask,
  | 'output_image_path'
  | 'output_text'
  | 'output_options'
  | 'orderstatus'
  | 'fail_reason'
  | 'cost_time'
>>;

// Type for the callback payload (typically includes status and output)
export interface AiTaskCallbackPayload {
  order_no: string;
  orderstatus: AiTaskStatus;
  output_image_path?: string;
  output_text?: string;
  output_options?: AiTaskOutputOptions;
  fail_reason?: string;
  cost_time?: number; // Optional: Worker might calculate and send this
}