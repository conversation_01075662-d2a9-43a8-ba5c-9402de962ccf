import { BaseProps } from './base';

export interface Card {
  uuid: string;
  name: string;
  brief: string;
  avatar_url: string;
  author_name: string;
  updated_at: string;
  is_recommended: boolean;
  is_official: boolean;
  clicks: number;
  tags: string[];
  metadata: {
    forks?: number;
    stars?: number;
    watchers?: number;
  };
}

export interface GallerySection {
  title?: string;
  description?: string;
  showFilters?: boolean;
}

export interface GalleryProps extends BaseProps {
  section?: GallerySection;
  searchParams?: { 
    page?: string; 
    limit?: string; 
    tags?: string | string[]; 
    official?: string; 
    recommended?: string;
    query?: string;
  };
} 