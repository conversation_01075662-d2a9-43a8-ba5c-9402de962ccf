// nextjs/types/itemTools.d.ts

/**
 * Represents multi-language content for tool descriptions.
 * Key is the language code (e.g., 'en', 'zh', 'fr'), value is the translated content.
 */
export interface I18nContent {
  [locale: string]: string;
}

export interface ToolParameter {
  name: string;
  // Single language description (legacy support)
  description?: string;
  // Multi-language descriptions
  // Example: { "en": "English description", "zh": "中文描述" }
  i18n_description?: I18nContent;
  type: string;
  required?: boolean;
}

export interface Tool {
  name: string;
  // Single language description (legacy support)
  description: string;
  // Multi-language descriptions
  // Example: { "en": "English description", "zh": "中文描述" }
  i18n_description?: I18nContent;
  parameters?: ToolParameter[];
  updated_at?: string; // ISO date string
}

export interface ToolUsage {
  sse?: {
    code_example: string;
    description?: string;
    // Multi-language descriptions for SSE usage
    i18n_description?: I18nContent;
  };
  stdio?: {
    code_example: string;
    description?: string;
    // Multi-language descriptions for stdio usage
    i18n_description?: I18nContent;
  };
}

export interface ItemTool {
  uuid: string;
  allow_public: boolean;
  type: 'sse' | 'stdio' | 'both'; // Type of Item: SSE, stdio, or both
  tools: Tool[];
  usage?: ToolUsage;
  created_at?: string;
  updated_at?: string;
} 