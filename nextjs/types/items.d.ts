// CREATE TABLE if NOT EXISTS items.items (
//     id SERIAL PRIMARY KEY,
//     uuid VARCHAR(255) UNIQUE NOT NULL,
//     name VARCHAR(255) NOT NULL,
//     brief VARCHAR(255), --- brief description from github README.md
//     item_avatar_url VARCHAR(255),
//     user_avatar_url VARCHAR(512),
//     website_url VARCHAR(255),
//     author_name VARCHAR(255),
//     created_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
//     updated_at timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
//     is_recommended BOOLEAN NOT NULL DEFAULT FALSE,
//     is_official BOOLEAN NOT NULL DEFAULT FALSE,
//     clicks INT NOT NULL DEFAULT 0,
//     allow_public BOOLEAN NOT NULL DEFAULT FALSE,
//     tags VARCHAR(255)[] DEFAULT '{}'::VARCHAR[],
//     metadata JSONB DEFAULT '{}'::JSONB, -- metadata from github: forks, stars, recent updates time, watchers
// );

export interface Items {
    id: number;
    uuid: string;
    name: string;
    brief: string;
    item_avatar_url: string;
    user_avatar_url: string;
    website_url: string;
    author_name: string;
    created_at: string;
    updated_at: string;
    is_recommended: boolean;
    is_official: boolean;
    clicks: number;
    allow_public: boolean;
    tags: string[];
    metadata: Record<string, any>;
    item_location?: string;
}

// CREATE TABLE if NOT EXISTS items.item_localizations (
//     id SERIAL PRIMARY KEY,
//     item_uuid INT NOT NULL REFERENCES items(uuid) ON DELETE CASCADE,
//     language_code VARCHAR(50) NOT NULL,
//     brief VARCHAR(255), --- brief description from github README.md
//     detail TEXT, -- markdown format from github README.md
//     processinfo TEXT, -- processinfo By AI
//     UNIQUE (item_id, language_code)
// );
export interface ItemLocalization {
    id: number;
    item_uuid: string;
    language_code: string;
    brief: string;
    detail: string;
    processinfo: string; // processinfo By AI, only contain 3 h2 headers, and the content of the h2 headers
}

// CREATE TABLE if NOT EXISTS items.submissions (
//     id SERIAL PRIMARY KEY,
//     name VARCHAR(255) NOT NULL,
//     author_name VARCHAR(255) NOT NULL,
//     website_url VARCHAR(512) NOT NULL,
//     item_avatar_url VARCHAR(512),
//     user_avatar_url VARCHAR(512),
//     email VARCHAR(255),
//     subscribe_newsletter BOOLEAN DEFAULT TRUE,
//     created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
//     processed_at timestamptz,
//     approved_at timestamptz,
//     rejected_at timestamptz,
//     status VARCHAR(50) DEFAULT 'pending',
//     detail TEXT, -- markdown format from github README.md
//     preProcessInfo JSONB DEFAULT '{}'::JSONB, -- preProcessInfo By AI, including berif, detail, tags, metadata
// );
export interface Submission {
    id: number;
    name: string;
    author_name: string;
    website_url: string;
    item_avatar_url: string;
    user_avatar_url: string;
    email: string;
    subscribe_newsletter: boolean;
    created_at: string;
    processed_at: string;
    approved_at: string;
    rejected_at: string;
    status: string;
    detail: string;
    preprocessinfo: Record<string, any>;
}