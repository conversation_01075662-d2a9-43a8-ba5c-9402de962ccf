// Simplified import types for direct public_link_resources table usage

// Client-side import state management
export interface ImportState {
  total_records: number;
  processed_records: number;
  successful_imports: number;
  failed_imports: number;
  status: 'idle' | 'parsing' | 'validating' | 'importing' | 'completed' | 'error';
  errors: ImportError[];
  preview_data: PublicLinkResourceData[];
}

// Import error tracking
export interface ImportError {
  row_number: number;
  field?: string;
  error_message: string;
  raw_data: Record<string, any>;
  severity: 'error' | 'warning';
}

// Data structure matching public_link_resources table
export interface PublicLinkResourceData {
  domain: string;
  title: string;
  website_url: string;
  submission_method: string;
  submission_url?: string;
  contact_email?: string;
  is_paid: boolean;
  price_range?: string;
  currency: string;
  category?: string;
  description?: string;
  requirements?: string;
  response_time?: string;
  success_rate?: number;
  // Client-side import fields
  _row_number: number;
  _selected: boolean;
  _validation_status?: 'valid' | 'warning' | 'error';
  _validation_errors?: string[];
}

// Import configuration options
export interface ImportConfiguration {
  mode: 'create_only' | 'update_existing' | 'upsert';
  skip_duplicates: boolean;
  validate_urls: boolean;
  validate_emails: boolean;
  selected_rows?: number[];
}

// Import result summary
export interface ImportResult {
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  skipped_duplicates: number;
  errors: ImportError[];
  duration_ms: number;
  completed_at: string;
  session_id?: string;
}

// Bulk insert request
export interface BulkInsertRequest {
  data: PublicLinkResourceData[];
  configuration: ImportConfiguration;
}

// CSV template field definitions
export const CSV_TEMPLATE_FIELDS = [
  { key: 'domain', label: 'Domain', required: true, example: 'example.com' },
  { key: 'title', label: 'Title', required: true, example: 'Example Directory' },
  { key: 'website_url', label: 'Website URL', required: true, example: 'https://example.com' },
  { key: 'submission_method', label: 'Submission Method', required: true, example: 'online_form' },
  { key: 'submission_url', label: 'Submission URL', required: false, example: 'https://example.com/submit' },
  { key: 'contact_email', label: 'Contact Email', required: false, example: '<EMAIL>' },
  { key: 'is_paid', label: 'Is Paid', required: false, example: 'false' },
  { key: 'price_range', label: 'Price Range', required: false, example: '$50-100' },
  { key: 'currency', label: 'Currency', required: false, example: 'USD' },
  { key: 'category', label: 'Category', required: false, example: 'Directory' },
  { key: 'description', label: 'Description', required: false, example: 'Sample directory description' },
  { key: 'requirements', label: 'Requirements', required: false, example: 'Basic requirements' },
  { key: 'response_time', label: 'Response Time', required: false, example: '1-2 days' },
  { key: 'success_rate', label: 'Success Rate', required: false, example: '0.8' }
] as const;

// Validation rules
export const VALIDATION_RULES = {
  domain: {
    pattern: /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/,
    maxLength: 255
  },
  title: {
    minLength: 3,
    maxLength: 255
  },
  website_url: {
    pattern: /^https?:\/\/.+/,
    maxLength: 500
  },
  submission_url: {
    pattern: /^https?:\/\/.+/,
    maxLength: 500
  },
  contact_email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    maxLength: 255
  },
  currency: {
    enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY'],
    default: 'USD'
  },
  success_rate: {
    min: 0,
    max: 1
  }
} as const;