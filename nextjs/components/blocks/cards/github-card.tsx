import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, GitFork, Eye, Clock } from "lucide-react";

export default function GithubCard({ metadata, formattedRelativeDates }: { metadata: any, formattedRelativeDates: any }) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">Repository Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
                <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>Stars: <Badge variant="outline">{metadata.stars || '-'}</Badge></span>
                </div>

                <div className="flex items-center gap-2">
                <GitFork className="w-4 h-4 text-blue-500" />
                <span>Forks: <Badge variant="outline">{metadata.forks || '-'}</Badge></span>
                </div>

                <div className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-green-500" />
                <span>Watchers: <Badge variant="outline">{metadata.watchers || '-'}</Badge></span>
                </div>

                <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-purple-500" />
                <span>Last Updated: {metadata.updated_at && formattedRelativeDates[metadata.updated_at] || '-'}</span>
                </div>
                </CardContent>
            </Card>
    );
}