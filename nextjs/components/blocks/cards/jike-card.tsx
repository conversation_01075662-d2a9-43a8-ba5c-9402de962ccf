"use client";

import { Suspense } from "react";

interface JikeIconProps {
  className?: string;
  [key: string]: any;
}

const JikeIcon = ({ className, ...props }: JikeIconProps) => (
  <svg 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <path 
      d="M11.9989 2C6.47988 2 2 6.48 2 12C2 17.52 6.47988 22 11.9989 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 11.9989 2ZM16.5 8.5H13.9191L11.9989 4L10.0809 8.5H7.5L10.0809 13L11.9989 8.5L13.9191 13L16.5 8.5ZM16.5 15.5H7.5V17.5H16.5V15.5Z" 
      fill="#FFD100"
    />
  </svg>
);

export const truncate = (str: string | null, length: number) => {
  if (!str || str.length <= length) return str;
  return `${str.slice(0, length - 3)}...`;
};

const Skeleton = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`rounded-md bg-primary/10 ${className}`} {...props} />
  );
};

export const JikeSkeleton = ({
  className,
  ...props
}: {
  className?: string;
  [key: string]: any;
}) => (
  <div
    className={`flex size-full max-h-max min-w-72 flex-col gap-2 rounded-lg border p-4 ${className}`}
    {...props}
  >
    <div className="flex flex-row gap-2">
      <Skeleton className="size-10 shrink-0 rounded-full" />
      <Skeleton className="h-10 w-full" />
    </div>
    <Skeleton className="h-20 w-full" />
  </div>
);

export const JikeNotFound = ({
  className,
  ...props
}: {
  className?: string;
  [key: string]: any;
}) => (
  <div
    className={`flex size-full flex-col items-center justify-center gap-2 rounded-lg border p-4 ${className}`}
    {...props}
  >
    <h3>Jike post not found</h3>
  </div>
);

// Client Side Jike Card Component
export const JikeCard = ({
  id,
  className,
  content,
  author,
  authorAvatar,
  date,
  imageUrls = [],
  likeCount,
  ...props
}: {
  id: string;
  className?: string;
  content?: string;
  author?: string;
  authorAvatar?: string;
  date?: string;
  imageUrls?: string[];
  likeCount?: number;
  [key: string]: any;
}) => {
  return (
    <Suspense fallback={<JikeSkeleton />}>
      <div
        className={`relative flex size-full max-w-lg flex-col gap-2 overflow-hidden rounded-lg border p-4 backdrop-blur-md shadow-sm bg-white ${className}`}
        {...props}
      >
        <div className="flex items-center gap-2 mb-3">
          {authorAvatar && (
            <img 
              src={authorAvatar} 
              alt={author || "Jike user"} 
              className="size-10 rounded-full"
              onError={(e) => {
                (e.target as HTMLImageElement).src = "https://jike-assets.jk-cdn.com/system/user_avatar/default.png";
              }}
            />
          )}
          <div>
            <div className="flex items-center gap-1">
              <span className="font-semibold">{author || "Jike User"}</span>
            </div>
            <div className="text-xs text-gray-500">
              {date ? new Date(date).toLocaleDateString() : ""}
            </div>
          </div>
          <JikeIcon className="w-5 h-5 text-yellow-400 ml-auto" />
        </div>
        <div className="text-gray-800">
          {content && content.split('\n').map((paragraph, idx) => (
            <p key={idx} className="mb-2">{paragraph}</p>
          ))}
        </div>
        
        {imageUrls && imageUrls.length > 0 && (
          <div className="mt-3 overflow-hidden">
            {imageUrls.length === 1 ? (
              <img 
                src={imageUrls[0]} 
                alt="Jike content" 
                className="w-full rounded-lg object-cover max-h-80"
              />
            ) : (
              <div className="grid gap-2 grid-cols-2">
                {imageUrls.slice(0, 4).map((img, idx) => (
                  <img 
                    key={idx} 
                    src={img} 
                    alt={`Jike content ${idx + 1}`} 
                    className="w-full h-40 rounded-lg object-cover"
                  />
                ))}
                {imageUrls.length > 4 && (
                  <div className="relative">
                    <span className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white font-bold rounded-lg">
                      +{imageUrls.length - 4} more
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        
        {likeCount !== undefined && (
          <div className="flex items-center gap-1 mt-3 text-gray-500 text-sm">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
            </svg>
            <span>{likeCount || 0}</span>
          </div>
        )}
        
        <div className="text-sm text-yellow-500 mt-2">
          <a 
            href={`https://m.okjike.com/originalPosts/${id}`} 
            target="_blank" 
            rel="noopener noreferrer"
            className="hover:underline"
          >
            View on Jike
          </a>
        </div>
      </div>
    </Suspense>
  );
}; 