"use client";

import { Suspense } from "react";

interface TwitterIconProps {
  className?: string;
  [key: string]: any;
}

const Twitter = ({ className, ...props }: TwitterIconProps) => (
  <svg
    stroke="currentColor"
    fill="currentColor"
    strokeWidth="0"
    viewBox="0 0 24 24"
    height="1em"
    width="1em"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <g>
      <path fill="none" d="M0 0h24v24H0z"></path>
      <path d="M22.162 5.656a8.384 8.384 0 0 1-2.402.658A4.196 4.196 0 0 0 21.6 4c-.82.488-1.719.83-2.656 1.015a4.182 4.182 0 0 0-7.126 3.814 11.874 11.874 0 0 1-8.62-4.37 4.168 4.168 0 0 0-.566 2.103c0 1.45.738 2.731 1.86 3.481a4.168 4.168 0 0 1-1.894-.523v.052a4.185 4.185 0 0 0 3.355 4.101 4.21 4.21 0 0 1-1.89.072A4.185 4.185 0 0 0 7.97 16.65a8.394 8.394 0 0 1-6.191 1.732 11.83 11.83 0 0 0 6.41 1.88c7.693 0 11.9-6.373 11.9-11.9 0-.18-.005-.362-.013-.54a8.496 8.496 0 0 0 2.087-2.165z"></path>
    </g>
  </svg>
);

const Verified = ({ className, ...props }: TwitterIconProps) => (
  <svg
    aria-label="Verified Account"
    viewBox="0 0 24 24"
    className={className}
    {...props}
  >
    <g fill="currentColor">
      <path d="M22.5 12.5c0-1.58-.875-2.95-2.148-3.6.154-.435.238-.905.238-1.4 0-2.21-1.71-3.998-3.818-3.998-.47 0-.92.084-1.336.25C14.818 2.415 13.51 1.5 12 1.5s-2.816.917-3.437 2.25c-.415-.165-.866-.25-1.336-.25-2.11 0-3.818 1.79-3.818 4 0 .494.083.964.237 1.4-1.272.65-2.147 2.018-2.147 3.6 0 1.495.782 2.798 1.942 3.486-.02.17-.032.34-.032.514 0 2.21 1.708 4 3.818 4 .47 0 .92-.086 1.335-.25.62 1.334 1.926 2.25 3.437 2.25 1.512 0 2.818-.916 3.437-2.25.415.163.865.248 1.336.248 2.11 0 3.818-1.79 3.818-4 0-.174-.012-.344-.033-.513 1.158-.687 1.943-1.99 1.943-3.484zm-6.616-3.334l-4.334 6.5c-.145.217-.382.334-.625.334-.143 0-.288-.04-.416-.126l-.115-.094-2.415-2.415c-.293-.293-.293-.768 0-1.06s.768-.294 1.06 0l1.77 1.767 3.825-5.74c.23-.345.696-.436 1.04-.207.346.23.44.696.21 1.04z" />
    </g>
  </svg>
);

export const truncate = (str: string | null, length: number) => {
  if (!str || str.length <= length) return str;
  return `${str.slice(0, length - 3)}...`;
};

const Skeleton = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`rounded-md bg-primary/10 ${className}`} {...props} />
  );
};

export const TweetSkeleton = ({
  className,
  ...props
}: {
  className?: string;
  [key: string]: any;
}) => (
  <div
    className={`flex size-full max-h-max min-w-72 flex-col gap-2 rounded-lg border p-4 ${className}`}
    {...props}
  >
    <div className="flex flex-row gap-2">
      <Skeleton className="size-10 shrink-0 rounded-full" />
      <Skeleton className="h-10 w-full" />
    </div>
    <Skeleton className="h-20 w-full" />
  </div>
);

export const TweetNotFound = ({
  className,
  ...props
}: {
  className?: string;
  [key: string]: any;
}) => (
  <div
    className={`flex size-full flex-col items-center justify-center gap-2 rounded-lg border p-4 ${className}`}
    {...props}
  >
    <h3>Tweet not found</h3>
  </div>
);

// Client Side Twitter Card Component
export const TwitterCard = ({
  id,
  className,
  content,
  author,
  authorAvatar,
  date,
  imageUrls = [],
  videoUrls = [],
  ...props
}: {
  id: string;
  className?: string;
  content?: string;
  author?: string;
  authorAvatar?: string;
  date?: string;
  imageUrls?: string[];
  videoUrls?: string[];
  [key: string]: any;
}) => {
  // If no custom content is provided, use Twitter's embedded widget
  const useEmbedWidget = !content && imageUrls.length === 0 && videoUrls.length === 0;

  return (
    <Suspense fallback={<TweetSkeleton />}>
      <div
        className={`relative flex size-full max-w-lg flex-col gap-2 overflow-hidden rounded-lg border p-4 backdrop-blur-md shadow-sm bg-white ${className}`}
        {...props}
      >
        {useEmbedWidget ? (
          <div
            dangerouslySetInnerHTML={{
              __html: `<blockquote class="twitter-tweet"><a href="https://twitter.com/anyuser/status/${id}"></a></blockquote>
                     <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>`,
            }}
          />
        ) : (
          <>
            <div className="flex items-center gap-2 mb-3">
              {authorAvatar && (
                <img 
                  src={authorAvatar} 
                  alt={author || "Tweet author"} 
                  className="size-10 rounded-full"
                />
              )}
              <div>
                <div className="flex items-center gap-1">
                  <span className="font-semibold">{author || "Twitter User"}</span>
                  <Verified className="w-4 h-4 text-blue-500" />
                </div>
                <div className="text-xs text-gray-500">
                  {date ? new Date(date).toLocaleDateString() : ""}
                </div>
              </div>
              <Twitter className="w-5 h-5 text-blue-400 ml-auto" />
            </div>
            <div className="text-gray-800">
              {content && content.split('\n').map((paragraph, idx) => (
                <p key={idx} className="mb-2">{paragraph}</p>
              ))}
            </div>
            
            {/* Display images if available */}
            {imageUrls && imageUrls.length > 0 && (
              <div className="mt-3 overflow-hidden">
                {imageUrls.length === 1 ? (
                  <img 
                    src={imageUrls[0]} 
                    alt="Tweet media" 
                    className="w-full rounded-lg object-cover max-h-80"
                  />
                ) : (
                  <div className={`grid gap-2 ${imageUrls.length > 1 ? 'grid-cols-2' : 'grid-cols-1'}`}>
                    {imageUrls.slice(0, 4).map((img, idx) => (
                      <img 
                        key={idx} 
                        src={img} 
                        alt={`Tweet media ${idx + 1}`} 
                        className="w-full h-40 rounded-lg object-cover"
                      />
                    ))}
                    {imageUrls.length > 4 && (
                      <div className="relative">
                        <span className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white font-bold rounded-lg">
                          +{imageUrls.length - 4} more
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
            
            {/* Display videos if available */}
            {videoUrls && videoUrls.length > 0 && (
              <div className="mt-3">
                <video 
                  src={videoUrls[0]} 
                  controls 
                  className="w-full rounded-lg"
                  poster={imageUrls && imageUrls.length > 0 ? imageUrls[0] : undefined}
                  preload="metadata"
                  playsInline
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            )}
            
            <div className="text-sm text-blue-500 mt-2">
              <a 
                href={`https://twitter.com/anyuser/status/${id}`} 
                target="_blank" 
                rel="noopener noreferrer"
                className="hover:underline"
              >
                View on Twitter
              </a>
            </div>
          </>
        )}
      </div>
    </Suspense>
  );
}; 