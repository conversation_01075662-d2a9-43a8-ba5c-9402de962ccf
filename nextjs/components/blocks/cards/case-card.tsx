import Image from "next/image";
import Link from "next/link";
import { UserCase } from "@/types/usercase";
import { TwitterIcon, YoutubeIcon } from "lucide-react";
import { formatDate } from "@/lib/utils";

interface CaseCardProps {
  userCase: UserCase;
  locale?: string;
}

const JikeIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 176 176"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    className="h-5 w-5"
  >
    <defs>
      <path id="prefix__a" d="M0 0h176v176H0z" />
      <path id="prefix__c" d="M0 0h45v117H0z" />
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="prefix__d">
        <stop stopColor="#3EECF1" offset="0%" />
        <stop stopColor="#39C5F5" offset="100%" />
      </linearGradient>
    </defs>
    <g fill="none" fillRule="evenodd">
      <mask id="prefix__b" fill="#fff">
        <use xlinkHref="#prefix__a" />
      </mask>
      <path
        fill="#FFE411"
        d="M135.959 176H39.414C17.646 176 0 158.354 0 136.585V40.041C0 18.272 17.646.627 39.414.627h96.545c21.768 0 39.414 17.645 39.414 39.414v96.544c0 21.77-17.646 39.415-39.414 39.415"
        mask="url(#prefix__b)"
      />
      <path
        fill="#FEFEFE"
        d="M68.429 143.92l-12.895-18.242a1.628 1.628 0 01.39-2.267l8.526-6.025c8.822-6.238 14.088-14.889 14.088-27.212V34.637c0-.899.728-1.625 1.626-1.625h22.757v57.162c0 22.433-6.247 33.624-24.638 46.78l-9.854 6.965z"
      />
      <g transform="translate(67 33)">
        <mask id="prefix__e" fill="#fff">
          <use xlinkHref="#prefix__c" />
        </mask>
        <path
          fill="url(#prefix__d)"
          d="M35.247.012V56.22c0 22.434-6.248 33.624-24.638 46.779l-9.855 6.966 4.691 6.637a1.627 1.627 0 002.266.389l8.571-6.059c10.907-7.801 17.738-14.972 22.149-23.258C42.976 79.15 45 69.447 45 56.22V1.637C45 .74 44.271.012 43.374.012h-8.127z"
          mask="url(#prefix__e)"
        />
      </g>
    </g>
  </svg>
);

const CaseCard = ({ userCase, locale = "en" }: CaseCardProps) => {
  const { type, title, author_name, author_avatar_url, created_at, uuid } = userCase;

  const getTypeIcon = () => {
    switch (type) {
      case "twitter":
      case "x":
        return <TwitterIcon className="h-5 w-5 text-[#1DA1F2]" />;
      case "youtube":
        return <YoutubeIcon className="h-5 w-5 text-[#FF0000]" />;
      case "jike":
        return <JikeIcon />;
      default:
        return null;
    }
  };

  return (
    <Link
      href={`/${locale}/cases/${uuid}`}
      className="group flex h-full flex-col overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-950"
    >
      <div className="flex items-center gap-3 border-b border-gray-100 p-4 dark:border-gray-800">
        {author_avatar_url ? (
          <Image
            src={author_avatar_url}
            alt={author_name || "Author"}
            width={40}
            height={40}
            className="h-10 w-10 rounded-full object-cover"
          />
        ) : (
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
            {getTypeIcon()}
          </div>
        )}
        <div className="flex flex-col">
          <h3 className="font-medium text-gray-900 dark:text-gray-100">
            {author_name || "User Case"}
          </h3>
          <div className="flex items-center gap-1 text-xs text-gray-500">
            {getTypeIcon()}
            <span>{formatDate(created_at)}</span>
          </div>
        </div>
      </div>
      <div className="flex flex-1 flex-col p-4">
        <h4 className="mb-2 line-clamp-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
          {title || "User Case"}
        </h4>
        {userCase.details?.excerpt && (
          <p className="line-clamp-3 text-sm text-gray-600 dark:text-gray-400">
            {userCase.details.excerpt}
          </p>
        )}
      </div>
    </Link>
  );
};

export default CaseCard; 