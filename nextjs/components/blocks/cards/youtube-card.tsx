"use client";

import { Suspense } from "react";
import { YoutubeIcon } from "lucide-react";

const Skeleton = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`rounded-md bg-primary/10 ${className}`} {...props} />
  );
};

export const YoutubeSkeleton = ({
  className,
  ...props
}: {
  className?: string;
  [key: string]: any;
}) => (
  <div
    className={`flex size-full max-h-max min-w-72 flex-col gap-2 rounded-lg border p-4 ${className}`}
    {...props}
  >
    <div className="flex flex-row gap-2">
      <Skeleton className="size-10 shrink-0 rounded-full" />
      <Skeleton className="h-10 w-full" />
    </div>
    <Skeleton className="h-40 w-full" />
  </div>
);

export const YoutubeNotFound = ({
  className,
  ...props
}: {
  className?: string;
  [key: string]: any;
}) => (
  <div
    className={`flex size-full flex-col items-center justify-center gap-2 rounded-lg border p-4 ${className}`}
    {...props}
  >
    <h3>YouTube video not found</h3>
  </div>
);

export const YoutubeCard = ({
  id,
  title,
  aiSummary,
  className,
  ...props
}: {
  id: string;
  title?: string;
  aiSummary?: string;
  className?: string;
  [key: string]: any;
}) => {
  return (
    <Suspense fallback={<YoutubeSkeleton />}>
      <div
        className={`relative flex size-full max-w-lg flex-col gap-4 overflow-hidden rounded-lg border p-4 backdrop-blur-md shadow-sm ${className}`}
        {...props}
      >
        <div className="flex items-center gap-2">
          <YoutubeIcon className="h-6 w-6 text-red-600" />
          {title && (
            <h3 className="font-medium text-gray-900 dark:text-gray-100">
              {title}
            </h3>
          )}
        </div>
        
        <div className="aspect-video w-full overflow-hidden rounded-md">
          <iframe
            src={`https://www.youtube.com/embed/${id}`}
            className="h-full w-full border-none"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        </div>
        
        {aiSummary && (
          <div className="mt-2">
            <h4 className="mb-1 text-sm font-medium text-gray-900 dark:text-gray-100">
              AI Summary
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {aiSummary}
            </p>
          </div>
        )}
      </div>
    </Suspense>
  );
}; 