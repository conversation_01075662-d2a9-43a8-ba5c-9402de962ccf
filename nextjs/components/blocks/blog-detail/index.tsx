"use client";

import { <PERSON><PERSON>, AvatarImage } from "@/components/ui/avatar";

import Crumb from "./crumb";
import Markdown from "@/components/markdown";
import { Post } from "@/types/post";
import moment from "moment";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";

export default function BlogDetail({ post }: { post: Post }) {
  // Calculate estimated reading time
  const readingTime = Math.ceil(((post.content?.length || 0) / 1000) * 0.3);

  // Process avatar URL - handle both local paths and full URLs
  const avatarUrl = post.author_avatar_url ?
    (post.author_avatar_url.startsWith("http")) ?
      post.author_avatar_url :
      `/${post.author_avatar_url}`
    : null;

  return (
    <section className="py-16">
      <div className="md:max-w-10xl mx-auto px-4">
        <Crumb post={post} />
        <h1 className="mb-7 mt-9 max-w-3xl text-2xl font-bold md:mb-10 md:text-4xl text-blue-600">
          {post.title}
        </h1>
        <div className="flex items-center gap-3 text-sm md:text-base mb-8">
          {avatarUrl && (
            <Avatar className="h-8 w-8 border">
              <AvatarImage
                src={avatarUrl}
                alt={post.author_name || ""}
              />
            </Avatar>
          )}
          <div>
            {post.author_name && (
              <a href="javascript:void(0)" className="font-medium">
                {post.author_name}
              </a>
            )}

            <span className="ml-2 text-muted-foreground">
              on {post.created_at && moment(post.created_at).fromNow()} · {readingTime} min read
            </span>
          </div>
        </div>
        <div className="relative mt-0 grid max-w-screen-xl gap-4 lg:mt-0 lg:grid lg:grid-cols-12 lg:gap-6">
          <div className="order-2 lg:order-none lg:col-span-8">
            {post.content && <Markdown content={post.content} />}
          </div>
          <div className="order-1 flex h-fit flex-col text-sm lg:sticky lg:top-8 lg:order-none lg:col-span-3 lg:col-start-10 lg:text-xs">

          </div>
        </div>
      </div>
    </section>
  );
}
