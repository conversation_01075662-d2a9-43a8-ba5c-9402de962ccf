"use client";

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MetricsData } from "./types";
import { formatNumber } from "./utils";

// Format date for X-axis display
const formatXAxisDate = (dateStr: string) => {
  const [year, month, day] = dateStr.split('/').map(Number);
  const date = new Date(year, month - 1, day);
  return `${month}/${day}`;
};

interface CoreMetricsTrendsProps {
  coreMetricsData: MetricsData[];
  coreTimeRange: '4w' | '12w' | '24w';
  onTimeRangeChange: (value: '4w' | '12w' | '24w') => void;
}

function processMetricsData(data: MetricsData[], timeRange: '4w' | '12w' | '24w'): MetricsData[] {
  // Data is already processed by generateCoreTimeSeriesData in utils.ts
  // Just return the data as-is since the time range filtering is handled in the parent component
  console.log("processMetricsData received data:", data);
  return data;
}

export function CoreMetricsTrends({ 
  coreMetricsData, 
  coreTimeRange, 
  onTimeRangeChange 
}: CoreMetricsTrendsProps) {
  const processedData = processMetricsData(coreMetricsData, coreTimeRange);
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">核心指标趋势</h3>
          <Badge variant="secondary" className="text-xs">按周统计</Badge>
        </div>
        
        <Select value={coreTimeRange} onValueChange={onTimeRangeChange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="4w">过去4周</SelectItem>
            <SelectItem value="12w">过去12周</SelectItem>
            <SelectItem value="24w">过去24周</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">DR评分趋势</CardTitle>
            <CardDescription>域名权威度变化（来自SEO工具）</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={processedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  fontSize={12}
                  tickFormatter={formatXAxisDate}
                  interval="preserveStartEnd"
                />
                <YAxis fontSize={12} />
                <Tooltip 
                  labelFormatter={(value) => `周: ${value}`}
                  formatter={(value: number) => [value, 'DR评分']}
                />
                <Line
                  type="monotone"
                  dataKey="dr_score"
                  stroke="hsl(var(--chart-secondary))"
                  strokeWidth={2}
                  dot={{ r: 3, fill: "hsl(var(--chart-secondary))" }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">外链数量趋势</CardTitle>
            <CardDescription>外链增长情况（来自数据库统计）</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={processedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  fontSize={12}
                  tickFormatter={formatXAxisDate}
                  interval="preserveStartEnd"
                />
                <YAxis fontSize={12} />
                <Tooltip 
                  labelFormatter={(value) => `周: ${value}`}
                  formatter={(value: number) => [value, '外链数量']}
                />
                <Line
                  type="monotone"
                  dataKey="linkCount"
                  stroke="hsl(var(--chart-primary))"
                  strokeWidth={2}
                  dot={{ r: 3, fill: "hsl(var(--chart-primary))" }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">流量数据趋势</CardTitle>
            <CardDescription>有机流量变化（来自数据库统计）</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={processedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  fontSize={12}
                  tickFormatter={formatXAxisDate}
                  interval="preserveStartEnd"
                />
                <YAxis tickFormatter={formatNumber} fontSize={12} />
                <Tooltip 
                  labelFormatter={(value) => `周: ${value}`}
                  formatter={(value: number) => [value.toLocaleString(), '有机流量']}
                />
                <Line
                  type="monotone"
                  dataKey="pageViews"
                  stroke="hsl(var(--chart-tertiary))"
                  strokeWidth={2}
                  dot={{ r: 3, fill: "hsl(var(--chart-tertiary))" }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}