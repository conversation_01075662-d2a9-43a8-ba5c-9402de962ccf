"use client";

import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Bar<PERSON>hart3, 
  Link2, 
  <PERSON>older<PERSON><PERSON>, 
  <PERSON><PERSON>hart, 
  Upload,
  Shield,
  Crown,
  Home,
  X,
  Globe
} from "lucide-react";
import ThemeToggle from "@/components/theme/toggle";
import { TierQuotaCard } from "./tier-quota-card";
import { useTierStatus } from "@/lib/hooks/useTierStatus";

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  projectsCount: number;
  linksCount: number;
  onClose?: () => void;
}

export function Sidebar({ 
  activeSection, 
  onSectionChange, 
  projectsCount, 
  linksCount,
  onClose
}: SidebarProps) {
  const t = useTranslations("links");
  const router = useRouter();
  const { isPaidUser, tierInfo } = useTierStatus();
  const isAdmin = fetch("/api/admin/auth").then(res => res.json()).then(data => data.isAdmin);

  const mainMenuItems = [
    {
      id: "overview",
      label: "Overview",
      icon: Home,
      count: null
    },
    {
      id: "all-links",
      label: "Link Resources",
      icon: Link2,
      count: linksCount
    },
    {
      id: "projects",
      label: "Projects",
      icon: FolderOpen,
      count: projectsCount
    },
    // {
    //   id: "integrations",
    //   label: "Integrations",
    //   icon: PieChart,
    //   count: null
    // }
  ];

  const toolsMenuItems = [
    {
     id: "domain-management",
     label: "Domain Management",
     icon: Globe
    },
    // Admin panel only for admins
    ...(isAdmin ? [
      {
        id: "admin-panel",
        label: "Admin Panel",
        icon: Shield
      }
    ] : []),
    // Batch import for paid users
    ...(isAdmin ? [
      {
        id: "batch-import",
        label: "Batch Import(Admin)",
        icon: Upload
      }
    ] : [])
  ];

  return (
    <div className="w-52 xl:w-56 h-full bg-card border-r border-border flex flex-col">
      {/* Mobile Header with Close Button */}
      {onClose && (
        <div className="lg:hidden flex items-center justify-between p-3 border-b border-border">
          <h2 className="font-semibold text-base">Menu</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Main Navigation - Optimized spacing */}
      <div className="p-3 flex-1 overflow-y-auto">
        <div className="mb-3">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide mb-2">
            MAIN
          </h3>
          <nav className="space-y-1">
            {mainMenuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => onSectionChange(item.id)}
                  className={`w-full flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    isActive
                      ? "bg-primary text-primary-foreground border border-primary"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted border border-transparent hover:border-border"
                  }`}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  <span className="flex-1 text-left truncate text-xs xl:text-sm">{item.label}</span>
                  {item.count !== null && (
                    <Badge variant="secondary" className="ml-auto text-xs px-1.5 py-0.5">
                      {item.count}
                    </Badge>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="mb-3">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide mb-2">
            TOOLS
          </h3>
          <nav className="space-y-1">
            {toolsMenuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    if (item.id === "admin-panel") {
                      router.push("/admin");
                    } else if (item.id === "domain-lookup") {
                      router.push("/link-resources/domain-lookup");
                    } else {
                      onSectionChange(item.id);
                    }
                  }}
                  className={`w-full flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    isActive
                      ? "bg-primary text-primary-foreground border border-primary"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted border border-transparent hover:border-border"
                  }`}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  <span className="flex-1 text-left truncate text-xs xl:text-sm">{item.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Theme Toggle - More compact */}
      {/* <div className="p-3 border-t border-border/50">
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
            THEME
          </span>
          <ThemeToggle />
        </div>
      </div> */}

      {/* Tier Quota Card */}
      <TierQuotaCard />
    </div>
  );
} 
