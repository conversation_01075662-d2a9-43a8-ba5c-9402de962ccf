export interface MetricsData {
  date: string;
  dr_score: number;
  linkCount: number;
  pageViews: number;
}

export interface AnalyticsData {
  date: string;
  pageViews: number;
  sessions: number;
  users: number;
}

export interface BacklinkData {
  id: string;
  url: string;
  title: string;
  domain: string;
  dr_score?: number;
  traffic_contribution: number;
  link_type: 'dofollow' | 'nofollow';
  anchor_text: string;
  source_url: string;
  discovered_at: string;
}

export interface AnalyticsConfig {
  provider: 'google' | 'plausible' | 'umami';
  api_key: string;
  website_id: string;
  base_url?: string;
  domain: string;
  isActive: boolean;
  lastTested?: string;
  testStatus?: 'success' | 'error' | 'pending';
}

export interface RealTimeData {
  dr_score: number;
  linkCount: number;
  pageViews: number;
}