"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp, Activity, Globe, Link2, Users } from "lucide-react";

export function AnalyticsView() {
  return (
    <div className="w-full max-w-none space-y-4 lg:space-y-6">
      {/* Analytics Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-foreground">--</p>
                <p className="text-sm text-muted-foreground">Total Views</p>
              </div>
              <div className="bg-muted p-2 rounded-md border">
                <Activity className="h-6 w-6 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-foreground">--</p>
                <p className="text-sm text-muted-foreground">Unique Visitors</p>
              </div>
              <div className="bg-muted p-2 rounded-md border">
                <Users className="h-6 w-6 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-foreground">--</p>
                <p className="text-sm text-muted-foreground">Bounce Rate</p>
              </div>
              <div className="bg-muted p-2 rounded-md border">
                <Globe className="h-6 w-6 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-foreground">--</p>
                <p className="text-sm text-muted-foreground">Avg. Session</p>
              </div>
              <div className="bg-muted p-2 rounded-md border">
                <Link2 className="h-6 w-6 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Charts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
        {/* Link Performance Chart */}
        <Card className="min-h-[400px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Link Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-80">
            <div className="text-center text-muted-foreground">
              <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-8 w-8" />
              </div>
              <p className="text-lg font-medium">Chart visualization would be here</p>
              <p className="text-sm">Connect analytics to see performance data</p>
            </div>
          </CardContent>
        </Card>

        {/* DR Score Trends Chart */}
        <Card className="min-h-[400px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              DR Score Trends
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-80">
            <div className="text-center text-muted-foreground">
              <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8" />
              </div>
              <p className="text-lg font-medium">Chart visualization would be here</p>
              <p className="text-sm">Track DR score changes over time</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Info */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20">
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Analytics Integration</h3>
            <p className="text-muted-foreground mb-4">
              Connect your analytics platforms to get detailed insights about your link performance, 
              traffic sources, and user behavior.
            </p>
            <div className="flex flex-wrap justify-center gap-2">
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm">
                Google Analytics
              </span>
              <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm">
                Plausible
              </span>
              <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm">
                Umami
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 