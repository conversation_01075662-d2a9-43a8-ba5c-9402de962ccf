'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RefreshCw, Search, Filter, Globe, Calendar, Building, Server, ExternalLink, Plus, DollarSign, Star, Trash2, Edit, MoreVertical, Smartphone, Monitor, ChevronDown } from 'lucide-react';
import { DomainInfo, DomainFilters, DomainStats } from '@/types/domain';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

export default function DomainManagement() {
  const t = useTranslations();
  const [domains, setDomains] = useState<DomainInfo[]>([]);
  const [stats, setStats] = useState<DomainStats | null>(null);
  const [registrars, setRegistrars] = useState<string[]>([]);
  const [dnsProviders, setDnsProviders] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<DomainFilters>({
    status: 'all',
    sortBy: 'domain',
    sortOrder: 'asc'
  });

  // Add domain dialog state
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingDomain, setEditingDomain] = useState<DomainInfo | null>(null);
  const [addingDomain, setAddingDomain] = useState(false);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [isMobile, setIsMobile] = useState(false);
  
  // Form state
  const [domainForm, setDomainForm] = useState({
    domain: '',
    registrationPrice: '',
    renewalPrice: '',
    currency: 'USD',
    notes: '',
    tags: '',
    autoRenew: false,
    monitorExpiry: true,
    alertDaysBefore: '30'
  });

  useEffect(() => {
    loadData();
  }, [filters]);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setViewMode('cards');
      }
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load domains with current filters
      const domainsParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all') {
          domainsParams.append(key, value);
        }
      });

      const [domainsRes, statsRes, registrarsRes, dnsRes] = await Promise.all([
        fetch(`/api/domain/management?${domainsParams}`),
        fetch('/api/domain/management?action=stats'),
        fetch('/api/domain/management?action=registrars'),
        fetch('/api/domain/management?action=dns-providers')
      ]);

      if (domainsRes.ok) {
        const domainsData = await domainsRes.json();
        setDomains(domainsData);
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData);
      }

      if (registrarsRes.ok) {
        const registrarsData = await registrarsRes.json();
        setRegistrars(registrarsData);
      }

      if (dnsRes.ok) {
        const dnsData = await dnsRes.json();
        setDnsProviders(dnsData);
      }
    } catch (error) {
      console.error('Failed to load domain data:', error);
      toast.error(t('domain.loadError') || 'Failed to load domain data');
    } finally {
      setLoading(false);
    }
  };

  const refreshWhois = async (domain: string) => {
    try {
      setRefreshing(domain);
      
      const response = await fetch('/api/domain/management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'refresh-whois',
          domain
        })
      });

      const result = await response.json();

      if (response.ok) {
        if (result.refreshed) {
          toast.success(t('domain.whoisRefreshed') || 'WHOIS data refreshed successfully');
          loadData(); // Reload data to show updated information
        } else {
          toast.info(t('domain.whoisFresh') || 'WHOIS data is still fresh');
        }
      } else {
        toast.error(result.error || 'Failed to refresh WHOIS data');
      }
    } catch (error) {
      console.error('Failed to refresh WHOIS:', error);
      toast.error(t('domain.refreshError') || 'Failed to refresh WHOIS data');
    } finally {
      setRefreshing(null);
    }
  };

  const handleAddDomain = async () => {
    try {
      setAddingDomain(true);
      
      const response = await fetch('/api/domain/management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'add-domain',
          domain: domainForm.domain,
          domainData: {
            registrationPrice: domainForm.registrationPrice ? parseFloat(domainForm.registrationPrice) : undefined,
            renewalPrice: domainForm.renewalPrice ? parseFloat(domainForm.renewalPrice) : undefined,
            currency: domainForm.currency,
            notes: domainForm.notes,
            tags: domainForm.tags.split(',').map(tag => tag.trim()).filter(Boolean),
            autoRenew: domainForm.autoRenew,
            monitorExpiry: domainForm.monitorExpiry,
            alertDaysBefore: parseInt(domainForm.alertDaysBefore)
          }
        })
      });

      const result = await response.json();

      if (response.ok) {
        let successMessage = 'Domain added successfully';
        
        // Show information about auto-discovered projects
        if (result.relatedProjects && result.relatedProjects.associatedCount > 0) {
          const projectNames = result.relatedProjects.projects.map((p: any) => p.name).join(', ');
          successMessage += ` and automatically linked to ${result.relatedProjects.associatedCount} project(s): ${projectNames}`;
        }
        
        if (result.whoisFetched) {
          successMessage += ' with WHOIS data';
        }
        
        toast.success(successMessage);
        setShowAddDialog(false);
        resetForm();
        loadData();
      } else {
        toast.error(result.error || 'Failed to add domain');
      }
    } catch (error) {
      console.error('Failed to add domain:', error);
      toast.error('Failed to add domain');
    } finally {
      setAddingDomain(false);
    }
  };

  const handleEditDomain = async () => {
    if (!editingDomain) return;

    try {
      setAddingDomain(true);
      
      const response = await fetch('/api/domain/management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update-domain',
          domainId: editingDomain.id,
          domainData: {
            domain: editingDomain.domain,
            registrationPrice: domainForm.registrationPrice ? parseFloat(domainForm.registrationPrice) : undefined,
            renewalPrice: domainForm.renewalPrice ? parseFloat(domainForm.renewalPrice) : undefined,
            currency: domainForm.currency,
            notes: domainForm.notes,
            tags: domainForm.tags.split(',').map(tag => tag.trim()).filter(Boolean),
            autoRenew: domainForm.autoRenew,
            monitorExpiry: domainForm.monitorExpiry,
            alertDaysBefore: parseInt(domainForm.alertDaysBefore)
          }
        })
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Domain updated successfully');
        setShowEditDialog(false);
        setEditingDomain(null);
        resetForm();
        loadData();
      } else {
        toast.error(result.error || 'Failed to update domain');
      }
    } catch (error) {
      console.error('Failed to update domain:', error);
      toast.error('Failed to update domain');
    } finally {
      setAddingDomain(false);
    }
  };

  const handleDeleteDomain = async (domain: DomainInfo) => {
    if (!confirm(`Are you sure you want to delete ${domain.domain}?`)) return;

    try {
      const response = await fetch('/api/domain/management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'delete-domain',
          domainId: domain.id
        })
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Domain deleted successfully');
        loadData();
      } else {
        toast.error(result.error || 'Failed to delete domain');
      }
    } catch (error) {
      console.error('Failed to delete domain:', error);
      toast.error('Failed to delete domain');
    }
  };

  const handleRefreshWhois = async (domainId: string) => {
    try {
      setRefreshing(domainId);
      
      const response = await fetch('/api/domain/management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'refresh-whois',
          domainId: domainId
        })
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('WHOIS data refreshed successfully');
        loadData();
      } else {
        toast.error(result.error || 'Failed to refresh WHOIS data');
      }
    } catch (error) {
      console.error('Failed to refresh WHOIS:', error);
      toast.error('Failed to refresh WHOIS data');
    } finally {
      setRefreshing(null);
    }
  };

  const resetForm = () => {
    setDomainForm({
      domain: '',
      registrationPrice: '',
      renewalPrice: '',
      currency: 'USD',
      notes: '',
      tags: '',
      autoRenew: false,
      monitorExpiry: true,
      alertDaysBefore: '30'
    });
  };

  const openEditDialog = (domain: DomainInfo) => {
    setEditingDomain(domain);
    setDomainForm({
      domain: domain.domain,
      registrationPrice: domain.registrationPrice?.toString() || '',
      renewalPrice: domain.renewalPrice?.toString() || '',
      currency: domain.currency || 'USD',
      notes: domain.notes || '',
      tags: domain.tags?.join(', ') || '',
      autoRenew: domain.autoRenew || false,
      monitorExpiry: domain.monitorExpiry !== false,
      alertDaysBefore: domain.alertDaysBefore?.toString() || '30'
    });
    setShowEditDialog(true);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (domain: DomainInfo) => {
    if (!domain.expiryDate) {
      return <Badge variant="secondary">{t('domain.status.unknown') || 'Unknown'}</Badge>;
    }

    const expiryDate = new Date(domain.expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    if (expiryDate < now) {
      return <Badge variant="destructive">{t('domain.status.expired') || 'Expired'}</Badge>;
    } else if (expiryDate <= thirtyDaysFromNow) {
      return <Badge variant="outline" className="border-orange-500 text-orange-600">
        {t('domain.status.expiring') || 'Expiring Soon'}
      </Badge>;
    } else {
      return <Badge variant="default">{t('domain.status.active') || 'Active'}</Badge>;
    }
  };

  // Mobile Domain Card Component
  const DomainCard = ({ domain }: { domain: DomainInfo }) => (
    <Card className="p-4 space-y-3 touch-manipulation">
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <h3 className="font-medium truncate">{domain.domain}</h3>
            {domain.isFavorite && <Star className="h-3 w-3 text-yellow-500 fill-current flex-shrink-0" />}
          </div>
          <div className="flex items-center gap-2 mb-2">
            {getStatusBadge(domain)}
            {domain.projectCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {domain.projectCount} {domain.projectCount === 1 ? 'project' : 'projects'}
              </Badge>
            )}
          </div>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => openEditDialog(domain)}>
              <Edit className="h-4 w-4 mr-2" />
              {t('domain.table.edit') || 'Edit'}
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleRefreshWhois(domain.id)}
              disabled={refreshing === domain.id}
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", refreshing === domain.id && "animate-spin")} />
              {t('domain.table.refresh') || 'Refresh WHOIS'}
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleDeleteDomain(domain)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {t('domain.table.delete') || 'Delete'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <div className="text-muted-foreground mb-1">{t('domain.table.registrar') || 'Registrar'}</div>
          <div className="flex items-center gap-1">
            {domain.registrar && <Building className="h-3 w-3 text-muted-foreground" />}
            <span className="truncate">{domain.registrar || '-'}</span>
          </div>
        </div>
        
        <div>
          <div className="text-muted-foreground mb-1">{t('domain.table.expiryDate') || 'Expires'}</div>
          <div className="flex items-center gap-1">
            {domain.expiryDate && <Calendar className="h-3 w-3 text-muted-foreground" />}
            <span className="truncate">{formatDate(domain.expiryDate)}</span>
          </div>
        </div>
        
        {domain.dnsProvider && (
          <div>
            <div className="text-muted-foreground mb-1">DNS Provider</div>
            <div className="flex items-center gap-1">
              <Server className="h-3 w-3 text-muted-foreground" />
              <span className="truncate">{domain.dnsProvider}</span>
            </div>
          </div>
        )}
        
        {domain.renewalPrice && (
          <div>
            <div className="text-muted-foreground mb-1">{t('domain.table.renewalPrice') || 'Renewal'}</div>
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <span className="truncate">{domain.currency} {domain.renewalPrice}</span>
              {domain.autoRenew && <span className="text-xs text-green-600 ml-1">Auto</span>}
            </div>
          </div>
        )}
      </div>

      {domain.tags && domain.tags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {domain.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {domain.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{domain.tags.length - 3}
            </Badge>
          )}
        </div>
      )}

      {domain.associatedProjects && domain.associatedProjects.length > 0 && (
        <div className="pt-2 border-t">
          <div className="text-xs text-muted-foreground mb-1">Associated Projects:</div>
          <div className="text-xs">
            {domain.associatedProjects.slice(0, 2).map(p => p.project_name).join(', ')}
            {domain.associatedProjects.length > 2 && ` +${domain.associatedProjects.length - 2} more`}
          </div>
        </div>
      )}
    </Card>
  );

  const filteredDomains = domains.filter(domain =>
    domain.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (domain.registrar && domain.registrar.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (domain.dnsProvider && domain.dnsProvider.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading && domains.length === 0) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {t('domain.management.title') || 'Domain Management'}
            </CardTitle>
            <CardDescription>
              {t('domain.management.description') || 'Manage all your domains and their registration information'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">{t('domain.stats.total') || 'Total Domains'}</p>
                  <p className="text-2xl font-bold">{stats.totalDomains}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">{t('domain.stats.active') || 'Active'}</p>
                  <p className="text-2xl font-bold">{stats.activeDomains}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">{t('domain.stats.expiring') || 'Expiring'}</p>
                  <p className="text-2xl font-bold">{stats.expiringDomains}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-red-500" />
                <div>
                  <p className="text-sm text-muted-foreground">{t('domain.stats.expired') || 'Expired'}</p>
                  <p className="text-2xl font-bold">{stats.expiredDomains}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">{t('domain.stats.projects') || 'Projects'}</p>
                  <p className="text-2xl font-bold">{stats.totalProjects}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Domain Management Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {t('domain.management.title') || 'Domain Management'}
              </CardTitle>
              <CardDescription>
                {t('domain.management.description') || 'Manage all your domains and their registration information'}
              </CardDescription>
            </div>
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button onClick={() => setShowAddDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Domain
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>{t('domain.management.addDomain') || 'Add New Domain'}</DialogTitle>
                  <DialogDescription>
                    {t('domain.form.addDescription') || 'Enter domain details. WHOIS data will be fetched automatically.'}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="domain">Domain Name</Label>
                    <Input
                      id="domain"
                      placeholder="example.com"
                      value={domainForm.domain}
                      onChange={(e) => setDomainForm(prev => ({ ...prev, domain: e.target.value }))}
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="regPrice">{t('domain.form.registrationPrice') || 'Registration Price'}</Label>
                      <Input
                        id="regPrice"
                        type="number"
                        step="0.01"
                        placeholder="12.99"
                        value={domainForm.registrationPrice}
                        onChange={(e) => setDomainForm(prev => ({ ...prev, registrationPrice: e.target.value }))}
                        className="text-base" // Prevent zoom on iOS
                      />
                    </div>
                    <div>
                      <Label htmlFor="renewPrice">{t('domain.form.renewalPrice') || 'Renewal Price'}</Label>
                      <Input
                        id="renewPrice"
                        type="number"
                        step="0.01"
                        placeholder="15.99"
                        value={domainForm.renewalPrice}
                        onChange={(e) => setDomainForm(prev => ({ ...prev, renewalPrice: e.target.value }))}
                        className="text-base" // Prevent zoom on iOS
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="currency">Currency</Label>
                    <Select value={domainForm.currency} onValueChange={(value) => setDomainForm(prev => ({ ...prev, currency: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                        <SelectItem value="JPY">JPY</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="tags">Tags (comma-separated)</Label>
                    <Input
                      id="tags"
                      placeholder="hosting, production, client"
                      value={domainForm.tags}
                      onChange={(e) => setDomainForm(prev => ({ ...prev, tags: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Additional notes about this domain..."
                      value={domainForm.notes}
                      onChange={(e) => setDomainForm(prev => ({ ...prev, notes: e.target.value }))}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => { setShowAddDialog(false); resetForm(); }}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddDomain} disabled={addingDomain || !domainForm.domain}>
                    {addingDomain ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
                    Add Domain
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="space-y-4 mb-6">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('domain.management.searchPlaceholder') || 'Search domains...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters Row */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
              <div className="flex gap-2 flex-1 overflow-x-auto pb-2 sm:pb-0">
                <Select
                  value={filters.status}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
                >
                  <SelectTrigger className="w-32 sm:w-40 flex-shrink-0">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">{t('domain.status.active') || 'Active'}</SelectItem>
                    <SelectItem value="expiring">{t('domain.status.expiring') || 'Expiring'}</SelectItem>
                    <SelectItem value="expired">{t('domain.status.expired') || 'Expired'}</SelectItem>
                  </SelectContent>
                </Select>

                {registrars.length > 0 && (
                  <Select
                    value={filters.registrar || 'all'}
                    onValueChange={(value) => setFilters(prev => ({ 
                      ...prev, 
                      registrar: value === 'all' ? undefined : value 
                    }))}
                  >
                    <SelectTrigger className="w-32 sm:w-40 flex-shrink-0">
                      <SelectValue placeholder="Registrar" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Registrars</SelectItem>
                      {registrars.map(registrar => (
                        <SelectItem key={registrar} value={registrar}>{registrar}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                <Select
                  value={`${filters.sortBy}-${filters.sortOrder}`}
                  onValueChange={(value) => {
                    const [sortBy, sortOrder] = value.split('-');
                    setFilters(prev => ({ ...prev, sortBy: sortBy as any, sortOrder: sortOrder as any }));
                  }}
                >
                  <SelectTrigger className="w-32 sm:w-40 flex-shrink-0">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="domain-asc">Domain A-Z</SelectItem>
                    <SelectItem value="domain-desc">Domain Z-A</SelectItem>
                    <SelectItem value="expiryDate-asc">Expiry (Soon)</SelectItem>
                    <SelectItem value="expiryDate-desc">Expiry (Late)</SelectItem>
                    <SelectItem value="projectCount-desc">Most Projects</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* View Mode Toggle - Only show on desktop */}
              {!isMobile && (
                <div className="flex border rounded-lg p-1 bg-muted/50">
                  <Button
                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('table')}
                    className="h-8"
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'cards' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('cards')}
                    className="h-8"
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Domain Content */}
          {filteredDomains.length === 0 ? (
            <div className="text-center py-8">
              <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t('domain.management.noDomains') || 'No domains found'}
              </h3>
              <p className="text-muted-foreground">
                {searchTerm 
                  ? (t('domain.empty.searchDescription') || 'Try adjusting your search or filters')
                  : (t('domain.empty.description') || 'Start by adding domains to see them here')
                }
              </p>
            </div>
          ) : viewMode === 'cards' ? (
            /* Mobile/Card View */
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {filteredDomains.map((domain) => (
                <DomainCard key={domain.id} domain={domain} />
              ))}
            </div>
          ) : (
            /* Desktop Table View */
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('domain.table.domain') || 'Domain'}</TableHead>
                    <TableHead>{t('domain.table.status') || 'Status'}</TableHead>
                    <TableHead>{t('domain.table.registrar') || 'Registrar'}</TableHead>
                    <TableHead>{t('domain.table.expires') || 'Expires'}</TableHead>
                    <TableHead>{t('domain.table.renewalPrice') || 'Renewal Price'}</TableHead>
                    <TableHead>{t('domain.table.projects') || 'Projects'}</TableHead>
                    <TableHead>{t('domain.table.actions') || 'Actions'}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDomains.map((domain) => (
                    <TableRow key={domain.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="flex items-center gap-2">
                              {domain.domain}
                              {domain.isFavorite && <Star className="h-3 w-3 text-yellow-500 fill-current" />}
                            </div>
                            {domain.tags && domain.tags.length > 0 && (
                              <div className="flex gap-1 mt-1">
                                {domain.tags.map(tag => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(domain)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {domain.registrar && <Building className="h-4 w-4 text-muted-foreground" />}
                          <div>
                            <div>{domain.registrar || '-'}</div>
                            {domain.dnsProvider && (
                              <div className="text-xs text-muted-foreground flex items-center gap-1">
                                <Server className="h-3 w-3" />
                                {domain.dnsProvider}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {domain.expiryDate && <Calendar className="h-4 w-4 text-muted-foreground" />}
                          <div>
                            <div>{formatDate(domain.expiryDate)}</div>
                            {domain.createdDate && (
                              <div className="text-xs text-muted-foreground">
                                Reg: {formatDate(domain.createdDate)}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {domain.renewalPrice ? (
                            <>
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <div>{domain.currency} {domain.renewalPrice}</div>
                                {domain.autoRenew && (
                                  <div className="text-xs text-green-600">Auto-renew</div>
                                )}
                              </div>
                            </>
                          ) : '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <Badge variant="secondary">{domain.projectCount}</Badge>
                          {domain.associatedProjects && domain.associatedProjects.length > 0 && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {domain.associatedProjects.slice(0, 2).map(p => p.project_name).join(', ')}
                              {domain.associatedProjects.length > 2 && ` +${domain.associatedProjects.length - 2}`}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(domain)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => refreshWhois(domain.domain)}
                            disabled={refreshing === domain.domain}
                          >
                            <RefreshCw className={`h-4 w-4 ${refreshing === domain.domain ? 'animate-spin' : ''}`} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(`https://${domain.domain}`, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDomain(domain)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Domain Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Domain: {editingDomain?.domain}</DialogTitle>
            <DialogDescription>
              Update domain details and pricing information.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="editRegPrice">Registration Price</Label>
                <Input
                  id="editRegPrice"
                  type="number"
                  step="0.01"
                  placeholder="12.99"
                  value={domainForm.registrationPrice}
                  onChange={(e) => setDomainForm(prev => ({ ...prev, registrationPrice: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="editRenewPrice">Renewal Price</Label>
                <Input
                  id="editRenewPrice"
                  type="number"
                  step="0.01"
                  placeholder="15.99"
                  value={domainForm.renewalPrice}
                  onChange={(e) => setDomainForm(prev => ({ ...prev, renewalPrice: e.target.value }))}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="editCurrency">Currency</Label>
              <Select value={domainForm.currency} onValueChange={(value) => setDomainForm(prev => ({ ...prev, currency: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="GBP">GBP</SelectItem>
                  <SelectItem value="JPY">JPY</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="editTags">Tags (comma-separated)</Label>
              <Input
                id="editTags"
                placeholder="hosting, production, client"
                value={domainForm.tags}
                onChange={(e) => setDomainForm(prev => ({ ...prev, tags: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="editNotes">Notes</Label>
              <Textarea
                id="editNotes"
                placeholder="Additional notes about this domain..."
                value={domainForm.notes}
                onChange={(e) => setDomainForm(prev => ({ ...prev, notes: e.target.value }))}
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="editAutoRenew"
                  checked={domainForm.autoRenew}
                  onChange={(e) => setDomainForm(prev => ({ ...prev, autoRenew: e.target.checked }))}
                />
                <Label htmlFor="editAutoRenew">Auto-renew</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="editMonitorExpiry"
                  checked={domainForm.monitorExpiry}
                  onChange={(e) => setDomainForm(prev => ({ ...prev, monitorExpiry: e.target.checked }))}
                />
                <Label htmlFor="editMonitorExpiry">Monitor expiry</Label>
              </div>
            </div>
            {domainForm.monitorExpiry && (
              <div>
                <Label htmlFor="editAlertDays">Alert days before expiry</Label>
                <Input
                  id="editAlertDays"
                  type="number"
                  value={domainForm.alertDaysBefore}
                  onChange={(e) => setDomainForm(prev => ({ ...prev, alertDaysBefore: e.target.value }))}
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => { setShowEditDialog(false); setEditingDomain(null); resetForm(); }}>
              Cancel
            </Button>
            <Button onClick={handleEditDomain} disabled={addingDomain}>
              {addingDomain ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Edit className="h-4 w-4 mr-2" />}
              Update Domain
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 