"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Settings, ArrowLeft, ArrowRight, Info, CheckCircle, AlertTriangle } from "lucide-react";
import { PublicLinkResourceData, ImportConfiguration as ImportConfigType } from "@/types/import";

interface ImportConfigurationProps {
  data: PublicLinkResourceData[];
  onComplete: (config: ImportConfigType) => void;
  onBack: () => void;
}

export function ImportConfiguration({ data, onComplete, onBack }: ImportConfigurationProps) {
  const t = useTranslations("links");
  
  const [importMode, setImportMode] = useState<'create_only' | 'update_existing' | 'upsert'>('create_only');
  const [skipDuplicates, setSkipDuplicates] = useState(true);
  const [validateUrls, setValidateUrls] = useState(true);
  const [validateEmails, setValidateEmails] = useState(true);

  const selectedData = data.filter(item => item._selected);
  const validData = selectedData.filter(item => item._validation_status === 'valid');
  const errorData = selectedData.filter(item => item._validation_status === 'error');
  const warningData = selectedData.filter(item => item._validation_status === 'warning');

  const handleContinue = () => {
    const config: ImportConfigType = {
      mode: importMode,
      skip_duplicates: skipDuplicates,
      validate_urls: validateUrls,
      validate_emails: validateEmails,
      selected_rows: data.map((_, index) => index).filter(index => data[index]._selected)
    };
    onComplete(config);
  };

  const getImportModeDescription = (mode: string) => {
    switch (mode) {
      case 'create_only':
        return 'Only create new records. Skip existing domains.';
      case 'update_existing':
        return 'Only update existing records. Skip new domains.';
      case 'upsert':
        return 'Create new records and update existing ones.';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Settings className="h-6 w-6 text-primary" />
          <h3 className="text-2xl font-semibold">Import Configuration</h3>
        </div>
        <p className="text-muted-foreground">
          Configure how your data should be imported into the system
        </p>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{selectedData.length}</div>
              <div className="text-sm text-muted-foreground">Selected</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{validData.length}</div>
              <div className="text-sm text-muted-foreground">Valid</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{warningData.length}</div>
              <div className="text-sm text-muted-foreground">Warnings</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{errorData.length}</div>
              <div className="text-sm text-muted-foreground">Errors</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Import Mode Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Import Mode</CardTitle>
          <CardDescription>
            Choose how to handle existing records during import
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup value={importMode} onValueChange={(value: any) => setImportMode(value)}>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg border">
                <RadioGroupItem value="create_only" id="create_only" />
                <div className="flex-1">
                  <Label htmlFor="create_only" className="font-medium">
                    Create Only
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {getImportModeDescription('create_only')}
                  </p>
                </div>
                <Badge variant="outline">Recommended</Badge>
              </div>
              
              <div className="flex items-center space-x-3 p-3 rounded-lg border">
                <RadioGroupItem value="update_existing" id="update_existing" />
                <div className="flex-1">
                  <Label htmlFor="update_existing" className="font-medium">
                    Update Existing
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {getImportModeDescription('update_existing')}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 rounded-lg border">
                <RadioGroupItem value="upsert" id="upsert" />
                <div className="flex-1">
                  <Label htmlFor="upsert" className="font-medium">
                    Create or Update (Upsert)
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {getImportModeDescription('upsert')}
                  </p>
                </div>
              </div>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Import Options */}
      <Card>
        <CardHeader>
          <CardTitle>Import Options</CardTitle>
          <CardDescription>
            Additional validation and processing options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-medium">Skip Duplicates</Label>
                <p className="text-sm text-muted-foreground">
                  Skip records with domains that already exist in the system
                </p>
              </div>
              <Switch 
                checked={skipDuplicates} 
                onCheckedChange={setSkipDuplicates}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-medium">Validate URLs</Label>
                <p className="text-sm text-muted-foreground">
                  Verify that website URLs and submission URLs are accessible
                </p>
              </div>
              <Switch 
                checked={validateUrls} 
                onCheckedChange={setValidateUrls}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-medium">Validate Emails</Label>
                <p className="text-sm text-muted-foreground">
                  Check that contact email addresses are properly formatted
                </p>
              </div>
              <Switch 
                checked={validateEmails} 
                onCheckedChange={setValidateEmails}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Import Summary */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Info className="h-5 w-5" />
            Import Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-800">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Records to import:</span>
              <span className="font-medium">{selectedData.length}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Import mode:</span>
              <span className="font-medium capitalize">{importMode.replace('_', ' ')}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Skip duplicates:</span>
              <span className="font-medium">{skipDuplicates ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>URL validation:</span>
              <span className="font-medium">{validateUrls ? 'Enabled' : 'Disabled'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Email validation:</span>
              <span className="font-medium">{validateEmails ? 'Enabled' : 'Disabled'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Warnings */}
      {(errorData.length > 0 || warningData.length > 0) && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              Import Warnings
            </CardTitle>
          </CardHeader>
          <CardContent className="text-yellow-800">
            <div className="space-y-2">
              {errorData.length > 0 && (
                <p className="text-sm">
                  <strong>{errorData.length}</strong> records have validation errors and may fail to import.
                </p>
              )}
              {warningData.length > 0 && (
                <p className="text-sm">
                  <strong>{warningData.length}</strong> records have warnings but can still be imported.
                </p>
              )}
              <p className="text-sm">
                You can go back to review and fix these issues before proceeding.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Preview
        </Button>
        <Button onClick={handleContinue}>
          Start Import
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}