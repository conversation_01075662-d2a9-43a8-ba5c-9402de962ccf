"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CheckCircle, AlertTriangle, X, Download, RotateCcw, ExternalLink } from "lucide-react";
import { ImportResult, ImportError } from "@/types/import";

interface ImportResultsProps {
  results?: ImportResult;
  onStartNew: () => void;
  onClose: () => void;
}

export function ImportResults({ results, onStartNew, onClose }: ImportResultsProps) {
  const t = useTranslations("links");
  const [exportingResults, setExportingResults] = useState(false);

  if (!results) {
    return null;
  }

  const successRate = results.total_processed > 0 
    ? Math.round((results.successful_imports / results.total_processed) * 100)
    : 0;

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const handleExportResults = async () => {
    setExportingResults(true);
    try {
      // Create CSV content with results
      const csvContent = [
        ['Metric', 'Value'],
        ['Session ID', results.session_id || 'N/A'],
        ['Total Processed', results.total_processed.toString()],
        ['Successful Imports', results.successful_imports.toString()],
        ['Failed Imports', results.failed_imports.toString()],
        ['Skipped Duplicates', results.skipped_duplicates.toString()],
        ['Success Rate', `${successRate}%`],
        ['Duration', formatDuration(results.duration_ms)],
        ['Completed At', new Date(results.completed_at).toLocaleString()],
        [],
        ['Error Details'],
        ['Row Number', 'Field', 'Error Message', 'Severity'],
        ...results.errors.map(error => [
          error.row_number.toString(),
          error.field || '-',
          error.error_message,
          error.severity
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `import_results_${(results.session_id || 'unknown').slice(0, 8)}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting results:', error);
    } finally {
      setExportingResults(false);
    }
  };

  const getStatusIcon = () => {
    if (results.failed_imports === 0) {
      return <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />;
    } else if (results.successful_imports > 0) {
      return <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto" />;
    } else {
      return <X className="h-16 w-16 text-red-500 mx-auto" />;
    }
  };

  const getStatusTitle = () => {
    if (results.failed_imports === 0) {
      return "Import Completed Successfully!";
    } else if (results.successful_imports > 0) {
      return "Import Completed with Warnings";
    } else {
      return "Import Failed";
    }
  };

  const getStatusDescription = () => {
    if (results.failed_imports === 0) {
      return "All records were imported successfully without any errors.";
    } else if (results.successful_imports > 0) {
      return "Some records were imported successfully, but there were errors with others.";
    } else {
      return "No records were imported due to errors.";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        {getStatusIcon()}
        <h3 className="text-2xl font-semibold">{getStatusTitle()}</h3>
        <p className="text-muted-foreground">{getStatusDescription()}</p>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-3xl font-bold">{results.total_processed}</div>
              <div className="text-sm text-muted-foreground">Total Processed</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{results.successful_imports}</div>
              <div className="text-sm text-muted-foreground">Successful</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{results.failed_imports}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{results.skipped_duplicates}</div>
              <div className="text-sm text-muted-foreground">Skipped</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results */}
      <Card>
        <CardHeader>
          <CardTitle>Import Summary</CardTitle>
          <CardDescription>
            Detailed breakdown of the import operation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Success Rate:</span>
                <Badge variant={successRate === 100 ? "default" : successRate > 50 ? "secondary" : "destructive"}>
                  {successRate}%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Duration:</span>
                <span className="font-medium">{formatDuration(results.duration_ms)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Completed At:</span>
                <span className="font-medium">
                  {new Date(results.completed_at).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Session ID:</span>
                <span className="font-mono text-sm">{(results.session_id || 'N/A').slice(0, 8)}...</span>
              </div>
            </div>
            
            <div className="space-y-4">
              {results.successful_imports > 0 && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-800">
                    <CheckCircle className="h-4 w-4" />
                    <span className="font-medium">
                      {results.successful_imports} records imported successfully
                    </span>
                  </div>
                </div>
              )}
              
              {results.failed_imports > 0 && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2 text-red-800">
                    <X className="h-4 w-4" />
                    <span className="font-medium">
                      {results.failed_imports} records failed to import
                    </span>
                  </div>
                </div>
              )}
              
              {results.skipped_duplicates > 0 && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 text-blue-800">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="font-medium">
                      {results.skipped_duplicates} duplicates skipped
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Details */}
      {results.errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Error Details ({results.errors.length})
            </CardTitle>
            <CardDescription>
              Detailed information about errors that occurred during import
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Row</TableHead>
                    <TableHead>Field</TableHead>
                    <TableHead>Error Message</TableHead>
                    <TableHead>Severity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {results.errors.map((error, index) => (
                    <TableRow key={index}>
                      <TableCell>{error.row_number || '-'}</TableCell>
                      <TableCell>{error.field || '-'}</TableCell>
                      <TableCell className="max-w-md">
                        <div className="truncate" title={error.error_message}>
                          {error.error_message}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={error.severity === 'error' ? 'destructive' : 'secondary'}>
                          {error.severity}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExportResults} disabled={exportingResults}>
            {exportingResults ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Results
              </>
            )}
          </Button>
          {results.successful_imports > 0 && (
            <Button variant="outline">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Imported Records
            </Button>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onStartNew}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Import More Data
          </Button>
          <Button onClick={onClose}>
            Close
          </Button>
        </div>
      </div>

      {/* Next Steps */}
      {results.successful_imports > 0 && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800">Next Steps</CardTitle>
          </CardHeader>
          <CardContent className="text-green-800">
            <div className="space-y-2">
              <p className="text-sm">
                ✓ Your public link resources have been imported successfully
              </p>
              <p className="text-sm">
                ✓ You can now view and manage them in the admin panel
              </p>
              <p className="text-sm">
                ✓ Consider verifying the imported data and updating any missing information
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}