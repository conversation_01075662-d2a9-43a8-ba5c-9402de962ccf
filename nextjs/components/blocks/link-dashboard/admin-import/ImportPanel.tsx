"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Upload, Download, FileText, Settings, CheckCircle, AlertCircle, ArrowLeft } from "lucide-react";
import { ImportState, ImportError, PublicLinkResourceData, ImportConfiguration, ImportResult } from "@/types/import";
import { TemplateDownload } from "./TemplateDownload";
import { FileUpload } from "./FileUpload";
import { ImportPreview } from "./ImportPreview";
import { ImportConfiguration as ImportConfigComponent } from "./ImportConfiguration";
import { ImportResults } from "./ImportResults";

// Simplified import workflow states
enum WorkflowState {
  TEMPLATE = 'template',
  UPLOAD = 'upload',
  PREVIEW = 'preview',
  CONFIGURE = 'configure',
  IMPORTING = 'importing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

interface ImportPanelProps {
  onBack?: () => void;
}

const STEP_INDICATORS = [
  { key: 'template', label: 'Template', icon: Download },
  { key: 'upload', label: 'Upload', icon: Upload },
  { key: 'preview', label: 'Preview', icon: FileText },
  { key: 'config', label: 'Configure', icon: Settings },
  { key: 'import', label: 'Import', icon: CheckCircle }
];

export function ImportPanel({ onBack }: ImportPanelProps) {
  const t = useTranslations("links");
  
  const [currentStep, setCurrentStep] = useState<WorkflowState>(WorkflowState.TEMPLATE);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<PublicLinkResourceData[]>([]);
  const [importState, setImportState] = useState<ImportState>({
    total_records: 0,
    processed_records: 0,
    successful_imports: 0,
    failed_imports: 0,
    status: 'idle',
    errors: [],
    preview_data: []
  });
  const [importResults, setImportResults] = useState<ImportResult | null>(null);

  const resetState = useCallback(() => {
    setCurrentStep(WorkflowState.TEMPLATE);
    setUploadedFile(null);
    setParsedData([]);
    setImportState({
      total_records: 0,
      processed_records: 0,
      successful_imports: 0,
      failed_imports: 0,
      status: 'idle',
      errors: [],
      preview_data: []
    });
    setImportResults(null);
  }, []);

  const handleBack = useCallback(() => {
    resetState();
    onBack?.();
  }, [resetState, onBack]);

  const handleFileUpload = useCallback((file: File) => {
    setUploadedFile(file);
    setCurrentStep(WorkflowState.UPLOAD);
  }, []);

  const handleParseComplete = useCallback((data: PublicLinkResourceData[]) => {
    setParsedData(data);
    setImportState(prev => ({
      ...prev,
      total_records: data.length,
      preview_data: data,
      status: 'validating'
    }));
    setCurrentStep(WorkflowState.PREVIEW);
  }, []);

  const handleParseError = useCallback((errors: ImportError[]) => {
    setImportState(prev => ({
      ...prev,
      errors,
      status: 'error'
    }));
    setCurrentStep(WorkflowState.ERROR);
  }, []);

  const handlePreviewContinue = useCallback(() => {
    setCurrentStep(WorkflowState.CONFIGURE);
  }, []);

  const handleConfigurationComplete = useCallback(async (config: ImportConfiguration) => {
    setCurrentStep(WorkflowState.IMPORTING);
    setImportState(prev => ({ ...prev, status: 'importing' }));

    try {
      const response = await fetch('/api/admin/import/public-links/bulk-insert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: parsedData,
          configuration: config
        })
      });

      if (!response.ok) {
        throw new Error('Import failed');
      }

      const result: ImportResult = await response.json();
      setImportResults(result);
      setImportState(prev => ({
        ...prev,
        successful_imports: result.successful_imports,
        failed_imports: result.failed_imports,
        processed_records: result.total_processed,
        errors: result.errors,
        status: 'completed'
      }));
      setCurrentStep(WorkflowState.COMPLETED);
    } catch (error) {
      setImportState(prev => ({
        ...prev,
        status: 'error',
        errors: [{
          row_number: 0,
          error_message: error instanceof Error ? error.message : 'Import failed',
          raw_data: {},
          severity: 'error'
        }]
      }));
      setCurrentStep(WorkflowState.ERROR);
    }
  }, [parsedData]);

  const handleStartNew = useCallback(() => {
    resetState();
  }, [resetState]);

  const getCurrentStepIndex = (): number => {
    const stepMap: Record<WorkflowState, number> = {
      [WorkflowState.TEMPLATE]: 0,
      [WorkflowState.UPLOAD]: 1,
      [WorkflowState.PREVIEW]: 2,
      [WorkflowState.CONFIGURE]: 3,
      [WorkflowState.IMPORTING]: 4,
      [WorkflowState.COMPLETED]: 4,
      [WorkflowState.ERROR]: 0
    };
    return stepMap[currentStep];
  };

  const getProgress = (): number => {
    const progress = (getCurrentStepIndex() / (STEP_INDICATORS.length - 1)) * 100;
    return Math.min(progress, 100);
  };

  const renderContent = () => {
    switch (currentStep) {
      case WorkflowState.TEMPLATE:
        return (
          <TemplateDownload 
            onSkipToUpload={() => setCurrentStep(WorkflowState.UPLOAD)}
          />
        );
      
      case WorkflowState.UPLOAD:
        return (
          <FileUpload
            onFileUpload={handleFileUpload}
            onParseComplete={handleParseComplete}
            onParseError={handleParseError}
            onBack={() => setCurrentStep(WorkflowState.TEMPLATE)}
            file={uploadedFile}
          />
        );
      
      case WorkflowState.PREVIEW:
        return (
          <ImportPreview
            data={parsedData}
            onContinue={handlePreviewContinue}
            onBack={() => setCurrentStep(WorkflowState.UPLOAD)}
          />
        );
      
      case WorkflowState.CONFIGURE:
        return (
          <ImportConfigComponent
            data={parsedData}
            onComplete={handleConfigurationComplete}
            onBack={() => setCurrentStep(WorkflowState.PREVIEW)}
          />
        );
      
      case WorkflowState.IMPORTING:
        return (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-4"></div>
            <h3 className="text-xl font-semibold mb-2">Importing Data...</h3>
            <p className="text-muted-foreground">
              Processing {importState.total_records} records
            </p>
          </div>
        );
      
      case WorkflowState.COMPLETED:
        return (
          <ImportResults
            results={importResults}
            onStartNew={handleStartNew}
            onClose={handleBack}
          />
        );
      
      case WorkflowState.ERROR:
        return (
          <div className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Import Error</h3>
            <div className="text-sm text-muted-foreground mb-4">
              {importState.errors.map((error, index) => (
                <p key={index}>{error.error_message}</p>
              ))}
            </div>
            <Button onClick={handleStartNew} variant="outline">
              Try Again
            </Button>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {onBack && (
                <Button variant="ghost" size="sm" onClick={handleBack}>
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              <div>
                <CardTitle className="text-xl font-semibold">
                  Bulk Import Public Link Resources
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Import public link resources in bulk using CSV format
                </p>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Progress Steps */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            {STEP_INDICATORS.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === getCurrentStepIndex();
              const isCompleted = index < getCurrentStepIndex();
              
              return (
                <div key={step.key} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    isActive 
                      ? 'border-primary bg-primary text-white' 
                      : isCompleted 
                      ? 'border-green-500 bg-green-500 text-white'
                      : 'border-muted-foreground bg-muted text-muted-foreground'
                  }`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <span className={`ml-2 text-sm ${
                    isActive ? 'font-medium' : 'text-muted-foreground'
                  }`}>
                    {step.label}
                  </span>
                  {index < STEP_INDICATORS.length - 1 && (
                    <div className={`w-8 h-px mx-4 ${
                      isCompleted ? 'bg-green-500' : 'bg-muted'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">{Math.round(getProgress())}%</span>
            </div>
            <Progress value={getProgress()} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="space-y-6">
        {renderContent()}
      </div>

      {/* Status Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {importState.status.toUpperCase()}
              </Badge>
              {uploadedFile && (
                <span className="text-sm text-muted-foreground">
                  File: {uploadedFile.name}
                </span>
              )}
            </div>
            {parsedData.length > 0 && (
              <span className="text-sm text-muted-foreground">
                {parsedData.length} records loaded
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}