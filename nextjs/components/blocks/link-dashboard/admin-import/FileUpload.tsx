"use client";

import { useState, useCallback, useRef } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Upload, FileText, X, CheckCircle, AlertTriangle, ArrowLeft } from "lucide-react";
import { ImportError, PublicLinkResourceData, CSV_TEMPLATE_FIELDS, VALIDATION_RULES } from "@/types/import";

interface FileUploadProps {
  onFileUpload: (file: File) => void;
  onParseComplete: (data: PublicLinkResourceData[]) => void;
  onParseError: (errors: ImportError[]) => void;
  onBack?: () => void;
  file?: File | null;
}

export function FileUpload({ 
  onFileUpload, 
  onParseComplete, 
  onParseError, 
  onBack,
  file
}: FileUploadProps) {
  const t = useTranslations("links");
  const [isDragging, setIsDragging] = useState(false);
  const [parseStatus, setParseStatus] = useState<'idle' | 'parsing' | 'completed' | 'error'>('idle');
  const [errors, setErrors] = useState<ImportError[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => file.type === 'text/csv' || file.name.endsWith('.csv'));
    
    if (csvFile) {
      handleFileSelection(csvFile);
    }
  }, []);

  const handleFileSelection = useCallback((selectedFile: File) => {
    // Validate file type
    if (!selectedFile.type.includes('csv') && !selectedFile.name.endsWith('.csv')) {
      setErrors([{
        row_number: 0,
        error_message: 'Please select a CSV file',
        raw_data: {},
        severity: 'error'
      }]);
      setParseStatus('error');
      onParseError([{
        row_number: 0,
        error_message: 'Please select a CSV file',
        raw_data: {},
        severity: 'error'
      }]);
      return;
    }

    // Validate file size (max 10MB)
    if (selectedFile.size > 10 * 1024 * 1024) {
      setErrors([{
        row_number: 0,
        error_message: 'File size must be less than 10MB',
        raw_data: {},
        severity: 'error'
      }]);
      setParseStatus('error');
      onParseError([{
        row_number: 0,
        error_message: 'File size must be less than 10MB',
        raw_data: {},
        severity: 'error'
      }]);
      return;
    }

    onFileUpload(selectedFile);
    setParseStatus('parsing');
    setErrors([]);
    
    // Parse CSV file on client side
    startParsing(selectedFile);
  }, [onFileUpload, onParseComplete, onParseError]);

  const startParsing = useCallback(async (selectedFile: File) => {
    try {
      const fileContent = await selectedFile.text();
      const parseResult = parseCSVContent(fileContent);
      
      if (parseResult.errors.length > 0 && parseResult.data.length === 0) {
        setErrors(parseResult.errors);
        setParseStatus('error');
        onParseError(parseResult.errors);
        return;
      }

      // Validate data
      const validationResult = validateImportData(parseResult.data);
      
      setParseStatus('completed');
      onParseComplete(validationResult.data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to parse CSV';
      const parseError: ImportError = {
        row_number: 0,
        error_message: errorMessage,
        raw_data: {},
        severity: 'error'
      };
      setErrors([parseError]);
      setParseStatus('error');
      onParseError([parseError]);
    }
  }, [onParseComplete, onParseError]);

  // Client-side CSV parsing
  const parseCSVContent = (content: string): { data: Record<string, any>[], errors: ImportError[] } => {
    const errors: ImportError[] = [];
    const data: Record<string, any>[] = [];

    try {
      const lines = content.split('\n').filter(line => line.trim());
      
      if (lines.length === 0) {
        errors.push({
          row_number: 0,
          error_message: 'CSV file is empty',
          raw_data: {},
          severity: 'error'
        });
        return { data, errors };
      }

      // Parse header
      const headerLine = lines[0];
      const headers = parseCSVLine(headerLine);
      
      // Validate headers
      const requiredHeaders = CSV_TEMPLATE_FIELDS.filter(f => f.required).map(f => f.key);
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
      
      if (missingHeaders.length > 0) {
        errors.push({
          row_number: 0,
          error_message: `Missing required headers: ${missingHeaders.join(', ')}`,
          raw_data: { headers },
          severity: 'error'
        });
        return { data, errors };
      }

      // Parse data rows
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        try {
          const values = parseCSVLine(line);
          
          if (values.length !== headers.length) {
            errors.push({
              row_number: i + 1,
              error_message: `Column count mismatch. Expected ${headers.length}, got ${values.length}`,
              raw_data: { line },
              severity: 'error'
            });
            continue;
          }

          const rowData: Record<string, any> = {};
          headers.forEach((header, index) => {
            rowData[header] = values[index];
          });
          
          rowData._row_number = i + 1;
          data.push(rowData);

        } catch (parseError) {
          errors.push({
            row_number: i + 1,
            error_message: `Failed to parse line: ${parseError}`,
            raw_data: { line },
            severity: 'error'
          });
        }
      }

      return { data, errors };

    } catch (error) {
      errors.push({
        row_number: 0,
        error_message: `CSV parsing failed: ${error}`,
        raw_data: {},
        severity: 'error'
      });
      return { data, errors };
    }
  };

  // Parse a single CSV line handling quotes and commas
  const parseCSVLine = (line: string): string[] => {
    const values: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }
    
    values.push(current.trim());
    return values;
  };

  // Validate import data against business rules
  const validateImportData = (data: Record<string, any>[]): {
    data: PublicLinkResourceData[];
    errors: ImportError[];
  } => {
    const errors: ImportError[] = [];
    const validatedData: PublicLinkResourceData[] = [];

    for (const row of data) {
      const rowErrors: string[] = [];
      const rowNumber = row._row_number || 0;

      // Validate required fields
      for (const field of CSV_TEMPLATE_FIELDS) {
        if (field.required && (!row[field.key] || row[field.key].toString().trim() === '')) {
          rowErrors.push(`${field.label} is required`);
          errors.push({
            row_number: rowNumber,
            field: field.key,
            error_message: `${field.label} is required`,
            raw_data: row,
            severity: 'error'
          });
        }
      }

      // Create validated row
      const validatedRow: PublicLinkResourceData = {
        domain: row.domain?.trim() || '',
        title: row.title?.trim() || '',
        website_url: row.website_url?.trim() || '',
        submission_method: row.submission_method?.trim() || '',
        submission_url: row.submission_url?.trim() || undefined,
        contact_email: row.contact_email?.trim() || undefined,
        is_paid: parseBooleanField(row.is_paid),
        price_range: row.price_range?.trim() || undefined,
        currency: row.currency?.trim() || 'USD',
        category: row.category?.trim() || undefined,
        description: row.description?.trim() || undefined,
        requirements: row.requirements?.trim() || undefined,
        response_time: row.response_time?.trim() || undefined,
        success_rate: parseFloatField(row.success_rate),
        _row_number: rowNumber,
        _selected: true
      };

      // Validate field formats
      if (validatedRow.domain && !VALIDATION_RULES.domain.pattern.test(validatedRow.domain)) {
        validatedRow.domain = validatedRow.domain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
      }

      if (validatedRow.website_url && !VALIDATION_RULES.website_url.pattern.test(validatedRow.website_url)) {
        rowErrors.push('Invalid website URL format');
        errors.push({
          row_number: rowNumber,
          field: 'website_url',
          error_message: 'Invalid website URL format',
          raw_data: row,
          severity: 'error'
        });
      }

      if (validatedRow.contact_email && !VALIDATION_RULES.contact_email.pattern.test(validatedRow.contact_email)) {
        rowErrors.push('Invalid email format');
        errors.push({
          row_number: rowNumber,
          field: 'contact_email',
          error_message: 'Invalid email format',
          raw_data: row,
          severity: 'error'
        });
      }

      if (validatedRow.success_rate !== undefined && 
          (validatedRow.success_rate < 0 || validatedRow.success_rate > 1)) {
        rowErrors.push('Success rate must be between 0 and 1');
        errors.push({
          row_number: rowNumber,
          field: 'success_rate',
          error_message: 'Success rate must be between 0 and 1',
          raw_data: row,
          severity: 'error'
        });
      }

      // Set validation status
      if (rowErrors.length > 0) {
        validatedRow._validation_status = 'error';
        validatedRow._validation_errors = rowErrors;
      } else {
        validatedRow._validation_status = 'valid';
      }

      validatedData.push(validatedRow);
    }

    return { data: validatedData, errors };
  };

  // Parse boolean field from various string formats
  const parseBooleanField = (value: any): boolean => {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      const normalized = value.toLowerCase().trim();
      return normalized === 'true' || normalized === 'yes' || normalized === '1';
    }
    return false;
  };

  // Parse float field with validation
  const parseFloatField = (value: any): number | undefined => {
    if (value === undefined || value === null || value === '') return undefined;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? undefined : parsed;
  };

  const handleFileInputClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelection(selectedFile);
    }
  };

  const handleRemoveFile = () => {
    setParseStatus('idle');
    setErrors([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <Upload className="h-16 w-16 text-primary mx-auto" />
        <h3 className="text-2xl font-semibold">Upload CSV File</h3>
        <p className="text-muted-foreground">
          Upload your CSV file to parse and validate the data
        </p>
      </div>

      {/* File Upload Area */}
      {!file && parseStatus === 'idle' && (
        <Card className={`border-2 border-dashed transition-colors ${
          isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
        }`}>
          <CardContent className="p-8">
            <div 
              className="text-center space-y-4 cursor-pointer"
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleFileInputClick}
            >
              <Upload className="h-12 w-12 text-muted-foreground mx-auto" />
              <div className="space-y-2">
                <p className="text-lg font-medium">
                  Drag & drop your CSV file here, or click to browse
                </p>
                <p className="text-sm text-muted-foreground">
                  Supports CSV files up to 10MB
                </p>
              </div>
              <Button variant="outline" className="mt-4">
                <FileText className="h-4 w-4 mr-2" />
                Browse Files
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* File Info Card */}
      {file && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Selected File
              </div>
              {parseStatus === 'idle' && (
                <Button variant="ghost" size="sm" onClick={handleRemoveFile}>
                  <X className="h-4 w-4" />
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(file.size)} • {file.type || 'CSV file'}
                  </p>
                </div>
                <Badge variant={
                  parseStatus === 'completed' ? 'default' :
                  parseStatus === 'error' ? 'destructive' :
                  'secondary'
                }>
                  {parseStatus === 'idle' ? 'Ready' :
                   parseStatus === 'parsing' ? 'Parsing' :
                   parseStatus === 'completed' ? 'Completed' :
                   'Error'}
                </Badge>
              </div>

              {/* Progress Bar */}
              {parseStatus === 'parsing' && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Parsing CSV</span>
                    <span className="text-sm text-muted-foreground">Processing...</span>
                  </div>
                  <Progress className="h-2" />
                </div>
              )}

              {/* Success Message */}
              {parseStatus === 'completed' && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    File parsed successfully
                  </span>
                </div>
              )}

              {/* Error Messages */}
              {parseStatus === 'error' && errors.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-red-600">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {errors.length} error(s) found
                    </span>
                  </div>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {errors.slice(0, 5).map((error, index) => (
                      <p key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                        {error.row_number > 0 && `Row ${error.row_number}: `}
                        {error.error_message}
                      </p>
                    ))}
                    {errors.length > 5 && (
                      <p className="text-sm text-muted-foreground">
                        ... and {errors.length - 5} more errors
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Back Button */}
      {onBack && parseStatus === 'idle' && !file && (
        <div className="flex justify-start">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Template
          </Button>
        </div>
      )}
    </div>
  );
}