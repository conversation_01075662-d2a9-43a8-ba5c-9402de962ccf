"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Download, FileText, Info, CheckCircle } from "lucide-react";
import { CSV_TEMPLATE_FIELDS } from "@/types/import";

interface TemplateDownloadProps {
  onSkipToUpload: () => void;
}

export function TemplateDownload({ onSkipToUpload }: TemplateDownloadProps) {
  const t = useTranslations("links");
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);
    
    try {
      const response = await fetch('/api/admin/import/public-links/template', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to download template');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'public_link_resources_template.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading template:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <FileText className="h-16 w-16 text-primary mx-auto" />
        <h3 className="text-2xl font-semibold">Download CSV Template</h3>
        <p className="text-muted-foreground">
          Get started by downloading our CSV template to structure your data properly
        </p>
      </div>

      {/* Download Action */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            CSV Template
          </CardTitle>
          <CardDescription>
            Download the pre-formatted CSV template with sample data and proper column headers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="font-medium">public_link_resources_template.csv</p>
              <p className="text-sm text-muted-foreground">
                Contains {CSV_TEMPLATE_FIELDS.length} columns with sample data
              </p>
            </div>
            <Button 
              onClick={handleDownload}
              disabled={isDownloading}
              className="min-w-[120px]"
            >
              {isDownloading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Downloading...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Template Structure */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Template Structure
          </CardTitle>
          <CardDescription>
            Understanding the required and optional fields in the CSV template
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Field</TableHead>
                  <TableHead>Required</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Example</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {CSV_TEMPLATE_FIELDS.map((field) => (
                  <TableRow key={field.key}>
                    <TableCell className="font-medium">{field.label}</TableCell>
                    <TableCell>
                      <Badge variant={field.required ? "default" : "secondary"}>
                        {field.required ? "Required" : "Optional"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {field.key === 'is_paid' ? 'Boolean' : 
                       field.key === 'success_rate' ? 'Number (0-1)' :
                       field.key.includes('url') ? 'URL' :
                       field.key.includes('email') ? 'Email' :
                       'Text'}
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {field.example}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Important Notes */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-800">
            <Info className="h-5 w-5" />
            Important Notes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-yellow-800">
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-yellow-600" />
            <div>
              <p className="font-medium">Domain Format</p>
              <p className="text-sm">Use domain-only format (e.g., "example.com" not "https://example.com")</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-yellow-600" />
            <div>
              <p className="font-medium">Boolean Values</p>
              <p className="text-sm">Use "true" or "false" for the is_paid field</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-yellow-600" />
            <div>
              <p className="font-medium">Success Rate</p>
              <p className="text-sm">Use decimal values between 0 and 1 (e.g., 0.85 for 85%)</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 mt-0.5 text-yellow-600" />
            <div>
              <p className="font-medium">CSV Format</p>
              <p className="text-sm">Ensure proper CSV formatting with commas as separators</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Skip to Upload */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-blue-800 font-medium mb-4">
              Already have your CSV file ready?
            </p>
            <Button 
              variant="outline" 
              onClick={onSkipToUpload}
              className="bg-white border-blue-300 text-blue-700 hover:bg-blue-50"
            >
              Skip Template & Upload File
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}