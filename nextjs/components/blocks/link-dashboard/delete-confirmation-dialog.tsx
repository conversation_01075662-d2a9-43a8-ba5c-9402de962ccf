"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Trash2, AlertTriangle, ExternalLink } from "lucide-react";
import { Link } from "@/types/links";

interface DeleteConfirmationDialogProps {
  link: Link;
  onDelete: (link: Link) => void;
  trigger?: React.ReactNode;
  disabled?: boolean;
}

export function DeleteConfirmationDialog({
  link,
  onDelete,
  trigger,
  disabled = false
}: DeleteConfirmationDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(link);
    } finally {
      setIsDeleting(false);
    }
  };

  const getDomain = (link: Link) => {
    if (link.domain) return link.domain;
    try {
      return new URL(link.url).hostname;
    } catch {
      return link.url.replace(/^https?:\/\//, '').split('/')[0];
    }
  };

  const defaultTrigger = (
    <Button 
      variant="ghost" 
      size="sm"
      disabled={disabled}
      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  );

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        {trigger || defaultTrigger}
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-lg">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <AlertDialogTitle>Delete Link</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left space-y-4">
            <p>Are you sure you want to delete this link? This action cannot be undone.</p>
            
            {/* Link Preview */}
            <div className="bg-muted/50 rounded-lg p-3 space-y-2 w-full overflow-hidden">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 min-w-0 overflow-hidden">
                  <p className="font-medium text-foreground text-sm line-clamp-2 break-words">
                    {link.title}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                    <span className="text-xs text-muted-foreground truncate">
                      {getDomain(link)}
                    </span>
                  </div>
                </div>
                <Badge 
                  variant={link.link_type === 'paid' ? 'default' : 'secondary'} 
                  className="text-xs flex-shrink-0"
                >
                  {link.link_type === 'paid' ? 'Paid' : 'Free'}
                </Badge>
              </div>
              
              {/* Stats */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span>DR: {link.dr_score || 'N/A'}</span>
                <span>Traffic: {link.traffic?.toLocaleString() || '0'}</span>
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground">
              This will permanently remove the link from your project and cannot be recovered.
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-3 w-3 mr-2" />
                Delete Link
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 