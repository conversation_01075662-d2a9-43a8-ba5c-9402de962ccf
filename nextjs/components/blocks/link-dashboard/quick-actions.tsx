"use client";

import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Upload, Plus } from "lucide-react";
import { Project } from "@/types/links";

interface QuickActionsProps {
  projects?: Project[];
  selectedProject?: Project | null;
  onProjectChange?: (project: Project | null) => void;
  onImportClick: () => void;
  onAddLinkClick: () => void;
}

export function QuickActions({
  projects = [],
  selectedProject,
  onProjectChange,
  onImportClick,
  onAddLinkClick
}: QuickActionsProps) {
  const t = useTranslations("links");
  const hasProjects = projects.length > 0;

  return (
    <div className="bg-background border border-border rounded-lg p-3 lg:p-4">
      {/* Header - More compact */}
      <div className={`flex items-center justify-between ${hasProjects ? 'mb-3' : 'mb-2'}`}>
        <div>
          <h2 className="text-base lg:text-lg font-semibold text-foreground">Quick Actions</h2>
          {hasProjects && (
            <p className="text-xs lg:text-sm text-muted-foreground hidden sm:block">
              Manage your links and projects efficiently
            </p>
          )}
        </div>
      </div>

      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
        {/* Project Filter - Only show if projects exist and onProjectChange is provided */}
        {onProjectChange && hasProjects && (
          <div className="flex-1 sm:max-w-xs">
            <Select
              value={selectedProject?.id || "all"}
              onValueChange={(value) => {
                if (value === "all") {
                  onProjectChange(null);
                } else {
                  const project = projects.find(p => p.id === value);
                  onProjectChange(project || null);
                }
              }}
            >
              <SelectTrigger className="h-8 text-sm">
                <SelectValue placeholder="Filter by project" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Projects</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Action Buttons - More compact layout */}
        <div className="flex flex-col sm:flex-row gap-2 sm:ml-auto">
          <Button 
            variant="outline" 
            onClick={onImportClick} 
            className="justify-center h-8 text-sm"
            size="sm"
          >
            <Upload className="h-3 w-3 mr-2" />
            Import CSV
          </Button>
          <Button 
            onClick={onAddLinkClick} 
            className="justify-center h-8 text-sm"
            size="sm"
          >
            <Plus className="h-3 w-3 mr-2" />
            Add Link
          </Button>
        </div>
      </div>
    </div>
  );
} 