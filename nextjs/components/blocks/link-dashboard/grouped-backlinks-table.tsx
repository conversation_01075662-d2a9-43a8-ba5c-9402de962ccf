"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  ExternalLink, 
  Plus, 
  MoreHorizontal, 
  Search,
  Filter,
  SortAsc,
  CheckCircle,
  Circle,
  Trash2,
  Archive,
  TrendingUp,
  BarChart3,
  ChevronDown,
  ChevronRight,
  Globe
} from 'lucide-react';
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { getSourceDomainStats } from "@/utils/url-normalization";
import { DiscoveredLinkWithStats } from "@/types/links";

// Extended interface to include management status
interface DiscoveredLinkExtended extends DiscoveredLinkWithStats {
  isInManagedList: boolean;
}

interface GroupedBacklinksTableProps {
  discoveredLinks: DiscoveredLinkExtended[];
  onAddToManagedLinks: (linkId: string) => Promise<void>;
  onScanForLinks: () => void;
  isScanning: boolean;
  onDeleteLink: (linkId: string) => Promise<void>;
  onUpdateLinkStatus: (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => Promise<void>;
}

interface SourceDomainGroup {
  source_url: string;
  count: number;
  links: DiscoveredLinkExtended[];
}

export function GroupedBacklinksTable({
  discoveredLinks,
  onAddToManagedLinks,
  onScanForLinks,
  isScanning,
  onDeleteLink,
  onUpdateLinkStatus
}: GroupedBacklinksTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'count' | 'domain' | 'discovered_at'>('count');
  const [filterType, setFilterType] = useState<'all' | 'dofollow' | 'nofollow' | 'not-managed' | 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED'>('all');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [addingLinks, setAddingLinks] = useState<Set<string>>(new Set());
  const [processingLinks, setProcessingLinks] = useState<Set<string>>(new Set());

  // Group links by normalized source URL
  const sourceGroups: SourceDomainGroup[] = useMemo(() => {
    const filteredLinks = discoveredLinks.filter(link => {
      // Hide archived links from all views unless explicitly filtered for ARCHIVED
      if (link.status === 'ARCHIVED' && filterType !== 'ARCHIVED') {
        return false;
      }

      const matchesSearch = 
        link.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        link.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
        link.anchor_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        link.source_url.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = 
        filterType === 'all' ||
        (filterType === 'dofollow' && link.link_type === 'dofollow') ||
        (filterType === 'nofollow' && link.link_type === 'nofollow') ||
        (filterType === 'not-managed' && !link.isInManagedList) ||
        (filterType === 'NEW' && link.status === 'NEW') ||
        (filterType === 'SUBMITTED' && link.status === 'SUBMITTED') ||
        (filterType === 'INDEXED' && link.status === 'INDEXED') ||
        (filterType === 'ARCHIVED' && link.status === 'ARCHIVED');

      return matchesSearch && matchesFilter;
    });

    const stats = getSourceDomainStats(filteredLinks.map(link => ({
      ...link,
      source_url: link.source_url
    })));

    return stats.sort((a, b) => {
      switch (sortBy) {
        case 'count':
          return b.count - a.count;
        case 'domain':
          return a.source_url.localeCompare(b.source_url);
        case 'discovered_at':
          const aLatest = Math.max(...a.links.map(l => new Date(l.discovered_at).getTime()));
          const bLatest = Math.max(...b.links.map(l => new Date(l.discovered_at).getTime()));
          return bLatest - aLatest;
        default:
          return 0;
      }
    });
  }, [discoveredLinks, searchTerm, filterType, sortBy]);

  const toggleGroupExpansion = (sourceUrl: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(sourceUrl)) {
      newExpanded.delete(sourceUrl);
    } else {
      newExpanded.add(sourceUrl);
    }
    setExpandedGroups(newExpanded);
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'NEW':
        return { text: 'New', color: 'text-blue-600 bg-blue-50 border-blue-200', icon: Circle };
      case 'SUBMITTED':
        return { text: 'Submitted', color: 'text-yellow-600 bg-yellow-50 border-yellow-200', icon: Circle };
      case 'INDEXED':
        return { text: 'Indexed', color: 'text-green-600 bg-green-50 border-green-200', icon: CheckCircle };
      case 'ARCHIVED':
        return { text: 'Archived', color: 'text-gray-600 bg-gray-50 border-gray-200', icon: Archive };
      default:
        return { text: status, color: 'text-gray-600 bg-gray-50 border-gray-200', icon: Circle };
    }
  };

  const handleAddToManagedLinks = async (linkId: string) => {
    if (addingLinks.has(linkId)) return;
    
    setAddingLinks(prev => new Set(prev).add(linkId));
    try {
      await onAddToManagedLinks(linkId);
      toast.success("Link added to managed links");
    } catch (error) {
      toast.error("Failed to add link to managed links");
    } finally {
      setAddingLinks(prev => {
        const newSet = new Set(prev);
        newSet.delete(linkId);
        return newSet;
      });
    }
  };

  const handleUpdateStatus = async (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => {
    if (processingLinks.has(linkId)) return;
    
    setProcessingLinks(prev => new Set(prev).add(linkId));
    try {
      await onUpdateLinkStatus(linkId, status);
      toast.success(`Link status updated to ${status}`);
    } catch (error) {
      toast.error("Failed to update link status");
    } finally {
      setProcessingLinks(prev => {
        const newSet = new Set(prev);
        newSet.delete(linkId);
        return newSet;
      });
    }
  };

  const totalLinks = discoveredLinks.filter(l => l.status !== 'ARCHIVED').length;
  const totalGroups = sourceGroups.length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Discovered Backlinks (Grouped by Source)
            </CardTitle>
            <CardDescription>
              Backlinks grouped by source domain. Each row represents a source website with multiple backlinks.
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex-1">
            <Input
              placeholder="Search by domain, title, or anchor text..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setFilterType('all')}>
                  All Links
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setFilterType('dofollow')}>
                  Dofollow Only
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType('nofollow')}>
                  Nofollow Only
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setFilterType('not-managed')}>
                  Not Managed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType('NEW')}>
                  New Links
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType('SUBMITTED')}>
                  Submitted Links
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType('INDEXED')}>
                  Indexed Links
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType('ARCHIVED')}>
                  Archived Links
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <SortAsc className="h-4 w-4 mr-2" />
                  Sort
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setSortBy('count')}>
                  By Link Count
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy('domain')}>
                  By Domain Name
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSortBy('discovered_at')}>
                  By Latest Discovery
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Grouped Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-8"></TableHead>
                <TableHead>Source Domain</TableHead>
                <TableHead>Backlinks Count</TableHead>
                <TableHead>Latest Status</TableHead>
                <TableHead>DR Score</TableHead>
                <TableHead>Traffic</TableHead>
                <TableHead>Latest Discovery</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sourceGroups.map((group) => {
                const isExpanded = expandedGroups.has(group.source_url);
                const latestLink = group.links.reduce((latest, current) => 
                  new Date(current.discovered_at) > new Date(latest.discovered_at) ? current : latest
                );
                const statusInfo = getStatusInfo(latestLink.status);
                const StatusIcon = statusInfo.icon;
                
                return (
                  <React.Fragment key={group.source_url}>
                    {/* Group Header Row */}
                    <TableRow 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => toggleGroupExpansion(group.source_url)}
                    >
                      <TableCell>
                        {isExpanded ? 
                          <ChevronDown className="h-4 w-4" /> : 
                          <ChevronRight className="h-4 w-4" />
                        }
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {new URL(group.source_url).hostname}
                          </div>
                          <a 
                            href={group.source_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center gap-1 text-xs text-blue-600 hover:underline"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <ExternalLink className="w-3 h-3" />
                            Visit Site
                          </a>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {group.count} backlinks
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <StatusIcon className="w-4 h-4" />
                          <Badge variant="outline" className={statusInfo.color}>
                            {statusInfo.text}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{latestLink.dr_score || '-'}</span>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">
                          {latestLink.traffic?.toLocaleString() || '0'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {new Date(latestLink.discovered_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Add action for the group if needed
                          }}
                        >
                          Manage Group
                        </Button>
                      </TableCell>
                    </TableRow>

                    {/* Expanded Individual Links */}
                    {isExpanded && group.links.map((link) => {
                      const linkStatusInfo = getStatusInfo(link.status);
                      const LinkStatusIcon = linkStatusInfo.icon;
                      const isAdding = addingLinks.has(link.id);
                      const isProcessing = processingLinks.has(link.id);

                      return (
                        <TableRow key={link.id} className="bg-muted/20">
                          <TableCell className="pl-8"></TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm font-medium truncate max-w-xs">
                                {link.title || link.domain}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Anchor: {link.anchor_text}
                              </div>
                              <a 
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center gap-1 text-xs text-blue-600 hover:underline"
                              >
                                <ExternalLink className="w-3 h-3" />
                                View Page
                              </a>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={link.link_type === 'dofollow' ? 'default' : 'secondary'}
                            >
                              {link.link_type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <LinkStatusIcon className="w-4 h-4" />
                              <Badge variant="outline" className={linkStatusInfo.color}>
                                {linkStatusInfo.text}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">{link.dr_score || '-'}</span>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">
                              {link.traffic?.toLocaleString() || '0'}
                            </span>
                          </TableCell>
                          <TableCell>
                            {new Date(link.discovered_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => handleAddToManagedLinks(link.id)}
                                  disabled={isAdding || link.isInManagedList}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  {link.isInManagedList ? 'Already Managed' : 'Add to Managed'}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleUpdateStatus(link.id, 'SUBMITTED')}
                                  disabled={isProcessing}
                                >
                                  Mark as Submitted
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleUpdateStatus(link.id, 'INDEXED')}
                                  disabled={isProcessing}
                                >
                                  Mark as Indexed
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleUpdateStatus(link.id, 'ARCHIVED')}
                                  disabled={isProcessing}
                                  className="text-destructive"
                                >
                                  <Archive className="h-4 w-4 mr-2" />
                                  Archive
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Summary Stats */}
        {totalLinks > 0 && (
          <div className="mt-4 space-y-2 sm:space-y-0 sm:flex sm:items-center sm:justify-between text-xs sm:text-sm text-muted-foreground">
            <div className="flex flex-wrap items-center gap-2 sm:gap-4">
              <span>Total: {totalLinks} backlinks from {totalGroups} domains</span>
              <span>Managed: {discoveredLinks.filter(l => l.isInManagedList).length}</span>
              <span>Unmanaged: {discoveredLinks.filter(l => !l.isInManagedList).length}</span>
            </div>
            <div className="flex flex-wrap items-center gap-2 sm:gap-4">
              <span>Dofollow: {discoveredLinks.filter(l => l.link_type === 'dofollow').length}</span>
              <span>Nofollow: {discoveredLinks.filter(l => l.link_type === 'nofollow').length}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 