"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { FileText, Upload, CheckCircle, AlertCircle, Trash2, Shield } from "lucide-react";
import { toast } from "sonner";

interface GoogleConsoleConfigProps {
  onConfigSaved?: () => void;
}

interface ConfigData {
  id?: number;
  configName: string;
  isActive: boolean;
  created_at?: string;
  updated_at?: string;
  clientEmail?: string;
  project_id?: string;
}

export function GoogleConsoleConfig({ onConfigSaved }: GoogleConsoleConfigProps) {
  const [config, setConfig] = useState<ConfigData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  
  // Form data
  const [configName, setConfigName] = useState("");
  const [jsonInput, setJsonInput] = useState("");
  const [uploadMethod, setUploadMethod] = useState<"json" | "manual">("json");

  useEffect(() => {
    fetchCurrentConfig();
  }, []);

  const fetchCurrentConfig = async () => {
    try {
      const response = await fetch("/api/user-configs/google-search-console");
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error("Error fetching config:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === "application/json") {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const jsonData = JSON.parse(content);
          setJsonInput(JSON.stringify(jsonData, null, 2));
          if (jsonData.project_id) {
            setConfigName(`GSC Config - ${jsonData.project_id}`);
          }
        } catch (error) {
          toast.error("Invalid JSON file");
        }
      };
      reader.readAsText(file);
    } else {
      toast.error("Please upload a valid JSON file");
    }
  };

  const validateAndParseJson = () => {
    try {
      const jsonData = JSON.parse(jsonInput);
      
      const requiredFields = [
        'type', 'project_id', 'private_key_id', 'private_key',
        'client_email', 'client_id', 'auth_uri', 'token_uri'
      ];

      for (const field of requiredFields) {
        if (!jsonData[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      if (jsonData.type !== 'service_account') {
        throw new Error('Invalid service account type');
      }

      return jsonData;
    } catch (error: any) {
      toast.error(`Invalid configuration: ${error.message}`);
      return null;
    }
  };

  const handleTestConnection = async () => {
    const configData = validateAndParseJson();
    if (!configData) return;

    setTesting(true);
    try {
      const response = await fetch("/api/user-configs/google-search-console", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          configName: configName || "Test Configuration",
          configData,
          testConnection: true
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        toast.success("Connection test successful!");
      } else {
        toast.error(result.details || result.error || "Connection test failed");
      }
    } catch (error) {
      toast.error("Connection test failed");
    } finally {
      setTesting(false);
    }
  };

  const handleSaveConfig = async () => {
    if (!configName.trim()) {
      toast.error("Please enter a configuration name");
      return;
    }

    const configData = validateAndParseJson();
    if (!configData) return;

    setSaving(true);
    try {
      const response = await fetch("/api/user-configs/google-search-console", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          configName: configName.trim(),
          configData,
          testConnection: false
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        toast.success("Configuration saved successfully!");
        setShowForm(false);
        setJsonInput("");
        setConfigName("");
        fetchCurrentConfig();
        onConfigSaved?.();
      } else {
        toast.error(result.error || "Failed to save configuration");
      }
    } catch (error) {
      toast.error("Failed to save configuration");
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteConfig = async () => {
    if (!confirm("Are you sure you want to delete this configuration? This will disable Google Search Console integration.")) {
      return;
    }

    try {
      const response = await fetch("/api/user-configs/google-search-console", {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Configuration deleted successfully");
        setConfig(null);
        onConfigSaved?.();
      } else {
        toast.error("Failed to delete configuration");
      }
    } catch (error) {
      toast.error("Failed to delete configuration");
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading configuration...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Google Search Console Integration</h3>
        <p className="text-muted-foreground">
          Connect your Google Search Console to automatically discover backlinks for your verified domains.
        </p>
      </div>

      {/* Security Notice */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>Security Notice:</strong> Your service account keys are encrypted and securely stored in our database. 
          We only use them to access Google Search Console data for your verified domains. 
          You can delete this configuration at any time.
        </AlertDescription>
      </Alert>

      {/* Current Configuration */}
      {config ? (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Configuration Active
                </CardTitle>
                <CardDescription>
                  Google Search Console is configured and ready to use
                </CardDescription>
              </div>
              <Badge variant="outline">Connected</Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Configuration Name</Label>
                <p className="text-sm text-muted-foreground">{config.configName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Project ID</Label>
                <p className="text-sm text-muted-foreground">{config.project_id}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Service Account Email</Label>
                <p className="text-sm text-muted-foreground">{config.clientEmail}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Last Updated</Label>
                <p className="text-sm text-muted-foreground">
                  {config.updated_at ? new Date(config.updated_at).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button 
                variant="outline" 
                onClick={() => setShowForm(true)}
              >
                Update Configuration
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDeleteConfig}
                className="ml-auto"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Configuration
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-orange-500" />
              No Configuration Found
            </CardTitle>
            <CardDescription>
              Configure Google Search Console to enable automatic backlink discovery
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => setShowForm(true)}>
              <Upload className="h-4 w-4 mr-2" />
              Setup Google Search Console
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Configuration Form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>Configure Google Search Console</CardTitle>
            <CardDescription>
              Upload your Google Cloud service account JSON file or paste the configuration manually
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Configuration Name */}
            <div>
              <Label htmlFor="configName">Configuration Name</Label>
              <Input
                id="configName"
                value={configName}
                onChange={(e) => setConfigName(e.target.value)}
                placeholder="e.g., My GSC Config"
                className="mt-1"
              />
            </div>

            {/* Upload Method Selection */}
            <div>
              <Label>Configuration Method</Label>
              <div className="flex gap-4 mt-2">
                <Button
                  variant={uploadMethod === "json" ? "default" : "outline"}
                  onClick={() => setUploadMethod("json")}
                  size="sm"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Upload JSON File
                </Button>
                <Button
                  variant={uploadMethod === "manual" ? "default" : "outline"}
                  onClick={() => setUploadMethod("manual")}
                  size="sm"
                >
                  Manual Input
                </Button>
              </div>
            </div>

            {/* File Upload */}
            {uploadMethod === "json" && (
              <div>
                <Label htmlFor="jsonFile">Service Account JSON File</Label>
                <Input
                  id="jsonFile"
                  type="file"
                  accept=".json"
                  onChange={handleFileUpload}
                  className="mt-1"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Upload the JSON file downloaded from Google Cloud Console
                </p>
              </div>
            )}

            {/* Manual JSON Input */}
            <div>
              <Label htmlFor="jsonInput">Service Account Configuration</Label>
              <Textarea
                id="jsonInput"
                value={jsonInput}
                onChange={(e) => setJsonInput(e.target.value)}
                placeholder={uploadMethod === "json" 
                  ? "JSON content will appear here after file upload..."
                  : 'Paste your service account JSON here...\n{\n  "type": "service_account",\n  "project_id": "your-project",\n  ...\n}'
                }
                rows={10}
                className="mt-1 font-mono text-sm"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Paste the complete service account JSON configuration from Google Cloud Console
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                onClick={handleTestConnection}
                variant="outline"
                disabled={!jsonInput || testing}
              >
                {testing ? "Testing..." : "Test Connection"}
              </Button>
              <Button
                onClick={handleSaveConfig}
                disabled={!jsonInput || !configName || saving}
              >
                {saving ? "Saving..." : "Save Configuration"}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowForm(false);
                  setJsonInput("");
                  setConfigName("");
                }}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            <p><strong>1. Create a Google Cloud Project:</strong></p>
            <p className="text-muted-foreground ml-4">
              Go to Google Cloud Console and create a new project or select an existing one.
            </p>
            
            <p><strong>2. Enable Google Search Console API:</strong></p>
            <p className="text-muted-foreground ml-4">
              In the Google Cloud Console, enable the Google Search Console API for your project.
            </p>
            
            <p><strong>3. Create Service Account:</strong></p>
            <p className="text-muted-foreground ml-4">
              Go to IAM & Admin → Service Accounts → Create Service Account. Download the JSON key file.
            </p>
            
            <p><strong>4. Grant Access in Search Console:</strong></p>
            <p className="text-muted-foreground ml-4">
              In Google Search Console, add the service account email as a user with "Full" permissions.
            </p>
            
            <p><strong>5. Upload Configuration:</strong></p>
            <p className="text-muted-foreground ml-4">
              Upload the JSON file or paste the configuration above to complete the setup.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 