"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Link } from "@/types/links";
import { LinksTable } from "./links-table";
import { LinkResourcesImportDialog } from "./link-resources-import-dialog";

interface AllLinksViewProps {
  links: Link[];
  loading: boolean;
  onEditLink: (link: Link) => void;
  onDeleteLink: (link: Link) => void;
  onAddLink: () => void;
  onImportComplete?: () => void;
  onBatchUpdateDR?: () => Promise<void>;
  onBatchUpdateTraffic?: () => Promise<void>;
  onBatchDiscoverLinks?: () => Promise<void>;
}

export function AllLinksView({
  links,
  loading,
  onEditLink,
  onDeleteLink,
  onAddLink,
  onImportComplete,
  onBatchUpdateDR,
  onBatchUpdateTraffic,
  onBatchDiscoverLinks
}: AllLinksViewProps) {
  return (
    <div className="w-full max-w-none space-y-4 lg:space-y-6">
      {/* Header with Actions - Optimized for mobile */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex-1 min-w-0">
          <h2 className="text-lg font-semibold">Link Resources</h2>
          <p className="text-sm text-muted-foreground">
            {links.length} link{links.length !== 1 ? 's' : ''} total
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          {onImportComplete && (
            <LinkResourcesImportDialog 
              onImportComplete={onImportComplete}
            />
          )}
          <Button onClick={onAddLink} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add Link
          </Button>
        </div>
      </div>

      {/* Links Table - Full width utilization */}
      <div className="w-full overflow-hidden">
        <LinksTable
          links={links}
          loading={loading}
          onEditLink={onEditLink}
          onDeleteLink={onDeleteLink}
          onAddLink={onAddLink}
          onBatchUpdateDR={onBatchUpdateDR}
          onBatchUpdateTraffic={onBatchUpdateTraffic}
          onBatchDiscoverLinks={onBatchDiscoverLinks}
        />
      </div>
    </div>
  );
} 