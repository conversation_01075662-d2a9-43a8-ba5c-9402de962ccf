"use client";

import React, { useState, useEffect } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, RefreshCw } from 'lucide-react';
import { ProjectStats } from "@/types/links";
import { AnalyticsConfigDialog } from "./analytics-config-dialog";
import { toast } from "sonner";

// Import the new components
import { OverviewCards } from "./overview-cards";
import { CoreMetricsTrends } from "./core-metrics-trends";
import { AnalyticsPlatformData } from "./analytics-platform-data";
import { BacklinksTrafficContribution } from "./backlinks-traffic-contribution";

// Import types and utilities
import { MetricsData, AnalyticsData, BacklinkData, AnalyticsConfig, RealTimeData } from "./types";
import { formatLastUpdated, generateCoreTimeSeriesData, generateEmptyAnalyticsData } from "./utils";


interface MetricsChartsProps {
  project_id: string;
  projectName: string;
  domain: string;
  currentDR?: number;
  currentLinks?: number;
  currentPageViews?: number;
  statsData?: ProjectStats[];
  onRefreshStats?: () => Promise<void>;
  onConfigsUpdate?: (configs: any[]) => void;
}


export default function MetricsCharts({ 
  project_id,
  projectName, 
  domain, 
  currentDR = 0, 
  currentLinks = 0, 
  currentPageViews = 0,
  statsData = [],
  onRefreshStats,
  onConfigsUpdate
}: MetricsChartsProps) {
  const [coreTimeRange, setCoreTimeRange] = useState<'4w' | '12w' | '24w'>('12w'); // 周为单位
  const [analyticsTimeRange, setAnalyticsTimeRange] = useState<'7d' | '30d' | '90d'>('30d'); // 天为单位
  const [analyticsSource, setAnalyticsSource] = useState<'google' | 'plausible' | 'umami'>('plausible');
  const [configsLoaded, setConfigsLoaded] = useState(false);
  const [coreMetricsData, setCoreMetricsData] = useState<MetricsData[]>([]);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [backlinksData, setBacklinksData] = useState<BacklinkData[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [realTimeData, setRealTimeData] = useState<RealTimeData>({
    dr_score: currentDR,
    linkCount: currentLinks,
    pageViews: currentPageViews
  });
  
  // Analytics configurations
  const [analyticsConfigs, setAnalyticsConfigs] = useState<AnalyticsConfig[]>([
    {
      provider: 'google',
      api_key: '',
      website_id: '',
      domain: domain,
      isActive: false
    },
    {
      provider: 'plausible',
      api_key: '',
      website_id: '',
      domain: domain,
      isActive: false
    },
    {
      provider: 'umami',
      api_key: '',
      website_id: '',
      base_url: '',
      domain: domain,
      isActive: false
    }
  ]);


  // Load core metrics data from database
  const loadCoreMetricsData = () => {
    try {
      const rangeWeeks = coreTimeRange === '4w' ? 4 : coreTimeRange === '12w' ? 12 : 24;
      
      if (statsData.length > 0) {
        const timeSeriesData = generateCoreTimeSeriesData(statsData, rangeWeeks);
        setCoreMetricsData(timeSeriesData);
        
        // Update real-time data with latest values from database
        const latestData = statsData[0]; // statsData is ordered by checked_at DESC
        setRealTimeData({
          dr_score: latestData?.dr_score || 0,
          linkCount: latestData?.backlink_count || latestData?.total_links || 0,
          pageViews: latestData?.traffic || 0
        });
        setLastUpdated(latestData?.checked_at || new Date().toISOString());
        console.log('Using core metrics data from database:', timeSeriesData);
      } else {
        // No data available, generate empty time series
        const emptyTimeSeriesData = generateCoreTimeSeriesData([], rangeWeeks);
        setCoreMetricsData(emptyTimeSeriesData);
        setRealTimeData({
          dr_score: 0,
          linkCount: 0,
          pageViews: 0
        });
        setLastUpdated(new Date().toISOString());
      }
    } catch (error) {
      console.error('Error loading core metrics data:', error);
      toast.error('核心指标数据加载失败');
    }
  };

  // Load analytics data from external platforms
  const loadAnalyticsData = async (provider?: string, range?: string) => {
    setLoading(true);
    try {
      const selectedProvider = provider || analyticsSource;
      const selectedRange = range || analyticsTimeRange;
      
      const response = await fetch(
        `/api/projects/${project_id}/analytics-data?provider=${selectedProvider}&timeRange=${selectedRange}`
      );
      
      if (response.ok) {
        const data = await response.json();
        if (data.data && data.data.length > 0) {
          setAnalyticsData(data.data);
          console.log('Using analytics data from', selectedProvider, ':', data.data);
        } else {
          // Generate empty analytics data for the time range
          const rangeDays = selectedRange === '7d' ? 7 : selectedRange === '30d' ? 30 : 90;
          const emptyAnalyticsData = generateEmptyAnalyticsData(rangeDays);
          setAnalyticsData(emptyAnalyticsData);
          console.log('No analytics data available, using empty data');
        }
      } else {
        const rangeDays = selectedRange === '7d' ? 7 : selectedRange === '30d' ? 30 : 90;
        const emptyAnalyticsData = generateEmptyAnalyticsData(rangeDays);
        setAnalyticsData(emptyAnalyticsData);
        toast.warning(`${selectedProvider}分析平台暂不可用`);
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
      const rangeDays = analyticsTimeRange === '7d' ? 7 : analyticsTimeRange === '30d' ? 30 : 90;
      const emptyAnalyticsData = generateEmptyAnalyticsData(rangeDays);
      setAnalyticsData(emptyAnalyticsData);
      toast.error('分析平台数据加载失败');
    } finally {
      setLoading(false);
    }
  };


  // Load backlinks data with traffic contribution
  const loadBacklinksData = async () => {
    try {
      // 从 analytics-data API 获取外链流量贡献数据
      const response = await fetch(
        `/api/projects/${project_id}/analytics-data?provider=${analyticsSource}&timeRange=${analyticsTimeRange}`
      );
      
      if (response.ok) {
        const data = await response.json();
        if (data.backlinkTraffic) {
          setBacklinksData(data.backlinkTraffic);
        }
      } else {
        console.warn('Failed to load backlinks traffic data from analytics API');
        // 如果分析数据API失败，尝试使用备用API
        const fallbackResponse = await fetch(
          `/api/projects/${project_id}/discovered-links?includeTraffic=true&provider=${analyticsSource}`
        );
        
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          if (fallbackData.links) {
            setBacklinksData(fallbackData.links);
          }
        }
      }
    } catch (error) {
      console.error('Error loading backlinks data:', error);
    }
  };

  // Refresh all data
  const refreshAllData = async () => {
    setLoading(true);
    try {
      // Call parent refresh function if provided
      if (onRefreshStats) {
        await onRefreshStats();
      }
      
      // Refresh project stats
      const response = await fetch(`/api/projects/${project_id}/stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'refresh' 
        }),
      });

      if (response.ok) {
        toast.success('数据刷新成功');
        // Reload all data
        loadCoreMetricsData();
        await loadAnalyticsData();
        await loadBacklinksData();
      } else {
        const error = await response.json();
        toast.error(`刷新失败: ${error.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('数据刷新失败');
    } finally {
      setLoading(false);
    }
  };

  // Load analytics configs from database
  const loadAnalyticsConfigs = async () => {
    try {
      // Load project-specific analytics configurations
      const response = await fetch(`/api/projects/${project_id}/analytics-config`);
      if (response.ok) {
        const data = await response.json();
        if (data.configs && data.configs.length > 0) {
          const loadedConfigs = analyticsConfigs.map(defaultConfig => {
            const dbConfig = data.configs.find((c: any) => c.provider === defaultConfig.provider);
            
            if (dbConfig) {
              return {
                ...defaultConfig,
                api_key: '***',
                website_id: dbConfig.website_id || '',
                base_url: dbConfig.base_url || '',
                isActive: dbConfig.isActive || false,
                testStatus: 'success' as const,
                lastTested: dbConfig.updated_at
              };
            }
            
            return defaultConfig;
          });
          
          setAnalyticsConfigs(loadedConfigs);
          
          // Set the active provider as the default source
          const activeConfig = loadedConfigs.find(c => c.isActive);
          if (activeConfig) {
            setAnalyticsSource(activeConfig.provider);
          }
          setConfigsLoaded(true);
        } else {
          // If no project-specific configs found, try loading global configs as fallback
          console.log('No project-specific analytics configs found, trying global configs...');
          const globalResponse = await fetch('/api/user-configs/analytics');
          if (globalResponse.ok) {
            const globalData = await globalResponse.json();
            if (globalData.configs && globalData.configs.length > 0) {
              const loadedConfigs = analyticsConfigs.map(defaultConfig => {
                const dbConfig = globalData.configs.find((c: any) => c.provider === defaultConfig.provider);
                
                if (dbConfig) {
                  return {
                    ...defaultConfig,
                    api_key: '***',
                    website_id: dbConfig.website_id || '',
                    base_url: dbConfig.base_url || '',
                    isActive: dbConfig.isActive || false,
                    testStatus: 'success' as const,
                    lastTested: dbConfig.updated_at
                  };
                }
                
                return defaultConfig;
              });
              
              setAnalyticsConfigs(loadedConfigs);
              
              // Set the active provider as the default source
              const activeConfig = loadedConfigs.find(c => c.isActive);
              if (activeConfig) {
                setAnalyticsSource(activeConfig.provider);
              }
            }
          }
          setConfigsLoaded(true);
        }
      } else {
        setConfigsLoaded(true);
      }
    } catch (error) {
      console.error('Failed to load analytics configs:', error);
      setConfigsLoaded(true); // Mark as loaded even on error to prevent infinite waiting
    }
  };

  // Load analytics configs on mount
  useEffect(() => {
    loadAnalyticsConfigs();
  }, []);

  // Load core metrics when component mounts or dependencies change
  useEffect(() => {
    loadCoreMetricsData();
  }, [coreTimeRange, project_id, statsData]);

  // Load analytics data when provider or range changes (only after configs are loaded)
  useEffect(() => {
    if (configsLoaded) {
      loadAnalyticsData();
      loadBacklinksData();
    }
  }, [analyticsSource, analyticsTimeRange, project_id, configsLoaded]);

  // Handle analytics configs update
  const handleConfigsUpdate = async (configs: AnalyticsConfig[]) => {
    setAnalyticsConfigs(configs);
    
    if (onConfigsUpdate) {
      onConfigsUpdate(configs);
    }
    
    // Switch to an active provider if current one becomes inactive
    const currentConfig = configs.find(c => c.provider === analyticsSource);
    if (!currentConfig?.isActive) {
      const activeConfig = configs.find(c => c.isActive);
      if (activeConfig) {
        setAnalyticsSource(activeConfig.provider);
        // Analytics data will be reloaded automatically by useEffect when analyticsSource changes
        return;
      }
    }
    
    // Only reload if provider didn't change (to avoid double loading)
    await loadAnalyticsData();
    await loadBacklinksData();
  };


  // Get active analytics providers for the select dropdown
  const activeProviders = analyticsConfigs.filter(config => config.isActive);

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Top Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshAllData}
            disabled={loading}
            className={`transition-all duration-200 ${loading ? 'bg-blue-50 border-blue-200' : 'hover:bg-blue-50 hover:border-blue-200'}`}
          >
            <RefreshCw className={`w-4 h-4 mr-2 transition-transform duration-500 ${loading ? 'animate-spin text-blue-600' : 'text-gray-600'}`} />
            <span className={loading ? 'text-blue-600 font-medium' : ''}>
              {loading ? '正在刷新...' : '刷新所有数据'}
            </span>
          </Button>

          <AnalyticsConfigDialog
            project_id={project_id}
            projectDomain={domain}
            configs={analyticsConfigs}
            onConfigsUpdate={handleConfigsUpdate}
          />
        </div>

        <Badge variant="outline" className="text-xs">
          <Calendar className="w-3 h-3 mr-1" />
          数据更新：{formatLastUpdated(lastUpdated)}
        </Badge>
      </div>

      {/* Section 1: Overview Cards - Current Values from Database */}
      <OverviewCards realTimeData={realTimeData} />

      {/* Section 2: Core Metrics Trends - Weekly Data from Database */}
      <CoreMetricsTrends 
        coreMetricsData={coreMetricsData}
        coreTimeRange={coreTimeRange}
        onTimeRangeChange={(value: '4w' | '12w' | '24w') => setCoreTimeRange(value)}
      />

      {/* Section 3: External Analytics Platform Data - Daily Data */}
      <AnalyticsPlatformData 
        analyticsData={analyticsData}
        analyticsTimeRange={analyticsTimeRange}
        analyticsSource={analyticsSource}
        activeProviders={activeProviders}
        onTimeRangeChange={(value: '7d' | '30d' | '90d') => setAnalyticsTimeRange(value)}
        onSourceChange={(value: 'google' | 'plausible' | 'umami') => setAnalyticsSource(value)}
      />

      {/* Section 4: Backlinks Traffic Contribution */}
      <BacklinksTrafficContribution 
        backlinksData={backlinksData}
        analyticsSource={analyticsSource}
      />
    </div>
  );
}
