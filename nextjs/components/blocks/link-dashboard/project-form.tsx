"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Project, ProjectCategoryStats } from "@/types/links";
import { getCategoryConfig, getPredefinedCategories } from "@/lib/project-categories";
import { X } from "lucide-react";

interface ProjectFormProps {
  project?: Project | null;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export function ProjectForm({ project, onSubmit, onCancel }: ProjectFormProps) {
  const t = useTranslations("links");
  const [formData, setFormData] = useState({
    name: project?.name || "",
    domain: project?.domain || "",
    description: project?.description || "",
    category: project?.category || "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [userCategories, setUserCategories] = useState<ProjectCategoryStats[]>([]);
  const [customCategoryInput, setCustomCategoryInput] = useState("");
  const [showCustomInput, setShowCustomInput] = useState(false);

  // 获取用户已有的分类
  useEffect(() => {
    const fetchUserCategories = async () => {
      try {
        const response = await fetch('/api/projects/categories');
        if (response.ok) {
          const data = await response.json();
          setUserCategories(data.categories || []);
        }
      } catch (error) {
        console.error('Error fetching user categories:', error);
      }
    };

    fetchUserCategories();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const submitData = {
        name: formData.name,
        domain: formData.domain,
        description: formData.description || undefined,
        category: formData.category || undefined,
      };
      await onSubmit(submitData);
    } finally {
      setSubmitting(false);
    }
  };

  // 处理自定义分类输入
  const handleCustomCategorySubmit = () => {
    if (customCategoryInput.trim()) {
      setFormData(prev => ({
        ...prev,
        category: customCategoryInput.trim()
      }));
      setCustomCategoryInput("");
      setShowCustomInput(false);
    }
  };

  // 获取所有可用的分类选项
  const getAllCategories = () => {
    const predefined = getPredefinedCategories();
    const userDefined = userCategories.map(cat => cat.category);
    const allCategories = Array.from(new Set([...predefined, ...userDefined]));
    return allCategories.sort();
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {project ? t("project.edit_project") : t("project.create_project")}
          </DialogTitle>
          <DialogDescription>
            {project 
              ? "Update project information and settings"
              : "Create a new project to organize your links"
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t("project.name")}</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Project name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="domain">{t("project.domain")}</Label>
            <Input
              id="domain"
              value={formData.domain}
              onChange={(e) => handleChange("domain", e.target.value)}
              placeholder="example.com (will be normalized automatically)"
              required
            />
            <p className="text-xs text-muted-foreground">
              Domain will be automatically normalized (www prefix removed, lowercased)
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t("project.description")}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange("description", e.target.value)}
              placeholder="Optional description of your project"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">项目分类</Label>
            <div className="space-y-3">
              {/* 当前选中的分类显示 */}
              {formData.category && (
                <div className="flex items-center gap-2">
                  <Badge 
                    variant="secondary" 
                    className="flex items-center gap-1"
                  >
                    {(() => {
                      const categoryConfig = getCategoryConfig(formData.category);
                      const IconComponent = categoryConfig.icon;
                      return (
                        <>
                          <IconComponent className="h-3 w-3" />
                          {formData.category}
                          <button
                            type="button"
                            onClick={() => setFormData(prev => ({ ...prev, category: "" }))}
                            className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </>
                      );
                    })()}
                  </Badge>
                </div>
              )}

              {/* 分类选择器 */}
              {!formData.category && (
                <div className="space-y-2">
                  {!showCustomInput ? (
                    <Select
                      value=""
                      onValueChange={(value) => {
                        if (value === "__custom__") {
                          setShowCustomInput(true);
                        } else {
                          setFormData(prev => ({ ...prev, category: value }));
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择或创建分类" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="__custom__">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            创建新分类...
                          </div>
                        </SelectItem>
                        {getAllCategories().map((category) => {
                          const categoryConfig = getCategoryConfig(category);
                          const IconComponent = categoryConfig.icon;
                          const userCat = userCategories.find(c => c.category === category);
                          return (
                            <SelectItem key={category} value={category}>
                              <div className="flex items-center gap-2">
                                <IconComponent className="h-4 w-4" />
                                <span>{category}</span>
                                {userCat && (
                                  <span className="text-xs text-muted-foreground">
                                    ({userCat.project_count}个项目)
                                  </span>
                                )}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex gap-2">
                      <Input
                        value={customCategoryInput}
                        onChange={(e) => setCustomCategoryInput(e.target.value)}
                        placeholder="输入新的分类名称"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleCustomCategorySubmit();
                          } else if (e.key === 'Escape') {
                            setShowCustomInput(false);
                            setCustomCategoryInput("");
                          }
                        }}
                        autoFocus
                      />
                      <Button
                        type="button"
                        size="sm"
                        onClick={handleCustomCategorySubmit}
                        disabled={!customCategoryInput.trim()}
                      >
                        确定
                      </Button>
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setShowCustomInput(false);
                          setCustomCategoryInput("");
                        }}
                      >
                        取消
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              为项目选择一个分类，便于组织和管理
            </p>
          </div>

          <div className="space-y-4 pt-4 border-t">
            <h4 className="text-sm font-medium text-muted-foreground">
              Analytics Platform (Optional)
            </h4>
            <p className="text-xs text-muted-foreground">
              You can configure analytics integration later from the Integrations tab
            </p>

          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("actions.cancel")}
            </Button>
            <Button type="submit" disabled={submitting}>
              {submitting ? "Saving..." : (project ? t("actions.update") : t("actions.create"))}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 