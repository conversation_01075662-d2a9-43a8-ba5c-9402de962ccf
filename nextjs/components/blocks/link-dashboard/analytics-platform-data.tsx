"use client";

import React, { useState, useEffect } from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, LineChart, Line } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AnalyticsData, AnalyticsConfig } from "./types";
import { formatDate, formatNumber } from "./utils";
import { Search, TrendingUp, Link, Globe, BarChart3, Eye, Users, MousePointer } from 'lucide-react';

interface AnalyticsPlatformDataProps {
  analyticsData: AnalyticsData[];
  analyticsTimeRange: '7d' | '30d' | '90d';
  analyticsSource: 'google' | 'plausible' | 'umami';
  activeProviders: AnalyticsConfig[];
  onTimeRangeChange: (value: '7d' | '30d' | '90d') => void;
  onSourceChange: (value: 'google' | 'plausible' | 'umami') => void;
}

const COLORS = [
  'hsl(var(--chart-primary))',
  'hsl(var(--chart-secondary))',
  'hsl(var(--chart-tertiary))',
  'hsl(var(--chart-quaternary))',
  'hsl(var(--chart-quinary))',
  'hsl(var(--dashboard-accent-1))',
  'hsl(var(--dashboard-accent-2))',
  'hsl(var(--dashboard-accent-3))'
];

export function AnalyticsPlatformData({
  analyticsData,
  analyticsTimeRange,
  analyticsSource,
  activeProviders,
  onTimeRangeChange,
  onSourceChange
}: AnalyticsPlatformDataProps) {
  const [internalLinksData, setInternalLinksData] = useState<{
    totalTop10: any[];
    recentTop10: any[];
  }>({ totalTop10: [], recentTop10: [] });
  const [keywordData, setKeywordData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // 检查是否有Google Analytics配置
  const hasGoogleAnalytics = activeProviders.some(config => config.provider === 'google');

  // 当activeTab是keywords但没有Google Analytics时，自动切换到overview
  useEffect(() => {
    if (activeTab === 'keywords' && !hasGoogleAnalytics) {
      setActiveTab('overview');
    }
  }, [activeTab, hasGoogleAnalytics]);

  useEffect(() => {
    fetchInternalLinksData();
    if (hasGoogleAnalytics) {
      fetchKeywordData();
    } else {
      setLoading(false);
    }
  }, [analyticsTimeRange, analyticsSource, hasGoogleAnalytics]);

  const fetchInternalLinksData = async () => {
    try {
      // 从API获取真实的内部链接数据
      const projectId = window.location.pathname.split('/').find(segment => 
        segment.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
      );
      
      if (!projectId) {
        console.error('Project ID not found in URL');
        setInternalLinksData({ totalTop10: [], recentTop10: [] });
        return;
      }
      
      console.log(`Fetching internal links data for project ${projectId}, timeRange: ${analyticsTimeRange}`);
      
      const response = await fetch(
        `/api/projects/${projectId}/internal-links?timeRange=${analyticsTimeRange}&provider=plausible`
      );
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error ${response.status}: ${errorText}`);
        
        // 尝试解析错误信息
        let errorMessage = `HTTP ${response.status}`;
        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.message || errorJson.error || errorMessage;
        } catch (parseError) {
          errorMessage = errorText || errorMessage;
        }
        
        throw new Error(errorMessage);
      }
      
      const result = await response.json();
      console.log('Internal links API result:', result);
      
      if (result.success) {
        setInternalLinksData({
          totalTop10: result.data.totalTop10 || [],
          recentTop10: result.data.recentTop10 || []
        });
        
        console.log(`Loaded ${result.data.totalTop10?.length || 0} total pages, ${result.data.recentTop10?.length || 0} recent pages`);
      } else {
        throw new Error(result.error || 'Failed to fetch data');
      }
    } catch (error) {
      console.error('Error fetching internal links data:', error);
      
      // 设置空数据并在控制台显示详细错误
      setInternalLinksData({ totalTop10: [], recentTop10: [] });
      
      // 如果是配置问题，给出提示
      if (error.message?.includes('No active') || error.message?.includes('not found')) {
        console.warn('💡 提示: 请检查项目的 Plausible Analytics 配置是否正确');
      }
    }
  };

  const fetchKeywordData = async () => {
    try {
      // Mock Google Search Console keyword data
      const mockKeywords = [
        { keyword: 'link tracking', impressions: 12450, clicks: 542, ctr: 4.35, position: 3.2 },
        { keyword: 'seo analytics', impressions: 8760, clicks: 398, ctr: 4.54, position: 2.8 },
        { keyword: 'backlink monitor', impressions: 6840, clicks: 276, ctr: 4.04, position: 4.1 },
        { keyword: 'domain authority', impressions: 5230, clicks: 234, ctr: 4.48, position: 3.7 },
        { keyword: 'link building tools', impressions: 4560, clicks: 198, ctr: 4.34, position: 3.5 },
        { keyword: 'website analytics', impressions: 3890, clicks: 167, ctr: 4.29, position: 4.2 },
        { keyword: 'organic traffic', impressions: 3240, clicks: 145, ctr: 4.47, position: 3.1 },
        { keyword: 'serp tracking', impressions: 2780, clicks: 98, ctr: 3.53, position: 5.4 }
      ];
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      setKeywordData(mockKeywords);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching keyword data:', error);
      setLoading(false);
    }
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Main Analytics Chart */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-muted-foreground" />
              网站流量分析
            </div>
            <Badge variant="secondary" className="text-xs">
              {analyticsSource === 'google' ? 'Google Analytics' : 
               analyticsSource === 'plausible' ? 'Plausible Analytics' : 
               'Umami Analytics'}
            </Badge>
          </CardTitle>
          <CardDescription>
            来自 {analyticsSource === 'google' ? 'Google Analytics' : 
                   analyticsSource === 'plausible' ? 'Plausible' : 
                   'Umami'} 的详细流量数据
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={320}>
            <AreaChart data={analyticsData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis 
                dataKey="date" 
                tickFormatter={formatDate}
                fontSize={12}
              />
              <YAxis 
                tickFormatter={formatNumber}
                fontSize={12}
              />
              <Tooltip 
                labelFormatter={(value) => `日期: ${formatDate(value as string)}`}
                formatter={(value: number, name: string) => [
                  value.toLocaleString(), 
                  name === 'pageViews' ? '页面浏览量' : 
                  name === 'sessions' ? '会话数' : '用户数'
                ]}
              />
              <Area
                type="monotone"
                dataKey="pageViews"
                stackId="1"
                stroke="#64748b"
                fill="#64748b"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="sessions"
                stackId="2"
                stroke="#475569"
                fill="#475569"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="users"
                stackId="3"
                stroke="#334155"
                fill="#334155"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderInternalLinksTab = () => {
    // 根据时间范围选择显示的数据和标题
    const isRecentData = analyticsTimeRange === '7d';
    const currentData = isRecentData ? internalLinksData.recentTop10 : internalLinksData.totalTop10;
    const dataTitle = isRecentData 
      ? "近期增量" 
      : `总量 (${analyticsTimeRange === '30d' ? '30天' : '90天'})`;

    return renderPageAnalysisContent(currentData, dataTitle);
  };

  const renderPageAnalysisContent = (data: any[], title: string) => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pie Chart */}
        <Card className="border-2">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Globe className="w-5 h-5 text-muted-foreground" />
              {title} - 页面流量分布
            </CardTitle>
            <CardDescription>内部页面访问量占比</CardDescription>
          </CardHeader>
          <CardContent>
            {data && data.length > 0 ? (
              <ResponsiveContainer width="100%" height={280}>
                <PieChart>
                  <Pie
                    data={data}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={2}
                    dataKey="views"
                  >
                    {data.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value: number) => [`${value.toLocaleString()} 次访问`, '页面浏览量']}
                    labelFormatter={(label) => `页面: ${label}`}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-280 flex items-center justify-center text-muted-foreground">
                暂无数据
              </div>
            )}
          </CardContent>
        </Card>

        {/* Bar Chart */}
        <Card className="border-2">
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-muted-foreground" />
              {title} - 页面性能排行
            </CardTitle>
            <CardDescription>按访问量排序的页面</CardDescription>
          </CardHeader>
          <CardContent>
            {data && data.length > 0 ? (
              <ResponsiveContainer width="100%" height={280}>
                <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis dataKey="path" fontSize={12} />
                  <YAxis fontSize={12} />
                  <Tooltip
                    formatter={(value: number) => [value.toLocaleString(), '访问量']}
                    labelFormatter={(label) => `页面: ${label}`}
                  />
                  <Bar dataKey="views" fill="#64748b" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-280 flex items-center justify-center text-muted-foreground">
                暂无数据
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Table */}
      <Card className="border-2 border-indigo-100/50 shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Link className="w-5 h-5 text-indigo-600" />
            {title} - 内部页面详细分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data && data.length > 0 ? (
            <div className="space-y-3">
              {data.map((page, index) => (
                <div key={page.path} className="flex items-center justify-between p-4 rounded-md border hover:border-primary/50 transition-all duration-200 bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <div>
                      <div className="font-semibold">{page.path}</div>
                      <div className="text-sm text-muted-foreground">
                        {page.percentage ? `${page.percentage.toFixed(1)}% 总流量` : '占比计算中'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-foreground">{page.views.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">
                      跳出率: {page.bounce_rate ? `${page.bounce_rate.toFixed(1)}%` : 'N/A'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              暂无页面数据，请确保已正确配置 Plausible Analytics
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const renderKeywordsTab = () => (
    <div className="space-y-6">
      {/* Keywords Performance Chart */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Search className="w-5 h-5 text-muted-foreground" />
            关键词表现趋势
          </CardTitle>
          <CardDescription>Google Search Console 关键词点击率和排名</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={320}>
            <LineChart data={keywordData.slice(0, 5)}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis dataKey="keyword" fontSize={12} />
              <YAxis yAxisId="left" fontSize={12} />
              <YAxis yAxisId="right" orientation="right" fontSize={12} />
              <Tooltip />
              <Line yAxisId="left" type="monotone" dataKey="clicks" stroke="#64748b" strokeWidth={3} />
              <Line yAxisId="right" type="monotone" dataKey="ctr" stroke="#475569" strokeWidth={3} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Keywords Table */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Eye className="w-5 h-5 text-muted-foreground" />
            Google Search Console 关键词数据
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {keywordData.map((keyword, index) => (
              <div key={keyword.keyword} className="p-4 rounded-md border hover:border-primary/50 transition-all duration-200 bg-muted/30">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="text-lg font-semibold">{keyword.keyword}</div>
                    <Badge variant="outline" className="text-xs">
                      排名 #{keyword.position.toFixed(1)}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-foreground">{keyword.clicks}</div>
                    <div className="text-sm text-muted-foreground">点击量</div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">展示次数</div>
                    <div className="font-medium">{keyword.impressions.toLocaleString()}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">点击率</div>
                    <div className="font-medium">{keyword.ctr}%</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">平均排名</div>
                    <div className="font-medium">#{keyword.position.toFixed(1)}</div>
                  </div>
                </div>
                <div className="mt-2">
                  <Progress value={keyword.ctr} className="h-2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-muted rounded-md border">
            <BarChart3 className="w-5 h-5 text-muted-foreground" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-foreground">
              分析平台数据
            </h3>
            <Badge variant="secondary" className="text-xs mt-1">实时数据分析</Badge>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={analyticsTimeRange} onValueChange={onTimeRangeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">过去7天</SelectItem>
              <SelectItem value="30d">过去30天</SelectItem>
              <SelectItem value="90d">过去90天</SelectItem>
            </SelectContent>
          </Select>

          <Select 
            value={analyticsSource} 
            onValueChange={onSourceChange}
            disabled={activeProviders.length === 0}
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {activeProviders.length > 0 ? (
                activeProviders.map(config => (
                  <SelectItem key={config.provider} value={config.provider}>
                    {config.provider === 'google' ? 'Google Analytics' :
                     config.provider === 'plausible' ? 'Plausible' :
                     'Umami'}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="none" disabled>
                  未配置分析平台
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className={`grid w-full ${hasGoogleAnalytics ? 'grid-cols-3' : 'grid-cols-2'}`}>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            总览
          </TabsTrigger>
          <TabsTrigger value="internal-links" className="flex items-center gap-2">
            <Link className="w-4 h-4" />
            内部链接
          </TabsTrigger>
          <TabsTrigger 
            value="keywords" 
            className="flex items-center gap-2"
            disabled={!hasGoogleAnalytics}
            style={hasGoogleAnalytics ? {} : { display: 'none' }}
          >
            <Search className="w-4 h-4" />
            关键词
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-6">
          {renderOverviewTab()}
        </TabsContent>
        
        <TabsContent value="internal-links" className="mt-6">
          {loading ? (
            <div className="animate-pulse space-y-4">
              <div className="h-64 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          ) : (
            renderInternalLinksTab()
          )}
        </TabsContent>
        
        <TabsContent value="keywords" className="mt-6">
          {!hasGoogleAnalytics ? (
            <Card className="border-2">
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Search className="w-5 h-5 text-muted-foreground" />
                  关键词分析不可用
                </CardTitle>
                <CardDescription>需要配置 Google Analytics 才能访问关键词数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="text-muted-foreground mb-4">
                    关键词分析功能仅支持 Google Analytics (Google Search Console)
                  </div>
                  <div className="text-sm text-muted-foreground">
                    请在项目设置中配置 Google Analytics 集成以访问此功能
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : loading ? (
            <div className="animate-pulse space-y-4">
              <div className="h-64 bg-gray-200 rounded"></div>
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
          ) : (
            renderKeywordsTab()
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}