"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Project } from "@/types/links";
import { Integration, IntegrationFormData } from "@/types/integrations";

interface IntegrationFormProps {
  integration?: Integration;
  projects: Project[];
  onSubmit: (data: IntegrationFormData) => Promise<void>;
  onCancel: () => void;
}

export function IntegrationForm({ 
  integration, 
  projects, 
  onSubmit, 
  onCancel 
}: IntegrationFormProps) {
  const t = useTranslations("integrations");
  const [formData, setFormData] = useState<IntegrationFormData>({
    project_id: integration?.project_id || (projects.length > 0 ? projects[0].id : ""),
    provider: integration?.provider || "umami",
    website_id: integration?.website_id || "",
    api_key: integration?.api_key || "",
    base_url: integration?.base_url || "",
    name: integration?.name || ""
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (field: keyof IntegrationFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting integration:", error);
    } finally {
      setLoading(false);
    }
  };

  const getProviderConfig = () => {
    switch (formData.provider) {
      case "umami":
        return {
          websiteIdLabel: "Website ID",
          websiteIdPlaceholder: "Enter your Umami Website ID",
          apiKeyLabel: "API Key (Optional)",
          apiKeyPlaceholder: "Enter your Umami API key",
          baseUrlLabel: "Base URL (Optional)",
          baseUrlPlaceholder: "https://your-umami-instance.com",
          showBaseUrl: true
        };
      case "plausible":
        return {
          websiteIdLabel: "Site Domain",
          websiteIdPlaceholder: "example.com",
          apiKeyLabel: "API Key",
          apiKeyPlaceholder: "Enter your Plausible API key",
          baseUrlLabel: "Base URL (Optional)",
          baseUrlPlaceholder: "Leave empty for plausible.io",
          showBaseUrl: true
        };
      default:
        return {
          websiteIdLabel: "Website ID",
          websiteIdPlaceholder: "Enter website ID",
          apiKeyLabel: "API Key",
          apiKeyPlaceholder: "Enter API key",
          baseUrlLabel: "Base URL",
          baseUrlPlaceholder: "Enter base URL",
          showBaseUrl: true
        };
    }
  };

  const config = getProviderConfig();

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {integration ? "Edit" : "Add"} Analytics Integration
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription>
            Connect your analytics platform to track referral traffic for your project.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Integration Name */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="name">Integration Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                placeholder="e.g., Main Website Analytics"
                required
              />
            </div>

            {/* Project Selection */}
            <div className="space-y-2">
              <Label htmlFor="project_id">Project</Label>
              <Select 
                value={formData.project_id} 
                onValueChange={(value) => handleChange("project_id", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Provider Selection */}
            <div className="space-y-2">
              <Label htmlFor="provider">Analytics Provider</Label>
              <Select 
                value={formData.provider} 
                onValueChange={(value) => handleChange("provider", value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select provider" />
                </SelectTrigger>
                <SelectContent>
                  {/* <SelectItem value="umami">Umami Analytics</SelectItem> */}
                  <SelectItem value="plausible">Plausible Analytics</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.provider && (
              <>
                {/* Website ID */}
                <div className="space-y-2">
                  <Label htmlFor="website_id">{config.websiteIdLabel}</Label>
                  <Input
                    id="website_id"
                    value={formData.website_id}
                    onChange={(e) => handleChange("website_id", e.target.value)}
                    placeholder={config.websiteIdPlaceholder}
                    required
                  />
                </div>

                {/* API Key */}
                <div className="space-y-2">
                  <Label htmlFor="api_key">{config.apiKeyLabel}</Label>
                  <Input
                    id="api_key"
                    type="password"
                    value={formData.api_key}
                    onChange={(e) => handleChange("api_key", e.target.value)}
                    placeholder={config.apiKeyPlaceholder}
                    required={formData.provider === "plausible"}
                  />
                </div>

                {/* Base URL */}
                {config.showBaseUrl && (
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="base_url">{config.baseUrlLabel}</Label>
                    <Input
                      id="base_url"
                      value={formData.base_url}
                      onChange={(e) => handleChange("base_url", e.target.value)}
                      placeholder={config.baseUrlPlaceholder}
                    />
                  </div>
                )}
              </>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : integration ? "Update" : "Add"} Integration
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 