"use client";

import { Card, CardContent } from "@/components/ui/card";
import { 
  Link2, 
  Eye, 
  BarChart3, 
  T<PERSON>dingUp,
  ArrowUpIcon,
  ArrowDownIcon
} from "lucide-react";

interface StatsCardsProps {
  totalLinks: number;
  indexedLinks: number;
  avgDrScore: number;
  monthlyTraffic: number;
  trends?: {
    totalLinks?: { value: number; isPositive: boolean };
    indexedLinks?: { value: number; isPositive: boolean };
    avgDrScore?: { value: number; isPositive: boolean };
    monthlyTraffic?: { value: number; isPositive: boolean };
  };
}

export function StatsCards({ 
  totalLinks, 
  indexedLinks, 
  avgDrScore, 
  monthlyTraffic,
  trends = {}
}: StatsCardsProps) {
  const stats = [
    {
      label: "Total Links",
      value: totalLinks,
      icon: Link2,
      iconBg: "hsl(var(--dashboard-accent-1) / 0.15)",
      iconColor: "hsl(var(--dashboard-accent-1))",
      trend: trends.totalLinks
    },
    {
      label: "Indexed Links",
      value: indexedLinks,
      icon: Eye,
      iconBg: "hsl(var(--data-positive) / 0.15)",
      iconColor: "hsl(var(--data-positive))",
      trend: trends.indexedLinks,
      subtitle: indexedLinks > 0 ? `${((indexedLinks / totalLinks) * 100).toFixed(0)}% index rate` : "0% index rate"
    },
    {
      label: "Avg DR Score",
      value: avgDrScore,
      icon: BarChart3,
      iconBg: "hsl(var(--chart-secondary) / 0.15)",
      iconColor: "hsl(var(--chart-secondary))",
      trend: trends.avgDrScore,
      subtitle: "vs last month"
    },
    {
      label: "Monthly Traffic",
      value: monthlyTraffic,
      icon: TrendingUp,
      iconBg: "hsl(var(--chart-tertiary) / 0.15)",
      iconColor: "hsl(var(--chart-tertiary))",
      trend: trends.monthlyTraffic,
      subtitle: "vs last month"
    }
  ];

  const formatValue = (value: number, label: string) => {
    if (label === "Monthly Traffic" && value >= 1000) {
      return (value / 1000).toFixed(1) + "k";
    }
    return value.toLocaleString();
  };

  const TrendIndicator = ({ trend }: { trend?: { value: number; isPositive: boolean } }) => {
    if (!trend) return null;

    const Icon = trend.isPositive ? ArrowUpIcon : ArrowDownIcon;
    const colorClass = trend.isPositive ? "text-slate-600 dark:text-slate-400" : "text-slate-500 dark:text-slate-500";

    return (
      <div className={`flex items-center gap-1 text-sm ${colorClass}`}>
        <Icon className="h-3 w-3" />
        <span>{Math.abs(trend.value)}%</span>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon;

        return (
          <Card key={index} className="border-2 hover:border-primary/20 hover:shadow-md transition-all duration-200">
            <CardContent className="p-3 lg:p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="text-lg lg:text-2xl font-bold text-foreground mb-1 truncate">
                    {formatValue(stat.value, stat.label)}
                  </div>
                  <div className="text-xs lg:text-sm text-muted-foreground font-medium truncate">
                    {stat.label}
                  </div>
                  {stat.subtitle && (
                    <div className="text-xs text-muted-foreground mt-1 truncate">
                      {stat.subtitle}
                    </div>
                  )}
                  {stat.trend && (
                    <div className="mt-2">
                      <TrendIndicator trend={stat.trend} />
                    </div>
                  )}
                </div>
                <div
                  className="p-2 lg:p-3 rounded-md border flex-shrink-0"
                  style={{ backgroundColor: stat.iconBg }}
                >
                  <Icon
                    className="h-4 w-4 lg:h-6 lg:w-6"
                    style={{ color: stat.iconColor }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
} 