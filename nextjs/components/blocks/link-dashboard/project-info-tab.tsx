"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Copy, 
  Globe, 
  Calendar,
  Shield,
  RefreshCw,
  Plus,
  Trash2,
  ExternalLink,
  ImageIcon,
  Clock
} from "lucide-react";
import { Project, ProjectInfo } from "@/types/links";
import { toast } from "sonner";

interface ProjectInfoTabProps {
  project: Project;
  onProjectUpdate?: (project: Project) => void;
}

export function ProjectInfoTab({ project, onProjectUpdate }: ProjectInfoTabProps) {
  const [projectInfo, setProjectInfo] = useState<ProjectInfo>(project.info || {});
  const [loading, setLoading] = useState(false);
  const [domainInfoLoading, setDomainInfoLoading] = useState(false);
  const [faviconLoading, setFaviconLoading] = useState(false);

  // Form state
  const [introduction, setIntroduction] = useState(projectInfo.introduction || '');
  const [sitemaps, setSitemaps] = useState<string[]>(projectInfo.sitemaps || []);
  const [robotsTxt, setRobotsTxt] = useState(projectInfo.robotsTxt || '');
  const [newSitemapUrl, setNewSitemapUrl] = useState('');

  useEffect(() => {
    if (project.info) {
      setProjectInfo(project.info);
      setIntroduction(project.info.introduction || '');
      setSitemaps(project.info.sitemaps || []);
      setRobotsTxt(project.info.robotsTxt || '');
    }
  }, [project.info]);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} 已复制到剪贴板`);
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const fetchDomainInfo = async () => {
    setDomainInfoLoading(true);
    try {
      const response = await fetch(`/api/domain/whois?domain=${encodeURIComponent(project.domain)}`);
      
      if (response.ok) {
        const data = await response.json();
        
        // Check if the service is unavailable
        if (data.serviceUnavailable) {
          toast.info(`域名信息服务暂时不可用: ${data.serviceError || 'WHOIS服务维护中'}`);
          return;
        }
        
        const updatedInfo = {
          ...projectInfo,
          domainInfo: {
            registrar: data.registrar,
            createdDate: data.createdDate,
            expiryDate: data.expiryDate,
            status: data.status || [],
            nameServers: data.nameServers || []
          }
        };
        
        await updateProjectInfo(updatedInfo);
        toast.success('域名信息已更新');
      } else {
        const error = await response.json();
        toast.error(`获取域名信息失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Error fetching domain info:', error);
      toast.error('获取域名信息失败');
    } finally {
      setDomainInfoLoading(false);
    }
  };

  const fetchFavicon = async () => {
    setFaviconLoading(true);
    try {
      const response = await fetch(`/api/domain/favicon?domain=${encodeURIComponent(project.domain)}`);
      
      if (response.ok) {
        const data = await response.json();
        const updatedInfo = {
          ...projectInfo,
          favicon: data.favicon
        };
        
        await updateProjectInfo(updatedInfo);
        toast.success('网站图标已更新');
      } else {
        toast.error('获取网站图标失败');
      }
    } catch (error) {
      console.error('Error fetching favicon:', error);
      toast.error('获取网站图标失败');
    } finally {
      setFaviconLoading(false);
    }
  };

  const updateProjectInfo = async (info: ProjectInfo) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/projects/${project.id}/info`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(info),
      });

      if (response.ok) {
        const data = await response.json();
        setProjectInfo(info);
        onProjectUpdate?.(data.project);
      } else {
        const error = await response.json();
        toast.error(`更新失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating project info:', error);
      toast.error('更新项目信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    const updatedInfo = {
      ...projectInfo,
      introduction,
      sitemaps,
      robotsTxt
    };
    
    await updateProjectInfo(updatedInfo);
    toast.success('项目信息已保存');
  };

  const addSitemap = () => {
    if (newSitemapUrl.trim()) {
      setSitemaps([...sitemaps, newSitemapUrl.trim()]);
      setNewSitemapUrl('');
    }
  };

  const removeSitemap = (index: number) => {
    setSitemaps(sitemaps.filter((_, i) => i !== index));
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getDaysUntilExpiry = () => {
    if (!projectInfo.domainInfo?.expiryDate) return null;
    const expiry = new Date(projectInfo.domainInfo.expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getExpiryProgress = () => {
    if (!projectInfo.domainInfo?.createdDate || !projectInfo.domainInfo?.expiryDate) return 0;
    const created = new Date(projectInfo.domainInfo.createdDate);
    const expiry = new Date(projectInfo.domainInfo.expiryDate);
    const now = new Date();
    const totalTime = expiry.getTime() - created.getTime();
    const elapsedTime = now.getTime() - created.getTime();
    return Math.min(100, Math.max(0, (elapsedTime / totalTime) * 100));
  };

  const daysUntilExpiry = getDaysUntilExpiry();
  const expiryProgress = getExpiryProgress();

  return (
    <div className="space-y-6">
      {/* 网站图标和基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            网站图标
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            {projectInfo.favicon ? (
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <img 
                    src={projectInfo.favicon} 
                    alt="网站图标" 
                    className="w-8 h-8 rounded border border-border object-cover"
                    onError={(e) => {
                      e.currentTarget.src = `https://www.google.com/s2/favicons?domain=${project.domain}&sz=64`;
                    }}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <Input 
                    value={projectInfo.favicon} 
                    readOnly 
                    className="text-sm"
                  />
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(projectInfo.favicon!, '图标链接')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(projectInfo.favicon!, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-3 text-muted-foreground">
                <div className="w-8 h-8 rounded border border-dashed border-border flex items-center justify-center">
                  <ImageIcon className="h-4 w-4" />
                </div>
                <span>未设置网站图标</span>
              </div>
            )}
            <Button
              onClick={fetchFavicon}
              disabled={faviconLoading}
              size="sm"
            >
              {faviconLoading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              {faviconLoading ? '获取中...' : '自动获取'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 项目介绍 */}
      <Card>
        <CardHeader>
          <CardTitle>项目介绍</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="输入项目介绍..."
            value={introduction}
            onChange={(e) => setIntroduction(e.target.value)}
            rows={4}
            className="mb-3"
          />
        </CardContent>
      </Card>

      {/* Sitemap 配置 */}
      <Card>
        <CardHeader>
          <CardTitle>Sitemap 链接</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex gap-2">
              <Input
                placeholder="输入 sitemap.xml 链接"
                value={newSitemapUrl}
                onChange={(e) => setNewSitemapUrl(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && addSitemap()}
              />
              <Button onClick={addSitemap} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {sitemaps.length > 0 && (
              <div className="space-y-2">
                {sitemaps.map((sitemap, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 border rounded">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <span className="flex-1 text-sm">{sitemap}</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(sitemap, 'Sitemap 链接')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(sitemap, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeSitemap(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Robots.txt 配置 */}
      <Card>
        <CardHeader>
          <CardTitle>Robots.txt 链接</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="输入 robots.txt 链接"
              value={robotsTxt}
              onChange={(e) => setRobotsTxt(e.target.value)}
            />
            {robotsTxt && (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(robotsTxt, 'Robots.txt 链接')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(robotsTxt, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 域名注册信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            域名注册信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          {projectInfo.domainInfo ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">注册商</label>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">{projectInfo.domainInfo.registrar || 'N/A'}</span>
                    {projectInfo.domainInfo.registrar && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(projectInfo.domainInfo!.registrar!, '注册商')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">注册时间</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{formatDate(projectInfo.domainInfo.createdDate)}</span>
                  </div>
                </div>
              </div>

              {projectInfo.domainInfo.expiryDate && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">到期时间</label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{formatDate(projectInfo.domainInfo.expiryDate)}</span>
                      {daysUntilExpiry !== null && (
                        <Badge variant={daysUntilExpiry < 30 ? "destructive" : daysUntilExpiry < 90 ? "secondary" : "default"}>
                          {daysUntilExpiry > 0 ? `还有 ${daysUntilExpiry} 天` : '已过期'}
                        </Badge>
                      )}
                    </div>
                    <Progress value={expiryProgress} className="h-2" />
                  </div>
                </div>
              )}

              {projectInfo.domainInfo.nameServers && projectInfo.domainInfo.nameServers.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">DNS 服务器</label>
                  <div className="space-y-1">
                    {projectInfo.domainInfo.nameServers.slice(0, 4).map((ns, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <span className="text-sm font-mono">{ns}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(ns, 'DNS 服务器')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <Shield className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>暂无域名注册信息</p>
            </div>
          )}
          
          <div className="flex justify-end mt-4">
            <Button
              onClick={fetchDomainInfo}
              disabled={domainInfoLoading}
              size="sm"
            >
              {domainInfoLoading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              {domainInfoLoading ? '获取中...' : '刷新域名信息'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 保存按钮 */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={loading}
          className="min-w-[120px]"
        >
          {loading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
          {loading ? '保存中...' : '保存设置'}
        </Button>
      </div>
    </div>
  );
}