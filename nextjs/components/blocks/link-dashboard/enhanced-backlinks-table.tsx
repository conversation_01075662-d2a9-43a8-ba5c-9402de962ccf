"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  ExternalLink, 
  Plus, 
  MoreHorizontal, 
  Search,
  Filter,
  SortAsc,
  CheckCircle,
  Circle,
  Trash2,
  Archive,
  TrendingUp,
  BarChart3,
  Check,
  X
} from 'lucide-react';
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

interface DiscoveredLinkExtended {
  id: string;
  url: string;
  title: string;
  domain: string;
  anchorText: string;
  link_type: 'dofollow' | 'nofollow';
  discoveredAt: string;
  sourceUrl: string;
  isActive: boolean;
  status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED';
  dr_score?: number;
  traffic?: number; // Total domain traffic (website's own traffic)
  isInManagedList: boolean;
  is_indexed?: boolean;
  // Traffic contribution from analytics platforms
  referral_traffic?: number; // Traffic from this specific link source
  analytics_source?: 'plausible' | 'google-analytics' | 'umami' | 'manual';
  traffic_period?: string;
  last_traffic_update?: string;
  traffic_contribution_percentage?: number;
}

interface EnhancedBacklinksTableProps {
  discoveredLinks: DiscoveredLinkExtended[];
  onAddToManagedLinks: (linkId: string) => Promise<void>;
  onScanForLinks: () => void;
  isScanning: boolean;
  onDeleteLink: (linkId: string) => Promise<void>;
  onUpdateLinkStatus: (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => Promise<void>;
}

export default function EnhancedBacklinksTable({
  discoveredLinks,
  onAddToManagedLinks,
  onScanForLinks,
  isScanning,
  onDeleteLink,
  onUpdateLinkStatus
}: EnhancedBacklinksTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'discoveredAt' | 'dr_score' | 'domain'>('discoveredAt');
  const [filterType, setFilterType] = useState<'all' | 'dofollow' | 'nofollow' | 'not-managed' | 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED'>('all');
  const [addingLinks, setAddingLinks] = useState<Set<string>>(new Set());
  const [processingLinks, setProcessingLinks] = useState<Set<string>>(new Set());
  const [checkingIndexLinks, setCheckingIndexLinks] = useState<Set<string>>(new Set());

  const filteredAndSortedLinks = discoveredLinks
    .filter(link => {
      // Hide archived links from all views unless explicitly filtered for ARCHIVED
      if (link.status === 'ARCHIVED' && filterType !== 'ARCHIVED') {
        return false;
      }

      const matchesSearch = 
        link.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        link.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
        link.anchorText.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = 
        filterType === 'all' ||
        (filterType === 'dofollow' && link.link_type === 'dofollow') ||
        (filterType === 'nofollow' && link.link_type === 'nofollow') ||
        (filterType === 'not-managed' && !link.isInManagedList) ||
        (filterType === 'NEW' && link.status === 'NEW') ||
        (filterType === 'SUBMITTED' && link.status === 'SUBMITTED') ||
        (filterType === 'INDEXED' && link.status === 'INDEXED') ||
        (filterType === 'ARCHIVED' && link.status === 'ARCHIVED');

      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'discoveredAt':
          return new Date(b.discoveredAt).getTime() - new Date(a.discoveredAt).getTime();
        case 'dr_score':
          return (b.dr_score || 0) - (a.dr_score || 0);
        case 'domain':
          return a.domain.localeCompare(b.domain);
        default:
          return 0;
      }
    });

  const handleAddToManagedLinks = async (linkId: string) => {
    setAddingLinks(prev => new Set(prev).add(linkId));
    try {
      await onAddToManagedLinks(linkId);
      toast.success('外链已添加到资源总表');
    } catch (error) {
      toast.error('添加外链失败，请重试');
    } finally {
      setAddingLinks(prev => {
        const newSet = new Set(prev);
        newSet.delete(linkId);
        return newSet;
      });
    }
  };

  const handleDeleteLink = async (linkId: string) => {
    setProcessingLinks(prev => new Set(prev).add(linkId));
    try {
      await onDeleteLink(linkId);
      toast.success('外链已删除');
    } catch (error) {
      toast.error('删除外链失败，请重试');
    } finally {
      setProcessingLinks(prev => {
        const newSet = new Set(prev);
        newSet.delete(linkId);
        return newSet;
      });
    }
  };

  const handleUpdateStatus = async (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => {
    setProcessingLinks(prev => new Set(prev).add(linkId));
    try {
      await onUpdateLinkStatus(linkId, status);
      toast.success('状态已更新');
    } catch (error) {
      toast.error('更新状态失败，请重试');
    } finally {
      setProcessingLinks(prev => {
        const newSet = new Set(prev);
        newSet.delete(linkId);
        return newSet;
      });
    }
  };

  const handleCheckIndex = async (linkId: string) => {
    setCheckingIndexLinks(prev => new Set(prev).add(linkId));
    try {
      const response = await fetch(`/api/discovered-links/${linkId}/check-index`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();
      
      if (response.ok) {
        const { data } = result;
        const projectDomain = data?.projectDomain;
        const sourceDomain = data?.sourceDomain;
        
        if (data?.isIndexed) {
          toast.success(`域名收录检查完成：项目域名 ${projectDomain} 在 ${sourceDomain} 中已被收录，共找到 ${data.indexedPages || 0} 个相关页面`);
        } else {
          toast.success(`域名收录检查完成：项目域名 ${projectDomain} 在 ${sourceDomain} 中暂未被收录`);
        }
        // Optionally refresh the links to show updated status
        // You may want to add a callback to refresh the parent component data
      } else if (response.status === 401) {
        toast.error('权限不足：仅管理员可执行域名收录检查');
      } else if (response.status === 408) {
        toast.error('检查超时：请求处理时间过长，请稍后重试');
      } else {
        toast.error(result.error || '域名收录检查失败');
      }
    } catch (error) {
      console.error('Error checking domain index:', error);
      toast.error('域名收录检查失败，请重试');
    } finally {
      setCheckingIndexLinks(prev => {
        const newSet = new Set(prev);
        newSet.delete(linkId);
        return newSet;
      });
    }
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('zh-CN');
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'NEW':
        return { text: '新发现', color: 'bg-slate-100 text-slate-700 dark:bg-slate-800 dark:text-slate-300', icon: Circle };
      case 'SUBMITTED':
        return { text: '已提交', color: 'bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-slate-200', icon: Circle };
      case 'INDEXED':
        return { text: '已收录', color: 'bg-slate-300 text-slate-900 dark:bg-slate-600 dark:text-slate-100', icon: CheckCircle };
      case 'ARCHIVED':
        return { text: '已归档', color: 'bg-muted text-muted-foreground', icon: Archive };
      default:
        return { text: '未知', color: 'bg-muted text-muted-foreground', icon: Circle };
    }
  };

  const getAnalyticsSourceInfo = (source?: string) => {
    switch (source) {
      case 'plausible':
        return { text: 'Plausible', color: 'bg-slate-100 text-slate-700 dark:bg-slate-800 dark:text-slate-300' };
      case 'google-analytics':
        return { text: 'Google Analytics', color: 'bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-slate-200' };
      case 'umami':
        return { text: 'Umami', color: 'bg-slate-100 text-slate-700 dark:bg-slate-800 dark:text-slate-300' };
      case 'manual':
        return { text: '手动输入', color: 'bg-muted text-muted-foreground' };
      default:
        return { text: '未知来源', color: 'bg-muted text-muted-foreground' };
    }
  };

  // Domain normalization utility
  const normalizeDomain = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return domain.replace(/^www\./, '').toLowerCase();
    } catch {
      return url.replace(/^www\./, '').toLowerCase();
    }
  };

  // Truncate text utility
  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Get traffic data display based on analytics source
  const getTrafficDataDisplay = (link: DiscoveredLinkExtended) => {
    if (link.referral_traffic !== undefined) {
      return {
        value: link.referral_traffic.toLocaleString(),
        label: '引荐流量',
        source: link.analytics_source,
        percentage: link.traffic_contribution_percentage
      };
    }
    if (link.traffic) {
      return {
        value: link.traffic.toLocaleString(),
        label: '网站流量',
        source: 'semrush'
      };
    }
    return null;
  };



  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-lg">外链发现</CardTitle>
            <CardDescription className="text-sm">
              自动发现的外部链接 ({filteredAndSortedLinks.length} / {discoveredLinks.length})
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <div className="flex-1">
              <Input
                placeholder="搜索域名、标题或锚文本..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>

            <div className="flex gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                    <Filter className="w-4 h-4 mr-2" />
                    过滤
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setFilterType('all')}>
                    全部链接
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setFilterType('NEW')}>
                    新发现
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterType('SUBMITTED')}>
                    已提交
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterType('INDEXED')}>
                    已收录
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterType('ARCHIVED')}>
                    已归档
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setFilterType('dofollow')}>
                    Dofollow 链接
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterType('nofollow')}>
                    Nofollow 链接
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setFilterType('not-managed')}>
                    未管理链接
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                    <SortAsc className="w-4 h-4 mr-2" />
                    排序
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setSortBy('discoveredAt')}>
                    按发现时间
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('dr_score')}>
                    按DR评分
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('domain')}>
                    按域名
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

            </div>
          </div>
        </div>

        {filteredAndSortedLinks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {discoveredLinks.length === 0 ? (
              <div>
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>暂无发现的外链</p>
                <p className="text-sm">点击"扫描外链"开始自动发现</p>
              </div>
            ) : (
              <div>
                <p>没有找到匹配的结果</p>
                <p className="text-sm">尝试调整搜索条件或过滤器</p>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Mobile Card View */}
            <div className="block lg:hidden space-y-4">
              {filteredAndSortedLinks.map((link) => {
                const linkStatus = getStatusInfo(link.status);
                const LinkStatusIcon = linkStatus.icon;
                const isAdding = addingLinks.has(link.id);
                const isProcessing = processingLinks.has(link.id);

                return (
                  <Card key={link.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        {/* Header with title */}
                        <div className="flex items-start gap-3">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-sm line-clamp-2">
                              {link.domain}
                            </h3>
                            <p className="text-xs text-muted-foreground mt-1">
                              {truncateText(link.title, 60)}
                            </p>
                          </div>
                          <div 
                            className={`flex items-center gap-1 cursor-pointer ${
                              link.is_indexed ? 'text-green-600' : 'text-red-500 hover:text-red-600'
                            }`}
                            onClick={() => !link.is_indexed && handleCheckIndex(link.id)}
                            title={link.is_indexed ? '已收录' : '点击检查收录状态'}
                          >
                            {checkingIndexLinks.has(link.id) ? (
                              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                            ) : link.is_indexed ? (
                              <Check className="w-4 h-4" />
                            ) : (
                              <X className="w-4 h-4" />
                            )}
                          </div>
                        </div>

                        {/* Anchor text and link type */}
                        <div className="space-y-2">
                          <div className="text-xs text-muted-foreground">
                            锚文本: {link.anchorText}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant={link.link_type === 'dofollow' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {link.link_type}
                              </Badge>
                              {link.dr_score && (
                                <span className="text-xs font-medium">DR: {link.dr_score}</span>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {formatDate(link.discoveredAt)}
                            </span>
                          </div>
                        </div>

                        {/* Traffic data */}
                        <div className="space-y-2">
                          {(() => {
                            const trafficData = getTrafficDataDisplay(link);
                            return trafficData ? (
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 text-xs">
                                  <TrendingUp className="w-3 h-3 text-blue-600" />
                                  <span className="text-blue-600 font-medium">
                                    {trafficData.label}: {trafficData.value}
                                  </span>
                                  {trafficData.percentage && (
                                    <span className="text-muted-foreground">
                                      ({trafficData.percentage.toFixed(1)}%)
                                    </span>
                                  )}
                                </div>
                                {trafficData.source && trafficData.source !== 'semrush' && (
                                  <div className="flex items-center gap-2">
                                    <BarChart3 className="w-3 h-3" />
                                    <Badge variant="outline" className={`${getAnalyticsSourceInfo(trafficData.source).color} text-xs`}>
                                      {getAnalyticsSourceInfo(trafficData.source).text}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div className="text-xs text-muted-foreground">
                                -
                              </div>
                            );
                          })()} 
                        </div>

                        {/* Actions */}
                        <div className="flex items-center justify-between pt-2 border-t">
                          <div className="flex items-center gap-2">
                            <a 
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-1 text-xs text-blue-600 hover:underline"
                            >
                              <ExternalLink className="w-3 h-3" />
                              查看链接
                            </a>
                            <a 
                              href={link.sourceUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-1 text-xs text-muted-foreground hover:underline"
                            >
                              <ExternalLink className="w-3 h-3" />
                              来源
                            </a>
                          </div>
                          <div className="flex items-center gap-2">
                            {!link.isInManagedList && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleAddToManagedLinks(link.id)}
                                disabled={isAdding || isProcessing}
                                className="h-7 px-2 text-xs"
                              >
                                {isAdding ? (
                                  <>
                                    <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                                    添加中
                                  </>
                                ) : (
                                  <>
                                    <Plus className="w-3 h-3" />
                                    添加
                                  </>
                                )}
                              </Button>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'NEW')}>
                                  标记为新发现
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'SUBMITTED')}>
                                  标记为已提交
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'INDEXED')}>
                                  标记为已收录
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'ARCHIVED')}>
                                  归档（从列表中移除）
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteLink(link.id)}
                                  className="text-destructive"
                                  disabled={isProcessing}
                                >
                                  {isProcessing ? (
                                    <>
                                      <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                                      删除中...
                                    </>
                                  ) : (
                                    <>
                                      <Trash2 className="w-3 h-3 mr-2" />
                                      删除
                                    </>
                                  )}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Desktop Table View */}
            <div className="hidden lg:block border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>链接信息</TableHead>
                    <TableHead>来源标题</TableHead>
                    <TableHead>流量数据</TableHead>
                    <TableHead>链接类型</TableHead>
                    <TableHead>DR评分</TableHead>
                    <TableHead>收录状态</TableHead>
                    <TableHead>发现时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedLinks.map((link) => {
                    const linkStatus = getStatusInfo(link.status);
                    const LinkStatusIcon = linkStatus.icon;
                    const isAdding = addingLinks.has(link.id);
                    const isProcessing = processingLinks.has(link.id);

                    return (
                      <TableRow key={link.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium text-sm truncate max-w-xs">
                              {link.domain}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              锚文本: {link.anchorText}
                            </div>
                            <a 
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-1 text-xs text-blue-600 hover:underline"
                            >
                              <ExternalLink className="w-3 h-3" />
                              查看链接
                            </a>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm max-w-xs truncate" title={link.title}>
                            {truncateText(link.title, 50)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {(() => {
                              const trafficData = getTrafficDataDisplay(link);
                              return trafficData ? (
                                <div className="space-y-1">
                                  <div className="flex items-center gap-1">
                                    <TrendingUp className="w-3 h-3 text-blue-600" />
                                    <span className="text-sm font-medium text-blue-600">
                                      {trafficData.value}
                                    </span>
                                    {trafficData.percentage && (
                                      <span className="text-xs text-muted-foreground">
                                        ({trafficData.percentage.toFixed(1)}%)
                                      </span>
                                    )}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {trafficData.label}
                                  </div>
                                  {trafficData.source && trafficData.source !== 'semrush' && (
                                    <div className="flex items-center gap-1">
                                      <BarChart3 className="w-3 h-3" />
                                      <Badge variant="outline" className={`${getAnalyticsSourceInfo(trafficData.source).color} text-xs`}>
                                        {getAnalyticsSourceInfo(trafficData.source).text}
                                      </Badge>
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="text-xs text-muted-foreground">
                                  -
                                </div>
                              );
                            })()} 
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={link.link_type === 'dofollow' ? 'default' : 'secondary'}
                          >
                            {link.link_type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {link.dr_score ? (
                            <div className="flex items-center gap-1">
                              <span className="font-medium">{link.dr_score}</span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div 
                            className={`flex items-center justify-center cursor-pointer ${
                              link.is_indexed ? 'text-green-600' : 'text-red-500 hover:text-red-600'
                            }`}
                            onClick={() => !link.is_indexed && handleCheckIndex(link.id)}
                            title={link.is_indexed ? '已收录' : '点击检查收录状态'}
                          >
                            {checkingIndexLinks.has(link.id) ? (
                              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                            ) : link.is_indexed ? (
                              <Check className="w-4 h-4" />
                            ) : (
                              <X className="w-4 h-4" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatDate(link.discoveredAt)}</div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            {!link.isInManagedList && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleAddToManagedLinks(link.id)}
                                disabled={isAdding || isProcessing}
                                className="flex items-center gap-1"
                              >
                                {isAdding ? (
                                  <>
                                    <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                                    添加中
                                  </>
                                ) : (
                                  <>
                                    <Plus className="w-3 h-3" />
                                    添加
                                  </>
                                )}
                              </Button>
                            )}
                            
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" disabled={isProcessing}>
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem asChild>
                                  <a 
                                    href={link.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-2"
                                  >
                                    <ExternalLink className="w-4 h-4" />
                                    访问链接
                                  </a>
                                </DropdownMenuItem>
                                <DropdownMenuItem asChild>
                                  <a 
                                    href={link.sourceUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-2"
                                  >
                                    <ExternalLink className="w-4 h-4" />
                                    访问来源页面
                                  </a>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'NEW')}>
                                  标记为新发现
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'SUBMITTED')}>
                                  标记为已提交
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'INDEXED')}>
                                  标记为已收录
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateStatus(link.id, 'ARCHIVED')}>
                                  归档（从列表中移除）
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleDeleteLink(link.id)}
                                  className="text-red-600"
                                  disabled={isProcessing}
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </>
        )}

        {discoveredLinks.length > 0 && (
          <div className="mt-4 space-y-2 sm:space-y-0 sm:flex sm:items-center sm:justify-between text-xs sm:text-sm text-muted-foreground">
            <div className="flex flex-wrap items-center gap-2 sm:gap-4">
              <span>总计: {discoveredLinks.length} 个外链</span>
              <span>已管理: {discoveredLinks.filter(l => l.isInManagedList).length} 个</span>
              <span>未管理: {discoveredLinks.filter(l => !l.isInManagedList).length} 个</span>
            </div>
            <div className="flex flex-wrap items-center gap-2 sm:gap-4">
              <span>Dofollow: {discoveredLinks.filter(l => l.link_type === 'dofollow').length}</span>
              <span>Nofollow: {discoveredLinks.filter(l => l.link_type === 'nofollow').length}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 