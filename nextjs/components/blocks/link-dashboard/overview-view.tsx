"use client";

import { StatsCards } from "./stats-cards";
import { QuickActions } from "./quick-actions";
import { Project } from "@/types/links";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Link2, 
  Plus, 
  BarChart3, 
  TrendingUp, 
  Globe,
  ExternalLink,
  FileText,
  Target,
  Archive,
  ArchiveRestore,
  MoreHorizontal,
  Trash2
} from "lucide-react";
import { ProjectDeleteConfirmationDialog } from "./project-delete-confirmation-dialog";

interface OverviewViewProps {
  projects: Project[];
  selectedProject: Project | null;
  onProjectChange: (project: Project | null) => void;
  onImportClick: () => void;
  onAddLinkClick: () => void;
  onArchiveProject?: (projectId: string, archived: boolean) => Promise<void>;
  onDeleteProject?: (projectId: string) => Promise<void>;
  stats: {
    totalLinks: number;
    indexedLinks: number;
    avgDrScore: number;
    monthlyTraffic: number;
  };
}

// Empty state component for better UX when no projects exist
function EmptyProjectsState({ onAddLinkClick, onImportClick }: { onAddLinkClick: () => void; onImportClick: () => void }) {
  return (
    <div className="space-y-8">
      {/* Main empty state */}
      <div className="text-center py-8 px-4">
        <div className="flex justify-center mb-6">
          <div className="relative">
            <div className="bg-primary/10 p-4 rounded-full">
              <Globe className="h-8 w-8 text-primary" />
            </div>
            <div className="absolute -top-1 -right-1 bg-background border-2 border-primary/20 p-1 rounded-full">
              <Plus className="h-3 w-3 text-primary" />
            </div>
          </div>
        </div>
        <h3 className="text-xl font-semibold mb-2">Start Managing Your Links</h3>
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          Create projects to organize your backlinks, track performance, and optimize your link building strategy.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={onAddLinkClick} className="gap-2">
            <Plus className="h-4 w-4" />
            Add Your First Link
          </Button>
          <Button variant="outline" onClick={onImportClick} className="gap-2">
            <ExternalLink className="h-4 w-4" />
            Import from CSV
          </Button>
        </div>
      </div>

      {/* Feature highlights */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="border-2 border-dashed border-border">
          <CardContent className="p-6 text-center">
            <div className="bg-muted p-3 rounded-md w-fit mx-auto mb-4 border">
              <Link2 className="h-6 w-6 text-muted-foreground" />
            </div>
            <h4 className="font-semibold mb-2">Organize Links</h4>
            <p className="text-sm text-muted-foreground">
              Group your backlinks by projects and domains for better management
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 border-dashed border-border">
          <CardContent className="p-6 text-center">
            <div className="bg-muted p-3 rounded-md w-fit mx-auto mb-4 border">
              <BarChart3 className="h-6 w-6 text-muted-foreground" />
            </div>
            <h4 className="font-semibold mb-2">Track Performance</h4>
            <p className="text-sm text-muted-foreground">
              Monitor DR scores, traffic, and indexing status of your links
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 border-dashed border-border">
          <CardContent className="p-6 text-center">
            <div className="bg-muted p-3 rounded-md w-fit mx-auto mb-4 border">
              <Target className="h-6 w-6 text-muted-foreground" />
            </div>
            <h4 className="font-semibold mb-2">Optimize Strategy</h4>
            <p className="text-sm text-muted-foreground">
              Analyze data to improve your link building and SEO efforts
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export function OverviewView({
  projects,
  selectedProject,
  onProjectChange,
  onImportClick,
  onAddLinkClick,
  onArchiveProject,
  onDeleteProject,
  stats
}: OverviewViewProps) {
  const activeProjects = projects.filter(p => !p.is_archived);
  const archivedProjects = projects.filter(p => p.is_archived);
  const hasProjects = projects.length > 0;
  const hasActiveProjects = activeProjects.length > 0;
  const hasArchivedProjects = archivedProjects.length > 0;
  const hasLinks = stats.totalLinks > 0;

  const handleArchiveProject = async (projectId: string, archived: boolean, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onArchiveProject) {
      await onArchiveProject(projectId, archived);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (onDeleteProject) {
      await onDeleteProject(projectId);
    }
  };

  return (
    <div className="w-full max-w-none space-y-4 lg:space-y-5">
      {/* Stats Cards - Always show, but make more compact when no data */}
      <div className="w-full">
        <StatsCards
          totalLinks={stats.totalLinks}
          indexedLinks={stats.indexedLinks}
          avgDrScore={stats.avgDrScore}
          monthlyTraffic={stats.monthlyTraffic}
        />
      </div>

      {/* Quick Actions - Make more compact and contextual */}
      {/* <div className="w-full">
        <QuickActions
          projects={projects}
          selectedProject={selectedProject}
          onProjectChange={onProjectChange}
          onImportClick={onImportClick}
          onAddLinkClick={onAddLinkClick}
        />
      </div> */}

      {/* Conditional content based on data availability */}
      {hasActiveProjects ? (
        /* Recent Projects Overview - Show when active projects exist */
        <div className="w-full space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Recent Projects</h3>
            {activeProjects.length > 6 && (
              <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80">
                View all {activeProjects.length} projects →
              </Button>
            )}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {activeProjects.slice(0, 6).map((project) => (
              <Card
                key={project.id}
                className="cursor-pointer hover:shadow-md hover:border-primary transition-all group relative border-2"
                onClick={() => onProjectChange(project)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                      {project.name}
                    </h4>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md border">
                        {project.total_links} links
                      </span>
                      {onArchiveProject && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => handleArchiveProject(project.id, true, e)}
                            >
                              <Archive className="h-4 w-4 mr-2" />
                              Archive Project
                            </DropdownMenuItem>
                            <ProjectDeleteConfirmationDialog
                              project={project}
                              onDelete={handleDeleteProject}
                              trigger={
                                <div className="flex items-center w-full px-2 py-1.5 text-sm text-destructive hover:text-destructive cursor-pointer rounded-sm hover:bg-accent">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Project
                                </div>
                              }
                            />
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mb-3 truncate">
                    {project.domain}
                  </p>
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">
                      {project.indexed_links} indexed
                    </span>
                    <span className="text-primary font-medium group-hover:underline">
                      View Details →
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : !hasLinks ? (
        /* Empty state when no projects and no links */
        <EmptyProjectsState 
          onAddLinkClick={onAddLinkClick}
          onImportClick={onImportClick}
        />
      ) : (
        /* Show getting started tips when links exist but no projects */
        <div className="space-y-6">
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="bg-primary/10 p-2 rounded-lg">
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold mb-2">Organize Your Links with Projects</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    You have {stats.totalLinks} links but no projects yet. Create projects to better organize and track your backlinks by domain or campaign.
                  </p>
                  <Button size="sm" variant="outline" className="gap-2">
                    <Plus className="h-4 w-4" />
                    Create Your First Project
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Archived Projects Section - Display at bottom */}
      {hasArchivedProjects && (
        <div className="w-full space-y-4 mt-8 pt-6 border-t border-border/50">
          <div className="flex items-center gap-2">
            <Archive className="h-4 w-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold text-muted-foreground">Archived Projects</h3>
            <span className="text-sm text-muted-foreground">({archivedProjects.length})</span>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {archivedProjects.map((project) => (
              <Card
                key={project.id}
                className="cursor-pointer hover:shadow-lg hover:border-primary/50 transition-all group relative opacity-75"
                onClick={() => onProjectChange(project)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                      {project.name}
                    </h4>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                        {project.total_links} links
                      </span>
                      {onArchiveProject && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => handleArchiveProject(project.id, false, e)}
                            >
                              <ArchiveRestore className="h-4 w-4 mr-2" />
                              Restore Project
                            </DropdownMenuItem>
                            <ProjectDeleteConfirmationDialog
                              project={project}
                              onDelete={handleDeleteProject}
                              trigger={
                                <div className="flex items-center w-full px-2 py-1.5 text-sm text-destructive hover:text-destructive cursor-pointer rounded-sm hover:bg-accent">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Project
                                </div>
                              }
                            />
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mb-3 truncate">
                    {project.domain}
                  </p>
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">
                      {project.indexed_links} indexed
                    </span>
                    <span className="text-muted-foreground/70">
                      Archived {project.archived_at ? new Date(project.archived_at).toLocaleDateString() : ''}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 