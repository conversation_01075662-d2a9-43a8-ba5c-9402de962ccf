"use client";

import { useState, useRef } from "react";
import { useTranslations } from "next-intl";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, Download, FileText, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from "sonner";

interface LinkResourcesImportDialogProps {
  onImportComplete: () => void;
  trigger?: React.ReactNode;
}

export function LinkResourcesImportDialog({ onImportComplete, trigger }: LinkResourcesImportDialogProps) {
  const t = useTranslations("links");
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<{
    success: number;
    failed: number;
    errors: string[];
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const csvTemplate = `url,title,link_type,price,source,acquisition_method,notes
https://example.com/page1,Example Article 1,free,,Guest Post,Outreach,High quality blog
https://example.com/page2,Example Article 2,paid,50.00,Directory,Paid Listing,Business directory
https://example.com/page3,Example Resource,free,,Resource Page,HARO,Listed in resources section`;

  const downloadTemplate = () => {
    const blob = new Blob([csvTemplate], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'link-resources-template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error("Please select a CSV file");
        return;
      }
      setFile(selectedFile);
      setImportResult(null);
    }
  };

  const parseCSV = (text: string): any[] => {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      const row: any = {};
      
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });

      if (row.url && row.title) {
        data.push(row);
      }
    }

    return data;
  };

  const handleImport = async () => {
    if (!file) {
      toast.error("Please select a file to import");
      return;
    }

    setImporting(true);
    try {
      const text = await file.text();
      const linkResources = parseCSV(text);

      if (linkResources.length === 0) {
        toast.error("No valid link resources found in the CSV file");
        setImporting(false);
        return;
      }

      // Use the link-resources API endpoint with bulk import flag
      const response = await fetch("/api/link-resources", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          bulk: true,
          linkResources,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setImportResult({
          success: result.success || 0,
          failed: result.failed || 0,
          errors: result.errors || [],
        });
        
        if (result.success > 0) {
          toast.success(`Successfully imported ${result.success} link resources`);
        }
        
        if (result.failed > 0) {
          toast.error(`${result.failed} link resources failed to import`);
        }
      } else {
        toast.error(result.error || "Import failed");
        if (result.details) {
          setImportResult({
            success: 0,
            failed: 0,
            errors: Array.isArray(result.details) ? result.details : [result.details],
          });
        }
      }
    } catch (error) {
      console.error("Error importing link resources:", error);
      toast.error("Failed to import link resources");
    } finally {
      setImporting(false);
    }
  };

  const handleClose = () => {
    if (importResult && importResult.success > 0) {
      onImportComplete();
    }
    setOpen(false);
    setFile(null);
    setImportResult(null);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Import Link Resources</DialogTitle>
          <DialogDescription>
            Import your link resources from a CSV file
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* CSV Format Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4" />
                <h4 className="font-medium">CSV Format</h4>
              </div>
              <div className="text-sm text-muted-foreground space-y-1">
                <p><strong>Required columns:</strong> url, title</p>
                <p><strong>Optional columns:</strong> link_type, price, source, acquisition_method, notes</p>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={downloadTemplate}
                className="mt-2"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Template
              </Button>
            </CardContent>
          </Card>

          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="csvFile">Upload CSV File</Label>
            <div 
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
              {file ? (
                <div>
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(file.size / 1024).toFixed(1)} KB
                  </p>
                </div>
              ) : (
                <div>
                  <p className="font-medium">Click to select CSV file</p>
                  <p className="text-sm text-muted-foreground">
                    Or drag and drop your file here
                  </p>
                </div>
              )}
            </div>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>

          {/* Import Results */}
          {importResult && (
            <div className="space-y-2">
              {importResult.success > 0 && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Successfully imported {importResult.success} link resources
                  </AlertDescription>
                </Alert>
              )}
              
              {importResult.errors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p>Validation errors:</p>
                      <ul className="text-sm list-disc list-inside">
                        {importResult.errors.slice(0, 5).map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                        {importResult.errors.length > 5 && (
                          <li>... and {importResult.errors.length - 5} more errors</li>
                        )}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={handleClose}>
              {importResult && importResult.success > 0 ? "Close" : "Cancel"}
            </Button>
            {!importResult && (
              <Button 
                onClick={handleImport} 
                disabled={!file || importing}
              >
                {importing ? "Importing..." : "Import"}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}