"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Trash2, AlertTriangle, Globe, Calendar, Link2 } from "lucide-react";
import { Project } from "@/types/links";

interface ProjectDeleteConfirmationDialogProps {
  project: Project;
  onDelete: (projectId: string) => Promise<void>;
  trigger?: React.ReactNode;
  disabled?: boolean;
}

export function ProjectDeleteConfirmationDialog({
  project,
  onDelete,
  trigger,
  disabled = false
}: ProjectDeleteConfirmationDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(project.id);
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const defaultTrigger = (
    <Button 
      variant="ghost" 
      size="sm"
      disabled={disabled}
      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  );

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        {trigger || defaultTrigger}
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-lg">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">Delete Project</AlertDialogTitle>
              <p className="text-sm text-muted-foreground">This action cannot be undone</p>
            </div>
          </div>
          
          <AlertDialogDescription className="text-left space-y-4 pt-4">
            <p className="text-foreground">
              Are you sure you want to delete <span className="font-semibold">"{project.name}"</span>?
            </p>
            
            {/* Project Preview */}
            <div className="bg-muted/50 rounded-lg p-4 space-y-3 border border-border/50">
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="font-medium text-foreground truncate">
                      {project.name}
                    </span>
                    {project.is_archived && (
                      <Badge variant="secondary" className="text-xs">
                        Archived
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground truncate">
                    {project.domain}
                  </p>
                </div>
              </div>
              
              {/* Project Stats */}
              <div className="grid grid-cols-2 gap-4 pt-2 border-t border-border/30">
                <div className="space-y-1">
                  <div className="flex items-center gap-1">
                    <Link2 className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs font-medium">Total Links</span>
                  </div>
                  <p className="text-sm font-semibold">{project.total_links}</p>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs font-medium">Created</span>
                  </div>
                  <p className="text-sm">{formatDate(project.created_at)}</p>
                </div>
              </div>
            </div>
            
            {/* Warning Message */}
            <div className="bg-destructive/5 border border-destructive/20 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-destructive flex-shrink-0 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-destructive mb-1">This will permanently delete:</p>
                  <ul className="text-muted-foreground space-y-0.5 text-xs">
                    <li>• The project and all its settings</li>
                    <li>• All {project.total_links} associated links</li>
                    <li>• All discovered external links</li>
                    <li>• Analytics configurations</li>
                    <li>• Project history and data</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground">
              This data cannot be recovered after deletion. Consider archiving the project instead if you might need it later.
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="gap-2 sm:gap-2">
          <AlertDialogCancel disabled={isDeleting} className="flex-1 sm:flex-none">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 flex-1 sm:flex-none"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Project
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 