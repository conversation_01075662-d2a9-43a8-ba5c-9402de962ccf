"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, ExternalLink, Eye } from 'lucide-react';
import { RealTimeData } from "./types";
import { formatNumber } from "./utils";

interface OverviewCardsProps {
  realTimeData: RealTimeData;
}

export function OverviewCards({ realTimeData }: OverviewCardsProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h3 className="text-lg font-semibold">核心指标概览</h3>
        <Badge variant="secondary" className="text-xs">数据库</Badge>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DR评分</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{realTimeData.dr_score}</div>
            <p className="text-xs text-muted-foreground">域名权威度</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">外链数量</CardTitle>
            <ExternalLink className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{realTimeData.linkCount}</div>
            <p className="text-xs text-muted-foreground">总外链数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">流量数据</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(realTimeData.pageViews)}</div>
            <p className="text-xs text-muted-foreground">SEMrush 数据</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}