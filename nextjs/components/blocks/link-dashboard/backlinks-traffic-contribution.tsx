"use client";

import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Link, TrendingUp, BarChart3, Globe } from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { BacklinkData } from "./types";

interface BacklinksTrafficContributionProps {
  backlinksData: BacklinkData[];
  analyticsSource: 'google' | 'plausible' | 'umami';
}

const COLORS = [
  'hsl(var(--chart-primary))',
  'hsl(var(--chart-secondary))',
  'hsl(var(--chart-tertiary))',
  'hsl(var(--chart-quaternary))',
  'hsl(var(--chart-quinary))',
  'hsl(var(--dashboard-accent-1))',
  'hsl(var(--dashboard-accent-2))',
  'hsl(var(--dashboard-accent-3))',
  'hsl(var(--data-info))',
  'hsl(var(--data-neutral))'
];

export function BacklinksTrafficContribution({
  backlinksData,
  analyticsSource
}: BacklinksTrafficContributionProps) {
  const domainData = useMemo(() => {
    // Group backlinks by domain and aggregate traffic
    const domainMap = new Map();
    
    backlinksData.forEach(link => {
      const domain = link.domain;
      const traffic = link.traffic_contribution || 0;
      
      if (domainMap.has(domain)) {
        const existing = domainMap.get(domain);
        existing.traffic += traffic;
        existing.links.push(link);
      } else {
        domainMap.set(domain, {
          domain,
          traffic,
          links: [link],
          count: 1
        });
      }
    });
    
    // Convert to array and sort by traffic
    const domains = Array.from(domainMap.values())
      .sort((a, b) => b.traffic - a.traffic)
      .slice(0, 10);
    
    const totalTraffic = domains.reduce((sum, d) => sum + d.traffic, 0);
    
    return domains.map(d => ({
      ...d,
      percentage: totalTraffic > 0 ? (d.traffic / totalTraffic * 100) : 0
    }));
  }, [backlinksData]);

  const pieData = domainData.map(d => ({
    name: d.domain,
    value: d.traffic,
    percentage: d.percentage
  }));

  const barData = domainData.map(d => ({
    domain: d.domain.length > 15 ? d.domain.substring(0, 15) + '...' : d.domain,
    traffic: d.traffic,
    count: d.links.length
  }));

  const totalTraffic = domainData.reduce((sum, d) => sum + d.traffic, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-muted rounded-md border">
            <BarChart3 className="w-5 h-5 text-muted-foreground" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-foreground">
              域名流量贡献分析
            </h3>
            <Badge variant="secondary" className="text-xs mt-1">
              基于 {analyticsSource === 'google' ? 'Google Analytics' :
                     analyticsSource === 'plausible' ? 'Plausible' :
                     'Umami'} 数据
            </Badge>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-foreground">{totalTraffic.toLocaleString()}</div>
          <div className="text-sm text-muted-foreground">总流量</div>
        </div>
      </div>

      {domainData.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pie Chart */}
          <Card className="border-2">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <Globe className="w-5 h-5 text-muted-foreground" />
                域名流量分布
              </CardTitle>
              <CardDescription>按域名显示流量占比</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={280}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value: number, name: string) => [
                      `${value.toLocaleString()} 流量`,
                      name
                    ]}
                    labelFormatter={(label) => `域名: ${label}`}
                  />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Bar Chart */}
          <Card className="border-2">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-muted-foreground" />
                流量排行榜
              </CardTitle>
              <CardDescription>前10个域名流量对比</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={280}>
                <BarChart data={barData} margin={{ top: 5, right: 30, left: 20, bottom: 60 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis
                    dataKey="domain"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    fontSize={12}
                  />
                  <YAxis fontSize={12} />
                  <Tooltip
                    formatter={(value: number, name: string) => [
                      value.toLocaleString(),
                      name === 'traffic' ? '流量' : '链接数'
                    ]}
                    labelFormatter={(label) => `域名: ${label}`}
                  />
                  <Bar dataKey="traffic" fill="hsl(var(--chart-secondary))" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      ) : null}

      {/* Domain Details List */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">域名详细信息</CardTitle>
          <CardDescription>
            显示各域名的流量贡献和链接统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          {domainData.length > 0 ? (
            <div className="space-y-4">
              {domainData.map((domain, index) => (
                <div
                  key={domain.domain}
                  className="flex items-center justify-between p-5 border rounded-md hover:border-primary/50 transition-all duration-200 bg-muted/30"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: COLORS[index % COLORS.length] }}
                      />
                      <span className="font-semibold text-lg">{domain.domain}</span>
                      <Badge variant="outline" className="text-xs">
                        {domain.links.length} 个链接
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">流量贡献占比</span>
                        <span className="font-medium">{domain.percentage.toFixed(1)}%</span>
                      </div>
                      <Progress 
                        value={domain.percentage} 
                        className="h-2" 
                      />
                    </div>
                  </div>
                  <div className="text-right ml-6">
                    <div className="text-2xl font-bold text-foreground">
                      {domain.traffic.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">流量贡献</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      平均 {Math.round(domain.traffic / domain.links.length)} / 链接
                    </div>
                  </div>
                </div>
              ))}
              
              {backlinksData.length > domainData.length && (
                <div className="text-center pt-4">
                  <Button variant="outline" size="sm">
                    查看更多域名 ({backlinksData.length - domainData.reduce((sum, d) => sum + d.links.length, 0)} 个链接)
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <div className="p-4 bg-muted rounded-md border inline-block mb-4">
                <Link className="w-12 h-12 text-muted-foreground" />
              </div>
              <p className="text-lg font-medium mb-2">暂无外链流量数据</p>
              <p className="text-sm">请确保已配置分析平台并有外链数据</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}