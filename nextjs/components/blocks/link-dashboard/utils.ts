import { ProjectStats } from "@/types/links";
import { MetricsData, AnalyticsData } from "./types";

export const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

export const formatNumber = (num: number) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
};

export const formatLastUpdated = (timestamp: string) => {
  if (!timestamp) return '未知';
  const date = new Date(timestamp);
  const now = new Date();
  const diffMinutes = Math.floor((now.getTime() - date.getTime()) / 60000);
  
  if (diffMinutes < 1) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`;
  return date.toLocaleDateString('zh-CN');
};

// Generate core metrics time series with weekly aggregation
export const generateCoreTimeSeriesData = (data: ProjectStats[], rangeWeeks: number): MetricsData[] => {
  const now = new Date();
  const timeSeriesData: MetricsData[] = [];
  
  // Create data points for each week in the range
  for (let i = rangeWeeks - 1; i >= 0; i--) {
    const weekStart = new Date(now);
    weekStart.setDate(weekStart.getDate() - (i * 7));
    weekStart.setHours(0, 0, 0, 0);
    
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    
    const weekLabel = `${weekStart.getFullYear()}/${weekStart.getMonth() + 1}/${weekStart.getDate()}`;
    
    // Find data for this week (use latest data point in the week)
    const weekData = data.filter(stat => {
      const statDate = new Date(stat.checked_at);
      return statDate >= weekStart && statDate <= weekEnd;
    }).sort((a, b) => new Date(b.checked_at).getTime() - new Date(a.checked_at).getTime())[0];
    
    timeSeriesData.push({
      date: weekLabel,
      dr_score: weekData?.dr_score || 0,
      linkCount: weekData?.backlink_count || weekData?.total_links || 0,
      pageViews: weekData?.traffic || 0,
    });
  }
  
  // Apply forward-fill logic: if current week has no data, use the most recent available data
  for (let i = 0; i < timeSeriesData.length; i++) {
    const point = timeSeriesData[i];
    
    // If this point has no data (all zeros), look for the most recent non-zero data
    if (point.dr_score === 0 && point.linkCount === 0 && point.pageViews === 0) {
      // Find the most recent week with actual data
      for (let j = i - 1; j >= 0; j--) {
        const previousPoint = timeSeriesData[j];
        if (previousPoint.dr_score > 0 || previousPoint.linkCount > 0 || previousPoint.pageViews > 0) {
          point.dr_score = previousPoint.dr_score;
          point.linkCount = previousPoint.linkCount;
          point.pageViews = previousPoint.pageViews;
          break;
        }
      }
    }
  }
  
  return timeSeriesData;
};

// Generate empty analytics data
export const generateEmptyAnalyticsData = (rangeDays: number): AnalyticsData[] => {
  const now = new Date();
  const data: AnalyticsData[] = [];
  
  for (let i = rangeDays - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    data.push({
      date: date.toISOString().split('T')[0],
      pageViews: 0,
      sessions: 0,
      users: 0
    });
  }
  
  return data;
};