"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Link, Project } from "@/types/links";

interface LinkFormProps {
  link?: Link | null;
  projects: Project[];
  selectedProject?: Project | null;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export function LinkForm({ link, projects, selectedProject, onSubmit, onCancel }: LinkFormProps) {
  const t = useTranslations("links");
  const [formData, setFormData] = useState({
    url: link?.url || "",
    title: link?.title || "",
    link_type: link?.link_type || "free",
    price: link?.price?.toString() || "",
    currency: link?.currency || "USD",
    source: link?.source || "",
    acquisition_method: link?.acquisition_method || "",
    notes: link?.notes || "",
    dr_score: link?.dr_score?.toString() || "",
    traffic: link?.traffic?.toString() || "",
  });
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // Convert numeric fields
      const submitData = {
        ...formData,
        price: formData.price ? parseFloat(formData.price) : undefined,
        dr_score: formData.dr_score ? parseFloat(formData.dr_score) : undefined,
        traffic: formData.traffic ? parseInt(formData.traffic) : 0,
      };
      await onSubmit(submitData);
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {link ? t("link.edit") : t("add_link")}
          </DialogTitle>
          <DialogDescription>
            {link 
              ? "Update link information and tracking data"
              : "Add a new link to track its performance"
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                基本信息
              </h3>
              <Separator className="flex-1" />
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="url">{t("link.url")} *</Label>
                <Input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) => handleChange("url", e.target.value)}
                  placeholder="https://example.com/page (will be normalized)"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  URL will be automatically normalized for consistency
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">{t("link.title")} *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleChange("title", e.target.value)}
                  placeholder="Link title or anchor text"
                  required
                />
              </div>

            </div>
          </div>

          {/* 链接类型和费用 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                链接类型和费用
              </h3>
              <Separator className="flex-1" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="link_type">{t("link.link_type")}</Label>
                <Select 
                  value={formData.link_type} 
                  onValueChange={(value) => handleChange("link_type", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free">{t("link_type.free")}</SelectItem>
                    <SelectItem value="paid">{t("link_type.paid")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.link_type === "paid" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="price">{t("link.price")}</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => handleChange("price", e.target.value)}
                      placeholder="0.00"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currency">{t("link.currency")}</Label>
                    <Select 
                      value={formData.currency} 
                      onValueChange={(value) => handleChange("currency", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="CNY">CNY</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* 来源和获取方式 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                来源和获取方式
              </h3>
              <Separator className="flex-1" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="source">{t("link.source")}</Label>
                <Input
                  id="source"
                  value={formData.source}
                  onChange={(e) => handleChange("source", e.target.value)}
                  placeholder="e.g., Guest post, Directory, Resource page"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="acquisition_method">{t("link.acquisition_method")}</Label>
                <Input
                  id="acquisition_method"
                  value={formData.acquisition_method}
                  onChange={(e) => handleChange("acquisition_method", e.target.value)}
                  placeholder="e.g., Outreach, HARO, Partnership"
                />
              </div>
            </div>
          </div>

          {/* 性能指标 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                性能指标
              </h3>
              <Separator className="flex-1" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dr_score">Domain Rating (DR)</Label>
                <Input
                  id="dr_score"
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={formData.dr_score}
                  onChange={(e) => handleChange("dr_score", e.target.value)}
                  placeholder="0.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="traffic">Monthly Traffic</Label>
                <Input
                  id="traffic"
                  type="number"
                  min="0"
                  value={formData.traffic}
                  onChange={(e) => handleChange("traffic", e.target.value)}
                  placeholder="0"
                />
              </div>
            </div>
          </div>

          {/* 备注 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                备注
              </h3>
              <Separator className="flex-1" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">{t("link.notes")}</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleChange("notes", e.target.value)}
                placeholder="Additional notes about this link..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("cancel")}
            </Button>
            <Button type="submit" disabled={submitting}>
              {submitting ? t("saving") : (link ? t("update") : t("add"))}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 