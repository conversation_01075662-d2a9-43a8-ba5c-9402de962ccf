"use client";

import { ReactNode } from "react";
import { toast } from "sonner";

export default function ({
  text,
  children,
}: {
  text: string;
  children: ReactNode;
}) {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied");
    } catch (err) {
      console.error("Failed to copy text: ", err);
      toast.error("Failed to copy");
    }
  };

  return (
    <div className="cursor-pointer" onClick={handleCopy}>
      {children}
    </div>
  );
}
