"use client";

import { Card } from "@/components/ui/card";
import { ChevronRight, Code, Key, BookOpen } from "lucide-react";

export function ApiDocsClient() {
  return (
    <section className="w-full py-12 md:py-16">
      <div className="md:max-w-10xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8 md:mb-12">
          <h1 className="mb-3 text-pretty text-3xl font-semibold md:mb-4 md:text-4xl lg:mb-6">
            API Documentation
          </h1>
          <p className="text-muted-foreground md:text-lg mx-auto max-w-3xl">
            Integrate with MyBacklinks.app using our REST API. Access data programmatically with your API key.
          </p>
        </div>
        
        {/* Feature cards */}
        <div className="grid gap-6 grid-cols-1 md:grid-cols-3 mb-10">
          <Card className="p-6 flex flex-col items-start">
            <div className="bg-blue-100 dark:bg-blue-950 p-2 rounded-lg mb-4">
              <Key className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Authentication</h3>
            <p className="text-muted-foreground text-sm mb-3">
              Get your API key from [your account](/dashboard/api-keys) and use it to authenticate your requests.
            </p>
            <a href="#authentication" className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:underline mt-auto">
              View Authentication <ChevronRight className="h-4 w-4 ml-1" />
            </a>
          </Card>
          
          <Card className="p-6 flex flex-col items-start">
            <div className="bg-emerald-100 dark:bg-emerald-950 p-2 rounded-lg mb-4">
              <Code className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Endpoints</h3>
            <p className="text-muted-foreground text-sm mb-3">
              Use our REST API endpoints to search and explore Item data programmatically.
            </p>
            <a href="#search-api" className="text-emerald-600 dark:text-emerald-400 text-sm font-medium flex items-center hover:underline mt-auto">
              View Endpoints <ChevronRight className="h-4 w-4 ml-1" />
            </a>
          </Card>
          
          <Card className="p-6 flex flex-col items-start">
            <div className="bg-amber-100 dark:bg-amber-950 p-2 rounded-lg mb-4">
              <BookOpen className="h-6 w-6 text-amber-600 dark:text-amber-400" />
            </div>
            <h3 className="font-semibold text-lg mb-2">Examples</h3>
            <p className="text-muted-foreground text-sm mb-3">
              Learn how to use our API with code examples and documentation.
            </p>
            <a href="#example-request" className="text-amber-600 dark:text-amber-400 text-sm font-medium flex items-center hover:underline mt-auto">
              See Examples <ChevronRight className="h-4 w-4 ml-1" />
            </a>
          </Card>
        </div>
        
        {/* Documentation content in vertical layout */}
        <div className="space-y-12">
          <section id="overview" className="rounded-xl border border-border overflow-hidden shadow-sm">
            <div className="p-6">
              <div className="prose dark:prose-invert max-w-none">
                <h2 className="text-2xl font-semibold mb-6">Overview</h2>
                <p className="mb-4">
                  Our API provides programmatic access to search and explore Item data. 
                  You can use it to integrate Item information into your applications.
                </p>
                
                <section id="authentication" className="mt-8">
                  <h3 className="text-xl font-semibold mb-4">Authentication</h3>
                  <p className="mb-4">
                    All API endpoints require an API key. You can obtain your API key from the API Keys page.
                  </p>
                  
                  <h4 className="text-lg font-medium mb-2">API Key Usage</h4>
                  <p className="mb-2">Include your API key in the Authorization header:</p>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                    <code>Authorization: Bearer YOUR_API_KEY</code>
                  </pre>
                </section>
                
                <section id="rate-limits" className="mt-8">
                  <h3 className="text-xl font-semibold mb-4">Rate Limits</h3>
                  <p>
                    The API has a rate limit of 20 requests per hour. If you exceed this limit, 
                    you'll receive a 401 Unauthorized response.
                  </p>
                </section>
              </div>
            </div>
          </section>
          
          <section id="search-api" className="rounded-xl border border-border overflow-hidden shadow-sm">
            <div className="p-6">
              <div className="prose dark:prose-invert max-w-none">
                <h2 className="text-2xl font-semibold mb-6">Search API</h2>
                
                <h3 className="text-xl font-semibold mb-4">Endpoint</h3>
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                  <code>GET /api/open/v1/search</code>
                </pre>
                <p className="mb-4">Search for Items based on keywords.</p>
                
                <h3 className="text-xl font-semibold mb-4">Parameters</h3>
                <table className="w-full mb-6">
                  <thead>
                    <tr>
                      <th className="text-left p-2 border-b">Parameter</th>
                      <th className="text-left p-2 border-b">Type</th>
                      <th className="text-left p-2 border-b">Required</th>
                      <th className="text-left p-2 border-b">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-2 border-b">keywords</td>
                      <td className="p-2 border-b">string</td>
                      <td className="p-2 border-b">Yes</td>
                      <td className="p-2 border-b">The search terms to look for</td>
                    </tr>
                  </tbody>
                </table>
                
                <section id="example-request">
                  <h3 className="text-xl font-semibold mb-4">Example Request</h3>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                    <code>GET /api/open/v1/search?keywords=blockchain<br/>Authorization: Bearer YOUR_API_KEY</code>
                  </pre>
                  
                  <h3 className="text-xl font-semibold mb-4">Example Response</h3>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                    <code>{`{
  "data": [
    {
      "name": "Blockchain Project",
      "brief": "A decentralized blockchain solution",
      "website_url": "https://example.com"
    }
  ],
  "count": 1
}`}</code>
                  </pre>
                </section>
                
                <section id="error-responses">
                  <h3 className="text-xl font-semibold mb-4">Error Responses</h3>
                  <table className="w-full">
                    <thead>
                      <tr>
                        <th className="text-left p-2 border-b">Status</th>
                        <th className="text-left p-2 border-b">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="p-2 border-b">400</td>
                        <td className="p-2 border-b">Missing required parameters</td>
                      </tr>
                      <tr>
                        <td className="p-2 border-b">401</td>
                        <td className="p-2 border-b">Invalid API key or rate limit exceeded</td>
                      </tr>
                      <tr>
                        <td className="p-2 border-b">500</td>
                        <td className="p-2 border-b">Server error</td>
                      </tr>
                    </tbody>
                  </table>
                </section>
              </div>
            </div>
          </section>

          <section id="item-api" className="rounded-xl border border-border overflow-hidden shadow-sm">
            <div className="p-6">
              <div className="prose dark:prose-invert max-w-none">
                <h2 className="text-2xl font-semibold mb-6">Item API</h2>
                
                <p className="mb-4">
                  MyBacklinks.app provides a Model Context Protocol (Item) compatible API endpoint that allows AI assistants and tools to search for Items and retrieve Item information directly.
                </p>
                
                <h3 className="text-xl font-semibold mb-4">HTTP Item Endpoint</h3>
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                  <code>GET/POST /api/open/v1/streamable</code>
                </pre>
                <p className="mb-4">This endpoint implements the Model Context Protocol over HTTP, enabling AI assistants to search and access Item data programmatically.</p>
                
                <h3 className="text-xl font-semibold mb-4">SSE Item Endpoint</h3>
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                  <code>GET/POST /api/open/v1/sse</code>
                </pre>
                <p className="mb-4">
                  This endpoint implements the Model Context Protocol using Server-Sent Events (SSE), providing a more browser-friendly alternative 
                  that works well with modern web applications. SSE maintains a single persistent connection for receiving server messages
                  and uses separate HTTP requests for client-to-server messages.
                </p>
                
                <section id="item-authentication" className="mt-8">
                  <h3 className="text-xl font-semibold mb-4">Authentication</h3>
                  <p className="mb-4">
                    The Item API requires authentication with a valid API key. You can obtain your API key from the <a href="/dashboard/api-keys">API Keys page</a>.
                  </p>
                  
                  <h4 className="text-lg font-medium mb-2">API Key Usage</h4>
                  <p className="mb-2">Include your API key in the Authorization header for all requests:</p>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                    <code>Authorization: Bearer YOUR_API_KEY</code>
                  </pre>
                  
                  <p className="text-sm text-amber-600 dark:text-amber-400">
                    <strong>Note:</strong> API keys have a rate limit of 20 requests per hour. Exceeding this limit will result in 401 Unauthorized responses.
                  </p>
                </section>
                
                <h3 className="text-xl font-semibold mb-4">Available Tools</h3>
                <table className="w-full mb-6">
                  <thead>
                    <tr>
                      <th className="text-left p-2 border-b">Tool Name</th>
                      <th className="text-left p-2 border-b">Description</th>
                      <th className="text-left p-2 border-b">Parameters</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-2 border-b">search_item_hub</td>
                      <td className="p-2 border-b">Search for Items on the MyBacklinks.app</td>
                      <td className="p-2 border-b">keywords (string): Search keywords</td>
                    </tr>
                    <tr>
                      <td className="p-2 border-b">get_item_info</td>
                      <td className="p-2 border-b">Get detailed information about a specific Item</td>
                      <td className="p-2 border-b">id (string): Item UUID</td>
                    </tr>
                  </tbody>
                </table>
                
                <section id="item-example">
                  <h3 className="text-xl font-semibold mb-4">Connection Example</h3>
                  <p className="mb-2">1. First, establish a connection to get a session ID:</p>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-4">
                    <code>GET /api/open/v1/streamable
Authorization: Bearer YOUR_API_KEY</code>
                  </pre>
                  <p className="mb-2">Response:</p>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-6">
                    <code>{`{
  "success": true,
  "sessionId": "194830ab-eb0b-4d17-a574-af96705276c2",
  "message": "Connection established. Use this sessionId for subsequent calls."
}`}</code>
                  </pre>
                  
                  <p className="mb-2">2. Call a tool with the session ID and API key:</p>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded mb-4">
                    <code>{`POST /api/open/v1/streamable?sessionId=194830ab-eb0b-4d17-a574-af96705276c2
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
  "jsonrpc": "2.0",
  "method": "callTool",
  "params": {
    "name": "search_item_hub",
    "arguments": {
      "keywords": "example"
    }
  },
  "id": "call-1"
}`}</code>
                  </pre>
                </section>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
}
