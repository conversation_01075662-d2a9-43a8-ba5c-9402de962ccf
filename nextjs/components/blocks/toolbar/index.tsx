"use client";

import { Button } from "@/components/ui/button";
import { Button as ButtonType } from "@/types/blocks/base";
import Icon from "@/components/icon";
import Link from "next/link";

export default function Toolbar({ items, component }: { 
  items?: (ButtonType & { onClick?: () => void })[], 
  component?: React.ReactNode 
}) {
  return (
    <div className="flex flex-col md:flex-row md:justify-between gap-4 mb-8">
      {component && <div className="mb-4 w-full">{component}</div>}
      {items && items.length > 0 && (
        <div className="flex gap-2 items-center">
          {items.map((item, idx) => (
            item.type === 'custom' && item.element ? (
              <div key={idx}>{item.element}</div>
            ) : (
              <Button
                key={idx}
                variant={item.variant}
                size="sm"
                className={item.className}
                onClick={item.onClick}
              >
                {item.url && !item.onClick ? (
                  <Link
                    href={item.url}
                    target={item.target}
                    className="flex items-center gap-1"
                  >
                    {item.title}
                    {item.icon && <Icon name={item.icon} />}
                  </Link>
                ) : (
                  <span className="flex items-center gap-1">
                    {item.title}
                    {item.icon && <Icon name={item.icon} />}
                  </span>
                )}
              </Button>
            )
          ))}
        </div>
      )}
    </div>
  );
}
