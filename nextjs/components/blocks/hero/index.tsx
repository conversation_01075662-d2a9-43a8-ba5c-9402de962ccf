"use client";

import { useEffect, useMemo, useState } from "react";
import { <PERSON>R<PERSON>, <PERSON>Call, BarChart3, ExternalLink, Shield } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";

export default function Hero({ hero }: { hero: HeroType }) {
  if (hero.disabled) {
    return null;
  }

  return (
    <div className="w-full relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-indigo-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-indigo-950/20"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-100/20 via-transparent to-transparent dark:from-blue-900/20"></div>
      
      <div className="container relative mx-auto px-4 py-20 lg:py-32">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Content Section */}
          <div className="flex flex-col space-y-8">
            {hero.announcement && (
              <div className="inline-flex w-fit">
                <Link href={hero.announcement.url || "#"} passHref>
                  <Button variant="secondary" size="sm" className="gap-2 rounded-full border-2 border-primary/20 bg-primary/5 hover:bg-primary/10 transition-all duration-300">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    {hero.announcement.title} 
                    <MoveRight className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            )}
            
            <div className="space-y-6">
              <h1 className="text-5xl lg:text-7xl font-bold tracking-tight leading-tight">
                <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 dark:from-white dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent">
                  {hero.title}
                </span>
              </h1>

              <p className="text-lg lg:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-xl"
                dangerouslySetInnerHTML={{ __html: hero.description || "" }}
              />
            </div>

            {/* Key Metrics Preview */}
            <div className="flex flex-wrap gap-6 py-4">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                Traffic Analytics
              </div>
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                <ExternalLink className="w-5 h-5 text-green-600" />
                Backlink Tracking
              </div>
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                <Shield className="w-5 h-5 text-purple-600" />
                Domain Management
              </div>
            </div>

            {hero.buttons && hero.buttons.length > 0 && (
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                {hero.buttons.map((item, i) => (
                  <Link
                    key={i}
                    href={item.url || "#"}
                    target={item.target || ""}
                    passHref
                  >
                    <Button
                      size="lg"
                      variant={i === 0 ? "default" : "outline"}
                      className={`gap-3 px-8 py-6 text-base font-semibold transition-all duration-300 ${
                        i === 0 
                          ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" 
                          : "border-2 hover:bg-gray-50 dark:hover:bg-gray-800"
                      }`}
                    >
                      {item.title}
                      {item.icon ? (
                        <Icon name={item.icon} className="w-5 h-5" />
                      ) : (
                        i === 0 ? <PhoneCall className="w-5 h-5" /> : <MoveRight className="w-5 h-5" />
                      )}
                    </Button>
                  </Link>
                ))}
              </div>
            )}

            {hero.tip && (
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                {hero.tip}
              </div>
            )}
          </div>

          {/* Demo Screenshot Section */}
          <div className="relative">
            <div className="relative z-10">
              {/* Main Dashboard Preview */}
              <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="bg-gray-50 dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div className="text-sm font-medium text-gray-600 dark:text-gray-300">MyBackLinks Dashboard</div>
                  </div>
                </div>
                <div className="p-6 space-y-6">
                  {/* Stats Cards */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">1.2M</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Monthly Views</div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-950/30 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">DR 65</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Domain Rating</div>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-950/30 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">3.2K</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Backlinks</div>
                    </div>
                  </div>
                  
                  {/* Chart Placeholder */}
                  <div className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6 h-32 flex items-end justify-between">
                    {(() => {
                      // Pre-defined heights for consistent SSR/client rendering
                      const chartHeights = [55, 35, 77, 27, 73, 51, 64, 22, 55, 65, 33, 62];
                      return Array.from({ length: 12 }).map((_, i) => (
                        <div 
                          key={i} 
                          className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-sm opacity-80"
                          style={{ 
                            height: `${chartHeights[i]}%`, 
                            width: '6px',
                            animation: `growUp 0.8s ease-out ${i * 0.1}s both`
                          }}
                        ></div>
                      ));
                    })()}
                  </div>
                </div>
              </div>
              
              {/* Floating Cards */}
              <div className="absolute -right-4 -top-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 z-20">
                <div className="text-sm font-medium text-green-600">↗ +24% Traffic</div>
              </div>
              
              <div className="absolute -left-4 -bottom-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 z-20">
                <div className="text-sm font-medium text-blue-600">🔗 New Backlinks</div>
              </div>
            </div>
            
            {/* Background Elements */}
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-2xl"></div>
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-radial from-blue-500/5 to-transparent rounded-full"></div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes growUp {
          from {
            height: 0;
            opacity: 0;
          }
          to {
            opacity: 0.8;
          }
        }
      `}</style>
    </div>
  );
}
