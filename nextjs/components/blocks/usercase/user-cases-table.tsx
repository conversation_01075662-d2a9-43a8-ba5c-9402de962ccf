"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UserCase, UserCaseType } from "@/types/usercase";
import Link from "next/link";
import { useState } from "react";
import { redirect } from "next/navigation";

type DataTableProps = {
  columns: any[];
  data: any[];
  loading?: boolean;
};

const DataTable = ({ columns, data, loading }: DataTableProps) => {
  if (loading) {
    return <div className="py-8 text-center">Loading...</div>;
  }

  if (data.length === 0) {
    return <div className="py-8 text-center">No data available</div>;
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            {columns.map((column, index) => (
              <th key={index} className="px-4 py-2 text-left text-sm font-medium">
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, rowIndex) => (
            <tr key={rowIndex} className="border-b hover:bg-gray-50">
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="px-4 py-2 text-sm">
                  {column.cell ? 
                    column.cell({ row: {
                      getValue: (key: string) => row[key],
                      original: row
                    }}) : 
                    row[column.accessorKey]
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Define the columns for the DataTable
const columns = [
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const type = row.getValue("type") as UserCaseType;
      return <div className="capitalize">{type}</div>;
    },
  },
  {
    accessorKey: "title",
    header: "Title",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const title = row.getValue("title") as string;
      return <div className="font-medium">{title || "Untitled"}</div>;
    },
  },
  {
    accessorKey: "author_name",
    header: "Author",
  },
  {
    accessorKey: "created_at",
    header: "Created At",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const created_at = row.getValue("created_at") as string;
      return created_at ? new Date(created_at).toLocaleDateString() : "-";
    },
  },
  {
    accessorKey: "image_urls",
    header: "Images",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const imageUrls = row.getValue("image_urls") as string[];
      return imageUrls?.length ? imageUrls.length : 0;
    },
  },
  {
    accessorKey: "video_urls",
    header: "Videos",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const videoUrls = row.getValue("video_urls") as string[];
      return videoUrls?.length ? videoUrls.length : 0;
    },
  },
  {
    accessorKey: "locale",
    header: "Locale",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const locale = row.getValue("locale") as string;
      return <div className="font-medium">{locale || "en"}</div>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const status = row.getValue("status") as string;
      return <div className="capitalize">{status}</div>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }: { row: { getValue: (key: string) => any; original: UserCase } }) => {
      const userCase = row.original;
      
      // Handle delete functionality
      const handleDelete = async () => {
        try {
          const sessionKey = localStorage.getItem("session_key");
          
          const response = await fetch(`/api/admin/user-cases/${userCase.uuid}`, {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              "X-Session-Key": sessionKey || "",
            },
          });
          
          if (!response.ok) throw new Error("Failed to delete user case");
          
          // Refresh the page to update the list
          window.location.href = "/admin/user-cases";
        } catch (error) {
          console.error("Error deleting user case:", error);
          alert("Failed to delete user case");
        }
      };
      
      return (
        <div className="flex items-center gap-2">
          <Link href={`/admin/user-cases/${userCase.uuid}`}>
            <Button variant="outline" size="sm">
              View
            </Button>
          </Link>
          <Link href={`/admin/user-cases/${userCase.uuid}/edit`}>
            <Button variant="outline" size="sm">
              Edit
            </Button>
          </Link>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            Delete
          </Button>
        </div>
      );
    },
  },
];

interface UserCasesTableProps {
  userCases: UserCase[];
  loading?: boolean;
}

export function UserCasesTable({ userCases, loading = false }: UserCasesTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  
  // Filter user cases based on search term and type filter
  const filteredUserCases = userCases.filter(uc => {
    // Apply type filter if selected
    if (typeFilter !== "all" && uc.type !== typeFilter) {
      return false;
    }
    
    // Apply search filter if entered
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        (uc.title?.toLowerCase().includes(searchLower)) || 
        (uc.author_name?.toLowerCase().includes(searchLower)) ||
        (uc.url?.toLowerCase().includes(searchLower))
      );
    }
    
    return true;
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>All User Cases</CardTitle>
            <CardDescription>
              Manage user cases that showcase Item usage
            </CardDescription>
          </div>
          <Link href="/admin/user-cases/create">
            <Button>Add New Case</Button>
          </Link>
        </div>
        <div className="mt-4 flex flex-wrap gap-4">
          <Input 
            placeholder="Search user cases..." 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              <SelectItem value="twitter">Twitter/X</SelectItem>
              <SelectItem value="jike">Jike</SelectItem>
              <SelectItem value="youtube">YouTube</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          columns={columns}
          data={filteredUserCases}
          loading={loading}
        />
      </CardContent>
    </Card>
  );
} 