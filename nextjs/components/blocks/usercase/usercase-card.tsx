"use client";

import { UserCase } from "@/types/usercase";
import { TwitterCard } from "../cards/twitter-card";
import { JikeCard } from "../cards/jike-card";

interface UserCaseCardProps {
  userCase: UserCase;
  className?: string;
  showContent?: boolean;
  onClick?: () => void;
}

export const UserCaseCard: React.FC<UserCaseCardProps> = ({ 
  userCase, 
  className, 
  onClick 
}) => {
  const {
    type,
    uuid,
    url,
    title,
    author_name,
    author_avatar_url,
    details,
    created_at,
    image_urls = [],
    video_urls = []
  } = userCase;

  // Extract ID from URL if not available in details
  const getIdFromUrl = (url: string): string => {
    let id = '';
    
    if (type === 'twitter' || type === 'x') {
      const match = url.match(/(?:twitter\.com|x\.com)\/(?:\w+)\/status\/(\d+)/);
      id = match?.[1] || '';
    } else if (type === 'jike') {
      const match = url.match(/okjike\.com\/(?:originalPosts|repost)\/([a-zA-Z0-9]+)/);
      id = match?.[1] || '';
    } else if (type === 'youtube') {
      const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
      const match = url.match(regex);
      id = match?.[1] || '';
    }
    
    return id;
  };

  const id = userCase.uuid;
  

  const localizedContent = details || '';
  
  // Get a short excerpt from the content for preview
  const contentPreview = localizedContent 
    ? localizedContent.substring(0, 150) + (localizedContent.length > 150 ? '...' : '')
    : '';
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  // Render based on the type of user case
  switch (type) {
    case 'twitter':
    case 'x':
      return (
        <div 
          onClick={handleClick}
          className={`cursor-pointer ${className}`}
        >
          <TwitterCard
            id={id || ''}
            content={localizedContent}
            author={author_name}
            authorAvatar={author_avatar_url}
            date={created_at}
            imageUrls={image_urls}
            videoUrls={video_urls}
          />
        </div>
      );
    case 'jike':
      return (
        <div 
          onClick={handleClick}
          className={`cursor-pointer ${className}`}
        >
          <JikeCard
            id={id || ''}
            content={localizedContent}
            author={author_name}
            authorAvatar={author_avatar_url}
            date={created_at}
            imageUrls={image_urls}
            likeCount={userCase.details?.likeCount}
          />
        </div>
      );
    case 'youtube':
      return (
        <div 
          className={`rounded-lg border shadow-sm overflow-hidden ${className}`}
          onClick={handleClick}
        >
          <iframe
            src={`https://www.youtube.com/embed/${id}?controls=1`}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="w-full aspect-video"
            title={title || "YouTube video"}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation"
          />
          <div className="p-4">
            {title && <h3 className="font-semibold">{title}</h3>}
            {author_name && <p className="text-sm text-gray-500">{author_name}</p>}
          </div>
        </div>
      );
    default:
      return (
        <div 
          className={`rounded-lg border p-4 ${className}`}
          onClick={handleClick}
        >
          <p>Unsupported content type: {type}</p>
        </div>
      );
  }
}; 