"use client";

import { useState } from "react";
import { UserCase } from "@/types/usercase";
import { UserCaseCard } from "@/components/blocks/usercase/usercase-card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import Link from "next/link";
import CaseCard from "@/components/blocks/cards/case-card";

interface CasesPreviewWrapperProps {
  userCases: UserCase[];
  locale: string;
}

export default function CasesPreviewWrapper({ userCases, locale }: CasesPreviewWrapperProps) {
  const [selectedCase, setSelectedCase] = useState<UserCase | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const handleCaseClick = (userCase: UserCase) => {
    setSelectedCase(userCase);
    setShowPreview(true);
  };

  return (
    <>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {userCases.map((userCase) => (
          <div 
            key={userCase.uuid} 
            onClick={() => handleCaseClick(userCase)}
            className="cursor-pointer"
          >
            <CaseCard userCase={userCase} locale={locale} />
          </div>
        ))}
      </div>
      
      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedCase?.title || "User Case"}</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {selectedCase && (
              <UserCaseCard userCase={selectedCase} />
            )}
          </div>
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              Close
            </Button>
            <Link href={`/${locale}/cases/${selectedCase?.uuid}`} passHref>
              <Button>View details</Button>
            </Link>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
} 