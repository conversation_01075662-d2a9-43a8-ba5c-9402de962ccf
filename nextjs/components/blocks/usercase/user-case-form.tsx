"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { UserCaseCard } from "@/components/blocks/usercase/usercase-card";
import { toast } from "@/components/ui/use-toast";
import { Items } from "@/types/items";
import { UserCase, UserCaseType } from "@/types/usercase";
import { useParams } from "next/navigation";
import { useRef, useState, useTransition } from "react";

interface UserCaseFormProps {
  items: Partial<Items & { item_uuid?: string }>[];
  extractMediaAction: (formData: FormData) => Promise<any>;
  searchItemsAction: (formData: FormData) => Promise<any>;
  createUserCaseAction: (formData: FormData) => Promise<any>;
}

export function UserCaseForm({ 
  items: initialItems, 
  extractMediaAction, 
  searchItemsAction, 
  createUserCaseAction 
}: UserCaseFormProps) {
  const params = useParams();
  const locale = (params?.locale as string) || "en";
  const formRef = useRef<HTMLFormElement>(null);
  
  const [isPending, startTransition] = useTransition();
  const [url, setUrl] = useState("");
  const [type, setType] = useState<UserCaseType>("twitter");
  const [items, setItems] = useState<Partial<Items & { item_uuid?: string }>[]>(initialItems);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [extractedData, setExtractedData] = useState<any>(null);
  const [aiSummary, setAiSummary] = useState("");
  const [contentText, setContentText] = useState("");
  const [searchTerm, setSearchTerm] = useState("");

  // 处理提取URL
  const handleExtractUrl = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!url.trim()) {
      toast({
        title: "Error",
        description: "Please enter a URL",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Processing",
      description: "Extracting media content...",
    });

    startTransition(async () => {
      const formData = new FormData();
      formData.append("url", url);
      formData.append("type", type);
      
      const result = await extractMediaAction(formData);
      
      if (result.success) {
        setExtractedData(result.data);
        
        // 设置默认的AI摘要和内容
        if (result.data.description) {
          setAiSummary(result.data.description);
        }
        
        if (result.data.content) {
          setContentText(result.data.content);
        }
        
        // 自动搜索相关Items
        if (result.data && result.data.title) {
          await handleAutoSearchItems(result.data.title);
        }
        
        toast({
          title: "Success",
          description: "Media content extracted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to extract media content",
          variant: "destructive",
        });
      }
    });
  };

  // 自动搜索Items
  const handleAutoSearchItems = async (term: string) => {
    if (!term.trim()) return;
    
    const formData = new FormData();
    formData.append("query", term);
    
    startTransition(async () => {
      try {
        const result = await searchItemsAction(formData);
        
        console.log("Auto search result from server:", result);
        
        if (result.success && result.data && Array.isArray(result.data)) {
          const searchResults = result.data;
          console.log("Processing auto search results:", searchResults);
          
          // Update Item list with search results, preserving selected Items
          setItems(prevItems => {
            // Get current selected Items
            const selectedItemObjects = prevItems.filter((m: Partial<Items & { item_uuid?: string }>) => {
              const uuid = getItemUuid(m);
              return uuid && selectedItems.includes(uuid);
            });
            
            // Create map of UUIDs from search results
            const searchResultUuids = new Set(searchResults.map((m: Partial<Items & { item_uuid?: string }>) => getItemUuid(m)));
            
            // Keep selected Items that aren't in search results
            const keptSelectedItems = selectedItemObjects.filter((m: Partial<Items & { item_uuid?: string }>) => {
              const uuid = getItemUuid(m);
              return uuid && !searchResultUuids.has(uuid);
            });
            
            // Combine selected Items with search results
            const newItems = [...keptSelectedItems, ...searchResults];
            console.log(`Auto search found ${searchResults.length} Items, total ${newItems.length} after combining with selected`);
            return newItems;
          });
        }
      } catch (error) {
        console.error("Error during auto Item search:", error);
      }
    });
  };

  // 处理用户搜索Items
  const handleSearchItems = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!searchTerm.trim()) {
      toast({
        title: "Error",
        description: "Please enter a search term",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Searching",
      description: "Looking for Items...",
    });
    
    const formData = new FormData();
    formData.append("query", searchTerm);
    
    startTransition(async () => {
      try {
        const result = await searchItemsAction(formData);
        
        console.log("Search result from server:", result);
        
        if (result.success && result.data && Array.isArray(result.data)) {
          const searchResults = result.data;
          console.log("Processing search results:", searchResults);
          
          // Update Item list with search results, preserving selected items
          setItems(prevItems => {
            // Get current selected Items
            const selectedItemObjects = prevItems.filter((m: Partial<Items & { item_uuid?: string }>) => {
              const uuid = getItemUuid(m);
              return uuid && selectedItems.includes(uuid);
            });
            
            // Create map of UUIDs from search results
            const searchResultUuids = new Set(searchResults.map((m: Partial<Items & { item_uuid?: string }>) => getItemUuid(m)));
            
            // Keep selected Items that aren't in search results
            const keptSelectedItems = selectedItemObjects.filter((m: Partial<Items & { item_uuid?: string }>) => {
              const uuid = getItemUuid(m);
              return uuid && !searchResultUuids.has(uuid);
            });
            
            // Combine selected Items with search results
            return [...keptSelectedItems, ...searchResults];
          });
          
          toast({
            title: "Success",
            description: `Found ${searchResults.length} matching Items`,
          });
        } else {
          console.error("Invalid search result format:", result);
          toast({
            title: "Warning",
            description: result.error || "No Items found with this search term",
            variant: "default",
          });
        }
      } catch (error) {
        console.error("Error during Item search:", error);
        toast({
          title: "Error",
          description: "Failed to search Items",
          variant: "destructive",
        });
      }
    });
  };

  // 处理创建用户案例
  const handleCreateUserCase = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!extractedData) {
      toast({
        title: "Error",
        description: "No data extracted from URL",
        variant: "destructive",
      });
      return;
    }
    
    toast({
      title: "Processing",
      description: "Creating user case...",
    });
    
    const formData = new FormData();
    formData.append("type", type);
    formData.append("url", url);
    formData.append("title", extractedData.title || "");
    formData.append("author_name", extractedData.authorName || "");
    formData.append("author_avatar_url", extractedData.authorAvatar || "");
    formData.append("ai_summary", aiSummary);
    formData.append("content", contentText);
    formData.append("selected_items", JSON.stringify(selectedItems));
    formData.append("image_urls", JSON.stringify(extractedData.imageUrls || []));
    formData.append("video_urls", JSON.stringify(extractedData.videoUrls || []));
    formData.append("details", extractedData.content || '');
    formData.append("locale", locale);
    
    startTransition(async () => {
      const result = await createUserCaseAction(formData);
      console.log("Form data:", formData);
      
      if (!result.success) {
        toast({
          title: "Error",
          description: result.error || "Failed to create user case",
          variant: "destructive",
        });
      } else {
        // 成功创建，显示成功消息
        toast({
          title: "Success",
          description: result.message || "User case created successfully",
        });
        
        // 检查是否有重定向URL
        if (result.redirectTo) {
          // 使用window.location进行客户端重定向
          window.location.href = result.redirectTo;
        }
      }
    });
  };

  // 创建预览用户案例
  const createPreviewUserCase = (): UserCase | null => {
    if (!extractedData) return null;
    
    return {
      type: type as UserCaseType,
      url: url,
      title: extractedData.title,
      author_name: extractedData.authorName,
      author_avatar_url: extractedData.authorAvatar,
      content: { [locale]: contentText },
      details: extractedData,
      image_urls: extractedData.imageUrls || [],
      video_urls: extractedData.videoUrls || []
    };
  };

  // 获取Item UUID（支持两种可能的字段名）
  const getItemUuid = (item: Partial<Items & { item_uuid?: string }>) => {
    return item.uuid || item.item_uuid || "";
  };

  // 获取Item名称
  const getItemName = (item: Partial<Items & { item_uuid?: string }>) => {
    return item.name || `Item ${getItemUuid(item)}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New User Case</CardTitle>
        <CardDescription>
          Add a new user case by extracting content from social media
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form ref={formRef} className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="type">Media Type</Label>
              <Select 
                value={type} 
                onValueChange={(value: UserCaseType) => setType(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select media type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="twitter">Twitter/X</SelectItem>
                  <SelectItem value="jike">Jike</SelectItem>
                  <SelectItem value="youtube">YouTube</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="url">URL</Label>
              <div className="flex gap-2">
                <Input
                  id="url"
                  placeholder="Enter media URL"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                />
                <Button 
                  onClick={handleExtractUrl}
                  disabled={isPending || !url.trim()}
                  type="button"
                >
                  Extract
                </Button>
              </div>
            </div>
          </div>
          
          {extractedData && (
            <>
              <div className="mt-4 rounded-lg border p-4">
                <h3 className="mb-4 text-lg font-semibold">Content Preview</h3>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div>
                    <p className="mb-2"><span className="font-semibold">Title:</span> {extractedData.title}</p>
                    <p className="mb-2"><span className="font-semibold">Author:</span> {extractedData.authorName}</p>
                    <p className="mb-2"><span className="font-semibold">Type:</span> {extractedData.type}</p>
                    {(extractedData.imageUrls && extractedData.imageUrls.length > 0) && (
                      <>
                        <p className="mb-2"><span className="font-semibold">Images:</span> {extractedData.imageUrls.length}</p>
                        <div className="mb-2">
                          <details className="text-xs">
                            <summary className="cursor-pointer text-blue-500 hover:text-blue-700">Show image URLs</summary>
                            <div className="mt-2 max-h-32 overflow-y-auto rounded border p-2">
                              {extractedData.imageUrls.map((url: string, idx: number) => (
                                <div key={idx} className="mb-1 truncate">
                                  <a href={url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                    {url}
                                  </a>
                                </div>
                              ))}
                            </div>
                          </details>
                        </div>
                      </>
                    )}
                    {(extractedData.videoUrls && extractedData.videoUrls.length > 0) && (
                      <>
                        <p className="mb-2"><span className="font-semibold">Videos:</span> {extractedData.videoUrls.length}</p>
                        <div className="mb-2">
                          <details className="text-xs">
                            <summary className="cursor-pointer text-blue-500 hover:text-blue-700">Show video URLs</summary>
                            <div className="mt-2 max-h-32 overflow-y-auto rounded border p-2">
                              {extractedData.videoUrls.map((url: string, idx: number) => (
                                <div key={idx} className="mb-1 truncate">
                                  <a href={url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                    {url}
                                  </a>
                                </div>
                              ))}
                            </div>
                          </details>
                        </div>
                      </>
                    )}
                  </div>
                  
                  <div>
                    {createPreviewUserCase() && (
                      <UserCaseCard userCase={createPreviewUserCase()!} />
                    )}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="ai-summary">AI Summary ({locale})</Label>
                <Textarea
                  id="ai-summary"
                  placeholder="Enter AI summary for this content"
                  value={aiSummary}
                  onChange={(e) => setAiSummary(e.target.value)}
                  className="min-h-20"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="content">Content ({locale})</Label>
                <Textarea
                  id="content"
                  placeholder="Enter additional content"
                  value={contentText}
                  onChange={(e) => setContentText(e.target.value)}
                  className="min-h-20"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Related Items</Label>
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <Input 
                      type="text" 
                      placeholder="Search Items..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button 
                      onClick={handleSearchItems}
                      disabled={isPending || !searchTerm.trim()}
                      type="button"
                    >
                      Search
                    </Button>
                  </div>
                  <div className="max-h-80 overflow-y-auto rounded-lg border p-4">
                    {items.length > 0 ? (
                      <div className="space-y-2">
                        <p className="mb-4 text-sm text-blue-500">显示 {items.length} 个Items，已选择 {selectedItems.length} 个</p>
                        {items
                          .sort((a, b) => {
                            // 排序：首先展示已选择的Items
                            const aUuid = getItemUuid(a);
                            const bUuid = getItemUuid(b);
                            const aSelected = selectedItems.includes(aUuid);
                            const bSelected = selectedItems.includes(bUuid);
                            
                            if (aSelected && !bSelected) return -1;
                            if (!aSelected && bSelected) return 1;
                            return getItemName(a).localeCompare(getItemName(b));
                          })
                          .map((item) => {
                            const itemUuid = getItemUuid(item);
                            if (!itemUuid) return null;
                            
                            return (
                              <div 
                                key={itemUuid} 
                                className={`flex items-center space-x-2 p-1 ${selectedItems.includes(itemUuid) ? 'bg-blue-50 rounded' : ''}`}
                              >
                                <input
                                  type="checkbox"
                                  id={`item-${itemUuid}`}
                                  checked={selectedItems.includes(itemUuid)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedItems([...selectedItems, itemUuid]);
                                    } else {
                                      setSelectedItems(selectedItems.filter(id => id !== itemUuid));
                                    }
                                  }}
                                  className="h-4 w-4 rounded border-gray-300"
                                />
                                <label 
                                  htmlFor={`item-${itemUuid}`} 
                                  className="text-sm flex items-center cursor-pointer w-full"
                                >
                                  <span className="font-medium">{getItemName(item)}</span>
                                  {item.brief && (
                                    <span className="ml-2 text-xs text-gray-500 truncate max-w-[200px]">
                                      - {item.brief}
                                    </span>
                                  )}
                                </label>
                              </div>
                            );
                          })}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Loading Items or no Items available</p>
                        {selectedItems.length > 0 && (
                          <p className="mt-2 text-sm text-amber-600">
                            You have {selectedItems.length} selected Items, but the Item list failed to load.
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                  {selectedItems.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium">Selected Items: {selectedItems.length}</p>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {selectedItems.map(uuid => {
                          const item = items.find(m => getItemUuid(m) === uuid);
                          return item ? (
                            <div key={uuid} className="flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs">
                              <span>{getItemName(item)}</span>
                              <button
                                className="ml-1 text-gray-500 hover:text-red-500"
                                onClick={() => setSelectedItems(selectedItems.filter(id => id !== uuid))}
                                type="button"
                              >
                                &times;
                              </button>
                            </div>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button 
                  onClick={handleCreateUserCase} 
                  disabled={isPending || !extractedData}
                  type="button"
                >
                  Create User Case
                </Button>
              </div>
            </>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
