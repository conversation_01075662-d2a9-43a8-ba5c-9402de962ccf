"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { UserCaseCard } from "@/components/blocks/usercase/usercase-card";
import { Items } from "@/types/items";
import { UserCase, UserCaseType } from "@/types/usercase";
import { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";

interface EditUserCaseFormProps {
  userCase: UserCase;
  items: Items[];
  locale: string;
  updateUserCaseAction: (formData: FormData) => Promise<{
    success: boolean;
    message: string;
    redirectTo: string;
  }>;
}

export function EditUserCaseForm({ userCase, items, locale, updateUserCaseAction }: EditUserCaseFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [title, setTitle] = useState("");
  const [status, setStatus] = useState<string>("online");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [aiSummary, setAiSummary] = useState("");
  const [contentText, setContentText] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState<Items[]>([]);

  // 初始化表单数据
  useEffect(() => {
    setTitle(userCase.title || "");
    setStatus(userCase.status || "online");
    setSelectedItems(userCase.related_items || []);
    
    // 获取本地化内容
    if (userCase.ai_summary && userCase.ai_summary[locale]) {
      setAiSummary(userCase.ai_summary[locale]);
    }
    
    if (userCase.content && userCase.content[locale]) {
      setContentText(userCase.content[locale]);
    }
    
    setFilteredItems(items);
  }, [userCase, locale, items]);

  // 搜索Items
  const handleSearchItems = () => {
    if (!searchTerm.trim()) {
      setFilteredItems(items);
      return;
    }
    
    const searchLower = searchTerm.toLowerCase();
    const results = items.filter(item => 
      item.name.toLowerCase().includes(searchLower) || 
      (item.brief && item.brief.toLowerCase().includes(searchLower))
    );
    
    setFilteredItems(results);
    
    toast({
      title: "Search Results",
      description: `Found ${results.length} matching Items`,
    });
  };

  // 提交表单
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    toast({
      title: "Processing",
      description: "Updating user case...",
    });
    
    const formData = new FormData();
    formData.append("uuid", userCase.uuid || "");
    formData.append("title", title || "");
    formData.append("status", status || "online");
    formData.append("ai_summary", aiSummary || "");
    formData.append("content", contentText || "");
    formData.append("selected_items", JSON.stringify(selectedItems || []));
    formData.append("locale", locale || "en");
    
    startTransition(async () => {
      try {
        const result = await updateUserCaseAction(formData);
        
        toast({
          title: "Success",
          description: result.message,
        });
        
        if (result.redirectTo) {
          router.push(result.redirectTo);
        }
      } catch (error) {
        console.error("Error updating user case:", error);
        toast({
          title: "Error",
          description: String(error) || "Failed to update user case",
          variant: "destructive",
        });
      }
    });
  };

  // 创建预览用户案例
  const createPreviewUserCase = (): UserCase => {
    return {
      ...userCase,
      title,
      status,
      related_items: selectedItems,
      content: {
        ...userCase.content,
        [locale]: contentText
      },
      ai_summary: {
        ...userCase.ai_summary,
        [locale]: aiSummary
      }
    };
  };

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <div className="lg:col-span-2">
        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Edit User Case</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Enter title"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select value={status} onValueChange={setStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="created">Created</SelectItem>
                        <SelectItem value="online">Online</SelectItem>
                        <SelectItem value="offline">Offline</SelectItem>
                        <SelectItem value="deleted">Deleted</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="ai-summary">AI Summary ({locale})</Label>
                  <Textarea
                    id="ai-summary"
                    name="ai_summary"
                    value={aiSummary}
                    onChange={(e) => setAiSummary(e.target.value)}
                    placeholder="Enter AI summary"
                    className="min-h-20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="content">Content ({locale})</Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={contentText}
                    onChange={(e) => setContentText(e.target.value)}
                    placeholder="Enter content"
                    className="min-h-20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Related Items</Label>
                  <div className="space-y-4">
                    <div className="flex space-x-2">
                      <Input 
                        type="text" 
                        placeholder="Search Items..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <Button 
                        onClick={handleSearchItems}
                        disabled={isPending}
                        type="button"
                      >
                        Search
                      </Button>
                    </div>
                    <div className="max-h-48 overflow-y-auto rounded-lg border p-4">
                      {filteredItems.length > 0 ? (
                        <div className="space-y-2">
                          <p className="mb-4 text-sm text-blue-500">显示 {filteredItems.length} 个Items，已选择 {selectedItems.length} 个</p>
                          {filteredItems
                            .sort((a, b) => {
                              // 排序：首先展示已选择的Items
                              const aSelected = selectedItems.includes(a.uuid);
                              const bSelected = selectedItems.includes(b.uuid);
                              
                              if (aSelected && !bSelected) return -1;
                              if (!aSelected && bSelected) return 1;
                              return a.name.localeCompare(b.name);
                            })
                            .map((item) => (
                              <div key={item.uuid} className={`flex items-center space-x-2 p-1 ${selectedItems.includes(item.uuid) ? 'bg-blue-50 rounded' : ''}`}>
                                <input
                                  type="checkbox"
                                  id={`item-${item.uuid}`}
                                  checked={selectedItems.includes(item.uuid)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedItems([...selectedItems, item.uuid]);
                                    } else {
                                      setSelectedItems(selectedItems.filter(id => id !== item.uuid));
                                    }
                                  }}
                                  className="h-4 w-4 rounded border-gray-300"
                                />
                                <label htmlFor={`item-${item.uuid}`} className="text-sm flex items-center cursor-pointer w-full">
                                  <span className="font-medium">{item.name}</span>
                                  {item.brief && (
                                    <span className="ml-2 text-xs text-gray-500 truncate max-w-[200px]">
                                      - {item.brief}
                                    </span>
                                  )}
                                </label>
                              </div>
                            ))}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-gray-500">No Items found</p>
                        </div>
                      )}
                    </div>
                    {selectedItems.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium">Selected Items: {selectedItems.length}</p>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {selectedItems.map((uuid: string) => {
                            const item = items.find(m => m.uuid === uuid);
                            return (
                              <div key={uuid} className="flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs">
                                <span>{item?.name || uuid}</span>
                                <button
                                  className="ml-1 text-gray-500 hover:text-red-500"
                                  onClick={() => setSelectedItems(selectedItems.filter(id => id !== uuid))}
                                  type="button"
                                >
                                  &times;
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button type="submit" disabled={isPending}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
      
      <div>
        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <UserCaseCard userCase={createPreviewUserCase()} />
          </CardContent>
        </Card>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Basic Info</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex flex-wrap">
                <dt className="w-20 font-medium text-gray-500">Type:</dt>
                <dd className="flex-1 capitalize">{userCase.type}</dd>
              </div>
              <div className="flex flex-wrap">
                <dt className="w-20 font-medium text-gray-500">URL:</dt>
                <dd className="flex-1 break-all text-sm">
                  <a href={userCase.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                    {userCase.url}
                  </a>
                </dd>
              </div>
              <div className="flex flex-wrap">
                <dt className="w-20 font-medium text-gray-500">Author:</dt>
                <dd className="flex-1">{userCase.author_name || 'Unknown'}</dd>
              </div>
              <div className="flex flex-wrap">
                <dt className="w-20 font-medium text-gray-500">Media:</dt>
                <dd className="flex-1">
                  {userCase.image_urls?.length || 0} images, {userCase.video_urls?.length || 0} videos
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 