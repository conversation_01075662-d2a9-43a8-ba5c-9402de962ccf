'use client';

import React, { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Loader2, RefreshCw } from 'lucide-react';

type FormData = {
  website_url: string;
  name: string;
  author_name: string;
  item_avatar_url: string;
  user_avatar_url: string;
  email: string;
  subscribe_newsletter: boolean;
  detail: string;
};

type PrefetchData = {
  name: string;
  author_name: string;
  website_url: string;
  item_avatar_url: string;
  user_avatar_url: string;
  detail: string;
  error?: string;
};

interface FormProps {
  initialData?: {
    name: string;
    author_name: string;
    website_url: string;
    item_avatar_url: string;
    user_avatar_url: string;
    email: string;
    subscribe_newsletter: boolean;
  };
  onSubmit?: (data: FormData) => void;
}

const SubmitForm = ({ initialData, onSubmit }: FormProps) => {
  const router = useRouter();
  const { locale } = useParams() || { locale: 'en' };
  const [formData, setFormData] = useState<FormData>({
    website_url: '',
    name: '',
    author_name: '',
    item_avatar_url: '',
    user_avatar_url: '',
    email: '',
    subscribe_newsletter: true,
    detail: '',
  });
  
  const [isValid, setIsValid] = useState(false);
  const [isPrefetching, setIsPrefetching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const isGitHubUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return (urlObj.hostname === 'github.com' || urlObj.hostname === 'www.github.com') && urlObj.pathname.split('/').filter(Boolean).length >= 2;
    } catch {
      return false;
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    if (name === 'website_url') {
      setIsValid(isGitHubUrl(value));
    }
  };
  
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };
  
  const prefetchFromGitHub = async () => {
    if (!isValid) return;
    
    setIsPrefetching(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/items/prefetch?url=${encodeURIComponent(formData.website_url)}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch repository data');
      }
      
      const data: PrefetchData = await response.json();
      
      if (data.error) {
        setError(data.error);
      } else {
        setFormData(prev => ({
          ...prev,
          name: data.name || prev.name,
          author_name: data.author_name || prev.author_name,
          item_avatar_url: data.item_avatar_url || prev.item_avatar_url,
          user_avatar_url: data.user_avatar_url || prev.user_avatar_url,
          detail: data.detail || prev.detail,
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
    } finally {
      setIsPrefetching(false);
    }
  };
  
  // Subscribe to newsletter if checkbox is checked
  const subscribeToNewsletter = async (email: string) => {
    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          source: 'item_submission'
        }),
      });
      
      if (!response.ok) {
        console.error('Failed to subscribe to newsletter');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.website_url || !formData.name || !formData.author_name) {
      setError('Please fill in all required fields');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Call onSubmit if provided, otherwise use default submission logic
      if (onSubmit) {
        onSubmit(formData);
      } else {
        const response = await fetch('/api/items/submit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to submit Item');
        }
        
        // If email is provided and newsletter subscription is checked, subscribe to the newsletter
        if (formData.email && formData.subscribe_newsletter) {
          await subscribeToNewsletter(formData.email);
        }
        
        setSuccess(true);
        // Reset form
        setFormData({
          website_url: '',
          name: '',
          author_name: '',
          item_avatar_url: '',
          user_avatar_url: '',
          email: '',
          subscribe_newsletter: true,
          detail: '',
        });
        
        // Redirect after successful submission
        setTimeout(() => {
          router.push(`/${locale}`);
        }, 3000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while submitting');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const updateFormData = (data: Partial<FormData>) => {
    setFormData((prev) => ({
      ...prev,
      name: data.name || prev.name,
      author_name: data.author_name || prev.author_name,
      website_url: data.website_url || prev.website_url,
      item_avatar_url: data.item_avatar_url || prev.item_avatar_url,
      user_avatar_url: data.user_avatar_url || prev.user_avatar_url,
      email: data.email || prev.email,
      subscribe_newsletter:
        typeof data.subscribe_newsletter !== 'undefined'
          ? data.subscribe_newsletter
          : prev.subscribe_newsletter,
    }));
  };
  
  const resetForm = () => {
    setFormData({
      name: '',
      author_name: '',
      website_url: '',
      item_avatar_url: '',
      user_avatar_url: '',
      email: '',
      subscribe_newsletter: true,
      detail: '',
    });
  };
  
  if (success) {
    return (
      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-6 rounded-lg text-center">
        <h2 className="text-xl font-semibold mb-2">Thank you for your submission!</h2>
        <p className="text-muted-foreground mb-4">
          Your Item has been submitted successfully and is now awaiting review.
          You will be redirected to the home page shortly.
        </p>
        <div className="flex justify-center">
          <Loader2 className="animate-spin h-6 w-6 text-muted-foreground" />
        </div>
      </div>
    );
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-lg text-red-600 dark:text-red-400">
          {error}
        </div>
      )}
      
      <div className="space-y-1">
        <label htmlFor="website_url" className="block text-sm font-medium">
          GitHub Repository URL <span className="text-red-500">*</span>
        </label>
        <div className="flex gap-2">
          <input
            type="url"
            id="website_url"
            name="website_url"
            value={formData.website_url}
            onChange={handleChange}
            placeholder="https://github.com/username/repository"
            className="flex-1 px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            required
          />
          <button
            type="button"
            onClick={prefetchFromGitHub}
            disabled={!isValid || isPrefetching}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50 disabled:pointer-events-none"
          >
            {isPrefetching ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <RefreshCw className="h-5 w-5" />
            )}
          </button>
        </div>
        <p className="text-xs text-muted-foreground">
          Enter the GitHub repository URL of your Item and click the button to auto-fill information.
        </p>
      </div>
      
      <div className="space-y-1">
        <label htmlFor="name" className="block text-sm font-medium">
          Item Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="Item Name"
          className="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          required
        />
      </div>
      
      <div className="space-y-1">
        <label htmlFor="author_name" className="block text-sm font-medium">
          Author/Organization Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="author_name"
          name="author_name"
          value={formData.author_name}
          onChange={handleChange}
          placeholder="Author Name"
          className="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          required
        />
      </div>
      
      <div className="space-y-1">
        <label htmlFor="item_avatar_url" className="block text-sm font-medium">
          Item Avatar URL (optional)
        </label>
        <input
          type="text"
          id="item_avatar_url"
          name="item_avatar_url"
          value={formData.item_avatar_url}
          onChange={handleChange}
          className="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        />
        <p className="text-xs text-muted-foreground">
          Optional URL to an avatar image for your Item
        </p>
      </div>
      
      <div className="space-y-1">
        <label htmlFor="email" className="block text-sm font-medium">
          Contact Email
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="<EMAIL>"
          className="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        />
        <p className="text-xs text-muted-foreground">
          Optional email for contact about your submission (not publicly displayed)
        </p>
      </div>
      
      <div className="space-y-1">
        <label htmlFor="detail" className="block text-sm font-medium">
          Description
        </label>
        <textarea
          id="detail"
          name="detail"
          value={formData.detail}
          onChange={handleChange}
          rows={5}
          placeholder="Describe your Item..."
          className="w-full px-3 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        />
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="subscribe_newsletter"
          name="subscribe_newsletter"
          checked={formData.subscribe_newsletter}
          onChange={handleCheckboxChange}
          className="h-4 w-4 text-primary rounded"
        />
        <label htmlFor="subscribe_newsletter" className="ml-2 text-sm text-muted-foreground">
          Subscribe to updates about your submission and Item Hub news
        </label>
      </div>
      
      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50 disabled:pointer-events-none"
      >
        {isSubmitting ? (
          <span className="flex items-center justify-center">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Submitting...
          </span>
        ) : (
          'Submit Item'
        )}
      </button>
    </form>
  );
};

export default SubmitForm; 