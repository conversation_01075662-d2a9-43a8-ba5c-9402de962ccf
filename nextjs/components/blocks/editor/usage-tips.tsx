import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { useTranslations } from 'next-intl'

interface UsageTipsProps {
  productName: string
  creditCost: number
  isSignedIn?: boolean
}

export default function UsageTips({ productName, creditCost, isSignedIn }: UsageTipsProps) {
  const t = useTranslations('editor')
  
  return (
    <Card className="mt-4">
      <CardContent className="pt-4">
        <CardTitle className="text-base mb-2">{t('usage_tips_title')}</CardTitle>
        <ul className="text-sm text-muted-foreground space-y-2">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>{t('usage_credit_cost', { productName, creditCost, s: creditCost > 1 ? 's' : '' })}</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>{t('usage_supported_formats')}</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>{t('usage_max_file_size')}</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>{t('usage_processing_time')}</span>
          </li>
          {!isSignedIn && (
            <li className="flex items-start text-primary font-semibold">
              <span className="mr-2">•</span>
              <span>{t('usage_sign_in')}</span>
            </li>
          )}
        </ul>
      </CardContent>
    </Card>
  )
} 