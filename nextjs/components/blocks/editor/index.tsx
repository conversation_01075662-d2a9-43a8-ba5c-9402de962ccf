'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { Spinner } from '@/components/ui/spinner'
import { AiOutlineCloudUpload } from 'react-icons/ai'
import { AiTask, AiTaskStatus } from '@/types/aiTask'
import UsageTips from './usage-tips'
import { useTranslations } from 'next-intl'
import { newStorage } from '@/lib/storage'
import { useSession } from 'next-auth/react'
// Define props for the Editor component
interface EditorProps {
  productName: string
  creditCost: number
  locale: string
}

export default function Editor({ productName = "AI Processing", creditCost = 1, locale}: EditorProps) {
  const t = useTranslations('editor')
  const router = useRouter()
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string>('')
  const [fileType, setFileType] = useState<string>('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [isSignedIn, setIsSignedIn] = useState(false)
  const { data: session, status } = useSession();
  const [result, setResult] = useState<{
    text: string | null
    image: string | null
    options: Record<string, any> | null
  }>({
    text: null,
    image: null,
    options: null
  })
  const [displayMode, setDisplayMode] = useState<'text' | 'json' | 'image'>('text')
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setIsSignedIn(!!session)
  }, [])

  // Handle file upload
  const handleFileUpload = (file: File) => {
    // Validate file type and size
    const validTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg', 'image/webp']
    if (!validTypes.includes(file.type)) {
      toast.error(t('error_file_type'))
      return false
    }
    
    // Limit file size to 10MB
    if (file.size > 10 * 1024 * 1024) {
      toast.error(t('error_file_size'))
      return false
    }
    
    // Create URL for the uploaded file
    const fileUrl = URL.createObjectURL(file)
    setUploadedFileUrl(fileUrl)
    setUploadedFile(file)
    setFileType(file.type)
    toast.success(t('file_selected'))
    return true
  }

  // Handle file removal
  const handleRemoveFile = () => {
    if (uploadedFileUrl) {
      URL.revokeObjectURL(uploadedFileUrl)
    }
    setUploadedFileUrl('')
    setUploadedFile(null)
    setFileType('')
    setResult({
      text: null,
      image: null,
      options: null
    })
    
    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Redirect to sign in page if not signed in
  const handleAuthRequired = () => {
    if (!isSignedIn) {
      router.push(`/${locale}/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`)
      return true
    }
    return false
  }

  // Process AI task
  const processAiTask = async () => {
    if (handleAuthRequired()) return
    
    if (!uploadedFile) {
      toast.error(t('error_no_file'))
      return
    }

    setIsProcessing(true)
    
    try {
      // Create form data to upload file
      const formData = new FormData()
      formData.append('file', uploadedFile)
      
      // Upload the file using storage lib
      const storage = newStorage()
      const fileBuffer = await uploadedFile.arrayBuffer()
      const buffer = Buffer.from(fileBuffer)
      const fileKey = `${process.env.STORAGE_BUCKET}/${Date.now()}-${uploadedFile.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
      
      // Upload the file
      const uploadResult = await storage.uploadFile({
        body: buffer,
        key: fileKey,
        contentType: uploadedFile.type,
      })
      
      const inputFilePath = uploadResult.url
      
      // Create AI task
      const aiTaskResponse = await fetch('/api/ai-task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          product_name: productName,
          credit_cost: creditCost,
          input_file_path: inputFilePath,
          output_options: {
            locale,
            fileType: uploadedFile.type,
            fileName: uploadedFile.name,
            // Add any other options needed for processing
          }
        })
      })
      
      if (!aiTaskResponse.ok) {
        const errorData = await aiTaskResponse.json()
        throw new Error(errorData.error || t('error_create_task'))
      }
      
      const aiTaskData = await aiTaskResponse.json() as AiTask
      
      // Check if the task is processed synchronously
      if (aiTaskData.orderstatus === 'SUCCEED') {
        // Task completed synchronously
        setResult({
          text: aiTaskData.output_text || null,
          image: aiTaskData.output_image_path || null,
          options: aiTaskData.output_options || null
        })
        setIsProcessing(false)
        toast.success(t('task_complete'))
      } else if (aiTaskData.orderstatus === 'FAILED') {
        // Task failed
        setIsProcessing(false)
        toast.error(`${t('task_failed')}: ${aiTaskData.fail_reason || t('unknown_error')}`)
      } else {
        // Task is pending, start polling
        pollTaskStatus(aiTaskData.order_no)
      }
    } catch (error) {
      setIsProcessing(false)
      toast.error(error instanceof Error ? error.message : t('unexpected_error'))
    }
  }

  // Poll for task status
  const pollTaskStatus = async (orderNo: string) => {
    const maxAttempts = 60 // Maximum 60 attempts
    const interval = 3000 // 3 seconds interval
    let attempts = 0
    
    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/ai-task/${orderNo}`)
        
        if (!response.ok) {
          throw new Error(`${t('error')}: ${response.status}`)
        }
        
        const task = await response.json() as AiTask
        
        // Handle different task statuses
        if (task.orderstatus === 'SUCCEED') {
          // Task completed successfully
          setResult({
            text: task.output_text || null,
            image: task.output_image_path || null,
            options: task.output_options || null
          })
          setIsProcessing(false)
          toast.success(t('task_complete'))
          return true
        } else if (task.orderstatus === 'FAILED') {
          // Task failed
          setIsProcessing(false)
          toast.error(`${t('task_failed')}: ${task.fail_reason || t('unknown_error')}`)
          return true
        } else if (attempts >= maxAttempts) {
          // Max attempts reached
          setIsProcessing(false)
          toast.warning(t('task_timeout'))
          return true
        }
        
        // Continue polling
        return false
      } catch (error) {
        console.error('Error checking task status:', error)
        if (attempts >= maxAttempts) {
          setIsProcessing(false)
          toast.error(t('error_polling'))
          return true
        }
        return false
      }
    }
    
    // Start polling
    while (attempts < maxAttempts) {
      attempts++
      const isDone = await checkStatus()
      if (isDone) break
      
      // Update progress notification
      if (attempts % 5 === 0) {
        toast.loading(`${t('processing')}... (${attempts * 3}s)`)
      }
      
      // Wait for the interval
      await new Promise(resolve => setTimeout(resolve, interval))
    }
  }

  // Handle copy result
  const handleCopyResult = () => {
    if (displayMode === 'text' && result.text) {
      navigator.clipboard.writeText(result.text)
      toast.success(t('copied_text'))
    } else if (displayMode === 'json' && result.options) {
      navigator.clipboard.writeText(JSON.stringify(result.options, null, 2))
      toast.success(t('copied_json'))
    }
  }

  // Clean up URLs when component unmounts
  useEffect(() => {
    return () => {
      if (uploadedFileUrl) {
        URL.revokeObjectURL(uploadedFileUrl)
      }
    }
  }, [uploadedFileUrl])

  return (
    <div className="w-full max-w-screen-xl mx-auto px-4 sm:px-6 mb-12">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Upload Area */}
        <Card className="w-full md:w-1/2">
          <CardHeader>
            <CardTitle>{t('upload_title')}</CardTitle>
            <CardDescription>
              {t('upload_description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div 
              className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors hover:border-primary"
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="flex justify-center mb-4">
                <AiOutlineCloudUpload className="w-12 h-12 text-muted-foreground" />
              </div>
              <p className="text-lg text-muted-foreground">
                {t('drop_files')}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                {t('max_file_size')}
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept="application/pdf,.pdf,image/jpeg,.jpg,.jpeg,image/png,.png,image/webp,.webp"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0]
                  if (file) {
                    handleFileUpload(file)
                  }
                }}
              />
            </div>
            
            {uploadedFile && (
              <div className="mt-4 p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 flex items-center justify-center bg-primary/10 rounded-lg overflow-hidden">
                      {fileType.startsWith('image/') ? (
                        <img 
                          src={uploadedFileUrl} 
                          alt={uploadedFile.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <div className="font-medium">
                        {uploadedFile.name}
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <span>{t('file_type')}:</span>
                        <span className="font-medium">
                          {fileType === 'application/pdf' ? 'PDF' : fileType.split('/')[1].toUpperCase()}
                        </span>
                        <span className="mx-1">•</span>
                        <span>{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveFile}
                    aria-label={t('remove_file')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </Button>
                </div>
                
                {/* Preview for images */}
                {fileType.startsWith('image/') && (
                  <div className="mt-4 p-2 border rounded-lg bg-muted/50">
                    <div className="flex justify-center">
                      <img 
                        src={uploadedFileUrl} 
                        alt={uploadedFile.name}
                        className="max-h-48 max-w-full object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {/* Usage Tips */}
            <UsageTips 
              productName={productName} 
              creditCost={creditCost} 
              isSignedIn={isSignedIn} 
            />
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              onClick={processAiTask}
              disabled={!uploadedFile || isProcessing}
            >
              {isProcessing ? (
                <>
                  <Spinner className="mr-2" />
                  {t('processing')}...
                </>
              ) : (
                t('process_file')
              )}
            </Button>
          </CardFooter>
        </Card>
        
        {/* Result Area */}
        <Card className="w-full md:w-1/2">
          <CardHeader>
            <CardTitle>{t('results_title')}</CardTitle>
            <div className="flex justify-between items-center">
              <CardDescription>
                {t('results_description')}
              </CardDescription>
              {(result.text || result.image || result.options) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyResult}
                  disabled={displayMode === 'image' || (!result.text && !result.options)}
                >
                  {t('copy')}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isProcessing ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Spinner size="lg" />
                <p className="mt-4 text-muted-foreground">{t('processing_file')}...</p>
              </div>
            ) : (
              <>
                {(result.text || result.image || result.options) ? (
                  <Tabs defaultValue="text" value={displayMode} onValueChange={(v) => setDisplayMode(v as 'text' | 'json' | 'image')}>
                    <TabsList className="mb-4">
                      <TabsTrigger value="text" disabled={!result.text}>{t('tab_text')}</TabsTrigger>
                      <TabsTrigger value="json" disabled={!result.options}>{t('tab_json')}</TabsTrigger>
                      <TabsTrigger value="image" disabled={!result.image}>{t('tab_image')}</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="text">
                      {result.text ? (
                        <Textarea 
                          value={result.text} 
                          readOnly 
                          className="min-h-[300px]" 
                        />
                      ) : (
                        <p className="text-center py-8 text-muted-foreground">{t('no_text_result')}</p>
                      )}
                    </TabsContent>
                    
                    <TabsContent value="json">
                      {result.options ? (
                        <pre className="p-4 bg-muted rounded-md overflow-auto max-h-[300px]">
                          {JSON.stringify(result.options, null, 2)}
                        </pre>
                      ) : (
                        <p className="text-center py-8 text-muted-foreground">{t('no_json_result')}</p>
                      )}
                    </TabsContent>
                    
                    <TabsContent value="image">
                      {result.image ? (
                        <div className="flex justify-center">
                          <img 
                            src={result.image} 
                            alt={t('result_image')} 
                            className="max-h-[300px] object-contain" 
                          />
                        </div>
                      ) : (
                        <p className="text-center py-8 text-muted-foreground">{t('no_image_result')}</p>
                      )}
                    </TabsContent>
                  </Tabs>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <p>{t('upload_process_prompt')}</p>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 