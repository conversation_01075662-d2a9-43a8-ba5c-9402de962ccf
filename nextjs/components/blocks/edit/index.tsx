'use client';

import { useState, useEffect } from "react";
import { Items, ItemLocalization } from "@/types/items";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Edit, Loader2 } from "lucide-react";
import { getItemLocalizationsByUuid } from "@/models/items";

export default function EditButton({ 
  item, 
  onSave 
}: { 
  item: Items, 
  onSave: (data: Partial<Items> & { localizations: Partial<ItemLocalization>[] }) => Promise<{ success: boolean; error?: string } | undefined> 
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [name, setName] = useState(item.name);
  const [brief, setBrief] = useState(item.brief);
  const [itemAvatarUrl, setItemAvatarUrl] = useState(item.item_avatar_url || "");
  const [userAvatarUrl, setUserAvatarUrl] = useState(item.user_avatar_url || "");
  const [websiteUrl, setWebsiteUrl] = useState(item.website_url);
  const [authorName, setAuthorName] = useState(item.author_name);
  const [tags, setTags] = useState(item.tags ? item.tags.join(", ") : "");
  const [isRecommended, setIsRecommended] = useState(item.is_recommended);
  const [isOfficial, setIsOfficial] = useState(item.is_official);
  const [isPublic, setIsPublic] = useState(item.allow_public);
  const [isSaving, setIsSaving] = useState(false);
  const [localizations, setLocalizations] = useState<Partial<ItemLocalization>[]>([]);
  const [localizationLoading, setLocalizationLoading] = useState(true);
  
  useEffect(() => {
    setLocalizationLoading(true);
    Promise.all([
      getItemLocalizationsByUuid(item.uuid),
      Promise.resolve(item.item_location ? JSON.parse(item.item_location) : {})
    ]).then(([res, itemLocObjRaw]) => {
      const itemLocObj = (itemLocObjRaw && typeof itemLocObjRaw === 'object') ? itemLocObjRaw : {};
      const locs = res.data && res.data.length > 0 ? res.data : [];
      const allLangs = new Set([
        ...locs.map(l => l.language_code),
        ...Object.keys(itemLocObj || {})
      ]);
      const merged = Array.from(allLangs).map(lang => {
        const dbLoc = locs.find(l => l.language_code === lang) || {};
        let locObj: any = {};
        if (typeof lang === 'string' && itemLocObj && typeof itemLocObj === 'object' && itemLocObj !== null && Object.prototype.hasOwnProperty.call(itemLocObj, lang)) {
          locObj = (itemLocObj as Record<string, any>)[lang] || {};
        }
        return {
          language_code: lang,
          brief: dbLoc.brief || locObj.brief || "",
          processinfo: dbLoc.processinfo || locObj.processinfo || ""
        };
      });
      setLocalizations(merged.length > 0 ? merged : [{ language_code: "en", brief: item.brief, processinfo: "" }]);
      setLocalizationLoading(false);
    });
  }, [item.uuid, item.item_location]);

  const handleLocalizationChange = (idx: number, field: "language_code" | "brief" | "processinfo", value: string) => {
    setLocalizations(locs => {
      const copy = [...locs];
      copy[idx] = { ...copy[idx], [field]: value };
      return copy;
    });
  };

  const handleAddLocalization = () => {
    setLocalizations(locs => [...locs, { language_code: "", brief: "", processinfo: "" }]);
  };

  const handleRemoveLocalization = (idx: number) => {
    setLocalizations(locs => locs.filter((_, i) => i !== idx));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      // Format tags as array
      const tagsArray = tags
        .split(",")
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);
      
      const result = await onSave({
        uuid: item.uuid,
        name,
        brief: localizations.filter(l => l.language_code==='en').map(l => l.brief).join("\n"),
        item_avatar_url: itemAvatarUrl,
        user_avatar_url: userAvatarUrl,
        website_url: websiteUrl,
        author_name: authorName,
        tags: tagsArray,
        is_recommended: isRecommended,
        is_official: isOfficial,
        allow_public: isPublic,
        localizations: localizations.filter(l => l.language_code && l.brief)
      });
      
      setIsSaving(false);
      
      if (result?.success) {
        setIsDialogOpen(false);
      } else {
        alert("Failed to save Item");
      }
    } catch (error) {
      console.error("Error saving Item:", error);
      setIsSaving(false);
      alert("Failed to save Item");
    }
  };
  
  return (
    <div className="flex gap-2">
      <Button 
        variant="outline" 
        size="icon" 
        className="h-8 w-8"
        onClick={() => setIsDialogOpen(true)}
      >
        <Edit className="h-4 w-4" />
      </Button>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Edit Item: {item.name}</DialogTitle>
            <DialogDescription>
              Make changes to the Item here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="brief" className="text-right">Brief</Label>
              <Textarea
                id="brief"
                value={brief}
                onChange={(e) => setBrief(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="item_avatar" className="text-right">Item Avatar URL</Label>
              <Input
                id="item_avatar"
                value={itemAvatarUrl}
                onChange={(e) => setItemAvatarUrl(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="user_avatar" className="text-right">User Avatar URL</Label>
              <Input
                id="user_avatar"
                value={userAvatarUrl}
                onChange={(e) => setUserAvatarUrl(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="website" className="text-right">Website URL</Label>
              <Input
                id="website"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="author" className="text-right">Author Name</Label>
              <Input
                id="author"
                value={authorName}
                onChange={(e) => setAuthorName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tags" className="text-right">Tags</Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="comma, separated, tags"
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="recommended" className="text-right">Recommended</Label>
              <div className="col-span-3">
                <Switch
                  id="recommended"
                  checked={isRecommended}
                  onCheckedChange={setIsRecommended}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="official" className="text-right">Official</Label>
              <div className="col-span-3">
                <Switch
                  id="official"
                  checked={isOfficial}
                  onCheckedChange={setIsOfficial}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="public" className="text-right">Public</Label>
              <div className="col-span-3">
                <Switch
                  id="public"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
              </div>
            </div>
            <div className="border-t pt-4 mt-2">
              <div className="flex items-center justify-between mb-2">
                <span className="font-semibold">Localizations (brief & processinfo)</span>
                <Button size="sm" variant="outline" onClick={handleAddLocalization}>Add Language</Button>
              </div>
              {localizationLoading ? (
                <div>Loading...</div>
              ) : (
                <div className="flex flex-col gap-4">
                  {localizations.map((loc, idx) => (
                    <div key={idx} className="rounded border bg-white dark:bg-muted p-4 shadow-sm relative">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="inline-block px-2 py-0.5 rounded bg-blue-100 text-blue-800 text-xs font-mono uppercase border border-blue-300">{loc.language_code || "-"}</span>
                        <Button size="icon" variant="ghost" onClick={() => handleRemoveLocalization(idx)} disabled={localizations.length === 1} className="ml-auto">
                          ×
                        </Button>
                      </div>
                      <div className="mb-2">
                        <Label>Brief</Label>
                        <Textarea
                          value={loc.brief || ""}
                          onChange={e => handleLocalizationChange(idx, "brief", e.target.value)}
                          className="w-full"
                          placeholder={`Brief in ${loc.language_code || 'this language'}`}
                        />
                      </div>
                      <div>
                        <Label>Processinfo</Label>
                        <Textarea
                          value={loc.processinfo || ""}
                          onChange={e => handleLocalizationChange(idx, "processinfo", e.target.value)}
                          className="w-full"
                          placeholder={`Processinfo in ${loc.language_code || 'this language'}`}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 