'use client';

import React from 'react';
import Link from 'next/link';
import { Items } from '@/types/items';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Award } from 'lucide-react';

interface FeaturedItemsProps {
  items: Items[];
  locale: string;
}

export default function FeaturedItems({ items, locale }: FeaturedItemsProps) {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item) => (
        <Link key={item.uuid} href={`/${locale}/g/${item.uuid}`}>
          <Card className="h-full hover:shadow-md transition-shadow border-2 border-primary/10 hover:border-primary/20">
            <CardContent className="p-4 flex flex-col h-full">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-semibold text-lg line-clamp-1">{item.name}</h3>
                <div className="flex gap-1">
                  <Award className="h-5 w-5 text-primary" />
                </div>
              </div>
              
              <div className="flex items-center text-sm text-muted-foreground mb-2">
                <span>by {item.author_name || 'Unknown'}</span>
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2 mb-auto">
                {item.brief}
              </p>
              
              <div className="flex flex-wrap gap-1 mt-3">
                {item.tags && item.tags.slice(0, 3).map((tag, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="text-xs bg-muted/50"
                  >
                    {tag.includes('/') ? tag.split('/')[1] : tag}
                  </Badge>
                ))}
                {item.tags && item.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs bg-muted/50">
                    +{item.tags.length - 3}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
} 