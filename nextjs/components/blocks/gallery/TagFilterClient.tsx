'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { ChevronDown, ChevronUp, Search, Filter, RefreshCw, CheckCircle, Award } from 'lucide-react';

interface TagFilterClientProps {
  allTags: string[];
  selectedTags: string[];
  tagCounts: Record<string, number>;
  isOfficial: boolean;
  searchQuery?: string;
}

// Helper to update URLSearchParams with proper array handling
function updateSearchParams(
  currentParams: URLSearchParams,
  newParams: Record<string, string | string[] | undefined>
): URLSearchParams {
  const params = new URLSearchParams(currentParams.toString());
  
  // Clear the params that we're going to update
  Object.keys(newParams).forEach(key => {
    params.delete(key);
  });
  
  // Add the new parameter values
  Object.entries(newParams).forEach(([key, value]) => {
    if (value === undefined) {
      // If value is undefined, we've already deleted the param above
      return;
    }
    
    if (Array.isArray(value)) {
      // Handle array values (like tags)
      value.forEach(item => {
        params.append(key, item);
      });
    } else {
      // Handle single values
      params.set(key, value);
    }
  });
  
  return params;
}

export default function TagFilterClient({
  allTags,
  selectedTags,
  tagCounts,
  isOfficial,
  searchQuery = ''
}: TagFilterClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // State for searchbox input
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  
  // State for filtering UI
  const [expandedFilter, setExpandedFilter] = useState(true);
  
  // Group tags by their prefix
  const groupedTags = useMemo(() => {
    const groups: Record<string, string[]> = { default: [] };
    
    allTags.forEach(tag => {
      if (tag.includes('/')) {
        const [group] = tag.split('/');
        if (!groups[group]) {
          groups[group] = [];
        }
        groups[group].push(tag);
      } else {
        groups.default.push(tag);
      }
    });
    
    return groups;
  }, [allTags]);
  
  // State for expanded groups - all collapsed by default
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(() => {
    const groups: Record<string, boolean> = {};
    Object.keys(groupedTags).forEach(group => {
      groups[group] = false; // Initialize all groups as collapsed
    });
    return groups;
  });
  
  // Create record of filtered tags by group
  const filteredGroups = useMemo(() => {
    const filtered: Record<string, string[]> = {};
    
    Object.entries(groupedTags).forEach(([group, tags]) => {
      filtered[group] = tags;
    });
    
    return filtered;
  }, [groupedTags]);
  
  // Toggle a group's expanded state
  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };
  
  // Toggle official filter
  const toggleOfficial = () => {
    const newParams = updateSearchParams(searchParams, {
      official: isOfficial ? undefined : 'true',
      page: '1' // Reset to first page
    });
    
    router.push(`${pathname}?${newParams.toString()}`);
  };
  
  // Handle tag selection
  const handleTagSelect = (tag: string) => {
    let newTags: string[];
    
    if (selectedTags.includes(tag)) {
      // Remove tag if already selected
      newTags = selectedTags.filter(t => t !== tag);
    } else {
      // Add tag if not already selected
      newTags = [...selectedTags, tag];
    }
    
    // If no tags left, remove tags param entirely
    const tagsParam = newTags.length > 0 ? newTags : undefined;
    
    const newParams = updateSearchParams(searchParams, {
      tags: tagsParam,
      page: '1' // Reset to first page
    });
    
    router.push(`${pathname}?${newParams.toString()}`);
  };
  
  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };
  
  // Apply search when leaving search input
  const applySearch = () => {
    const newParams = updateSearchParams(searchParams, {
      query: localSearchQuery || undefined,
      page: '1' // Reset to first page
    });
    
    router.push(`${pathname}?${newParams.toString()}`);
  };
  
  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applySearch();
    }
  };
  
  // Clear all filters
  const clearAllFilters = () => {
    router.push(pathname);
  };
  
  // Toggle filter visibility on mobile
  const toggleFilterVisibility = () => {
    setExpandedFilter(prev => !prev);
  };
  
  const hasActiveFilters = selectedTags.length > 0 || isOfficial || searchQuery;

  return (
    <div className="sticky top-4 space-y-6">
      {/* Search box */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search tags..."
            value={localSearchQuery}
            onChange={handleSearchChange}
            onBlur={applySearch}
            onKeyDown={handleSearchKeyDown}
            className="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
      
      {/* Filter controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </h3>
          
          {hasActiveFilters && (
            <button 
              onClick={clearAllFilters}
              className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
            >
              Clear all
            </button>
          )}
        </div>
        
        {/* Item Properties */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Properties</h4>
          
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isOfficial}
                onChange={toggleOfficial}
                className="rounded text-blue-600 focus:ring-blue-500 h-4 w-4 mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300 flex items-center">
                <Award className="h-4 w-4 mr-1 text-blue-500" />
                Official
              </span>
            </label>
          </div>
        </div>
        
        {/* Tags */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Tags</h4>
          
          {/* Default tags (without group) */}
          {filteredGroups.default && filteredGroups.default.length > 0 && (
            <div className="mb-4">
              <button
                onClick={() => toggleGroup('default')}
                className="flex items-center justify-between w-full text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 mb-2 group transition-colors"
              >
                <span className="font-medium flex items-center">
                  General
                  <span className="ml-2 text-xs text-gray-500">
                    ({filteredGroups.default.length})
                  </span>
                </span>
                {expandedGroups.default ? (
                  <ChevronUp className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                )}
              </button>
              
              {expandedGroups.default && (
                <div className="pl-2 space-y-1 mb-2">
                  {filteredGroups.default.map(tag => (
                    <label key={tag} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedTags.includes(tag)}
                          onChange={() => handleTagSelect(tag)}
                          className="rounded text-blue-600 focus:ring-blue-500 h-4 w-4 mr-2"
                        />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {tag}
                        </span>
                      </div>
                      <span className="text-xs text-gray-400">
                        {tagCounts[tag] || 0}
                      </span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          )}
          
          {/* Grouped tags */}
          {Object.keys(filteredGroups)
            .filter(group => group !== 'default')
            .map(group => (
              <div key={group} className="mb-4">
                <button
                  onClick={() => toggleGroup(group)}
                  className="flex items-center justify-between w-full text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 mb-2 group transition-colors"
                >
                  <span className="font-medium capitalize flex items-center">
                    {group}
                    <span className="ml-2 text-xs text-gray-500">
                      ({filteredGroups[group].length})
                    </span>
                  </span>
                  {expandedGroups[group] ? (
                    <ChevronUp className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                  )}
                </button>
                
                {expandedGroups[group] && (
                  <div className="pl-2 space-y-1 mb-2">
                    {filteredGroups[group].map(tag => (
                      <label key={tag} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedTags.includes(tag)}
                            onChange={() => handleTagSelect(tag)}
                            className="rounded text-blue-600 focus:ring-blue-500 h-4 w-4 mr-2"
                          />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {tag.split('/')[1] || tag}
                          </span>
                        </div>
                        <span className="text-xs text-gray-400">
                          {tagCounts[tag] || 0}
                        </span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            ))}
        </div>
      </div>
    </div>
  );
} 