'use client';

import React from 'react';
import { Items } from '@/types/items';
import Card from './Card';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';

interface CardGridProps {
  items: Items[];
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onTagClick?: (tag: string) => void;
}

export default function CardGrid({ 
  items, 
  loading, 
  currentPage, 
  totalPages, 
  onPageChange,
  onTagClick
}: CardGridProps) {
  // Create pagination buttons
  const renderPaginationButtons = () => {
    const buttons = [];
    
    // Previous button
    buttons.push(
      <button
        key="prev"
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className={`flex items-center justify-center h-9 w-9 rounded-md ${
          currentPage === 1
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </button>
    );
    
    // Calculate which page buttons to show
    const pagesToShow = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pagesToShow.push(i);
      }
    } else {
      // Always show first page
      pagesToShow.push(1);
      
      // Calculate middle pages
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if at edges
      if (currentPage <= 2) {
        endPage = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        startPage = Math.max(2, totalPages - 3);
      }
      
      // Add ellipsis before middle pages if needed
      if (startPage > 2) {
        pagesToShow.push(-1); // -1 represents ellipsis
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pagesToShow.push(i);
      }
      
      // Add ellipsis after middle pages if needed
      if (endPage < totalPages - 1) {
        pagesToShow.push(-2); // -2 represents ellipsis (different key from first ellipsis)
      }
      
      // Always show last page
      if (totalPages > 1) {
        pagesToShow.push(totalPages);
      }
    }
    
    // Page number buttons
    pagesToShow.forEach(page => {
      if (page < 0) {
        // Ellipsis
        buttons.push(
          <span key={`ellipsis${page}`} className="px-2 text-gray-500">
            …
          </span>
        );
      } else {
        buttons.push(
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`flex items-center justify-center h-9 w-9 text-sm rounded-md ${
              currentPage === page
                ? 'bg-blue-500 text-white'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            aria-label={`Page ${page}`}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </button>
        );
      }
    });
    
    // Next button
    buttons.push(
      <button
        key="next"
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className={`flex items-center justify-center h-9 w-9 rounded-md ${
          currentPage === totalPages
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </button>
    );
    
    return buttons;
  };

  // Loading state
  if (loading && items.length === 0) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-10 w-10 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading Items...</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!loading && items.length === 0) {
    return (
      <div className="min-h-[40vh] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">No Items found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Try adjusting your filters or search terms to find what you're looking for.
          </p>
          <div className="flex justify-center">
            <button
              onClick={() => window.location.href = window.location.pathname}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Reset all filters
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Loading overlay when refreshing with existing items */}
      {loading && items.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-20 dark:bg-opacity-30 z-10 flex items-center justify-center pointer-events-none">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          </div>
        </div>
      )}
      
      {/* Cards grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map((item) => (
          <Card key={item.uuid} item={item} onTagClick={onTagClick} />
        ))}
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-12">
          <nav className="flex items-center space-x-1" aria-label="Pagination">
            {renderPaginationButtons()}
          </nav>
        </div>
      )}
    </div>
  );
} 