import { Suspense } from 'react';
import { GalleryProps } from '@/types/blocks/gallery';
import { getAllPublicItems, getAllTags, getTagCounts } from '@/models/items';
import TagFilterClient from './TagFilterClient';
import CardGridClient from './CardGridClient';
import MobileFilterControls from './MobileFilterControls';
import FeaturedItems from './FeaturedItems';
import { 
  Loader2, 
} from 'lucide-react';

// Server-side data fetching function
async function fetchGalleryData(
  page = 1, 
  itemsPerPage = 24, 
  selectedTags: string[] = [],
  isOfficial = false,
  isRecommended = false,
  locale = 'en'
) {
  // Fetch tags and tag counts
  const tagsResult = await getAllTags();
  const allTags = tagsResult.data?.map(item => item.tag) || [];
  
  const tagCountsResult = await getTagCounts();
  const countsMap: Record<string, number> = {};
  tagCountsResult.data?.forEach(item => {
    countsMap[item.tag] = item.count;
  });

  // Fetch Items with server-side filtering
  const response = await getAllPublicItems(
    itemsPerPage, 
    page,
    locale,
    {
      official: isOfficial,
      recommended: isRecommended,
      tags: selectedTags.length > 0 ? selectedTags : undefined
    }
  );
  
  const items = response.data || [];
  const totalCount = response.count || 0;
  const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

  return {
    items,
    allTags,
    tagCounts: countsMap,
    totalFilteredCount: totalCount,
    totalItemsCount: totalCount,
    totalPages
  };
}

// Fetch featured Items
async function fetchFeaturedItems(locale = 'en') {
  // Get recommended Items for featured section
  const { data: featuredItems } = await getAllPublicItems(
    9, // Limit to 6 featured Items
    1, // First page
    locale,
    { recommended: true }
  );
  
  return featuredItems || [];
}

export default async function Gallery({ 
  section,
  searchParams 
}: GalleryProps & { 
  searchParams?: { 
    page?: string; 
    limit?: string; 
    tags?: string | string[]; 
    official?: string; 
    recommended?: string;
    query?: string;
    locale?: string;
  }
}) {
  // Parse search parameters
  const page = searchParams?.page ? parseInt(searchParams.page, 10) : 1;
  const itemsPerPage = searchParams?.limit ? parseInt(searchParams.limit, 10) : 24;
  
  // Handle tags (can be string or array)
  let selectedTags: string[] = [];
  if (searchParams?.tags) {
    selectedTags = Array.isArray(searchParams.tags) 
      ? searchParams.tags 
      : [searchParams.tags];
  }
  
  // Parse boolean filters
  const isOfficial = searchParams?.official === 'true';
  const isRecommended = searchParams?.recommended === 'true';
  const searchQuery = searchParams?.query || '';

  // Get locale from URL or searchParams - using 'en' as default
  const locale = searchParams?.locale || 'en';

  // Fetch data server-side
  const { 
    items, 
    allTags, 
    tagCounts, 
    totalFilteredCount, 
    totalItemsCount, 
    totalPages 
  } = await fetchGalleryData(
    page, 
    itemsPerPage, 
    selectedTags,
    isOfficial,
    isRecommended,
    locale
  );

  // Fetch featured Items
  const featuredItems = await fetchFeaturedItems(locale);

  // Filter tags by search query if provided
  const filteredTags = searchQuery 
    ? allTags.filter((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    : allTags;

  const showFilters = section?.showFilters !== false;
  const hasActiveFilters = selectedTags.length > 0 || isOfficial || searchQuery !== '';

  return (
    <section className="py-8 bg-background dark:bg-background" aria-labelledby="gallery-heading">
      {section?.title && (
        <h2 id="gallery-heading" className="sr-only">
          {section.title}
        </h2>
      )}
      
              <div className="max-w-10xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Affiliate Products Section */}
        {/* <Suspense fallback={<div className="h-20"></div>}>
          <AffiliateGrid limit={3} variant="compact" />
        </Suspense> */}
        
        {/* Featured Items Section */}
        {featuredItems.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Featured Items</h2>
            <FeaturedItems items={featuredItems} locale={locale} />
          </div>
        )}
        
        {/* Mobile filter toggle - Client Component */}
        <Suspense fallback={null}>
          <div className="flex md:hidden justify-between items-center mb-4">
            <MobileFilterControls
              hasActiveFilters={hasActiveFilters}
              totalCount={totalItemsCount}
            />
          </div>
        </Suspense>
        
        {/* Main content */}
        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters sidebar - Client Component */}
          {showFilters && (
            <Suspense fallback={
              <div className="w-full md:w-1/4 lg:w-1/5">
                <div className="sticky top-4 space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                  </div>
                </div>
              </div>
            }>
              <div className="w-full md:w-1/4 lg:w-1/5">
                <TagFilterClient
                  allTags={filteredTags}
                  selectedTags={selectedTags}
                  tagCounts={tagCounts}
                  isOfficial={isOfficial}
                  searchQuery={searchQuery}
                />
              </div>
            </Suspense>
          )}
          
          {/* Gallery content - Client component for interactive parts */}
          <div className="flex-1">
            <Suspense fallback={
              <div className="min-h-[60vh] flex items-center justify-center">
                <div className="text-center">
                  <Loader2 className="h-10 w-10 animate-spin text-blue-500 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">Loading Items...</p>
                </div>
              </div>
            }>
              <CardGridClient
                items={items}
                currentPage={page}
                totalPages={totalPages}
                selectedTags={selectedTags}
                isOfficial={isOfficial}
                isRecommended={isRecommended}
                itemsPerPage={itemsPerPage}
                totalFilteredCount={totalFilteredCount}
                totalItemsCount={totalItemsCount}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </section>
  );
} 