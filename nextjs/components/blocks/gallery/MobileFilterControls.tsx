'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Filter, RefreshCw } from 'lucide-react';

interface MobileFilterControlsProps {
  hasActiveFilters: boolean;
  totalCount: number;
}

export default function MobileFilterControls({
  hasActiveFilters,
  totalCount
}: MobileFilterControlsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [expandedFilter, setExpandedFilter] = useState(true);
  
  const toggleFilterVisibility = () => {
    setExpandedFilter(prev => !prev);
  };
  
  const clearAllFilters = () => {
    router.push(pathname);
  };
  
  return (
    <>
      <div className="flex items-center space-x-2">
        <button 
          className="px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center space-x-2"
          onClick={toggleFilterVisibility}
        >
          <Filter className="h-4 w-4" />
          <span>{expandedFilter ? 'Hide Filters' : 'Show Filters'}</span>
        </button>
        
        {hasActiveFilters && (
          <button 
            className="px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300"
            onClick={clearAllFilters}
          >
            <RefreshCw className="h-3 w-3" />
            <span>Clear</span>
          </button>
        )}
      </div>
      <div className="text-sm text-gray-500 dark:text-gray-400">
        {totalCount} Items
      </div>
    </>
  );
} 