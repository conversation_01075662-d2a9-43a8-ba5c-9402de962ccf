'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { Tag, Search, ChevronDown, ChevronUp } from 'lucide-react';

interface TagFilterProps {
  allTags: string[];
  selectedTags: string[];
  onTagSelect: (tag: string) => void;
  tagCounts: Record<string, number>;
  searchQuery?: string;
}

export default function TagFilter({
  allTags,
  selectedTags,
  onTagSelect,
  tagCounts,
  searchQuery = ''
}: TagFilterProps) {
  // Initialize all groups as not expanded by default
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  
  // Group tags by their category/prefix
  const groupedTags = useMemo(() => {
    // Create the grouped tags object
    const grouped: Record<string, string[]> = { default: [] };

    console.log("TagFilter", allTags)
    
    allTags.forEach(tag => {
      if (tag.includes('/')) {
        const [group] = tag.split('/');
        if (!grouped[group]) {
          grouped[group] = [];
        }
        grouped[group].push(tag);
      } else {
        grouped.default.push(tag);
      }
    });
    
    return grouped;
  }, [allTags]);
  
  // Initialize all groups as expanded when tags change
  useEffect(() => {
    const groups = Object.keys(groupedTags);
    const initialExpanded: Record<string, boolean> = {};
    groups.forEach(group => {
      initialExpanded[group] = false;  // Set all groups to not expanded by default
    });
    setExpandedGroups(initialExpanded);
  }, [groupedTags]);
  
  // Filter tags based on search query
  const filteredGroups = useMemo(() => {
    if (!searchQuery) return groupedTags;
    
    const filtered: Record<string, string[]> = {};
    
    Object.entries(groupedTags).forEach(([group, tags]) => {
      const matchingTags = tags.filter(tag => {
        // For tags with slashes, match against the display part
        const displayTag = tag.includes('/') ? tag.split('/')[1] : tag;
        return displayTag.toLowerCase().includes(searchQuery.toLowerCase());
      });
      
      if (matchingTags.length > 0) {
        filtered[group] = matchingTags;
      }
    });
    
    return filtered;
  }, [groupedTags, searchQuery]);
  
  // Toggle a group's expanded state
  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };
  
  // Get all unique groups from the tags
  const groups = useMemo(() => {
    return Object.keys(filteredGroups).filter(group => 
      filteredGroups[group] && filteredGroups[group].length > 0
    );
  }, [filteredGroups]);

  return (
    <div className="tag-filter">
      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
        <Tag className="h-4 w-4 mr-2" />
        Tags
      </h4>
      
      {/* Main tags list */}
      <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
        {/* Uncategorized/default tags */}
        {filteredGroups.default && filteredGroups.default.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {filteredGroups.default.map(tag => (
                <button
                  key={tag}
                  onClick={() => onTagSelect(tag)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors flex items-center gap-1 ${
                    selectedTags.includes(tag)
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <span>{tag}</span>
                  {tagCounts[tag] && (
                    <span className={`text-xs ${
                      selectedTags.includes(tag) ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      ({tagCounts[tag]})
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
        
        {/* Categorized tags */}
        {groups.filter(group => group !== 'default').map(group => (
          <div key={group} className="mb-4">
            <button
              onClick={() => toggleGroup(group)}
              className="flex items-center justify-between w-full text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 mb-2 group transition-colors"
            >
              <span className="font-medium capitalize flex items-center">
                {group}
                <span className="ml-2 text-xs text-gray-500">
                  ({filteredGroups[group].length})
                </span>
              </span>
              {expandedGroups[group] ? (
                <ChevronUp className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
              )}
            </button>
            
            {expandedGroups[group] && (
              <div className="ml-2 flex flex-wrap gap-2 mt-2">
                {filteredGroups[group].map(tag => {
                  const displayTag = tag.split('/')[1];
                  return (
                    <button
                      key={tag}
                      onClick={() => onTagSelect(tag)}
                      className={`px-3 py-1 text-xs rounded-full transition-colors flex items-center gap-1 ${
                        selectedTags.includes(tag)
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      <span>{displayTag}</span>
                      {tagCounts[tag] && (
                        <span className={`text-xs ${
                          selectedTags.includes(tag) ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          ({tagCounts[tag]})
                        </span>
                      )}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        ))}
        
        {/* Empty state when no tags match search */}
        {groups.length === 0 && (
          <div className="text-center py-3 text-sm text-gray-500 dark:text-gray-400">
            No tags match your search
          </div>
        )}
      </div>
    </div>
  );
} 