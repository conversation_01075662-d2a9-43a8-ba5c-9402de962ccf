'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { Items } from '@/types/items';
import { useParams } from 'next/navigation';
import { useTheme } from 'next-themes';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ye, FiUser } from 'react-icons/fi';
import { FaStar, FaCodeBranch } from 'react-icons/fa';
import { AiOutlineEye } from 'react-icons/ai';
import { View } from 'lucide-react';

interface CardProps {
  item: Items;
  onTagClick?: (tag: string) => void;
}

// Custom type for CSS properties including CSS variables
interface CustomCSSProperties extends React.CSSProperties {
  '--main-color'?: string;
  '--bg-color'?: string;
  '--font-color'?: string;
  '--font-color-sub'?: string;
}

// Helper functions for formatting and color generation
// Moved outside component for potential reuse in SSR contexts
const generateCardColor = (uuid?: string) => {
  if (!uuid) return { color: 'text-blue-500', hoverColor: 'hover:text-blue-700' };
  
  // Use the first 6 characters of UUID for hue calculation
  const hash = uuid.split('-')[0] || '';
  const hue = parseInt(hash.substring(0, 6), 16) % 360;
  
  // Generate HSL color with fixed saturation and lightness
  const colorHsl = `hsl(${hue}, 70%, 45%)`;
  const hoverColorHsl = `hsl(${hue}, 80%, 40%)`;
  
  return { 
    color: colorHsl,
    hoverColor: hoverColorHsl
  };
};

// Format date manually
export const formatRelativeDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'just now';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  }
  
  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
};

export default function Card({ item, onTagClick }: CardProps) {
  const { locale } = useParams() || { locale: 'en' };
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';
  
  // Generate a color based on the UUID
  const cardColor = useMemo(() => generateCardColor(item.uuid), [item.uuid]);
  
  const formattedDate = formatRelativeDate(item.updated_at);
  
  const handleTagClick = (e: React.MouseEvent, tag: string) => {
    e.preventDefault();
    e.stopPropagation();
    if (onTagClick) {
      onTagClick(tag);
    }
  };
  
  // Border and shadow color based on theme
  const borderColor = isDark ? '#e0e0e0' : '#333';
  
  // Card styles with CSS variables
  const cardStyle: CustomCSSProperties = {
    border: `3px solid ${borderColor}`,
    boxShadow: `6px 6px 0 ${borderColor}`,
    '--main-color': isDark ? 'var(--foreground, #e0e0e0)' : 'var(--foreground, #323232)',
    '--bg-color': 'var(--card, white)',
    '--font-color': 'var(--card-foreground, #323232)',
    '--font-color-sub': 'var(--muted-foreground, #666)',
  };

  const elementStyle = {
    border: `2px solid ${borderColor}`,
    boxShadow: `2px 2px 0 ${borderColor}`
  };

  return (
    <div className="card-container">
      <Link href={`/${locale}/g/${item.uuid}`} className="block">
        <div 
          className="task bg-white dark:bg-gray-800 p-6 rounded-lg transition-all duration-300 transform hover:-translate-y-1"
          style={cardStyle}
        >
          <div className="tags flex items-center justify-between">
            <div className="flex gap-2">
              {item.is_official && (
                <span className="tag bg-blue-500 text-white px-2 py-1 text-xs rounded-full">Official</span>
              )}
              {item.is_recommended && (
                <span className="tag bg-green-500 text-white px-2 py-1 text-xs rounded-full">Recommended</span>
              )}
              {item.tags && item.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {item.tags.slice(0, 3).map((tag, index) => (
                    <button
                      key={index}
                      onClick={(e) => handleTagClick(e, tag)}
                      className="tag bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 text-xs rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                      style={elementStyle}
                    >
                      {tag.includes('/') ? tag.split('/')[1] : tag}
                    </button>
                  ))}
                  {item.tags.length > 3 && (
                    <span className="tag bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 text-xs rounded-full">
                      +{item.tags.length - 3}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center justify-between mt-4 mb-3">
            <div className="flex items-center">
              {item.item_avatar_url && (
                <div 
                  className="w-10 h-10 min-w-[40px] min-h-[40px] flex-shrink-0 rounded-full overflow-hidden mr-3"
                  style={elementStyle}
                >
                  <img 
                    src={item.item_avatar_url} 
                    alt={item.author_name} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = "https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y";
                    }}
                  />
                </div>
              )}
              <h3 className="font-semibold text-gray-800 dark:text-gray-100 text-lg" style={{ color: cardColor.color }}>
                {item.name}
              </h3>
            </div>
            
            {item.user_avatar_url && (
              <div 
                className="w-8 h-8 min-w-[32px] min-h-[32px] flex-shrink-0 rounded-full overflow-hidden"
                style={elementStyle}
              >
                <img 
                  src={item.user_avatar_url} 
                  alt={`Author: ${item.author_name || 'User'}`} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = "https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y";
                  }}
                />
              </div>
            )}
          </div>
          
          <p className="text-gray-600 dark:text-gray-300 text-sm my-4 line-clamp-3">{item.brief}</p>
          
          <div className="stats text-gray-400 dark:text-gray-500 text-xs mt-4">
            {/* Time and clicks info */}
            <div className="flex space-x-3 mb-2">
              <div className="flex items-center">
                <FiClock size={16} className="mr-1" />
                {formattedDate}
              </div>
              
              <div className="flex items-center">
                <View size={16} className="mr-1" />
                {item.clicks}
              </div>
            </div>
            
            {/* GitHub stats on a separate line */}
            {item.metadata && (item.metadata.stars || item.metadata.forks || item.metadata.watchers) && (
              <div className="flex space-x-3 mb-2">
                {item.metadata.stars && (
                  <div className="flex items-center">
                    <FaStar size={16} className="mr-1" />
                    {item.metadata.stars}
                  </div>
                )}
                
                {item.metadata.forks && (
                  <div className="flex items-center">
                    <FaCodeBranch size={16} className="mr-1" />
                    {item.metadata.forks}
                  </div>
                )}
                
                {item.metadata.watchers && (
                  <div className="flex items-center">
                    <AiOutlineEye size={16} className="mr-1" />
                    {item.metadata.watchers}
                  </div>
                )}
              </div>
            )}
            
          </div>
        </div>
      </Link>
    </div>
  );
} 