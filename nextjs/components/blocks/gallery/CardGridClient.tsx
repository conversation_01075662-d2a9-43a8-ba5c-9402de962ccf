'use client';

import React from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Items } from '@/types/items';
import Card from './Card';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CardGridClientProps {
  items: Items[];
  currentPage: number;
  totalPages: number;
  selectedTags: string[];
  isOfficial: boolean;
  isRecommended: boolean;
  itemsPerPage: number;
  totalFilteredCount?: number;
  totalItemsCount?: number;
}

// Helper to update URLSearchParams with proper array handling
function updateSearchParams(
  params: URLSearchParams,
  updates: Record<string, string | string[] | undefined>
): URLSearchParams {
  const newParams = new URLSearchParams(params.toString());
  
  // Clear the params that we're going to update
  Object.keys(updates).forEach(key => {
    newParams.delete(key);
  });
  
  // Add the new parameter values
  Object.entries(updates).forEach(([key, value]) => {
    if (value === undefined) {
      // If value is undefined, we've already deleted the param above
      return;
    }
    
    if (Array.isArray(value)) {
      // Handle array values (like tags)
      value.forEach(item => {
        newParams.append(key, item);
      });
    } else {
      // Handle single values
      newParams.set(key, value);
    }
  });
  
  return newParams;
}

export default function CardGridClient({ 
  items, 
  currentPage, 
  totalPages,
  selectedTags,
  isOfficial,
  isRecommended,
  itemsPerPage,
  totalFilteredCount = 0,
  totalItemsCount = 0
}: CardGridClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Handle page change
  const handlePageChange = (page: number) => {
    // Update URL with new page parameter
    const newParams = updateSearchParams(searchParams, { page: page.toString() });
    router.push(`${pathname}?${newParams.toString()}`);
    
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  // Handle tag click
  const handleTagClick = (tag: string) => {
    // Add the tag to selected tags if not already there
    if (!selectedTags.includes(tag)) {
      const newTags = [...selectedTags, tag];
      const newParams = updateSearchParams(searchParams, { 
        tags: newTags,
        page: '1' // Reset to first page
      });
      router.push(`${pathname}?${newParams.toString()}`);
    }
  };
  
  // Create pagination buttons
  const renderPaginationButtons = () => {
    const buttons = [];
    
    // Previous button
    buttons.push(
      <button
        key="prev"
        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className={`flex items-center justify-center h-9 w-9 rounded-md ${
          currentPage === 1
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </button>
    );
    
    // Calculate which page buttons to show
    const pagesToShow = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pagesToShow.push(i);
      }
    } else {
      // Always show first page
      pagesToShow.push(1);
      
      // Calculate middle pages
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if at edges
      if (currentPage <= 2) {
        endPage = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        startPage = Math.max(2, totalPages - 3);
      }
      
      // Add ellipsis before middle pages if needed
      if (startPage > 2) {
        pagesToShow.push(-1); // -1 represents ellipsis
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pagesToShow.push(i);
      }
      
      // Add ellipsis after middle pages if needed
      if (endPage < totalPages - 1) {
        pagesToShow.push(-2); // -2 represents ellipsis (different key from first ellipsis)
      }
      
      // Always show last page
      if (totalPages > 1) {
        pagesToShow.push(totalPages);
      }
    }
    
    // Page number buttons
    pagesToShow.forEach(page => {
      if (page < 0) {
        // Ellipsis
        buttons.push(
          <span key={`ellipsis${page}`} className="px-2 text-gray-500">
            …
          </span>
        );
      } else {
        buttons.push(
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={`flex items-center justify-center h-9 w-9 text-sm rounded-md ${
              currentPage === page
                ? 'bg-blue-500 text-white'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            aria-label={`Page ${page}`}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </button>
        );
      }
    });
    
    // Next button
    buttons.push(
      <button
        key="next"
        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className={`flex items-center justify-center h-9 w-9 rounded-md ${
          currentPage === totalPages
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </button>
    );
    
    return buttons;
  };

  // Empty state
  if (items.length === 0) {
    return (
      <div className="min-h-[40vh] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">No Items found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Try adjusting your filters or search terms to find what you're looking for.
          </p>
          <div className="flex justify-center">
            <button
              onClick={() => router.push(pathname)}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Reset all filters
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Stats and sorting (desktop) */}
      <div className="hidden md:flex justify-between items-center mb-6">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Showing <span className="font-medium text-gray-900 dark:text-white">{items.length}</span> of <span className="font-medium text-gray-900 dark:text-white">{totalItemsCount}</span> Items
          {selectedTags.length > 0 && (
            <span> with tags: <span className="font-medium text-blue-600">{selectedTags.join(', ')}</span></span>
          )}
        </div>
      </div>
      
      {/* Cards grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map((item) => (
          <Card key={item.uuid} item={item} onTagClick={handleTagClick} />
        ))}
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-12">
          <nav className="flex items-center space-x-1" aria-label="Pagination">
            {renderPaginationButtons()}
          </nav>
        </div>
      )}
    </div>
  );
} 