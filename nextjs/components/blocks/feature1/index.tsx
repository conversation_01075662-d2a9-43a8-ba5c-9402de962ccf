import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { TrendingUp, BarChart3, Target, Globe } from "lucide-react";

export default function Feature1({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/30 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/10"></div>
      
      <div className="container relative mx-auto px-4">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          {/* Demo Dashboard Section */}
          <div className="relative order-2 lg:order-1">
            {/* Custom Analytics Dashboard Mockup */}
            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Dashboard Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold">Traffic Analytics</h3>
                      <p className="text-blue-100">Real-time insights for your projects</p>
                    </div>
                    <div className="bg-white/20 rounded-lg p-3">
                      <BarChart3 className="w-6 h-6" />
                    </div>
                  </div>
                </div>

                {/* Dashboard Content */}
                <div className="p-6 space-y-6">
                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/20 p-4 rounded-xl border border-green-200 dark:border-green-800">
                      <div className="flex items-center gap-3">
                        <div className="bg-green-500 p-2 rounded-lg">
                          <TrendingUp className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-green-700 dark:text-green-400">+47%</div>
                          <div className="text-xs text-green-600 dark:text-green-500">Monthly Growth</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center gap-3">
                        <div className="bg-blue-500 p-2 rounded-lg">
                          <Globe className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">DR 68</div>
                          <div className="text-xs text-blue-600 dark:text-blue-500">Domain Rating</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Traffic Chart */}
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Weekly Traffic</h4>
                      <div className="text-xs text-green-600 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">+12.5%</div>
                    </div>
                    <div className="h-20 flex items-end justify-between gap-1">
                      {(() => {
                        // Pre-defined heights for consistent SSR/client rendering
                        const weeklyHeights = [45, 68, 52, 77, 61, 48, 73];
                        return Array.from({ length: 7 }).map((_, i) => (
                          <div key={i} className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-sm w-8 opacity-80" 
                               style={{ height: `${weeklyHeights[i]}%` }}></div>
                        ));
                      })()}
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Recent Activity</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">New backlink from tech-blog.com</span>
                      </div>
                      <div className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">Traffic spike detected: +156%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            
            {/* Floating Analytics Cards */}
            <div className="absolute -top-6 -left-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-10">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Live Tracking</span>
              </div>
            </div>
            
            <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-10">
              <div className="text-lg font-bold text-purple-600">2.4M</div>
              <div className="text-xs text-gray-500">Total Visits</div>
            </div>
          </div>

          {/* Content Section */}
          <div className="flex flex-col order-1 lg:order-2 space-y-8">
            {section.title && (
              <h2 className="text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                <span className="bg-gradient-to-r from-gray-900 via-blue-700 to-purple-700 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                  {section.title}
                </span>
              </h2>
            )}
            
            {section.description && (
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-xl">
                {section.description}
              </p>
            )}

            {/* Enhanced Feature List */}
            <div className="space-y-6">
              {section.items?.map((item, i) => (
                <div key={i} className="group">
                  <div className="flex items-start gap-4 p-6 rounded-2xl bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg transition-all duration-300 hover:border-blue-300 dark:hover:border-blue-600">
                    {item.icon && (
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <Icon
                            name={item.icon}
                            className="w-6 h-6 text-white"
                          />
                        </div>
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {item.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
