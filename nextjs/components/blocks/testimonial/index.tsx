"use client";

import { Avatar, AvatarImage } from "@/components/ui/avatar";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

import AutoScroll from "embla-carousel-auto-scroll";
import { Card } from "@/components/ui/card";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { Star, Quote, TrendingUp, Zap } from "lucide-react";
import { useRef } from "react";

export default function Testimonial({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  const plugin = useRef(
    AutoScroll({
      startDelay: 500,
      speed: 0.5,
    })
  );

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50/40 via-pink-50/30 to-purple-50/40 dark:from-orange-950/15 dark:via-pink-950/10 dark:to-purple-950/15"></div>
      
      <div className="container relative mx-auto px-4">
        {/* Header */}
        <div className="flex flex-col items-center gap-6 mb-16">
          {section.label && (
            <div className="flex items-center gap-2 text-sm font-semibold">
              <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white px-4 py-2 rounded-full flex items-center gap-2">
                {section.icon && (
                  <Icon name={section.icon} className="h-4 w-4" />
                )}
                {section.label}
              </div>
            </div>
          )}
          
          <h2 className="text-center text-4xl lg:text-5xl font-bold tracking-tight leading-tight max-w-4xl">
            <span className="bg-gradient-to-r from-gray-900 via-orange-700 to-pink-700 dark:from-white dark:via-orange-200 dark:to-pink-200 bg-clip-text text-transparent">
              {section.title}
            </span>
          </h2>
          
          <p className="text-center text-lg text-gray-600 dark:text-gray-300 max-w-2xl leading-relaxed">
            {section.description}
          </p>
        </div>

        {/* Social Proof Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 max-w-4xl mx-auto">
          <div className="text-center p-6 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
            <div className="text-3xl font-bold text-orange-600 mb-2">2.4k+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Active Users</div>
          </div>
          <div className="text-center p-6 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
            <div className="text-3xl font-bold text-pink-600 mb-2">15k+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Projects Tracked</div>
          </div>
          <div className="text-center p-6 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
            <div className="text-3xl font-bold text-purple-600 mb-2">89%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Success Rate</div>
          </div>
          <div className="text-center p-6 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
            <div className="text-3xl font-bold text-blue-600 mb-2">4.9/5</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">User Rating</div>
          </div>
        </div>

        {/* Testimonials Carousel */}
        <div className="relative">
          <Carousel
            opts={{
              loop: true,
              align: "start",
            }}
            plugins={[plugin.current]}
            onMouseEnter={() => plugin.current.stop()}
            onMouseLeave={() => plugin.current.play()}
            className="relative overflow-hidden"
          >
            {/* Gradient overlays */}
            <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-background via-background/80 to-transparent z-10 pointer-events-none"></div>
            <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-background via-background/80 to-transparent z-10 pointer-events-none"></div>
            
            <CarouselContent className="-ml-4">
              {section.items?.map((item, index) => (
                <CarouselItem key={index} className="pl-4 basis-auto">
                  <Card className="w-96 select-none group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
                    {/* Quote Icon */}
                    <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-orange-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                      <Quote className="w-6 h-6 text-white" />
                    </div>
                    
                    <div className="p-8">
                      {/* Stars */}
                      <div className="flex gap-1 mb-6">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className="w-5 h-5 fill-amber-400 text-amber-400"
                          />
                        ))}
                      </div>
                      
                      {/* Testimonial Text */}
                      <blockquote className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6 text-lg">
                        "{item.description}"
                      </blockquote>
                      
                      {/* User Info */}
                      <div className="flex items-center gap-4">
                        <Avatar className="w-14 h-14 ring-2 ring-orange-200 dark:ring-orange-800">
                          <AvatarImage
                            src={item.image?.src || `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face`}
                            alt={item.image?.alt || item.title}
                            className="object-cover"
                          />
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-semibold text-gray-900 dark:text-white text-lg">
                            {item.title}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {item.label}
                          </p>
                        </div>
                        {/* Success indicator based on index */}
                        <div className="flex items-center gap-2">
                          {index % 3 === 0 && (
                            <div className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
                              <TrendingUp className="w-3 h-3" />
                              +340% Growth
                            </div>
                          )}
                          {index % 3 === 1 && (
                            <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
                              <Zap className="w-3 h-3" />
                              5 Projects
                            </div>
                          )}
                          {index % 3 === 2 && (
                            <div className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
                              <Star className="w-3 h-3" />
                              Pro User
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            Join thousands of creators building their side project empire
          </p>
        </div>
      </div>
    </section>
  );
}
