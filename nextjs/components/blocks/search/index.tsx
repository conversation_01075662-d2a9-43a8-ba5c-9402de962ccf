"use client";

import { useState, useEffect } from "react";
import { Search as SearchType } from "@/types/blocks/search";
import { Button } from "@/components/ui/button";
import { Search as SearchIcon, X, Loader2 } from "lucide-react";
import Link from "next/link";
import Icon from "@/components/icon";
import { useRouter, useParams } from "next/navigation";
import { searchItems } from "@/services/items";

type SearchResult = {
  uuid: string;
  item_uuid: string;
  name: string;
  brief: string;
};

export default function Search({ search }: { search: SearchType }) {
  const [searchTerm, setSearchTerm] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const router = useRouter();
  const { locale } = useParams() || { locale: 'en' };

  // Debounced search effect
  useEffect(() => {
    if (!searchTerm || searchTerm.length < 2) {
      setResults([]);
      return;
    }

    const debounceTimeout = setTimeout(async () => {
      setIsLoading(true);
      try {
        const response = await searchItems(searchTerm, locale as string);
        setResults(response.data?.slice(0, 5) || []); // Limit to 5 results in dropdown
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm, locale]);

  if (search.disabled) {
    return null;
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchTerm.trim() === "") {
      return;
    }
    
    // Navigate to search results page with locale
    router.push(`/${locale}/search?q=${encodeURIComponent(searchTerm)}`);
    setShowResults(false);
    
    // If action is provided, the form will submit naturally
    // Otherwise, we prevent default behavior
    if (!search.action) {
      e.preventDefault();
    }
  };

  const handleReset = () => {
    setSearchTerm("");
    setResults([]);
  };

  const handleFocus = () => {
    if (searchTerm.length >= 2) {
      setShowResults(true);
    }
  };

  const handleBlur = () => {
    // Delay hiding results to allow clicking on them
    setTimeout(() => {
      setShowResults(false);
    }, 200);
  };

  return (
    <div className="w-full py-4">
              <div className="md:max-w-10xl mx-auto px-4">
        <div className="flex gap-4 items-center justify-center flex-col">
          {/* {search.title && (
            <h2 className="text-3xl md:text-4xl tracking-tighter text-center font-semibold">
              {search.title}
            </h2>
          )}
          
          {search.description && (
            <p className="text-lg leading-relaxed tracking-tight text-muted-foreground max-w-2xl text-center">
              {search.description}
            </p>
          )} */}

          <form 
            className="w-full max-w-2xl relative" 
            onSubmit={handleSubmit}
            action={search.action}
            method={search.method || "GET"}
          >
            <div className="absolute left-3 top-1/2 -translate-y-1/2 p-1 text-muted-foreground">
              <SearchIcon className="w-5 h-5" />
            </div>
            <input 
              className="w-full rounded-full px-10 py-3 border bg-card text-card-foreground border-input focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary placeholder:text-muted-foreground transition-all duration-300" 
              placeholder={search.placeholder || "Search Items..."}
              required 
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={handleFocus}
              onBlur={handleBlur}
              name="q"
            />
            {isLoading && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2 p-1 text-muted-foreground">
                <Loader2 className="w-5 h-5 animate-spin" />
              </div>
            )}
            {!isLoading && search.show_reset && searchTerm && (
              <button 
                type="button" 
                onClick={handleReset} 
                className="absolute right-3 top-1/2 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground"
              >
                <X className="w-5 h-5" />
              </button>
            )}

            {/* Dropdown search results */}
            {showResults && results.length > 0 && (
              <div className="absolute w-full mt-2 bg-card border border-border rounded-lg shadow-lg z-50 overflow-hidden">
                {results.map((result) => (
                  <Link
                    key={result.uuid || result.item_uuid}
                    href={`/${locale}/g/${result.item_uuid}`}
                    className="block px-4 py-3 hover:bg-accent cursor-pointer transition-colors"
                    onClick={() => setShowResults(false)}
                  >
                    <div className="font-medium text-foreground">{result.name}</div>
                    <div className="text-sm text-muted-foreground line-clamp-1">{result.brief}</div>
                  </Link>
                ))}
                
                {/* Show all results link */}
                <div className="bg-muted/50 p-2 text-center">
                  <button 
                    type="submit"
                    className="text-sm text-primary hover:underline"
                  >
                    See all results
                  </button>
                </div>
              </div>
            )}
          </form>

          {/* {search.buttons && search.buttons.length > 0 && (
            <div className="flex flex-row gap-3">
              {search.buttons.map((item, i) => (
                <Link 
                  key={i} 
                  href={item.url || "#"} 
                  target={item.target || ""} 
                  passHref
                >
                  <Button 
                    size={(item.size === "md" ? "default" : item.size) || "default"} 
                    variant={item.variant || "default"} 
                    className="gap-2"
                  >
                    {item.title}
                    {item.icon && (
                      <Icon name={item.icon} className="w-4 h-4" />
                    )}
                  </Button>
                </Link>
              ))}
            </div>
          )} */}

          {/* {search.tip && (
            <p className="text-sm text-muted-foreground">
              {search.tip}
            </p>
          )} */}
        </div>
      </div>
    </div>
  );
} 