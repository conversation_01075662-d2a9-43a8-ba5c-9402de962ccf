'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { searchItems } from '@/services/items';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';

type SearchResult = {
  item_uuid: string;
  name: string;
  brief: string;
  clicks: number;
};

export default function SearchResults({ locale }: { locale: string }) {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  useEffect(() => {
    const fetchResults = async () => {
      if (!query || query.trim().length < 2) {
        setResults([]);
        setTotalResults(0);
        return;
      }

      setLoading(true);
      try {
        const response = await searchItems(query, locale);
        // Normalize results to ensure they all have item_uuid
        const normalizedResults = response.data?.map((item: SearchResult) => ({
          item_uuid: item.item_uuid || '',
          name: item.name,
          brief: item.brief,
          clicks: item.clicks
        })) || [];
        
        setResults(normalizedResults);
        setTotalResults(response.count || 0);
      } catch (error) {
        console.error('Error fetching search results:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchResults();
  }, [query, locale]);

  return (
    <div className="pb-12">
      <h1 className="text-3xl font-bold mb-6">
        {query ? `Search results for "${query}"` : 'Search'}
      </h1>
      
      {query && (
        <p className="text-muted-foreground mb-8">
          Found {totalResults} {totalResults === 1 ? 'result' : 'results'}
        </p>
      )}

      {loading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <>
          {results.length === 0 && query ? (
            <div className="text-center py-12">
              <p className="text-lg text-muted-foreground">No results found for your search.</p>
              <p className="mt-2">Try using different keywords or check your spelling.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {results.map((result) => (
                <div 
                  key={result.item_uuid} 
                  className="bg-card p-6 rounded-lg border border-border shadow-sm hover:shadow-md transition-shadow"
                >
                  <Link href={`/${locale}/g/${result.item_uuid}`}>
                    <h2 className="text-xl font-semibold text-primary hover:underline">{result.name}</h2>
                  </Link>
                  <p className="text-muted-foreground mt-2 line-clamp-2">{result.brief}</p>
                  <div className="flex items-center gap-3 mt-3 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="16" height="16" className="mr-1">
                        <path strokeLinejoin="round" strokeLinecap="round" strokeWidth="1.5" d="M16 10H16.01M12 10H12.01M8 10H8.01M3 10C3 4.64706 5.11765 3 12 3C18.8824 3 21 4.64706 21 10C21 15.3529 18.8824 17 12 17C11.6592 17 11.3301 16.996 11.0124 16.9876L7 21V16.4939C4.0328 15.6692 3 13.7383 3 10Z" />
                      </svg>
                      {result.clicks} clicks
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
} 