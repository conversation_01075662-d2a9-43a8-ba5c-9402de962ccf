"use client";

import React, { useState } from "react";
import { Section as SectionType } from "@/types/blocks/section";
import { TrendingUp, BarChart3, Globe, Mail, MessageSquare, Loader2, CheckCircle, AlertCircle, Database, Activity, Shield, ExternalLink, Calendar, Users, Star, ArrowUpRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

interface WaitlistFormData {
  email: string;
  name: string;
  message: string;
}

export default function WaitList({ section }: { section: SectionType }) {
  const [formData, setFormData] = useState<WaitlistFormData>({
    email: "",
    name: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  if (section.disabled) {
    return null;
  }

  const handleInputChange = (field: keyof WaitlistFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email.trim()) {
      toast.error("Please enter your email address");
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/waitlist/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: formData.email,
          name: formData.name || undefined,
          message: formData.message || undefined,
          source: "landing_page",
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Submission failed");
      }

      setIsSuccess(true);
      toast.success("Successfully joined the waitlist! We'll contact you soon.");
      
      // Reset form
      setFormData({ email: "", name: "", message: "" });

    } catch (error: any) {
      console.error("Waitlist submission error:", error);
      
      if (error.message.includes("already on our waitlist")) {
        toast.error("This email is already on our waitlist");
      } else {
        toast.error(error.message || "Submission failed, please try again later");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/30 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/10"></div>
      
      <div className="container relative mx-auto px-4">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          {/* Demo Dashboard Section */}
          <div className="relative order-2 lg:order-1">
            {/* Main Dashboard Interface */}
            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Dashboard Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold">MyBackLinks Dashboard</h3>
                    <p className="text-blue-100">All-in-one backlink management platform</p>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3">
                    <BarChart3 className="w-6 h-6" />
                  </div>
                </div>
              </div>

              {/* Three Core Features */}
              <div className="p-6 space-y-4">
                {/* 1. Link Resource Database Management */}
                <div className="bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950/30 dark:to-teal-950/20 rounded-xl p-5 border border-emerald-200 dark:border-emerald-800">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-emerald-500 p-2 rounded-lg">
                        <Database className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-emerald-700 dark:text-emerald-400">Link Resource Database</h4>
                        <p className="text-sm text-emerald-600 dark:text-emerald-500">Centralized backlink opportunities</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-emerald-700 dark:text-emerald-400">2,847</div>
                      <div className="text-xs text-emerald-600 dark:text-emerald-500">Resources</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-emerald-700 dark:text-emerald-400">1,234</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Free Links</div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-emerald-700 dark:text-emerald-400">567</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Paid Links</div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-emerald-700 dark:text-emerald-400">89%</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Success Rate</div>
                    </div>
                  </div>
                </div>

                {/* 2. Side Project Tracking */}
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/20 rounded-xl p-5 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-blue-500 p-2 rounded-lg">
                        <Activity className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-blue-700 dark:text-blue-400">Project Analytics</h4>
                        <p className="text-sm text-blue-600 dark:text-blue-500">Track DR, traffic & backlinks</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">12</div>
                      <div className="text-xs text-blue-600 dark:text-blue-500">Projects</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="flex items-center justify-center gap-1">
                        <div className="text-lg font-bold text-blue-700 dark:text-blue-400">67</div>
                        <ArrowUpRight className="w-3 h-3 text-green-500" />
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Avg DR</div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-blue-700 dark:text-blue-400">45K</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Monthly Traffic</div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-blue-700 dark:text-blue-400">1,289</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Total Backlinks</div>
                    </div>
                  </div>
                </div>

                {/* 3. Domain Management */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/20 rounded-xl p-5 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-purple-500 p-2 rounded-lg">
                        <Shield className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-purple-700 dark:text-purple-400">Domain Management</h4>
                        <p className="text-sm text-purple-600 dark:text-purple-500">Monitor & protect your domains</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-purple-700 dark:text-purple-400">28</div>
                      <div className="text-xs text-purple-600 dark:text-purple-500">Domains</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-purple-700 dark:text-purple-400">23</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Active</div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-orange-600 dark:text-orange-400">3</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Expiring Soon</div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                      <div className="text-lg font-bold text-red-600 dark:text-red-400">2</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Expired</div>
                    </div>
                  </div>
                </div>

                {/* Quick Stats Bar */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-6">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">98% Uptime</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">Real-time monitoring</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">Auto-alerts</span>
                      </div>
                    </div>
                    <div className="text-xs text-green-600 bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full">
                      All systems operational
                    </div>
                  </div>
                </div>
              </div>
            </div>
          
            {/* Floating Feature Cards */}
            <div className="absolute -top-6 -left-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-10">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Live Monitoring</span>
              </div>
            </div>
            
            <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-10">
              <div className="text-lg font-bold text-blue-600">All-in-One</div>
              <div className="text-xs text-gray-500">Platform</div>
            </div>
          </div>

          {/* Content Section with Waitlist Form */}
          <div className="flex flex-col order-1 lg:order-2 space-y-8">
            {section.title && (
              <h2 className="text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                <span className="bg-gradient-to-r from-gray-900 via-blue-700 to-purple-700 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                  {section.title}
                </span>
              </h2>
            )}
            
            {section.description && (
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-xl">
                {section.description}
              </p>
            )}

            {/* Waitlist Form */}
            <div className="bg-white/50 dark:bg-gray-800/50 p-8 rounded-2xl border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm">
              {isSuccess ? (
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Thank you for your interest!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    You've successfully joined our waitlist. We'll notify you as soon as the product is ready.
                  </p>
                  <Button 
                    onClick={() => setIsSuccess(false)}
                    variant="outline"
                    className="mt-4"
                  >
                    Submit Again
                  </Button>
                </div>
              ) : (
                <>
                  <div className="mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      🚀 Early Access
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Join the waitlist to become one of our first users and enjoy free trial period with exclusive offers.
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          placeholder="<EMAIL>"
                          className="pl-10"
                          required
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Name (Optional)
                      </label>
                      <Input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                        placeholder="Your name"
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Message (Optional)
                      </label>
                      <div className="relative">
                        <MessageSquare className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                        <Textarea
                          id="message"
                          value={formData.message}
                          onChange={(e) => handleInputChange("message", e.target.value)}
                          placeholder="Tell us about your needs or expectations..."
                          className="pl-10 min-h-[80px]"
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={isSubmitting || !formData.email.trim()}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 transform hover:scale-[1.02] disabled:transform-none"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Joining...
                        </>
                      ) : (
                        "🎯 Join Waitlist"
                      )}
                    </Button>

                    <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                      We promise not to share your email with third parties or send spam.
                    </p>
                  </form>
                </>
              )}
            </div>

            {/* Enhanced Feature List */}
            {section.items && section.items.length > 0 && (
              <div className="space-y-6">
                {section.items.map((item, i) => (
                  <div key={i} className="group">
                    <div className="flex items-start gap-4 p-6 rounded-2xl bg-white/50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg transition-all duration-300 hover:border-blue-300 dark:hover:border-blue-600">
                      {item.icon && (
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <div className="w-6 h-6 text-white">
                              {/* You can add icon rendering logic here based on item.icon */}
                              <BarChart3 className="w-6 h-6" />
                            </div>
                          </div>
                        </div>
                      )}
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {item.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
