import { getFeaturedAffiliateProducts } from '@/models/affiliate';
import AffiliateCard from './AffiliateCard';

interface AffiliateGridProps {
  limit?: number;
  variant?: 'default' | 'compact' | 'sidebar';
}

export default async function AffiliateGrid({ limit = 3, variant = 'default' }: AffiliateGridProps) {
  const { data: products, error } = await getFeaturedAffiliateProducts(limit);
  
  if (error || products.length === 0) {
    return null;
  }
  
  return (
    <div className="mb-8">
      <h2 className="text-lg font-semibold mb-4">Sponsored Products</h2>
      
      <div className={`grid gap-4 ${
        variant === 'compact' 
          ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3'
          : variant === 'sidebar'
            ? 'grid-cols-1'
            : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3'
      }`}>
        {products.map((product) => (
          <AffiliateCard
            key={product.id}
            product={product}
            variant={variant}
          />
        ))}
      </div>
    </div>
  );
} 