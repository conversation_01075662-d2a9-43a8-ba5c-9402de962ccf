import { getFeaturedAffiliateProducts } from '@/models/affiliate';
import AffiliateCard from './AffiliateCard';

interface AffiliateSidebarProps {
  limit?: number;
}

export default async function AffiliateSidebar({ limit = 2 }: AffiliateSidebarProps) {
  const { data: products, error } = await getFeaturedAffiliateProducts(limit);
  
  if (error || products.length === 0) {
    return null;
  }
  
  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium mb-3 text-muted-foreground">Sponsored</h3>
      
      <div className="space-y-4">
        {products.map((product) => (
          <AffiliateCard
            key={product.id}
            product={product}
            variant="sidebar"
          />
        ))}
      </div>
    </div>
  );
} 