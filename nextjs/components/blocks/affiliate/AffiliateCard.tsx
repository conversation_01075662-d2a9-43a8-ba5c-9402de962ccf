'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import { ExternalLink } from 'lucide-react';
import { AffiliateCardProps } from '@/types/affiliate';

export default function AffiliateCard({ product, variant = 'default' }: AffiliateCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  
  const handleClick = async () => {
    try {
      // Track click analytics via API route
      await fetch('/api/affiliate/click', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: product.id }),
      });
      
      // Open the link in a new tab
      window.open(product.link, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Failed to track affiliate click:', error);
      // Still open the link even if tracking fails
      window.open(product.link, '_blank', 'noopener,noreferrer');
    }
  };

  if (variant === 'compact') {
    return (
      <Card 
        className="overflow-hidden transition-all duration-200 hover:shadow-md"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-center p-3">
          <div className="relative w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
            <Image 
              src={product.image_url} 
              alt={product.title}
              fill
              className="object-cover"
            />
          </div>
          
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium">{product.title}</h3>
            <Button 
              variant="link" 
              className="p-0 h-auto text-xs text-primary" 
              onClick={handleClick}
            >
              View Offer <ExternalLink className="ml-1 w-3 h-3" />
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  if (variant === 'sidebar') {
    return (
      <Card 
        className="overflow-hidden transition-all duration-200 hover:shadow-md"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative w-full h-32">
          <Image 
            src={product.image_url} 
            alt={product.title}
            fill
            className="object-cover"
          />
        </div>
        
        <CardContent className="p-3">
          <h3 className="font-medium text-sm mb-1">{product.title}</h3>
          <p className="text-xs text-muted-foreground line-clamp-2">
            {product.description}
          </p>
        </CardContent>
        
        <CardFooter className="p-3 pt-0">
          <Button 
            size="sm" 
            className="w-full text-xs" 
            onClick={handleClick}
          >
            View Offer <ExternalLink className="ml-1 w-3 h-3" />
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  // Default variant
  return (
    <Card 
      className={`overflow-hidden transition-all duration-200 ${isHovered ? 'shadow-lg' : 'shadow-sm'}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative w-full h-48">
        <Image 
          src={product.image_url} 
          alt={product.title}
          fill
          className="object-cover"
        />
        {product.tags && product.tags.length > 0 && (
          <div className="absolute top-2 left-2 flex flex-wrap gap-1">
            {product.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="bg-black/70 text-white">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>
      
      <CardHeader className="pb-2">
        <h3 className="text-lg font-semibold">{product.title}</h3>
      </CardHeader>
      
      <CardContent className="pb-3">
        <p className="text-muted-foreground line-clamp-3">
          {product.description}
        </p>
      </CardContent>
      
      <CardFooter>
        <Button 
          className="w-full" 
          onClick={handleClick}
        >
          View Offer <ExternalLink className="ml-1 w-4 h-4" />
        </Button>
      </CardFooter>
    </Card>
  );
} 