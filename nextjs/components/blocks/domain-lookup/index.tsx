"use client";

import { useState } from "react";
import { Search, Calendar, Building, Clock, Globe } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface DomainInfo {
  domain: string;
  registrar: string;
  createdDate: string | null;
  expiryDate: string | null;
  updatedDate: string | null;
  status: string[];
  nameServers: string[];
  contact: {
    registrant: any;
    admin: any;
    tech: any;
  };
}

export default function DomainLookup() {
  const [domain, setDomain] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<DomainInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleLookup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!domain.trim()) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch(`/api/link-resources/domain-lookup?domain=${encodeURIComponent(domain.trim())}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to lookup domain");
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Domain Lookup Tool
          </CardTitle>
          <CardDescription>
            Check domain registration status, expiration dates, and registrar information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLookup} className="flex gap-2">
            <Input
              type="text"
              placeholder="Enter domain name (e.g., example.com)"
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              disabled={loading}
              className="flex-1"
            />
            <Button type="submit" disabled={loading || !domain.trim()}>
              {loading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2" />
                  Looking up...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Lookup
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {loading && (
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-36" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-36" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {error && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-destructive">
              <div className="h-4 w-4 rounded-full bg-destructive/20 flex items-center justify-center">
                <div className="h-2 w-2 rounded-full bg-destructive" />
              </div>
              <span className="font-medium">Error:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {result.domain}
            </CardTitle>
            <CardDescription>Domain registration information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Building className="h-4 w-4" />
                    Registrar
                  </div>
                  <p className="text-sm text-muted-foreground">{result.registrar}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Calendar className="h-4 w-4" />
                    Registration Date
                  </div>
                  <p className="text-sm text-muted-foreground">{formatDate(result.createdDate)}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Clock className="h-4 w-4" />
                    Expiry Date
                  </div>
                  <p className="text-sm text-muted-foreground">{formatDate(result.expiryDate)}</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <Calendar className="h-4 w-4" />
                    Last Updated
                  </div>
                  <p className="text-sm text-muted-foreground">{formatDate(result.updatedDate)}</p>
                </div>

                {result.status && result.status.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Status</div>
                    <div className="flex flex-wrap gap-1">
                      {result.status.map((status, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {status}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {result.nameServers && result.nameServers.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Name Servers</div>
                    <div className="text-sm text-muted-foreground">
                      {result.nameServers.slice(0, 3).map((ns, index) => (
                        <div key={index}>{ns}</div>
                      ))}
                      {result.nameServers.length > 3 && (
                        <div className="text-xs text-muted-foreground mt-1">
                          +{result.nameServers.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}