import { But<PERSON> } from "@/components/ui/button";
import Icon from "@/components/icon";
import Link from "next/link";
import { Section as SectionType } from "@/types/blocks/section";
import { <PERSON>, TrendingUp, BarChart3, Zap, <PERSON>, Crown } from "lucide-react";

export default function CTA({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/10 dark:via-purple-950/10 dark:to-pink-950/10"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-100/10 via-transparent to-transparent dark:from-blue-900/10"></div>
      
      {/* Animated Background Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-pink-300/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-violet-300/10 rounded-full blur-xl animate-pulse delay-500"></div>
      
      <div className="container relative mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          {/* Main CTA Content */}
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-6xl font-bold tracking-tight leading-tight mb-6">
              <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 dark:from-white dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent">
                {section.title}
              </span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto mb-12">
              {section.description}
            </p>
            
            {/* Integration Showcase */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 mb-12 border border-gray-200/50 dark:border-gray-700/50">
              <div className="flex items-center justify-center gap-2 text-gray-700 dark:text-gray-300 text-sm font-semibold mb-6">
                <Zap className="w-4 h-4" />
                Seamless Analytics Integration
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {/* Google Analytics */}
                <div className="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-600/50 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-gray-900 dark:text-white font-semibold text-sm">Google Analytics</div>
                  <div className="text-gray-600 dark:text-gray-400 text-xs">Traffic insights</div>
                </div>
                
                {/* Plausible */}
                <div className="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-600/50 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-gray-900 dark:text-white font-semibold text-sm">Plausible</div>
                  <div className="text-gray-600 dark:text-gray-400 text-xs">Privacy-focused</div>
                </div>
                
                {/* Google Search Console */}
                <div className="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-600/50 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M9.822 5.036c.176-.177.427-.204.627-.204h7.098c.627 0 1.134.508 1.134 1.134v7.098c0 .2-.027.451-.204.627L12.86 19.31c-.322.322-.845.322-1.167 0L5.076 12.693c-.322-.322-.322-.845 0-1.167L9.822 5.036z"/>
                    </svg>
                  </div>
                  <div className="text-gray-900 dark:text-white font-semibold text-sm">Search Console</div>
                  <div className="text-gray-600 dark:text-gray-400 text-xs">SEO performance</div>
                </div>
                
                {/* SEMrush */}
                <div className="bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-600/50 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                  <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Crown className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-gray-900 dark:text-white font-semibold text-sm">SEMrush</div>
                  <div className="text-gray-600 dark:text-gray-400 text-xs">DR & backlinks</div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            {section.buttons && (
              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
                {section.buttons.map((item, idx) => (
                  <Link
                    key={idx}
                    href={item.url || ""}
                    target={item.target}
                    className="group"
                  >
                    <Button
                      size="lg"
                      variant={idx === 0 ? "default" : "outline"}
                      className={`px-8 py-6 text-lg font-bold rounded-2xl transition-all duration-300 ${
                        idx === 0
                          ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-2xl hover:shadow-xl transform hover:scale-105"
                          : "border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white/60 dark:bg-gray-800/60 hover:bg-white dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 backdrop-blur-sm shadow-lg hover:shadow-xl transform hover:scale-105"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        {idx === 0 ? (
                          <Rocket className="w-6 h-6" />
                        ) : (
                          item.icon ? (
                            <Icon name={item.icon as string} className="w-6 h-6" />
                          ) : (
                            <Users className="w-6 h-6" />
                          )
                        )}
                        {item.title}
                      </div>
                    </Button>
                  </Link>
                ))}
              </div>
            )}
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div className="text-gray-900 dark:text-gray-100">
              <div className="text-3xl font-bold mb-2">2.4k+</div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">Active Creators</div>
            </div>
            <div className="text-gray-900 dark:text-gray-100">
              <div className="text-3xl font-bold mb-2">15k+</div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">Projects Tracked</div>
            </div>
            <div className="text-gray-900 dark:text-gray-100">
              <div className="text-3xl font-bold mb-2">247k+</div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">Links Managed</div>
            </div>
            <div className="text-gray-900 dark:text-gray-100">
              <div className="text-3xl font-bold mb-2">4.9/5</div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">User Rating</div>
            </div>
          </div>

          {/* Bottom Text */}
          <div className="text-center mt-12">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              ✨ Free forever plan • No credit card required • Cancel anytime
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
