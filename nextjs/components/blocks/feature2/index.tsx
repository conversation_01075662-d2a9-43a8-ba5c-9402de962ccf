"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import Fade from "embla-carousel-fade";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { Database, Target, Star, TrendingUp, Users, CheckCircle } from "lucide-react";

const DURATION = 5000;

export default function Feature2({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  const [api, setApi] = useState<CarouselApi>();
  const [currentAccordion, setCurrentAccordion] = useState("1");

  useEffect(() => {
    api?.scrollTo(+currentAccordion - 1);
    const interval = setInterval(() => {
      setCurrentAccordion((prev) => {
        const next = parseInt(prev) + 1;
        return next > (section.items?.length || 3) ? "1" : next.toString();
      });
    }, DURATION);

    return () => clearInterval(interval);
  }, [api, currentAccordion, section.items?.length]);

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50/30 via-transparent to-blue-50/20 dark:from-purple-950/10 dark:via-transparent dark:to-blue-950/10"></div>
      
      <div className="container relative mx-auto px-4">
        <div className="mx-auto grid gap-16 lg:grid-cols-2 items-center">
          {/* Content Section */}
          <div className="space-y-8">
            {section.label && (
              <Badge variant="outline" className="mb-4 bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 border-purple-300 dark:border-purple-600">
                {section.label}
              </Badge>
            )}
            
            <div className="space-y-6">
              <h2 className="text-4xl lg:text-5xl font-bold tracking-tight leading-tight">
                <span className="bg-gradient-to-r from-gray-900 via-purple-700 to-blue-700 dark:from-white dark:via-purple-200 dark:to-blue-200 bg-clip-text text-transparent">
                  {section.title}
                </span>
              </h2>
              
              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-xl">
                {section.description}
              </p>
            </div>

            {/* Enhanced Accordion */}
            <Accordion
              type="single"
              value={currentAccordion}
              onValueChange={(value) => {
                setCurrentAccordion(value);
                api?.scrollTo(+value - 1);
              }}
              className="space-y-4"
            >
              {section.items?.map((item, i) => (
                <AccordionItem
                  key={i}
                  value={(i + 1).toString()}
                  className="border-0 bg-white/50 dark:bg-gray-800/50 rounded-2xl overflow-hidden data-[state=open]:bg-white dark:data-[state=open]:bg-gray-800 transition-all duration-300 data-[state=open]:shadow-lg"
                >
                  <AccordionTrigger className="text-left hover:no-underline px-6 py-4 data-[state=closed]:text-gray-700 dark:data-[state=closed]:text-gray-300 data-[state=open]:text-purple-600 dark:data-[state=open]:text-purple-400">
                    <div className="flex items-center gap-4 w-full">
                      {item.icon && (
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center data-[state=open]:scale-110 transition-transform duration-300">
                            <Icon
                              name={item.icon}
                              className="w-6 h-6 text-white"
                            />
                          </div>
                        </div>
                      )}
                      <span className="font-semibold text-lg flex-1">
                        {item.title}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-6 pt-0">
                    <div className="pl-16">
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                        {item.description}
                      </p>
                      <div className="bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-4">
                        <div className="h-2 bg-gradient-to-r from-purple-200 to-blue-200 dark:from-purple-800 dark:to-blue-800 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-1000 ease-out"
                            style={{
                              width: currentAccordion === (i + 1).toString() ? '100%' : '0%',
                              transition: `width ${DURATION}ms linear`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          {/* Demo/Visual Section */}
          <div className="relative">
            <Carousel
              opts={{
                duration: 50,
              }}
              setApi={setApi}
              plugins={[Fade()]}
              className="relative"
            >
              <CarouselContent>
                {section.items?.map((item, i) => (
                  <CarouselItem key={i}>
                    <div className="relative">
                      {/* Custom Resource Management Interface Mockup */}
                      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                          {/* Header */}
                          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="text-xl font-bold">Resource Library</h3>
                                <p className="text-purple-100">Manage your backlink opportunities</p>
                              </div>
                              <div className="bg-white/20 rounded-lg p-3">
                                <Database className="w-6 h-6" />
                              </div>
                            </div>
                          </div>

                          {/* Content based on accordion item */}
                          <div className="p-6 space-y-4">
                            {i === 0 && (
                              /* Resource Database View */
                              <>
                                <div className="grid grid-cols-3 gap-3 mb-4">
                                  <div className="bg-purple-50 dark:bg-purple-950/30 p-3 rounded-lg text-center">
                                    <div className="text-lg font-bold text-purple-600">247</div>
                                    <div className="text-xs text-gray-600 dark:text-gray-400">Total Resources</div>
                                  </div>
                                  <div className="bg-green-50 dark:bg-green-950/30 p-3 rounded-lg text-center">
                                    <div className="text-lg font-bold text-green-600">89%</div>
                                    <div className="text-xs text-gray-600 dark:text-gray-400">Success Rate</div>
                                  </div>
                                  <div className="bg-blue-50 dark:bg-blue-950/30 p-3 rounded-lg text-center">
                                    <div className="text-lg font-bold text-blue-600">12</div>
                                    <div className="text-xs text-gray-600 dark:text-gray-400">Categories</div>
                                  </div>
                                </div>
                                
                                <div className="space-y-2">
                                  {['Directory Listing', 'Guest Post', 'Resource Page'].map((type, idx) => (
                                    <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                      <span className="text-sm font-medium">{type}</span>
                                      <div className="flex items-center gap-2">
                                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                        <span className="text-xs text-gray-500">4.{8-idx}</span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                            
                            {i === 1 && (
                              /* Performance Tracking View */
                              <>
                                <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 p-4 rounded-lg">
                                  <div className="flex items-center justify-between mb-3">
                                    <h4 className="text-sm font-semibold">Success Rate Trends</h4>
                                    <TrendingUp className="w-4 h-4 text-green-600" />
                                  </div>
                                  <div className="grid grid-cols-4 gap-2">
                                    {Array.from({ length: 4 }).map((_, idx) => (
                                      <div key={idx} className="bg-gradient-to-t from-green-500 to-blue-500 rounded-sm" 
                                           style={{ height: `${40 + idx * 15}px` }}></div>
                                    ))}
                                  </div>
                                </div>
                                
                                <div className="space-y-2">
                                  {['Response Time: 2.3 days', 'Approval Rate: 78%', 'Average DR: 45'].map((metric, idx) => (
                                    <div key={idx} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                                      <CheckCircle className="w-4 h-4 text-green-500" />
                                      <span className="text-sm">{metric}</span>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                            
                            {i === 2 && (
                              /* Quality Management View */
                              <>
                                <div className="space-y-3">
                                  {['High Quality', 'Medium Quality', 'Needs Review'].map((status, idx) => (
                                    <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                      <div className="flex items-center gap-3">
                                        <div className={`w-3 h-3 rounded-full ${
                                          idx === 0 ? 'bg-green-500' : idx === 1 ? 'bg-yellow-500' : 'bg-red-500'
                                        }`}></div>
                                        <span className="text-sm font-medium">{status}</span>
                                      </div>
                                      <span className="text-xs text-gray-500">{[89, 45, 12][idx]} resources</span>
                                    </div>
                                  ))}
                                </div>
                                
                                <div className="bg-purple-50 dark:bg-purple-950/30 p-3 rounded-lg">
                                  <div className="text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">Quality Score</div>
                                  <div className="text-2xl font-bold text-purple-600">8.7/10</div>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
            
            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 z-10">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-medium">Active Campaign</span>
              </div>
            </div>
            
            <div className="absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 z-10">
              <div className="text-sm font-bold text-green-600">94% Success Rate</div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute -inset-6 bg-gradient-to-r from-purple-500/5 to-blue-500/5 rounded-3xl blur-2xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
