"use client";

import { <PERSON>, Loader, <PERSON>, Zap, Crown } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { Label } from "@/components/ui/label";
import { loadStripe } from "@stripe/stripe-js";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";

export default function Pricing({ pricing }: { pricing: PricingType }) {
  if (pricing.disabled) {
    return null;
  }

  const { user, setShowSignModal } = useAppContext();

  const [group, setGroup] = useState(pricing.groups?.[0]?.name);
  const [isLoading, setIsLoading] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  const handleCheckout = async (item: PricingItem, cn_pay: boolean = false) => {
    try {
      if (!user) {
        setShowSignModal(true);
        return;
      }

      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: item.credits,
        interval: item.interval,
        amount: cn_pay ? item.cn_amount : item.amount,
        currency: cn_pay ? "cny" : item.currency,
        valid_months: item.valid_months,
      };

      setIsLoading(true);
      setProductId(item.product_id);

      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setProductId(null);

        setShowSignModal(true);
        return;
      }

      const { code, message, data } = await response.json();
      if (code !== 0) {
        toast.error(message);
        return;
      }

      const { public_key, session_id } = data;

      const stripe = await loadStripe(public_key);
      if (!stripe) {
        toast.error("checkout failed");
        return;
      }

      const result = await stripe.redirectToCheckout({
        sessionId: session_id,
      });

      if (result.error) {
        toast.error(result.error.message);
      }
    } catch (e) {
      console.log("checkout failed: ", e);

      toast.error("checkout failed");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  useEffect(() => {
    if (pricing.items) {
      setGroup(pricing.items[0].group);
      setProductId(pricing.items[0].product_id);
      setIsLoading(false);
    }
  }, [pricing.items]);

  return (
    <section id={pricing.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/10 dark:via-purple-950/10 dark:to-pink-950/10"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-100/10 via-transparent to-transparent dark:from-blue-900/10"></div>
      
      <div className="container relative mx-auto px-4">
        <div className="mx-auto mb-16 text-center max-w-3xl">
          <h2 className="text-4xl lg:text-6xl font-bold tracking-tight leading-tight mb-6">
            <span className="bg-gradient-to-r from-gray-900 via-blue-700 to-purple-700 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
              {pricing.title}
            </span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
            {pricing.description}
          </p>
        </div>
        <div className="flex flex-col items-center gap-8">
          {pricing.groups && pricing.groups.length > 0 && (
            <div className="flex h-14 mb-12 items-center rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 p-2 text-lg shadow-lg">
              <RadioGroup
                value={group}
                className={`h-full grid-cols-${pricing.groups.length} gap-2`}
                onValueChange={(value) => {
                  setGroup(value);
                }}
              >
                {pricing.groups.map((item, i) => {
                  return (
                    <div
                      key={i}
                      className='h-full rounded-xl transition-all has-[button[data-state="checked"]]:bg-gradient-to-r has-[button[data-state="checked"]]:from-blue-600 has-[button[data-state="checked"]]:to-purple-600 has-[button[data-state="checked"]]:shadow-lg'
                    >
                      <RadioGroupItem
                        value={item.name || ""}
                        id={item.name}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={item.name}
                        className="flex h-full cursor-pointer items-center justify-center px-8 font-semibold text-gray-600 dark:text-gray-300 peer-data-[state=checked]:text-white transition-colors"
                      >
                        {item.title}
                        {item.label && (
                          <Badge
                            variant="outline"
                            className="border-yellow-400 bg-yellow-400 px-2 py-1 ml-2 text-yellow-900 text-xs font-bold"
                          >
                            {item.label}
                          </Badge>
                        )}
                      </Label>
                    </div>
                  );
                })}
              </RadioGroup>
            </div>
          )}
          <div
            className={`max-w-6xl mx-auto grid gap-8 ${
              pricing.items?.filter(
                (item) => !item.group || item.group === group
              )?.length === 2 ? 'md:grid-cols-2' : pricing.items?.filter(
                (item) => !item.group || item.group === group
              )?.length === 3 ? 'md:grid-cols-3' : 'md:grid-cols-1'
            }`}
          >
            {pricing.items?.map((item, index) => {
              if (item.group && item.group !== group) {
                return null;
              }

              return (
                <div
                  key={index}
                  className={`relative rounded-3xl p-8 transition-all duration-300 hover:-translate-y-2 ${
                    item.is_featured
                      ? "bg-gradient-to-br from-blue-600 to-purple-600 text-white shadow-2xl scale-105 border-0"
                      : "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:shadow-2xl"
                  }`}
                >
                  {item.is_featured && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-yellow-900 px-6 py-2 rounded-full text-sm font-bold flex items-center gap-2 shadow-lg">
                        <Crown className="w-4 h-4" />
                        Most Popular
                      </div>
                    </div>
                  )}
                  
                  <div className="flex h-full flex-col justify-between gap-8">
                    <div>
                      <div className="flex items-center gap-3 mb-6">
                        {item.is_featured ? (
                          <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <Zap className="w-6 h-6 text-white" />
                          </div>
                        ) : (
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <Star className="w-6 h-6 text-white" />
                          </div>
                        )}
                        {item.title && (
                          <h3 className={`text-2xl font-bold ${
                            item.is_featured ? "text-white" : "text-gray-900 dark:text-white"
                          }`}>
                            {item.title}
                          </h3>
                        )}
                        <div className="flex-1"></div>
                        {item.label && !item.is_featured && (
                          <Badge
                            variant="outline"
                            className="border-blue-300 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 text-blue-700 dark:text-blue-300 font-semibold"
                          >
                            {item.label}
                          </Badge>
                        )}
                      </div>
                      <div className="mb-6">
                        <div className="flex items-end gap-3 mb-3">
                          {item.original_price && (
                            <span className={`text-2xl font-semibold line-through ${
                              item.is_featured ? "text-white/60" : "text-gray-400 dark:text-gray-500"
                            }`}>
                              {item.original_price}
                            </span>
                          )}
                          {item.price && (
                            <span className={`text-6xl font-bold ${
                              item.is_featured ? "text-white" : "text-gray-900 dark:text-white"
                            }`}>
                              {item.price}
                            </span>
                          )}
                          {item.unit && (
                            <span className={`text-xl font-semibold mb-2 ${
                              item.is_featured ? "text-white/80" : "text-gray-600 dark:text-gray-400"
                            }`}>
                              {item.unit}
                            </span>
                          )}
                        </div>
                        {item.description && (
                          <p className={`text-lg ${
                            item.is_featured ? "text-white/90" : "text-gray-600 dark:text-gray-300"
                          }`}>
                            {item.description}
                          </p>
                        )}
                      </div>
                      {item.features_title && (
                        <p className={`mb-4 mt-8 font-bold text-lg ${
                          item.is_featured ? "text-white" : "text-gray-900 dark:text-white"
                        }`}>
                          {item.features_title}
                        </p>
                      )}
                      {item.features && (
                        <ul className="flex flex-col gap-4">
                          {item.features.map((feature, fi) => {
                            return (
                              <li className="flex gap-3 items-start" key={`feature-${fi}`}>
                                <div className={`mt-1 w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 ${
                                  item.is_featured ? "bg-white/20" : "bg-green-100 dark:bg-green-900/30"
                                }`}>
                                  <Check className={`w-3 h-3 ${
                                    item.is_featured ? "text-white" : "text-green-600 dark:text-green-400"
                                  }`} />
                                </div>
                                <span className={`${
                                  item.is_featured ? "text-white/90" : "text-gray-700 dark:text-gray-300"
                                }`}>
                                  {feature}
                                </span>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                    <div className="flex flex-col gap-4 mt-8">
                      {item.cn_amount && item.cn_amount > 0 ? (
                        <div className="flex items-center gap-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-xl">
                          <span className="text-sm font-medium">人民币支付 👉</span>
                          <div
                            className="inline-block p-2 hover:cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                            onClick={() => {
                              if (isLoading) {
                                return;
                              }
                              handleCheckout(item, true);
                            }}
                          >
                            <img
                              src="/imgs/cnpay.png"
                              alt="cnpay"
                              className="w-20 h-10 rounded-lg"
                            />
                          </div>
                        </div>
                      ) : null}
                      {item.button && (
                        <Button
                          className={`w-full flex items-center justify-center gap-3 font-bold py-6 text-lg rounded-2xl transition-all duration-300 ${
                            item.is_featured
                              ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-xl hover:shadow-2xl transform hover:scale-105"
                              : "bg-white text-gray-900 hover:bg-gray-100 shadow-lg hover:shadow-xl transform hover:scale-105 border-2 border-gray-200 hover:border-gray-300"
                          }`}
                          disabled={isLoading}
                          onClick={() => {
                            if (isLoading) {
                              return;
                            }
                            handleCheckout(item);
                          }}
                        >
                          {(!isLoading ||
                            (isLoading && productId !== item.product_id)) && (
                            <span>{item.button.title}</span>
                          )}

                          {isLoading && productId === item.product_id && (
                            <>
                              <span>{item.button.title}</span>
                              <Loader className="h-5 w-5 animate-spin" />
                            </>
                          )}
                          {!isLoading && item.button.icon && (
                            <Icon name={item.button.icon} className="w-5 h-5" />
                          )}
                        </Button>
                      )}
                      {item.tip && (
                        <p className={`text-sm mt-3 text-center ${
                          item.is_featured ? "text-white/70" : "text-gray-500 dark:text-gray-400"
                        }`}>
                          {item.tip}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
