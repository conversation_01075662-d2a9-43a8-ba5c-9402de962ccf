"use client";

import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";
import { HelpCircle, CheckCircle, ArrowRight, Sparkles } from "lucide-react";
import { useState } from "react";
import { useTranslations } from "next-intl";

export default function FAQ({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  const t = useTranslations("faq");
  const [openItems, setOpenItems] = useState<number[]>([0]); // First item open by default

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/40 via-cyan-50/30 to-teal-50/40 dark:from-indigo-950/15 dark:via-cyan-950/10 dark:to-teal-950/15"></div>
      
      <div className="container relative mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-16">
          {section.label && (
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-cyan-500 text-white px-6 py-3 rounded-full text-sm font-semibold mb-6">
              <HelpCircle className="w-4 h-4" />
              {section.label}
            </div>
          )}
          
          <h2 className="text-4xl lg:text-5xl font-bold tracking-tight leading-tight mb-6">
            <span className="bg-gradient-to-r from-gray-900 via-indigo-700 to-cyan-700 dark:from-white dark:via-indigo-200 dark:to-cyan-200 bg-clip-text text-transparent">
              {section.title}
            </span>
          </h2>
          
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
            {section.description}
          </p>
        </div>

        {/* FAQ Grid */}
        <div className="grid md:grid-cols-2 gap-6 lg:gap-8">
          {section.items?.map((item, index) => (
            <div key={index} className="group">
              <div 
                className={`bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden transition-all duration-300 hover:shadow-xl cursor-pointer ${
                  openItems.includes(index) 
                    ? 'shadow-lg bg-white dark:bg-gray-800 border-indigo-300 dark:border-indigo-600' 
                    : 'hover:bg-white dark:hover:bg-gray-800'
                }`}
                onClick={() => toggleItem(index)}
              >
                {/* Question Header */}
                <div className="p-6 pb-4">
                  <div className="flex items-start gap-4">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-xl flex items-center justify-center font-bold text-sm transition-all duration-300 ${
                      openItems.includes(index)
                        ? 'bg-gradient-to-br from-indigo-500 to-cyan-500 text-white scale-110'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      {index + 1}
                    </div>
                    
                    <div className="flex-1">
                      <h3 className={`font-semibold text-lg leading-tight transition-colors ${
                        openItems.includes(index) 
                          ? 'text-indigo-600 dark:text-indigo-400' 
                          : 'text-gray-900 dark:text-white'
                      }`}>
                        {item.title}
                      </h3>
                    </div>
                    
                    <div className={`transition-transform duration-300 ${
                      openItems.includes(index) ? 'rotate-90' : ''
                    }`}>
                      <ArrowRight className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Answer Content */}
                <div className={`transition-all duration-300 ease-in-out ${
                  openItems.includes(index) 
                    ? 'max-h-96 opacity-100' 
                    : 'max-h-0 opacity-0'
                } overflow-hidden`}>
                  <div className="px-6 pb-6">
                    <div className="pl-12">
                      <div className="bg-gradient-to-r from-indigo-50 to-cyan-50 dark:from-indigo-950/30 dark:to-cyan-950/20 rounded-xl p-4 border border-indigo-100 dark:border-indigo-800">
                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                          {item.description}
                        </p>
                        
                        {/* Add helpful indicator */}
                        <div className="flex items-center gap-2 mt-3 pt-3 border-t border-indigo-200 dark:border-indigo-700">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {t("helpful_information")}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-indigo-50 to-cyan-50 dark:from-indigo-950/30 dark:to-cyan-950/20 rounded-2xl p-8 border border-indigo-200 dark:border-indigo-700">
            <Sparkles className="w-8 h-8 text-indigo-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {t("still_have_questions")}
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {t("contact_support_description")}
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-block bg-gradient-to-r from-indigo-600 to-cyan-600 hover:from-indigo-700 hover:to-cyan-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:shadow-lg transform hover:scale-105"
            >
              {t("contact_support")}
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
