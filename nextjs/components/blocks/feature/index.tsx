import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { Clock, Shield, Globe, AlertTriangle, Calendar, Settings } from "lucide-react";

export default function Feature({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-green-50/40 via-transparent to-cyan-50/30 dark:from-green-950/15 dark:via-transparent dark:to-cyan-950/10"></div>
      
      <div className="container relative mx-auto px-4">
        {/* Header Section */}
        <div className="mx-auto max-w-4xl text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold tracking-tight leading-tight mb-6">
            <span className="bg-gradient-to-r from-gray-900 via-green-700 to-cyan-700 dark:from-white dark:via-green-200 dark:to-cyan-200 bg-clip-text text-transparent">
              {section.title}
            </span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-2xl mx-auto">
            {section.description}
          </p>
        </div>

        {/* Demo Domain Management Interface */}
        <div className="max-w-5xl mx-auto mb-16">
          <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            {/* Interface Header */}
            <div className="bg-gradient-to-r from-green-600 to-cyan-600 p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">Domain Portfolio Manager</h3>
                  <p className="text-green-100">Never miss a renewal again</p>
                </div>
                <div className="bg-white/20 rounded-lg p-3">
                  <Globe className="w-6 h-6" />
                </div>
              </div>
            </div>

            {/* Stats Dashboard */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/20 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-blue-600">47</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Domains</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/20 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-green-600">42</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Active</div>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/30 dark:to-orange-950/20 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-yellow-600">3</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Expiring Soon</div>
                </div>
                <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/20 p-4 rounded-xl">
                  <div className="text-2xl font-bold text-red-600">2</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Expired</div>
                </div>
              </div>
            </div>

            {/* Domain List Preview */}
            <div className="p-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/10 rounded-lg border border-yellow-200 dark:border-yellow-700">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="w-5 h-5 text-yellow-600" />
                    <div>
                      <div className="font-semibold">myproject.com</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Expires in 7 days</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full text-xs">Expiring</span>
                    <button className="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-700 transition-colors">Renew</button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <div className="font-semibold">portfolio-site.com</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Expires in 347 days</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs">Active</span>
                    <Calendar className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <div className="font-semibold">startup-tool.io</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Expires in 89 days</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs">Active</span>
                    <Settings className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {section.items?.map((item, i) => (
            <div key={i} className="group relative">
              <div className="h-full p-8 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 hover:shadow-xl transition-all duration-300 hover:border-green-300 dark:hover:border-green-600 hover:-translate-y-1">
                {item.icon && (
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-cyan-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <Icon name={item.icon} className="w-8 h-8 text-white" />
                    </div>
                  </div>
                )}
                
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                  {item.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {item.description}
                </p>
                
                {/* Feature highlight based on index */}
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  {i === 0 && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Clock className="w-4 h-4" />
                      <span>Automated alerts</span>
                    </div>
                  )}
                  {i === 1 && (
                    <div className="flex items-center gap-2 text-sm text-blue-600">
                      <Shield className="w-4 h-4" />
                      <span>Secure data storage</span>
                    </div>
                  )}
                  {i === 2 && (
                    <div className="flex items-center gap-2 text-sm text-purple-600">
                      <Settings className="w-4 h-4" />
                      <span>Project integration</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
