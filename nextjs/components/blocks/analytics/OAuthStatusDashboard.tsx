'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useOAuthStatus } from '@/hooks/useOAuthStatus';
import { useOAuthError } from '@/hooks/useOAuthError';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw, 
  AlertTriangle,
  BarChart3,
  Search,
  Shield,
  Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface OAuthStatusDashboardProps {
  projectId: string;
  userId: string;
  autoRefresh?: boolean;
  compact?: boolean;
  onRefreshNeeded?: () => void;
}

const OAuthStatusDashboard: React.FC<OAuthStatusDashboardProps> = ({
  projectId,
  userId,
  autoRefresh = true,
  compact = false,
  onRefreshNeeded
}) => {
  const { 
    status, 
    loading, 
    error, 
    refresh, 
    isConnected, 
    isExpiringSoon, 
    getExpirationDate,
    getConnectionSummary 
  } = useOAuthStatus({ 
    projectId, 
    userId, 
    autoRefresh,
    refreshInterval: 5 * 60 * 1000 // 5 minutes
  });

  const { handleAsyncOperation } = useOAuthError();

  const handleRefresh = async () => {
    await handleAsyncOperation(
      refresh,
      'OAuth Status',
      {
        loadingMessage: 'Refreshing connection status...',
        successMessage: 'Status refreshed successfully'
      }
    );
  };

  const handleTokenRefresh = async (service: 'analytics' | 'searchConsole') => {
    await handleAsyncOperation(
      async () => {
        // Map frontend service names to API service names
        const apiServiceName = service === 'searchConsole' ? 'search_console' : service;
        
        const response = await fetch(`/api/projects/${projectId}/oauth/refresh`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ tokenType: apiServiceName })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to refresh token');
        }

        await refresh();
        onRefreshNeeded?.();
      },
      `${service} Token Refresh`,
      {
        loadingMessage: `Refreshing ${service} tokens...`,
        successMessage: `${service} tokens refreshed successfully`
      }
    );
  };

  const getServiceIcon = (service: string) => {
    switch (service) {
      case 'analytics':
        return BarChart3;
      case 'searchConsole':
        return Search;
      default:
        return Shield;
    }
  };

  const getServiceName = (service: string) => {
    switch (service) {
      case 'analytics':
        return 'Google Analytics';
      case 'searchConsole':
        return 'Search Console';
      default:
        return service;
    }
  };

  const getStatusBadge = (service: keyof typeof status) => {
    if (!status) return null;

    const serviceStatus = status[service];
    const connected = isConnected(service);
    const expiringSoon = isExpiringSoon(service, 60); // 1 hour

    if (!connected) {
      return <Badge variant="secondary">Not Connected</Badge>;
    }

    if (expiringSoon) {
      return <Badge variant="destructive">Expires Soon</Badge>;
    }

    return <Badge variant="default">Connected</Badge>;
  };

  const getExpirationInfo = (service: keyof typeof status) => {
    const expirationDate = getExpirationDate(service);
    if (!expirationDate) return null;

    const expiringSoon = isExpiringSoon(service, 60);
    const timeToExpiry = formatDistanceToNow(expirationDate, { addSuffix: true });

    return (
      <div className={`text-xs ${expiringSoon ? 'text-red-600' : 'text-muted-foreground'}`}>
        Expires {timeToExpiry}
      </div>
    );
  };

  if (loading && !status) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>Checking OAuth status...</span>
        </div>
      </Card>
    );
  }

  if (error && !status) {
    return (
      <Alert variant="destructive">
        <XCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!status) {
    return null;
  }

  const summary = getConnectionSummary();

  if (compact) {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              {isConnected('analytics') ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-muted-foreground" />
              )}
              <span className="text-sm">Analytics</span>
            </div>
            
            <div className="flex items-center space-x-1">
              {isConnected('searchConsole') ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-muted-foreground" />
              )}
              <span className="text-sm">Search Console</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant={summary.allConnected ? 'default' : 'secondary'}>
              {summary.connected}/{summary.total}
            </Badge>
            <Button variant="ghost" size="sm" onClick={handleRefresh}>
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5" />
            <div>
              <h4 className="font-medium">OAuth Connection Status</h4>
              <p className="text-sm text-muted-foreground">
                {summary.connected} of {summary.total} services connected ({summary.percentage}%)
              </p>
            </div>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
            {loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            Refresh
          </Button>
        </div>
      </Card>

      {/* Individual Service Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {(['analytics', 'searchConsole'] as const).map((service) => {
          const ServiceIcon = getServiceIcon(service);
          const serviceName = getServiceName(service);
          const connected = isConnected(service);
          const expiringSoon = isExpiringSoon(service, 60);
          
          return (
            <Card key={service} className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-primary/10">
                    <ServiceIcon className="w-4 h-4" />
                  </div>
                  <div>
                    <h5 className="font-medium">{serviceName}</h5>
                    {getStatusBadge(service)}
                  </div>
                </div>
              </div>

              {connected && (
                <div className="space-y-2">
                  <div className="text-sm">
                    <strong>Scopes:</strong> {status[service].scopes.length}
                  </div>
                  {getExpirationInfo(service)}
                  
                  {expiringSoon && (
                    <Alert variant="destructive" className="mt-2">
                      <Clock className="h-4 w-4" />
                      <AlertTitle>Token Expiring Soon</AlertTitle>
                      <AlertDescription>
                        This connection will expire soon. Refresh the token to continue access.
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTokenRefresh(service)}
                          className="ml-2"
                        >
                          Refresh Token
                        </Button>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}

              {!connected && (
                <div className="text-sm text-muted-foreground">
                  No active connection. Connect this service to enable analytics features.
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* Warnings and Alerts */}
      {summary.noneConnected && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>No Analytics Services Connected</AlertTitle>
          <AlertDescription>
            Connect at least one analytics service to start tracking your website performance.
          </AlertDescription>
        </Alert>
      )}

      {(isExpiringSoon('analytics') || isExpiringSoon('searchConsole')) && (
        <Alert variant="destructive">
          <Clock className="h-4 w-4" />
          <AlertTitle>Tokens Expiring Soon</AlertTitle>
          <AlertDescription>
            One or more of your OAuth tokens are expiring soon. Refresh them to maintain access to your analytics data.
          </AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertTitle>Status Check Error</AlertTitle>
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default OAuthStatusDashboard;