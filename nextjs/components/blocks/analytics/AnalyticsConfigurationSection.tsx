'use client';

import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import ProgressiveOAuthConnector from './ProgressiveOAuthConnector';
import { useOAuthError } from '@/hooks/useOAuthError';
import { 
  BarChart3, 
  Search, 
  Settings, 
  Info, 
  CheckCircle, 
  AlertTriangle,
  Zap,
  Shield
} from 'lucide-react';

interface AnalyticsConfigurationSectionProps {
  projectId: string;
  userId: string;
  project?: {
    id: string;
    name: string;
    domain?: string;
  };
  onConfigurationChange?: () => void;
}

interface ConnectionStatus {
  analytics: boolean;
  searchConsole: boolean;
  lastChecked?: string;
}

const AnalyticsConfigurationSection: React.FC<AnalyticsConfigurationSectionProps> = ({
  projectId,
  userId,
  project,
  onConfigurationChange
}) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    analytics: false,
    searchConsole: false
  });
  
  const [activeTab, setActiveTab] = useState('connections');
  const { handleAsyncOperation } = useOAuthError();

  const updateConnectionStatus = useCallback(async () => {
    await handleAsyncOperation(
      async () => {
        const response = await fetch(`/api/projects/${projectId}/oauth/status`);
        if (!response.ok) throw new Error('Failed to fetch status');
        
        const status = await response.json();
        setConnectionStatus({
          analytics: status.analytics?.authorized || false,
          searchConsole: status.searchConsole?.authorized || false,
          lastChecked: new Date().toISOString()
        });
        
        return status;
      },
      'OAuth Status',
      {
        onError: (error) => {
          console.error('Failed to update connection status:', error);
        }
      }
    );
  }, [projectId, handleAsyncOperation]);

  const handleConnectionSuccess = useCallback(() => {
    updateConnectionStatus();
    onConfigurationChange?.();
  }, [updateConnectionStatus, onConfigurationChange]);

  const handleConnectionRevoked = useCallback(() => {
    updateConnectionStatus();
    onConfigurationChange?.();
  }, [updateConnectionStatus, onConfigurationChange]);

  const getConnectionSummary = () => {
    const connectedCount = [connectionStatus.analytics, connectionStatus.searchConsole].filter(Boolean).length;
    const totalCount = 2;
    
    if (connectedCount === 0) {
      return {
        status: 'none',
        message: 'No analytics services connected',
        color: 'text-muted-foreground'
      };
    } else if (connectedCount === totalCount) {
      return {
        status: 'complete',
        message: 'All analytics services connected',
        color: 'text-green-600'
      };
    } else {
      return {
        status: 'partial',
        message: `${connectedCount} of ${totalCount} services connected`,
        color: 'text-yellow-600'
      };
    }
  };

  const summary = getConnectionSummary();

  // Initialize connection status on mount
  React.useEffect(() => {
    updateConnectionStatus();
  }, [updateConnectionStatus]);

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Analytics Configuration</h2>
            <p className="text-muted-foreground">
              Connect and configure your analytics services for {project?.name || 'your project'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={summary.status === 'complete' ? 'default' : 'secondary'}>
              {summary.message}
            </Badge>
          </div>
        </div>

        {/* Quick Overview */}
        <Card className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span className="font-medium">Google Analytics</span>
              {connectionStatus.analytics ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <div className="w-4 h-4 rounded-full border-2 border-muted-foreground" />
              )}
            </div>
            
            <Separator orientation="vertical" className="h-6" />
            
            <div className="flex items-center space-x-2">
              <Search className="w-5 h-5" />
              <span className="font-medium">Search Console</span>
              {connectionStatus.searchConsole ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <div className="w-4 h-4 rounded-full border-2 border-muted-foreground" />
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Main Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="connections" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span>Connections</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span>Security</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="connections" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold">Service Connections</h3>
              <Info className="w-4 h-4 text-muted-foreground" />
            </div>
            
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Progressive Authorization</AlertTitle>
              <AlertDescription>
                Connect only the services you need. Each service requires separate authorization 
                to ensure maximum security and minimal permissions.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ProgressiveOAuthConnector
                projectId={projectId}
                userId={userId}
                serviceType="analytics"
                onConnectionSuccess={handleConnectionSuccess}
                onConnectionRevoked={handleConnectionRevoked}
              />
              
              <ProgressiveOAuthConnector
                projectId={projectId}
                userId={userId}
                serviceType="search_console"
                onConnectionSuccess={handleConnectionSuccess}
                onConnectionRevoked={handleConnectionRevoked}
              />
            </div>
          </div>

          {/* Connection Benefits */}
          <Card className="p-6">
            <h4 className="font-semibold mb-4">Why Connect These Services?</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <BarChart3 className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h5 className="font-medium">Google Analytics</h5>
                    <p className="text-sm text-muted-foreground">
                      Track user behavior, conversion rates, and traffic sources. 
                      Essential for understanding your audience and optimizing content.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Search className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <h5 className="font-medium">Google Search Console</h5>
                    <p className="text-sm text-muted-foreground">
                      Monitor search performance, track rankings, and identify SEO opportunities. 
                      Critical for organic growth and technical SEO.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Service Settings</h3>
            
            {!connectionStatus.analytics && !connectionStatus.searchConsole ? (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>No Services Connected</AlertTitle>
                <AlertDescription>
                  Connect at least one analytics service to configure settings.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {connectionStatus.analytics && (
                  <Card className="p-6">
                    <h4 className="font-semibold mb-4">Google Analytics Settings</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Data Collection</p>
                          <p className="text-sm text-muted-foreground">
                            Configure how analytics data is collected and processed
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                      
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Reporting Frequency</p>
                          <p className="text-sm text-muted-foreground">
                            How often to sync data from Google Analytics
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    </div>
                  </Card>
                )}

                {connectionStatus.searchConsole && (
                  <Card className="p-6">
                    <h4 className="font-semibold mb-4">Search Console Settings</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Domain Verification</p>
                          <p className="text-sm text-muted-foreground">
                            Manage verified domains and properties
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Manage
                        </Button>
                      </div>
                      
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Query Monitoring</p>
                          <p className="text-sm text-muted-foreground">
                            Configure search query tracking and alerts
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    </div>
                  </Card>
                )}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Security & Permissions</h3>
            
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertTitle>Your Data is Secure</AlertTitle>
              <AlertDescription>
                LinkTrackPro uses OAuth 2.0 with minimal permissions. We only request 
                read-only access to the specific data needed for analytics.
              </AlertDescription>
            </Alert>

            <Card className="p-6">
              <h4 className="font-semibold mb-4">Permission Overview</h4>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium">Read-only Access</p>
                    <p className="text-sm text-muted-foreground">
                      We can only read your analytics data, never modify or delete it
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium">Secure Token Storage</p>
                    <p className="text-sm text-muted-foreground">
                      All access tokens are encrypted and stored securely
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium">Revoke Anytime</p>
                    <p className="text-sm text-muted-foreground">
                      You can revoke access at any time from the connections tab
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-semibold mb-4">Manage Access</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Review Permissions</p>
                    <p className="text-sm text-muted-foreground">
                      See exactly what permissions have been granted
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Review
                  </Button>
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Access Logs</p>
                    <p className="text-sm text-muted-foreground">
                      View when and how your data has been accessed
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    View Logs
                  </Button>
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Revoke All Access</p>
                    <p className="text-sm text-muted-foreground">
                      Remove all analytics service connections
                    </p>
                  </div>
                  <Button variant="destructive" size="sm">
                    Revoke All
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsConfigurationSection;