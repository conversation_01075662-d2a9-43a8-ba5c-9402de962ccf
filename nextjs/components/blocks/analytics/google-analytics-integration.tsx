'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { GoogleAnalyticsSetup } from './google-analytics-setup';
import { getUserGoogleAnalyticsConfig, GoogleAnalyticsProperty } from '@/models/user-configs';
import { useToast } from '@/components/ui/use-toast';

interface GoogleAnalyticsIntegrationProps {
  projectId: string;
  userId: string;
}

export function GoogleAnalyticsIntegration({ projectId, userId }: GoogleAnalyticsIntegrationProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [properties, setProperties] = useState<GoogleAnalyticsProperty[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  // Check connection status on component mount
  useEffect(() => {
    checkConnectionStatus();
  }, [projectId, userId]);

  const checkConnectionStatus = async () => {
    try {
      const { data: config } = await getUserGoogleAnalyticsConfig(userId, projectId);
      
      if (config && config.isActive) {
        setIsConnected(true);
        setProperties(config.properties || []);
      } else {
        setIsConnected(false);
        setProperties([]);
      }
    } catch (error) {
      console.error('Error checking Google Analytics connection:', error);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    try {
      // Call the authorization API endpoint
      const response = await fetch('/api/auth/google/analytics/authorize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ projectId }),
      });

      const data = await response.json();
      
      if (data.success && data.data.authUrl) {
        // Redirect to Google OAuth
        window.location.href = data.data.authUrl;
      } else {
        throw new Error(data.error || 'Failed to generate authorization URL');
      }
    } catch (error) {
      console.error('OAuth connection error:', error);
      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : "Failed to connect to Google Analytics",
        variant: "destructive",
      });
      throw error;
    }
  };

  const handleDisconnect = async () => {
    try {
      const response = await fetch('/api/auth/google/analytics/disconnect', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ projectId }),
      });

      const data = await response.json();
      
      if (data.success) {
        setIsConnected(false);
        setProperties([]);
        toast({
          title: "Disconnected",
          description: "Google Analytics has been disconnected successfully",
        });
      } else {
        throw new Error(data.error || 'Failed to disconnect');
      }
    } catch (error) {
      console.error('Disconnect error:', error);
      toast({
        title: "Disconnect Failed",
        description: error instanceof Error ? error.message : "Failed to disconnect from Google Analytics",
        variant: "destructive",
      });
      throw error;
    }
  };

  const handleRefresh = async () => {
    try {
      const response = await fetch('/api/auth/google/analytics/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ projectId }),
      });

      const data = await response.json();
      
      if (data.success) {
        await checkConnectionStatus(); // Refresh the connection status
        toast({
          title: "Refreshed",
          description: "Google Analytics connection has been refreshed",
        });
      } else {
        throw new Error(data.error || 'Failed to refresh connection');
      }
    } catch (error) {
      console.error('Refresh error:', error);
      toast({
        title: "Refresh Failed",
        description: error instanceof Error ? error.message : "Failed to refresh Google Analytics connection",
        variant: "destructive",
      });
      throw error;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-sm text-muted-foreground">Loading Google Analytics configuration...</div>
      </div>
    );
  }

  return (
    <GoogleAnalyticsSetup
      projectId={projectId}
      isConnected={isConnected}
      properties={properties}
      onConnect={handleConnect}
      onDisconnect={handleDisconnect}
      onRefresh={handleRefresh}
    />
  );
}