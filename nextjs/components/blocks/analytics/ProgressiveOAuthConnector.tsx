'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { 
  BarChart3, 
  Search, 
  Plus, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info,
  RefreshCw,
  Unlink
} from 'lucide-react';

type ServiceType = 'analytics' | 'search_console';

interface OAuthStatus {
  authorized: boolean;
  scopes: string[];
  expiresAt?: number;
  error?: string;
}

interface ProgressiveOAuthConnectorProps {
  projectId: string;
  userId: string;
  serviceType: ServiceType;
  onConnectionSuccess?: () => void;
  onConnectionRevoked?: () => void;
}

interface ConnectionState {
  status: 'idle' | 'connecting' | 'connected' | 'error' | 'testing' | 'refreshing';
  error?: {
    message: string;
    type?: string;
    requiresReauth?: boolean;
  };
}

const ProgressiveOAuthConnector: React.FC<ProgressiveOAuthConnectorProps> = ({
  projectId,
  userId,
  serviceType,
  onConnectionSuccess,
  onConnectionRevoked
}) => {
  const [oauthStatus, setOauthStatus] = useState<OAuthStatus>({ 
    authorized: false, 
    scopes: [] 
  });
  
  const [connectionState, setConnectionState] = useState<ConnectionState>({ 
    status: 'idle' 
  });

  const serviceName = serviceType === 'analytics' ? 'Google Analytics' : 'Google Search Console';
  const serviceIcon = serviceType === 'analytics' ? BarChart3 : Search;

  const checkOAuthStatus = useCallback(async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/oauth/status`);
      if (!response.ok) {
        throw new Error('Failed to check OAuth status');
      }
      
      const status = await response.json();
      const serviceStatus = serviceType === 'analytics' ? status.analytics : status.searchConsole;
      
      setOauthStatus(serviceStatus || { authorized: false, scopes: [] });
      
      if (serviceStatus?.authorized) {
        setConnectionState({ status: 'connected' });
      } else {
        setConnectionState({ status: 'idle' });
      }
    } catch (error) {
      console.error('Failed to check OAuth status:', error);
      setConnectionState({ 
        status: 'error', 
        error: { message: 'Failed to check connection status' }
      });
    }
  }, [projectId, serviceType]);

  const initiateOAuthFlow = async () => {
    setConnectionState({ status: 'connecting' });
    
    try {
      const response = await fetch(`/api/projects/${projectId}/oauth/authorize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tokenType: serviceType,
          redirectUrl: `${window.location.origin}/oauth/success`
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to initiate OAuth flow');
      }

      const { authUrl, state } = await response.json();
      
      // Store state and service info for callback verification
      sessionStorage.setItem('oauth_state', state);
      sessionStorage.setItem('oauth_service_type', serviceType);
      sessionStorage.setItem('oauth_project_id', projectId);
      
      // Redirect to Google OAuth
      window.location.href = authUrl;
    } catch (error: any) {
      console.error('Failed to initiate OAuth flow:', error);
      setConnectionState({ 
        status: 'error', 
        error: { 
          message: error.message || 'Failed to start authorization',
          requiresReauth: false
        }
      });
      toast.error(`Failed to connect ${serviceName}: ${error.message}`);
    }
  };

  const testConnection = async () => {
    setConnectionState({ status: 'testing' });
    
    try {
      const response = await fetch(
        `/api/projects/${projectId}/oauth/test?tokenType=${serviceType}`
      );
      
      const result = await response.json();
      
      if (result.isValid) {
        setConnectionState({ status: 'connected' });
        toast.success(`${serviceName} connection is working!`);
        await checkOAuthStatus();
      } else {
        setConnectionState({ 
          status: 'error', 
          error: {
            message: result.error || 'Connection test failed',
            type: result.type,
            requiresReauth: result.requiresReauth
          }
        });
        toast.error(`${serviceName} connection failed: ${result.error}`);
      }
    } catch (error: any) {
      setConnectionState({ 
        status: 'error', 
        error: { message: 'Failed to test connection' }
      });
      toast.error('Failed to test connection');
    }
  };

  const refreshConnection = async () => {
    setConnectionState({ status: 'refreshing' });
    
    try {
      const response = await fetch(`/api/projects/${projectId}/oauth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tokenType: serviceType })
      });

      const result = await response.json();

      if (result.success) {
        setConnectionState({ status: 'connected' });
        toast.success(`${serviceName} tokens refreshed successfully`);
        await checkOAuthStatus();
      } else {
        throw new Error(result.error || 'Failed to refresh tokens');
      }
    } catch (error: any) {
      console.error('Failed to refresh connection:', error);
      setConnectionState({ 
        status: 'error', 
        error: { 
          message: error.message || 'Failed to refresh connection',
          requiresReauth: true
        }
      });
      toast.error(`Failed to refresh ${serviceName} connection`);
    }
  };

  const revokeConnection = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/oauth/revoke`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tokenType: serviceType })
      });

      const result = await response.json();

      if (result.success) {
        setOauthStatus({ authorized: false, scopes: [] });
        setConnectionState({ status: 'idle' });
        toast.success(`${serviceName} access revoked`);
        onConnectionRevoked?.();
      } else {
        throw new Error(result.error || 'Failed to revoke access');
      }
    } catch (error: any) {
      console.error('Failed to revoke connection:', error);
      toast.error(`Failed to revoke ${serviceName} access`);
    }
  };

  const formatDate = (timestamp?: number): string => {
    if (!timestamp) return 'Unknown';
    return new Date(timestamp).toLocaleDateString();
  };

  const getStatusBadge = () => {
    if (connectionState.status === 'connected' && oauthStatus.authorized) {
      return (
        <div className="flex items-center space-x-2 text-sm text-green-600">
          <CheckCircle className="w-4 h-4" />
          <span>Connected • Expires {formatDate(oauthStatus.expiresAt)}</span>
        </div>
      );
    }
    
    if (connectionState.status === 'error') {
      return (
        <div className="flex items-center space-x-2 text-sm text-red-600">
          <XCircle className="w-4 h-4" />
          <span>Connection Error</span>
        </div>
      );
    }
    
    return (
      <p className="text-sm text-muted-foreground">Not connected</p>
    );
  };

  const getActionButtons = () => {
    const isConnected = connectionState.status === 'connected' && oauthStatus.authorized;
    const isLoading = ['connecting', 'testing', 'refreshing'].includes(connectionState.status);
    
    if (isConnected) {
      return (
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={testConnection}
            disabled={isLoading}
          >
            {connectionState.status === 'testing' ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4" />
            )}
            Test
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={refreshConnection}
            disabled={isLoading}
          >
            {connectionState.status === 'refreshing' ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            Refresh
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={revokeConnection}
            disabled={isLoading}
          >
            <Unlink className="w-4 h-4" />
            Disconnect
          </Button>
        </div>
      );
    }
    
    return (
      <Button 
        onClick={initiateOAuthFlow} 
        disabled={isLoading}
        className="flex items-center space-x-2"
      >
        {connectionState.status === 'connecting' ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Plus className="w-4 h-4" />
        )}
        <span>Connect {serviceName}</span>
      </Button>
    );
  };

  const getConnectionInfo = () => {
    const isConnected = connectionState.status === 'connected' && oauthStatus.authorized;
    
    if (isConnected) {
      return (
        <div className="space-y-2">
          {getStatusBadge()}
          <div className="text-xs text-muted-foreground">
            Permissions: {oauthStatus.scopes.join(', ')}
          </div>
        </div>
      );
    }
    
    if (connectionState.status === 'error' && connectionState.error?.requiresReauth) {
      return (
        <Alert variant="destructive" className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Reconnection Required</AlertTitle>
          <AlertDescription>
            Your {serviceName} connection needs to be renewed. Please disconnect and reconnect to continue.
          </AlertDescription>
        </Alert>
      );
    }
    
    if (connectionState.status === 'error') {
      return (
        <Alert variant="destructive" className="mt-4">
          <XCircle className="h-4 w-4" />
          <AlertTitle>Connection Error</AlertTitle>
          <AlertDescription>
            {connectionState.error?.message || 'An unknown error occurred'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return (
      <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-4 mt-4">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              Why connect {serviceName}?
            </p>
            <ul className="text-blue-700 dark:text-blue-200 space-y-1">
              {serviceType === 'analytics' ? (
                <>
                  <li>• Track website traffic and user behavior</li>
                  <li>• Monitor conversion rates and goals</li>
                  <li>• Analyze audience demographics and interests</li>
                </>
              ) : (
                <>
                  <li>• Monitor search performance and rankings</li>
                  <li>• Track keyword impressions and clicks</li>
                  <li>• Identify indexing issues and opportunities</li>
                </>
              )}
            </ul>
            <p className="mt-2 text-xs text-blue-600 dark:text-blue-300">
              This will open a new window to grant specific permissions to LinkTrackPro.
            </p>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    checkOAuthStatus();
  }, [checkOAuthStatus]);

  // Check for successful OAuth callback
  useEffect(() => {
    const checkOAuthCallback = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const success = urlParams.get('oauth_success');
      const tokenType = urlParams.get('token_type');
      
      if (success === 'true' && tokenType === serviceType) {
        toast.success(`Successfully connected ${serviceName}!`);
        checkOAuthStatus();
        onConnectionSuccess?.();
        
        // Clean up URL parameters
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }
    };

    checkOAuthCallback();
  }, [serviceType, serviceName, checkOAuthStatus, onConnectionSuccess]);

  const ServiceIcon = serviceIcon;

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <ServiceIcon className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-semibold">{serviceName}</h3>
            {getStatusBadge()}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {getActionButtons()}
        </div>
      </div>

      {getConnectionInfo()}
    </Card>
  );
};

export default ProgressiveOAuthConnector;