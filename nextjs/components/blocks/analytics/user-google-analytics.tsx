'use client';

import { useState, useEffect } from 'react';
import { useSession, signIn } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, LogIn, CheckCircle, AlertCircle, RefreshCw, BarChart3, Search } from 'lucide-react';

interface GoogleProperty {
  propertyId: string;
  displayName: string;
  websiteUrl: string;
  timeZone: string;
  currencyCode: string;
  accountId: string;
  accountDisplayName: string;
}

interface ConnectionStatus {
  connected: boolean;
  hasAnalytics: boolean;
  hasSearchConsole: boolean;
  scopes?: string[];
  tokenExpiry?: number;
  message?: string;
}

interface UserGoogleAnalyticsProps {
  projectId: string;
}

export function UserGoogleAnalytics({ projectId }: UserGoogleAnalyticsProps) {
  const { data: session, status } = useSession();
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [properties, setProperties] = useState<GoogleProperty[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session) {
      checkConnection();
    }
  }, [session, projectId]);

  const checkConnection = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/google-analytics/test`);
      const data = await response.json();
      
      if (data.success || data.connected) {
        setConnectionStatus({
          connected: data.connected,
          hasAnalytics: data.hasAnalytics,
          hasSearchConsole: data.hasSearchConsole,
          scopes: data.scopes,
          tokenExpiry: data.tokenExpiry,
          message: data.message
        });
        
        // 如果有Analytics权限，获取属性列表
        if (data.hasAnalytics) {
          await fetchProperties();
        }
      } else {
        setConnectionStatus({
          connected: false,
          hasAnalytics: false,
          hasSearchConsole: false,
          message: data.message || data.error
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check connection');
      setConnectionStatus({
        connected: false,
        hasAnalytics: false,
        hasSearchConsole: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProperties = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/google-analytics/properties`);
      const data = await response.json();
      
      if (data.success) {
        setProperties(data.data.properties || []);
      }
    } catch (err) {
      console.warn('Failed to fetch properties:', err);
    }
  };

  const handleGoogleLogin = () => {
    signIn('google', { 
      callbackUrl: window.location.href,
      // 这会触发重新授权以获取Analytics权限
    });
  };

  const handleRefresh = () => {
    checkConnection();
  };

  if (status === 'loading' || isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Google Analytics & Search Console</CardTitle>
          <CardDescription>Loading connection status...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span className="text-sm text-muted-foreground">Checking permissions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!session) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Google Analytics & Search Console</CardTitle>
          <CardDescription>
            Login with your Google account to access Analytics and Search Console data directly.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <p>With your Google account, you can:</p>
            <ul className="mt-2 ml-4 list-disc space-y-1">
              <li>Access your Google Analytics properties</li>
              <li>View Search Console data</li>
              <li>Get real-time insights for your projects</li>
              <li>No additional configuration required</li>
            </ul>
          </div>
          
          <Button onClick={handleGoogleLogin} className="w-full">
            <LogIn className="w-4 h-4 mr-2" />
            Login with Google
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Google Analytics & Search Console
              {connectionStatus?.connected && (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Connected
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Access your Google Analytics and Search Console data using your Google account.
            </CardDescription>
          </div>
          {connectionStatus?.connected && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Refresh
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {connectionStatus?.connected ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="w-4 h-4" />
              {connectionStatus.message || 'Successfully connected to Google services'}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2 p-3 border rounded-lg">
                <BarChart3 className="w-5 h-5 text-blue-500" />
                <div>
                  <div className="text-sm font-medium">Google Analytics</div>
                  <div className="text-xs text-muted-foreground">
                    {connectionStatus.hasAnalytics ? 'Connected' : 'Not available'}
                  </div>
                </div>
                {connectionStatus.hasAnalytics && (
                  <Badge variant="outline" className="ml-auto">
                    {properties.length} properties
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-2 p-3 border rounded-lg">
                <Search className="w-5 h-5 text-green-500" />
                <div>
                  <div className="text-sm font-medium">Search Console</div>
                  <div className="text-xs text-muted-foreground">
                    {connectionStatus.hasSearchConsole ? 'Connected' : 'Not available'}
                  </div>
                </div>
              </div>
            </div>

            {connectionStatus.hasAnalytics && properties.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium mb-3">Your Google Analytics Properties</h4>
                  <div className="space-y-3">
                    {properties.map((property) => (
                      <div
                        key={property.propertyId}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="space-y-1">
                          <div className="font-medium text-sm">{property.displayName}</div>
                          <div className="text-xs text-muted-foreground">
                            {property.websiteUrl}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Account: {property.accountDisplayName}
                          </div>
                        </div>
                        <div className="text-right space-y-1">
                          <Badge variant="outline" className="text-xs">
                            {property.propertyId}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {property.timeZone}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}

            {(!connectionStatus.hasAnalytics && !connectionStatus.hasSearchConsole) && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Your Google account doesn't have access to Analytics or Search Console, or you didn't grant the necessary permissions. 
                  Please re-login to grant access.
                </AlertDescription>
              </Alert>
            )}

            <Separator />

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleGoogleLogin}
                className="flex-1"
              >
                Re-authorize Google Access
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {connectionStatus?.message || 'Google Analytics and Search Console access not available. Please re-login with Google to grant the necessary permissions.'}
              </AlertDescription>
            </Alert>
            
            <Button onClick={handleGoogleLogin} className="w-full">
              <LogIn className="w-4 h-4 mr-2" />
              Re-login with Google
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}