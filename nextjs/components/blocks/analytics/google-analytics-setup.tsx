'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, ExternalLink, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { GoogleAnalyticsProperty } from '@/models/user-configs';

interface GoogleAnalyticsSetupProps {
  projectId: string;
  isConnected: boolean;
  properties?: GoogleAnalyticsProperty[];
  onConnect: () => Promise<void>;
  onDisconnect: () => Promise<void>;
  onRefresh: () => Promise<void>;
}

export function GoogleAnalyticsSetup({
  projectId,
  isConnected,
  properties = [],
  onConnect,
  onDisconnect,
  onRefresh
}: GoogleAnalyticsSetupProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleConnect = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await onConnect();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect to Google Analytics');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await onDisconnect();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect from Google Analytics');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    setError(null);
    
    try {
      await onRefresh();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh properties');
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Google Analytics
              {isConnected && (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Connected
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Connect your Google Analytics account to track website performance and user behavior.
            </CardDescription>
          </div>
          {isConnected && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Refresh
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!isConnected ? (
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p>To connect Google Analytics, you will need to:</p>
              <ul className="mt-2 ml-4 list-disc space-y-1">
                <li>Grant permission to access your Google Analytics data</li>
                <li>Select which properties to track for this project</li>
                <li>Configure data collection preferences</li>
              </ul>
            </div>
            
            <Button 
              onClick={handleConnect} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Connect Google Analytics
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="w-4 h-4" />
              Successfully connected to Google Analytics
            </div>

            <Separator />

            <div>
              <h4 className="text-sm font-medium mb-3">Available Properties</h4>
              {properties.length > 0 ? (
                <div className="space-y-3">
                  {properties.map((property) => (
                    <div
                      key={property.propertyId}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="space-y-1">
                        <div className="font-medium text-sm">{property.displayName}</div>
                        <div className="text-xs text-muted-foreground">
                          {property.websiteUrl}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Account: {property.accountDisplayName}
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <Badge variant="outline" className="text-xs">
                          {property.propertyId}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          {property.timeZone}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground text-center py-8">
                  No Google Analytics properties found.
                  <br />
                  Make sure you have access to at least one GA4 property.
                </div>
              )}
            </div>

            <Separator />

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleDisconnect}
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : null}
                Disconnect
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}