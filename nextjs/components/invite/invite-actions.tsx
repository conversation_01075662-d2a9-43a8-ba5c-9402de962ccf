"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CopyIcon, Share2Icon } from "lucide-react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface InviteActionsProps {
  inviteLink: string;
}

export default function InviteActions({ inviteLink }: InviteActionsProps) {
  const t = useTranslations("invitation");
  const [copying, setCopying] = useState(false);

  const handleCopy = async () => {
    try {
      setCopying(true);
      await navigator.clipboard.writeText(inviteLink);
      toast.success(t("copy_success"));
    } catch (error) {
      toast.error(t("copy_failed"));
    } finally {
      setCopying(false);
      setTimeout(() => setCopying(false), 2000);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: t("share_title"),
          text: t("share_text"),
          url: inviteLink,
        });
        toast.success(t("share_success"));
      } catch (error) {
        if ((error as Error).name !== "AbortError") {
          toast.error(t("share_failed"));
        }
      }
    } else {
      handleCopy();
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-4">
      <Input readOnly value={inviteLink} className="flex-1" />
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          onClick={handleCopy}
          disabled={copying}
          className="copy-button"
        >
          <CopyIcon className="h-4 w-4 mr-2" />
          {copying ? t("copying") : t("copy")}
        </Button>
        
        <Button 
          variant="default"
          onClick={handleShare}
          className="share-button"
        >
          <Share2Icon className="h-4 w-4 mr-2" />
          {t("share")}
        </Button>
      </div>
    </div>
  );
} 