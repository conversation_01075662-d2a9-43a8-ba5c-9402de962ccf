"use client";

import { BsMoonStars, BsSun } from "react-icons/bs";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export default function () {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center gap-x-2 px-2">
        <div className="w-5 h-5"></div>
      </div>
    );
  }

  const handleThemeChange = function () {
    if (theme === "dark") {
      setTheme("light");
    } else {
      setTheme("dark");
    }
  };

  return (
    <div className="flex items-center gap-x-2 px-2">
      {theme === "dark" ? (
        <BsSun
          className="cursor-pointer text-lg text-muted-foreground hover:text-foreground transition-colors"
          onClick={handleThemeChange}
          width={80}
          height={20}
        />
      ) : (
        <BsMoonStars
          className="cursor-pointer text-lg text-muted-foreground hover:text-foreground transition-colors"
          onClick={handleThemeChange}
          width={80}
          height={20}
        />
      )}
    </div>
  );
}
