'use client';

import { useEffect } from 'react';
import { setupFetchOverride } from '@/lib/fetch-with-timeout';

/**
 * Component to initialize the fetch override for NextAuth OAuth requests
 * This must run on the client side to override the global fetch function
 */
export default function FetchOverrideInit() {
  useEffect(() => {
    // Initialize the fetch override for OAuth requests
    setupFetchOverride();
  }, []);

  // This component doesn't render anything
  return null;
}