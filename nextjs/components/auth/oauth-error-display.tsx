'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, RefreshCw, Wifi, Clock } from 'lucide-react';
import { classifyOAuthError, getOAuthRetryUrl, logOAuthError, type OAuthError } from '@/lib/oauth-error-handler';

interface OAuthErrorDisplayProps {
  error: string | Error;
  provider?: string;
  onRetry?: () => void;
  className?: string;
}

export default function OAuthErrorDisplay({ 
  error, 
  provider = 'OAuth', 
  onRetry,
  className = ''
}: OAuthErrorDisplayProps) {
  const [oauthError, setOauthError] = useState<OAuthError | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const classified = classifyOAuthError(error);
    setOauthError(classified);
    
    // Log the error for monitoring
    logOAuthError(error, { 
      provider, 
      retryCount,
      component: 'OAuthErrorDisplay'
    });
  }, [error, provider, retryCount]);

  const handleRetry = async () => {
    if (!oauthError || !oauthError.canRetry) return;

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      if (onRetry) {
        // Use custom retry handler
        await onRetry();
      } else {
        // Default retry behavior - redirect to OAuth provider
        const retryUrl = getOAuthRetryUrl(provider);
        
        // Add a small delay before redirect
        await new Promise(resolve => setTimeout(resolve, 1000));
        window.location.href = retryUrl;
      }
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      setIsRetrying(false);
    }
  };

  const getErrorIcon = () => {
    if (!oauthError) return <AlertCircle className="h-4 w-4" />;
    
    switch (oauthError.type) {
      case 'timeout':
        return <Clock className="h-4 w-4" />;
      case 'network':
        return <Wifi className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getErrorTitle = () => {
    if (!oauthError) return 'Authentication Error';
    
    switch (oauthError.type) {
      case 'timeout':
        return `${oauthError.provider} Connection Timeout`;
      case 'network':
        return 'Network Connection Error';
      case 'provider':
        return `${oauthError.provider} Service Error`;
      default:
        return 'Authentication Error';
    }
  };

  if (!oauthError) return null;

  return (
    <Alert className={`border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive ${className}`}>
      {getErrorIcon()}
      <AlertTitle>{getErrorTitle()}</AlertTitle>
      <AlertDescription className="mt-2">
        <p className="mb-3">{oauthError.userMessage}</p>
        
        {retryCount > 0 && (
          <p className="text-sm text-muted-foreground mb-3">
            Retry attempt: {retryCount}
          </p>
        )}
        
        {oauthError.canRetry && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              disabled={isRetrying}
              className="h-8"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-3 w-3" />
                  Try Again
                </>
              )}
            </Button>
            
            {oauthError.type === 'timeout' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.reload()}
                className="h-8 text-xs"
              >
                Refresh Page
              </Button>
            )}
          </div>
        )}
        
        {oauthError.type === 'timeout' && (
          <div className="mt-3 p-2 bg-muted rounded-md">
            <p className="text-xs text-muted-foreground">
              <strong>Tip:</strong> If this keeps happening, try:
            </p>
            <ul className="text-xs text-muted-foreground mt-1 ml-4 list-disc">
              <li>Check your internet connection</li>
              <li>Try again in a few minutes</li>
              <li>Use a different network if possible</li>
            </ul>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}