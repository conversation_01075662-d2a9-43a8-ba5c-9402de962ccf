"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { updateSetting } from "@/app/[locale]/(admin)/admin/settings/actions";

interface SettingsFormProps {
  settingKey: string;
  label: string;
  description?: string;
  initialValue: string;
  type?: string;
  min?: number;
  max?: number;
}

export default function SettingsForm({
  settingKey,
  label,
  description,
  initialValue,
  type = "text",
  min,
  max
}: SettingsFormProps) {
  const t = useTranslations("admin.settings");
  const [value, setValue] = useState(initialValue);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);
  
  const handleSave = async () => {
    try {
      setSaving(true);
      setSuccess(false);
      
      // Convert to number for validation if type is number
      if (type === "number") {
        const numValue = Number(value);
        if (isNaN(numValue)) {
          toast.error(t("validation.not_a_number"));
          return;
        }
        
        if (min !== undefined && numValue < min) {
          toast.error(t("validation.min_value", { min }));
          return;
        }
        
        if (max !== undefined && numValue > max) {
          toast.error(t("validation.max_value", { max }));
          return;
        }
      }
      
      const result = await updateSetting(settingKey, value);
      
      if (result.success) {
        toast.success(t("saved_success"));
        setSuccess(true);
      } else {
        toast.error(t("saved_error"));
      }
    } catch (error) {
      console.error("Error saving setting:", error);
      toast.error(t("saved_error"));
    } finally {
      setSaving(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor={settingKey}>{label}</Label>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
        <div className="flex gap-4">
          <Input
            id={settingKey}
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
              setSuccess(false);
            }}
            type={type}
            min={min}
            max={max}
            className="max-w-xs"
          />
          <Button 
            onClick={handleSave} 
            disabled={saving || (value === initialValue && !success)}
          >
            {saving ? t("saving") : success ? t("saved") : t("save")}
          </Button>
        </div>
      </div>
    </div>
  );
} 