"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { formatDistanceToNow } from "date-fns";
import { toast } from "sonner";
import { AiTask, AiTaskStatus } from "@/types/aiTask";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { deleteAiTaskByOrderNo, updateAiTaskByOrderNo } from "@/models/aiTask";
import { formatDate } from "@/lib/utils";
interface TaskDetailProps {
  task: AiTask;
}

export default function TaskDetail({ task }: TaskDetailProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [restartLoading, setRestartLoading] = useState(false);

  // Status badge colors
  const getStatusColor = (status: AiTaskStatus) => {
    switch (status) {
      case "SUCCEED":
        return "bg-green-500";
      case "FAILED":
        return "bg-red-500";
      case "PROCESSING":
        return "bg-blue-500";
      case "PENDING":
      default:
        return "bg-yellow-500";
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const { error } = await deleteAiTaskByOrderNo(task.order_no);
      
      if (error) {
        throw new Error(error.message);
      }
      
      toast.success("Task deleted successfully");
      router.push("/admin/ai-tasks");
    } catch (error) {
      console.error("Failed to delete task:", error);
      toast.error("Failed to delete task");
    } finally {
      setLoading(false);
    }
  };

  const handleRestartTask = async () => {
    try {
      setRestartLoading(true);
      const { error } = await updateAiTaskByOrderNo(task.order_no, {
        orderstatus: "PENDING",
        fail_reason: "", // Clear the error message
      });
      
      if (error) {
        throw new Error(error.message);
      }
      
      toast.success("Task restarted successfully");
      router.refresh();
    } catch (error) {
      console.error("Failed to restart task:", error);
      toast.error("Failed to restart task");
    } finally {
      setRestartLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Task #{task.order_no}</CardTitle>
            <CardDescription>
              Created {task.create_time && formatDate(task.create_time.toString())}
            </CardDescription>
          </div>
          <Badge className={getStatusColor(task.orderstatus)}>
            {task.orderstatus}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Product</h3>
            <p>{task.product_name}</p>
          </div>
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Credit Cost</h3>
            <p>{task.credit_cost}</p>
          </div>
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Processing Time</h3>
            <p>{task.cost_time ? `${task.cost_time} seconds` : "N/A"}</p>
          </div>
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">User ID</h3>
            <p className="truncate">{task.user_uuid}</p>
          </div>
        </div>

        {task.input_file_path && (
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Input File</h3>
            <p className="break-words">{task.input_file_path}</p>
          </div>
        )}

        {task.output_image_path && (
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Output File</h3>
            <p className="break-words">{task.output_image_path}</p>
          </div>
        )}

        {task.fail_reason && (
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1 text-red-500">Error Message</h3>
            <p className="text-red-500 break-words">{task.fail_reason}</p>
          </div>
        )}

        {task.output_options && (
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Output Options</h3>
            <pre className="bg-muted p-2 rounded text-sm overflow-auto">
              {JSON.stringify(task.output_options, null, 2)}
            </pre>
          </div>
        )}

        {task.callback_url && (
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">Callback URL</h3>
            <p className="break-words">{task.callback_url}</p>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-end space-x-2">
        {task.orderstatus === "FAILED" && (
          <Button 
            variant="secondary" 
            onClick={handleRestartTask} 
            disabled={restartLoading}
          >
            {restartLoading ? "Restarting..." : "Restart Task"}
          </Button>
        )}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" disabled={loading}>
              Delete Task
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                task and its associated data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete} disabled={loading}>
                {loading ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardFooter>
    </Card>
  );
} 