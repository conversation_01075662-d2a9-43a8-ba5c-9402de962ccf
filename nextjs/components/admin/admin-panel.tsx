"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  RefreshCw, 
  BarChart3, 
  Database,
  Users,
  ExternalLink,
  Search,
  Trash2,
  Filter,
  AlertCircle,
  CheckCircle,
  Info,
  Plus,
  Eye,
  Download
} from "lucide-react";
import { toast } from "sonner";
import { Link, Project } from "@/types/links";

interface AdminData {
  links: (Link & { project_name?: string; user_email?: string })[];
  projects: (Project & { user_email?: string; links_count?: number })[];
  users: Array<{
    user_id: string;
    user_email: string;
    projects_count: number;
    links_count: number;
    total_traffic: number;
    last_active?: string;
  }>;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export function AdminPanel() {
  const t = useTranslations("links");
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("links");
  const [adminData, setAdminData] = useState<AdminData>({
    links: [],
    projects: [],
    users: [],
    pagination: { total: 0, limit: 100, offset: 0, hasMore: false }
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    link_type: "all",
    user_id: ""
  });
  const [currentPage, setCurrentPage] = useState(0);

  // Fetch data based on active tab
  const fetchData = async (tab: string = activeTab, page: number = currentPage) => {
    setLoading(true);
    try {
      const limit = 50;
      const offset = page * limit;
      
      if (tab === "links") {
        const params = new URLSearchParams({
          limit: limit.toString(),
          offset: offset.toString(),
          ...(filters.search && { search: filters.search }),
          status: filters.status,
          link_type: filters.link_type,
          ...(filters.user_id && filters.user_id !== "all" && { user_id: filters.user_id })
        });

        const response = await fetch(`/api/admin/links?${params}`);
        const data = await response.json();
        
        if (response.ok) {
          setAdminData(prev => ({
            ...prev,
            links: data.links,
            pagination: data.pagination
          }));
        } else {
          toast.error(data.error || "Failed to fetch links");
        }
      } else if (tab === "projects") {
        const params = new URLSearchParams({
          limit: limit.toString(),
          offset: offset.toString(),
          ...(filters.search && { search: filters.search }),
          ...(filters.user_id && { user_id: filters.user_id })
        });

        const response = await fetch(`/api/admin/projects?${params}`);
        const data = await response.json();
        
        if (response.ok) {
          setAdminData(prev => ({
            ...prev,
            projects: data.projects,
            pagination: data.pagination
          }));
        } else {
          toast.error(data.error || "Failed to fetch projects");
        }
      } else if (tab === "users") {
        const response = await fetch("/api/admin/users");
        const data = await response.json();
        
        if (response.ok) {
          setAdminData(prev => ({
            ...prev,
            users: data.users
          }));
        } else {
          toast.error(data.error || "Failed to fetch users");
        }
      }
    } catch (error) {
      console.error("Error fetching admin data:", error);
      toast.error("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select items to delete");
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} items?`)) {
      return;
    }

    setLoading(true);
    try {
      let endpoint = "";
      let payload = {};

      if (activeTab === "links") {
        endpoint = "/api/admin/links";
        payload = { linkIds: selectedItems };
      } else if (activeTab === "projects") {
        endpoint = "/api/admin/projects";
        payload = { projectIds: selectedItems };
      }

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setSelectedItems([]);
        fetchData();
      } else {
        toast.error(result.error || "Batch delete failed");
      }
    } catch (error) {
      console.error("Error in batch delete:", error);
      toast.error("Batch delete failed");
    } finally {
      setLoading(false);
    }
  };

  // Handle selection changes
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      if (activeTab === "links") {
        setSelectedItems(adminData.links.map(link => link.id));
      } else if (activeTab === "projects") {
        setSelectedItems(adminData.projects.map(project => project.id));
      }
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  // Handle search and filters
  const handleSearch = () => {
    setCurrentPage(0);
    fetchData(activeTab, 0);
  };

  const handleResetFilters = () => {
    setFilters({ search: "", status: "all", link_type: "all", user_id: "" });
    setCurrentPage(0);
    fetchData(activeTab, 0);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchData(activeTab, newPage);
  };

  // Load data when tab changes
  useEffect(() => {
    fetchData(activeTab, 0);
    setCurrentPage(0);
    setSelectedItems([]);
  }, [activeTab]);

  // Initial data load
  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            管理员控制面板
          </CardTitle>
          <CardDescription>
            管理所有用户的外链资源和项目数据
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="links" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              外链管理
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              项目管理
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              用户统计
            </TabsTrigger>
          </TabsList>

          <div className="flex items-center gap-2">
            {selectedItems.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBatchDelete}
                disabled={loading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除选中 ({selectedItems.length})
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchData()}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        {(activeTab === "links" || activeTab === "projects") && (
          <Card className="mb-4">
            <CardContent className="pt-4">
              <div className="flex flex-wrap gap-4 items-end">
                <div className="flex-1 min-w-[300px]">
                  <label className="text-sm font-medium mb-2 block">搜索</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder={activeTab === "links" ? "搜索标题、URL或域名..." : "搜索项目名称、域名或描述..."}
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>

                {activeTab === "links" && (
                  <>
                    <div>
                      <label className="text-sm font-medium mb-2 block">状态</label>
                      <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="全部" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="pending">待处理</SelectItem>
                          <SelectItem value="active">活跃</SelectItem>
                          <SelectItem value="inactive">不活跃</SelectItem>
                          <SelectItem value="removed">已移除</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">类型</label>
                      <Select value={filters.link_type} onValueChange={(value) => setFilters(prev => ({ ...prev, link_type: value }))}>
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="全部" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="free">免费</SelectItem>
                          <SelectItem value="paid">付费</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                <div className="flex gap-2">
                  <Button onClick={handleSearch} disabled={loading}>
                    <Search className="h-4 w-4 mr-2" />
                    搜索
                  </Button>
                  <Button variant="outline" onClick={handleResetFilters}>
                    <Filter className="h-4 w-4 mr-2" />
                    重置
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Links Tab */}
        <TabsContent value="links" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>外链资源 ({adminData.pagination.total})</span>
                <div className="flex items-center gap-2">
                  {adminData.links.length > 0 && (
                    <Checkbox
                      checked={selectedItems.length === adminData.links.length}
                      onCheckedChange={handleSelectAll}
                    />
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {adminData.links.length === 0 && !loading ? (
                <div className="text-center py-8 text-gray-500">
                  暂无外链数据
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">选择</TableHead>
                        <TableHead>标题</TableHead>
                        <TableHead>URL</TableHead>
                        <TableHead>项目</TableHead>
                        <TableHead>用户</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>DR分数</TableHead>
                        <TableHead>流量</TableHead>
                        <TableHead>创建时间</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {adminData.links.map((link) => (
                        <TableRow key={link.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.includes(link.id)}
                              onCheckedChange={(checked) => handleSelectItem(link.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell className="font-medium max-w-[200px] truncate">
                            {link.title}
                          </TableCell>
                          <TableCell>
                            <a 
                              href={link.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 max-w-[200px] truncate inline-block"
                            >
                              {link.url}
                            </a>
                          </TableCell>
                          <TableCell>{link.project_name}</TableCell>
                          <TableCell>{link.user_email}</TableCell>
                          <TableCell>
                            <Badge variant={
                              link.status === 'active' ? 'default' :
                              link.status === 'pending' ? 'secondary' :
                              link.status === 'inactive' ? 'outline' : 'destructive'
                            }>
                              {link.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={link.link_type === 'paid' ? 'default' : 'secondary'}>
                              {link.link_type === 'paid' ? '付费' : '免费'}
                            </Badge>
                          </TableCell>
                          <TableCell>{link.dr_score || '-'}</TableCell>
                          <TableCell>{link.traffic.toLocaleString()}</TableCell>
                          <TableCell className="text-sm text-gray-500">
                            {new Date(link.created_at).toLocaleDateString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Pagination */}
              {adminData.pagination.total > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-500">
                    显示 {adminData.pagination.offset + 1} - {Math.min(adminData.pagination.offset + adminData.pagination.limit, adminData.pagination.total)} 
                    ，共 {adminData.pagination.total} 条
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 0 || loading}
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!adminData.pagination.hasMore || loading}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Projects Tab */}
        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>项目管理 ({adminData.pagination.total})</span>
                <div className="flex items-center gap-2">
                  {adminData.projects.length > 0 && (
                    <Checkbox
                      checked={selectedItems.length === adminData.projects.length}
                      onCheckedChange={handleSelectAll}
                    />
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {adminData.projects.length === 0 && !loading ? (
                <div className="text-center py-8 text-gray-500">
                  暂无项目数据
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">选择</TableHead>
                        <TableHead>项目名称</TableHead>
                        <TableHead>域名</TableHead>
                        <TableHead>用户</TableHead>
                        <TableHead>外链数量</TableHead>
                        <TableHead>总流量</TableHead>
                        <TableHead>已索引</TableHead>
                        <TableHead>DR分数</TableHead>
                        <TableHead>创建时间</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {adminData.projects.map((project) => (
                        <TableRow key={project.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.includes(project.id)}
                              onCheckedChange={(checked) => handleSelectItem(project.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{project.name}</TableCell>
                          <TableCell>
                            <a 
                              href={`https://${project.domain}`} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800"
                            >
                              {project.domain}
                            </a>
                          </TableCell>
                          <TableCell>{project.user_email}</TableCell>
                          <TableCell>{project.links_count || 0}</TableCell>
                          <TableCell>-</TableCell>
                          <TableCell>{project.indexed_links}</TableCell>
                          <TableCell>-</TableCell>
                          <TableCell className="text-sm text-gray-500">
                            {new Date(project.created_at).toLocaleDateString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Pagination */}
              {adminData.pagination.total > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-500">
                    显示 {adminData.pagination.offset + 1} - {Math.min(adminData.pagination.offset + adminData.pagination.limit, adminData.pagination.total)} 
                    ，共 {adminData.pagination.total} 条
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 0 || loading}
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!adminData.pagination.hasMore || loading}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>用户统计 ({adminData.users.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {adminData.users.length === 0 && !loading ? (
                <div className="text-center py-8 text-gray-500">
                  暂无用户数据
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>用户邮箱</TableHead>
                        <TableHead>项目数量</TableHead>
                        <TableHead>外链数量</TableHead>
                        <TableHead>总流量</TableHead>
                        <TableHead>最后活跃</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {adminData.users.map((user) => (
                        <TableRow key={user.user_id}>
                          <TableCell className="font-medium">{user.user_email}</TableCell>
                          <TableCell>{user.projects_count}</TableCell>
                          <TableCell>{user.links_count}</TableCell>
                          <TableCell>{user.total_traffic.toLocaleString()}</TableCell>
                          <TableCell className="text-sm text-gray-500">
                            {user.last_active ? new Date(user.last_active).toLocaleDateString() : '-'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {loading && (
        <Alert>
          <RefreshCw className="h-4 w-4 animate-spin" />
          <AlertDescription>正在加载数据...</AlertDescription>
        </Alert>
      )}
    </div>
  );
} 