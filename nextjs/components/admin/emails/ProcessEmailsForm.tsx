"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/components/ui/use-toast"
import { LoaderCircle, Send } from "lucide-react";
import { useTranslations } from "next-intl";

export default function ProcessEmailsForm() {
  const t = useTranslations("admin.emails.process");
  const router = useRouter();
  const { toast } = useToast();
  
  const [batchSize, setBatchSize] = useState(10);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<{ 
    processed: number; 
    success: number; 
    failed: number;
  } | null>(null);
  
  const handleProcess = async () => {
    setIsProcessing(true);
    setResult(null);
    
    try {
      const response = await fetch("/api/admin/email-logs/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ batchSize }),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setResult(data);
      
      if (data.processed > 0) {
        toast({
          title: t("process.success", { 
            processed: data.processed,
            success: data.success,
            failed: data.failed
          })
        });
      } else {
        toast({
          title: t("process.no_emails")
        });
      }
      
      // Refresh the page to update the pending count
      setTimeout(() => {
        router.refresh();
      }, 1500);
      
    } catch (error) {
      toast({
        title: t("process.error")
      });
      console.error("Error processing emails:", error);
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between">
          <Label htmlFor="batch-size">{t("form.batch_size")}</Label>
          <span className="text-sm text-muted-foreground">{batchSize}</span>
        </div>
        <Slider
          id="batch-size"
          min={1}
          max={50}
          step={1}
          value={[batchSize]}
          onValueChange={(value) => setBatchSize(value[0])}
          disabled={isProcessing}
        />
      </div>
      
      <Button 
        onClick={handleProcess} 
        disabled={isProcessing}
        className="w-full"
      >
        {isProcessing ? (
          <>
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            {t("form.processing")}
          </>
        ) : (
          <>
            <Send className="mr-2 h-4 w-4" />
            {t("form.process_button")}
          </>
        )}
      </Button>
      
      {result && (
        <Card className="p-4 mt-4">
          <p className="font-medium">{t("form.result_title")}</p>
          <ul className="mt-2 space-y-1 text-sm">
            <li className="flex justify-between">
              <span>{t("form.result_processed")}</span>
              <span className="font-mono">{result.processed}</span>
            </li>
            <li className="flex justify-between">
              <span>{t("form.result_success")}</span>
              <span className="font-mono text-green-600">{result.success}</span>
            </li>
            <li className="flex justify-between">
              <span>{t("form.result_failed")}</span>
              <span className="font-mono text-red-600">{result.failed}</span>
            </li>
          </ul>
        </Card>
      )}
    </div>
  );
} 