"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Save } from "lucide-react";
import { useTranslations } from "next-intl";
import { EmailTemplate } from "@/models/emailNotification";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TemplateEditFormProps {
  template?: EmailTemplate;
}

export default function TemplateEditForm({ template }: TemplateEditFormProps) {
  const t = useTranslations("admin.emails.templates.form");
  const router = useRouter();
  const { toast } = useToast();
  
  const isEditing = !!template;
  
  const [formData, setFormData] = useState({
    id: template?.id || undefined,
    name: template?.name || "",
    subject: template?.subject || "",
    body: template?.body || "",
    event_type: template?.event_type || "item_submitted",
    is_active: template?.is_active !== undefined ? template.is_active : true,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const url = isEditing 
        ? `/api/admin/email-templates/${formData.id}`
        : "/api/admin/email-templates";
      
      const method = isEditing ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      toast({
        title: isEditing 
          ? t("update_success") 
          : t("create_success")
      });
      
      // Navigate back to templates list
      router.push("/admin/emails/templates");
      router.refresh();
    } catch (error) {
      console.error("Error saving template:", error);
      toast({
        title: isEditing 
          ? t("update_error") 
          : t("create_error")
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="name">{t("name.label")}</Label>
            <Input 
              id="name"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder={t("name.placeholder")}
              required
              disabled={isSubmitting}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="event_type">{t("event_type.label")}</Label>
            <Select 
              value={formData.event_type} 
              onValueChange={(value) => handleChange("event_type", value)}
              disabled={isSubmitting || isEditing}
            >
              <SelectTrigger id="event_type">
                <SelectValue placeholder={t("event_type.placeholder")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="item_submitted">{t("event_type.item_submitted")}</SelectItem>
                <SelectItem value="item_published">{t("event_type.item_published")}</SelectItem>
                <SelectItem value="item_rejected">{t("event_type.item_rejected")}</SelectItem>
              </SelectContent>
            </Select>
            {isEditing && (
              <p className="text-sm text-muted-foreground mt-1">
                {t("event_type.cannot_change")}
              </p>
            )}
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="subject">{t("subject.label")}</Label>
          <Input 
            id="subject"
            value={formData.subject}
            onChange={(e) => handleChange("subject", e.target.value)}
            placeholder={t("subject.placeholder")}
            required
            disabled={isSubmitting}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="body">{t("body.label")}</Label>
          <Textarea 
            id="body"
            value={formData.body}
            onChange={(e) => handleChange("body", e.target.value)}
            placeholder={t("body.placeholder")}
            required
            rows={15}
            className="font-mono text-sm"
            disabled={isSubmitting}
          />
          <p className="text-sm text-muted-foreground">
            {t("body.help")}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            id="is_active"
            checked={formData.is_active}
            onCheckedChange={(checked) => handleChange("is_active", checked)}
            disabled={isSubmitting}
          />
          <Label htmlFor="is_active">{t("is_active.label")}</Label>
        </div>
      </div>
      
      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push("/admin/emails/templates")}
          disabled={isSubmitting}
        >
          {t("cancel_button")}
        </Button>
        
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("saving")}
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              {isEditing ? t("update_button") : t("create_button")}
            </>
          )}
        </Button>
      </div>
    </form>
  );
} 