'use client';

import { useState, useEffect } from 'react';
import { PublicLinkResourceWithStats } from '@/types/links';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

interface PublicLinkResourceFormProps {
  resource?: PublicLinkResourceWithStats | null;
  onSave: () => void;
  onCancel: () => void;
}

export function PublicLinkResourceForm({ resource, onSave, onCancel }: PublicLinkResourceFormProps) {
  const [formData, setFormData] = useState({
    title: '',
    website_url: '',
    submission_method: undefined as string | undefined,
    submission_url: '',
    contact_email: '',
    is_paid: false,
    price_range: '',
    currency: 'USD',
    category: undefined as string | undefined,
    description: '',
    requirements: '',
    response_time: undefined as string | undefined,
    success_rate: '',
    is_active: true,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    'directory',
    'blog',
    'news',
    'resource-page',
    'guest-post',
    'forum',
    'social-media',
    'press-release',
    'startup-directory',
    'tool-directory'
  ];

  const submissionMethods = [
    'Email',
    'Contact Form',
    'Submission Form',
    'Manual Review',
    'Direct Contact',
    'Partnership',
    'Guest Post Pitch',
    'Press Release',
    'Social Media',
    'Forum Post'
  ];

  const responseTimes = [
    '1-2 days',
    '3-5 days',
    '1 week',
    '2 weeks',
    '1 month',
    'Variable',
    'Unknown'
  ];

  useEffect(() => {
    if (resource) {
      setFormData({
        title: resource.title || '',
        website_url: resource.website_url || '',
        submission_method: resource.submission_method || undefined,
        submission_url: resource.submission_url || '',
        contact_email: resource.contact_email || '',
        is_paid: resource.is_paid || false,
        price_range: resource.price_range || '',
        currency: resource.currency || 'USD',
        category: resource.category || undefined,
        description: resource.description || '',
        requirements: resource.requirements || '',
        response_time: resource.response_time || undefined,
        success_rate: resource.success_rate?.toString() || '',
        is_active: resource.is_active !== undefined ? resource.is_active : true,
      });
    }
  }, [resource]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submitData = {
        ...formData,
        success_rate: formData.success_rate ? parseInt(formData.success_rate) : undefined,
      };

      const url = resource 
        ? `/api/admin/public-link-resources/${resource.id}`
        : '/api/admin/public-link-resources';
      
      const method = resource ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        toast.success(resource ? 'Resource updated successfully' : 'Resource created successfully');
        onSave();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to save resource');
      }
    } catch (error) {
      console.error('Failed to save resource:', error);
      toast.error('Failed to save resource');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="max-w-full">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="space-y-4 p-6 border-2 border-border rounded-lg bg-card/50">
          <h3 className="text-lg font-semibold text-foreground border-b-2 border-border pb-2">Basic Information</h3>
          
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium text-foreground">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
                className="form-enhanced"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="website_url" className="text-sm font-medium text-foreground">Website URL *</Label>
              <Input
                id="website_url"
                type="url"
                value={formData.website_url}
                onChange={(e) => handleInputChange('website_url', e.target.value)}
                required
                className="form-enhanced"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-foreground">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="form-enhanced"
            />
          </div>
        </div>

        {/* Submission Details */}
        <div className="space-y-4 p-6 border-2 border-border rounded-lg bg-card/50">
          <h3 className="text-lg font-semibold text-foreground border-b-2 border-border pb-2">Submission Details</h3>
          
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="submission_method" className="text-sm font-medium text-foreground">Submission Method *</Label>
              <Select value={formData.submission_method || undefined} onValueChange={(value) => handleInputChange('submission_method', value)}>
                <SelectTrigger className="form-enhanced">
                  <SelectValue placeholder="Select method" />
                </SelectTrigger>
                <SelectContent>
                  {submissionMethods.map(method => (
                    <SelectItem key={method} value={method}>{method}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="submission_url" className="text-sm font-medium text-foreground">Submission URL</Label>
              <Input
                id="submission_url"
                type="url"
                value={formData.submission_url}
                onChange={(e) => handleInputChange('submission_url', e.target.value)}
                className="form-enhanced"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="contact_email" className="text-sm font-medium text-foreground">Contact Email</Label>
              <Input
                id="contact_email"
                type="email"
                value={formData.contact_email}
                onChange={(e) => handleInputChange('contact_email', e.target.value)}
                className="form-enhanced"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="response_time" className="text-sm font-medium text-foreground">Response Time</Label>
              <Select value={formData.response_time || undefined} onValueChange={(value) => handleInputChange('response_time', value)}>
                <SelectTrigger className="form-enhanced">
                  <SelectValue placeholder="Select response time" />
                </SelectTrigger>
                <SelectContent>
                  {responseTimes.map(time => (
                    <SelectItem key={time} value={time}>{time}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="requirements" className="text-sm font-medium text-foreground">Requirements</Label>
            <Textarea
              id="requirements"
              value={formData.requirements}
              onChange={(e) => handleInputChange('requirements', e.target.value)}
              rows={3}
              placeholder="Domain requirements, content guidelines, etc."
              className="form-enhanced"
            />
          </div>
        </div>

        {/* Category and Pricing */}
        <div className="space-y-4 p-6 border-2 border-border rounded-lg bg-card/50">
          <h3 className="text-lg font-semibold text-foreground border-b-2 border-border pb-2">Category and Pricing</h3>
          
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="category" className="text-sm font-medium text-foreground">Category</Label>
              <Select value={formData.category || undefined} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger className="form-enhanced">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(cat => (
                    <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="success_rate" className="text-sm font-medium text-foreground">Success Rate (%)</Label>
              <Input
                id="success_rate"
                type="number"
                min="0"
                max="100"
                value={formData.success_rate}
                onChange={(e) => handleInputChange('success_rate', e.target.value)}
                className="form-enhanced"
              />
            </div>
          </div>

          <div className="flex items-center space-x-3 p-4 border-2 border-border rounded-md bg-muted/30">
            <Switch
              id="is_paid"
              checked={formData.is_paid}
              onCheckedChange={(checked) => handleInputChange('is_paid', checked)}
            />
            <Label htmlFor="is_paid" className="text-sm font-medium text-foreground cursor-pointer">Paid Resource</Label>
          </div>

          {formData.is_paid && (
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 p-4 border-2 border-border rounded-md bg-muted/20">
              <div className="space-y-2">
                <Label htmlFor="price_range" className="text-sm font-medium text-foreground">Price Range</Label>
                <Input
                  id="price_range"
                  value={formData.price_range}
                  onChange={(e) => handleInputChange('price_range', e.target.value)}
                  placeholder="e.g., $50-100, $200+"
                  className="form-enhanced"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="currency" className="text-sm font-medium text-foreground">Currency</Label>
                <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                  <SelectTrigger className="form-enhanced">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="AUD">AUD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>

        {/* Status */}
        <div className="space-y-4 p-6 border-2 border-border rounded-lg bg-card/50">
          <h3 className="text-lg font-semibold text-foreground border-b-2 border-border pb-2">Status</h3>
          
          <div className="flex items-center space-x-3 p-4 border-2 border-border rounded-md bg-muted/30">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => handleInputChange('is_active', checked)}
            />
            <Label htmlFor="is_active" className="text-sm font-medium text-foreground cursor-pointer">Active Resource</Label>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t-2 border-border">
          <Button type="button" variant="outline" onClick={onCancel} className="px-6">
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting} className="px-6">
            {isSubmitting ? 'Saving...' : resource ? 'Update Resource' : 'Create Resource'}
          </Button>
        </div>
      </form>
    </div>
  );
}