'use client';

import { useState, useEffect } from 'react';
import { PublicLinkResourceWithStats } from '@/types/links';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Plus, Edit, Trash2, ExternalLink, Search } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { PublicLinkResourceForm } from './public-link-resource-form';
import { toast } from 'sonner';

export function PublicLinkResourcesManagement() {
  const [resources, setResources] = useState<PublicLinkResourceWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResource, setSelectedResource] = useState<PublicLinkResourceWithStats | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    fetchResources();
  }, []);

  const fetchResources = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/public-link-resources');
      if (response.ok) {
        const data = await response.json();
        setResources(data);
      } else {
        toast.error('Failed to fetch resources');
      }
    } catch (error) {
      console.error('Failed to fetch resources:', error);
      toast.error('Failed to fetch resources');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this resource?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/public-link-resources/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Resource deleted successfully');
        fetchResources();
      } else {
        toast.error('Failed to delete resource');
      }
    } catch (error) {
      console.error('Failed to delete resource:', error);
      toast.error('Failed to delete resource');
    }
  };

  const handleSave = async () => {
    setIsDialogOpen(false);
    setSelectedResource(null);
    fetchResources();
  };

  const filteredResources = resources.filter(
    resource =>
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getDRColor = (dr?: number) => {
    if (!dr) return 'text-muted-foreground';
    if (dr >= 80) return 'text-green-600';
    if (dr >= 60) return 'text-blue-600';
    if (dr >= 40) return 'text-yellow-600';
    if (dr >= 20) return 'text-orange-600';
    return 'text-red-600';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="animate-pulse">Loading resources...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search resources..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedResource(null)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Resource
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedResource ? 'Edit Resource' : 'Add New Resource'}
              </DialogTitle>
            </DialogHeader>
            <PublicLinkResourceForm
              resource={selectedResource}
              onSave={handleSave}
              onCancel={() => setIsDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{resources.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Active Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resources.filter(r => r.is_active).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Free Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resources.filter(r => !r.is_paid).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Paid Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resources.filter(r => r.is_paid).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resources Table */}
      <Card>
        <CardHeader>
          <CardTitle>Resources ({filteredResources.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Domain</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>DR</TableHead>
                  <TableHead>Traffic</TableHead>
                  <TableHead>Success Rate</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResources.map((resource) => (
                  <TableRow key={resource.id}>
                    <TableCell>
                      <div className="font-medium">{resource.title}</div>
                      {resource.description && (
                        <div className="text-sm text-muted-foreground truncate max-w-48">
                          {resource.description}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <a
                        href={resource.website_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center hover:text-primary"
                      >
                        {resource.domain}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </TableCell>
                    <TableCell>
                      {resource.category && (
                        <Badge variant="outline">{resource.category}</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={resource.is_paid ? 'default' : 'secondary'}>
                        {resource.is_paid ? 'Paid' : 'Free'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getDRColor(resource.dr_score)}>
                        {resource.dr_score || 'N/A'}
                      </span>
                    </TableCell>
                    <TableCell>
                      {resource.traffic > 0 ? formatNumber(resource.traffic) : 'N/A'}
                    </TableCell>
                    <TableCell>
                      {resource.success_rate ? `${resource.success_rate}%` : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={resource.is_active ? 'default' : 'secondary'}>
                        {resource.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(resource.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedResource(resource);
                            setIsDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(resource.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {filteredResources.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No resources found.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}