"use client";

import { useState } from "react";
import { ItemTool, Tool } from "@/types/ItemTools";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Save, Trash2, AlertTriangle } from "lucide-react";
import { useTranslations } from "next-intl";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ItemToolEditProps {
  itemTools: ItemTool;
  isCreating?: boolean;
}

export function ItemToolEdit({ itemTools, isCreating = false }: ItemToolEditProps) {
  const t = useTranslations("admin.mcps.tools");
  
  const [tools, setTools] = useState<Tool[]>(itemTools.tools || []);
  const [type, setType] = useState<"sse" | "stdio" | "both">(itemTools.type);
  const [allowPublic, setAllowPublic] = useState<boolean>(itemTools.allow_public);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const addTool = () => {
    const newTool: Tool = {
      name: "",
      description: "",
      parameters: [],
    };
    setTools([...tools, newTool]);
  };
  
  const removeTool = (index: number) => {
    const newTools = [...tools];
    newTools.splice(index, 1);
    setTools(newTools);
  };
  
  const updateTool = (index: number, field: keyof Tool, value: any) => {
    const newTools = [...tools];
    newTools[index] = { ...newTools[index], [field]: value };
    setTools(newTools);
  };
  
  const addParameter = (toolIndex: number) => {
    const newTools = [...tools];
    if (!newTools[toolIndex].parameters) {
      newTools[toolIndex].parameters = [];
    }
    newTools[toolIndex].parameters!.push({
      name: "",
      description: "",
      type: "string",
      required: false,
    });
    setTools(newTools);
  };
  
  const removeParameter = (toolIndex: number, paramIndex: number) => {
    const newTools = [...tools];
    newTools[toolIndex].parameters!.splice(paramIndex, 1);
    setTools(newTools);
  };
  
  const updateParameter = (toolIndex: number, paramIndex: number, field: string, value: any) => {
    const newTools = [...tools];
    newTools[toolIndex].parameters![paramIndex] = {
      ...newTools[toolIndex].parameters![paramIndex],
      [field]: field === "required" ? (value === "true" || value === true) : value,
    };
    setTools(newTools);
  };
  
  const saveItemTools = async () => {
    // Validate the data
    for (let i = 0; i < tools.length; i++) {
      if (!tools[i].name) {
        setError(t("validation.tool_name_required"));
        return;
      }
      if (!tools[i].description) {
        setError(t("validation.tool_description_required"));
        return;
      }
      
      if (tools[i].parameters) {
        for (let j = 0; j < tools[i].parameters.length; j++) {
          const param = tools[i].parameters[j];
          if (!param.name) {
            setError(t("validation.param_name_required", { toolName: tools[i].name }));
            return;
          }
          if (!param.type) {
            setError(t("validation.param_type_required", { paramName: param.name, toolName: tools[i].name }));
            return;
          }
        }
      }
    }
    
    setError(null);
    setIsLoading(true);
    
    try {
      const response = await fetch("/api/admin/mcps/tools/update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          uuid: itemTools.uuid,
          type,
          tools,
          allow_public: allowPublic,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save Item tools");
      }
      
      toast.success(t("success.saved"));
    } catch (err: any) {
      setError(err.message || t("error.save_failed"));
      toast.error(t("error.save_failed"));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-8">
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{t("error.title")}</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="type">{t("fields.type")}</Label>
            <Select value={type} onValueChange={(value: "sse" | "stdio" | "both") => setType(value)}>
              <SelectTrigger id="type">
                <SelectValue placeholder={t("placeholders.select_type")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sse">SSE (Server-Sent Events)</SelectItem>
                <SelectItem value="stdio">STDIO (Standard Input/Output)</SelectItem>
                <SelectItem value="both">Both SSE and STDIO</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label>{t("fields.allow_public")}</Label>
            <div className="flex items-center space-x-2">
              <Switch 
                id="allowPublic" 
                checked={allowPublic} 
                onCheckedChange={setAllowPublic} 
              />
              <Label htmlFor="allowPublic" className="cursor-pointer">
                {allowPublic ? t("fields.public") : t("fields.private")}
              </Label>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">{t("sections.tools")}</h3>
          <Button onClick={addTool} variant="outline" size="sm" className="flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            {t("actions.add_tool")}
          </Button>
        </div>
        
        {tools.length === 0 ? (
          <div className="text-center py-8 border rounded-md">
            <p className="text-muted-foreground">{t("empty.no_tools")}</p>
          </div>
        ) : (
          <div className="space-y-6">
            {tools.map((tool, toolIndex) => (
              <Card key={toolIndex}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>
                      {tool.name || t("placeholders.unnamed_tool")}
                    </CardTitle>
                    <Button 
                      onClick={() => removeTool(toolIndex)} 
                      variant="ghost" 
                      size="sm"
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor={`tool-${toolIndex}-name`}>
                          {t("fields.tool_name")}
                        </Label>
                        <Input 
                          id={`tool-${toolIndex}-name`}
                          value={tool.name} 
                          onChange={(e) => updateTool(toolIndex, "name", e.target.value)}
                          placeholder={t("placeholders.tool_name")}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`tool-${toolIndex}-updated_at`}>
                          {t("fields.updated_at")}
                        </Label>
                        <Input 
                          id={`tool-${toolIndex}-updated_at`}
                          value={tool.updated_at || new Date().toISOString()}
                          onChange={(e) => updateTool(toolIndex, "updated_at", e.target.value)}
                          placeholder="YYYY-MM-DD"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor={`tool-${toolIndex}-description`}>
                        {t("fields.description")} <span className="text-xs text-muted-foreground">({t("fields.default_language")})</span>
                      </Label>
                      <Textarea 
                        id={`tool-${toolIndex}-description`}
                        value={tool.description} 
                        onChange={(e) => updateTool(toolIndex, "description", e.target.value)}
                        placeholder={t("placeholders.tool_description")}
                        className="min-h-[100px]"
                      />
                    </div>
                    
                    <div className="border-t pt-4 mt-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium">{t("sections.parameters")}</h4>
                        <Button 
                          onClick={() => addParameter(toolIndex)} 
                          variant="outline" 
                          size="sm"
                          className="flex items-center"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          {t("actions.add_parameter")}
                        </Button>
                      </div>
                      
                      {!tool.parameters || tool.parameters.length === 0 ? (
                        <p className="text-sm text-muted-foreground">{t("empty.no_parameters")}</p>
                      ) : (
                        <div className="space-y-4">
                          {tool.parameters.map((param, paramIndex) => (
                            <div 
                              key={paramIndex} 
                              className="border rounded-md p-4 relative"
                            >
                              <Button
                                onClick={() => removeParameter(toolIndex, paramIndex)}
                                variant="ghost"
                                size="sm"
                                className="absolute top-2 right-2 text-destructive"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              
                              <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                  <Label htmlFor={`tool-${toolIndex}-param-${paramIndex}-name`}>
                                    {t("fields.param_name")}
                                  </Label>
                                  <Input 
                                    id={`tool-${toolIndex}-param-${paramIndex}-name`}
                                    value={param.name} 
                                    onChange={(e) => updateParameter(toolIndex, paramIndex, "name", e.target.value)}
                                    placeholder={t("placeholders.param_name")}
                                  />
                                </div>
                                
                                <div className="space-y-2">
                                  <Label htmlFor={`tool-${toolIndex}-param-${paramIndex}-type`}>
                                    {t("fields.param_type")}
                                  </Label>
                                  <Select 
                                    value={param.type}
                                    onValueChange={(value) => updateParameter(toolIndex, paramIndex, "type", value)}
                                  >
                                    <SelectTrigger id={`tool-${toolIndex}-param-${paramIndex}-type`}>
                                      <SelectValue placeholder={t("placeholders.select_type")} />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="string">string</SelectItem>
                                      <SelectItem value="number">number</SelectItem>
                                      <SelectItem value="boolean">boolean</SelectItem>
                                      <SelectItem value="object">object</SelectItem>
                                      <SelectItem value="array">array</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                              
                              <div className="space-y-2 mt-4">
                                <Label htmlFor={`tool-${toolIndex}-param-${paramIndex}-description`}>
                                  {t("fields.param_description")}
                                </Label>
                                <Textarea
                                  id={`tool-${toolIndex}-param-${paramIndex}-description`}
                                  value={param.description || ""}
                                  onChange={(e) => updateParameter(toolIndex, paramIndex, "description", e.target.value)}
                                  placeholder={t("placeholders.param_description")}
                                />
                              </div>
                              
                              <div className="mt-4">
                                <div className="flex items-center space-x-2">
                                  <Switch 
                                    id={`tool-${toolIndex}-param-${paramIndex}-required`}
                                    checked={param.required}
                                    onCheckedChange={(value) => updateParameter(toolIndex, paramIndex, "required", value)}
                                  />
                                  <Label htmlFor={`tool-${toolIndex}-param-${paramIndex}-required`}>
                                    {t("fields.required")}
                                  </Label>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
      
      <div className="flex justify-end">
        <Button onClick={saveItemTools} disabled={isLoading} className="flex items-center">
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? t("actions.saving") : t("actions.save")}
        </Button>
      </div>
    </div>
  );
} 