"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Delete } from "lucide-react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

interface DeleteItemToolActionProps {
  uuid: string;
}

export function DeleteItemToolAction({ uuid }: DeleteItemToolActionProps) {
  const t = useTranslations("admin.mcps.tools");
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!confirm(t("edit.confirm_delete"))) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/admin/mcps/tools/delete`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ uuid }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete Item tools");
      }

      toast.success(t("success.deleted"));
      router.push("/admin/mcps/tools");
    } catch (err: any) {
      toast.error(t("error.delete_failed"));
      console.error("Error deleting Item tools:", err);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Button 
      variant="destructive" 
      onClick={handleDelete} 
      disabled={isDeleting}
      className="flex items-center"
    >
      <Delete className="h-4 w-4 mr-2" />
      {isDeleting ? t("actions.deleting") : t("actions.delete")}
    </Button>
  );
} 