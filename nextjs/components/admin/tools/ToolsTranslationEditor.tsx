"use client";

import { useState } from "react";
import { ItemTool, Tool, I18nContent } from "@/types/ItemTools";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Globe, Save, Trash2, AlertTriangle, Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface ToolsTranslationEditorProps {
  itemTools: ItemTool;
}

// Supported languages
const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "zh", name: "中文 (Chinese)" },
  // Add more languages as needed
];

export function ToolsTranslationEditor({ itemTools }: ToolsTranslationEditorProps) {
  const t = useTranslations("admin.mcps.tools");
  
  const [tools, setTools] = useState<Tool[]>(itemTools.tools || []);
  const [activeLanguage, setActiveLanguage] = useState<string>("zh");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Update a tool's i18n description
  const updateToolI18nDescription = (toolIndex: number, locale: string, value: string) => {
    const newTools = [...tools];
    const tool = newTools[toolIndex];
    
    if (!tool.i18n_description) {
      tool.i18n_description = {};
    }
    
    tool.i18n_description[locale] = value;
    setTools(newTools);
  };
  
  // Update a parameter's i18n description
  const updateParamI18nDescription = (toolIndex: number, paramIndex: number, locale: string, value: string) => {
    const newTools = [...tools];
    const param = newTools[toolIndex].parameters?.[paramIndex];
    
    if (!param) return;
    
    if (!param.i18n_description) {
      param.i18n_description = {};
    }
    
    param.i18n_description[locale] = value;
    setTools(newTools);
  };
  
  const saveTranslations = async () => {
    setError(null);
    setIsLoading(true);
    
    try {
      const response = await fetch("/api/admin/mcps/tools/update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          uuid: itemTools.uuid,
          tools: tools,
          type: itemTools.type,
          allow_public: itemTools.allow_public,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save translations");
      }
      
      toast.success(t("success.translations_saved"));
    } catch (err: any) {
      setError(err.message || t("error.save_failed"));
      toast.error(t("error.save_failed"));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-8">
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{t("error.title")}</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Globe className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-medium">{t("translations.edit_title")}</h3>
          </div>
          
          <Select value={activeLanguage} onValueChange={setActiveLanguage}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("translations.select_language")} />
            </SelectTrigger>
            <SelectContent>
              {LANGUAGES.map((lang) => (
                <SelectItem key={lang.code} value={lang.code}>
                  {lang.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <Alert variant="default" className="bg-slate-50 dark:bg-slate-900">
          <Globe className="h-4 w-4" />
          <AlertTitle>{t("translations.current_language")}</AlertTitle>
          <AlertDescription>
            {t("translations.editing_language", { 
              language: LANGUAGES.find(l => l.code === activeLanguage)?.name || activeLanguage
            })}
          </AlertDescription>
        </Alert>
      </div>
      
      {tools.length === 0 ? (
        <div className="text-center py-8 border rounded-md">
          <p className="text-muted-foreground">{t("empty.no_tools_to_translate")}</p>
        </div>
      ) : (
        <Accordion type="multiple" className="space-y-4">
          {tools.map((tool, toolIndex) => (
            <AccordionItem 
              key={toolIndex} 
              value={`tool-${toolIndex}`}
              className="border rounded-lg overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-2 hover:no-underline">
                <div className="flex items-center space-x-2 text-left">
                  <span>{tool.name || t("placeholders.unnamed_tool")}</span>
                  <Badge variant="outline" className="ml-2">
                    {tool.i18n_description && tool.i18n_description[activeLanguage] 
                      ? t("translations.translated") 
                      : t("translations.not_translated")}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor={`tool-${toolIndex}-desc-${activeLanguage}`}>
                        {t("translations.tool_description")}
                      </Label>
                      
                      <div className="text-xs text-muted-foreground">
                        {t("translations.original")}: <span className="font-medium">{tool.description.substring(0, 30)}...</span>
                      </div>
                    </div>
                    
                    <Textarea 
                      id={`tool-${toolIndex}-desc-${activeLanguage}`}
                      value={tool.i18n_description?.[activeLanguage] || ""}
                      onChange={(e) => updateToolI18nDescription(toolIndex, activeLanguage, e.target.value)}
                      placeholder={t("placeholders.translation")}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  {tool.parameters && tool.parameters.length > 0 && (
                    <div className="space-y-4 pt-4 border-t">
                      <h4 className="font-medium">{t("translations.parameters")}</h4>
                      
                      {tool.parameters.map((param, paramIndex) => (
                        <Card key={paramIndex} className="overflow-hidden">
                          <CardHeader className="bg-slate-50 dark:bg-slate-900 py-3">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-sm font-medium">{param.name}</CardTitle>
                              <Badge variant="outline">
                                {param.i18n_description && param.i18n_description[activeLanguage] 
                                  ? t("translations.translated") 
                                  : t("translations.not_translated")}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Label htmlFor={`param-${toolIndex}-${paramIndex}-desc-${activeLanguage}`}>
                                  {t("translations.parameter_description")}
                                </Label>
                                
                                <div className="text-xs text-muted-foreground">
                                  {t("translations.original")}: <span className="font-medium">{param.description?.substring(0, 30) || t("translations.no_description")}</span>
                                </div>
                              </div>
                              
                              <Textarea 
                                id={`param-${toolIndex}-${paramIndex}-desc-${activeLanguage}`}
                                value={param.i18n_description?.[activeLanguage] || ""}
                                onChange={(e) => updateParamI18nDescription(toolIndex, paramIndex, activeLanguage, e.target.value)}
                                placeholder={t("placeholders.parameter_translation")}
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
      
      <div className="flex justify-end">
        <Button onClick={saveTranslations} disabled={isLoading} className="flex items-center">
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? t("actions.saving") : t("actions.save_translations")}
        </Button>
      </div>
    </div>
  );
} 