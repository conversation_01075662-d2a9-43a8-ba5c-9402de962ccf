"use client";

import Header from "@/components/dashboard/header";
import TableBlock from "@/components/blocks/table";
import { Table as TableSlotType } from "@/types/slots/table";
import Toolbar from "@/components/blocks/toolbar";
import { useState, useCallback } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

export default function ({ ...table }: TableSlotType) {
  // 使用本地状态来处理按钮点击事件
  const [, setClickTrigger] = useState(0);
  
  const handleButtonClick = useCallback((onClick?: () => void) => {
    if (onClick) {
      onClick();
    }
    // 触发重新渲染
    setClickTrigger(prev => prev + 1);
  }, []);

  // Pagination handler
  const handlePageChange = async (page: number) => {
    if (table.pagination?.onPageChange) {
      await table.pagination.onPageChange(page);
    }
  };

  // Render pagination buttons
  const renderPaginationButtons = () => {
    if (!table.pagination) return null;
    
    const { currentPage, totalPages } = table.pagination;
    if (totalPages <= 1) return null;
    
    const buttons = [];
    
    // Previous button
    buttons.push(
      <button
        key="prev"
        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className={cn(
          "flex items-center justify-center h-10 w-10 rounded-md",
          currentPage === 1 
            ? "pointer-events-none opacity-50" 
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        )}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </button>
    );
    
    // Calculate which page buttons to show
    const pagesToShow = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pagesToShow.push(i);
      }
    } else {
      // Always show first page
      pagesToShow.push(1);
      
      // Calculate middle pages
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if at edges
      if (currentPage <= 2) {
        endPage = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        startPage = Math.max(2, totalPages - 3);
      }
      
      // Add ellipsis before middle pages if needed
      if (startPage > 2) {
        pagesToShow.push(-1); // -1 represents ellipsis
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pagesToShow.push(i);
      }
      
      // Add ellipsis after middle pages if needed
      if (endPage < totalPages - 1) {
        pagesToShow.push(-2); // -2 represents ellipsis (different key from first ellipsis)
      }
      
      // Always show last page
      if (totalPages > 1) {
        pagesToShow.push(totalPages);
      }
    }
    
    // Page number buttons
    pagesToShow.forEach(page => {
      if (page < 0) {
        // Ellipsis
        buttons.push(
          <span key={`ellipsis${page}`} className="px-2 text-muted-foreground">
            …
          </span>
        );
      } else {
        buttons.push(
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={cn(
              "flex items-center justify-center h-10 w-10 text-sm rounded-md",
              currentPage === page
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            )}
            aria-label={`Page ${page}`}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </button>
        );
      }
    });
    
    // Next button
    buttons.push(
      <button
        key="next"
        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className={cn(
          "flex items-center justify-center h-10 w-10 rounded-md",
          currentPage === totalPages 
            ? "pointer-events-none opacity-50" 
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        )}
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </button>
    );
    
    return buttons;
  };

  return (
    <>
      <Header crumb={table.crumb} />
      <div className="w-full px-4 md:px-8 py-8">
        <h1 className="text-2xl font-medium mb-8">{table.title}</h1>
        {table.description && (
          <p className="text-sm text-muted-foreground mb-8">
            {table.description}
          </p>
        )}
        {table.tip && (
          <p className="text-sm text-muted-foreground mb-8">
            {table.tip.description || table.tip.title}
          </p>
        )}
        {table.toolbar && <Toolbar items={table.toolbar.items} />}
        
        {/* Filter Groups */}
        {table.filters && table.filters.length > 0 && (
          <div className="space-y-4 mb-8">
            {table.filters.map((filterGroup, groupIndex) => (
              <div key={groupIndex} className="space-y-2">
                {filterGroup.title && (
                  <h3 className="text-sm font-medium text-gray-500">{filterGroup.title}:</h3>
                )}
                <div className="flex flex-wrap gap-2">
                  {filterGroup.items.map((item, itemIndex) => {
                    // Check if item has formAction (for server action)
                    if (item.formAction) {
                      return (
                        <form key={itemIndex} action={item.formAction}>
                          {/* Add hidden inputs for formData */}
                          {item.formData && Object.entries(item.formData).map(([key, value]) => (
                            <input 
                              key={key} 
                              type="hidden" 
                              name={key} 
                              value={value !== null && value !== undefined ? String(value) : ''} 
                            />
                          ))}
                          <button
                            type="submit"
                            className={item.className}
                          >
                            {item.title}
                          </button>
                        </form>
                      );
                    }
                    
                    // Regular button for onClick handlers
                    return (
                      <button
                        key={itemIndex}
                        className={item.className}
                        onClick={() => handleButtonClick(item.onClick)}
                      >
                        {item.title}
                      </button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="overflow-x-auto">
          <TableBlock columns={table.columns} data={table.data} />
        </div>

        {/* Pagination */}
        {table.pagination && table.pagination.totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <nav
              className="flex items-center space-x-1"
              aria-label="Pagination"
            >
              {renderPaginationButtons()}
            </nav>
          </div>
        )}
      </div>
    </>
  );
}
