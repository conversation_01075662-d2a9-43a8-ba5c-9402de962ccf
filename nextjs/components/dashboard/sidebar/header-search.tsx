"use client";

import { useState, useEffect } from "react";
import { Search as SearchIcon, X, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import { searchItems } from "@/services/items";

type SearchResult = {
  uuid: string;
  item_uuid: string;
  name: string;
  brief: string;
};

export default function HeaderSearch() {
  const [searchTerm, setSearchTerm] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const router = useRouter();
  const { locale } = useParams() || { locale: 'en' };

  // Debounced search effect
  useEffect(() => {
    if (!searchTerm || searchTerm.length < 2) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const debounceTimeout = setTimeout(async () => {
      setIsLoading(true);
      try {
        const response = await searchItems(searchTerm, locale as string);
        setResults(response.data?.slice(0, 5) || []); // Limit to 5 results in dropdown
        setShowResults(true);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
        setShowResults(false);
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm, locale]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchTerm.trim() === "") {
      return;
    }
    
    // Navigate to search results page with locale
    router.push(`/${locale}/search?q=${encodeURIComponent(searchTerm)}`);
    setShowResults(false);
    setIsFocused(false);
  };

  const handleReset = () => {
    setSearchTerm("");
    setResults([]);
    setShowResults(false);
  };

  const handleFocus = () => {
    setIsFocused(true);
    if (searchTerm.length >= 2 && results.length > 0) {
      setShowResults(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding results to allow clicking on them
    setTimeout(() => {
      setShowResults(false);
    }, 200);
  };

  const handleResultClick = () => {
    setShowResults(false);
    setIsFocused(false);
    setSearchTerm("");
  };

  return (
    <div className="relative w-full">
      <form onSubmit={handleSubmit} className="relative">
        <div className="absolute left-2 top-1/2 -translate-y-1/2 p-1 text-muted-foreground">
          <SearchIcon className="w-4 h-4" />
        </div>
        <input 
          className={`w-full rounded-md px-8 py-2 text-sm border bg-background text-foreground border-input focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary placeholder:text-muted-foreground transition-all duration-200 ${
            isFocused ? 'shadow-sm' : ''
          }`}
          placeholder="Search..."
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          name="q"
        />
        {isLoading && (
          <div className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-muted-foreground">
            <Loader2 className="w-4 h-4 animate-spin" />
          </div>
        )}
        {!isLoading && searchTerm && (
          <button 
            type="button" 
            onClick={handleReset} 
            className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}

        {/* Dropdown search results */}
        {showResults && results.length > 0 && (
          <div className="absolute w-full mt-1 bg-popover border border-border rounded-md shadow-lg z-50 overflow-hidden">
            {results.map((result) => (
              <Link
                key={result.uuid || result.item_uuid}
                href={`/${locale}/g/${result.item_uuid}`}
                className="block px-3 py-2 hover:bg-accent cursor-pointer transition-colors"
                onClick={handleResultClick}
              >
                <div className="font-medium text-foreground text-sm truncate">{result.name}</div>
                <div className="text-xs text-muted-foreground line-clamp-1 mt-0.5">{result.brief}</div>
              </Link>
            ))}
            
            {/* Show all results link */}
            <div className="bg-muted/30 px-3 py-2 text-center border-t">
              <button 
                type="submit"
                className="text-xs text-primary hover:underline"
              >
                See all results →
              </button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}