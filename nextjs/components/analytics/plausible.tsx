import Script from 'next/script'

export function PlausibleAnalytics() {
  const domain = process.env.NEXT_PUBLIC_PLAUSIBLE_DOMAIN;
  const scriptUrl = process.env.NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL;
  const plausibleEnabled = process.env.PLAUSIBLE_ENABLED;
  
  if (!domain || !scriptUrl || !plausibleEnabled || plausibleEnabled === 'false') {
    return null;
  }

  if (process.env.NODE_ENV !== 'production') {
    return null;
  }
  
  return (
    <Script
      src={scriptUrl}
      data-domain={domain}
      strategy="lazyOnload"
    />
  );
}

export default PlausibleAnalytics; 