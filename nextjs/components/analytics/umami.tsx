'use client';

import Script from 'next/script'
import { usePathname } from 'next/navigation'

export function UmamiAnalytics() {
  const websiteId = process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID;
  const scriptUrl = process.env.NEXT_PUBLIC_UMAMI_SCRIPT_URL;
  const umamiEnabled = process.env.UMAMI_ENABLED;
  const pathname = usePathname();
  
  if (!websiteId || !scriptUrl || !umamiEnabled || umamiEnabled === 'false') {
    return null;
  }

  if (process.env.NODE_ENV !== 'production') {
    return null;
  }

  if (pathname && pathname.includes('/admin')) {
    return null;
  }
  
  return (
    <Script
      src={scriptUrl}
      data-website-id={websiteId}
      strategy="lazyOnload"
    />
  );
}

export default UmamiAnalytics; 