"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BookmarkIcon, CheckIcon } from "lucide-react";
import { addUserItem, getUserItemByItemUuid, deleteUserItem } from "@/models/userItems";
import { toast } from "sonner";

interface FavoriteButtonProps {
  itemUuid: string;
  user_uuid: string;
}

export function FavoriteButton({ itemUuid, user_uuid }: FavoriteButtonProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userItemUuid, setuserItemUuid] = useState("");

  useEffect(() => {
    const checkFavoriteStatus = async () => {
      try {
        setIsLoading(true);
        const { data, error } = await getUserItemByItemUuid(user_uuid, itemUuid);
        
        if (data) {
          setIsFavorite(true);
          setuserItemUuid(data.uuid);
        } else {
          setIsFavorite(false);
        }
      } catch (error) {
        console.error("Error checking favorite status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkFavoriteStatus();
  }, [itemUuid]);

  const handleToggleFavorite = async () => {
    try {
      setIsLoading(true);

      if (isFavorite) {
        // Remove from favorites
        const { error } = await deleteUserItem(user_uuid, userItemUuid);
        
        if (error) {
          toast.error("Failed to remove from favorites");
          console.error("Error removing favorite:", error);
          return;
        }
        
        setIsFavorite(false);
        toast.success("Removed from favorites");
      } else {
        // Add to favorites
        const { data, error } = await addUserItem(user_uuid, itemUuid);
        
        if (error) {
          toast.error("Failed to add to favorites");
          console.error("Error adding favorite:", error);
          return;
        }
        
        if (data && data[0]) {
          setuserItemUuid(data[0].uuid);
        }
        
        setIsFavorite(true);
        toast.success("Added to favorites");
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast.error("An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={isFavorite ? "default" : "outline"}
      size="sm"
      onClick={handleToggleFavorite}
      disabled={isLoading}
      className={`gap-1 ${isFavorite ? "bg-blue-600 hover:bg-blue-700" : ""}`}
    >
      {isFavorite ? (
        <>
          <CheckIcon className="h-4 w-4" />
          <span>Saved</span>
        </>
      ) : (
        <>
          <BookmarkIcon className="h-4 w-4" />
          <span>Save Item</span>
        </>
      )}
    </Button>
  );
} 