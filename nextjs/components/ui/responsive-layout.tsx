import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export function ResponsiveLayout({
  children,
  className,
  maxWidth = 'none',
  padding = 'md',
  spacing = 'md'
}: ResponsiveLayoutProps) {
  const maxWidthClasses = {
    none: 'max-w-none',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl'
  };

  const paddingClasses = {
    none: '',
    sm: 'p-2 sm:p-3',
    md: 'p-3 sm:p-4 lg:p-4 xl:p-6',
    lg: 'p-4 sm:p-6 lg:p-6 xl:p-8',
    xl: 'p-6 sm:p-8 lg:p-8 xl:p-10'
  };

  const spacingClasses = {
    none: '',
    sm: 'space-y-2 lg:space-y-3',
    md: 'space-y-4 lg:space-y-6',
    lg: 'space-y-6 lg:space-y-8',
    xl: 'space-y-8 lg:space-y-10'
  };

  return (
    <div
      className={cn(
        'w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        spacingClasses[spacing],
        className
      )}
    >
      {children}
    </div>
  );
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 2, lg: 3, xl: 4 },
  gap = 'md'
}: ResponsiveGridProps) {
  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  const gridColsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6'
  };

  const responsiveClasses = [
    gridColsClasses[cols.default],
    cols.sm && `sm:${gridColsClasses[cols.sm]}`,
    cols.md && `md:${gridColsClasses[cols.md]}`,
    cols.lg && `lg:${gridColsClasses[cols.lg]}`,
    cols.xl && `xl:${gridColsClasses[cols.xl]}`,
    cols['2xl'] && `2xl:${gridColsClasses[cols['2xl']]}`
  ].filter(Boolean);

  return (
    <div
      className={cn(
        'grid',
        gapClasses[gap],
        ...responsiveClasses,
        className
      )}
    >
      {children}
    </div>
  );
}

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  fluid?: boolean;
  centerContent?: boolean;
}

export function ResponsiveContainer({
  children,
  className,
  fluid = false,
  centerContent = false
}: ResponsiveContainerProps) {
  return (
    <div
      className={cn(
        'w-full',
        fluid ? 'max-w-none px-4 lg:px-6' : 'container mx-auto',
        centerContent && 'flex items-center justify-center',
        className
      )}
    >
      {children}
    </div>
  );
}

interface ResponsiveStackProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'column' | 'row';
  breakpoint?: 'sm' | 'md' | 'lg' | 'xl';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
}

export function ResponsiveStack({
  children,
  className,
  direction = 'column',
  breakpoint = 'sm',
  spacing = 'md',
  align = 'start',
  justify = 'start'
}: ResponsiveStackProps) {
  const spacingClasses = {
    none: '',
    sm: direction === 'column' ? 'space-y-2' : 'space-x-2',
    md: direction === 'column' ? 'space-y-4' : 'space-x-4',
    lg: direction === 'column' ? 'space-y-6' : 'space-x-6',
    xl: direction === 'column' ? 'space-y-8' : 'space-x-8'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  };

  return (
    <div
      className={cn(
        'flex',
        direction === 'column' ? 'flex-col' : 'flex-row',
        `${breakpoint}:flex-${direction === 'column' ? 'row' : 'col'}`,
        spacingClasses[spacing],
        alignClasses[align],
        justifyClasses[justify],
        className
      )}
    >
      {children}
    </div>
  );
} 