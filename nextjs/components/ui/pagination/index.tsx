'use client';

import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export interface PaginationProps {
  currentPage: number;
  pageSize: number;
  count: number | null;
  baseUrl: string;
  className?: string;
}

export function Pagination({
  currentPage,
  pageSize,
  count,
  baseUrl,
  className,
}: PaginationProps) {
  if (!count) return null;
  
  const totalPages = Math.ceil(count / pageSize);
  if (totalPages <= 1) return null;
  
  // Function to generate page URL with query parameters
  const getPageUrl = (page: number) => {
    const url = new URL(baseUrl, 'http://dummy.com');
    const searchParams = new URLSearchParams(url.search);
    searchParams.set('page', page.toString());
    return `${url.pathname}?${searchParams.toString()}`;
  };
  
  // Create pagination buttons
  const renderPaginationButtons = () => {
    const buttons = [];
    
    // Previous button
    buttons.push(
      <Link
        key="prev"
        href={currentPage > 1 ? getPageUrl(currentPage - 1) : '#'}
        aria-disabled={currentPage === 1}
        className={cn(
          "flex items-center justify-center h-10 w-10 rounded-md",
          currentPage === 1 
            ? "pointer-events-none opacity-50" 
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        )}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </Link>
    );
    
    // Calculate which page buttons to show
    const pagesToShow = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pagesToShow.push(i);
      }
    } else {
      // Always show first page
      pagesToShow.push(1);
      
      // Calculate middle pages
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if at edges
      if (currentPage <= 2) {
        endPage = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        startPage = Math.max(2, totalPages - 3);
      }
      
      // Add ellipsis before middle pages if needed
      if (startPage > 2) {
        pagesToShow.push(-1); // -1 represents ellipsis
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pagesToShow.push(i);
      }
      
      // Add ellipsis after middle pages if needed
      if (endPage < totalPages - 1) {
        pagesToShow.push(-2); // -2 represents ellipsis (different key from first ellipsis)
      }
      
      // Always show last page
      if (totalPages > 1) {
        pagesToShow.push(totalPages);
      }
    }
    
    // Page number buttons
    pagesToShow.forEach(page => {
      if (page < 0) {
        // Ellipsis
        buttons.push(
          <span key={`ellipsis${page}`} className="px-2 text-muted-foreground">
            …
          </span>
        );
      } else {
        buttons.push(
          <Link
            key={page}
            href={getPageUrl(page)}
            className={cn(
              "flex items-center justify-center h-10 w-10 text-sm rounded-md",
              currentPage === page
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            )}
            aria-label={`Page ${page}`}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </Link>
        );
      }
    });
    
    // Next button
    buttons.push(
      <Link
        key="next"
        href={currentPage < totalPages ? getPageUrl(currentPage + 1) : '#'}
        aria-disabled={currentPage === totalPages}
        className={cn(
          "flex items-center justify-center h-10 w-10 rounded-md",
          currentPage === totalPages 
            ? "pointer-events-none opacity-50" 
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        )}
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </Link>
    );
    
    return buttons;
  };

  return (
    <nav 
      className={cn("flex items-center justify-center space-x-1", className)} 
      aria-label="Pagination"
    >
      {renderPaginationButtons()}
    </nav>
  );
} 