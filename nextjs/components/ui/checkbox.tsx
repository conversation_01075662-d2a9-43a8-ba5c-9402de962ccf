"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {
    indeterminate?: boolean
  }
>(({ className, indeterminate, ...props }, ref) => {
  // Create a separate mutable ref
  const internalRef = React.useRef<HTMLButtonElement | null>(null)
  
  // Function to handle both refs
  const handleRef = React.useCallback((node: HTMLButtonElement | null) => {
    // Handle the forwarded ref
    if (typeof ref === "function") ref(node)
    else if (ref) ref.current = node
    
    // Handle our internal ref
    internalRef.current = node
  }, [ref])
  
  React.useEffect(() => {
    if (internalRef.current && indeterminate !== undefined) {
      internalRef.current.dataset.indeterminate = indeterminate.toString()
      internalRef.current.ariaChecked = indeterminate ? "mixed" : props.checked ? "true" : "false"
    }
  }, [indeterminate, props.checked])

  return (
    <CheckboxPrimitive.Root
      ref={handleRef}
      className={cn(
        "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[indeterminate=true]:bg-primary data-[indeterminate=true]:text-primary-foreground",
        className
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        className={cn("flex items-center justify-center text-current")}
      >
        {indeterminate ? (
          <div className="h-2 w-2 bg-current" />
        ) : (
          <Check className="h-4 w-4" />
        )}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )
})
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox } 