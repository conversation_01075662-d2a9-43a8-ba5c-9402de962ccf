"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Check, Crown, Users, Globe, Link, TrendingUp, BarChart3, CreditCard, Mail, ArrowDown } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface TierInfo {
  tier: 'free' | 'paid';
  subscription_status: string;
  limits: {
    projects: { used: number; limit: number; canCreate: boolean };
    domains: { used: number; limit: number; canAdd: boolean };
    link_resources: { used: number; limit: number; canAdd: boolean };
    monthly_dr_queries: { used: number; limit: number; canUse: boolean };
    monthly_traffic_updates: { used: number; limit: number; canUse: boolean };
  };
  usage_reset_date: string;
}

interface Translations {
  current_plan: string;
  usage_summary: string;
  upgrade_plan: string;
  manage_billing: string;
  features: string;
  limits: string;
  projects: string;
  domains: string;
  link_resources: string;
  dr_queries: string;
  traffic_updates: string;
  free_tier: string;
  professional_tier: string;
  unlimited: string;
  per_month: string;
  upgrade_now: string;
  contact_support: string;
}

interface SubscriptionManagementProps {
  tierInfo: TierInfo | null;
  translations: Translations;
}

export default function SubscriptionManagement({ tierInfo, translations }: SubscriptionManagementProps) {
  const [isScrolling, setIsScrolling] = useState(false);

  const handleScrollToPricing = async () => {
    try {
      setIsScrolling(true);
      
      // 滚动到pricing部分（如果在同一页面）或跳转到首页
      const pricingElement = document.getElementById('pricing');
      if (pricingElement) {
        pricingElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      } else {
        // 如果在当前页面找不到pricing部分，跳转到首页
        window.location.href = "/#pricing";
      }
      
    } catch (error) {
      console.error("Scroll/redirect error:", error);
      toast.error("Failed to navigate to pricing");
    } finally {
      // 延迟重置状态，给滚动动画时间
      setTimeout(() => setIsScrolling(false), 1000);
    }
  };

  const handleManageBilling = () => {
    // This would integrate with Stripe customer portal
    toast.info("Billing management coming soon");
  };

  const formatUsage = (used: number, limit: number) => {
    if (limit === -1) return `${used} / ${translations.unlimited}`;
    return `${used} / ${limit}`;
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0;
    return Math.min((used / limit) * 100, 100);
  };

  if (!tierInfo) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Unable to load subscription information</p>
      </div>
    );
  }

  const isPaid = tierInfo.tier === 'paid';

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {isPaid ? <Crown className="h-5 w-5 text-yellow-500" /> : <Users className="h-5 w-5" />}
                {translations.current_plan}
              </CardTitle>
              <CardDescription>
                {isPaid ? translations.professional_tier : translations.free_tier}
              </CardDescription>
            </div>
            <Badge variant={isPaid ? "default" : "secondary"}>
              {isPaid ? "Professional" : "Free"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold">
                {isPaid ? "$29" : "$0"}
                {isPaid && <span className="text-sm text-muted-foreground ml-1">{translations.per_month}</span>}
              </p>
              {isPaid && (
                <p className="text-sm text-muted-foreground">
                  Status: {tierInfo.subscription_status}
                </p>
              )}
            </div>
            <div className="flex gap-2">
              {!isPaid && (
                <Button onClick={handleScrollToPricing} disabled={isScrolling}>
                  <ArrowDown className="h-4 w-4 mr-2" />
                  {isScrolling ? "Scrolling..." : translations.upgrade_now}
                </Button>
              )}
              {isPaid && (
                <Button variant="outline" onClick={handleManageBilling}>
                  <CreditCard className="h-4 w-4 mr-2" />
                  {translations.manage_billing}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Summary */}
      <Card>
        <CardHeader>
          <CardTitle>{translations.usage_summary}</CardTitle>
          <CardDescription>
            Your current usage and limits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Projects */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">{translations.projects}</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {formatUsage(tierInfo.limits.projects.used, tierInfo.limits.projects.limit)}
              </span>
            </div>
            <Progress 
              value={getUsagePercentage(tierInfo.limits.projects.used, tierInfo.limits.projects.limit)}
              className="h-2"
            />
          </div>

          {/* Domains */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <span className="font-medium">{translations.domains}</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {formatUsage(tierInfo.limits.domains.used, tierInfo.limits.domains.limit)}
              </span>
            </div>
            <Progress 
              value={getUsagePercentage(tierInfo.limits.domains.used, tierInfo.limits.domains.limit)}
              className="h-2"
            />
          </div>

          {/* Link Resources */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Link className="h-4 w-4" />
                <span className="font-medium">{translations.link_resources}</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {formatUsage(tierInfo.limits.link_resources.used, tierInfo.limits.link_resources.limit)}
              </span>
            </div>
            <Progress 
              value={getUsagePercentage(tierInfo.limits.link_resources.used, tierInfo.limits.link_resources.limit)}
              className="h-2"
            />
          </div>

          <Separator />

          {/* Premium Features (for paid users) */}
          {isPaid && (
            <>
              {/* DR Queries */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    <span className="font-medium">{translations.dr_queries}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {formatUsage(tierInfo.limits.monthly_dr_queries.used, tierInfo.limits.monthly_dr_queries.limit)}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(tierInfo.limits.monthly_dr_queries.used, tierInfo.limits.monthly_dr_queries.limit)}
                  className="h-2"
                />
              </div>

              {/* Traffic Updates */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    <span className="font-medium">{translations.traffic_updates}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {formatUsage(tierInfo.limits.monthly_traffic_updates.used, tierInfo.limits.monthly_traffic_updates.limit)}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(tierInfo.limits.monthly_traffic_updates.used, tierInfo.limits.monthly_traffic_updates.limit)}
                  className="h-2"
                />
              </div>

              <p className="text-sm text-muted-foreground">
                Usage resets monthly on {new Date(tierInfo.usage_reset_date).toLocaleDateString()}
              </p>
            </>
          )}
        </CardContent>
      </Card>

      {/* Quick upgrade hint for free users */}
      {/* {!isPaid && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Ready to unlock more features?</h3>
              <p className="text-muted-foreground mb-4">
                Upgrade to Professional and get unlimited projects, advanced analytics, and priority support.
              </p>
              
              <Button 
                onClick={handleScrollToPricing} 
                disabled={isScrolling}
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <ArrowDown className="h-4 w-4 mr-2" />
                {isScrolling ? "Scrolling..." : "View Pricing Plans Below"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )} */}

      {/* Support */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Need Help?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Have questions about your subscription or need assistance? Our support team is here to help.
          </p>
          <Button variant="outline">
            {translations.contact_support}
          </Button>
        </CardContent>
      </Card> */}
    </div>
  );
}