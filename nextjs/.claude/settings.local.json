{"permissions": {"allow": ["Bash(pnpm type-check:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(grep:*)", "Bash(npx tsc:*)", "Bash(git commit:*)", "Bash(pnpm lint:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rg:*)", "Bash(pnpm dev:*)", "Bash(pnpm install:*)", "Bash(pnpm build:*)", "<PERSON><PERSON>(cat:*)", "Bash(timeout 30s pnpm build 2>&1)", "<PERSON><PERSON>(mv:*)", "Bash(npx eslint:*)", "WebFetch(domain:mybacklinks.app)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "Bash(pnpm run:*)", "Bash(ls:*)", "Bash(pnpm cf:env:*)"], "deny": []}}