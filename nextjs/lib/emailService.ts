import { Resend } from 'resend';
import { 
  createEmailLog, 
  getEmailTemplateByEventType,
  updateEmailLogStatus, 
  getPendingEmails
} from '@/models/emailNotification';
import { Submission, Items } from '@/types/items';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Template variable patterns
const TEMPLATE_VARIABLES = {
  Item_NAME: /{{Item_NAME}}/g,
  Item_AUTHOR: /{{Item_AUTHOR}}/g,
  Item_URL: /{{Item_URL}}/g,
  Item_LINK: /{{Item_LINK}}/g,
  RECIPIENT_NAME: /{{RECIPIENT_NAME}}/g,
  RECIPIENT_EMAIL: /{{RECIPIENT_EMAIL}}/g,
  SITE_URL: /{{SITE_URL}}/g,
  DATE: /{{DATE}}/g,
};

// Function to replace template variables with actual data
function processTemplate(template: string, variables: Record<string, string>): string {
  let processed = template;
  
  Object.entries(variables).forEach(([key, value]) => {
    const pattern = TEMPLATE_VARIABLES[key as keyof typeof TEMPLATE_VARIABLES];
    if (pattern) {
      processed = processed.replace(pattern, value);
    }
  });
  
  return processed;
}

// Function to send email when an Item is submitted
export async function sendItemSubmittedEmail(
  recipientEmail: string, 
  itemData: Partial<Submission>
): Promise<{ success: boolean; error?: any }> {
  if (process.env.NODE_ENV === 'development') {
    return { success: true };
  }

  try {
    // Get email template for Item submission event
    const { data: template, error: templateError } = await getEmailTemplateByEventType('item_submitted');
    
    if (templateError || !template || !template.is_active) {
      console.error('Email template not found or inactive for item_submitted event:', templateError);
      return { success: false, error: templateError || 'Template not found or inactive' };
    }
    
    // Process variables for the template
    const siteUrl = process.env.NEXT_PUBLIC_WEB_URL;
    const variables = {
      Item_NAME: itemData.name || '',
      Item_AUTHOR: itemData.author_name || '',
      Item_URL: itemData.website_url || '',
      RECIPIENT_EMAIL: recipientEmail,
      RECIPIENT_NAME: itemData.author_name || '',
      SITE_URL: siteUrl,
      DATE: new Date().toLocaleDateString(),
    };
    
    // Process the subject and body
    const subject = processTemplate(template.subject, variables);
    const body = processTemplate(template.body, variables);
    
    // Log the email before sending
    const { data: emailLog, error: logError } = await createEmailLog(
      template.id,
      recipientEmail,
      subject,
      body,
      'item_submitted',
      { itemData }
    );
    
    if (logError || !emailLog) {
      console.error('Failed to log email before sending:', logError);
      return { success: false, error: logError || 'Failed to log email' };
    }
    
    // Send the email
    const { data, error } = await resend.emails.send({
      from: process.env.RESEND_EMAIL_FROM,
      to: recipientEmail,
      subject: subject,
      html: body,
    });
    
    if (error) {
      console.error('Failed to send item_submitted email:', error);
      await updateEmailLogStatus(emailLog.id, 'failed', error.message);
      return { success: false, error };
    }
    
    // Update the email log status to sent
    await updateEmailLogStatus(emailLog.id, 'sent');
    
    return { success: true };
  } catch (error) {
    console.error('Error sending item_submitted email:', error);
    return { success: false, error };
  }
}

// Function to send email when an Item is published (set to public)
export async function sendItemPublishedEmail(
  recipientEmail: string, 
  itemData: Partial<Items>
): Promise<{ success: boolean; error?: any }> {
  if (process.env.NODE_ENV === 'development') {
    return { success: true };
  }

  try {
    // Get email template for Item publication event
    const { data: template, error: templateError } = await getEmailTemplateByEventType('item_published');
    
    if (templateError || !template || !template.is_active) {
      console.error('Email template not found or inactive for item_published event:', templateError);
      return { success: false, error: templateError || 'Template not found or inactive' };
    }
    
    // Process variables for the template
    const siteUrl = process.env.NEXT_PUBLIC_WEB_URL;
    const itemUrl = `${siteUrl}/g/${itemData.uuid}`;
    const variables = {
      Item_NAME: itemData.name || '',
      Item_AUTHOR: itemData.author_name || '',
      Item_URL: itemData.website_url || '',
      Item_LINK: itemUrl,
      RECIPIENT_EMAIL: recipientEmail,
      RECIPIENT_NAME: itemData.author_name || '',
      SITE_URL: siteUrl,
      DATE: new Date().toLocaleDateString(),
    };
    
    // Process the subject and body
    const subject = processTemplate(template.subject, variables);
    const body = processTemplate(template.body, variables);
    
    // Log the email before sending
    const { data: emailLog, error: logError } = await createEmailLog(
      template.id,
      recipientEmail,
      subject,
      body,
      'item_published',
      { itemData }
    );
    
    if (logError || !emailLog) {
      console.error('Failed to log email before sending:', logError);
      return { success: false, error: logError || 'Failed to log email' };
    }
    
    // Send the email
    const { data, error } = await resend.emails.send({
      from: process.env.RESEND_EMAIL_FROM,
      to: recipientEmail,
      subject: subject,
      html: body,
    });
    
    if (error) {
      console.error('Failed to send item_published email:', error);
      await updateEmailLogStatus(emailLog.id, 'failed', error.message);
      return { success: false, error };
    }
    
    // Update the email log status to sent
    await updateEmailLogStatus(emailLog.id, 'sent');
    
    return { success: true };
  } catch (error) {
    console.error('Error sending item_published email:', error);
    return { success: false, error };
  }
}

// Function to process pending emails from the queue
export async function processPendingEmails(batchSize: number = 10): Promise<{
  processed: number;
  success: number;
  failed: number;
}> {
  try {
    const { data: pendingEmails, error } = await getPendingEmails(batchSize);
    
    if (error || !pendingEmails || pendingEmails.length === 0) {
      return { processed: 0, success: 0, failed: 0 };
    }
    
    let success = 0;
    let failed = 0;
    
    for (const email of pendingEmails) {
      try {
        // Send the email
        const { data, error } = await resend.emails.send({
          from: process.env.RESEND_EMAIL_FROM,
          to: email.recipient,
          subject: email.subject,
          html: email.body,
        });
        
        if (error) {
          console.error(`Failed to send email ID ${email.id}:`, error);
          await updateEmailLogStatus(email.id, 'failed', error.message);
          failed++;
        } else {
          await updateEmailLogStatus(email.id, 'sent');
          success++;
        }
      } catch (sendError: any) {
        console.error(`Error processing email ID ${email.id}:`, sendError);
        await updateEmailLogStatus(email.id, 'failed', sendError.message || String(sendError));
        failed++;
      }
    }
    
    return {
      processed: pendingEmails.length,
      success,
      failed,
    };
  } catch (error) {
    console.error('Error processing pending emails:', error);
    return { processed: 0, success: 0, failed: 0 };
  }
} 