/**
 * Custom fetch implementation with timeout and retry logic for NextAuth OAuth connections
 */

interface FetchWithTimeoutOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  maxTimeout?: number;
}

interface OAuthEndpointConfig {
  baseTimeout: number;
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
}

// OAuth endpoint configurations with specific timeout settings
const OAUTH_ENDPOINTS: Record<string, OAuthEndpointConfig> = {
  'accounts.google.com': {
    baseTimeout: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 2000, // 2 seconds
    exponentialBackoff: true,
  },
  'googleapis.com': {
    baseTimeout: 25000, // 25 seconds
    maxRetries: 3,
    retryDelay: 1500,
    exponentialBackoff: true,
  },
  'github.com': {
    baseTimeout: 20000, // 20 seconds
    maxRetries: 2,
    retryDelay: 1000,
    exponentialBackoff: true,
  },
  'oauth': { // Generic OAuth endpoints
    baseTimeout: 30000,
    maxRetries: 3,
    retryDelay: 2000,
    exponentialBackoff: true,
  }
};

/**
 * Detect if URL is an OAuth endpoint and return appropriate configuration
 */
function getOAuthConfig(url: string): OAuthEndpointConfig | null {
  for (const [endpoint, config] of Object.entries(OAUTH_ENDPOINTS)) {
    if (url.includes(endpoint)) {
      return config;
    }
  }
  return null;
}

/**
 * Enhanced fetch with OAuth-specific timeout and retry handling
 */
export async function fetchWithTimeout(
  url: string, 
  options: FetchWithTimeoutOptions = {}
): Promise<Response> {
  const oauthConfig = getOAuthConfig(url);
  const isOAuthRequest = !!oauthConfig;
  
  const { 
    timeout = oauthConfig?.baseTimeout || 30000,
    retries = oauthConfig?.maxRetries || 3,
    retryDelay = oauthConfig?.retryDelay || 1000,
    exponentialBackoff = oauthConfig?.exponentialBackoff || false,
    maxTimeout = 60000, // Maximum timeout of 60 seconds
    ...fetchOptions 
  } = options;

  let currentTimeout = timeout;
  let currentDelay = retryDelay;

  if (isOAuthRequest) {
    console.log(`🔐 OAuth request detected for ${url}:`, {
      baseTimeout: timeout,
      maxRetries: retries,
      exponentialBackoff,
      endpoint: Object.keys(OAUTH_ENDPOINTS).find(key => url.includes(key))
    });
  }

  for (let attempt = 0; attempt <= retries; attempt++) {
    let timeoutId: NodeJS.Timeout | undefined;
    const attemptStartTime = Date.now();
    
    try {
      const controller = new AbortController();
      timeoutId = setTimeout(() => {
        console.warn(`⏰ Request timeout after ${currentTimeout}ms for ${url} (attempt ${attempt + 1})`);
        controller.abort();
      }, currentTimeout);

      if (isOAuthRequest) {
        console.log(`🔄 OAuth attempt ${attempt + 1}/${retries + 1} for ${url} (timeout: ${currentTimeout}ms)`);
      }

      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal,
      });

      if (timeoutId) clearTimeout(timeoutId);
      
      const duration = Date.now() - attemptStartTime;
      
      if (response.ok) {
        if (isOAuthRequest) {
          console.log(`✅ OAuth request successful for ${url} in ${duration}ms`);
        }
        return response;
      }
      
      if (attempt === retries) {
        console.error(`❌ OAuth request failed after ${retries + 1} attempts for ${url}: HTTP ${response.status}`);
        return response;
      }
      
      console.warn(`⚠️ OAuth attempt ${attempt + 1} failed for ${url} (HTTP ${response.status}), retrying in ${currentDelay}ms...`);
      
    } catch (error) {
      if (timeoutId) clearTimeout(timeoutId);
      
      const duration = Date.now() - attemptStartTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isTimeoutError = errorMessage.includes('timeout') || errorMessage.includes('aborted');
      
      if (attempt === retries) {
        console.error(`❌ All OAuth attempts failed for ${url} after ${retries + 1} tries:`, {
          error: errorMessage,
          totalDuration: duration,
          isTimeout: isTimeoutError,
          finalTimeout: currentTimeout
        });
        throw error;
      }
      
      console.warn(`⚠️ OAuth attempt ${attempt + 1} failed for ${url}:`, {
        error: errorMessage,
        duration,
        isTimeout: isTimeoutError,
        nextRetryIn: currentDelay,
        nextTimeout: exponentialBackoff ? Math.min(currentTimeout * 1.5, maxTimeout) : currentTimeout
      });
      
      await new Promise(resolve => setTimeout(resolve, currentDelay));
    }
    
    // Apply exponential backoff for next attempt
    if (exponentialBackoff && attempt < retries) {
      currentTimeout = Math.min(currentTimeout * 1.5, maxTimeout);
      currentDelay = Math.min(currentDelay * 2, 10000); // Max 10 second delay
    }
  }
  
  throw new Error(`Failed to fetch ${url} after ${retries + 1} attempts`);
}

/**
 * Override global fetch for NextAuth OAuth requests with enhanced timeout handling
 */
export function setupFetchOverride() {
  if (typeof global !== 'undefined' && !global.__fetchOverrideSetup) {
    const originalFetch = global.fetch;
    
    global.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      
      // Check if this is an OAuth request that needs enhanced handling
      const oauthConfig = getOAuthConfig(url);
      if (oauthConfig) {
        console.log(`🔧 Using enhanced OAuth fetch for: ${url}`);
        return fetchWithTimeout(url, {
          ...init,
          // Use OAuth-specific configuration with exponential backoff
          exponentialBackoff: true,
        });
      }
      
      // Use original fetch for non-OAuth requests
      return originalFetch(input, init);
    };
    
    global.__fetchOverrideSetup = true;
    console.log('🔧 Enhanced OAuth fetch override setup complete');
    console.log('📋 OAuth endpoints configured:', Object.keys(OAUTH_ENDPOINTS));
  }
}