/**
 * Safely accesses headers with error handling for Next.js 14
 * 
 * @param headerAccessor Function that accesses headers
 * @returns Result of the header access operation
 */
export async function safeHeadersAccess<T>(headerAccessor: () => T): Promise<T> {
  try {
    // In Next.js 14, headers() is synchronous
    return headerAccessor();
  } catch (error) {
    const err = error as Error;
    console.error('Headers access error:', err.message);
    throw err;
  }
}

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  jitter: boolean;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
}

export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffFactor: 2,
  jitter: true,
  retryCondition: (error) => {
    // Default: retry on network errors and 5xx status codes
    return error.name === 'NetworkError' ||
      error.message.includes('5') ||
      error.message.includes('timeout') ||
      error.message.includes('ECONNRESET') ||
      error.message.includes('ENOTFOUND');
  }
};

export class RetryableError extends Error {
  constructor(message: string, public shouldRetry: boolean = true) {
    super(message);
    this.name = 'RetryableError';
  }
}

export class NonRetryableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NonRetryableError';
  }
}

// Calculate delay with exponential backoff and optional jitter
function calculateDelay(
  attempt: number,
  baseDelay: number,
  maxDelay: number,
  backoffFactor: number,
  jitter: boolean
): number {
  const exponentialDelay = Math.min(
    baseDelay * Math.pow(backoffFactor, attempt - 1),
    maxDelay
  );

  if (jitter) {
    // Add random jitter to prevent thundering herd
    const jitterAmount = exponentialDelay * 0.1; // 10% jitter
    return exponentialDelay + (Math.random() - 0.5) * 2 * jitterAmount;
  }

  return exponentialDelay;
}

// Generic retry function
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: Error;

  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      const result = await operation();
      return result;
    } catch (error) {
      lastError = error as Error;

      // Don't retry on non-retryable errors
      if (error instanceof NonRetryableError) {
        throw error;
      }

      // Check if we should retry this error
      const shouldRetry = error instanceof RetryableError ?
        error.shouldRetry :
        config.retryCondition!(lastError);

      // Don't retry if we've exhausted attempts or shouldn't retry
      if (attempt > config.maxRetries || !shouldRetry) {
        throw lastError;
      }

      // Call retry callback if provided
      if (config.onRetry) {
        config.onRetry(lastError, attempt);
      }

      // Calculate delay and wait
      const delay = calculateDelay(
        attempt,
        config.baseDelay,
        config.maxDelay,
        config.backoffFactor,
        config.jitter
      );

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

// Specialized retry function for database operations
export async function retryDatabaseOperation<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const dbOptions: Partial<RetryOptions> = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    jitter: true,
    retryCondition: (error) => {
      const errorMessage = error.message.toLowerCase();
      return errorMessage.includes('connection') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('network') ||
        errorMessage.includes('econnreset') ||
        errorMessage.includes('enotfound') ||
        errorMessage.includes('503') ||
        errorMessage.includes('502') ||
        errorMessage.includes('504');
    },
    onRetry: (error, attempt) => {
      console.warn(`Database operation failed, retrying (${attempt}/3):`, error.message);
    },
    ...options
  };

  return retryWithBackoff(operation, dbOptions);
}

// Specialized retry function for API calls
export async function retryApiCall<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const apiOptions: Partial<RetryOptions> = {
    maxRetries: 3,
    baseDelay: 2000,
    maxDelay: 20000,
    backoffFactor: 1.5,
    jitter: true,
    retryCondition: (error) => {
      const errorMessage = error.message.toLowerCase();
      return errorMessage.includes('network') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('502') ||
        errorMessage.includes('503') ||
        errorMessage.includes('504') ||
        errorMessage.includes('429'); // Rate limit
    },
    onRetry: (error, attempt) => {
      console.warn(`API call failed, retrying (${attempt}/3):`, error.message);
    },
    ...options
  };

  return retryWithBackoff(operation, apiOptions);
}

// Retry decorator for functions
export function retryable<T extends (...args: any[]) => Promise<any>>(
  options: Partial<RetryOptions> = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      return retryWithBackoff(
        () => originalMethod.apply(this, args),
        options
      );
    };

    return descriptor;
  };
}

// Helper function for HTTP fetch with retry
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryOptions: Partial<RetryOptions> = {}
): Promise<Response> {
  return retryApiCall(async () => {
    const response = await fetch(url, options);

    // Throw error for non-2xx status codes
    if (!response.ok) {
      const error = new Error(`HTTP ${response.status}: ${response.statusText}`);

      // Don't retry 4xx errors (except 429)
      if (response.status >= 400 && response.status < 500 && response.status !== 429) {
        throw new NonRetryableError(error.message);
      }

      throw new RetryableError(error.message);
    }

    return response;
  }, retryOptions);
}

// Batch retry function for multiple operations
export async function retryBatch<T>(
  operations: (() => Promise<T>)[],
  options: Partial<RetryOptions> = {}
): Promise<T[]> {
  const results = await Promise.allSettled(
    operations.map(op => retryWithBackoff(op, options))
  );

  const failures = results.filter(r => r.status === 'rejected') as PromiseRejectedResult[];

  if (failures.length > 0) {
    const errorMessage = `Batch operation failed: ${failures.length}/${operations.length} operations failed`;
    console.error(errorMessage, failures.map(f => f.reason));
    throw new Error(errorMessage);
  }

  return results.map(r => (r as PromiseFulfilledResult<T>).value);
}

// Circuit breaker pattern for preventing cascading failures
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 5,
    private resetTimeout: number = 60000, // 1 minute
    private monitoringPeriod: number = 60000 // 1 minute
  ) { }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new NonRetryableError('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    };
  }
}

// Pre-configured circuit breakers for common operations
export const databaseCircuitBreaker = new CircuitBreaker(5, 60000, 60000);
export const apiCircuitBreaker = new CircuitBreaker(10, 30000, 60000);