import { 
  Globe, 
  Gamepad2, 
  Wrench, 
  BookOpen, 
  ShoppingCart, 
  Camera, 
  Music, 
  Briefcase,
  Heart,
  Palette,
  GraduationCap,
  Utensils,
  Home,
  Car,
  Plane,
  Code,
  TrendingUp,
  Users,
  MessageSquare,
  Shield,
  type LucideIcon
} from "lucide-react";

// 预定义的分类配置（用于图标和颜色映射）
export interface CategoryConfig {
  icon: LucideIcon;
  color: string;
  bgColor: string;
  description: string;
}

// 常见分类的图标和颜色映射
export const categoryConfigs: Record<string, CategoryConfig> = {
  // 技术类
  '工具': {
    icon: Wrench,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    description: '实用工具和软件'
  },
  
  // 娱乐类
  '游戏': {
    icon: Gamepad2,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    description: '游戏相关项目'
  }
};

// 获取分类配置
export function getCategoryConfig(category: string | undefined | null): CategoryConfig {
  if (!category) {
    return {
      icon: Globe,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      description: '未分类项目'
    };
  }

  // 先尝试精确匹配
  if (categoryConfigs[category]) {
    return categoryConfigs[category];
  }

  // 模糊匹配 - 检查是否包含关键词
  const lowerCategory = category.toLowerCase();
  for (const [key, config] of Object.entries(categoryConfigs)) {
    if (lowerCategory.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerCategory)) {
      return config;
    }
  }

  // 英文关键词匹配
  const englishMappings: Record<string, string> = {
    'game': '游戏',
    'gaming': '游戏',
    'tool': '工具',
    'tools': '工具',
    'blog': '博客',
    'news': '新闻',
    'ecommerce': '电商',
    'shop': '电商',
    'business': '商业',
    'health': '健康',
    'food': '美食',
    'travel': '旅行',
    'art': '艺术',
    'music': '音乐',
    'photo': '摄影',
    'education': '教育',
    'tech': '技术',
    'code': '开发',
    'dev': '开发',
    'social': '社交',
    'forum': '论坛',
    'security': '安全'
  };

  for (const [english, chinese] of Object.entries(englishMappings)) {
    if (lowerCategory.includes(english) && categoryConfigs[chinese]) {
      return categoryConfigs[chinese];
    }
  }

  // 默认配置
  return {
    icon: Globe,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    description: `${category} 相关项目`
  };
}

// 获取所有预定义分类
export function getPredefinedCategories(): string[] {
  return Object.keys(categoryConfigs).sort();
}

// 分类颜色变体（用于UI展示）
export const categoryColorVariants = [
  'bg-blue-100 text-blue-600',
  'bg-green-100 text-green-600',
  'bg-purple-100 text-purple-600',
  'bg-orange-100 text-orange-600',
  'bg-pink-100 text-pink-600',
  'bg-indigo-100 text-indigo-600',
  'bg-red-100 text-red-600',
  'bg-yellow-100 text-yellow-600',
  'bg-emerald-100 text-emerald-600',
  'bg-teal-100 text-teal-600',
  'bg-cyan-100 text-cyan-600',
  'bg-violet-100 text-violet-600',
  'bg-slate-100 text-slate-600',
  'bg-gray-100 text-gray-600',
];

// 根据分类名称生成一致的颜色
export function getCategoryColorVariant(category: string): string {
  if (!category) return categoryColorVariants[0];
  
  // 使用简单的哈希函数为分类名称生成一致的颜色索引
  let hash = 0;
  for (let i = 0; i < category.length; i++) {
    const char = category.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  const index = Math.abs(hash) % categoryColorVariants.length;
  return categoryColorVariants[index];
} 