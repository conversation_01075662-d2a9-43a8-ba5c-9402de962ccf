import { z } from 'zod';

// Tier action enum schema
export const TierActionSchema = z.enum([
  'create_project',
  'add_domain', 
  'add_link_resource',
  'dr_query',
  'traffic_update'
]);

// User tier schema
export const UserTierSchema = z.enum(['free', 'paid']);

// Subscription status schema
export const SubscriptionStatusSchema = z.enum([
  'active',
  'inactive',
  'canceled',
  'expired',
  'trial'
]);

// Tier middleware options schema
export const TierMiddlewareOptionsSchema = z.object({
  action: TierActionSchema,
  recordUsage: z.boolean().optional().default(false),
  projectId: z.string().uuid().optional(),
  apiEndpoint: z.string().url().optional(),
  metadata: z.record(z.string(), z.any()).optional()
});

// Tier validation result schema
export const TierValidationResultSchema = z.object({
  allowed: z.boolean(),
  message: z.string().optional(),
  currentUsage: z.number().int().min(0).optional(),
  limit: z.number().int().min(-1).optional() // -1 for unlimited
});

// Tier info schema
export const TierInfoSchema = z.object({
  tier: UserTierSchema,
  subscription_status: SubscriptionStatusSchema,
  limits: z.object({
    projects: z.object({
      used: z.number().int().min(0),
      limit: z.number().int().min(-1),
      canCreate: z.boolean()
    }),
    domains: z.object({
      used: z.number().int().min(0),
      limit: z.number().int().min(-1),
      canAdd: z.boolean()
    }),
    link_resources: z.object({
      used: z.number().int().min(0),
      limit: z.number().int().min(-1),
      canAdd: z.boolean()
    }),
    monthly_dr_queries: z.object({
      used: z.number().int().min(0),
      limit: z.number().int().min(-1),
      canUse: z.boolean()
    }),
    monthly_traffic_updates: z.object({
      used: z.number().int().min(0),
      limit: z.number().int().min(-1),
      canUse: z.boolean()
    })
  }),
  usage_reset_date: z.string().datetime()
});

// User usage summary schema
export const UserUsageSummarySchema = z.object({
  tier: UserTierSchema,
  subscription_status: SubscriptionStatusSchema,
  projects_count: z.number().int().min(0),
  projects_limit: z.number().int().min(-1),
  domains_count: z.number().int().min(0),
  domains_limit: z.number().int().min(-1),
  link_resources_count: z.number().int().min(0),
  link_resources_limit: z.number().int().min(-1),
  monthly_dr_queries_used: z.number().int().min(0),
  monthly_dr_queries_limit: z.number().int().min(-1),
  monthly_traffic_updates_used: z.number().int().min(0),
  monthly_traffic_updates_limit: z.number().int().min(-1),
  usage_reset_date: z.string().datetime()
});

// Rate limit options schema
export const RateLimitOptionsSchema = z.object({
  windowMs: z.number().int().min(1000).max(24 * 60 * 60 * 1000), // 1 second to 24 hours
  maxRequests: z.number().int().min(1).max(10000),
  skipSuccessfulRequests: z.boolean().optional().default(false),
  skipFailedRequests: z.boolean().optional().default(false)
});

// API request metadata schema
export const ApiRequestMetadataSchema = z.object({
  endpoint: z.string().max(200).optional(),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).optional(),
  userAgent: z.string().max(500).optional(),
  ip: z.string().ip().optional(),
  timestamp: z.string().datetime().optional(),
  duration: z.number().int().min(0).optional()
});

// Usage tracking schema
export const UsageTrackingSchema = z.object({
  user_uuid: z.string().uuid(),
  action: TierActionSchema,
  project_id: z.string().uuid().optional(),
  api_endpoint: z.string().url().optional(),
  metadata: ApiRequestMetadataSchema.optional(),
  created_at: z.string().datetime()
});

// Tier limits schema
export const TierLimitsSchema = z.object({
  projects: z.number().int().min(-1),
  domains: z.number().int().min(-1),
  link_resources: z.number().int().min(-1),
  monthly_dr_queries: z.number().int().min(-1),
  monthly_traffic_updates: z.number().int().min(-1)
});

// Subscription upgrade request schema
export const SubscriptionUpgradeSchema = z.object({
  userUuid: z.string().uuid(),
  subscriptionPlan: z.enum(['monthly', 'yearly']).default('monthly'),
  subscriptionStartDate: z.string().datetime().optional(),
  subscriptionEndDate: z.string().datetime().optional()
});

// Validation helper functions
export function validateTierAction(action: unknown): action is z.infer<typeof TierActionSchema> {
  return TierActionSchema.safeParse(action).success;
}

export function validateUserTier(tier: unknown): tier is z.infer<typeof UserTierSchema> {
  return UserTierSchema.safeParse(tier).success;
}

export function validateTierInfo(data: unknown): data is z.infer<typeof TierInfoSchema> {
  return TierInfoSchema.safeParse(data).success;
}

export function validateUsageSummary(data: unknown): data is z.infer<typeof UserUsageSummarySchema> {
  return UserUsageSummarySchema.safeParse(data).success;
}

// Sanitization functions using Zod
export function sanitizeMetadata(metadata: unknown): z.infer<typeof ApiRequestMetadataSchema> {
  const result = ApiRequestMetadataSchema.safeParse(metadata);
  if (result.success) {
    return result.data;
  }
  
  // Return empty object if validation fails
  return {};
}

export function sanitizeTierMiddlewareOptions(options: unknown): z.infer<typeof TierMiddlewareOptionsSchema> | null {
  const result = TierMiddlewareOptionsSchema.safeParse(options);
  return result.success ? result.data : null;
}

// Error messages for validation failures
export const VALIDATION_ERROR_MESSAGES = {
  INVALID_TIER_ACTION: 'Invalid tier action. Must be one of: create_project, add_domain, add_link_resource, dr_query, traffic_update',
  INVALID_USER_TIER: 'Invalid user tier. Must be either "free" or "paid"',
  INVALID_PROJECT_ID: 'Invalid project ID. Must be a valid UUID',
  INVALID_API_ENDPOINT: 'Invalid API endpoint. Must be a valid URL',
  INVALID_METADATA: 'Invalid metadata format',
  INVALID_USAGE_LIMITS: 'Invalid usage limits. Must be non-negative integers or -1 for unlimited',
  INVALID_SUBSCRIPTION_STATUS: 'Invalid subscription status'
};

// Type exports
export type TierAction = z.infer<typeof TierActionSchema>;
export type UserTier = z.infer<typeof UserTierSchema>;
export type SubscriptionStatus = z.infer<typeof SubscriptionStatusSchema>;
export type TierInfo = z.infer<typeof TierInfoSchema>;
export type UserUsageSummary = z.infer<typeof UserUsageSummarySchema>;
export type TierValidationResult = z.infer<typeof TierValidationResultSchema>;
export type TierMiddlewareOptions = z.infer<typeof TierMiddlewareOptionsSchema>;
export type ApiRequestMetadata = z.infer<typeof ApiRequestMetadataSchema>;
export type UsageTracking = z.infer<typeof UsageTrackingSchema>;
export type TierLimits = z.infer<typeof TierLimitsSchema>;
export type SubscriptionUpgrade = z.infer<typeof SubscriptionUpgradeSchema>;