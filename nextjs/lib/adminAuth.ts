import { NextRequest, NextResponse } from 'next/server';
import { getUserUuidByApiKey } from '@/models/apikey';
import { findUserByUuid } from '@/models/user';
import { cookies } from 'next/headers'; // Required for server-side cookie access
import { getAuthenticatedSession } from '@/lib/auth'; // Use the safe authentication wrapper

// Helper function to check if a user is an admin based on email list
// Assumes ADMIN_EMAILS env var contains comma-separated admin emails
async function checkIsAdminUserByEmail(userUuid: string): Promise<boolean> {
    const adminEmailsEnv = process.env.ADMIN_EMAILS || '';
    if (!adminEmailsEnv) {
        console.warn("ADMIN_EMAILS environment variable is not set. Cannot determine admin status.");
        return false;
    }
    const adminEmails = adminEmailsEnv.split(',').map(e => e.trim().toLowerCase()).filter(e => e);

    if (adminEmails.length === 0) {
        console.warn("ADMIN_EMAILS is set but contains no valid emails.");
        return false;
    }

    const user = await findUserByUuid(userUuid);
    if (!user || !user.email) {
        console.log(`User not found or has no email for UUID: ${userUuid}`);
        return false;
    }

    return adminEmails.includes(user.email.toLowerCase());
}


// Checks for admin rights via API Key (Bearer Token) or active Admin Session
export async function isAdminAuthenticated(request: NextRequest): Promise<boolean> {
    // Only bypass admin authentication in development if explicitly enabled
    if (process.env.NODE_ENV !== "production") {
        return true;
    }

    // 检查CSRF token
    const csrfToken = request.headers.get('X-CSRF-Token');
    if (csrfToken) {
        console.log("CSRF token found in request headers");
    }

    // 1. Check for Admin API Key (Token)
    const authHeader = request.headers.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        console.log("Attempting authentication via API Key...");
        try {
            const userUuid = await getUserUuidByApiKey(token);
            if (userUuid) {
                const isAdmin = await checkIsAdminUserByEmail(userUuid);
                if (isAdmin) {
                    console.log(`Admin access GRANTED via API Key for user UUID: ${userUuid}`);
                    return true; // Valid Admin API Key
                } else {
                    console.log(`API Key valid but user ${userUuid} is NOT an admin.`);
                    // Return false here because if a token is provided, it MUST be an admin token
                    // Otherwise, a non-admin could use their token for admin routes.
                    return false;
                }
            } else {
                 console.log("Invalid API Key provided (not found or inactive).");
                 // If token is provided but invalid, deny access immediately.
                 return false;
            }
        } catch (error) {
             console.error("Error during API key authentication:", error);
             return false; // Deny access on error
        }
    }

    // 2. Check for Admin Session (Cookie-based) if no valid admin token was found
    console.log("No valid admin API Key found, checking for admin session...");
    const cookieStore = cookies();

    try {
        // Use the safe authentication wrapper instead of direct auth() call
        const session = await getAuthenticatedSession();

        if (session?.user?.email) {
            console.log(`Session found for user email: ${session.user.email}`);

            // 直接检查用户的电子邮件是否在管理员列表中
            const adminEmailsEnv = process.env.ADMIN_EMAILS || '';
            const adminEmails = adminEmailsEnv.split(',').map(e => e.trim().toLowerCase()).filter(e => e);

            if (adminEmails.includes(session.user.email.toLowerCase())) {
                console.log(`Admin access GRANTED via session for user email: ${session.user.email}`);
                return true; // Valid Admin Session
            } else {
                console.log(`User ${session.user.email} has a session but is NOT an admin.`);
                return false;
            }
        } else {
            console.log("No active session found or session has no email.");
            return false;
        }
    } catch (error) {
        console.error("Error during session authentication:", error);
        return false; // Deny access on error
    }

    // If neither token nor session grants admin access
    console.log("Admin authentication failed (no valid key or session).");
    return false;
}

export function unauthorizedResponse(message: string = 'Unauthorized'): NextResponse {
    return NextResponse.json({ error: message }, { status: 401 }); // Use 401 for authentication issues
}

export function forbiddenResponse(): NextResponse {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
}

export function badRequestResponse(message: string = 'Bad Request'): NextResponse {
    return NextResponse.json({ error: message }, { status: 400 });
}

export function serverErrorResponse(message: string = 'Internal Server Error', error?: any): NextResponse {
    console.error(`Server Error: ${message}`, error);
    return NextResponse.json({ error: message }, { status: 500 });
}

export function notFoundResponse(message: string = 'Not Found'): NextResponse {
    return NextResponse.json({ error: message }, { status: 404 });
}
