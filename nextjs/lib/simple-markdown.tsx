// A simple markdown renderer that works in Edge Runtime

export function SimpleMarkdown({ markdown }: { markdown: string }) {
  if (!markdown) return null;

  // Convert headers
  let html = markdown
    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mt-6 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-7 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-5 pb-2 border-b border-gray-200 dark:border-gray-700">$1</h1>');

  // Convert line breaks
  html = html.replace(/\n$/gim, '<br/>');
  
  // Convert bold
  html = html.replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold">$1</strong>');
  
  // Convert italic
  html = html.replace(/\*(.*?)\*/gim, '<em class="italic">$1</em>');
  
  // Convert links
  html = html.replace(/\[(.*?)\]\((.*?)\)/gim, '<a href="$2" class="text-blue-600 hover:underline hover:text-blue-800 transition-colors">$1</a>');
  
  // Convert unordered lists
  html = html.replace(/^\s*\*(.*)/gim, '<li class="py-1">$1</li>');
  html = html.replace(/<\/li>\n<li>/g, '</li><li>');
  html = html.replace(/(<li>.*<\/li>)/gim, '<ul class="list-disc pl-6 my-4 space-y-1">$1</ul>');

  // Convert ordered lists
  html = html.replace(/^\s*(\d+)\.\s+(.*)/gim, '<li class="py-1">$2</li>');
  html = html.replace(/(<li>.*<\/li>)/gim, (match) => {
    // Only wrap in <ol> if not already wrapped in <ul>
    if (match.indexOf('<ul') === -1) {
      return '<ol class="list-decimal pl-6 my-4 space-y-1">' + match + '</ol>';
    }
    return match;
  });

  // Convert blockquotes
  html = html.replace(/^\s*>(.+)/gim, '<blockquote class="pl-4 border-l-4 border-gray-300 dark:border-gray-600 italic text-gray-700 dark:text-gray-300 my-4">$1</blockquote>');
  
  // Convert code blocks with language support
  html = html.replace(/```(\w+)?\n([\s\S]*?)```/gim, (_, lang, code) => {
    const language = lang ? ` language-${lang}` : '';
    return `<div class="relative my-6">
      ${lang ? `<div class="absolute top-0 right-0 bg-gray-200 dark:bg-gray-700 text-xs px-2 py-1 rounded-bl rounded-tr">${lang}</div>` : ''}
      <pre class="bg-gray-100 dark:bg-gray-800 rounded-md p-4 overflow-x-auto"><code class="text-sm${language}">${code}</code></pre>
    </div>`;
  });
  
  // Convert inline code
  html = html.replace(/`(.*?)`/gim, '<code class="bg-gray-100 dark:bg-gray-800 rounded-md px-1.5 py-0.5 text-sm font-mono">$1</code>');

  // Convert horizontal rules
  html = html.replace(/^\s*---\s*$/gim, '<hr class="my-6 border-t border-gray-300 dark:border-gray-600" />');

  // Convert paragraphs with improved spacing
  html = html
    .replace(/^\s*(.+)/gim, function(match) {
      if (match.match(/<h|<ul|<ol|<li|<pre|<blockquote|<hr/)) {
        return match;
      }
      return '<p class="my-4 leading-relaxed">$1</p>'.replace('$1', match);
    })
    .replace(/<\/p><p>/g, '</p>\n<p>');

  return (
    <div className="prose prose-sm md:prose-base lg:prose-lg dark:prose-invert max-w-none px-1 py-2">
      <div className="rounded-md" dangerouslySetInnerHTML={{ __html: html }} />
    </div>
  );
} 