import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { Session } from "next-auth";

// Enhanced interfaces for safe authentication
interface SafeAuthResult {
  session: Session | null;
  error?: string;
  fallbackUsed?: boolean;
  compatibilityMode?: 'nextjs14' | 'nextjs15' | 'fallback';
}

interface AuthErrorContext {
  errorType: 'headers_compatibility' | 'auth_failure' | 'unknown';
  originalError: Error;
  fallbackApplied: boolean;
  environment: 'development' | 'production' | 'test';
}

// Cache for compatibility detection to avoid repeated checks
let compatibilityCache: { 
  hasHeadersIssue?: boolean; 
  lastChecked?: number; 
  cacheExpiry: number;
} = { cacheExpiry: 5 * 60 * 1000 }; // 5 minutes cache

/**
 * Safe wrapper around NextAuth's auth() function that handles Next.js 15 headers compatibility issues
 */
async function safeGetAuthenticatedSession(): Promise<SafeAuthResult> {
  const environment = (process.env.NODE_ENV || 'development') as 'development' | 'production' | 'test';
  const now = Date.now();
  
  try {
    // Check cache first to avoid repeated compatibility checks
    if (compatibilityCache.hasHeadersIssue && 
        compatibilityCache.lastChecked && 
        (now - compatibilityCache.lastChecked) < compatibilityCache.cacheExpiry) {
      
      if (environment === 'development') {
        console.log("🔄 Using cached compatibility detection - headers issue detected");
        return await handleHeadersCompatibilityIssue(environment);
      }
    }

    // Call auth() directly - this works fine in Next.js 14
    const session = await auth();
    
    // Success - update cache and return session
    compatibilityCache.hasHeadersIssue = false;
    compatibilityCache.lastChecked = now;
    
    return {
      session,
      compatibilityMode: 'nextjs15'
    };
    
  } catch (error) {
    const authError = error as Error;
    const errorMessage = authError.message || '';
    
    // Detect NextAuth headers compatibility issue
    if (errorMessage.includes("headers().get") && errorMessage.includes("should be awaited") ||
        errorMessage.includes("Headers compatibility issue")) {
      console.log("🚨 NextAuth headers compatibility issue detected");
      
      // Update cache
      compatibilityCache.hasHeadersIssue = true;
      compatibilityCache.lastChecked = now;
      
      return await handleHeadersCompatibilityIssue(environment, authError);
    }
    
    // Detect OAuth connection timeout errors
    if (errorMessage.includes("Connect Timeout Error") || 
        errorMessage.includes("UND_ERR_CONNECT_TIMEOUT") ||
        errorMessage.includes("accounts.google.com") ||
        errorMessage.includes("timeout")) {
      
      console.error("🌐 OAuth connection timeout detected:", {
        error: errorMessage,
        provider: errorMessage.includes("google") ? "Google" : "Unknown",
        timestamp: new Date().toISOString()
      });
      
      const errorContext: AuthErrorContext = {
        errorType: 'auth_failure',
        originalError: authError,
        fallbackApplied: false,
        environment
      };
      
      return {
        session: null,
        error: 'oauth_timeout',
        compatibilityMode: 'fallback'
      };
    }
    
    // Handle other authentication errors
    const errorContext: AuthErrorContext = {
      errorType: 'auth_failure',
      originalError: authError,
      fallbackApplied: false,
      environment
    };
    
    console.error("Authentication error:", errorContext);
    
    return {
      session: null,
      error: 'Authentication failed',
      compatibilityMode: 'fallback'
    };
  }
}

/**
 * Handle NextAuth headers compatibility issues with environment-specific fallbacks
 */
async function handleHeadersCompatibilityIssue(
  environment: 'development' | 'production' | 'test',
  originalError?: Error
): Promise<SafeAuthResult> {
  
  const errorContext: AuthErrorContext = {
    errorType: 'headers_compatibility',
    originalError: originalError || new Error('Headers compatibility issue'),
    fallbackApplied: true,
    environment
  };
  
  // Log the compatibility issue once but don't spam
  if (!global.__nextAuthCompatibilityWarningShown) {
    console.warn('⚠️  NextAuth v5 + Next.js 15 Compatibility Issue: NextAuth is not yet fully compatible with Next.js 15\'s async headers API. Session will be null until this is resolved.');
    
    if (environment === 'development') {
      console.log("📝 Debug info:", {
        error: originalError?.message,
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        nextjsVersion: process.env.npm_package_dependencies_next || 'unknown'
      });
      console.log("🔧 Development debugging enabled:");
      console.log("  - NextAuth headers() compatibility issue detected");
      console.log("  - This is likely due to Next.js 15 requiring await for headers()");
      console.log("  - Application will continue with null session for security");
      console.log("  - Check NextAuth version compatibility with Next.js 15");
    }
    
    global.__nextAuthCompatibilityWarningShown = true;
  }
  
  if (environment === 'development') {
    // In development, return a minimal session structure to allow development to continue
    // This should NOT be used in production for security reasons
    return {
      session: null, // Return null to maintain security even in dev mode
      error: 'NextAuth headers compatibility issue - check console for details',
      fallbackUsed: true,
      compatibilityMode: 'fallback'
    };
  }
  
  if (environment === 'production') {
    console.error("❌ Production: NextAuth headers compatibility issue", {
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent : 'server-side'
    });
    
    // In production, fail securely
    return {
      session: null,
      error: 'Authentication service temporarily unavailable',
      fallbackUsed: true,
      compatibilityMode: 'fallback'
    };
  }
  
  // Test environment
  return {
    session: null,
    error: 'NextAuth headers compatibility issue in test environment',
    fallbackUsed: true,
    compatibilityMode: 'fallback'
  };
}

export async function getAuthenticatedUser() {
  try {
    // Use the safe authentication wrapper
    const authResult = await safeGetAuthenticatedSession();
    
    if (authResult.error) {
      console.log("👤 Authentication error:", authResult.error);
      if (authResult.fallbackUsed) {
        console.log("🔄 Fallback mechanism was used");
      }
      return null;
    }
    
    if (!authResult.session || !authResult.session.user) {
      console.log("👤 No authenticated session found");
      return null;
    }

    console.log("👤 Session found:", { 
      email: authResult.session.user.email, 
      uuid: authResult.session.user.uuid,
      compatibilityMode: authResult.compatibilityMode
    });

    // Return the user from the session
    return authResult.session.user;
  } catch (error) {
    console.error("Error getting authenticated user:", error);
    return null;
  }
}

export async function requireAuth() {
  const user = await getAuthenticatedUser();
  if (!user || !user.uuid) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    );
  }
  return user;
}

export async function getAuthenticatedSession() {
  try {
    // Use the safe authentication wrapper
    const authResult = await safeGetAuthenticatedSession();
    
    if (authResult.error) {
      console.log("👤 Authentication error:", authResult.error);
      if (authResult.fallbackUsed) {
        console.log("🔄 Fallback mechanism was used");
      }
      return null;
    }
    
    if (!authResult.session || !authResult.session.user) {
      console.log("👤 No authenticated session found");
      return null;
    }

    console.log("👤 Session found with tokens:", { 
      email: authResult.session.user.email,
      hasGoogleToken: !!authResult.session.googleAccessToken,
      compatibilityMode: authResult.compatibilityMode
    });

    // Return the complete session including Google tokens
    return authResult.session;
  } catch (error) {
    console.error("Error getting authenticated session:", error);
    return null;
  }
}

export function unauthorizedResponse(message: string = "Authentication required") {
  return NextResponse.json(
    { error: message },
    { status: 401 }
  );
} 