import { auth } from "@/auth";
import { Session } from "next-auth";

/**
 * Next.js 14 compatible wrapper for NextAuth's auth() function
 * This function works properly with Next.js 14 and NextAuth v5
 */
export async function getNextAuthSession(): Promise<Session | null> {
  try {
    // In Next.js 14, we can call auth() directly
    return await auth();
  } catch (error) {
    const authError = error as Error;
    
    // Log the error and return null
    console.error('NextAuth session error:', authError.message);
    return null;
  }
}

/**
 * Convenience function to get authenticated session with error handling
 * Returns null if no session or if there's an error
 */
export async function getAuthSessionSafe(): Promise<Session | null> {
  try {
    return await getNextAuthSession();
  } catch (error) {
    console.error('Failed to get auth session:', error);
    return null;
  }
}

/**
 * Get authenticated user UUID from session
 */
export async function getAuthenticatedUserUuid(): Promise<string | null> {
  try {
    const session = await getNextAuthSession();
    return session?.user?.uuid || null;
  } catch (error) {
    console.error('Failed to get authenticated user UUID:', error);
    return null;
  }
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const session = await getNextAuthSession();
    return !!session?.user?.uuid;
  } catch (error) {
    console.error('Failed to check authentication:', error);
    return false;
  }
} 