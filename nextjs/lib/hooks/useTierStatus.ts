import useSWR from 'swr';
import { useCallback } from 'react';
import { fetchWithRetry, RetryableError } from '@/lib/utils/retry';

export interface TierInfo {
  tier: 'free' | 'paid';
  subscription_status: string;
  limits: {
    projects: { used: number; limit: number; canCreate: boolean };
    domains: { used: number; limit: number; canAdd: boolean };
    link_resources: { used: number; limit: number; canAdd: boolean };
    monthly_dr_queries: { used: number; limit: number; canUse: boolean };
    monthly_traffic_updates: { used: number; limit: number; canUse: boolean };
  };
  usage_reset_date: string;
}

// SWR fetcher function with retry logic
const fetcher = async (url: string): Promise<TierInfo> => {
  const response = await fetchWithRetry(url, {
    method: 'GET',
    credentials: 'include',
  }, {
    maxRetries: 3,
    baseDelay: 1000,
    backoffFactor: 2,
    jitter: true
  });

  const data = await response.json();
  
  if (!data.tierInfo) {
    throw new RetryableError('Invalid response format');
  }
  
  return data.tierInfo;
};

export function useTierStatus() {
  const { 
    data: tierInfo, 
    error, 
    isLoading: loading, 
    mutate 
  } = useSWR<TierInfo>('/api/user/tier-info', fetcher, {
    // Cache for 2 minutes
    dedupingInterval: 2 * 60 * 1000,
    // Revalidate every 5 minutes
    refreshInterval: 5 * 60 * 1000,
    // Revalidate when window regains focus
    revalidateOnFocus: true,
    // Retry on error with exponential backoff
    errorRetryCount: 3,
    errorRetryInterval: 1000,
    // Keep previous data while revalidating
    keepPreviousData: true,
    // Fallback data
    fallbackData: null,
    // Only revalidate if data is stale
    revalidateIfStale: true,
    // Revalidate when user comes back online
    revalidateOnReconnect: true,
    // Custom error handler
    onError: (err) => {
      console.error('Error fetching tier info:', err);
    }
  });

  // Manual refresh function
  const refetch = useCallback(() => {
    return mutate();
  }, [mutate]);

  // Optimistic update function for better UX
  const updateTierInfo = useCallback((newTierInfo: TierInfo) => {
    mutate(newTierInfo, false); // Update cache without revalidation
  }, [mutate]);

  // Helper functions for tier checks
  const isPaidUser = tierInfo?.tier === 'paid' && tierInfo?.subscription_status === 'active';
  const canCreateProject = tierInfo?.limits.projects.canCreate || false;
  const canAddDomain = tierInfo?.limits.domains.canAdd || false;
  const canAddLinkResource = tierInfo?.limits.link_resources.canAdd || false;
  const canMakeDrQuery = tierInfo?.limits.monthly_dr_queries.canUse || false;
  const canMakeTrafficUpdate = tierInfo?.limits.monthly_traffic_updates.canUse || false;

  const getUsagePercentage = useCallback((used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  }, []);

  const formatUsage = useCallback((used: number, limit: number) => {
    if (limit === -1) return `${used} / Unlimited`;
    return `${used} / ${limit}`;
  }, []);

  return {
    tierInfo,
    loading,
    error: error?.message || null,
    refetch,
    updateTierInfo,
    // Convenience methods
    isPaidUser,
    canCreateProject,
    canAddDomain,
    canAddLinkResource,
    canMakeDrQuery,
    canMakeTrafficUpdate,
    getUsagePercentage,
    formatUsage
  };
}