// Helper function to parse GitHub URL and extract owner/repo
function parseGithubUrl(url: string): { owner: string; repo: string } | null {
    try {
        const parsedUrl = new URL(url);
        if (parsedUrl.hostname !== 'github.com') {
            return null;
        }
        const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0);
        if (pathParts.length >= 2) {
            return { owner: pathParts[0], repo: pathParts[1] };
        }
        return null;
    } catch (e) {
        console.error("Invalid GitHub URL:", url, e);
        return null;
    }
}

// fetchGithubRepoInfo(github_url)
// return: github metadata, including forks, stars, watchers, update_time, readme content, description
export async function fetchGithubRepoInfo(github_url: string): Promise<{
    metadata: {
        forks: number;
        stars: number;
        watchers: number;
        updated_at: string;
        description: string | null; // Added description
        owner: string;
        repo: string;
        user_avatar_url?: string; // Added user avatar URL
    } | null;
    readme: string | null;
    error?: string;
}> {
    const repoInfo = parseGithubUrl(github_url);
    if (!repoInfo) {
        return { metadata: null, readme: null, error: "Invalid GitHub URL format." };
    }

    const { owner, repo } = repoInfo;
    const apiUrl = `https://api.github.com/repos/${owner}/${repo}`;
    const readmeUrl = `https://api.github.com/repos/${owner}/${repo}/readme`;
    const userUrl = `https://api.github.com/users/${owner}`; // Added user API endpoint
    const headers: HeadersInit = {
        'Accept': 'application/vnd.github.v3+json',
        'X-GitHub-Api-Version': '2022-11-28',
    };

    // Use GITHUB_TOKEN if available for higher rate limits
    const token = process.env.GITHUB_TOKEN;
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    try {
        // Fetch repository metadata
        const repoResponse = await fetch(apiUrl, { headers });
        if (!repoResponse.ok) {
            const errorData = await repoResponse.json().catch(() => ({}));
            console.error(`GitHub API error fetching repo ${owner}/${repo}:`, repoResponse.status, errorData);
            return { metadata: null, readme: null, error: `Failed to fetch repository metadata: ${repoResponse.status} ${errorData.message || ''}`.trim() };
        }
        const repoData = await repoResponse.json();

        // Fetch user data to get avatar URL
        let userAvatarUrl: string | undefined = undefined;
        try {
            const userResponse = await fetch(userUrl, { headers });
            if (userResponse.ok) {
                const userData = await userResponse.json();
                userAvatarUrl = userData.avatar_url;
            } else {
                console.warn(`Could not fetch user info for ${owner}:`, userResponse.status);
            }
        } catch (userError) {
            console.warn(`Error fetching user data for ${owner}:`, userError);
            // Continue execution even if user data fetch fails
        }

        // Fetch README content
        const readmeResponse = await fetch(readmeUrl, { headers });
        let readmeContent: string | null = null;
        if (readmeResponse.ok) {
            const readmeData = await readmeResponse.json();
            if (readmeData.content) {
                // Decode Base64 content
                readmeContent = Buffer.from(readmeData.content, 'base64').toString('utf-8');
            }
        } else {
            // README might not exist, which is not necessarily a fatal error
            console.warn(`Could not fetch README for ${owner}/${repo}:`, readmeResponse.status);
        }

        const metadata = {
            forks: repoData.forks_count || 0,
            stars: repoData.stargazers_count || 0,
            watchers: repoData.watchers_count || 0, // watchers_count often same as stargazers_count, consider subscribers_count if different metric needed
            updated_at: repoData.updated_at || new Date().toISOString(),
            description: repoData.description || null,
            owner: owner,
            repo: repo,
            user_avatar_url: userAvatarUrl, // Added user avatar URL to metadata
        };

        return { metadata, readme: readmeContent };

    } catch (error: any) {
        console.error(`Error fetching GitHub info for ${owner}/${repo}:`, error);
        return { metadata: null, readme: null, error: `An unexpected error occurred: ${error.message}` };
    }
}
