/**
 * OAuth Error Handler - Provides user-friendly error messages and retry functionality
 */

export interface OAuthError {
  type: 'timeout' | 'network' | 'provider' | 'unknown';
  provider?: string;
  message: string;
  userMessage: string;
  canRetry: boolean;
  retryDelay?: number;
}

/**
 * Detect and classify OAuth errors
 */
export function classifyOAuthError(error: string | Error): OAuthError {
  const errorMessage = typeof error === 'string' ? error : error.message || '';
  const lowerError = errorMessage.toLowerCase();

  // Connection timeout errors
  if (lowerError.includes('connect timeout error') || 
      lowerError.includes('und_err_connect_timeout') ||
      lowerError.includes('timeout')) {
    
    let provider = 'OAuth provider';
    if (lowerError.includes('google') || lowerError.includes('accounts.google.com')) {
      provider = 'Google';
    } else if (lowerError.includes('github')) {
      provider = 'GitHub';
    }

    return {
      type: 'timeout',
      provider,
      message: errorMessage,
      userMessage: `Connection to ${provider} timed out. This might be due to a slow network connection or temporary server issues.`,
      canRetry: true,
      retryDelay: 3000, // 3 seconds
    };
  }

  // Network errors
  if (lowerError.includes('network') || 
      lowerError.includes('fetch failed') ||
      lowerError.includes('connection refused')) {
    
    return {
      type: 'network',
      message: errorMessage,
      userMessage: 'Network connection failed. Please check your internet connection and try again.',
      canRetry: true,
      retryDelay: 2000,
    };
  }

  // Provider-specific errors
  if (lowerError.includes('oauth') || 
      lowerError.includes('authorization') ||
      lowerError.includes('invalid_client')) {
    
    return {
      type: 'provider',
      message: errorMessage,
      userMessage: 'Authentication service is temporarily unavailable. Please try again in a few moments.',
      canRetry: true,
      retryDelay: 5000,
    };
  }

  // Unknown errors
  return {
    type: 'unknown',
    message: errorMessage,
    userMessage: 'An unexpected error occurred during sign-in. Please try again.',
    canRetry: true,
    retryDelay: 3000,
  };
}

/**
 * Get user-friendly error message for OAuth errors
 */
export function getOAuthErrorMessage(error: string | Error): string {
  const oauthError = classifyOAuthError(error);
  return oauthError.userMessage;
}

/**
 * Check if an OAuth error can be retried
 */
export function canRetryOAuthError(error: string | Error): boolean {
  const oauthError = classifyOAuthError(error);
  return oauthError.canRetry;
}

/**
 * Get recommended retry delay for OAuth errors
 */
export function getOAuthRetryDelay(error: string | Error): number {
  const oauthError = classifyOAuthError(error);
  return oauthError.retryDelay || 3000;
}

/**
 * Generate retry URL for OAuth authentication
 */
export function getOAuthRetryUrl(provider: string, currentUrl?: string): string {
  const baseUrl = currentUrl || window.location.href;
  const url = new URL('/api/auth/signin/' + provider.toLowerCase(), window.location.origin);
  
  // Add callback URL to return to current page after successful auth
  if (currentUrl) {
    url.searchParams.set('callbackUrl', currentUrl);
  }
  
  return url.toString();
}

/**
 * Log OAuth error for monitoring and debugging
 */
export function logOAuthError(error: string | Error, context?: Record<string, any>) {
  const oauthError = classifyOAuthError(error);
  
  console.error('OAuth Error:', {
    type: oauthError.type,
    provider: oauthError.provider,
    message: oauthError.message,
    userMessage: oauthError.userMessage,
    canRetry: oauthError.canRetry,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent : 'server-side',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown',
    ...context
  });
}