import { Notifier, NotificationPayload, NotificationLevel } from '..';

interface FeishuMessageContent {
  title: string;
  content: string[];
}

interface FeishuCardConfig {
  wide_screen_mode: boolean;
  enable_forward: boolean;
}

interface FeishuHeader {
  title: {
    tag: 'plain_text';
    content: string;
  };
  template: 'red' | 'orange' | 'green' | 'blue' | 'turquoise' | 'indigo' | 'grey' | 'wathet';
}

interface FeishuElement {
  tag: string;
  text?: {
    tag: string;
    content: string;
  };
  fields?: {
    is_short: boolean;
    text: {
      tag: string;
      content: string;
    };
  }[];
}

interface FeishuCardMessage {
  config: FeishuCardConfig;
  header: FeishuHeader;
  elements: FeishuElement[];
}

export class FeishuNotifier implements Notifier {
  private webhookUrl: string;

  constructor(webhookUrl: string) {
    this.webhookUrl = webhookUrl;
  }

  async send(payload: NotificationPayload): Promise<boolean> {
    try {
      const cardMessage = this.createCardMessage(payload);
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          msg_type: 'interactive',
          card: cardMessage,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Feishu notification failed:', errorText);
        return false;
      }

      const result = await response.json();
      // Successful Feishu response contains "StatusCode": 0
      return result.StatusCode === 0 || result.code === 0;
    } catch (error) {
      console.error('Error sending Feishu notification:', error);
      return false;
    }
  }

  private createCardMessage(payload: NotificationPayload): FeishuCardMessage {
    const { title, content, level = 'info', data } = payload;
    
    // Convert notification level to Feishu template color
    const template = this.getLevelTemplate(level);
    
    // Create card elements
    const elements: FeishuElement[] = [
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content,
        },
      },
    ];

    // Add data fields if provided
    if (data && Object.keys(data).length > 0) {
      const fields = Object.entries(data).map(([key, value]) => ({
        is_short: true,
        text: {
          tag: 'lark_md',
          content: `**${key}**: ${typeof value === 'object' ? JSON.stringify(value) : value}`,
        },
      }));

      if (fields.length > 0) {
        elements.push({
          tag: 'div',
          fields,
        });
      }
    }

    // Add timestamp
    elements.push({
      tag: 'note',
      text: {
        tag: 'plain_text',
        content: `Time: ${new Date().toLocaleString()}`,
      },
    });

    return {
      config: {
        wide_screen_mode: true,
        enable_forward: true,
      },
      header: {
        title: {
          tag: 'plain_text',
          content: title,
        },
        template,
      },
      elements,
    };
  }

  private getLevelTemplate(level: NotificationLevel): FeishuHeader['template'] {
    switch (level) {
      case 'success':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      case 'info':
      default:
        return 'blue';
    }
  }
} 