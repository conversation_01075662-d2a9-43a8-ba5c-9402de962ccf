# Notification Module

This module provides a unified way to send notifications to different platforms from the web-AiMCP application.

## Features

- Modular provider system that allows for easy extension
- Currently supports Feishu (Lark) webhook notifications
- Unified notification interface with consistent payloads
- Configurable through environment variables

## Configuration

Configure notification settings in your `.env` file:

```bash
# Feishu (Lark) Notification
NOTIFICATION_FEISHU_ENABLED=true
NOTIFICATION_FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
```

## Usage

Import the notification service and use it to send notifications:

```typescript
import { notificationService } from '@/lib/notification';

async function handleImportantEvent() {
  // Your business logic...

  // Send a notification
  await notificationService.notify({
    title: 'Event Title', // Required
    content: 'Event details or description with **markdown** support', // Required
    level: 'info', // Optional, default is 'info'
    data: { // Optional, additional structured data
      'Field Name': 'Field Value',
      'Another Field': 123,
      // ...
    }
  });
}
```

### Notification Levels

- `info`: Blue color, for general information (default)
- `success`: Green color, for successful operations
- `warning`: Orange color, for warnings
- `error`: Red color, for errors

## Extending with New Providers

To add a new notification provider:

1. Create a new provider class in `providers/your-provider.ts` that implements the `Notifier` interface

```typescript
import { Notifier, NotificationPayload } from '..';

export class YourProviderNotifier implements Notifier {
  constructor(private config: any) {
    // Initialize with your provider-specific config
  }

  async send(payload: NotificationPayload): Promise<boolean> {
    // Implement your provider-specific logic
    // Return true on success, false on failure
  }
}
```

2. Update the `NotificationService` class in `index.ts` to include your new provider:

```typescript
private initializeProviders() {
  // Existing providers...

  // Add your new provider
  if (process.env.NOTIFICATION_YOUR_PROVIDER_ENABLED === 'true') {
    const config = {
      // Parse your provider-specific config from env vars
    };
    const yourProvider = new YourProviderNotifier(config);
    this.providers.set('your-provider', yourProvider);
    this.enabledProviders.push('your-provider');
  }
}
```

3. Update `.env.example` with your provider's configuration variables

## Supported Notification Providers

### Feishu (Lark)

Sends interactive card messages to Feishu/Lark channels using webhooks.

#### Setup:
1. Create a bot in your Feishu workspace
2. Get the webhook URL
3. Configure in your `.env` file:
   ```
   NOTIFICATION_FEISHU_ENABLED=true
   NOTIFICATION_FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id
   ``` 