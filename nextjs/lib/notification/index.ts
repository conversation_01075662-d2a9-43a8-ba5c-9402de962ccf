import { FeishuNotifier } from './providers/feishu';

export type NotificationLevel = 'info' | 'success' | 'warning' | 'error';

export interface NotificationPayload {
  title: string;
  content: string;
  level?: NotificationLevel;
  data?: Record<string, any>;
}

export interface Notifier {
  send(payload: NotificationPayload): Promise<boolean>;
}

class NotificationService {
  private providers: Map<string, Notifier> = new Map();
  private enabledProviders: string[] = [];

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    // Initialize Feishu provider if configured
    if (process.env.NOTIFICATION_FEISHU_ENABLED === 'true' && process.env.NOTIFICATION_FEISHU_WEBHOOK_URL) {
      const feishuNotifier = new FeishuNotifier(process.env.NOTIFICATION_FEISHU_WEBHOOK_URL);
      this.providers.set('feishu', feishuNotifier);
      this.enabledProviders.push('feishu');
    }

    // Add more providers here in the future
  }

  async notify(payload: NotificationPayload): Promise<boolean> {
    if (this.enabledProviders.length === 0) {
      console.log('No notification providers enabled');
      return false;
    }

    const results = await Promise.all(
      this.enabledProviders.map(async (providerName) => {
        const provider = this.providers.get(providerName);
        if (!provider) return false;

        try {
          return await provider.send(payload);
        } catch (error) {
          console.error(`Error sending notification via ${providerName}:`, error);
          return false;
        }
      })
    );

    // Return true if at least one provider succeeded
    return results.some(Boolean);
  }
}

// Export a singleton instance
export const notificationService = new NotificationService(); 