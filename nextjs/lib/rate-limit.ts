import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

// In-memory store for rate limiting (consider Redis for production)
const store: RateLimitStore = {};

// Clean up expired entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key];
    }
  });
}, 5 * 60 * 1000);

export class RateLimitError extends Error {
  constructor(
    message: string,
    public retryAfter: number,
    public limit: number,
    public remaining: number
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}

export async function checkRateLimit(
  request: NextRequest,
  options: RateLimitOptions
): Promise<{ success: boolean; error?: RateLimitError }> {
  const { windowMs, maxRequests } = options;
  
  // Generate unique key for user (IP + user ID if available)
  const ip = request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown';
  let userId = 'anonymous';
  
  try {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET!
    });
    if (token?.sub) {
      userId = token.sub;
    }
  } catch (error) {
    // Continue with IP-based rate limiting
  }
  
  const key = `${ip}:${userId}`;
  const now = Date.now();
  const resetTime = now + windowMs;
  
  if (!store[key]) {
    store[key] = { count: 1, resetTime };
    return { success: true };
  }
  
  // Reset counter if window has expired
  if (store[key].resetTime < now) {
    store[key] = { count: 1, resetTime };
    return { success: true };
  }
  
  // Increment counter
  store[key].count += 1;
  
  // Check if limit exceeded
  if (store[key].count > maxRequests) {
    const retryAfter = Math.ceil((store[key].resetTime - now) / 1000);
    return {
      success: false,
      error: new RateLimitError(
        `Rate limit exceeded. Try again in ${retryAfter} seconds.`,
        retryAfter,
        maxRequests,
        0
      )
    };
  }
  
  return { success: true };
}

export function withRateLimit(
  options: RateLimitOptions,
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: any) => {
    const result = await checkRateLimit(request, options);
    
    if (!result.success) {
      const error = result.error!;
      return NextResponse.json(
        {
          error: error.message,
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: error.retryAfter
        },
        { 
          status: 429,
          headers: {
            'Retry-After': error.retryAfter.toString(),
            'X-RateLimit-Limit': error.limit.toString(),
            'X-RateLimit-Remaining': error.remaining.toString(),
            'X-RateLimit-Reset': Math.ceil(Date.now() / 1000 + error.retryAfter).toString()
          }
        }
      );
    }
    
    return handler(request, context);
  };
}

// Common rate limit configurations
export const RATE_LIMITS = {
  // General API endpoints
  API_GENERAL: { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes
  
  // Tier-sensitive operations
  TIER_OPERATIONS: { windowMs: 60 * 1000, maxRequests: 30 }, // 30 requests per minute
  
  // Authentication endpoints
  AUTH_ENDPOINTS: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 requests per 15 minutes
  
  // DR queries (expensive operations)
  DR_QUERIES: { windowMs: 60 * 1000, maxRequests: 10 }, // 10 requests per minute
  
  // Traffic updates
  TRAFFIC_UPDATES: { windowMs: 60 * 1000, maxRequests: 20 }, // 20 requests per minute
};