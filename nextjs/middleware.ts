import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";
import { NextRequest, NextResponse } from "next/server";

// Create the next-intl middleware
const intlMiddleware = createMiddleware(routing);

// Middleware to handle language routing and invitation codes
const middleware = (request: NextRequest) => {
  const { pathname, searchParams } = request.nextUrl;
  
  // Check for invitation code in search params
  const inviteCode = searchParams.get('invite');
  
  // If accessing the root path, redirect to /en
  if (pathname === '/') {
    // Preserve any query parameters including invite code
    const url = new URL('/en', request.url);
    
    // Use forEach instead of for...of to avoid TypeScript iteration error
    searchParams.forEach((value, key) => {
      url.searchParams.set(key, value);
    });
    
    const response = NextResponse.redirect(url);
    
    // Set invite code cookie if present
    if (inviteCode) {
      response.cookies.set('invite_code', inviteCode, { 
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
        sameSite: 'lax'
      });
    }
    
    return response;
  }
  
  // Apply the next-intl middleware for all other paths
  const response = intlMiddleware(request);
  
  // Add invite code cookie to the response if present
  if (inviteCode && response instanceof NextResponse) {
    response.cookies.set('invite_code', inviteCode, { 
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/',
      sameSite: 'lax'
    });
  }
  
  return response;
};

export default middleware;

export const config = {
  matcher: [
    "/",
    "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*",
    "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)",
  ],
};
