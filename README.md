# 个人AI模板

## 全栈部分，位于nextjs目录

### 设置方式

- Set your environment variables

```bash
cp .env.example .env.local
```

- Set your theme in `app/theme.css`

[shadcn-ui-theme-generator](https://zippystarter.com/tools/shadcn-ui-theme-generator)

- Set your landing page content in `i18n/pages/landing`

- Set your i18n messages in `i18n/messages`

#### Resend

1. Add domain and dns record in https://resend.com/domains

### 部署方式

#### Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FAiMCPai%2FAiMCP-template-one&project-name=my-AiMCP-project&repository-name=my-AiMCP-project&redirect-url=https%3A%2F%2FAiMCP.ai&demo-title=AiMCP&demo-description=Ship%20Any%20AI%20Startup%20in%20hours%2C%20not%20days&demo-url=https%3A%2F%2FAiMCP.ai&demo-image=https%3A%2F%2Fpbs.twimg.com%2Fmedia%2FGgGSW3La8AAGJgU%3Fformat%3Djpg%26name%3Dlarge)

#### Deploy to Cloudflare

1. Customize your environment variables

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

2. Edit your environment variables in `.env.production`

3. Sync to wrangler.toml
```
pnpm run cf:env
```

4. Build and Deploy

```bash
pnpm run cf:build
pnpm run cf:deploy
```

## Vector Search Functionality

The application includes semantic search capabilities using vector embeddings stored directly in the MCPs tables. This enables advanced text similarity searches beyond basic keyword matching.

### Setup

1. Install PostgreSQL with pgvector extension:
   ```sql
   -- Run in PostgreSQL
   CREATE EXTENSION IF NOT EXISTS vector;
   ```

2. The database schema already includes vector columns in the following tables:
   - `schema.items` has a `doc_embedding_en` column for English embeddings
   - `schema.item_localizations` has a `doc_embedding` column for localized embeddings

3. Configure environment variables in `.env`:
   ```
   AI_API_KEY=your_openai_api_key
   AI_API_BASE_URL=optional_custom_api_url
   EMBEDDING_MODEL_NAME=text-embedding-3-small
   EMBEDDING_DIMENSIONS=512
   ```

### Updating Embeddings

To generate or update embeddings for MCPs:

1. Run the embedding update tool:
   ```bash
   cd tools
   npm run update-embeddings
   ```

This tool will:
- Process MCPs without embeddings and generate vectors for them
- Process localized MCP content and create respective embeddings
- Use the configured embedding model to create vectors from MCP fields (name, brief, location) and localization fields (brief, detail, processinfo)

### Using the Semantic Search API

The API endpoint for vector search is available at `/api/items/embedding-search`:

**Request:**
```json
{
  "text": "Your search query here",
  "language": "en", // or "zh" or other supported language codes
  "threshold": 0.7, // similarity threshold (0-1)
  "limit": 10 // max results to return
}
```

**Response:**
```json
{
  "results": [
    {
      "uuid": "item-uuid",
      "name": "MCP Name",
      "brief": "Brief description",
      "similarity": 0.89, // similarity score
      "item_location": "Location information"
    }
  ],
  "total": 25 // total matches before limit
}
```

The vector search works by:
1. Converting the search text into an embedding vector using the same model
2. Finding MCPs with similar embedding vectors using cosine similarity
3. Returning MCPs above the specified similarity threshold, sorted by relevance

This provides more relevant search results than traditional text search, especially for concept matching and semantic relationships.