# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

.kiro

# typescript
*.tsbuildinfo
next-env.d.ts
/.idea/
/pnpm-lock.yaml

.idea
yarn.lock

__pycache__

.*.json

.cache

*.log

.*.csv

bin
etc
nohup.out

dist

.cursor

tools/backup/data

.*.txt

.*.sql

# Added by Task Master AI
# Logs
logs
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 

.open-next
.wrangler

seo-reports