{"permissions": {"allow": ["Bash(git add:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(git commit:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(npx tsc:*)", "Bash(cp:*)", "Bash(git rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm type-check:*)", "Bash(pnpm lint:*)", "Bash(pnpm build:*)", "Bash(npx next build:*)", "<PERSON><PERSON>(echo:*)", "Bash(find:*)", "Bash(node:*)", "Bash(nvm use:*)", "Bash(pnpm run:*)", "Bash(timeout 30s pnpm build 2 >& 1)", "<PERSON><PERSON>(pkill:*)", "Bash(timeout 15s pnpm dev 2 >& 1)", "<PERSON><PERSON>(gtimeout:*)", "Bash(pnpm dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON>sh(wrangler d1 execute:*)", "Bash(nvm install:*)", "Bash(export NVM_DIR=\"$HOME/.nvm\")", "Bash([ -s \"$NVM_DIR/nvm.sh\" ])", "Bash(. \"$NVM_DIR/nvm.sh\")", "Bash(npx wrangler kv:namespace create:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npx wrangler kv namespace create:*)", "WebFetch(domain:umami.is)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(sed:*)", "Bash(for file in /Users/<USER>/Code/LinkTrackPro/nextjs/i18n/messages/*.json)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(for:*)", "Bash(do)", "Bash(pnpm add:*)", "Bash(python tools/scripts/manage_i18n.py:*)", "<PERSON><PERSON>(python i18n:*)", "Bash(python scripts/manage_i18n.py:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx eslint:*)", "Bash(timeout 60 pnpm build)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(timeout:*)", "Bash(./tools/scripts/svg-to-favicon.sh:*)", "Bash(npm install:*)", "Bash(pnpm test:*)", "Bash(npm test)", "Bash(pnpm install:*)", "Bash(pnpm prisma:generate:*)", "<PERSON><PERSON>(env)", "Bash(NODE_NO_WARNINGS=1 npx tsc --noEmit --skipLibCheck)", "Bash(pnpm approve-builds:*)", "Bash(pnpm config set:*)", "Bash(pnpm rebuild:*)", "Bash(SHARP_IGNORE_GLOBAL_LIBVIPS=1 pnpm install --no-optional)", "Bash(NODE_NO_WARNINGS=1 npx tsc --noEmit --skipLibCheck components/blocks/hero/index.tsx components/blocks/feature1/index.tsx components/ui/sidebar.tsx)", "Bash(SHARP_IGNORE_GLOBAL_LIBVIPS=1 pnpm run dev)", "Bash(pnpm remove:*)", "Bash(pnpm store prune:*)"], "deny": []}}