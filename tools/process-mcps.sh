#!/bin/bash

# Change directory to script location
cd "$(dirname "$0")"

# Load environment variables
if [ -f .env ]; then
  export $(cat .env | grep -v '^#' | xargs)
else
  echo "Error: .env file not found"
  exit 1
fi

# Define log file
LOG_DIR="./logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/process-mcps-$(date +%Y%m%d-%H%M%S).log"

# Start logging
echo "Starting automated MCP processing at $(date)" | tee -a $LOG_FILE

# Process submissions
echo "Step 1: Processing submissions..." | tee -a $LOG_FILE
pnpm process-submissions 2>&1 | tee -a $LOG_FILE

# Check if previous command was successful
if [ $? -ne 0 ]; then
  echo "Error processing submissions. See log for details." | tee -a $LOG_FILE
else
  echo "Submissions processed successfully." | tee -a $LOG_FILE
fi

# Auto-approve MCPs (if configured)
if [ "$AUTO_APPROVE_MCPS" == "true" ]; then
  echo "Step 2: Auto-approving MCPs..." | tee -a $LOG_FILE
  pnpm approve-mcps 2>&1 | tee -a $LOG_FILE
  
  # Check if previous command was successful
  if [ $? -ne 0 ]; then
    echo "Error auto-approving MCPs. See log for details." | tee -a $LOG_FILE
  else
    echo "MCPs auto-approved successfully." | tee -a $LOG_FILE
  fi
else
  echo "Step 2: Auto-approve is disabled. Skipping..." | tee -a $LOG_FILE
fi

# Update MCP translations
echo "Step 3: Updating MCP translations..." | tee -a $LOG_FILE
pnpm update-translations 2>&1 | tee -a $LOG_FILE

# Check if previous command was successful
if [ $? -ne 0 ]; then
  echo "Error updating MCP translations. See log for details." | tee -a $LOG_FILE
else
  echo "MCP translations updated successfully." | tee -a $LOG_FILE
fi

# Extract Item tools
echo "Step 4: Extracting Item tools..." | tee -a $LOG_FILE
pnpm extract-all-tools 2>&1 | tee -a $LOG_FILE

# Check if previous command was successful
if [ $? -ne 0 ]; then
  echo "Error extracting Item tools. See log for details." | tee -a $LOG_FILE
else
  echo "Item tools extracted successfully." | tee -a $LOG_FILE
fi

# Update GitHub metadata (run this less frequently, e.g., once a day)
CURRENT_HOUR=$(date +%H)
if [ "$CURRENT_HOUR" == "02" ]; then  # Run at 2 AM
  echo "Step 5: Updating GitHub metadata..." | tee -a $LOG_FILE
  pnpm update-github-meta 2>&1 | tee -a $LOG_FILE
  
  # Check if previous command was successful
  if [ $? -ne 0 ]; then
    echo "Error updating GitHub metadata. See log for details." | tee -a $LOG_FILE
  else
    echo "GitHub metadata updated successfully." | tee -a $LOG_FILE
  fi
else
  echo "Step 5: Skipping GitHub metadata update (only runs at 2 AM)." | tee -a $LOG_FILE
fi

# Create backup (if configured)
if [ "$AUTO_BACKUP" == "true" ]; then
  echo "Step 6: Creating backup..." | tee -a $LOG_FILE
  pnpm backup 2>&1 | tee -a $LOG_FILE
  
  # Check if previous command was successful
  if [ $? -ne 0 ]; then
    echo "Error creating backup. See log for details." | tee -a $LOG_FILE
  else
    echo "Backup created successfully." | tee -a $LOG_FILE
  fi
else
  echo "Step 6: Auto-backup is disabled. Skipping..." | tee -a $LOG_FILE
fi

echo "Automated MCP processing completed at $(date)" | tee -a $LOG_FILE 