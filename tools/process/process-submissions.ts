#!/usr/bin/env node

import * as dotenv from 'dotenv';
import { getPendingSubmissions, checkMcpExists, getAllTags, createMcp, updateSubmission } from '../common/db';
import { getGithubInfo } from '../common/github';
import { analyzeGithubInfoForMcp, translateMcpContent } from '../common/ai';
import { v4 as uuidv4 } from 'uuid';
import { McpLocalization, McpSubmission, Items } from '../../nextjs/types/items';

// Load environment variables
dotenv.config();

// Define supported languages for translations
const SUPPORTED_LANGUAGES = ['en', 'zh', 'fr', 'es', 'de', 'ja', 'ko', 'ru'];

// Define command line arguments
interface CliArgs {
  id?: number;
  all: boolean;
  translate: boolean;
  concurrency: number;
}

// Parse command line arguments
function parseArgs(): CliArgs {
  const args = process.argv.slice(2);
  
  // Parse ID if provided
  let id: number | undefined;
  const idArg = args.find(arg => !arg.startsWith('--') && /^\d+$/.test(arg));
  if (idArg) {
    id = parseInt(idArg, 10);
  }
  
  const allArg = args.includes('--all');
  const translateArg = args.includes('--translate');
  
  // Parse concurrency parameter
  const concurrencyArg = args.find(arg => arg.startsWith('--concurrency='));
  const concurrency = concurrencyArg 
    ? parseInt(concurrencyArg.split('=')[1], 10) 
    : 1;
  
  // Validate arguments
  if (!id && !allArg) {
    console.error('Usage: process-submissions [submission-id] [--all] [--translate] [--concurrency=N]');
    console.error('  --all: Process all pending submissions');
    console.error('  --translate: Translate content to supported languages');
    console.error('  --concurrency=N: Set number of concurrent operations (default: 1)');
    process.exit(1);
  }
  
  return {
    id,
    all: allArg,
    translate: translateArg,
    concurrency: isNaN(concurrency) || concurrency < 1 ? 1 : concurrency
  };
}

// Concurrency control function
async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  concurrency: number
): Promise<R[]> {
  const results: R[] = [];
  const inProgress: Promise<void>[] = [];
  const itemQueue = [...items];

  const startNext = async (): Promise<void> => {
    if (itemQueue.length === 0) return;
    
    const item = itemQueue.shift()!;
    const processingPromise = processor(item)
      .then(result => {
        results.push(result);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      })
      .catch(error => {
        console.error('Error in batch processing:', error);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      });
    
    inProgress.push(processingPromise);
  };

  // Start initial batch of tasks
  const initialBatchSize = Math.min(concurrency, items.length);
  for (let i = 0; i < initialBatchSize; i++) {
    await startNext();
  }

  // Wait for all tasks to complete
  await Promise.all(inProgress);
  return results;
}

async function processSubmission(submission: McpSubmission, translate: boolean) {
  console.log(`Processing submission ID ${submission.id} (${submission.name})...`);
  
  try {
    // Step 1: Check if MCP already exists by website_url
    console.log('Checking if MCP already exists...');
    const exists = await checkMcpExists(submission.website_url);
    
    if (exists) {
      console.log(`MCP with URL ${submission.website_url} already exists. Skipping.`);
      return true;
    }
    
    // Step 2: Get GitHub information if details are missing
    let githubInfo: any = { metadata: {}, readme: submission.detail };
    let submissionDetail = submission.detail;
    
    if (!submissionDetail || submissionDetail.trim() === '') {
      console.log('Getting GitHub information...');
      githubInfo = await getGithubInfo(submission.website_url);
      console.log('Get Github info done');
      
      if (githubInfo.error || !githubInfo.metadata) {
        throw new Error(`Failed to get GitHub info: ${githubInfo.error}`);
      }
      
      submissionDetail = githubInfo.readme || '';
    }
    
    // Step 3: Fetch existing tags using the cached DB function
    console.log('Fetching existing tags...');
    const tagsResult = await getAllTags();
    console.log('Fetched existing tags');
    const existingTags = tagsResult.error || !tagsResult.data 
      ? [] 
      : tagsResult.data.map((tagItem: { tag: string, count: number }) => tagItem.tag);
    
    console.log(`Found ${existingTags.length} existing tags`);
    
    // Step 4: Use AI to analyze GitHub info
    console.log('Analyzing GitHub info with AI...');
    const aiAnalysis = await analyzeGithubInfoForMcp(
      submission.name,
      githubInfo.metadata.description || '',
      submissionDetail,
      existingTags
    );
    console.log('AI analysis completed');
    
    // Generate a new UUID
    const uuid = uuidv4();
    
    // Step 5: Prepare localizations
    const localizations: Partial<McpLocalization>[] = [
      {
        language_code: 'en',
        brief: aiAnalysis.brief,
        detail: submissionDetail,
        processinfo: aiAnalysis.processinfo
      }
    ];
    
    // Step 6: Translate to other languages if requested
    if (translate) {
      for (const lang of SUPPORTED_LANGUAGES) {
        // Skip English as it's the source language
        if (lang === 'en') continue;
        
        try {
          console.log(`Translating to ${lang}...`);
          const translation = await translateMcpContent(
            aiAnalysis.brief,
            "",
            aiAnalysis.processinfo,
            lang
          );
          
          localizations.push({
            language_code: lang,
            brief: translation.brief,
            detail: submissionDetail,
            processinfo: translation.processinfo
          });
          
          console.log(`Successfully translated to ${lang}`);
        } catch (error) {
          console.error(`Failed to translate to ${lang}:`, error);
        }
      }
    }
    
    // Step 7: Update preprocessinfo with AI-generated content and UUID
    const preprocessinfo = {
      uuid,
      ai_processed: {
        en: {
          brief: aiAnalysis.brief,
          tags: aiAnalysis.tags,
          summary: aiAnalysis.processinfo
        }
      }
    };
    
    // Step 8: Create MCP using the new DB operation
    console.log('Creating MCP...');
    const mcpData: Partial<Items> & { localizations?: Partial<McpLocalization>[] } = {
      uuid,
      name: submission.name,
      brief: aiAnalysis.brief,
      item_avatar_url: submission.item_avatar_url || '',
      user_avatar_url: submission.user_avatar_url || '',
      website_url: submission.website_url,
      author_name: submission.author_name,
      is_recommended: false,
      is_official: false,
      allow_public: false, // Set to false initially, will be made public in approve step
      tags: aiAnalysis.tags,
      metadata: {
        ...githubInfo.metadata,
        // Add source submission info
        submission_id: submission.id,
        submission_email: submission.email
      },
      localizations
    };
    console.log("create mcp data", mcpData);  
    const createResult = await createMcp(mcpData);
    
    if (createResult.error) {
      throw new Error(`Failed to create MCP: ${createResult.error}`);
    }
    
    // Step 9: Update submission status using the new DB operation
    console.log('Updating submission status to processed...');
    const updateResult = await updateSubmission(submission.id, 'processed', uuid);
    
    if (updateResult.error) {
      console.error(`Failed to update submission status: ${updateResult.error}`);
      return false;
    }
    
    console.log(`Successfully processed submission ID ${submission.id}`);
    return true;
  } catch (error) {
    console.error(`Error processing submission ID ${submission.id}:`, error);
    return false;
  }
}

async function main() {
  // Parse command line arguments
  const args = parseArgs();
  
  console.log('Starting MCP submission processing...');
  console.log(`Translate: ${args.translate}`);
  console.log(`Concurrency: ${args.concurrency}`);
  
  try {
    // Get pending submissions
    let submissions = await getPendingSubmissions();
    
    // Filter by ID if provided
    if (args.id) {
      submissions = submissions.filter(sub => sub.id === args.id);
      if (submissions.length === 0) {
        console.error(`Submission with ID ${args.id} not found or not pending`);
        process.exit(1);
      }
    }
    
    console.log(`Found ${submissions.length} pending submissions to process`);
    
    // Process submissions with concurrency
    const results = await processBatch(
      submissions,
      (submission) => processSubmission(submission, args.translate),
      args.concurrency
    );
    
    // Calculate results
    const successCount = results.filter(result => result).length;
    const failureCount = results.length - successCount;
    
    console.log(`Processing completed. Successfully processed ${successCount} submissions. Failed: ${failureCount}`);
  } catch (error) {
    console.error('Error during submission processing:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 