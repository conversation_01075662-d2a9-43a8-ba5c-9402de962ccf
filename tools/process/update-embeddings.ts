import { createOpenAI } from '@ai-sdk/openai';
import { embed } from 'ai';
import dotenv from 'dotenv';
import path from 'path';
import { getAllItems, getAllMcpLocalizations, getAllEmbeddings } from '../common/db';
import { EmbeddingItem } from "../../nextjs/types/embeddingItem";
import { Items, McpLocalization } from '../../nextjs/types/items';
import { updateEmbeddings as updateEmbeddingsToDb } from '../common/db';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Initialize OpenAI client using Vercel AI SDK
const aiApiKey = process.env.AI_API_KEY || '';
const aiApiBaseUrl = process.env.AI_API_BASE_URL || '';
const embeddingModelName = process.env.EMBEDDING_MODEL_NAME || 'text-embedding-3-small';

if (!aiApiKey) {
  console.error('AI API key is missing. Please check your .env file.');
  process.exit(1);
}

const ai = createOpenAI({
  apiKey: aiApiKey,
  baseURL: aiApiBaseUrl || undefined,
});

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 1000;

/**
 * Retry wrapper for async functions
 * @param fn Function to retry
 * @param maxRetries Maximum number of retry attempts
 * @param delay Initial delay between retries in ms (will increase exponentially)
 * @returns Promise with the function result
 */
async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = MAX_RETRIES,
  delay: number = RETRY_DELAY_MS
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (attempt < maxRetries) {
        // Calculate exponential backoff
        const backoff = delay * Math.pow(2, attempt);
        console.log(`Embed API call failed. Retrying in ${backoff}ms... (Attempt ${attempt + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, backoff));
      }
    }
  }
  
  throw lastError || new Error('Max retries exceeded with unknown error');
}

/**
 * Process a batch of items with controlled concurrency
 */
async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  concurrency: number
): Promise<R[]> {
  const results: R[] = [];
  const inProgress: Promise<void>[] = [];
  const itemQueue = [...items];

  const startNext = async (): Promise<void> => {
    if (itemQueue.length === 0) return;
    
    const item = itemQueue.shift()!;
    const processingPromise = processor(item)
      .then(result => {
        results.push(result);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      })
      .catch(error => {
        console.error('Error in batch processing:', error);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      });
    
    inProgress.push(processingPromise);
  };

  // Start initial batch of tasks
  const initialBatchSize = Math.min(concurrency, items.length);
  for (let i = 0; i < initialBatchSize; i++) {
    await startNext();
  }

  // Wait for all tasks to complete
  await Promise.all(inProgress);
  return results;
}

/**
 * Process a single MCP to create or update its embeddings
 */
async function processOneMcpEmbedding(
  mcp: Items, 
  localizations: McpLocalization[], 
  embeddingMap: Map<string, EmbeddingItem>,
  languageCode: string
): Promise<{ status: 'created' | 'updated' | 'skipped' }> {
  try {
    const key = `${mcp.uuid}:${languageCode}`;
    const existingItem = embeddingMap.get(key);
    const mcpLocalization = localizations.find(l => l.item_uuid === mcp.uuid && l.language_code === languageCode);
    
    // Check if we need to update the embedding
    // Only update if there's no existing embedding or if the MCP was updated after the embedding
    if (!existingItem || new Date(mcp.updated_at) > new Date(existingItem.update_time)) {
      // Create text content for embeddings
      const briefContent = mcpLocalization?.brief || '';
      const processinfoContent = mcpLocalization?.processinfo || '';
      
      if (!briefContent.trim() && !processinfoContent.trim()) {
        console.warn(`MCP ${mcp.uuid} has no brief or processinfo content. Skipping.`);
        return { status: 'skipped' };
      }
      
      // Generate brief embedding with retry
      const { embedding: briefEmbedding } = await withRetry(() => embed({
        model: ai.embedding(embeddingModelName),
        value: briefContent,
      }));

      // Generate processinfo embedding if it exists with retry
      const processinfoEmbedding = processinfoContent.trim() ?
        await withRetry(() => embed({
          model: ai.embedding(embeddingModelName),
          value: processinfoContent,
        })) : null;
      
      // Create or update embedding item
      const embeddingItem: Partial<EmbeddingItem> = {
        item_uuid: mcp.uuid,
        language_code: languageCode,
        brief_vector: briefEmbedding,
        processinfo_vector: processinfoEmbedding?.embedding || undefined,
        update_time: new Date(mcp.updated_at),
      };
      
      if (existingItem && existingItem.id) {
        // If we have an existing item, update it with the ID
        await updateEmbeddingsToDb({...embeddingItem, id: existingItem.id} as EmbeddingItem, existingItem);
      } else {
        // If no existing item, create a new one
        await updateEmbeddingsToDb(embeddingItem as EmbeddingItem, {} as EmbeddingItem);
      }
      console.log(`Updated embedding for MCP ${mcp.uuid} (${languageCode})`);
      return { status: existingItem ? 'updated' : 'created' };
    } else {
      return { status: 'skipped' };
    }
  } catch (error) {
    console.error(`Error processing MCP ${mcp.uuid} (${languageCode}):`, error);
    throw error;
  }
}

async function updateEmbeddings(concurrency = 5) {
  console.log('Starting embedding update process...');
  console.log(`Concurrency level: ${concurrency}`);
  
  try {
    // Get all MCPs with their updated_at timestamp
    const mcps = await getAllItems();
    
    console.log(`Found ${mcps?.length || 0} MCPs to process.`);
    
    // Get all MCP localizations
    const localizations = await getAllMcpLocalizations();
    
    console.log(`Found ${localizations?.length || 0} MCP localizations to process.`);
    
    // Get existing embedding items
    const existingEmbeddings = await getAllEmbeddings();
    
    console.log(`Found ${existingEmbeddings?.length || 0} existing embedding items.`);
    
    // Create a map of existing embeddings for quick lookup
    const embeddingMap = new Map<string, EmbeddingItem>();
    existingEmbeddings?.forEach(item => {
      const key = `${item.item_uuid}:${item.language_code}`;
      const newItem = { 
        id: item.id,
        item_uuid: item.item_uuid,
        language_code: item.language_code,
        brief_vector: item.brief_vector,
        processinfo_vector: item.processinfo_vector,
        update_time: item.update_time
      };
      embeddingMap.set(key, newItem);
    });
    
    // Set up counters
    let updatedCount = 0;
    let createdCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    // Process English content
    console.log('Processing English content...');
    const englishResults = await processBatch(
      mcps,
      (mcp) => processOneMcpEmbedding(mcp, localizations, embeddingMap, 'en')
        .then(result => {
          if (result.status === 'created') createdCount++;
          else if (result.status === 'updated') updatedCount++;
          else if (result.status === 'skipped') skippedCount++;
          return result;
        })
        .catch(error => {
          errorCount++;
          console.error(`Error processing MCP ${mcp.uuid} (English):`, error);
          return { status: 'skipped' };
        }),
      concurrency
    );
    
    console.log(`English processing complete. Created: ${createdCount}, Updated: ${updatedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}`);
    
    // Process Chinese content
    console.log('Processing Chinese content...');
    const chineseResults = await processBatch(
      mcps,
      (mcp) => processOneMcpEmbedding(mcp, localizations, embeddingMap, 'zh')
        .then(result => {
          if (result.status === 'created') createdCount++;
          else if (result.status === 'updated') updatedCount++;
          else if (result.status === 'skipped') skippedCount++;
          return result;
        })
        .catch(error => {
          errorCount++;
          console.error(`Error processing MCP ${mcp.uuid} (Chinese):`, error);
          return { status: 'skipped' };
        }),
      concurrency
    );
    
    console.log('Embedding update process completed.');
    console.log(`Final stats - Created: ${createdCount}, Updated: ${updatedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error in updateEmbeddings:', error);
  }
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  
  // Parse concurrency parameter
  const concurrencyArg = args.find(arg => arg.startsWith('--concurrency='));
  const concurrency = concurrencyArg 
    ? parseInt(concurrencyArg.split('=')[1], 10) 
    : 5; // Default concurrency is 5
  
  return {
    concurrency: isNaN(concurrency) || concurrency < 1 ? 5 : concurrency
  };
}

// Run the update function
const args = parseArgs();
updateEmbeddings(args.concurrency);