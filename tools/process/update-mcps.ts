import dotenv from 'dotenv';
import { Octokit } from '@octokit/rest';
import path from 'path';
import { translateMcpContent } from '../common/ai';
import { getSupabaseClient, getAllItems } from '../common/db';

// Load environment variables
dotenv.config();

// Helper function to handle rate limit errors
async function withRateLimitRetry<T>(fn: () => Promise<T>, maxRetries = 3): Promise<T> {
  let retries = 0;
  
  while (true) {
    try {
      return await fn();
    } catch (error: any) {
      // Check if it's a rate limit error (TPM, RPM, etc.)
      const isRateLimit = 
        error?.statusCode === 429 || 
        (error?.message && error.message.includes("rate limit")) ||
        (error?.responseBody && error.responseBody.includes("TPM limit")) ||
        (error?.responseBody && error.responseBody.includes("RPM limit"));
      
      if (isRateLimit && retries < maxRetries) {
        retries++;
        console.log(`Rate limit reached. Waiting 1 minute before retry (${retries}/${maxRetries})...`);
        // Wait for 1 minute (60000 ms)
        await new Promise(resolve => setTimeout(resolve, 60000));
        console.log('Retrying...');
      } else {
        // Re-throw the error if it's not a rate limit error or we've exceeded retries
        throw error;
      }
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const translationsOnly = args.includes('--translations-only');
const githubOnly = args.includes('--github-only');

// Parse concurrency parameter
const concurrencyArg = args.find(arg => arg.startsWith('--concurrency='));
const concurrency = concurrencyArg 
  ? parseInt(concurrencyArg.split('=')[1], 10) 
  : 1;

// Validate concurrency
const validConcurrency = isNaN(concurrency) || concurrency < 1 ? 1 : concurrency;

console.log(`Concurrency level: ${validConcurrency}`);

// Supported languages from the constant
const SUPPORTED_LANGUAGES = ['en', 'zh', 'fr', 'es', 'de', 'ja', 'ko', 'ru'];

// Supabase client setup
const supabase = getSupabaseClient();

// GitHub API setup
const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

// Type definitions
interface Mcp {
  uuid: string;
  name: string;
  brief: string;
  detail?: string;
  website_url?: string;
  allow_public: boolean;
  metadata?: {
    forks?: number;
    stars?: number;
    watchers?: number;
    updated_at?: string;
  };
}

interface McpLocalization {
  item_uuid: string;
  language_code: string;
  brief: string;
  detail: string;
  processinfo: string;
}

// Helper function to extract owner and repo from GitHub URL
function extractGitHubInfo(url: string): { owner: string; repo: string } | null {
  if (!url) return null;
  
  try {
    const githubUrlPattern = /github\.com\/([^\/]+)\/([^\/]+)/;
    const match = url.match(githubUrlPattern);
    
    if (match && match.length >= 3) {
      return {
        owner: match[1],
        repo: match[2].replace('.git', '')
      };
    }
    return null;
  } catch (error) {
    console.error('Error extracting GitHub info:', error);
    return null;
  }
}

// Concurrency control function
async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  concurrency: number
): Promise<R[]> {
  const results: R[] = [];
  const promises: Promise<R>[] = [];
  
  // Process items in chunks based on concurrency
  for (let i = 0; i < items.length; i += concurrency) {
    const chunk = items.slice(i, i + concurrency);
    console.log(`Processing batch ${i/concurrency + 1}/${Math.ceil(items.length/concurrency)}, items ${i+1}-${Math.min(i+concurrency, items.length)} of ${items.length}`);
    
    // Create promise for each item in the chunk
    const chunkPromises = chunk.map(async (item) => {
      try {
        return await processor(item);
      } catch (error) {
        console.error('Error in batch processing:', error);
        throw error;
      }
    });
    
    // Wait for all promises in this chunk to resolve
    const chunkResults = await Promise.allSettled(chunkPromises);
    
    // Process results
    chunkResults.forEach(result => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      }
    });
    
    // Add a small delay between batches to avoid overwhelming APIs
    if (i + concurrency < items.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

// Process a single MCP for translations
async function processMcpTranslation(mcp: Mcp): Promise<boolean> {
  try {
    // Get existing localizations for this MCP
    const { data: existingLocalizations, error: locError } = await supabase
      .from('item_localizations')
      .select('language_code')
      .eq('item_uuid', mcp.uuid);
    
    if (locError) {
      console.error(`Error fetching localizations for MCP ${mcp.uuid}: ${locError.message}`);
      return false;
    }
    
    // Create a set of existing language codes
    const existingLanguages = new Set(existingLocalizations?.map(loc => loc.language_code) || []);
    
    // Get English localization as source for translations
    const { data: englishLocalization, error: enError } = await supabase
      .from('item_localizations')
      .select('brief, processinfo')
      .eq('item_uuid', mcp.uuid)
      .eq('language_code', 'en')
      .single();
    
    if (enError && enError.code !== 'PGRST116') { // Not PGRST116 (not found)
      console.error(`Error fetching English localization for MCP ${mcp.uuid}: ${enError.message}`);
      return false;
    }
    
    if (!englishLocalization) {
      console.warn(`No English localization found for MCP ${mcp.uuid} (${mcp.name}), using mcp.brief as fallback`);
      // If no English localization, we'll use just the mcp.brief and skip processinfo
    }
    
    // Source for translation
    const sourceBrief = englishLocalization?.brief || mcp.brief || '';
    const sourceProcessinfo = englishLocalization?.processinfo || '';
    
    // Find missing languages
    const missingLanguages = SUPPORTED_LANGUAGES.filter(lang => 
      lang !== 'en' && !existingLanguages.has(lang)
    );
    
    if (missingLanguages.length === 0) {
      console.log(`MCP ${mcp.uuid} (${mcp.name}) has all required translations.`);
      return true;
    }
    
    console.log(`MCP ${mcp.uuid} (${mcp.name}) is missing translations for: ${missingLanguages.join(', ')}`);
    
    // Add missing translations
    for (const targetLang of missingLanguages) {
      try {
        console.log(`Translating MCP ${mcp.uuid} to ${targetLang}...`);
        
        // Translate content with rate limit retry
        const translatedContent = await withRateLimitRetry(() => translateMcpContent(
          sourceBrief,
          '', // We're not translating detail
          sourceProcessinfo,
          targetLang
        ));
        
        // Insert translation into database
        const { error: insertError } = await supabase
          .from('item_localizations')
          .insert({
            item_uuid: mcp.uuid,
            language_code: targetLang,
            brief: translatedContent.brief,
            detail: mcp.detail || '', // Not translating detail
            processinfo: translatedContent.processinfo
          });
        
        if (insertError) {
          console.error(`Error inserting translation for MCP ${mcp.uuid} in ${targetLang}: ${insertError.message}`);
        } else {
          console.log(`Successfully added ${targetLang} translation for MCP ${mcp.uuid}.`);
        }
        
        // Add a small delay to avoid API rate limits
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.error(`Error processing translation for MCP ${mcp.uuid} to ${targetLang}:`, error);
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error processing translations for MCP ${mcp.uuid}:`, error);
    return false;
  }
}

// Process a single MCP for GitHub metadata update
async function processMcpGitHubMetadata(mcp: Mcp): Promise<boolean> {
  try {
    if (!mcp.website_url) return false;
    
    const githubInfo = extractGitHubInfo(mcp.website_url);
    
    if (!githubInfo) {
      console.warn(`Could not extract GitHub info from URL: ${mcp.website_url} for MCP ${mcp.uuid} (${mcp.name})`);
      return false;
    }
    
    console.log(`Fetching GitHub data for ${githubInfo.owner}/${githubInfo.repo}...`);
    
    // Fetch repository data from GitHub API with rate limit retry
    const repoResponse = await withRateLimitRetry(() => octokit.repos.get({
      owner: githubInfo.owner,
      repo: githubInfo.repo
    }));
    
    if (!repoResponse || !repoResponse.data) {
      console.warn(`No data returned from GitHub API for ${mcp.website_url}`);
      return false;
    }
    
    const repoData = repoResponse.data;
    
    // Prepare metadata update
    const metadata = {
      forks: repoData.forks_count,
      stars: repoData.stargazers_count,
      watchers: repoData.watchers_count,
      updated_at: repoData.updated_at
    };
    
    console.log(`Updating metadata for MCP ${mcp.uuid} (${mcp.name}): Stars=${metadata.stars}, Forks=${metadata.forks}, Watchers=${metadata.watchers}`);
    
    // Update MCP metadata in database
    const { error: updateError } = await supabase
      .from('mcps')
      .update({ 
        metadata,
        updated_at: new Date().toISOString()  // Also update the MCP's updated_at timestamp
      })
      .eq('uuid', mcp.uuid);
    
    if (updateError) {
      console.error(`Error updating metadata for MCP ${mcp.uuid}: ${updateError.message}`);
      return false;
    } else {
      console.log(`Successfully updated GitHub metadata for MCP ${mcp.uuid} (${mcp.name}).`);
      return true;
    }
    
    // Add a delay to avoid GitHub API rate limits
    await new Promise(resolve => setTimeout(resolve, 1000));
    
  } catch (error) {
    console.error(`Error processing GitHub metadata for MCP ${mcp.uuid}:`, error);
    return false;
  }
}

// Function to update MCP translations for missing languages
async function updateMcpTranslations() {
  console.log('Starting MCP translations update...');
  
  try {
    // Fetch all public MCPs
    const { data: mcpsData, error: mcpsError } = await supabase
      .from('mcps')
      .select('uuid, name, brief, allow_public')
      .eq('allow_public', true);
    
    if (mcpsError) {
      throw new Error(`Error fetching MCPs: ${mcpsError.message}`);
    }
    
    if (!mcpsData || mcpsData.length === 0) {
      console.log('No public MCPs found.');
      return;
    }
    
    console.log(`Found ${mcpsData.length} public MCPs to process for translations.`);
    
    // Convert to Mcp type
    let mcps: Mcp[] = mcpsData.map(mcp => ({
      ...mcp,
      allow_public: true // We know this is true from the query
    }));

    // Process MCPs with concurrency
    const results = await processBatch<Mcp, boolean>(
      mcps,
      mcp => processMcpTranslation(mcp),
      validConcurrency
    );
    
    const successCount = results.filter(success => success).length;
    console.log(`Completed MCP translations update. Successfully processed ${successCount}/${mcps.length} MCPs.`);
    
  } catch (error) {
    console.error('Error in updateMcpTranslations:', error);
  }
}

// Function to update MCP GitHub metadata
async function updateMcpGitHubMetadata() {
  console.log('Starting MCP GitHub metadata update...');
  
  try {
    // Fetch all MCPs with GitHub URLs
    const mcpsData = await getAllItems();
    
    if (!mcpsData || mcpsData.length === 0) {
      console.log('No MCPs with GitHub URLs found.');
      return;
    }
    
    console.log(`Found ${mcpsData.length} MCPs with GitHub URLs to update metadata.`);
    
    // Convert to Mcp type
    const mcps: Mcp[] = mcpsData;
    
    // Process MCPs with concurrency
    const results = await processBatch<Mcp, boolean>(
      mcps,
      mcp => processMcpGitHubMetadata(mcp),
      validConcurrency
    );
    
    const successCount = results.filter(success => success).length;
    console.log(`Completed MCP GitHub metadata update. Successfully processed ${successCount}/${mcps.length} MCPs.`);
    
  } catch (error) {
    console.error('Error in updateMcpGitHubMetadata:', error);
  }
}

// Main function
async function main() {
  console.log('Starting MCP update process...');
  
  // Check if we should run specific parts only
  if (!githubOnly) {
    // Step 1: Update MCP translations
    await updateMcpTranslations();
  }
  
  if (!translationsOnly) {
    // Step 2: Update GitHub metadata
    await updateMcpGitHubMetadata();
  }
  
  console.log('MCP update process completed.');
}

// Run the main function
main()
  .then(() => {
    console.log('Script execution completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script execution failed:', error);
    process.exit(1);
  }); 