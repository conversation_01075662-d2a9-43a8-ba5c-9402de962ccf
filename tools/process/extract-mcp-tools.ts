#!/usr/bin/env node

import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import { getAllItems, saveItemToolsToDb, getMcpLocalizationsByUuid, getItemToolsByUuid } from "../common/db"
import { ItemTool } from '../../nextjs/types/itemTools';
import { createOpenAI } from '@ai-sdk/openai';
import { generateText } from 'ai';
import { extractToolsFromDetails, extractToolsFromCode } from '../common/ai';
import { Items } from 'nextjs/types/mcps';

// Load environment variables
dotenv.config();

// Initialize OpenAI client
const openai = createOpenAI({
  baseURL: process.env.AI_API_BASE_URL,
  apiKey: process.env.AI_API_KEY,
});

// Temporary directory for cloning repositories
const TEMP_DIR = path.join(__dirname, 'temp');

// Define command line arguments
interface CliArgs {
  uuid?: string;
  all: boolean;
  concurrency: number;
  force: boolean;
}

// Parse command line arguments
function parseArgs(): CliArgs {
  const args = process.argv.slice(2);
  
  // Parse UUID if provided
  let uuid: string | undefined;
  const uuidArg = args.find(arg => !arg.startsWith('--') && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(arg));
  if (uuidArg) {
    uuid = uuidArg;
  }
  
  const allArg = args.includes('--all');
  const forceArg = args.includes('--force');
  
  // Parse concurrency parameter
  const concurrencyArg = args.find(arg => arg.startsWith('--concurrency='));
  const concurrency = concurrencyArg 
    ? parseInt(concurrencyArg.split('=')[1], 10) 
    : 1;
  
  // Validate arguments
  if (!uuid && !allArg) {
    console.error('Usage: extract-mcp-tools [mcp-uuid] [--all] [--concurrency=N]');
    console.error('  --all: Process all MCPs');
    console.error('  --concurrency=N: Set number of concurrent operations (default: 1)');
    process.exit(1);
  }
  
  return {
    uuid,
    all: allArg,
    concurrency: isNaN(concurrency) || concurrency < 1 ? 1 : concurrency,
    force: forceArg
  };
}

// Concurrency control function
async function processBatch<T, R>(
  items: T[],
  processor: (item: T, force: boolean) => Promise<R>,
  concurrency: number,
  force: boolean
): Promise<R[]> {
  const results: R[] = [];
  const inProgress: Promise<void>[] = [];
  const itemQueue = [...items];

  const startNext = async (): Promise<void> => {
    if (itemQueue.length === 0) return;
    
    const item = itemQueue.shift()!;
    const processingPromise = processor(item, force)
      .then(result => {
        results.push(result);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      })
      .catch(error => {
        console.error('Error in batch processing:', error);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      });
    
    inProgress.push(processingPromise);
  };

  // Start initial batch of tasks
  const initialBatchSize = Math.min(concurrency, items.length);
  for (let i = 0; i < initialBatchSize; i++) {
    await startNext();
  }

  // Wait for all tasks to complete
  await Promise.all(inProgress);
  return results;
}

function getGitHubRepoUrl(websiteUrl: string): string | null {
  // Check if the URL is a GitHub repository URL
  if (!websiteUrl || !websiteUrl.includes('github.com')) {
    return null;
  }

  try {
    // Parse URL to get owner and repo
    const url = new URL(websiteUrl);
    const parts = url.pathname.split('/').filter(Boolean);
    if (parts.length < 2) {
      return null;
    }

    const owner = parts[0];
    const repo = parts[1];
    return `https://github.com/${owner}/${repo}.git`;
  } catch (error) {
    console.error('Error parsing GitHub URL:', error);
    return null;
  }
}

async function cloneRepository(repoUrl: string, itemUuid: string): Promise<string> {
  // Create target directory
  const targetDir = path.join(TEMP_DIR, itemUuid);
  
  // Clean up existing directory if it exists
  if (fs.existsSync(targetDir)) {
    fs.rmSync(targetDir, { recursive: true, force: true });
  }
  fs.mkdirSync(targetDir, { recursive: true });

  try {
    // Clone repository
    console.log(`Cloning ${repoUrl} to ${targetDir}...`);
    execSync(`git clone --depth 1 ${repoUrl} ${targetDir}`, { stdio: 'pipe' });
    return targetDir;
  } catch (error) {
    console.error(`Error cloning repository ${repoUrl}:`, error);
    return '';
  }
}

async function findToolsFiles(repoDir: string): Promise<string[]> {
  try {
    // Use grep to find files containing "tools" in the repository
    const grepCommand = `grep -r --include="*.js" --include="*.ts" --include="*.py" --include="*.go" "tool\\|Tool\\|function" ${repoDir} | head -n 200`;
    const grepOutput = execSync(grepCommand, { encoding: 'utf-8' });
    
    // Parse grep output to get file paths
    const lines = grepOutput.split('\n');
    const filePaths = new Set<string>();
    
    for (const line of lines) {
      if (!line) continue;
      const filePath = line.split(':')[0];
      if (filePath) {
        filePaths.add(filePath);
      }
    }
    
    return Array.from(filePaths);
  } catch (error) {
    console.error(`Error finding tools files:`, error);
    return [];
  }
}

async function readFilesContent(filePaths: string[]): Promise<string> {
  let combinedContent = '';
  
  // Limit to max 10 files to avoid overwhelming
  const limitedPaths = filePaths.slice(0, 10);
  
  for (const filePath of limitedPaths) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      combinedContent += `\n\n--- File: ${path.basename(filePath)} ---\n\n${content}`;
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
    }
  }
  
  return combinedContent;
}

async function processToolsExtractionFromCode(mcp: Items): Promise<boolean> {
  try {
    console.log(`Processing MCP: ${mcp.name} (${mcp.uuid})`);
    
    // Get GitHub repository URL
    const repoUrl = getGitHubRepoUrl(mcp.website_url);
    if (!repoUrl) {
      console.log(`Not a GitHub repository: ${mcp.website_url}`);
      return false;
    }
    
    // Clone repository
    const repoDir = await cloneRepository(repoUrl, mcp.uuid);
    if (!repoDir) {
      console.log(`Failed to clone repository for MCP: ${mcp.name}`);
      return false;
    }
    
    // Find files containing tools
    const toolsFiles = await findToolsFiles(repoDir);
    if (toolsFiles.length === 0) {
      console.log(`No tools files found for MCP: ${mcp.name}`);
      // Clean up
      if (fs.existsSync(repoDir)) {
        fs.rmSync(repoDir, { recursive: true, force: true });
      }
      return false;
    }
    
    // Read files content
    const filesContent = await readFilesContent(toolsFiles);
    if (!filesContent) {
      console.log(`No content found in tools files for MCP: ${mcp.name}`);
      // Clean up
      if (fs.existsSync(repoDir)) {
        fs.rmSync(repoDir, { recursive: true, force: true });
      }
      return false;
    }
    
    // Analyze tools with AI
    const toolsData = await extractToolsFromCode(mcp.name, filesContent);
    if (!toolsData) {
      console.log(`Failed to analyze tools for MCP: ${mcp.name}`);
      // Clean up
      if (fs.existsSync(repoDir)) {
        fs.rmSync(repoDir, { recursive: true, force: true });
      }
      return false;
    }
    
    // Save Item tools to database
    const saveResult = await saveItemToolsToDb(toolsData);
    
    // Clean up
    if (fs.existsSync(repoDir)) {
      fs.rmSync(repoDir, { recursive: true, force: true });
    }
    
    if (saveResult) {
      console.log(`Successfully processed MCP: ${mcp.name}`);
      return true;
    } else {
      console.log(`Failed to save tools data for MCP: ${mcp.name}`);
      return false;
    }
  } catch (error) {
    console.error(`Error processing MCP ${mcp.name}:`, error);
    return false;
  }
}

async function processToolsExtractionFromReadme(mcp: Items, force: boolean = false): Promise<boolean> {
  console.log(`Processing Item tools extraction for UUID ${mcp.uuid}...`);
  
  try {
    // Check if Item tools has been processed before
    const existingTools = await getItemToolsByUuid(mcp.uuid);
    if (existingTools && existingTools.data && existingTools.data.length > 0 && !force) {
      console.log(`Item tools already processed for UUID ${mcp.uuid}`);
      return true;
    }
    
    // Get MCP localizations
    const localizations = await getMcpLocalizationsByUuid(mcp.uuid);
    
    if (!localizations || localizations.length === 0) {
      console.error(`No localizations found for MCP UUID ${mcp.uuid}`);
      return false;
    }
    
    // Get English localization for detail content (or first available)
    const enLocalization = localizations.find(loc => loc.language_code === 'en') || localizations[0];
    
    // Use AI to extract tools information from details
    console.log(`Extracting tools from MCP details (${mcp.uuid})...`);
    
    let extractedTools;
    try {
      extractedTools = await extractToolsFromDetails(enLocalization.detail);
    } catch (aiError) {
      console.error(`AI error while extracting tools for MCP ${mcp.uuid}:`, aiError);
      console.log(`Saving empty tools structure due to AI error for MCP ${mcp.uuid}...`);
      
      // Create empty tools data when AI extraction fails
      const emptyToolsData: ItemTool = {
        uuid: mcp.uuid,
        allow_public: mcp.allow_public,
        type: 'both', // Default
        tools: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      const { error: saveError } = await saveItemToolsToDb(emptyToolsData);
      
      if (saveError) {
        console.error(`Failed to save empty tools data for MCP ${mcp.uuid}:`, saveError);
        return false;
      }
      
      return true;
    }
    
    if (!extractedTools || (extractedTools.tools.length === 0 && !extractedTools.usage)) {
      console.log(`No tools information extracted for MCP ${mcp.uuid}`);
      // Save empty tools structure
      const emptyToolsData: ItemTool = {
        uuid: mcp.uuid,
        allow_public: mcp.allow_public,
        type: 'both', // Default
        tools: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log(`Saving empty tools structure for MCP ${mcp.uuid}...`);
      
      const { error: saveError } = await saveItemToolsToDb(emptyToolsData);
      
      if (saveError) {
        console.error(`Failed to save empty tools data for MCP ${mcp.uuid}:`, saveError);
        return false;
      }
      
      return true;
    }
    
    // Prepare the Item tools object
    const itemToolsData: ItemTool = {
      uuid: mcp.uuid,
      allow_public: mcp.allow_public,
      type: extractedTools.usage && extractedTools.usage.sse && extractedTools.usage.stdio 
        ? 'both' 
        : extractedTools.usage && extractedTools.usage.sse 
          ? 'sse' 
          : 'stdio',
      tools: extractedTools.tools,
      usage: extractedTools.usage,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Save to database
    console.log(`Saving extracted tools for MCP ${mcp.uuid}...`);
    
    try {
      const { error: saveError } = await saveItemToolsToDb(itemToolsData);
      
      if (saveError) {
        console.error(`Failed to save tools data for MCP ${mcp.uuid}:`, saveError);
        return false;
      }
      
      console.log(`Successfully processed tools extraction for MCP ${mcp.uuid}`);
      return true;
    } catch (saveError) {
      console.error(`Error saving tools data for MCP ${mcp.uuid}:`, saveError);
      return false;
    }
  } catch (error) {
    console.error(`Error processing tools extraction for MCP ${mcp.uuid}:`, error);
    return false;
  }
}

async function main() {
  try {
    // Parse command line arguments
    const args = parseArgs();
    
    console.log('Starting Item tools extraction...');
    console.log(`Concurrency: ${args.concurrency}`);
    
    // Ensure temp directory exists
    // if (!fs.existsSync(TEMP_DIR)) {
    //   fs.mkdirSync(TEMP_DIR, { recursive: true });
    // }
    
    // Fetch MCPs
    const mcps = await getAllItems(args.uuid);
    // const mcps = await getAllItems("b872f61c-f26b-464b-aa0e-93f54a16636a");
    console.log(`Found ${mcps.length} MCPs to process`);
    
    if (mcps.length === 0) {
      console.log('No MCPs found to process.');
      process.exit(0);
    }
    
    // Process MCPs with concurrency
    // const results = await processBatch(
    //   mcps,
    //   processToolsExtractionFromCode,
    //   args.concurrency
    // );

    const results = await processBatch(
        mcps,
        processToolsExtractionFromReadme,
        args.concurrency,
        args.force
      );
    
    // Calculate results
    const successCount = results.filter(result => result).length;
    const failureCount = results.length - successCount;
    
    console.log(`Processing completed. Successfully processed ${successCount} MCPs. Failed: ${failureCount}`);
  } catch (error) {
    console.error('Error in main process:', error);
  } finally {
    // Clean up temp directory
    // if (fs.existsSync(TEMP_DIR)) {
    //   fs.rmSync(TEMP_DIR, { recursive: true, force: true });
    // }
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { processToolsExtractionFromCode }; 