# MCP Processing Tools

This directory contains scripts for processing Model Context Protocols (MCPs).

## Available Scripts

### extract-mcp-tools.ts

Extracts tool information from MCP details using AI processing. The script analyzes the MCP's markdown content to identify tools, their parameters, and usage examples.

#### Usage

```bash
# Process a single MCP by UUID
pnpm extract-tools <mcp-uuid>

# Process all MCPs
pnpm extract-all-tools

# Process with concurrency control (parallel processing)
pnpm extract-tools <mcp-uuid> --concurrency=5
pnpm extract-tools --all --concurrency=5
```

#### How It Works

1. The script reads MCP details from the database
2. It uses AI to analyze the content and extract:
   - List of tools with name, description, and parameters
   - Usage examples for SSE and/or STDIO interfaces
3. The extracted data is saved to the `mcps.item_tools` table

#### Data Structure

The extracted tools data follows this structure:

```typescript
interface ItemTool {
  uuid: string;
  allow_public: boolean;
  type: 'sse' | 'stdio' | 'both'; // Type of MCP
  tools: Tool[];
  usage?: ToolUsage;
  created_at?: string;
  updated_at?: string;
}

interface Tool {
  name: string;
  description: string;
  parameters?: ToolParameter[];
  updated_at?: string;
}

interface ToolParameter {
  name: string;
  description?: string;
  type: string;
  required?: boolean;
}

interface ToolUsage {
  sse?: {
    code_example: string;
    description?: string;
  };
  stdio?: {
    code_example: string;
    description?: string;
  };
}
```

### Other Scripts

* `process-submissions.ts`: Processes pending MCP submissions
* `update-mcps.ts`: Updates MCPs with latest information
* `update-embeddings.ts`: Updates search embeddings for MCPs

## Automated Processing

The `process-mcps.sh` script in the root tools directory runs these scripts in sequence for automated processing. 