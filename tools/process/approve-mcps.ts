#!/usr/bin/env node

import * as dotenv from 'dotenv';
import { getAllSubmissions, updateMcp } from '../common/api';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config();

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  
  const id = args.find(arg => !arg.startsWith('--') && /^\d+$/.test(arg));
  const allFlag = args.includes('--all');
  
  if (!id && !allFlag) {
    console.error('Usage: approve-mcps [submission-id] [--all]');
    console.error('  --all: Approve all processed submissions');
    process.exit(1);
  }
  
  return {
    id: id ? parseInt(id, 10) : undefined,
    all: allFlag
  };
}

// Get the UUID from submission preprocessinfo
function getUuidFromSubmission(submission: any): string | null {
  if (!submission.preprocessinfo || !submission.preprocessinfo.uuid) {
    console.error(`Submission ID ${submission.id} does not have a UUID in preprocessinfo`);
    return null;
  }
  
  return submission.preprocessinfo.uuid;
}

// Approve a single MCP
async function approveMcp(submission: any): Promise<boolean> {
  console.log(`Approving MCP for submission ID ${submission.id} (${submission.name})...`);
  
  try {
    const uuid = getUuidFromSubmission(submission);
    
    if (!uuid) {
      return false;
    }
    
    // Update the MCP to be public
    const updateResult = await updateMcp(uuid, {
      allow_public: true
    });
    
    if (updateResult.error) {
      console.error(`Failed to update MCP ${uuid}: ${updateResult.error}`);
      return false;
    }
    
    console.log(`Successfully approved MCP ${uuid} for submission ID ${submission.id}`);
    return true;
  } catch (error) {
    console.error(`Error approving MCP for submission ID ${submission.id}:`, error);
    return false;
  }
}

async function main() {
  // Parse command line arguments
  const args = parseArgs();
  
  console.log('Starting MCP approval process...');
  
  try {
    // Get all submissions
    const { data: submissions, error } = await getAllSubmissions();
    
    if (error || !submissions) {
      console.error('Failed to get submissions:', error);
      process.exit(1);
    }
    
    // Filter processed submissions
    let processedSubmissions = submissions.filter(s => s.status === 'processed');
    
    // Filter by ID if provided
    if (args.id) {
      processedSubmissions = processedSubmissions.filter(s => s.id === args.id);
      
      if (processedSubmissions.length === 0) {
        console.error(`No processed submission found with ID ${args.id}`);
        process.exit(1);
      }
    }
    
    console.log(`Found ${processedSubmissions.length} processed submissions to approve`);
    
    // Approve each MCP
    let successCount = 0;
    let failureCount = 0;
    
    for (const submission of processedSubmissions) {
      const success = await approveMcp(submission);
      
      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
    }
    
    console.log(`Approval completed. Successfully approved ${successCount} MCPs. Failed: ${failureCount}`);
  } catch (error) {
    console.error('Error during MCP approval:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 