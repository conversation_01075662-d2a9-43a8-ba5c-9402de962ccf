import { createOpenAI } from '@ai-sdk/openai';
import { generateText } from 'ai';
import * as dotenv from 'dotenv';
import { Tool, ItemTool, ToolUsage } from '@/nextjs/types/ItemTools';

// Define interfaces for AI responses
export interface AiAnalysisResult {
  brief: string;
  tags: string[];
  processinfo: string;
}

export interface AiTranslationResult {
  brief: string;
  detail: string;
  processinfo: string;
}

// Interfaces for tool extraction
export interface ExtractedToolsResult {
  tools: Tool[];
  usage?: ToolUsage;
}

// Load environment variables
dotenv.config();

const AI_MODEL_NAME=process.env.AI_MODEL_NAME || 'gpt-4o-mini'

/**
 * Create an OpenAI compatible client
 * @returns OpenAI compatible client
 */
export function createAiClient() {
  // Get the API key and base URL from env variables
  const apiKey = process.env.AI_API_KEY || '';
  const baseUrl = process.env.AI_API_BASE_URL || '';
  
  if (!apiKey) {
    throw new Error('AI_API_KEY is not set');
  }
  
  // Create the client with the Vercel AI SDK
  return createOpenAI({
    apiKey,
    baseURL: baseUrl || undefined,
  });
}

/**
 * Safely parse JSON string with error handling and repair attempts
 * @param str JSON string to parse
 * @returns Parsed object or null if parsing fails
 */
function safeJsonParse(str: string): any | null {
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error("[safeJsonParse] JSON parsing failed, attempting repair");
    // Remove markdown code block markers if present
    str = str.replace(/^\s*```json\s*/i, "");
    str = str.replace(/\s*```\s*$/i, "");
    // Remove control characters
    str = str.replace(/[\u0000-\u001F]+/g, "");
    // Fix common JSON syntax errors
    str = str.replace(/([\[{])\s*,/g, "$1");
    str = str.replace(/,\s*([\]}])/g, "$1");
    
    console.log("[safeJsonParse] JSON after repair attempt:", str.substring(0, 200) + "...");
    
    try {
      return JSON.parse(str);
    } catch (e) {
      console.error("[safeJsonParse] Failed to parse JSON after repair:", e);
      return null;
    }
  }
}

/**
 * Send a prompt to the AI and get the response
 * @param prompt The prompt to send to the AI
 * @param model The model to use (defaults to the value in env or gpt-4o-mini)
 * @returns The AI's response text
 */
export async function openAIChat(prompt: string, model: string = AI_MODEL_NAME): Promise<string> {
  const client = createAiClient();
  
  try {
    const result = await generateText({
      model: client(model),
      messages: [{ role: 'user', content: prompt }]
    });
    
    console.log('[openAIChat] AI result:\n', result.text.substring(0, 200) + '...');
    
    if (result.text.length > 0) {
      return result.text;
    } else {
      console.error("[openAIChat] AI returned empty result");
      throw new Error('AI returned empty result');
    }
  } catch (error) {
    console.error("[openAIChat] Error calling AI:", error);
    throw error;
  }
}

/**
 * Translate content using AI
 * @param prompt The prompt to send to the AI for translation
 * @param model Optional model name to use
 * @returns The translated text
 */
export async function translate(prompt: string, model: string = AI_MODEL_NAME): Promise<string> {
  return await openAIChat(prompt, model);
}

// Helper function to handle rate limit errors
async function withRateLimitRetry<T>(fn: () => Promise<T>, maxRetries = 3): Promise<T> {
  let retries = 0;
  
  while (true) {
    try {
      return await fn();
    } catch (error: any) {
      // Check if it's a rate limit error (TPM, RPM, etc.) or connection issue
      const isRateLimit = 
        error?.statusCode === 429 || 
        (error?.message && (
          error.message.includes("rate limit") ||
          error.message.includes("Cannot connect to API") ||
          error.message.includes("other side closed")
        )) ||
        (error?.responseBody && (
          error.responseBody.includes("TPM limit") ||
          error.responseBody.includes("RPM limit")
        )) ||
        error?.reason === 'maxRetriesExceeded' ||
        error?.code === 'UND_ERR_SOCKET' ||
        (error?.cause && 
          (error.cause.code === 'UND_ERR_SOCKET' || 
           error.cause.message && error.cause.message.includes("other side closed")));
      
      if (isRateLimit && retries < maxRetries) {
        retries++;
        console.log(`API rate limit or connection issue reached. Waiting 1 minute before retry (${retries}/${maxRetries})...`);
        console.log(`Error details: ${error.message || error.reason || error.code || JSON.stringify(error).substring(0, 100)}...`);
        // Wait for 1 minute (60000 ms)
        await new Promise(resolve => setTimeout(resolve, 60000));
        console.log('Retrying...');
      } else {
        // Re-throw the error if it's not a rate limit error or we've exceeded retries
        throw error;
      }
    }
  }
}

// Analyze GitHub info and generate MCP metadata
export async function analyzeGithubInfoForMcp(
  name: string,
  description: string,
  readme: string,
  existingTags: string[] = []
): Promise<AiAnalysisResult> {
  const client = createAiClient();
  
  try {
    const prompt = `
You are a helpful AI assistant helping to analyze GitHub repositories for Model Context Protocols (MCPs).
I'll provide you with information about a GitHub repository, and I want you to generate the following:

1. A brief description (under 80 characters)
2. A list of up to 5 relevant tags in the format "Category/Subcategory" (e.g., "Tool/GitHub", "AI/ChatGPT")
3. A brief process info section describing(markdown format):
- what is this MCP
- how to use this MCP
- what this MCP can be used for

Repository Name: ${name}
Description: ${description || 'No description provided'}
README Content: ${readme || 'No README content available'}

Existing tags in our system: ${existingTags.join(', ')}

IMPORTANT TAG GUIDELINES:
1. STRONGLY PREFER using existing tags from the list provided above
2. Only create new tags if absolutely necessary
3. All tags MUST follow the "Category/Subcategory" format (e.g., "Tool/GitHub", "AI/Assistant")
4. Use at most 3 tags total

Please respond in the following JSON format:
{
  "brief": "Brief description here",
  "tags": ["Category/Subcategory", "Category/Subcategory"],
  "processinfo": "Process info here, only contain 3 h2 headers, and the content of the h2 headers"
}
`;

    const result = await withRateLimitRetry(() => generateText({
      model: client(AI_MODEL_NAME),
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.5,
    }));

    const content = result.text || '';
    console.log(`AI analysis response: ${content}`);
    if (content.length == 0) {
      throw new Error('AI returned empty result');
    }
    
    const parsedContent = safeJsonParse(content);
    if (!parsedContent) {
      return {
        brief: 'Failed to generate brief description',
        tags: [],
        processinfo: 'Failed to generate process info',
      };
    }
    
    // Validate that all tags follow the required format
    const validTags = Array.isArray(parsedContent.tags) 
      ? parsedContent.tags.filter((tag: string) => typeof tag === 'string' && /^[A-Za-z0-9]+\/[A-Za-z0-9]+$/.test(tag))
      : [];
    
    return {
      brief: parsedContent.brief || 'No brief available',
      tags: validTags,
      processinfo: parsedContent.processinfo || 'No process info available',
    };
  } catch (error) {
    console.error('AI analysis error:', error);
    return {
      brief: 'Error generating brief description',
      tags: [],
      processinfo: 'Error generating process info',
    };
  }
}

// Translate MCP content to a specific language
export async function translateMcpContent(
  originalBrief: string,
  originalDetail: string,
  originalProcessInfo: string,
  targetLanguage: string
): Promise<AiTranslationResult> {
  const client = createAiClient();
  
  try {
    const prompt = `
You are a professional translator. Please translate the following content from English to ${targetLanguage}.
Maintain the format of the original text, including any markdown formatting.

Original Brief:
${originalBrief}

Original Detail:
${originalDetail}

Original Process Info:
${originalProcessInfo}

Please respond in the following JSON format:
{
  "brief": "Translated brief here",
  "detail": "Translated detail here",
  "processinfo": "Translated process info here"
}
`;

    const result = await withRateLimitRetry(() => generateText({
      model: client(AI_MODEL_NAME),
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
    }));

    const content = result.text || '';
    
    const parsedContent = safeJsonParse(content);
    if (!parsedContent) {
      return {
        brief: originalBrief,
        detail: originalDetail,
        processinfo: originalProcessInfo,
      };
    }
    
    return {
      brief: parsedContent.brief || originalBrief,
      detail: parsedContent.detail || originalDetail,
      processinfo: parsedContent.processinfo || originalProcessInfo,
    };
  } catch (error) {
    console.error('AI translation error:', error);
    return {
      brief: originalBrief,
      detail: originalDetail,
      processinfo: originalProcessInfo,
    };
  }
}

export async function extractToolsFromCode(mcpName: string, codeContent: string): Promise<ItemTool | null> {
  const client = createAiClient();
  
  try {
    console.log(`Analyzing tools for ${mcpName}...`);
    
    const prompt = `
    Analyze the provided code and identify tools (functions) provided by this MCP (Model Context Protocol) framework.
    
    MCP Name: ${mcpName}
    
    1. Determine if this MCP supports SSE (Server-Sent Events), STDIO (Standard Input/Output), or both.
    2. Extract all tool functions with:
       - Function name
       - Description
       - Parameters (with names, types, and whether they're required)
    3. Identify usage examples for both SSE and STDIO if available
    
    Format your response as a valid JSON object with this structure:
    {
      "type": "sse" | "stdio" | "both",
      "tools": [
        {
          "name": "function_name",
          "description": "description of what the function does",
          "parameters": [
            {
              "name": "parameter_name",
              "description": "parameter description",
              "type": "string|number|boolean|object|array",
              "required": true|false
            }
          ],
          "updated_at": "YYYY-MM-DD"
        }
      ],
      "usage": {
        "sse": {
          "code_example": "code example for SSE usage",
          "description": "description of how to use with SSE"
        },
        "stdio": {
          "code_example": "code example for STDIO usage, ",
          "description": "description of how to use with STDIO"
        }
      }
    }
    
    ONLY return the JSON object, no other text.
    `;

    const result = await withRateLimitRetry(() => generateText({
      model: client(AI_MODEL_NAME),
      messages: [
        { role: "system", content: "You are an expert code analyzer specializing in MCP (Model Context Protocol) frameworks. You extract tool definitions and usage patterns from code." },
        { role: "user", content: prompt },
        { role: "user", content: codeContent }
      ],
      temperature: 0.2,
    }));

    const content = result.text;
    if (!content) {
      throw new Error("No response from AI");
    }

    // Parse JSON response
    const toolsData: ItemTool = JSON.parse(content);
    return toolsData;
  } catch (error) {
    console.error(`Error analyzing tools with AI:`, error);
    return null;
  }
}

// Extract tool information from MCP details
export async function extractToolsFromDetails(
  detailContent: string
): Promise<ExtractedToolsResult> {
  const client = createAiClient();
  
  try {
    const prompt = `
You are a helpful AI assistant specialized in extracting structured tool information from documentation.
I'll provide the markdown content of an MCP (Model Context Protocol) and I want you to extract:

1. Tools: List of tools with their name, description, and parameters
2. Usage examples: Code examples for using these tools via SSE and/or stdio

Documentation Content:
${detailContent}

Please extract the structured information and respond in the following JSON format:

{
  "tools": [
    {
      "name": "tool_name",
      "description": "Tool description",
      "parameters": [
        {
          "name": "parameter_name",
          "description": "Parameter description",
          "type": "string|number|boolean|array|object",
          "required": true|false
        }
      ]
    }
  ],
  "usage": {
    "sse": {
      "code_example": "(keep empty as not supported in mistralocr.com yet)",
      "description": "Cooming soon"
    },
    "stdio": {
      "code_example": "Code config example for using via stdio method, for example:
        {
          "mcpServers": {
            "filesystem": {
              "command": "npx",
              "args": [
                "-y",
                "@modelcontextprotocol/server-filesystem",
                "/Users/<USER>/Desktop",
                "/path/to/other/allowed/dir"
              ]
            }
          }
        }
      ",
      "description": "Description of stdio usage"
    }
  }
}

If you can't extract tools or usage information, provide an empty array or omit the section.
Try to be as accurate as possible in extracting parameter types and requirements.
The response MUST be valid JSON.
`;

    const result = await withRateLimitRetry(() => generateText({
      model: client(AI_MODEL_NAME),
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2,
    }));

    const content = result.text || '';
    console.log(`AI tools extraction response: ${content.substring(0, 200)}...`);
    
    if (content.length === 0) {
      console.error('AI returned empty result for tools extraction');
      return { tools: [] };
    }
    
    const parsedContent = safeJsonParse(content);
    if (!parsedContent) {
      console.error('Failed to parse AI response for tools extraction');
      return { tools: [] };
    }
    
    // Validate tools
    const tools: Tool[] = [];
    if (Array.isArray(parsedContent.tools)) {
      for (const tool of parsedContent.tools) {
        if (typeof tool.name === 'string' && typeof tool.description === 'string') {
          const validTool: Tool = {
            name: tool.name,
            description: tool.description,
            updated_at: new Date().toISOString()
          };
          
          // Add parameters if available
          if (Array.isArray(tool.parameters)) {
            validTool.parameters = tool.parameters
              .filter((param: any) => typeof param.name === 'string')
              .map((param: any) => ({
                name: param.name,
                description: typeof param.description === 'string' ? param.description : '',
                type: typeof param.type === 'string' ? param.type : 'string',
                required: typeof param.required === 'boolean' ? param.required : false
              }));
          }
          
          tools.push(validTool);
        }
      }
    }
    
    // Validate usage
    let usage: ToolUsage | undefined;
    if (parsedContent.usage) {
      usage = {};
      
      // SSE usage
      if (parsedContent.usage.sse && 
          typeof parsedContent.usage.sse.code_example === 'string') {
        usage.sse = {
          code_example: parsedContent.usage.sse.code_example,
          description: typeof parsedContent.usage.sse.description === 'string' 
            ? parsedContent.usage.sse.description 
            : undefined
        };
      }
      
      // stdio usage
      if (parsedContent.usage.stdio && 
          typeof parsedContent.usage.stdio.code_example === 'string') {
        usage.stdio = {
          code_example: parsedContent.usage.stdio.code_example,
          description: typeof parsedContent.usage.stdio.description === 'string' 
            ? parsedContent.usage.stdio.description 
            : undefined
        };
      }
      
      // If no valid usage was extracted, set to undefined
      if (!usage.sse && !usage.stdio) {
        usage = undefined;
      }
    }
    
    return {
      tools,
      usage
    };
  } catch (error) {
    console.error('AI tools extraction error:', error);
    return {
      tools: []
    };
  }
} 