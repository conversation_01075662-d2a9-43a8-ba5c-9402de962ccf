import * as fs from 'fs';
import * as path from 'path';
import { parse } from 'csv-parse/sync';

// Define CSV row interface
export interface CsvMcpRow {
  uuid?: string;
  website_url: string;
  [key: string]: any;
}

// Read CSV file
export function readCsvFile(filePath: string): CsvMcpRow[] {
  try {
    // Check if path is absolute or relative
    const resolvedPath = path.isAbsolute(filePath) 
      ? filePath 
      : path.resolve(process.cwd(), filePath);
    
    // Check if file exists
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`CSV file not found: ${resolvedPath}`);
    }
    
    // Read and parse CSV file
    const content = fs.readFileSync(resolvedPath, { encoding: 'utf-8' });
    const records = parse(content, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });
    
    // Validate each row has website_url
    const validRecords = records.filter((row: any) => {
      if (!row.website_url) {
        console.warn('Row missing website_url, skipping:', row);
        return false;
      }
      return true;
    });
    
    return validRecords;
  } catch (error) {
    console.error('Error reading CSV file:', error);
    throw error;
  }
} 