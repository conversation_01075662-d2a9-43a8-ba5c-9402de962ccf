// @ts-ignore - Axios types are bundled with the package
import axios from 'axios';
import { Items, McpLocalization, McpSubmission } from '../../nextjs/types/items';

// Define API response interfaces
export interface ApiResponse<T> {
  data?: T | null;
  error?: string | null;
}

// Define API configuration
interface ApiConfig {
  baseUrl: string;
  adminToken: string;
}

// Create API client
export function createApiClient(): ApiConfig {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
  const adminToken = process.env.ADMIN_API_TOKEN || '';
  
  if (!baseUrl) {
    console.warn('NEXT_PUBLIC_WEB_URL is not set, defaulting to http://localhost:3000');
  }
  
  if (!adminToken) {
    console.warn('ADMIN_API_TOKEN is not set, admin API calls may fail');
  }
  
  return { baseUrl, adminToken };
}

// Prefetch MCP information from GitHub URL
export async function prefetchMcpInfo(githubUrl: string): Promise<ApiResponse<any>> {
  const { baseUrl } = createApiClient();
  
  try {
    const response = await axios.get(`${baseUrl}/api/mcps/prefetch`, {
      params: { url: githubUrl }
    });
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to prefetch MCP info' 
    };
  }
}

// Submit MCP
export async function submitMcp(data: {
  name: string;
  author_name: string;
  website_url: string;
  user_avatar_url?: string;
  item_avatar_url?: string;
  email?: string;
  subscribe_newsletter?: boolean;
  detail?: string;
  preprocessinfo?: any;
}): Promise<ApiResponse<any>> {
  const { baseUrl } = createApiClient();
  
  try {
    const response = await axios.post(`${baseUrl}/api/mcps/submit`, data);
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to submit MCP' 
    };
  }
}

// Get all submissions
export async function getAllSubmissions(): Promise<ApiResponse<McpSubmission[]>> {
  const { baseUrl, adminToken } = createApiClient();
  
  try {
    const response = await axios.get(`${baseUrl}/api/admin/submissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to get submissions' 
    };
  }
}

// Get pending submissions
export async function getPendingSubmissions(): Promise<ApiResponse<McpSubmission[]>> {
  const { baseUrl, adminToken } = createApiClient();
  
  try {
    const response = await axios.get(`${baseUrl}/api/admin/submissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` },
      params: { status: 'pending' }
    });
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to get pending submissions' 
    };
  }
}

// Update submission status
export async function updateSubmission(id: number, status: string, uuid?: string): Promise<ApiResponse<any>> {
  const { baseUrl, adminToken } = createApiClient();
  
  try {
    // Use dedicated endpoints for different statuses
    let endpoint;
    if (['approved', 'rejected', 'processed'].includes(status)) {
      endpoint = `${baseUrl}/api/admin/submissions/${id}/${status}`;
    } else {
      endpoint = `${baseUrl}/api/admin/submissions/${id}`;
    }
      
    const method = ['approved', 'rejected', 'processed'].includes(status) ? 'post' : 'put';
    
    const response = await axios({
      method,
      url: endpoint,
      data: {
        status,
        preprocessinfo: uuid ? { uuid } : undefined
      },
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to update submission' 
    };
  }
}

// Create MCP (admin API)
export async function createMcp(mcp: Partial<Items>): Promise<ApiResponse<Items>> {
  const { baseUrl, adminToken } = createApiClient();
  
  try {
    const response = await axios.post(`${baseUrl}/api/admin/mcps`, mcp, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to create MCP' 
    };
  }
}

// Update MCP
export async function updateMcp(uuid: string, data: Partial<Items>): Promise<ApiResponse<Items>> {
  const { baseUrl, adminToken } = createApiClient();
  
  try {
    const response = await axios.put(`${baseUrl}/api/admin/mcps/${uuid}`, data, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to update MCP' 
    };
  }
}

// Get all tags
export async function getAllTags(): Promise<ApiResponse<any[]>> {
  const { baseUrl } = createApiClient();
  
  try {
    const response = await axios.get(`${baseUrl}/api/mcps/tags`);

    console.log(response)
    
    return { data: response.data, error: null };
  } catch (error: any) {
    return { 
      data: null, 
      error: error.response?.data?.message || error.message || 'Failed to get tags' 
    };
  }
} 