import { fetchGithubRepoInfo } from "../../nextjs/lib/github";

export interface GithubInfo {
  metadata: {
    forks: number;
    stars: number;
    watchers: number;
    updated_at: string;
    description: string | null;
    owner: string;
    repo: string;
  } | null;
  readme: string | null;
  error?: string;
}

// Parse GitHub URL
export function parseGithubUrl(url: string): { owner: string; repo: string } | null {
  try {
    const parsedUrl = new URL(url);
    if (parsedUrl.hostname !== 'github.com') {
      return null;
    }
    const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0);
    if (pathParts.length >= 2) {
      return { owner: pathParts[0], repo: pathParts[1] };
    }
    return null;
  } catch (e) {
    console.error("Invalid GitHub URL:", url, e);
    return null;
  }
}

// Fetch GitHub repository information
export async function getGithubInfo(githubUrl: string): Promise<GithubInfo> {
  try {
    return await fetchGithubRepoInfo(githubUrl);
  } catch (error) {
    console.error("Error fetching GitHub info:", error);
    return {
      metadata: null,
      readme: null,
      error: `Failed to fetch GitHub repository information: ${error instanceof Error ? error.message : String(error)}`
    };
  }
} 