import { createClient } from "@supabase/supabase-js";
import { Items, McpLocalization, McpSubmission } from "nextjs/types/mcps";
import { ItemTool } from "nextjs/types/itemTools";
import { EmbeddingItem } from "nextjs/types/embeddingItem";
import * as fs from 'fs';
import * as path from 'path';

// Get Supabase client
export function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
  
  let supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "";
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  }

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Supabase URL or key is not set");
  }

  const client = createClient(supabaseUrl, supabase<PERSON>ey, {
    db: {
      schema: process.env.SUPABASE_SCHEMA || "public"
    }
  });

  return client;
}

// Export all MCPs
export async function getAllItems(uuid:string = ""): Promise<Items[]> {
  const client = getSupabaseClient();
  const batchSize = 1000;
  let allData: Items[] = [];
  let from = 0;

  if (uuid) {
    const { data, error } = await client
      .from("mcps")
      .select("*")
      .eq("uuid", uuid);
    if (error) {
      throw new Error(`Failed to fetch MCPs: ${error.message}`);
    }
    if (!data || data.length === 0) return [];
    return data;
  }

  while (true) {
    const { data, error } = await client
      .from("mcps")
      .select("*")
      .range(from, from + batchSize - 1);
    if (error) {
      throw new Error(`Failed to fetch MCPs: ${error.message}`);
    }
  
    if (!data || data.length === 0) break;
    allData = allData.concat(data);
    if (data.length < batchSize) break;
    from += batchSize;
  }
  return allData;
}

// Export MCP localizations
export async function getAllMcpLocalizations(): Promise<McpLocalization[]> {
  const client = getSupabaseClient();
  const batchSize = 1000;
  let allData: McpLocalization[] = [];
  let from = 0;
  while (true) {
    const { data, error } = await client
      .from("item_localizations")
      .select("*")
      .range(from, from + batchSize - 1);
    if (error) {
      throw new Error(`Failed to fetch MCP localizations: ${error.message}`);
    }
    if (!data || data.length === 0) break;
    allData = allData.concat(data);
    if (data.length < batchSize) break;
    from += batchSize;
  }
  return allData;
}

export async function updateEmbeddings(item: EmbeddingItem, existingItem: EmbeddingItem): Promise<void> {
  const client = getSupabaseClient();
  try {
    if (!existingItem.id) {
      // For new items, don't include the id field as it's generated by the database
      const { item_uuid, language_code, brief_vector, processinfo_vector, update_time } = item;
      const newItem = { item_uuid, language_code, brief_vector, processinfo_vector, update_time };
      
      const { error: insertError } = await client
                .from('embedding_items')
                .insert(newItem);
      if (insertError) {
        throw new Error(`Failed to insert embedding item: ${insertError.message}`);
      }
      console.log(`Inserted embedding item for ${item.item_uuid} (${item.language_code})`);
    } else {
      // For existing items, make sure to include the id for the update
      const { error: updateError } = await client
                .from('embedding_items')
                .update(item)
                .eq('id', existingItem.id);
      if (updateError) {
        throw new Error(`Failed to update embedding item: ${updateError.message}`);
      }
      console.log(`Updated embedding item ${existingItem.id}`);
    }
  }
  catch (error) {
    console.error(error);
  }
}

export async function getAllEmbeddings(): Promise<EmbeddingItem[]> {
  const client = getSupabaseClient();
  const batchSize = 1000;
  let allData: EmbeddingItem[] = [];
  let from = 0;
  while (true) {
    const { data, error } = await client
     .from("embedding_items")
     .select("*")
     .range(from, from + batchSize - 1);
    if (error) {
      throw new Error(`Failed to fetch MCP Embeddings: ${error.message}`);
    }

    if (!data || data.length === 0) break;
    allData = allData.concat(data);
    if (data.length < batchSize) break;
    from += batchSize;
  }

  return allData;
}

// Export MCP submissions
export async function exportSubmissions(): Promise<McpSubmission[]> {
  const client = getSupabaseClient();
  const batchSize = 1000;
  let allData: McpSubmission[] = [];
  let from = 0;
  while (true) {
    const { data, error } = await client
      .from("submissions")
      .select("*")
      .range(from, from + batchSize - 1);
    if (error) {
      throw new Error(`Failed to fetch MCP submissions: ${error.message}`);
    }
    if (!data || data.length === 0) break;
    allData = allData.concat(data);
    if (data.length < batchSize) break;
    from += batchSize;
  }
  return allData;
}

// Save data to backup file
export function saveBackup<T>(data: T[], name: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(process.cwd(), 'backup/data');
  
  // Create backups directory if it doesn't exist
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const filename = path.join(backupDir, `${name}_${timestamp}.json`);
  fs.writeFileSync(filename, JSON.stringify(data, null, 2));
  
  return filename;
}

// Get pending MCP submissions
export async function getPendingSubmissions(): Promise<McpSubmission[]> {
  const client = getSupabaseClient();
  const { data, error } = await client
    .from("submissions")
    .select("*")
    .eq("status", "pending")
    .order("created_at", { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch pending MCP submissions: ${error.message}`);
  }

  return data || [];
}

/**
 * Fetch tools for a specific MCP by UUID
 * @param uuid - The UUID of the MCP
 * @returns Item tools data or null if not found
 */
export async function getItemToolsByUuid(uuid: string) {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from('item_tools')
    .select('*')
    .eq('uuid', uuid)
    .single();
    
  return { data, error };
}

// Define function to save Item tools directly with Supabase
export async function saveItemToolsToDb(itemTools: ItemTool) {
  try {
    const supabase = getSupabaseClient();
    
    // Update the timestamps if not set
    if (!itemTools.created_at) {
      itemTools.created_at = new Date().toISOString();
    }
    itemTools.updated_at = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('item_tools')
      .upsert(itemTools, { onConflict: 'uuid' })
      .select()
      .single();
      
    return { data, error };
  } catch (error) {
    return { data: null, error };
  }
}

// Get MCP by UUID
export async function getMcpByUuid(uuid: string): Promise<Items | null> {
  const client = getSupabaseClient();
  const { data, error } = await client
    .from("mcps")
    .select("*")
    .eq("uuid", uuid)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Not found
    }
    throw new Error(`Failed to fetch MCP by UUID: ${error.message}`);
  }

  return data;
}

// Get MCP localizations by UUID
export async function getMcpLocalizationsByUuid(uuid: string): Promise<McpLocalization[]> {
  const client = getSupabaseClient();
  const { data, error } = await client
    .from("item_localizations")
    .select("*")
    .eq("item_uuid", uuid);

  if (error) {
    throw new Error(`Failed to fetch MCP localizations: ${error.message}`);
  }

  return data || [];
}

// Check if MCP exists by website_url
export async function checkMcpExists(websiteUrl: string): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('mcps')
      .select('uuid')
      .eq('website_url', websiteUrl)
      .limit(1);
    
    if (error) {
      console.error('Error checking MCP existence:', error);
      return false;
    }
    
    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking MCP existence:', error);
    return false;
  }
}

// Get all tags with counts
let cachedTags: { tag: string, count: number }[] | null = null;
let tagsCacheTime: number = 0;
const TAGS_CACHE_TTL = 3600000; // 1 hour in milliseconds

export async function getAllTags(): Promise<{ data: { tag: string, count: number }[] | null, error: Error | null }> {
  try {
    // Check if we have valid cached tags
    const now = Date.now();
    if (cachedTags && (now - tagsCacheTime < TAGS_CACHE_TTL)) {
      return { data: cachedTags, error: null };
    }
    
    const supabase = getSupabaseClient();
    
    // Get all tags from public MCPs
    const { data: mcps, error } = await supabase
      .from('mcps')
      .select('tags')
      .eq('allow_public', true);
      
    if (error) {
      console.error('Error fetching tags:', error);
      return { data: null, error };
    }
    
    // Collect all tags and count occurrences
    const tagCounts: Record<string, number> = {};
    
    mcps?.forEach(mcp => {
      if (Array.isArray(mcp.tags)) {
        mcp.tags.forEach((tag: string) => {
          if (tag) {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          }
        });
      }
    });
    
    // Format the results
    const result = Object.entries(tagCounts).map(([tag, count]) => ({
      tag,
      count
    })).sort((a, b) => b.count - a.count);
    
    // Cache the results
    cachedTags = result;
    tagsCacheTime = now;
    
    return { data: result, error: null };
  } catch (error: any) {
    console.error('Error in getAllTags:', error);
    return { data: null, error };
  }
}

// Update MCP submission status
export async function updateSubmission(
  id: number, 
  status: string, 
  itemUuid?: string
): Promise<{ data: McpSubmission | null, error: Error | null }> {
  try {
    const supabase = getSupabaseClient();
    
    const updates: Partial<McpSubmission> = { status };
    if (itemUuid) {
      updates.preprocessinfo = {
        ...updates.preprocessinfo,
        uuid: itemUuid
      };
    }
    
    const { data, error } = await supabase
      .from('submissions')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating submission:', error);
      return { data: null, error };
    }
    
    return { data, error: null };
  } catch (error: any) {
    console.error('Error in updateSubmission:', error);
    return { data: null, error };
  }
}

// Create a new MCP
export async function createMcp(
  mcpData: Partial<Items> & { localizations?: Partial<McpLocalization>[] }
): Promise<{ data: Items | null, error: Error | null }> {
  try {
    const supabase = getSupabaseClient();
    
    // Extract localizations from the input
    const localizations = mcpData.localizations || [];
    delete mcpData.localizations;
    
    // Start a transaction by using .rpc() with a custom function
    // Or use multiple queries and handle errors manually
    
    // Insert the main MCP record
    const { data: mcp, error: mcpError } = await supabase
      .from('mcps')
      .insert(mcpData)
      .select()
      .single();
      
    if (mcpError) {
      console.error('Error creating MCP:', mcpError);
      return { data: null, error: mcpError };
    }
    
    // Insert localizations if provided
    if (localizations.length > 0) {
      // Ensure item_uuid is set correctly for all localizations
      const localizationsWithUuid = localizations.map(loc => ({
        ...loc,
        item_uuid: mcpData.uuid
      }));
      
      const { error: locError } = await supabase
        .from('item_localizations')
        .insert(localizationsWithUuid);
        
      if (locError) {
        console.error('Error creating MCP localizations:', locError);
        // Consider whether to rollback the MCP creation here
        // For simplicity, we continue even if localization insertion fails
      }
    }
    
    return { data: mcp, error: null };
  } catch (error: any) {
    console.error('Error in createMcp:', error);
    return { data: null, error };
  }
}

// Update an existing MCP
export async function updateMcp(
  uuid: string,
  mcpData: Partial<Items>
): Promise<{ data: Items | null, error: Error | null }> {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('mcps')
      .update(mcpData)
      .eq('uuid', uuid)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating MCP:', error);
      return { data: null, error };
    }
    
    return { data, error: null };
  } catch (error: any) {
    console.error('Error in updateMcp:', error);
    return { data: null, error };
  }
} 