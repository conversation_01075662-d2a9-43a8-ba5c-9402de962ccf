#!/usr/bin/env python3
"""
Unified i18n Management Script

This script provides comprehensive i18n management functionality:
1. Discovers all i18n keys used in the codebase
2. Compares with en.json to find missing/extra translations
3. Syncs all language files with the reference (en.json)
4. Integrates with AI translation services for missing translations

Usage:
    python manage_i18n.py [command] [options]

Commands:
    check       - Check for missing/extra keys without making changes
    sync        - Sync all language files with reference
    translate   - Translate missing keys using AI (requires API key)
    full        - Run complete workflow: check → sync → translate
"""

import json
import os
import argparse
import re
import sys
from pathlib import Path
from typing import Dict, Set, Any, Optional, List, Tuple
from collections import OrderedDict
import subprocess
import tempfile

# Configuration
SOURCE_FILE_EXTENSIONS = {'.ts', '.tsx', '.js', '.jsx'}
INCLUDE_DIRS = ['app', 'components', 'lib', 'hooks', 'services', 'contexts', 'providers', 'auth']
EXCLUDE_PATTERNS = ['node_modules', '.next', 'public', '.git']

# Translation key regex - matches t('key') or t("key") patterns including template literals
TRANSLATION_KEY_REGEX = re.compile(r"""
    (?:t|getText)\(          # Match t( or getText(
    \s*                      # Optional whitespace
    (?:                      # Non-capturing group for different quote types
        ['"]                 # Standard quotes
        (                    # Capture group 1 for standard quoted keys
            (?:[a-zA-Z0-9_.${}]+\.?)+  # Match key segments with template literal variables
        )
        ['"]                 # Closing quote
        |                    # OR
        `                    # Template literal backtick
        (                    # Capture group 2 for template literal keys
            (?:[a-zA-Z0-9_.${}]+\.?)+  # Match key segments with ${} variables
        )
        `                    # Closing backtick
    )
    (?:\s*,\s*\{.*?\})?      # Optional arguments like , { count: 1 }
    \s*                      # Optional whitespace
    \)                       # Closing parenthesis
""", re.VERBOSE)

# Additional regex for complex key patterns like t(`categories.${category}`)
TEMPLATE_KEY_REGEX = re.compile(r"""
    (?:t|getText)\(          # Match t( or getText(
    \s*                      # Optional whitespace
    `                        # Template literal backtick
    ([a-zA-Z0-9_.]+)         # Base key part before ${
    \.\$\{[^}]+\}           # Template variable like ${category}
    ([a-zA-Z0-9_.]*)?        # Optional suffix after template variable
    `                        # Closing backtick
    (?:\s*,\s*\{.*?\})?      # Optional arguments
    \s*                      # Optional whitespace
    \)                       # Closing parenthesis
""", re.VERBOSE)

# Namespace regex - matches useTranslations('namespace') and getTranslations({ namespace: 'name' }) patterns
NAMESPACE_REGEX = re.compile(r"""
    (?:useTranslations\(     # Match useTranslations(
    \s*                      # Optional whitespace
    ['"]                     # Opening quote (single or double)
    ([a-zA-Z0-9_]+)          # Capture namespace name
    ['"]                     # Closing quote (single or double)
    \s*                      # Optional whitespace
    \)|                      # Closing parenthesis OR
    getTranslations\(        # Match getTranslations(
    \s*\{[^}]*namespace:\s*  # Match { ... namespace:
    ['"]                     # Opening quote (single or double)
    ([a-zA-Z0-9_]+)          # Capture namespace name  
    ['"]                     # Closing quote (single or double)
    [^}]*\}\s*\))            # Match rest of object and closing )
""", re.VERBOSE)

# Language configurations for translation
LOCALE_CONFIG = {
    "zh": "Chinese",
    "ja": "Japanese", 
    "ko": "Korean",
    "fr": "French",
    "de": "German",
    "es": "Spanish",
    "it": "Italian",
    "pt": "Portuguese",
    "ru": "Russian",
    "ar": "Arabic",
    "tr": "Turkish",
    "vi": "Vietnamese",
    "th": "Thai",
    "id": "Indonesian",
    "ms": "Malay",
    "he": "Hebrew",
    "ur": "Urdu",
    "bn": "Bengali",
    "hi": "Hindi",
    "pa": "Punjabi",
    "gu": "Gujarati",
    "ta": "Tamil",
    "te": "Telugu",
    "kn": "Kannada",
    "ml": "Malayalam",
    "or": "Odia",
    "si": "Sinhala",
    "am": "Amharic",
    "ig": "Igbo",
    "yo": "Yoruba",
    "ha": "Hausa",
    "zu": "Zulu",
    "af": "Afrikaans",
    "sq": "Albanian",
    "hy": "Armenian",
}

class I18nManager:
    def __init__(self, source_dir: str, i18n_dir: str, ref_file: str = "en.json"):
        self.source_dir = Path(source_dir)
        self.i18n_dir = Path(i18n_dir)
        self.ref_file = ref_file
        self.ref_path = self.i18n_dir / ref_file
        self.ref_data = None
        self.ref_keys = set()
        
    def _validate_paths(self) -> bool:
        """Validate that required paths exist."""
        if not self.source_dir.is_dir():
            print(f"Error: Source directory not found: {self.source_dir}", file=sys.stderr)
            return False
        if not self.i18n_dir.is_dir():
            print(f"Error: i18n directory not found: {self.i18n_dir}", file=sys.stderr)
            return False
        if not self.ref_path.is_file():
            print(f"Error: Reference file not found: {self.ref_path}", file=sys.stderr)
            return False
        return True
    
    def _load_reference_data(self) -> bool:
        """Load reference language data."""
        try:
            with open(self.ref_path, 'r', encoding='utf-8') as f:
                self.ref_data = json.load(f, object_pairs_hook=OrderedDict)
            self.ref_keys = self._get_all_keys(self.ref_data)
            print(f"Loaded reference file: {self.ref_file} ({len(self.ref_keys)} keys)")
            return True
        except json.JSONDecodeError as e:
            print(f"Error reading reference file {self.ref_path}: {e}", file=sys.stderr)
            return False
        except Exception as e:
            print(f"Error loading reference file: {e}", file=sys.stderr)
            return False
    
    def _get_all_keys(self, data: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Set[str]:
        """Recursively flatten a nested dictionary and return all keys."""
        keys = set()
        for k, v in data.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            keys.add(new_key)
            if isinstance(v, dict):
                keys.update(self._get_all_keys(v, new_key, sep=sep))
        return keys
    
    def _is_excluded(self, path: Path, base_path: Path) -> bool:
        """Check if a path should be excluded from scanning."""
        try:
            relative_parts = path.relative_to(base_path).parts
            for pattern in EXCLUDE_PATTERNS:
                if pattern in relative_parts:
                    return True
            if path.name.endswith('.d.ts'):
                return True
        except ValueError:
            return True
        return False
    
    def scan_code_keys(self) -> Set[str]:
        """Scan source code for translation keys with namespace context."""
        found_keys = set()
        namespaces = set()
        seen_keys = set()  # Track unique keys for reduced verbosity
        print(f"Scanning source code in '{self.source_dir}' (checking {', '.join(INCLUDE_DIRS)})...")
        
        file_count = 0
        scanned_files = 0
        
        for include_dir in INCLUDE_DIRS:
            scan_dir = self.source_dir / include_dir
            if not scan_dir.is_dir():
                continue
                
            for filepath in scan_dir.rglob('*'):
                file_count += 1
                if self._is_excluded(filepath, self.source_dir):
                    continue
                    
                if filepath.is_file() and filepath.suffix in SOURCE_FILE_EXTENSIONS:
                    scanned_files += 1
                    if scanned_files % 50 == 0:
                        print(f"  Scanned {scanned_files} files...", end='\r')
                    
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                            # Find namespace declarations first
                            namespace_matches = NAMESPACE_REGEX.findall(content)
                            file_namespaces = []
                            for match in namespace_matches:
                                # Handle both patterns: useTranslations() and getTranslations()
                                if isinstance(match, tuple):
                                    # Filter out empty groups from alternation
                                    namespace = next((group for group in match if group), None)
                                    if namespace:
                                        file_namespaces.append(namespace)
                                        namespaces.add(namespace)
                                else:
                                    file_namespaces.append(match)
                                    namespaces.add(match)
                            
                            # Find translation keys using both regex patterns
                            key_matches = TRANSLATION_KEY_REGEX.findall(content)
                            template_matches = TEMPLATE_KEY_REGEX.findall(content)
                            
                            # Process standard key matches (handles both quoted and template literal keys)
                            processed_keys = []
                            for match in key_matches:
                                if isinstance(match, tuple):
                                    # Get the first non-empty group from the tuple
                                    key = next((group for group in match if group), None)
                                    if key:
                                        processed_keys.append(key)
                                else:
                                    processed_keys.append(match)
                            
                            # Process template literal matches like t(`categories.${category}`)
                            for match in template_matches:
                                if isinstance(match, tuple) and len(match) >= 1:
                                    base_key = match[0]
                                    # Add the base pattern - we'll need to infer the actual keys
                                    # For patterns like "categories.${category}", we add "categories"
                                    if base_key:
                                        processed_keys.append(base_key)
                                        # Also try to infer common category keys
                                        if 'categories' in base_key:
                                            common_categories = ['directory', 'blog', 'news', 'resource-page', 
                                                               'guest-post', 'forum', 'social-media', 'press-release',
                                                               'startup-directory', 'tool-directory']
                                            for cat in common_categories:
                                                processed_keys.append(f"{base_key}.{cat}")
                            
                            # If file has namespaces, scope the keys to those namespaces
                            if file_namespaces:
                                for namespace in file_namespaces:
                                    found_keys.add(namespace)  # Add namespace as a key
                                    for key in processed_keys:
                                        # Clean up key (remove any template literal artifacts)
                                        clean_key = key.replace('${', '').replace('}', '')
                                        if clean_key and not clean_key.startswith('.'):
                                            # Scope each key to the namespace
                                            scoped_key = f"{namespace}.{clean_key}"
                                            found_keys.add(scoped_key)
                                            # Reduced verbosity: Only show unique keys
                                            if scoped_key not in seen_keys:
                                                seen_keys.add(scoped_key)
                                                if len(seen_keys) % 10 == 0:
                                                    print(f"  Found {len(seen_keys)} unique keys so far...")
                            else:
                                # No namespace, add keys at root level
                                for key in processed_keys:
                                    clean_key = key.replace('${', '').replace('}', '')
                                    if clean_key and not clean_key.startswith('.'):
                                        found_keys.add(clean_key)
                                
                    except Exception as e:
                        print(f"\nWarning: Could not read file {filepath}: {e}", file=sys.stderr)
        
        # Add base namespace keys
        for namespace in namespaces:
            found_keys.add(namespace)
            print(f"  Found namespace: {namespace}")
        
        print(f"\nScan complete. Checked {file_count} paths, scanned {scanned_files} files.")
        print(f"Found {len(found_keys)} unique translation keys and {len(namespaces)} namespaces.")
        return found_keys
    
    def _load_language_file(self, filepath: Path) -> Optional[OrderedDict]:
        """Load a language file safely."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f, object_pairs_hook=OrderedDict)
        except json.JSONDecodeError as e:
            print(f"Error reading {filepath}: {e}", file=sys.stderr)
            return None
        except Exception as e:
            print(f"Error loading {filepath}: {e}", file=sys.stderr)
            return None
    
    def _save_language_file(self, filepath: Path, data: OrderedDict) -> bool:
        """Save a language file safely."""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.write('\n')
            return True
        except Exception as e:
            print(f"Error saving {filepath}: {e}", file=sys.stderr)
            return False
    
    def _sync_structure(self, ref_data: OrderedDict, target_data: OrderedDict) -> OrderedDict:
        """Synchronize target data structure with reference data."""
        synced_data = OrderedDict()
        
        for key, ref_value in ref_data.items():
            if key in target_data:
                target_value = target_data[key]
                if isinstance(ref_value, OrderedDict) and isinstance(target_value, (dict, OrderedDict)):
                    target_ordered = OrderedDict(target_value) if isinstance(target_value, dict) else target_value
                    synced_data[key] = self._sync_structure(ref_value, target_ordered)
                else:
                    synced_data[key] = target_value
            else:
                if isinstance(ref_value, OrderedDict):
                    synced_data[key] = self._sync_structure(ref_value, OrderedDict())
                    print(f"  + Added missing structure: {key}")
                else:
                    synced_data[key] = ""
                    print(f"  + Added missing key: {key}")
        
        # Report removed keys
        extra_keys = set(target_data.keys()) - set(ref_data.keys())
        for key in sorted(extra_keys):
            print(f"  - Removed extra key: {key}")
        
        return synced_data
    
    def _update_reference_with_code_keys(self, code_keys: Set[str]) -> bool:
        """Update reference file with missing keys found in code."""
        missing_keys = code_keys - self.ref_keys
        if not missing_keys:
            return False
        
        print(f"Found {len(missing_keys)} missing keys in reference file:")
        for key in sorted(missing_keys):
            print(f"  + {key}")
        
        # Create a copy of reference data to modify
        updated_ref_data = OrderedDict(self.ref_data)
        
        # Add missing keys to reference data
        for key in missing_keys:
            parts = key.split('.')
            current_level = updated_ref_data
            
            for i, part in enumerate(parts):
                if i == len(parts) - 1:
                    # Last part - add the key with empty value
                    current_level[part] = ""
                else:
                    # Intermediate part - ensure it exists as a dict
                    if part not in current_level:
                        current_level[part] = OrderedDict()
                    elif not isinstance(current_level[part], (dict, OrderedDict)):
                        # Convert to dict if it's not already
                        current_level[part] = OrderedDict()
                    current_level = current_level[part]
        
        # Save updated reference file
        if self._save_language_file(self.ref_path, updated_ref_data):
            print(f"Updated reference file with {len(missing_keys)} new keys")
            self.ref_data = updated_ref_data
            self.ref_keys = self._get_all_keys(self.ref_data)
            return True
        
        return False
    
    def _remove_unused_keys(self, code_keys: Set[str]) -> bool:
        """Remove unused keys from reference file."""
        unused_keys = self.ref_keys - code_keys
        if not unused_keys:
            return False
        
        print(f"Found {len(unused_keys)} unused keys in reference file:")
        for key in sorted(unused_keys):
            print(f"  - {key}")
        
        # Create a copy of reference data to modify
        updated_ref_data = OrderedDict(self.ref_data)
        
        # Remove unused keys
        for key in unused_keys:
            parts = key.split('.')
            current_level = updated_ref_data
            
            # Navigate to the parent of the key to remove
            for i, part in enumerate(parts[:-1]):
                if part in current_level and isinstance(current_level[part], (dict, OrderedDict)):
                    current_level = current_level[part]
                else:
                    break
            else:
                # Remove the final key
                final_key = parts[-1]
                if final_key in current_level:
                    del current_level[final_key]
        
        # Save updated reference file
        if self._save_language_file(self.ref_path, updated_ref_data):
            print(f"Removed {len(unused_keys)} unused keys from reference file")
            self.ref_data = updated_ref_data
            self.ref_keys = self._get_all_keys(self.ref_data)
            return True
        
        return False
    
    def check_keys(self) -> Dict[str, Any]:
        """Check for missing and extra keys."""
        if not self._validate_paths() or not self._load_reference_data():
            return {}
        
        # Scan code for keys
        code_keys = self.scan_code_keys()
        
        # Compare with reference
        missing_in_ref = code_keys - self.ref_keys
        unused_in_ref = self.ref_keys - code_keys
        
        # Check language files
        lang_files = []
        for filepath in sorted(self.i18n_dir.glob('*.json')):
            if filepath.name == self.ref_file:
                continue
            
            lang_data = self._load_language_file(filepath)
            if lang_data:
                lang_keys = self._get_all_keys(lang_data)
                missing_in_lang = self.ref_keys - lang_keys
                extra_in_lang = lang_keys - self.ref_keys
                
                lang_files.append({
                    'file': filepath.name,
                    'missing': missing_in_lang,
                    'extra': extra_in_lang,
                    'consistent': len(missing_in_lang) == 0 and len(extra_in_lang) == 0
                })
        
        return {
            'code_keys': code_keys,
            'ref_keys': self.ref_keys,
            'missing_in_ref': missing_in_ref,
            'unused_in_ref': unused_in_ref,
            'lang_files': lang_files
        }
    
    def sync_keys(self, update_reference: bool = True) -> bool:
        """Sync all language files with reference."""
        if not self._validate_paths() or not self._load_reference_data():
            return False
        
        # First, update reference with code keys if requested
        if update_reference:
            code_keys = self.scan_code_keys()
            self._update_reference_with_code_keys(code_keys)
            self._remove_unused_keys(code_keys)
        
        # Sync all language files
        success = True
        json_files = sorted(self.i18n_dir.glob('*.json'))
        
        for filepath in json_files:
            if filepath.name == self.ref_file:
                continue
            
            print(f"\nProcessing {filepath.name}...")
            
            target_data = self._load_language_file(filepath)
            if target_data is None:
                target_data = OrderedDict()
            
            synced_data = self._sync_structure(self.ref_data, target_data)
            
            # Check if changes are needed
            if synced_data != target_data:
                if self._save_language_file(filepath, synced_data):
                    print(f"  Successfully updated {filepath.name}")
                else:
                    print(f"  Failed to update {filepath.name}")
                    success = False
            else:
                print(f"  No changes needed for {filepath.name}")
        
        return success
    
    def _call_translate_service(self, keys_to_translate: Dict[str, str], target_language: str) -> Optional[Dict[str, str]]:
        """Call the AI translation service."""
        try:
            # Import the translator module using absolute import
            import sys
            import os
            
            # Add the scripts directory to Python path for import
            script_dir = Path(__file__).parent
            if str(script_dir) not in sys.path:
                sys.path.insert(0, str(script_dir))
            
            # Import the translator module
            import i18n_translator
            
            # Create translator instance
            project_root = str(self.source_dir.parent) if self.source_dir.name == 'nextjs' else str(self.source_dir)
            translator = i18n_translator.create_translator(project_root)
            
            # Get language name
            language_name = i18n_translator.LOCALE_CONFIG.get(target_language, target_language)
            
            print(f"  Translating {len(keys_to_translate)} keys to {language_name}...")
            
            # Call translation service
            result = translator.translate(keys_to_translate, target_language, language_name)
            
            if result:
                print(f"  Successfully translated {len(result)} keys")
                return result
            else:
                print(f"  Translation failed")
                return None
                
        except ImportError as e:
            print(f"  Error importing translator: {e}")
            print(f"  Falling back to placeholder translations...")
            # Fallback to simple placeholder
            return {k: f"[{target_language.upper()}] {v}" for k, v in keys_to_translate.items()}
        except Exception as e:
            print(f"  Error calling translation service: {e}")
            return None
    
    def translate_missing_keys(self) -> bool:
        """Translate missing keys using AI service."""
        if not self._validate_paths() or not self._load_reference_data():
            return False
        
        success = True
        json_files = sorted(self.i18n_dir.glob('*.json'))
        
        for filepath in json_files:
            if filepath.name == self.ref_file:
                continue
            
            language_code = filepath.stem
            if language_code not in LOCALE_CONFIG:
                print(f"Skipping {filepath.name} - language not supported for translation")
                continue
            
            print(f"\nProcessing translations for {filepath.name}...")
            
            target_data = self._load_language_file(filepath)
            if target_data is None:
                continue
            
            # Find keys that need translation (empty values)
            keys_to_translate = {}
            self._collect_empty_keys(target_data, keys_to_translate)
            
            if not keys_to_translate:
                print(f"  No missing translations found for {filepath.name}")
                continue
            
            print(f"  Found {len(keys_to_translate)} keys needing translation")
            
            # Call translation service
            translations = self._call_translate_service(keys_to_translate, language_code)
            
            if translations:
                # Update the target data with translations
                self._update_translations(target_data, translations)
                
                # Save updated file
                if self._save_language_file(filepath, target_data):
                    print(f"  Successfully translated {filepath.name}")
                else:
                    print(f"  Failed to save translations for {filepath.name}")
                    success = False
            else:
                print(f"  Failed to get translations for {filepath.name}")
                success = False
        
        return success
    
    def _collect_empty_keys(self, data: OrderedDict, result: Dict[str, str], prefix: str = '') -> None:
        """Collect keys with empty values that need translation."""
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, OrderedDict):
                self._collect_empty_keys(value, result, full_key)
            elif isinstance(value, str) and value.strip() == '':
                # Get the reference value for this key
                ref_value = self._get_reference_value(full_key)
                if ref_value:
                    result[full_key] = ref_value
    
    def _get_reference_value(self, key: str) -> Optional[str]:
        """Get the reference value for a given key."""
        parts = key.split('.')
        current = self.ref_data
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
        
        return current if isinstance(current, str) else None
    
    def _update_translations(self, data: OrderedDict, translations: Dict[str, str]) -> None:
        """Update data with new translations."""
        for key, translation in translations.items():
            if translation.strip():  # Only update non-empty translations
                parts = key.split('.')
                current = data
                
                for part in parts[:-1]:
                    if part in current and isinstance(current[part], OrderedDict):
                        current = current[part]
                    else:
                        break
                else:
                    final_key = parts[-1]
                    if final_key in current:
                        current[final_key] = translation
    
    def run_full_workflow(self) -> bool:
        """Run the complete workflow: check → sync → translate."""
        print("=== Starting Full i18n Workflow ===\n")
        
        print("1. Checking current state...")
        check_result = self.check_keys()
        if not check_result:
            return False
        
        print(f"\n2. Syncing language files...")
        if not self.sync_keys():
            print("Sync failed, aborting workflow")
            return False
        
        print(f"\n3. Translating missing keys...")
        if not self.translate_missing_keys():
            print("Translation failed, but sync was successful")
            return False
        
        print("\n=== Full i18n Workflow Complete ===")
        return True


def main():
    parser = argparse.ArgumentParser(
        description="Unified i18n management tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python manage_i18n.py check nextjs nextjs/i18n/messages
  python manage_i18n.py sync nextjs nextjs/i18n/messages --update-reference
  python manage_i18n.py translate nextjs nextjs/i18n/messages
  python manage_i18n.py full nextjs nextjs/i18n/messages
        """
    )
    
    parser.add_argument('command', choices=['check', 'sync', 'translate', 'full'],
                       help='Command to execute')
    parser.add_argument('source_dir', help='Source directory to scan for translation keys')
    parser.add_argument('i18n_dir', help='Directory containing i18n JSON files')
    parser.add_argument('--ref', default='en.json', help='Reference file name (default: en.json)')
    parser.add_argument('--update-reference', action='store_true', 
                       help='Update reference file with missing keys from code')
    parser.add_argument('--output', help='Output file for check results')
    parser.add_argument('--dry-run', action='store_true', help='Dry run mode')
    
    args = parser.parse_args()
    
    # Initialize manager
    manager = I18nManager(args.source_dir, args.i18n_dir, args.ref)
    
    # Execute command
    if args.command == 'check':
        result = manager.check_keys()
        if result:
            print(f"\n=== Check Results ===")
            print(f"Code keys found: {len(result['code_keys'])}")
            print(f"Reference keys: {len(result['ref_keys'])}")
            print(f"Missing in reference: {len(result['missing_in_ref'])}")
            print(f"Unused in reference: {len(result['unused_in_ref'])}")
            
            if result['missing_in_ref']:
                print(f"\nMissing keys in reference:")
                for key in sorted(result['missing_in_ref']):
                    print(f"  - {key}")
            
            if result['unused_in_ref']:
                print(f"\nUnused keys in reference:")
                for key in sorted(result['unused_in_ref']):
                    print(f"  ? {key}")
            
            for lang_file in result['lang_files']:
                print(f"\n{lang_file['file']}:")
                print(f"  Missing: {len(lang_file['missing'])}")
                print(f"  Extra: {len(lang_file['extra'])}")
                print(f"  Consistent: {lang_file['consistent']}")
    
    elif args.command == 'sync':
        if args.dry_run:
            print("Dry run mode - no changes will be made")
        success = manager.sync_keys(update_reference=args.update_reference)
        print(f"\nSync {'completed successfully' if success else 'failed'}")
    
    elif args.command == 'translate':
        if args.dry_run:
            print("Dry run mode - no translations will be made")
        success = manager.translate_missing_keys()
        print(f"\nTranslation {'completed successfully' if success else 'failed'}")
    
    elif args.command == 'full':
        if args.dry_run:
            print("Dry run mode - no changes will be made")
        success = manager.run_full_workflow()
        print(f"\nFull workflow {'completed successfully' if success else 'failed'}")


if __name__ == "__main__":
    main()