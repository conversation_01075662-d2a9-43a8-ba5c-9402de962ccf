#!/bin/bash

# svg-to-favicon.sh - Convert SVG to various favicon formats
# This script is a wrapper for the Node.js svg-to-favicon.js script

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TOOLS_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Check if svg-to-favicon.js exists
JS_SCRIPT="$SCRIPT_DIR/svg-to-favicon.js"
if [ ! -f "$JS_SCRIPT" ]; then
  echo "Error: svg-to-favicon.js script not found at $JS_SCRIPT"
  exit 1
fi

# Check dependencies
check_dependency() {
  if ! command -v "$1" &> /dev/null; then
    echo "Error: $1 is required but not installed."
    return 1
  fi
  return 0
}

# Check if sharp is installed
check_node_module() {
  if ! npm list -g $1 &> /dev/null && ! npm list --prefix "$TOOLS_ROOT" $1 &> /dev/null; then
    echo "Error: Node.js module '$1' is not installed."
    return 1
  fi
  return 0
}

# Function to install dependencies
install_dependencies() {
  echo "Installing required dependencies..."
  
  # Install ImageMagick if not present
  if ! check_dependency "convert"; then
    echo "Installing ImageMagick..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
      # macOS
      brew install imagemagick
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
      # Linux
      if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y imagemagick
      elif command -v yum &> /dev/null; then
        sudo yum install -y imagemagick
      else
        echo "Error: Unsupported Linux distribution. Please install ImageMagick manually."
        exit 1
      fi
    else
      echo "Error: Unsupported operating system. Please install ImageMagick manually."
      exit 1
    fi
  fi
  
  # Install sharp if not present
  if ! check_node_module "sharp"; then
    echo "Installing sharp module..."
    cd "$TOOLS_ROOT"
    npm install sharp
  fi
  
  echo "All dependencies installed successfully."
}

# Usage information
usage() {
  echo "Usage: $0 <path-to-svg-file> [output-directory]"
  echo
  echo "Convert SVG to various favicon formats and sizes for different platforms"
  echo
  echo "Arguments:"
  echo "  <path-to-svg-file>   Path to the source SVG file"
  echo "  [output-directory]   Optional output directory (default: ./public/favicon)"
  echo
  echo "Example:"
  echo "  $0 logo.svg"
  echo "  $0 logo.svg ./public/icons"
  echo
  exit 1
}

# Check if enough arguments provided
if [ $# -lt 1 ]; then
  usage
fi

# Check if help is requested
if [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
  usage
fi

# Check if SVG file exists
if [ ! -f "$1" ]; then
  echo "Error: SVG file not found at $1"
  exit 1
fi

# Check and install dependencies
check_dependency "node" || install_dependencies
check_dependency "convert" || install_dependencies
check_node_module "sharp" || install_dependencies

# Run the Node.js script
echo "Running SVG to favicon conversion..."
node "$JS_SCRIPT" "$@" 