# Umami 到 Plausible 转换工具

这个工具可以直接从 Umami 的 SQL 备份文件生成 Plausible 可导入的 CSV 文件，无需创建临时数据库。

## 🚀 快速使用

### 1. 导出 Umami 数据

```bash
# 如果 Umami 运行在 Docker 中
docker exec umami-db pg_dump -U umami umami > umami_backup.sql

# 或者直接从 PostgreSQL 导出
pg_dump -U umami_user -h localhost -p 5432 umami > umami_backup.sql
```

### 2. 转换为 CSV

```bash
cd tools/scripts
python sql2csv_direct.py --sql-file umami_backup.sql --output-dir ./plausible_csvs
```

## 📝 参数说明

| 参数 | 必需 | 说明 |
|------|------|------|
| `--sql-file` | ✅ | Umami SQL 备份文件路径 |
| `--output-dir` | ✅ | CSV 输出目录 |
| `--start-date` | ❌ | 开始日期 (YYYY-MM-DD) |
| `--end-date` | ❌ | 结束日期 (YYYY-MM-DD) |

## 📊 输出文件结构

脚本会为每个网站创建单独的子文件夹，每个文件夹包含：

```
plausible_csvs/
├── example.com/
│   ├── imported_visitors.csv
│   ├── imported_pages.csv
│   └── imported_sources.csv
├── blog.example.com/
│   ├── imported_visitors.csv
│   ├── imported_pages.csv
│   └── imported_sources.csv
└── api.example.com/
    ├── imported_visitors.csv
    ├── imported_pages.csv
    └── imported_sources.csv
```

### CSV 文件说明：
- `imported_visitors.csv` - 访客统计（按日期的访客数、页面浏览量等）
- `imported_pages.csv` - 页面访问统计（各页面的访问量）
- `imported_sources.csv` - 流量来源统计（目前主要是 Direct 流量）

## 💡 功能特点

- ✅ **自动处理所有网站**：无需手动选择，一次转换所有网站
- ✅ **子文件夹组织**：每个网站的文件都在独立的子文件夹中
- ✅ **按域名命名文件夹**：文件夹名称基于网站域名，便于识别
- ✅ **无需数据库**：直接解析 SQL 文件，不需要 PostgreSQL
- ✅ **简单快速**：一条命令完成转换

## 📋 导入到 Plausible

1. 在 Plausible 中为每个网站创建对应的站点
2. 进入网站设置页面
3. 找到 `Imports & Exports` > `Import Data` > `CSV`
4. 选择对应网站文件夹中的所有 CSV 文件进行导入

## 🔧 示例

```bash
# 转换所有数据
python sql2csv_direct.py --sql-file umami_backup.sql --output-dir ./csvs

# 只转换指定日期范围的数据
python sql2csv_direct.py \
  --sql-file umami_backup.sql \
  --output-dir ./csvs \
  --start-date 2024-01-01 \
  --end-date 2024-01-31
```

## ⚠️ 注意事项

- 确保 SQL 备份文件完整且格式正确
- 输出目录和子文件夹会自动创建
- 特殊字符会从域名中移除，替换为下划线
- 如果某个网站没有数据，会自动跳过并显示警告
- 每个网站的 CSV 文件都使用标准的 Plausible 导入格式

## 🆘 常见问题

**Q: 文件夹名称看起来不正确？**
A: 脚本会使用网站的 domain 字段作为文件夹名，如果为空则使用 name 字段，再为空则使用 website_id。特殊字符会被替换为下划线。

**Q: 某些网站没有生成文件夹？**
A: 如果网站没有页面访问数据，会自动跳过并显示警告信息。

**Q: 如何知道哪个文件夹对应哪个网站？**
A: 运行完成后脚本会显示完整的文件夹结构，包括每个网站的文件夹名称。

**Q: 导入到 Plausible 时要选择哪些文件？**
A: 对于每个网站，选择该网站文件夹中的所有 CSV 文件（通常是 3 个文件）一起导入。 