#!/usr/bin/env python3
"""
直接从 Umami SQL 备份文件生成 Plausible CSV 的转换器

这个脚本直接解析 SQL 备份文件，避免创建临时数据库。
适用于从 pg_dump 导出的 Umami PostgreSQL 备份文件。

使用方法:
python sql2csv_direct.py --sql-file umami_backup.sql --output-dir ./csv_output
"""

import argparse
import csv
import os
import re
import sys
from datetime import datetime
from typing import Dict, List
from collections import defaultdict

class UmamiDirectConverter:
    """直接从SQL文件转换的转换器"""
    
    def __init__(self, sql_file: str, output_dir: str):
        self.sql_file = sql_file
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        self.websites = {}
        self.website_events = []
        self.sessions = {}
    
    def convert(self, start_date: str = None, end_date: str = None):
        """转换流程"""
        print(f"🔍 正在解析 SQL 文件: {self.sql_file}")
        
        # 1. 解析SQL文件
        self._parse_sql_file()
        
        if not self.websites:
            print("❌ 未找到网站数据")
            return
        
        print(f"🌐 发现 {len(self.websites)} 个网站，开始转换所有数据...")
        
        # 2. 为每个网站生成CSV
        for website_id, website_info in self.websites.items():
            hostname = website_info['domain'] or website_info['name'] or website_id
            # 清理hostname，移除特殊字符
            safe_hostname = re.sub(r'[^\w\-_.]', '_', hostname)
            
            print(f"\n🎯 处理网站: {website_info['name']} ({hostname})")
            
            # 为每个网站创建子文件夹
            website_dir = os.path.join(self.output_dir, safe_hostname)
            os.makedirs(website_dir, exist_ok=True)
            
            # 过滤数据
            filtered_data = self._filter_data(website_id, start_date, end_date)
            
            if not filtered_data:
                print(f"⚠️  网站 {hostname} 没有数据，跳过")
                continue
            
            # 生成CSV到子文件夹
            self._generate_csvs(filtered_data, safe_hostname, website_dir)
        
        print("\n✅ 所有网站转换完成！")
    
    def _parse_sql_file(self):
        """解析SQL文件"""
        with open(self.sql_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 提取网站信息
        self._extract_websites_copy_format(content)
        print(f"找到 {len(self.websites)} 个网站")
        
        # 提取页面访问数据
        self._extract_website_events_copy_format(content)
        print(f"找到 {len(self.website_events)} 条页面访问记录")
    
    def _extract_websites_copy_format(self, content: str):
        """从COPY格式中提取网站信息"""
        # 查找website表的COPY数据块
        pattern = r'COPY public\.website \([^)]+\) FROM stdin;\n(.*?)\n\\\.'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print("未找到website表的COPY数据")
            return
        
        data_block = match.group(1)
        lines = data_block.strip().split('\n')
        
        for line in lines:
            if not line.strip() or line.startswith('--'):
                continue
            
            # 以制表符分隔数据
            parts = line.split('\t')
            if len(parts) >= 3:
                website_id = parts[0].strip()
                name = parts[1].strip() if parts[1] != '\\N' else 'Unknown'
                domain = parts[2].strip() if parts[2] != '\\N' else ''
                
                self.websites[website_id] = {
                    'name': name,
                    'domain': domain
                }
    
    def _extract_website_events_copy_format(self, content: str):
        """从COPY格式中提取网站事件数据"""
        # 查找website_event表的COPY数据块
        pattern = r'COPY public\.website_event \([^)]+\) FROM stdin;\n(.*?)\n\\\.'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print("未找到website_event表的COPY数据")
            return
        
        data_block = match.group(1)
        lines = data_block.strip().split('\n')
        
        for line in lines:
            if not line.strip() or line.startswith('--'):
                continue
            
            # 以制表符分隔数据
            parts = line.split('\t')
            if len(parts) >= 14:  # website_event表有14个字段
                try:
                    event_id = parts[0].strip()
                    website_id = parts[1].strip()
                    session_id = parts[2].strip()
                    created_at = parts[3].strip()
                    url_path = parts[4].strip() if parts[4] != '\\N' else '/'
                    url_query = parts[5].strip() if parts[5] != '\\N' else None
                    referrer_path = parts[6].strip() if parts[6] != '\\N' else None
                    referrer_query = parts[7].strip() if parts[7] != '\\N' else None
                    referrer_domain = parts[8].strip() if parts[8] != '\\N' else None
                    page_title = parts[9].strip() if parts[9] != '\\N' else None
                    event_type = parts[10].strip() if parts[10] != '\\N' else '1'
                    event_name = parts[11].strip() if parts[11] != '\\N' else None
                    visit_id = parts[12].strip()
                    tag = parts[13].strip() if parts[13] != '\\N' else None
                    
                    self.website_events.append({
                        'event_id': event_id,
                        'website_id': website_id,
                        'session_id': session_id,
                        'created_at': created_at,
                        'url_path': url_path,
                        'url_query': url_query,
                        'referrer_path': referrer_path,
                        'referrer_query': referrer_query,
                        'referrer_domain': referrer_domain,
                        'page_title': page_title,
                        'event_type': event_type,
                        'event_name': event_name,
                        'visit_id': visit_id,
                        'tag': tag
                    })
                except Exception as e:
                    print(f"解析行时出错: {line[:100]}... 错误: {e}")
                    continue
    
    def _filter_data(self, website_id: str, start_date: str = None, end_date: str = None):
        """过滤数据"""
        filtered = []
        
        for event in self.website_events:
            if event['website_id'] != website_id:
                continue
            
            # 只处理页面访问事件 (event_type = 1)
            if event['event_type'] != '1':
                continue
            
            if start_date or end_date:
                event_date = self._parse_date(event['created_at'])
                if not event_date:
                    continue
                
                if start_date and event_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                    continue
                
                if end_date and event_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                    continue
            
            filtered.append(event)
        
        print(f"📊 过滤后的数据: {len(filtered)} 条记录")
        return filtered
    
    def _parse_date(self, date_str: str):
        """解析日期"""
        try:
            # 处理带时区的时间戳，例如: 2025-05-06 11:59:31.386+00
            if '+' in date_str:
                date_str = date_str.split('+')[0]
            
            # 尝试各种日期格式
            formats = [
                '%Y-%m-%d %H:%M:%S.%f',  # 2025-05-06 11:59:31.386
                '%Y-%m-%d %H:%M:%S',     # 2025-05-06 11:59:31  
                '%Y-%m-%d'               # 2025-05-06
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue
                    
            print(f"无法解析日期: {date_str}")
            return None
        except Exception as e:
            print(f"日期解析异常: {date_str}, 错误: {e}")
            return None
    
    def _generate_csvs(self, events: List[Dict], hostname: str, website_dir: str):
        """生成CSV文件到指定的子文件夹"""
        if not events:
            print("❌ 没有数据可转换")
            return
        
        # 生成日期后缀
        dates = [self._parse_date(event['created_at']) for event in events]
        dates = [d for d in dates if d]
        
        if dates:
            min_date = min(dates).strftime('%Y%m%d')
            max_date = max(dates).strftime('%Y%m%d')
            print(f"📅 数据日期范围: {min_date} 到 {max_date}")
        
        # 生成各类CSV到子文件夹
        self._generate_visitors_csv(events, hostname, website_dir)
        self._generate_pages_csv(events, hostname, website_dir)
        self._generate_sources_csv(events, hostname, website_dir)
    
    def _generate_visitors_csv(self, events: List[Dict], hostname: str, website_dir: str):
        """生成访客统计CSV"""
        daily_stats = defaultdict(lambda: {
            'visitors': set(),
            'pageviews': 0,
            'visits': set()
        })
        
        for event in events:
            date = self._parse_date(event['created_at'])
            if not date:
                continue
            
            date_str = date.strftime('%Y-%m-%d')
            session_id = event['session_id']
            visit_id = event['visit_id']
            
            daily_stats[date_str]['visitors'].add(session_id)
            daily_stats[date_str]['pageviews'] += 1
            daily_stats[date_str]['visits'].add(visit_id)
        
        filename = "imported_visitors.csv"
        filepath = os.path.join(website_dir, filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['date', 'visitors', 'pageviews', 'bounces', 'visits', 'visit_duration'])
            
            for date_str in sorted(daily_stats.keys()):
                stats = daily_stats[date_str]
                writer.writerow([
                    date_str,
                    len(stats['visitors']),
                    stats['pageviews'],
                    len(stats['visitors']),  # 简化处理
                    len(stats['visits']),
                    0
                ])
        
        print(f"📊 访客数据已保存: {hostname}/{filename}")
    
    def _generate_pages_csv(self, events: List[Dict], hostname: str, website_dir: str):
        """生成页面统计CSV"""
        page_stats = defaultdict(lambda: defaultdict(lambda: {
            'visitors': set(),
            'pageviews': 0,
            'visits': set()
        }))
        
        for event in events:
            date = self._parse_date(event['created_at'])
            if not date:
                continue
            
            date_str = date.strftime('%Y-%m-%d')
            url_path = event['url_path'] or '/'
            session_id = event['session_id']
            visit_id = event['visit_id']
            
            page_stats[date_str][url_path]['visitors'].add(session_id)
            page_stats[date_str][url_path]['pageviews'] += 1
            page_stats[date_str][url_path]['visits'].add(visit_id)
        
        filename = "imported_pages.csv"
        filepath = os.path.join(website_dir, filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['date', 'hostname', 'page', 'visits', 'visitors', 'pageviews'])
            
            for date_str in sorted(page_stats.keys()):
                for url_path, stats in page_stats[date_str].items():
                    writer.writerow([
                        date_str,
                        hostname,
                        url_path,
                        len(stats['visits']),
                        len(stats['visitors']),
                        stats['pageviews']
                    ])
        
        print(f"📄 页面数据已保存: {hostname}/{filename}")
    
    def _generate_sources_csv(self, events: List[Dict], hostname: str, website_dir: str):
        """生成来源统计CSV"""
        source_stats = defaultdict(lambda: defaultdict(lambda: {
            'visitors': set(),
            'pageviews': 0,
            'visits': set(),
            'utm_source': '',
            'utm_medium': '',
            'utm_campaign': '',
            'utm_content': '',
            'utm_term': ''
        }))
        
        for event in events:
            date = self._parse_date(event['created_at'])
            if not date:
                continue
            
            date_str = date.strftime('%Y-%m-%d')
            session_id = event['session_id']
            visit_id = event['visit_id']
            
            # 解析来源信息
            referrer_domain = event['referrer_domain'] or 'Direct'
            source = referrer_domain if referrer_domain != 'Direct' else 'Direct'
            
            # 解析UTM参数
            utm_params = {}
            if event['url_query']:
                url_query = event['url_query']
                utm_params = self._parse_utm_params(url_query)
            
            source_key = f"{source}_{utm_params.get('utm_source', '')}"
            
            source_stats[date_str][source_key]['visitors'].add(session_id)
            source_stats[date_str][source_key]['pageviews'] += 1
            source_stats[date_str][source_key]['visits'].add(visit_id)
            source_stats[date_str][source_key]['utm_source'] = utm_params.get('utm_source', '')
            source_stats[date_str][source_key]['utm_medium'] = utm_params.get('utm_medium', '')
            source_stats[date_str][source_key]['utm_campaign'] = utm_params.get('utm_campaign', '')
            source_stats[date_str][source_key]['utm_content'] = utm_params.get('utm_content', '')
            source_stats[date_str][source_key]['utm_term'] = utm_params.get('utm_term', '')
        
        filename = "imported_sources.csv"
        filepath = os.path.join(website_dir, filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['date', 'source', 'referrer', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term', 'pageviews', 'visitors', 'visits', 'visit_duration', 'bounces'])
            
            for date_str in sorted(source_stats.keys()):
                for source_key, stats in source_stats[date_str].items():
                    source_name = source_key.split('_')[0]
                    visitors = len(stats['visitors'])
                    
                    writer.writerow([
                        date_str,
                        source_name,
                        source_name if source_name != 'Direct' else '',
                        stats['utm_source'],
                        stats['utm_medium'],
                        stats['utm_campaign'],
                        stats['utm_content'],
                        stats['utm_term'],
                        stats['pageviews'],
                        visitors,
                        len(stats['visits']),
                        0,
                        visitors  # 简化处理
                    ])
        
        print(f"🔗 来源数据已保存: {hostname}/{filename}")
    
    def _parse_utm_params(self, url_query: str) -> Dict[str, str]:
        """解析URL查询参数中的UTM参数"""
        utm_params = {}
        if not url_query:
            return utm_params
        
        # 简单解析URL参数
        params = url_query.split('&')
        for param in params:
            if '=' in param:
                key, value = param.split('=', 1)
                if key.startswith('utm_'):
                    utm_params[key] = value
        
        return utm_params

def main():
    parser = argparse.ArgumentParser(description='直接从 Umami SQL 备份文件生成 Plausible CSV')
    parser.add_argument('--sql-file', required=True, help='Umami SQL 备份文件路径')
    parser.add_argument('--output-dir', required=True, help='CSV 输出目录')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.sql_file):
        print(f"❌ SQL 文件不存在: {args.sql_file}")
        sys.exit(1)
    
    try:
        converter = UmamiDirectConverter(args.sql_file, args.output_dir)
        converter.convert(args.start_date, args.end_date)
        
        print(f"\n✅ 转换完成！输出文件保存在: {args.output_dir}")
        print("\n📋 接下来的步骤:")
        print("1. 检查生成的 CSV 文件")
        print("2. 在 Plausible 中为每个网站导入对应的 CSV 文件")
        print("3. 每个网站的文件都在对应的子文件夹中")
        
        # 显示生成的文件夹结构
        print(f"\n📊 生成的文件夹结构:")
        for root, dirs, files in os.walk(args.output_dir):
            level = root.replace(args.output_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            folder_name = os.path.basename(root) if root != args.output_dir else os.path.basename(args.output_dir)
            print(f"{indent}📁 {folder_name}/")
            subindent = ' ' * 2 * (level + 1)
            for file in sorted(files):
                if file.endswith('.csv'):
                    print(f"{subindent}📄 {file}")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 