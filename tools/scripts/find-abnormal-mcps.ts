#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as path from 'path';

if (process.argv.length < 3) {
  console.error('用法: ts-node find-duplicate-website-urls.ts <json文件路径>');
  process.exit(1);
}

const jsonPath = process.argv[2];
if (!fs.existsSync(jsonPath)) {
  console.error(`文件不存在: ${jsonPath}`);
  process.exit(1);
}

let data: any[];
try {
  const raw = fs.readFileSync(jsonPath, 'utf-8');
  data = JSON.parse(raw);
  if (!Array.isArray(data)) {
    throw new Error('JSON 文件内容不是数组');
  }
} catch (e) {
  console.error('读取或解析 JSON 文件失败:', e);
  process.exit(1);
}

// 统计 website_url -> name
const urlMap: Record<string, string[]> = {};
for (const item of data) {
  if (item.website_url && item.name) {
    if (!urlMap[item.website_url]) {
      urlMap[item.website_url] = [];
    }
    urlMap[item.website_url].push(item.name);
  }
}

// 输出重复 website_url 及其 name
let found = false;
for (const [url, names] of Object.entries(urlMap)) {
  if (names.length > 1) {
    found = true;
    console.log(`重复 website_url: ${url}`);
    console.log('对应的 MCP name:');
    for (const name of names) {
      console.log(`  - ${name}`);
    }
    console.log('---');
  }
}
if (!found) {
  console.log('未发现重复的 website_url');
} 