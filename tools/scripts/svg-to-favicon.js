#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const sharp = require('sharp');
const util = require('util');
const execPromise = util.promisify(exec);

// Favicon sizes for different platforms
const SIZES = {
  // Standard favicon sizes
  favicon: [16, 32, 48],
  // Apple Touch Icons
  apple: [57, 60, 72, 76, 114, 120, 144, 152, 180],
  // Android/PWA icons
  android: [36, 48, 72, 96, 144, 192, 512],
  // Microsoft/Windows icons
  microsoft: [70, 150, 310]
};

// Output directories
const FAVICON_DIR = 'favicon';
const DEFAULT_OUTPUT_DIR = path.join(process.cwd(), 'public', FAVICON_DIR);

// Check for SVG file argument
if (process.argv.length < 3) {
  console.error('Usage: node svg-to-favicon.js <path-to-svg-file> [output-directory]');
  process.exit(1);
}

const svgFilePath = process.argv[2];
const outputDir = process.argv[3] || DEFAULT_OUTPUT_DIR;

// Ensure the SVG file exists
if (!fs.existsSync(svgFilePath)) {
  console.error(`Error: SVG file not found at ${svgFilePath}`);
  process.exit(1);
}

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`Created output directory: ${outputDir}`);
}

/**
 * Convert SVG to PNG with specified size
 */
async function convertToPng(svgPath, outputPath, size) {
  try {
    await sharp(svgPath)
      .resize(size, size)
      .png()
      .toFile(outputPath);
    console.log(`Created: ${outputPath}`);
  } catch (error) {
    console.error(`Error creating ${outputPath}:`, error);
  }
}

/**
 * Convert SVG to ICO (requires ImageMagick)
 */
async function convertToIco(svgPath, outputPath, sizes) {
  try {
    // Create temporary PNGs for each size
    const tempDir = path.join(outputDir, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const tempFiles = [];
    for (const size of sizes) {
      const tempFile = path.join(tempDir, `temp_${size}.png`);
      await convertToPng(svgPath, tempFile, size);
      tempFiles.push(tempFile);
    }
    
    // Use ImageMagick to combine the PNGs into an ICO file
    const command = `convert ${tempFiles.join(' ')} ${outputPath}`;
    await execPromise(command);
    console.log(`Created: ${outputPath}`);
    
    // Clean up temporary files
    for (const file of tempFiles) {
      fs.unlinkSync(file);
    }
    fs.rmdirSync(tempDir);
  } catch (error) {
    console.error(`Error creating ICO file: ${error.message}`);
    console.log('Make sure ImageMagick is installed on your system.');
  }
}

/**
 * Generate the manifest.json file for PWA
 */
function generateWebManifest(sizes) {
  const icons = sizes.map(size => ({
    src: `${FAVICON_DIR}/android-chrome-${size}x${size}.png`,
    sizes: `${size}x${size}`,
    type: 'image/png'
  }));
  
  const manifest = {
    name: 'Your Site Name',
    short_name: 'Site',
    icons,
    theme_color: '#ffffff',
    background_color: '#ffffff',
    display: 'standalone'
  };
  
  const manifestPath = path.join(outputDir, 'site.webmanifest');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log(`Created: ${manifestPath}`);
}

/**
 * Generate browserconfig.xml file for Microsoft tiles
 */
function generateBrowserConfig() {
  const config = `<?xml version="1.0" encoding="utf-8"?>
<browserconfig>
  <msapplication>
    <tile>
      <square70x70logo src="${FAVICON_DIR}/mstile-70x70.png"/>
      <square150x150logo src="${FAVICON_DIR}/mstile-150x150.png"/>
      <wide310x150logo src="${FAVICON_DIR}/mstile-310x150.png"/>
      <square310x310logo src="${FAVICON_DIR}/mstile-310x310.png"/>
      <TileColor>#ffffff</TileColor>
    </tile>
  </msapplication>
</browserconfig>`;
  
  const configPath = path.join(outputDir, 'browserconfig.xml');
  fs.writeFileSync(configPath, config);
  console.log(`Created: ${configPath}`);
}

/**
 * Generate HTML code snippet for favicon inclusion
 */
function generateHtmlSnippet() {
  const snippet = `<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="/${FAVICON_DIR}/favicon.ico">
<link rel="icon" type="image/png" sizes="16x16" href="/${FAVICON_DIR}/favicon-16x16.png">
<link rel="icon" type="image/png" sizes="32x32" href="/${FAVICON_DIR}/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="48x48" href="/${FAVICON_DIR}/favicon-48x48.png">

<!-- Apple Touch Icons -->
<link rel="apple-touch-icon" sizes="57x57" href="/${FAVICON_DIR}/apple-touch-icon-57x57.png">
<link rel="apple-touch-icon" sizes="60x60" href="/${FAVICON_DIR}/apple-touch-icon-60x60.png">
<link rel="apple-touch-icon" sizes="72x72" href="/${FAVICON_DIR}/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="76x76" href="/${FAVICON_DIR}/apple-touch-icon-76x76.png">
<link rel="apple-touch-icon" sizes="114x114" href="/${FAVICON_DIR}/apple-touch-icon-114x114.png">
<link rel="apple-touch-icon" sizes="120x120" href="/${FAVICON_DIR}/apple-touch-icon-120x120.png">
<link rel="apple-touch-icon" sizes="144x144" href="/${FAVICON_DIR}/apple-touch-icon-144x144.png">
<link rel="apple-touch-icon" sizes="152x152" href="/${FAVICON_DIR}/apple-touch-icon-152x152.png">
<link rel="apple-touch-icon" sizes="180x180" href="/${FAVICON_DIR}/apple-touch-icon-180x180.png">
<link rel="apple-touch-icon" href="/${FAVICON_DIR}/apple-touch-icon.png">

<!-- Android/PWA -->
<link rel="manifest" href="/${FAVICON_DIR}/site.webmanifest">

<!-- Microsoft Tiles -->
<meta name="msapplication-config" content="/${FAVICON_DIR}/browserconfig.xml">
<meta name="msapplication-TileColor" content="#ffffff">
<meta name="msapplication-TileImage" content="/${FAVICON_DIR}/mstile-144x144.png">
<meta name="theme-color" content="#ffffff">`;

  const snippetPath = path.join(outputDir, 'favicon-html-snippet.html');
  fs.writeFileSync(snippetPath, snippet);
  console.log(`Created: ${snippetPath}`);
}

/**
 * Main function to generate all favicon files
 */
async function generateFavicons() {
  console.log(`Converting SVG from: ${svgFilePath}`);
  console.log(`Output directory: ${outputDir}`);

  try {
    // Create standard favicons (PNG)
    for (const size of SIZES.favicon) {
      const outputPath = path.join(outputDir, `favicon-${size}x${size}.png`);
      await convertToPng(svgFilePath, outputPath, size);
    }

    // Create ICO file with multiple sizes
    await convertToIco(
      svgFilePath, 
      path.join(outputDir, 'favicon.ico'), 
      SIZES.favicon
    );

    // Create Apple Touch Icons
    for (const size of SIZES.apple) {
      const outputPath = path.join(outputDir, `apple-touch-icon-${size}x${size}.png`);
      await convertToPng(svgFilePath, outputPath, size);
    }
    
    // Create default Apple Touch Icon
    await convertToPng(
      svgFilePath,
      path.join(outputDir, 'apple-touch-icon.png'),
      180
    );

    // Create Android/PWA icons
    for (const size of SIZES.android) {
      const outputPath = path.join(outputDir, `android-chrome-${size}x${size}.png`);
      await convertToPng(svgFilePath, outputPath, size);
    }

    // Create Microsoft Tiles
    await convertToPng(svgFilePath, path.join(outputDir, 'mstile-70x70.png'), 70);
    await convertToPng(svgFilePath, path.join(outputDir, 'mstile-150x150.png'), 150);
    await convertToPng(svgFilePath, path.join(outputDir, 'mstile-310x310.png'), 310);
    
    // Create wide Microsoft Tile
    await sharp(svgFilePath)
      .resize(310, 150)
      .png()
      .toFile(path.join(outputDir, 'mstile-310x150.png'));
    console.log(`Created: ${path.join(outputDir, 'mstile-310x150.png')}`);

    // Generate manifest.json for PWA
    generateWebManifest(SIZES.android);

    // Generate browserconfig.xml for Microsoft Tiles
    generateBrowserConfig();

    // Generate HTML snippet for easy copy-paste
    generateHtmlSnippet();

    console.log('\nFavicon generation completed successfully!');
    console.log(`Check the output directory: ${outputDir}`);
    console.log(`Use the HTML snippet in ${path.join(outputDir, 'favicon-html-snippet.html')} to include favicons in your website.`);
    
  } catch (error) {
    console.error('Error generating favicons:', error);
  }
}

// Run the main function
generateFavicons(); 