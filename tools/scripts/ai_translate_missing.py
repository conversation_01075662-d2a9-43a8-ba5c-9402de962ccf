#!/usr/bin/env python3
"""
AI-powered translation script to fill missing translations using OpenAI API.
Uses the translate function from ../common/ai.ts
"""

import json
import asyncio
import os
import sys
from pathlib import Path
import subprocess
from typing import Dict, Any, List

# Add the parent directory to the path so we can import from common
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Language mappings
LANGUAGES = {
    'de': 'German',
    'es': 'Spanish', 
    'fr': 'French',
    'ja': 'Japanese',
    'ko': 'Korean',
    'ru': 'Russian',
    'zh': 'Chinese (Simplified)'
}

def load_json(file_path: str) -> Dict[str, Any]:
    """Load JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return {}

def save_json(file_path: str, data: Dict[str, Any]) -> bool:
    """Save JSON file with proper formatting"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Error saving {file_path}: {e}")
        return False

def find_empty_keys(data: Dict[str, Any], prefix: str = "") -> List[str]:
    """Find all keys that have empty string values"""
    empty_keys = []
    for key, value in data.items():
        current_key = f"{prefix}.{key}" if prefix else key
        if isinstance(value, dict):
            empty_keys.extend(find_empty_keys(value, current_key))
        elif isinstance(value, str) and value == "":
            empty_keys.append(current_key)
    return empty_keys

def get_value_by_path(data: Dict[str, Any], path: str) -> str:
    """Get value from nested dict using dot notation path"""
    keys = path.split('.')
    current = data
    try:
        for key in keys:
            current = current[key]
        return current if isinstance(current, str) else ""
    except (KeyError, TypeError):
        return ""

def set_value_by_path(data: Dict[str, Any], path: str, value: str):
    """Set value in nested dict using dot notation path"""
    keys = path.split('.')
    current = data
    for key in keys[:-1]:
        if key not in current:
            current[key] = {}
        current = current[key]
    current[keys[-1]] = value

async def translate_with_ai(text: str, target_language: str) -> str:
    """Use Node.js to call the AI translation function"""
    try:
        # Create a temporary Node.js script to call the AI function
        node_script = f"""
const {{ translate }} = require('../common/ai.ts');

async function translateText() {{
    try {{
        const prompt = `Translate the following text to {target_language}. Only return the translated text, no explanations or additional formatting:

{text}`;
        const result = await translate(prompt);
        console.log(result);
    }} catch (error) {{
        console.error('Translation error:', error);
        console.log('{text}'); // Fallback to original text
    }}
}}

translateText();
"""
        
        # Write temporary script
        temp_script = '/tmp/translate_temp.js'
        with open(temp_script, 'w') as f:
            f.write(node_script)
        
        # Run the script
        process = await asyncio.create_subprocess_exec(
            'node', temp_script,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        # Clean up
        os.unlink(temp_script)
        
        if process.returncode == 0:
            return stdout.decode().strip()
        else:
            print(f"Translation error: {stderr.decode()}")
            return text  # Fallback to original
            
    except Exception as e:
        print(f"Error in AI translation: {e}")
        return text  # Fallback to original

def translate_batch_with_openai_api(texts: List[str], target_language: str) -> List[str]:
    """Translate a batch of texts using OpenAI API directly via curl"""
    try:
        # Get API key from environment
        api_key = os.getenv('AI_API_KEY')
        if not api_key:
            print("Error: AI_API_KEY environment variable not set")
            return texts
        
        # Prepare the prompt
        texts_json = json.dumps(texts, ensure_ascii=False)
        prompt = f"""Translate the following JSON array of English texts to {target_language}. 
Return only a JSON array with the translated texts in the same order, no explanations:

{texts_json}"""

        # Create curl command
        curl_data = {
            "model": "gpt-4o-mini",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3
        }
        
        curl_cmd = [
            'curl', '-s', '-X', 'POST',
            'https://api.openai.com/v1/chat/completions',
            '-H', 'Content-Type: application/json',
            '-H', f'Authorization: Bearer {api_key}',
            '-d', json.dumps(curl_data)
        ]
        
        result = subprocess.run(curl_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            response_data = json.loads(result.stdout)
            if 'choices' in response_data and len(response_data['choices']) > 0:
                content = response_data['choices'][0]['message']['content'].strip()
                # Try to parse as JSON array
                try:
                    translated_texts = json.loads(content)
                    if isinstance(translated_texts, list) and len(translated_texts) == len(texts):
                        return translated_texts
                except json.JSONDecodeError:
                    pass
        
        print(f"Translation API call failed or returned invalid format")
        return texts
        
    except Exception as e:
        print(f"Error in batch translation: {e}")
        return texts

async def translate_language_file(en_file: str, target_file: str, target_language: str):
    """Translate missing keys in a target language file"""
    print(f"\nProcessing {target_file} ({target_language})...")
    
    # Load files
    en_data = load_json(en_file)
    target_data = load_json(target_file)
    
    if not en_data or not target_data:
        print(f"Failed to load files for {target_language}")
        return
    
    # Find empty keys
    empty_keys = find_empty_keys(target_data)
    print(f"Found {len(empty_keys)} empty keys")
    
    if not empty_keys:
        print(f"No empty keys found for {target_language}")
        return
    
    # Get English texts for empty keys
    texts_to_translate = []
    valid_keys = []
    
    for key in empty_keys:
        en_text = get_value_by_path(en_data, key)
        if en_text:
            texts_to_translate.append(en_text)
            valid_keys.append(key)
    
    if not texts_to_translate:
        print(f"No valid English texts found for empty keys")
        return
    
    print(f"Translating {len(texts_to_translate)} texts to {target_language}...")
    
    # Translate in batches to avoid API limits
    batch_size = 10
    all_translations = []
    
    for i in range(0, len(texts_to_translate), batch_size):
        batch = texts_to_translate[i:i+batch_size]
        print(f"Translating batch {i//batch_size + 1}/{(len(texts_to_translate) + batch_size - 1)//batch_size}")
        
        translations = translate_batch_with_openai_api(batch, target_language)
        all_translations.extend(translations)
        
        # Small delay to respect API limits
        await asyncio.sleep(1)
    
    # Update target data with translations
    for key, translation in zip(valid_keys, all_translations):
        set_value_by_path(target_data, key, translation)
        print(f"  {key}: {translation[:50]}...")
    
    # Save updated file
    if save_json(target_file, target_data):
        print(f"Successfully updated {target_file}")
    else:
        print(f"Failed to save {target_file}")

async def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python ai_translate_missing.py <i18n_messages_dir>")
        sys.exit(1)
    
    messages_dir = sys.argv[1]
    en_file = os.path.join(messages_dir, 'en.json')
    
    if not os.path.exists(en_file):
        print(f"English reference file not found: {en_file}")
        sys.exit(1)
    
    print(f"Starting AI translation for directory: {messages_dir}")
    
    # Process each language
    for lang_code, lang_name in LANGUAGES.items():
        target_file = os.path.join(messages_dir, f'{lang_code}.json')
        if os.path.exists(target_file):
            await translate_language_file(en_file, target_file, lang_name)
        else:
            print(f"Target file not found: {target_file}")
    
    print("\nTranslation completed!")

if __name__ == "__main__":
    asyncio.run(main())