#!/usr/bin/env python3
"""
Simple CLI wrapper for the i18n management tool.

This script provides a convenient interface for common i18n operations.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from manage_i18n import I18n<PERSON>ana<PERSON>

def main():
    # Default paths for LinkTrackPro project
    project_root = Path(__file__).parent.parent.parent
    source_dir = project_root / 'nextjs'
    i18n_dir = source_dir / 'i18n' / 'messages'
    
    print("LinkTrackPro i18n Management Tool")
    print("=" * 40)
    
    # Check if paths exist
    if not source_dir.exists():
        print(f"Error: Source directory not found: {source_dir}")
        sys.exit(1)
    
    if not i18n_dir.exists():
        print(f"Error: i18n directory not found: {i18n_dir}")
        sys.exit(1)
    
    # Create manager
    manager = I18nManager(str(source_dir), str(i18n_dir))
    
    # Interactive menu
    while True:
        print("\nAvailable commands:")
        print("1. Check for missing/extra keys")
        print("2. Sync all language files")
        print("3. Translate missing keys")
        print("4. Run full workflow (check → sync → translate)")
        print("5. Exit")
        
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == '1':
                print("\nChecking i18n keys...")
                result = manager.check_keys()
                if result:
                    print(f"\nResults:")
                    print(f"  Code keys found: {len(result['code_keys'])}")
                    print(f"  Reference keys: {len(result['ref_keys'])}")
                    print(f"  Missing in reference: {len(result['missing_in_ref'])}")
                    print(f"  Unused in reference: {len(result['unused_in_ref'])}")
                    
                    if result['missing_in_ref']:
                        print(f"\n  Missing keys in reference:")
                        for key in sorted(list(result['missing_in_ref'])[:10]):  # Show first 10
                            print(f"    - {key}")
                        if len(result['missing_in_ref']) > 10:
                            print(f"    ... and {len(result['missing_in_ref']) - 10} more")
                    
                    if result['unused_in_ref']:
                        print(f"\n  Unused keys in reference:")
                        for key in sorted(list(result['unused_in_ref'])[:10]):  # Show first 10
                            print(f"    ? {key}")
                        if len(result['unused_in_ref']) > 10:
                            print(f"    ... and {len(result['unused_in_ref']) - 10} more")
                    
                    for lang_file in result['lang_files']:
                        status = "✅" if lang_file['consistent'] else "❌"
                        print(f"\n  {status} {lang_file['file']}:")
                        print(f"    Missing: {len(lang_file['missing'])}")
                        print(f"    Extra: {len(lang_file['extra'])}")
                
            elif choice == '2':
                print("\nSyncing language files...")
                update_ref = input("Update reference file with code keys? (y/N): ").strip().lower() == 'y'
                success = manager.sync_keys(update_reference=update_ref)
                print(f"Sync {'completed successfully' if success else 'failed'}")
                
            elif choice == '3':
                print("\nTranslating missing keys...")
                print("Note: This uses placeholder translations. Configure AI service for real translations.")
                success = manager.translate_missing_keys()
                print(f"Translation {'completed successfully' if success else 'failed'}")
                
            elif choice == '4':
                print("\nRunning full workflow...")
                success = manager.run_full_workflow()
                print(f"Full workflow {'completed successfully' if success else 'failed'}")
                
            elif choice == '5':
                print("Goodbye!")
                break
                
            else:
                print("Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()