const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const toml = require('@iarna/toml'); // Use the TOML parser

// --- Configuration ---
const DEFAULT_COMPATIBILITY_DATE = "2024-04-29"; // Update this as needed
const DEFAULT_WORKER_NAME_PREFIX = "mcp-hub-";

// --- Helper Functions ---
function parseArgs() {
  const args = process.argv.slice(2);
  if (args.length < 1) {
    console.error('错误：需要提供目标目录作为第一个参数。');
    console.error('用法: node tools/scripts/generate-wrangler-config.js <target_dir> [excluded_secrets]');
    console.error('示例: node tools/scripts/generate-wrangler-config.js ../nextjs "SECRET1,SECRET2"');
    process.exit(1);
  }
  const targetDir = args[0];
  // Exclude common sensitive keys by default, plus any provided ones
  const defaultExcludes = [];
  const providedExcludes = (args[1] || '').split(',').map(s => s.trim()).filter(Boolean);
  const excludedSecrets = [...new Set([...defaultExcludes, ...providedExcludes])]; // Combine and deduplicate
  return { targetDir, excludedSecrets };
}

function resolvePath(targetDir, file) {
  // Resolve relative to the script's parent directory ('tools/')
  return path.resolve(__dirname, '..', '..', targetDir, file);
}

function readEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.warn(`警告：环境文件 ${filePath} 不存在。`);
    return {};
  }
  try {
    return dotenv.parse(fs.readFileSync(filePath));
  } catch (err) {
    console.error(`错误：读取或解析环境文件 ${filePath} 失败:`, err);
    return {};
  }
}

function readTomlFile(filePath) {
    if (!fs.existsSync(filePath)) {
        console.warn(`警告：TOML 文件 ${filePath} 不存在。`);
        return {};
    }
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return toml.parse(content);
    } catch (err) {
        console.error(`错误：读取或解析 TOML 文件 ${filePath} 失败:`, err);
        return {};
    }
}


// --- Main Execution ---
const { targetDir, excludedSecrets } = parseArgs();

console.log(`目标目录: ${targetDir}`);
console.log(`排除的 Secrets: ${excludedSecrets.join(', ')}`);

const envPath = resolvePath(targetDir, '.env');
const tomlPath = resolvePath(targetDir, 'wrangler.toml');
const tomlExamplePath = resolvePath(targetDir, 'wrangler.toml.example');

console.log(`环境文件路径: ${envPath}`);
console.log(`输出 TOML 路径: ${tomlPath}`);
console.log(`TOML 示例路径: ${tomlExamplePath}`);

// Load .env variables with precedence: .env.production > .env.local > .env
const envConfig = {};

// Load base .env file first
const baseEnvConfig = readEnvFile(envPath);
Object.assign(envConfig, baseEnvConfig);

// Load .env.local if it exists (overrides .env)
const envLocalPath = resolvePath(targetDir, '.env.local');
const envLocalConfig = readEnvFile(envLocalPath);
Object.assign(envConfig, envLocalConfig);

// Load .env.production if it exists (highest priority, overrides everything)
const envProductionPath = resolvePath(targetDir, '.env.production');
const envProductionConfig = readEnvFile(envProductionPath);
Object.assign(envConfig, envProductionConfig);

console.log(`环境变量加载顺序: .env -> .env.local -> .env.production (优先级递增)`);

// Load base wrangler config from example or use defaults
let baseWranglerConfig = {};
if (fs.existsSync(tomlExamplePath)) {
    baseWranglerConfig = readTomlFile(tomlExamplePath);
    console.log(`从 ${tomlExamplePath} 加载了基础配置。`);
} else {
     console.log(`未找到 ${tomlExamplePath}，将创建新的 wrangler.toml。`);
}


// Define the final wrangler config, starting with the parsed example or an empty object
const finalWranglerConfig = { ...baseWranglerConfig };

// Ensure top-level keys exist or use defaults/env overrides
finalWranglerConfig.name = envConfig.WORKER_NAME || finalWranglerConfig.name || `${DEFAULT_WORKER_NAME_PREFIX}${path.basename(path.resolve(targetDir))}`;

// Don't add main if this is a Pages project (has pages_build_output_dir)
if (!finalWranglerConfig.pages_build_output_dir) {
    finalWranglerConfig.main = envConfig.WORKER_MAIN || finalWranglerConfig.main || "src/index.ts"; // Adjust if needed
}

finalWranglerConfig.compatibility_date = envConfig.COMPATIBILITY_DATE || finalWranglerConfig.compatibility_date || DEFAULT_COMPATIBILITY_DATE;
finalWranglerConfig.compatibility_flags = finalWranglerConfig.compatibility_flags || ["nodejs_compat"]; // Keep example flags if they exist

// Ensure [vars] section exists
finalWranglerConfig.vars = finalWranglerConfig.vars || {};

// Populate [vars] from .env, excluding specified secrets
console.log("处理环境变量以写入 wrangler.toml [vars]:");
for (const key in envConfig) {
    if (Object.hasOwnProperty.call(envConfig, key)) {
        // Skip keys that are meant for top-level config or are secrets
        if (key === 'WORKER_NAME' || key === 'WORKER_MAIN' || key === 'COMPATIBILITY_DATE') continue;

        if (!excludedSecrets.includes(key)) {
            // Ensure value is a string for TOML vars
            finalWranglerConfig.vars[key] = String(envConfig[key] ?? "");
            console.log(` - 添加 var: ${key}`);
        } else {
            console.log(` - 跳过 secret: ${key}`);
        }
    }
}

// Convert the final config object back to TOML string
// Use toml.stringify for proper formatting, including nested sections
let outputTomlString = "";
try {
    outputTomlString = toml.stringify(finalWranglerConfig);
} catch (err) {
    console.error("错误：将配置对象转换为 TOML 字符串失败:", err);
    process.exit(1);
}


// Write to wrangler.toml
try {
    fs.writeFileSync(tomlPath, outputTomlString);
    console.log(`\n成功生成 ${tomlPath}`);
    if (excludedSecrets.length > 0) {
        console.log("\n重要提示：请记得使用 'wrangler secret put <SECRET_NAME>' 设置以下 secrets:");
        const secretsToSet = excludedSecrets.filter(secret => envConfig[secret] !== undefined); // Only list secrets present in .env
         if (secretsToSet.length > 0) {
            console.log(`需要设置的 Secrets: ${secretsToSet.join(', ')}`);
         } else {
             console.log("(在 .env 文件中未找到需要设置的 secrets)");
         }
    }
} catch (err) {
    console.error(`错误：写入 ${tomlPath} 失败:`, err);
    process.exit(1);
}