# i18n Management Tools

This directory contains unified tools for managing internationalization (i18n) in the LinkTrackPro project.

## Files

- **`manage_i18n.py`** - Main unified script with full functionality
- **`i18n_translator.py`** - AI translation service wrapper
- **`i18n`** - Interactive CLI wrapper (executable)
- **`check_i18n_keys.py`** - ⚠️ **DEPRECATED** - Use `manage_i18n.py` instead
- **`sync_i18n_keys.py`** - ⚠️ **DEPRECATED** - Use `manage_i18n.py` instead

## Quick Start

### NPM Scripts (Recommended)
```bash
# From tools/ directory
npm run i18n:check       # Check for missing/extra keys
npm run i18n:sync        # Sync all language files with reference
npm run i18n:translate   # Translate missing keys
npm run i18n:full        # Complete workflow

# Legacy script compatibility
npm run check-i18n       # Same as i18n:check
npm run sync-i18n        # Same as i18n:sync
```

### Direct Command Line Usage
```bash
# Check for missing/extra keys
python3 tools/scripts/manage_i18n.py check nextjs nextjs/i18n/messages

# Sync all language files with reference
python3 tools/scripts/manage_i18n.py sync nextjs nextjs/i18n/messages --update-reference

# Translate missing keys (placeholder mode)
python3 tools/scripts/manage_i18n.py translate nextjs nextjs/i18n/messages

# Run complete workflow
python3 tools/scripts/manage_i18n.py full nextjs nextjs/i18n/messages
```

## Features

### 1. Key Discovery and Validation
- Scans TypeScript/JavaScript files for translation keys using regex pattern matching
- Finds `t('key')` and `getText('key')` patterns
- Supports nested keys like `t('section.subsection.key')`
- Compares found keys against reference file (en.json)

### 2. Missing Key Detection
- Identifies keys used in code but missing from reference file
- Automatically adds missing keys to reference file
- Reports unused keys in reference file

### 3. Language File Synchronization
- Synchronizes all language files with reference structure
- Adds missing keys with empty values
- Removes extra keys not present in reference
- Preserves existing translations

### 4. Translation Integration
- Integrates with AI translation services
- Currently uses placeholder translations
- Designed to work with existing TypeScript translation infrastructure
- Supports batch translation with rate limiting

### 5. Cleanup and Maintenance
- Removes unused translation keys
- Maintains consistent file structure
- Preserves JSON formatting and key ordering

## Configuration

### Language Support
The script supports all languages defined in the project:
- Chinese (zh), Japanese (ja), Korean (ko)
- French (fr), German (de), Spanish (es), Italian (it)
- Portuguese (pt), Russian (ru), Arabic (ar)
- Turkish (tr), Vietnamese (vi), Thai (th)
- Indonesian (id), Malay (ms), Hebrew (he)
- Urdu (ur), Bengali (bn), Hindi (hi)
- And many more...

### File Structure
```
nextjs/i18n/messages/
├── en.json          # Reference file
├── zh.json          # Chinese translations
├── fr.json          # French translations
└── ...              # Other language files
```

## Usage Examples

### Check Current State
```bash
python manage_i18n.py check nextjs nextjs/i18n/messages
```
This will:
- Scan code for translation keys
- Compare with reference file
- Report missing/extra keys
- Check all language files for consistency

### Sync and Update
```bash
python manage_i18n.py sync nextjs nextjs/i18n/messages --update-reference
```
This will:
- Add missing keys from code to reference file
- Remove unused keys from reference file
- Sync all language files with updated reference
- Preserve existing translations

### Translate Missing Keys
```bash
python manage_i18n.py translate nextjs nextjs/i18n/messages
```
This will:
- Find keys with empty translations
- Call AI translation service
- Update language files with new translations

### Full Workflow
```bash
python manage_i18n.py full nextjs nextjs/i18n/messages
```
This runs the complete process:
1. Check current state
2. Sync all files
3. Translate missing keys

## AI Translation Setup

### Current Implementation
The script currently uses placeholder translations marked with language codes (e.g., `Hello world`).

### Enabling Real AI Translation
To enable real AI translation, modify `i18n_translator.py`:

1. **OpenAI Integration:**
```python
# Set environment variable
export OPENAI_API_KEY="your-api-key"

# Uncomment and configure OpenAI calls in translate_with_real_ai()
```

2. **Existing TypeScript Service:**
```python
# Integrate with tools/translate/translate-locale.ts
# Call Node.js process to use existing translation infrastructure
```

3. **Other AI Services:**
```python
# Add support for Anthropic Claude, Google Translate, etc.
```

## Migration from Old Scripts

### From check_i18n_keys.py
```bash
# Old way
python tools/scripts/check_i18n_keys.py nextjs nextjs/i18n/messages

# New way (NPM - Recommended)
cd tools && npm run i18n:check

# New way (Direct)
python tools/scripts/manage_i18n.py check nextjs nextjs/i18n/messages
```

### From sync_i18n_keys.py
```bash
# Old way
python tools/scripts/sync_i18n_keys.py nextjs/i18n/messages

# New way (NPM - Recommended)
cd tools && npm run i18n:sync

# New way (Direct)
python tools/scripts/manage_i18n.py sync nextjs nextjs/i18n/messages
```

### From translate-locale.ts
```bash
# Old way
cd tools && npm run translate-locale

# New way (NPM - Recommended)
cd tools && npm run i18n:translate

# New way (Direct)
python tools/scripts/manage_i18n.py translate nextjs nextjs/i18n/messages
```

### NPM Script Integration
The new scripts are fully integrated into the tools/package.json:
- `npm run i18n:check` - Check for inconsistencies
- `npm run i18n:sync` - Sync all language files
- `npm run i18n:translate` - Translate missing keys
- `npm run i18n:full` - Complete workflow
- `npm run check-i18n` - Legacy compatibility
- `npm run sync-i18n` - Legacy compatibility

## Advanced Options

### Dry Run Mode
```bash
python manage_i18n.py sync nextjs nextjs/i18n/messages --dry-run
```

### Custom Reference File
```bash
python manage_i18n.py check nextjs nextjs/i18n/messages --ref custom.json
```

### Output Reports
```bash
python manage_i18n.py check nextjs nextjs/i18n/messages --output report.txt
```

## Troubleshooting

### Common Issues

1. **"Source directory not found"**
   - Ensure you're running from the correct directory
   - Check that the path to `nextjs/` is correct

2. **"Reference file not found"**
   - Verify `en.json` exists in the i18n messages directory
   - Use `--ref` option if using a different reference file

3. **"Translation failed"**
   - Check that AI service is properly configured
   - Verify API keys are set
   - Review error messages for specific issues

4. **"No translation keys found"**
   - Verify the regex pattern matches your code style
   - Check that you're using `t()` or `getText()` functions
   - Ensure source files have the correct extensions

### Debug Mode
Add verbose output by modifying the script or using Python's `-v` flag:
```bash
python -v tools/scripts/manage_i18n.py check nextjs nextjs/i18n/messages
```

## Contributing

When adding new features:

1. Update the regex pattern for new translation function names
2. Add support for new file types in `SOURCE_FILE_EXTENSIONS`
3. Update language configurations in `LOCALE_CONFIG`
4. Add tests for new functionality
5. Update this README

## Performance

The script is optimized for large codebases:
- Efficient regex scanning
- Batch processing of files
- Progress indicators for long operations
- Memory-efficient JSON handling
- Concurrent translation processing (when configured)

For very large projects, consider:
- Using `--dry-run` to test changes first
- Running sync and translate operations separately
- Implementing caching for repeated operations