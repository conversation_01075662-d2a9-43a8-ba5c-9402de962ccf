#!/usr/bin/env python3
"""
AI Translation Service Wrapper

This module provides a Python interface to the TypeScript AI translation service.
It handles the communication between the Python i18n management script and the 
existing TypeScript translation infrastructure.
"""

import json
import os
import subprocess
import tempfile
import sys
from pathlib import Path
from typing import Dict, Optional, Any

class AITranslator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.ai_module_path = self.project_root / 'tools' / 'common' / 'ai.ts'
        
    def _check_dependencies(self) -> bool:
        """Check if required dependencies are available."""
        if not self.ai_module_path.exists():
            print(f"Error: AI module not found at {self.ai_module_path}", file=sys.stderr)
            return False
        
        # Check if ts-node is available (should be available in tools directory)
        try:
            result = subprocess.run(['ts-node', '--version'], capture_output=True, check=True, cwd=str(self.project_root / 'tools'))
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            # Fallback to npx ts-node
            try:
                result = subprocess.run(['npx', 'ts-node', '--version'], capture_output=True, check=True, cwd=str(self.project_root / 'tools'))
                return True
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("Error: ts-node is required but not found. Please install it or run from tools directory.", file=sys.stderr)
                return False
    
    def _create_translation_script(self, temp_dir: str) -> str:
        """Create a temporary Node.js script for translation."""
        script_content = '''
const fs = require('fs');
const path = require('path');

// Import the AI module (assuming it exports a translate function)
async function loadAIModule() {
    try {
        // Try to require the compiled JS version
        const aiPath = process.argv[2];
        const { translate } = require(aiPath);
        return translate;
    } catch (error) {
        // If TypeScript, we need to compile or use ts-node
        console.error('Failed to load AI module:', error.message);
        process.exit(1);
    }
}

async function translateKeys() {
    const inputFile = process.argv[3];
    const outputFile = process.argv[4];
    const prompt = process.argv[5];
    
    try {
        const translate = await loadAIModule();
        const result = await translate(prompt);
        
        // Write result to output file
        fs.writeFileSync(outputFile, result, 'utf8');
        console.log('Translation completed successfully');
    } catch (error) {
        console.error('Translation failed:', error.message);
        process.exit(1);
    }
}

translateKeys();
'''
        script_path = os.path.join(temp_dir, 'translate.js')
        with open(script_path, 'w') as f:
            f.write(script_content)
        return script_path
    
    def translate(self, keys_to_translate: Dict[str, str], target_language: str, language_name: str) -> Optional[Dict[str, str]]:
        """
        Translate keys using the AI service.
        
        Args:
            keys_to_translate: Dictionary of key -> english_text to translate
            target_language: Target language code (e.g., 'zh', 'fr')
            language_name: Full language name (e.g., 'Chinese', 'French')
            
        Returns:
            Dictionary of key -> translated_text, or None if translation failed
        """
        if not keys_to_translate:
            return {}
        
        # Try to use the real AI translation service first
        try:
            result = self._translate_with_nodejs_ai(keys_to_translate, language_name)
            if result:
                return result
        except Exception as e:
            print(f"AI translation service failed: {e}", file=sys.stderr)
            print("Falling back to placeholder translation...", file=sys.stderr)
        
        # Fallback to placeholder translation
        return self._simple_translate(keys_to_translate, language_name)
    
    def _translate_with_nodejs_ai(self, keys_to_translate: Dict[str, str], language_name: str) -> Optional[Dict[str, str]]:
        """
        Use the Node.js AI service to translate keys.
        
        This method calls the existing TypeScript AI translation service
        using the same prompt structure as translate-locale.ts.
        """
        if not self._check_dependencies():
            return None
        
        # Create translation prompt (same format as translate-locale.ts)
        prompt = f"""
    - You are an AI expert in data processing and multilingual translation, with the ability to efficiently process JSON data and flexibly meet various language requirements.
    - The translation should consider professional terminology and formal style, suitable for official documents and communication.
    - Requirements:
    - 1. Target translation language: {language_name}
    - 2. Translation output should be in JSON format, with keys unchanged, directly output JSON content that can be parsed with json.load;
    - 3. Do not provide any explanations;
    - 4. Consider using local idioms rather than simple word-for-word translation, understanding the original meaning and finding appropriate local expressions
    - Input JSON data:
        {json.dumps(keys_to_translate, indent=2, ensure_ascii=False)}
  """
        
        with tempfile.TemporaryDirectory() as temp_dir:
            script_path = None
            try:
                # Create the translation script
                script_path = self._create_ai_translation_script(temp_dir)
                
                # Create input and output files
                input_file = os.path.join(temp_dir, 'input.txt')
                output_file = os.path.join(temp_dir, 'output.txt')
                
                # Write prompt to input file
                with open(input_file, 'w', encoding='utf-8') as f:
                    f.write(prompt)
                
                # Execute the TypeScript script using ts-node
                try:
                    result = subprocess.run([
                        'ts-node', script_path, 
                        input_file, 
                        output_file
                    ], capture_output=True, text=True, timeout=300, cwd=str(self.project_root / 'tools'))
                except FileNotFoundError:
                    # Fallback to npx ts-node
                    result = subprocess.run([
                        'npx', 'ts-node', script_path, 
                        input_file, 
                        output_file
                    ], capture_output=True, text=True, timeout=300, cwd=str(self.project_root / 'tools'))
                
                if result.returncode != 0:
                    print(f"TypeScript translation script failed: {result.stderr}", file=sys.stderr)
                    return None
                
                # Read the result
                if os.path.exists(output_file):
                    with open(output_file, 'r', encoding='utf-8') as f:
                        translation_result = f.read().strip()
                    
                    # Parse the JSON response (same logic as translate-locale.ts)
                    return self._safe_json_parse(translation_result)
                else:
                    print("Translation output file not found", file=sys.stderr)
                    return None
                    
            except subprocess.TimeoutExpired:
                print("Translation request timed out", file=sys.stderr)
                return None
            except Exception as e:
                print(f"Error during AI translation: {e}", file=sys.stderr)
                return None
            finally:
                # Clean up the temporary script file
                if script_path and os.path.exists(script_path):
                    try:
                        os.remove(script_path)
                    except OSError:
                        pass  # Ignore cleanup errors
    
    def _create_ai_translation_script(self, temp_dir: str) -> str:
        """Create a temporary TypeScript script for AI translation."""
        script_content = '''
import * as fs from 'fs';
import { translate } from './common/ai';

async function translateWithAI() {
    const inputFile = process.argv[2];
    const outputFile = process.argv[3];
    
    try {
        // Read the prompt
        const prompt = fs.readFileSync(inputFile, 'utf8');
        
        // Call the translate function
        const result = await translate(prompt);
        
        // Write result to output file
        fs.writeFileSync(outputFile, result, 'utf8');
        console.log('Translation completed successfully');
        
    } catch (error: any) {
        console.error('Translation failed:', error.message);
        process.exit(1);
    }
}

translateWithAI();
'''
        # Create the script in the tools directory so it can find the common/ai module
        script_path = os.path.join(str(self.project_root / 'tools'), 'translate_ai_temp.ts')
        with open(script_path, 'w') as f:
            f.write(script_content)
        return script_path
    
    def _safe_json_parse(self, json_str: str) -> Optional[Dict[str, str]]:
        """
        Safely parse JSON response with the same cleaning logic as translate-locale.ts
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            print("JSON parsing failed, attempting to fix", file=sys.stderr)
            
            # Apply the same cleaning logic as translate-locale.ts
            cleaned = json_str
            
            # Remove leading ```json
            cleaned = cleaned.replace('```json', '').strip()
            
            # Remove trailing ```
            cleaned = cleaned.replace('```', '').strip()
            
            # Remove control characters
            import re
            cleaned = re.sub(r'[\x00-\x1F]+', '', cleaned)
            
            # Fix trailing commas
            cleaned = re.sub(r'([\\[{])\\s*,', r'\\1', cleaned)
            cleaned = re.sub(r',\\s*([\\]}])', r'\\1', cleaned)
            
            print(f"Result after attempted fix: {cleaned}", file=sys.stderr)
            
            try:
                return json.loads(cleaned)
            except json.JSONDecodeError as e:
                print(f"Still unable to parse JSON after fix: {e}", file=sys.stderr)
                return None
    
    def _simple_translate(self, keys_to_translate: Dict[str, str], language_name: str) -> Dict[str, str]:
        """
        Simple translation fallback that returns placeholder translations.
        
        In a real implementation, this would call an AI service like OpenAI, Anthropic, etc.
        For now, it returns the original text with a language prefix to indicate 
        where translation would occur.
        """
        result = {}
        
        # For demo purposes, we'll just add a language prefix
        # In production, this would make actual AI API calls
        language_prefix = {
            'Chinese': '[ZH]',
            'Japanese': '[JA]', 
            'Korean': '[KO]',
            'French': '[FR]',
            'German': '[DE]',
            'Spanish': '[ES]',
            'Italian': '[IT]',
            'Portuguese': '[PT]',
            'Russian': '[RU]',
            'Arabic': '[AR]',
            'Turkish': '[TR]',
            'Vietnamese': '[VI]',
            'Thai': '[TH]',
            'Indonesian': '[ID]',
            'Malay': '[MS]',
            'Hebrew': '[HE]',
            'Urdu': '[UR]',
            'Bengali': '[BN]',
            'Hindi': '[HI]',
        }.get(language_name, f'[{language_name[:2].upper()}]')
        
        for key, english_text in keys_to_translate.items():
            if english_text.strip():
                # In production, this would be: result[key] = await ai_translate(english_text, language_name)
                result[key] = f"{language_prefix} {english_text}"
            else:
                result[key] = ""
        
        print(f"Mock translation completed for {len(result)} keys to {language_name}")
        print("Note: This is a placeholder implementation. To enable real translation:")
        print("1. Set up API keys for OpenAI, Anthropic, or other AI services")
        print("2. Implement actual AI service calls in this method")
        print("3. Or integrate with the existing TypeScript translation service")
        
        return result
    
    def translate_with_real_ai(self, keys_to_translate: Dict[str, str], target_language: str, language_name: str) -> Optional[Dict[str, str]]:
        """
        Real AI translation implementation.
        
        This method would contain the actual AI service integration.
        Commented out since it requires API keys and service setup.
        """
        # Example implementation for OpenAI:
        """
        import openai
        
        openai.api_key = os.getenv('OPENAI_API_KEY')
        if not openai.api_key:
            print("Error: OPENAI_API_KEY environment variable not set")
            return None
        
        try:
            prompt = f"Translate the following JSON object to {language_name}. Keep the keys unchanged, only translate the values. Return only valid JSON:\\n\\n{json.dumps(keys_to_translate, ensure_ascii=False, indent=2)}"
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional translator. Translate JSON values to the target language while preserving the structure and keys."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            translated_text = response.choices[0].message.content
            return json.loads(translated_text)
            
        except Exception as e:
            print(f"AI translation failed: {e}")
            return None
        """
        return self._simple_translate(keys_to_translate, language_name)


# Language configuration for easy integration
LOCALE_CONFIG = {
    "zh": "Chinese",
    "ja": "Japanese", 
    "ko": "Korean",
    "fr": "French",
    "de": "German",
    "es": "Spanish",
    "it": "Italian",
    "pt": "Portuguese",
    "ru": "Russian",
    "ar": "Arabic",
    "tr": "Turkish",
    "vi": "Vietnamese",
    "th": "Thai",
    "id": "Indonesian",
    "ms": "Malay",
    "he": "Hebrew",
    "ur": "Urdu",
    "bn": "Bengali",
    "hi": "Hindi",
    "pa": "Punjabi",
    "gu": "Gujarati",
    "ta": "Tamil",
    "te": "Telugu",
    "kn": "Kannada",
    "ml": "Malayalam",
    "or": "Odia",
    "si": "Sinhala",
    "am": "Amharic",
    "ig": "Igbo",
    "yo": "Yoruba",
    "ha": "Hausa",
    "zu": "Zulu",
    "af": "Afrikaans",
    "sq": "Albanian",
    "hy": "Armenian",
}


def create_translator(project_root: str = None) -> AITranslator:
    """Factory function to create a translator instance."""
    if project_root is None:
        # Try to detect project root
        current_dir = Path(__file__).parent
        while current_dir.parent != current_dir:
            if (current_dir / 'nextjs').exists() and (current_dir / 'tools').exists():
                project_root = str(current_dir)
                break
            current_dir = current_dir.parent
        
        if project_root is None:
            raise ValueError("Could not detect project root. Please specify project_root parameter.")
    
    return AITranslator(project_root)


if __name__ == "__main__":
    # Test the translator
    translator = create_translator()
    
    test_keys = {
        "test.hello": "Hello world",
        "test.goodbye": "Goodbye",
        "test.welcome": "Welcome to our application"
    }
    
    result = translator.translate(test_keys, "zh", "Chinese")
    if result:
        print("Translation result:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("Translation failed")