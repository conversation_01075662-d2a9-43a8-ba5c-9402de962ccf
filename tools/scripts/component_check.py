#!/usr/bin/env python3
"""
改进的组件使用检测工具

修复了误删组件的问题，增加了更全面的检测机制
"""

import os
import re
from pathlib import Path
from typing import Set, List, Dict, Tuple
from collections import defaultdict

class ImprovedComponentChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        
        # 扩展的扫描目录
        self.scan_dirs = [
            self.project_root / 'app',
            self.project_root / 'components', 
            self.project_root / 'lib',
            self.project_root / 'hooks',
            self.project_root / 'utils',
            self.project_root / 'services',
            self.project_root / 'types',
            self.project_root / 'providers',
        ]
        
        # 扫描的文件扩展名
        self.scan_extensions = {'.tsx', '.ts', '.jsx', '.js', '.mdx', '.md'}
        
        # 多种导入模式
        self.import_patterns = [
            # 1. 标准 named import: import { Button } from "@/components/ui/button"
            re.compile(r"import\s+\{[^}]*\}\s+from\s+['\"]@/components/([^'\"]+)['\"]"),
            
            # 2. 默认导入: import Button from "@/components/ui/button"
            re.compile(r"import\s+\w+\s+from\s+['\"]@/components/([^'\"]+)['\"]"),
            
            # 3. 整体导入: import * as UI from "@/components/ui/button"
            re.compile(r"import\s+\*\s+as\s+\w+\s+from\s+['\"]@/components/([^'\"]+)['\"]"),
            
            # 4. 动态导入: import("@/components/ui/button")
            re.compile(r"import\s*\(\s*['\"]@/components/([^'\"]+)['\"]\s*\)"),
            
            # 5. require: require("@/components/ui/button")
            re.compile(r"require\s*\(\s*['\"]@/components/([^'\"]+)['\"]\s*\)"),
            
            # 6. 相对路径导入
            re.compile(r"import\s+[^'\"]+\s+from\s+['\"](\.\./[^'\"]*components/[^'\"]+)['\"]"),
            re.compile(r"import\s+[^'\"]+\s+from\s+['\"](\./[^'\"]*)['\"]"),
            
            # 7. 字符串引用（在配置中）
            re.compile(r"['\"]@/components/([^'\"]+)['\"]"),
            re.compile(r"['\"]components/([^'\"]+)['\"]"),
        ]
        
    def find_all_components(self) -> Dict[str, Path]:
        """查找所有组件文件"""
        components = {}
        components_dir = self.project_root / 'components'
        
        if not components_dir.exists():
            return components
            
        for file_path in components_dir.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix in {'.tsx', '.ts', '.jsx', '.js'} and
                not any(test in file_path.name.lower() for test in ['.test.', '.spec.', '__tests__'])):
                
                relative_path = file_path.relative_to(self.project_root)
                components[str(relative_path)] = file_path
                
        return components
    
    def extract_imports_from_file(self, file_path: Path) -> Set[str]:
        """从文件中提取所有组件导入"""
        imports = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 移除注释
            content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
            content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
            
            # 对每种模式进行匹配
            for pattern in self.import_patterns:
                matches = pattern.findall(content)
                for match in matches:
                    if match:
                        # 标准化路径
                        import_path = match.strip()
                        if import_path.startswith('../'):
                            # 处理相对路径
                            resolved_path = self.resolve_relative_path(file_path, import_path)
                            if resolved_path:
                                imports.add(resolved_path)
                        else:
                            imports.add(f"components/{import_path}")
                            
        except Exception as e:
            print(f"警告：读取文件失败 {file_path}: {e}")
            
        return imports
    
    def resolve_relative_path(self, from_file: Path, relative_path: str) -> str:
        """解析相对路径到组件路径"""
        try:
            base_dir = from_file.parent
            resolved = (base_dir / relative_path).resolve()
            
            # 检查是否指向组件目录
            if 'components' in resolved.parts:
                # 提取从 components 开始的路径
                parts = resolved.parts
                if 'components' in parts:
                    idx = parts.index('components')
                    component_path = '/'.join(parts[idx:])
                    return component_path
                    
        except Exception:
            pass
        return ""
    
    def find_all_imports(self) -> Set[str]:
        """扫描所有文件找到组件导入"""
        all_imports = set()
        
        for scan_dir in self.scan_dirs:
            if not scan_dir.exists():
                continue
                
            print(f"📂 扫描目录: {scan_dir.name}")
            for ext in ['.tsx', '.ts', '.jsx', '.js', '.mdx']:
                for file_path in scan_dir.rglob(f'*{ext}'):
                    if file_path.is_file():
                        imports = self.extract_imports_from_file(file_path)
                        all_imports.update(imports)
                        
        return all_imports
    
    def normalize_component_path(self, component_path: str) -> Set[str]:
        """生成组件路径的所有可能变体"""
        variants = set()
        
        # 原路径
        variants.add(component_path)
        
        # 去掉扩展名的路径
        if '.' in component_path:
            base_path = component_path.rsplit('.', 1)[0]
            variants.add(base_path)
        
        # 如果是 index 文件，添加目录路径
        if component_path.endswith('/index.tsx') or component_path.endswith('/index.ts'):
            dir_path = component_path.rsplit('/', 1)[0]
            variants.add(dir_path)
            
        # 添加所有子路径组合
        parts = component_path.split('/')
        for i in range(1, len(parts) + 1):
            subpath = '/'.join(parts[i:])
            variants.add(subpath)
            if '.' in subpath:
                variants.add(subpath.rsplit('.', 1)[0])
                
        return variants
    
    def analyze_component_usage(self) -> Tuple[Dict[str, Path], Set[str], Dict[str, Set[str]]]:
        """分析组件使用情况"""
        print("🔍 查找所有组件...")
        all_components = self.find_all_components()
        print(f"找到 {len(all_components)} 个组件文件")
        
        print("🔍 扫描导入关系...")
        all_imports = self.find_all_imports()
        print(f"找到 {len(all_imports)} 个导入声明")
        
        # 创建组件路径变体映射
        component_variants = {}
        for component_path in all_components.keys():
            variants = self.normalize_component_path(component_path)
            for variant in variants:
                if variant not in component_variants:
                    component_variants[variant] = set()
                component_variants[variant].add(component_path)
        
        # 分析使用情况
        used_components = set()
        usage_details = defaultdict(set)
        
        for import_path in all_imports:
            # 直接匹配
            if import_path in component_variants:
                for component in component_variants[import_path]:
                    used_components.add(component)
                    usage_details[component].add(import_path)
            
            # 模糊匹配（文件名匹配）
            import_name = import_path.split('/')[-1]
            for variant, components in component_variants.items():
                if variant.endswith(import_name) or variant.endswith(f"{import_name}.tsx"):
                    for component in components:
                        used_components.add(component)
                        usage_details[component].add(import_path)
        
        unused_components = {k: v for k, v in all_components.items() if k not in used_components}
        
        return unused_components, used_components, dict(usage_details)

def main():
    project_root = "/Users/<USER>/Code/LinkTrackPro/nextjs"
    
    print(f"🔍 改进的组件使用分析 - {project_root}")
    print("=" * 60)
    
    checker = ImprovedComponentChecker(project_root)
    unused_components, used_components, usage_details = checker.analyze_component_usage()
    
    total_components = len(unused_components) + len(used_components)
    usage_rate = (len(used_components) / total_components * 100) if total_components > 0 else 0
    
    print(f"\n📊 分析结果:")
    print(f"  • 总组件数: {total_components}")
    print(f"  • 已使用组件: {len(used_components)}")
    print(f"  • 未使用组件: {len(unused_components)}")
    print(f"  • 使用率: {usage_rate:.1f}%")
    
    if unused_components:
        print(f"\n🗑️  可能未使用的组件 (显示前10个):")
        for i, (component_path, component_file) in enumerate(unused_components.items()):
            if i >= 10:
                break
            file_size = component_file.stat().st_size
            print(f"  📄 {component_path} ({file_size} bytes)")
        
        if len(unused_components) > 10:
            print(f"  ... 还有 {len(unused_components) - 10} 个组件")
    
    # 显示一些使用示例
    print(f"\n✅ 使用最频繁的组件 (前5个):")
    sorted_usage = sorted(usage_details.items(), key=lambda x: len(x[1]), reverse=True)
    for component, imports in sorted_usage[:5]:
        print(f"  📄 {component} (被 {len(imports)} 个地方引用)")
        
    print(f"\n💡 如果结果看起来更准确，请使用完整版脚本进行详细分析")

if __name__ == "__main__":
    main()