# SVG to Favicon Script

This tool converts SVG images to favicon formats for different platforms.

## Features

- Converts SVG to multiple favicon formats and sizes
- Generates favicons for:
  - Standard browser favicons (ICO and PNG)
  - Apple Touch Icons
  - Android/PWA icons
  - Microsoft/Windows tiles
- Creates necessary configuration files (site.webmanifest, browserconfig.xml)
- Provides an HTML snippet for easy integration

## Requirements

- Node.js
- ImageMagick (for ICO conversion)
- Sharp npm package (will be installed automatically)

## Usage

```bash
# Using the shell script (recommended)
./svg-to-favicon.sh <path-to-svg-file> [output-directory]

# Or directly with Node.js
node svg-to-favicon.js <path-to-svg-file> [output-directory]
```

### Examples

```bash
# Convert logo.svg to favicons in the default output directory (./public/favicon)
./svg-to-favicon.sh logo.svg

# Specify a custom output directory
./svg-to-favicon.sh logo.svg ./public/icons

# Get help
./svg-to-favicon.sh --help
```

## Output

The script generates the following files in the output directory:

- **Standard favicons**:
  - `favicon.ico` (multi-size ICO file)
  - `favicon-16x16.png`, `favicon-32x32.png`, `favicon-48x48.png`
  
- **Apple Touch Icons**:
  - `apple-touch-icon.png` (default)
  - `apple-touch-icon-57x57.png` through `apple-touch-icon-180x180.png`
  
- **Android/PWA Icons**:
  - `android-chrome-36x36.png` through `android-chrome-512x512.png`
  
- **Microsoft Tiles**:
  - `mstile-70x70.png`, `mstile-150x150.png`, `mstile-310x310.png`
  - `mstile-310x150.png` (wide tile)
  
- **Configuration files**:
  - `site.webmanifest` (for PWA)
  - `browserconfig.xml` (for Microsoft tiles)
  
- **HTML Snippet**:
  - `favicon-html-snippet.html` (reference code to include in your HTML)

## Integration

After running the script:

1. Copy the HTML snippet from `favicon-html-snippet.html` into your website's `<head>` section
2. Customize the site name in `site.webmanifest` if needed
3. Adjust theme colors in the configuration files if desired

## Troubleshooting

- **ImageMagick not found**: The script will attempt to install it, but you may need to install it manually
- **Sharp not installed**: The script will attempt to install it automatically
- **Input file not found**: Make sure your SVG file exists and the path is correct
- **Permission errors**: You might need to run with `sudo` for certain operations

## License

MIT 