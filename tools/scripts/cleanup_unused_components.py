#!/usr/bin/env python3
"""
改进的组件清理工具 v2

修复了误删组件的问题，使用更准确的检测算法
"""

import os
import re
import json
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
from collections import defaultdict

class ImprovedComponentCleaner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        
        # 扩展的扫描目录
        self.scan_dirs = [
            self.project_root / 'app',
            self.project_root / 'components', 
            self.project_root / 'lib',
            self.project_root / 'hooks',
            self.project_root / 'utils',
            self.project_root / 'services',
            self.project_root / 'types',
            self.project_root / 'providers',
        ]
        
        # 文件扩展名
        self.component_extensions = {'.tsx', '.ts', '.jsx', '.js'}
        self.scan_extensions = {'.tsx', '.ts', '.jsx', '.js', '.mdx', '.md'}
        
        # 排除的目录和文件
        self.exclude_dirs = {
            'node_modules', '.next', '.git', 'dist', 'build', '.vscode', '.idea'
        }
        
        # 改进的导入模式
        self.import_patterns = [
            # 1. 标准 named import: import { Button } from "@/components/ui/button"
            re.compile(r"import\s+\{[^}]*\}\s+from\s+['\"]@/components/([^'\"]+)['\"]"),
            
            # 2. 默认导入: import Button from "@/components/ui/button"
            re.compile(r"import\s+\w+\s+from\s+['\"]@/components/([^'\"]+)['\"]"),
            
            # 3. 整体导入: import * as UI from "@/components/ui/button"
            re.compile(r"import\s+\*\s+as\s+\w+\s+from\s+['\"]@/components/([^'\"]+)['\"]"),
            
            # 4. 动态导入: import("@/components/ui/button")
            re.compile(r"import\s*\(\s*['\"]@/components/([^'\"]+)['\"]\s*\)"),
            
            # 5. require: require("@/components/ui/button")
            re.compile(r"require\s*\(\s*['\"]@/components/([^'\"]+)['\"]\s*\)"),
            
            # 6. 相对路径导入
            re.compile(r"import\s+[^'\"]+\s+from\s+['\"](\.\./[^'\"]*components/[^'\"]+)['\"]"),
            re.compile(r"import\s+[^'\"]+\s+from\s+['\"](\./[^'\"]*)['\"]"),
            
            # 7. 字符串引用（在配置中）
            re.compile(r"['\"]@/components/([^'\"]+)['\"]"),
            re.compile(r"['\"]components/([^'\"]+)['\"]"),
        ]
        
        # UI 组件通常被广泛使用，需要特别小心
        self.ui_components_pattern = re.compile(r'components[/\\]ui[/\\]')
        
    def find_all_components(self) -> Dict[str, Path]:
        """查找所有组件文件"""
        components = {}
        components_dir = self.project_root / 'components'
        
        if not components_dir.exists():
            print(f"警告：组件目录不存在: {components_dir}")
            return components
            
        for file_path in components_dir.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix in self.component_extensions and
                not any(test in file_path.name.lower() for test in ['.test.', '.spec.', '__tests__'])):
                
                relative_path = file_path.relative_to(self.project_root)
                components[str(relative_path)] = file_path
                
        return components
    
    def extract_imports_from_file(self, file_path: Path) -> Set[str]:
        """从文件中提取所有组件导入"""
        imports = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 移除注释
            content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
            content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
            
            # 对每种模式进行匹配
            for pattern in self.import_patterns:
                matches = pattern.findall(content)
                for match in matches:
                    if match:
                        # 标准化路径
                        import_path = match.strip()
                        if import_path.startswith('../'):
                            # 处理相对路径
                            resolved_path = self.resolve_relative_path(file_path, import_path)
                            if resolved_path:
                                imports.add(resolved_path)
                        else:
                            imports.add(f"components/{import_path}")
                            
        except Exception as e:
            print(f"警告：读取文件失败 {file_path}: {e}")
            
        return imports
    
    def resolve_relative_path(self, from_file: Path, relative_path: str) -> str:
        """解析相对路径到组件路径"""
        try:
            base_dir = from_file.parent
            resolved = (base_dir / relative_path).resolve()
            
            # 检查是否指向组件目录
            if 'components' in resolved.parts:
                # 提取从 components 开始的路径
                parts = resolved.parts
                if 'components' in parts:
                    idx = parts.index('components')
                    component_path = '/'.join(parts[idx:])
                    return component_path
                    
        except Exception:
            pass
        return ""
    
    def find_all_source_files(self) -> List[Path]:
        """查找所有源代码文件"""
        source_files = []
        
        for scan_dir in self.scan_dirs:
            if scan_dir.exists():
                for ext in ['.tsx', '.ts', '.jsx', '.js', '.mdx']:
                    for file_path in scan_dir.rglob(f'*{ext}'):
                        if (file_path.is_file() and
                            not any(exclude_dir in file_path.parts for exclude_dir in self.exclude_dirs)):
                            source_files.append(file_path)
                        
        return source_files
    
    def normalize_component_path(self, component_path: str) -> Set[str]:
        """生成组件路径的所有可能变体"""
        variants = set()
        
        # 原路径
        variants.add(component_path)
        
        # 去掉扩展名的路径
        if '.' in component_path:
            base_path = component_path.rsplit('.', 1)[0]
            variants.add(base_path)
        
        # 如果是 index 文件，添加目录路径
        if component_path.endswith('/index.tsx') or component_path.endswith('/index.ts'):
            dir_path = component_path.rsplit('/', 1)[0]
            variants.add(dir_path)
            
        # 添加所有子路径组合
        parts = component_path.split('/')
        for i in range(1, len(parts) + 1):
            subpath = '/'.join(parts[i:])
            variants.add(subpath)
            if '.' in subpath:
                variants.add(subpath.rsplit('.', 1)[0])
                
        return variants
    
    def find_unused_components(self) -> Tuple[Dict[str, Path], Dict[str, List[str]]]:
        """查找未使用的组件"""
        print("正在扫描组件文件...")
        all_components = self.find_all_components()
        print(f"找到 {len(all_components)} 个组件文件")
        
        print("正在扫描源代码文件...")
        source_files = self.find_all_source_files()
        print(f"找到 {len(source_files)} 个源代码文件")
        
        # 收集所有导入的路径
        print("正在分析导入关系...")
        all_imports = set()
        import_details = defaultdict(list)
        
        for source_file in source_files:
            imports = self.extract_imports_from_file(source_file)
            all_imports.update(imports)
            
            for import_path in imports:
                import_details[import_path].append(str(source_file.relative_to(self.project_root)))
        
        # 创建组件路径变体映射
        component_variants = {}
        for component_path in all_components.keys():
            variants = self.normalize_component_path(component_path)
            for variant in variants:
                if variant not in component_variants:
                    component_variants[variant] = set()
                component_variants[variant].add(component_path)
        
        # 分析使用情况
        used_components = set()
        component_usage = defaultdict(list)
        
        for import_path in all_imports:
            # 直接匹配
            if import_path in component_variants:
                for component in component_variants[import_path]:
                    used_components.add(component)
                    component_usage[component].extend(import_details[import_path])
            
            # 模糊匹配（文件名匹配）
            import_name = import_path.split('/')[-1]
            for variant, components in component_variants.items():
                if variant.endswith(import_name) or variant.endswith(f"{import_name}.tsx"):
                    for component in components:
                        used_components.add(component)
                        component_usage[component].extend(import_details[import_path])
        
        # 去重使用列表
        for component in component_usage:
            component_usage[component] = list(set(component_usage[component]))
        
        unused_components = {k: v for k, v in all_components.items() if k not in used_components}
        
        return unused_components, dict(component_usage)
    
    def is_safe_to_delete(self, component_path: str) -> bool:
        """检查组件是否安全删除 - 更严格的安全检查"""
        
        # UI 组件需要特别小心
        if self.ui_components_pattern.search(component_path):
            return False
            
        # 检查是否是 index 文件（通常是重要的导出文件）
        if Path(component_path).name.startswith('index.'):
            return False
            
        # 检查是否是布局组件
        layout_patterns = ['layout', 'Layout', 'template', 'Template', 'dashboard', 'Dashboard', 'sidebar', 'header', 'footer']
        if any(pattern in component_path for pattern in layout_patterns):
            return False
        
        # 检查关键功能组件
        critical_patterns = [
            'auth', 'Auth', 'sign', 'Sign', 'login', 'Login',  # 认证相关
            'analytics', 'Analytics', 'tracking', 'Tracking',  # 分析相关
            'api', 'Api', 'hook', 'Hook', 'provider', 'Provider',  # 核心功能
            'context', 'Context', 'store', 'Store',  # 状态管理
            'config', 'Config', 'setting', 'Setting'  # 配置相关
        ]
        if any(pattern in component_path for pattern in critical_patterns):
            return False
        
        # 检查组件大小 - 大文件通常包含重要逻辑
        try:
            component_file = self.project_root / component_path
            if component_file.exists():
                file_size = component_file.stat().st_size
                if file_size > 5000:  # 大于5KB的文件需要审查
                    return False
        except Exception:
            pass
        
        # 检查是否有复杂的导入关系
        try:
            component_file = self.project_root / component_path
            dependencies = self.analyze_component_dependencies(component_file)
            if len(dependencies) > 3:  # 依赖超过3个的组件需要审查
                return False
        except Exception:
            pass
            
        return True
    
    def analyze_component_dependencies(self, component_path: Path) -> List[str]:
        """分析组件的依赖关系"""
        dependencies = []
        
        try:
            imports = self.extract_imports_from_file(component_path)
            dependencies = [imp for imp in imports if not imp.startswith('.') and '/' in imp]
                    
        except Exception as e:
            print(f"警告：分析组件依赖失败 {component_path}: {e}")
            
        return dependencies
    
    def generate_report(self, unused_components: Dict[str, Path], component_usage: Dict[str, List[str]], output_file: Optional[str] = None):
        """生成分析报告"""
        total_components = len(unused_components) + len(component_usage)
        used_components_count = len([k for k, v in component_usage.items() if v])
        
        report = {
            "总组件数": total_components,
            "已使用组件数": used_components_count,
            "未使用组件数": len(unused_components),
            "使用率": f"{(used_components_count / total_components * 100):.1f}%" if total_components else "0%",
            "未使用组件列表": {},
            "组件使用情况": {}
        }
        
        # 未使用组件详情
        for component_path, component_file in unused_components.items():
            file_size = component_file.stat().st_size
            dependencies = self.analyze_component_dependencies(component_file)
            safe_to_delete = self.is_safe_to_delete(component_path)
            
            report["未使用组件列表"][component_path] = {
                "文件大小": f"{file_size} bytes",
                "依赖组件": dependencies,
                "安全删除": safe_to_delete,
                "文件路径": str(component_file)
            }
        
        # 使用情况统计
        for component_path, usage_files in component_usage.items():
            if usage_files:
                report["组件使用情况"][component_path] = {
                    "使用次数": len(usage_files),
                    "使用文件": usage_files
                }
        
        # 输出报告
        report_text = self.format_report(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"报告已保存到: {output_file}")
        else:
            print(report_text)
            
        return report
    
    def format_report(self, report: dict) -> str:
        """格式化报告为可读文本"""
        lines = []
        lines.append("=" * 60)
        lines.append("📊 改进的组件使用情况分析报告")
        lines.append("=" * 60)
        lines.append("")
        lines.append(f"📈 总体统计:")
        lines.append(f"  • 总组件数: {report['总组件数']}")
        lines.append(f"  • 已使用组件数: {report['已使用组件数']}")
        lines.append(f"  • 未使用组件数: {report['未使用组件数']}")
        lines.append(f"  • 组件使用率: {report['使用率']}")
        lines.append("")
        
        if report["未使用组件列表"]:
            lines.append("🗑️  未使用组件列表:")
            lines.append("")
            
            safe_to_delete = []
            needs_review = []
            
            for component_path, details in report["未使用组件列表"].items():
                item = f"  📄 {component_path}"
                item += f" ({details['文件大小']})"
                
                if details["安全删除"]:
                    safe_to_delete.append((component_path, details))
                    item += " ✅ 安全删除"
                else:
                    needs_review.append((component_path, details))
                    item += " ⚠️  需要审查"
                
                if details["依赖组件"]:
                    item += f"\n    📦 依赖: {', '.join(details['依赖组件'][:3])}"
                    if len(details["依赖组件"]) > 3:
                        item += f" (+{len(details['依赖组件']) - 3} more)"
                
                lines.append(item)
                lines.append("")
            
            if safe_to_delete:
                lines.append(f"✅ 可安全删除的组件 ({len(safe_to_delete)} 个):")
                for component_path, _ in safe_to_delete:
                    lines.append(f"  • {component_path}")
                lines.append("")
            
            if needs_review:
                lines.append(f"⚠️  需要人工审查的组件 ({len(needs_review)} 个):")
                for component_path, _ in needs_review:
                    lines.append(f"  • {component_path}")
                lines.append("")
        
        # 高使用率组件
        if report["组件使用情况"]:
            sorted_usage = sorted(
                report["组件使用情况"].items(), 
                key=lambda x: x[1]["使用次数"], 
                reverse=True
            )
            
            if sorted_usage:
                lines.append("🔥 使用最频繁的组件 (Top 10):")
                for component_path, usage in sorted_usage[:10]:
                    lines.append(f"  📄 {component_path} ({usage['使用次数']} 次)")
                lines.append("")
        
        lines.append("=" * 60)
        
        return "\n".join(lines)
    
    def delete_unused_components(self, unused_components: Dict[str, Path], dry_run: bool = True, safe_only: bool = True):
        """删除未使用的组件"""
        to_delete = []
        
        for component_path, component_file in unused_components.items():
            if safe_only and not self.is_safe_to_delete(component_path):
                continue
            to_delete.append((component_path, component_file))
        
        if not to_delete:
            print("没有找到可以安全删除的组件。")
            return
        
        print(f"\n{'🔍 预览' if dry_run else '🗑️  删除'} 模式: 将{'检查' if dry_run else '删除'} {len(to_delete)} 个组件")
        print("=" * 50)
        
        deleted_count = 0
        total_size = 0
        
        for component_path, component_file in to_delete:
            file_size = component_file.stat().st_size
            total_size += file_size
            
            print(f"{'📋 ' if dry_run else '🗑️  '}{component_path} ({file_size} bytes)")
            
            if not dry_run:
                try:
                    component_file.unlink()
                    deleted_count += 1
                    print(f"  ✅ 已删除")
                except Exception as e:
                    print(f"  ❌ 删除失败: {e}")
            
        print("=" * 50)
        
        if dry_run:
            print(f"📊 预览结果: {len(to_delete)} 个文件，总计 {total_size} bytes")
            print("💡 使用 --delete 参数执行实际删除")
        else:
            print(f"✅ 删除完成: {deleted_count}/{len(to_delete)} 个文件，释放 {total_size} bytes")


def main():
    parser = argparse.ArgumentParser(description="改进的组件检查和清理工具")
    parser.add_argument("project_dir", help="Next.js 项目根目录")
    parser.add_argument("--delete", action="store_true", help="实际删除未使用的组件")
    parser.add_argument("--include-unsafe", action="store_true", help="包括可能不安全的组件")
    parser.add_argument("--output", help="保存报告到文件")
    parser.add_argument("--dry-run", action="store_true", default=True, help="预览模式（默认）")
    
    args = parser.parse_args()
    
    # 如果指定了 --delete，则关闭 dry-run
    if args.delete:
        args.dry_run = False
    
    try:
        cleaner = ImprovedComponentCleaner(args.project_dir)
        
        print("🔍 开始改进的组件使用情况分析...")
        unused_components, component_usage = cleaner.find_unused_components()
        
        print("📋 生成分析报告...")
        report = cleaner.generate_report(unused_components, component_usage, args.output)
        
        if unused_components:
            print(f"\n🎯 清理操作...")
            cleaner.delete_unused_components(
                unused_components, 
                dry_run=args.dry_run, 
                safe_only=not args.include_unsafe
            )
        else:
            print("\n🎉 太棒了！没有发现未使用的组件。")
            
    except KeyboardInterrupt:
        print("\n⏹️  操作已取消")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())