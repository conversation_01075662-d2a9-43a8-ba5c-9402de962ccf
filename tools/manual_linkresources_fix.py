#!/usr/bin/env python3
"""
Manual fix for linkResources translation structure
"""

import json
from pathlib import Path
from collections import OrderedDict

# Read the current en.json
en_path = Path("/Users/<USER>/Code/LinkTrackPro/nextjs/i18n/messages/en.json")

with open(en_path, 'r', encoding='utf-8') as f:
    data = json.load(f, object_pairs_hook=OrderedDict)

# Define the complete linkResources structure based on what we found in the code scan
linkresources_structure = OrderedDict({
    "title": "Link Building Resources",
    "description": "Discover the best platforms for building high-quality backlinks and growing your website's authority",
    "loading": "Loading...",
    "noResults": "No results found",
    "resultsCount": "{count} resources found",
    "paid": "Paid",
    "free": "Free",
    "common": OrderedDict({
        "home": "Home"
    }),
    "seo": OrderedDict({
        "title": "Best Link Building Resources & Directories 2025",
        "description": "Comprehensive directory of verified link building opportunities with high domain authority",
        "keywords": "link building, SEO backlinks, directory submission, guest posting, resource pages",
        "categoryDescription": "Browse {category} link building opportunities",
        "paidDescription": "Premium link building platforms with guaranteed results",
        "freeDescription": "Free link building opportunities and resource submissions", 
        "page": "Page",
        "additionalTitle": "Why Use Our Link Building Directory?",
        "howItWorks": "How It Works",
        "howItWorksDesc": "Our platform makes finding quality link building opportunities simple and efficient",
        "step1": "Browse our curated database of verified platforms",
        "step2": "Filter by category, domain rating, and pricing",
        "step3": "Submit your content to relevant opportunities",
        "step4": "Track your link building progress and results",
        "benefits": "Key Benefits",
        "benefitsDesc": "Everything you need to build authoritative backlinks",
        "benefit1": "500+ verified high-authority platforms",
        "benefit2": "Regular quality checks and updates",
        "benefit3": "Detailed submission guidelines",
        "benefit4": "Success rate tracking and analytics"
    }),
    "stats": OrderedDict({
        "totalResources": "Total Resources",
        "categories": "Categories",
        "highAuthority": "High Authority Sites"
    }),
    "features": OrderedDict({
        "title": "Platform Features",
        "highQuality": "High Quality",
        "highQualityDesc": "Manually verified platforms with proven results",
        "verified": "Verified Platforms",
        "verifiedDesc": "All platforms checked for authenticity and effectiveness",
        "qualityAssured": "Quality Assured",
        "qualityAssuredDesc": "Continuous monitoring to ensure platform quality",
        "updated": "Regularly Updated",
        "updatedDesc": "Fresh opportunities added weekly"
    }),
    "filters": OrderedDict({
        "searchPlaceholder": "Search platforms...",
        "categoryPlaceholder": "Select category",
        "allCategories": "All Categories",
        "pricingPlaceholder": "Select pricing",
        "allPricing": "All Pricing",
        "free": "Free Only",
        "paid": "Paid Only",
        "sortPlaceholder": "Sort by",
        "sortByDRDesc": "Domain Rating (High to Low)",
        "sortByDRAsc": "Domain Rating (Low to High)",
        "sortByTrafficDesc": "Traffic (High to Low)",
        "sortByTrafficAsc": "Traffic (Low to High)",
        "sortBySuccessRateDesc": "Success Rate (High to Low)",
        "sortByNewest": "Newest First"
    }),
    "pagination": OrderedDict({
        "page": "Page",
        "of": "of",
        "previous": "Previous",
        "next": "Next"
    }),
    "actions": OrderedDict({
        "submitHere": "Submit Here",
        "visitWebsite": "Visit Website",
        "contact": "Contact"
    }),
    "details": OrderedDict({
        "submissionMethod": "Submission Method",
        "responseTime": "Response Time",
        "successRate": "Success Rate",
        "priceRange": "Price Range", 
        "requirements": "Requirements"
    }),
    "faq": OrderedDict({
        "title": "Frequently Asked Questions",
        "q1": "What are link building resources?",
        "a1": "Link building resources are platforms, directories, and websites where you can submit your content to earn high-quality backlinks that improve your website's search engine rankings.",
        "q2": "How do I choose the right platforms?",
        "a2": "Focus on platforms with high domain authority (DR 50+), relevant to your niche, and with good success rates. Always check their guidelines before submitting.",
        "q3": "Are paid submissions worth it?",
        "a3": "Paid submissions often have higher acceptance rates and faster processing times. They can be worthwhile for high-value content and competitive niches.",
        "q4": "How long does it take to see results?",
        "a4": "Link building results typically take 3-6 months to show in search rankings. However, referral traffic can begin immediately after link placement.",
        "q5": "What information do I need to submit?",
        "a5": "Most platforms require your website URL, content description, category selection, and contact information. Some may require additional details like target keywords.",
        "q6": "How do you verify platform quality?",
        "a6": "We manually check each platform for domain authority, traffic levels, spam indicators, and submission success rates. Our team regularly reviews and updates the database."
    })
})

# Update the linkResources section
data["linkResources"] = linkresources_structure

# Save the updated file
with open(en_path, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
    f.write('\n')

print("✅ Successfully updated linkResources structure in en.json")
print(f"Added complete nested structure with FAQ q1-q6 including the missing q5!")