#!/usr/bin/env node

import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
import { parse } from 'csv-parse';
import { prefetchMcpInfo, submitMcp } from '../common/api';
import { checkMcpExists } from '../common/db';

// Load environment variables
dotenv.config();

// Define command line arguments
interface CliArgs {
  csvFile: string;
  allowPublic: boolean;
  concurrency: number;
}

// Define record structure
interface McpRecord {
  website_url: string;
  [key: string]: string;
}

// Helper function to handle rate limit errors
async function withRateLimitRetry<T>(fn: () => Promise<T>, maxRetries = 3): Promise<T> {
  let retries = 0;
  
  while (true) {
    try {
      return await fn();
    } catch (error: any) {
      // Check if it's a rate limit error (TPM, RPM, etc.)
      const isRateLimit = 
        error?.statusCode === 429 || 
        (error?.message && error.message.includes("rate limit")) ||
        (error?.responseBody && error.responseBody.includes("TPM limit")) ||
        (error?.responseBody && error.responseBody.includes("RPM limit"));
      
      if (isRateLimit && retries < maxRetries) {
        retries++;
        console.log(`Rate limit reached. Waiting 1 minute before retry (${retries}/${maxRetries})...`);
        // Wait for 1 minute (60000 ms)
        await new Promise(resolve => setTimeout(resolve, 60000));
        console.log('Retrying...');
      } else {
        // Re-throw the error if it's not a rate limit error or we've exceeded retries
        throw error;
      }
    }
  }
}

// Parse command line arguments
function parseArgs(): CliArgs {
  const args = process.argv.slice(2);
  
  // First argument should be the CSV file path
  const csvFile = args.find(arg => !arg.startsWith('--'));
  const allowPublicArg = args.includes('--allow-public');
  
  // Parse concurrency parameter
  const concurrencyArg = args.find(arg => arg.startsWith('--concurrency='));
  const concurrency = concurrencyArg 
    ? parseInt(concurrencyArg.split('=')[1], 10) 
    : 1;
  
  if (!csvFile) {
    console.error('Usage: import-csv path/to/mcps.csv [--allow-public] [--concurrency=N]');
    console.error('  --allow-public: Mark imported MCPs as publicly visible when processed');
    console.error('  --concurrency=N: Set number of concurrent operations (default: 1)');
    process.exit(1);
  }
  
  return {
    csvFile,
    allowPublic: allowPublicArg,
    concurrency: isNaN(concurrency) || concurrency < 1 ? 1 : concurrency
  };
}

// Function to write failed MCPs to a CSV file
function writeFailedItemsToCsv(failedItems: { record: McpRecord; error: string }[], outputPath: string): void {
  // Create CSV header if file doesn't exist
  if (!fs.existsSync(outputPath)) {
    const header = ['website_url', 'failure_reason'].join(',');
    fs.writeFileSync(outputPath, header + '\n', 'utf8');
    console.log(`Created failed MCPs log file at ${outputPath}`);
  }
  
  // Create CSV rows
  const rows = failedItems.map(item => {
    return `"${item.record.website_url}","${item.error.replace(/"/g, '""')}"`;
  });
  
  // Append to file
  fs.appendFileSync(outputPath, rows.join('\n') + '\n', 'utf8');
  
  console.log(`Wrote ${failedItems.length} failed MCP(s) to ${outputPath}`);
}

// Process a single GitHub URL
async function processGithubUrl(githubUrl: string, record: McpRecord, failedOutputPath: string): Promise<{ success: boolean; error?: string }> {
  console.log(`Processing GitHub URL: ${githubUrl}`);
  
  try {
    // Check if MCP already exists
    const exists = await checkMcpExists(githubUrl);
    
    if (exists) {
      console.log(`MCP with URL ${githubUrl} already exists. Skipping.`);
      return { success: true };
    }
    
    // Prefetch MCP info from GitHub with rate limit retry
    console.log('Prefetching MCP info...');
    const prefetchResult = await withRateLimitRetry(() => prefetchMcpInfo(githubUrl));
    
    if (prefetchResult.error || !prefetchResult.data) {
      const errorMsg = `Failed to prefetch MCP info for ${githubUrl}: ${prefetchResult.error}`;
      console.error(errorMsg);
      
      // Save failed record immediately
      writeFailedItemsToCsv([{ record, error: errorMsg }], failedOutputPath);
      
      return { success: false, error: errorMsg };
    }
    
    // Submit the MCP with rate limit retry
    console.log('Submitting MCP...');
    const submitResult = await withRateLimitRetry(() => submitMcp({
      name: prefetchResult.data.name,
      author_name: prefetchResult.data.author_name,
      website_url: githubUrl,
      user_avatar_url: prefetchResult.data.user_avatar_url,
      email: '<EMAIL>',
      subscribe_newsletter: false,
      detail: prefetchResult.data.detail,
      preprocessinfo: prefetchResult.data.preprocessinfo
    }));
    
    if (submitResult.error || !submitResult.data) {
      const errorMsg = `Failed to submit MCP for ${githubUrl}: ${submitResult.error}`;
      console.error(errorMsg);
      
      // Save failed record immediately
      writeFailedItemsToCsv([{ record, error: errorMsg }], failedOutputPath);
      
      return { success: false, error: errorMsg };
    }
    
    console.log(`Successfully submitted MCP for ${githubUrl}`);
    return { success: true };
  } catch (error) {
    const errorMsg = `Error processing GitHub URL ${githubUrl}: ${error instanceof Error ? error.message : String(error)}`;
    console.error(errorMsg);
    
    // Save failed record immediately
    writeFailedItemsToCsv([{ record, error: errorMsg }], failedOutputPath);
    
    return { success: false, error: errorMsg };
  }
}

// Concurrency control function
async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  concurrency: number
): Promise<R[]> {
  const results: R[] = [];
  const inProgress: Promise<void>[] = [];
  const itemQueue = [...items];

  const startNext = async (): Promise<void> => {
    if (itemQueue.length === 0) return;
    
    const item = itemQueue.shift()!;
    const processingPromise = processor(item)
      .then(result => {
        results.push(result);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      })
      .catch(error => {
        console.error('Error in batch processing:', error);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      });
    
    inProgress.push(processingPromise);
  };

  // Start initial batch of tasks
  const initialBatchSize = Math.min(concurrency, items.length);
  for (let i = 0; i < initialBatchSize; i++) {
    await startNext();
  }

  // Wait for all tasks to complete
  await Promise.all(inProgress);
  return results;
}

async function main() {
  // Parse command line arguments
  const args = parseArgs();
  
  console.log(`Starting CSV import from ${args.csvFile}...`);
  console.log(`Allow public: ${args.allowPublic}`);
  console.log(`Concurrency: ${args.concurrency}`);
  
  const csvFilePath = path.resolve(args.csvFile);
  
  if (!fs.existsSync(csvFilePath)) {
    console.error(`CSV file not found: ${csvFilePath}`);
    process.exit(1);
  }
  
  // Set up output file for failed MCPs
  const inputFile = path.basename(args.csvFile);
  const failedOutputPath = path.join(path.dirname(args.csvFile), `.failed_${inputFile}`);
  
  // Read all records from CSV
  const records: McpRecord[] = [];
  
  // Process CSV file
  const parser = fs.createReadStream(csvFilePath).pipe(
    parse({
      columns: true,
      skip_empty_lines: true
    })
  );
  
  try {
    for await (const record of parser) {
      // Check if the row has a website_url column
      if (!record.website_url) {
        console.error('CSV file must contain a website_url column');
        process.exit(1);
      }
      
      records.push(record);
    }
  } catch (error) {
    console.error(`Error parsing CSV file: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
  
  console.log(`Found ${records.length} URLs in CSV file`);
  
  // Process GitHub URLs with concurrency
  const results = await processBatch(
    records,
    record => processGithubUrl(record.website_url, record, failedOutputPath),
    args.concurrency
  );
  
  // Extract successful and failed results
  const successfulResults = results.filter(result => result.success);
  const failedResults = results.filter(result => !result.success);
  
  // Print results
  console.log('\nImport Results:');
  console.log('--------------');
  
  console.log(`Total: ${results.length}`);
  console.log(`Success: ${successfulResults.length}`);
  console.log(`Failed: ${failedResults.length}`);
  
  if (failedResults.length > 0) {
    console.log('\nFailed URLs were logged to:', failedOutputPath);
  }
}

// Run the main function with improved error handling
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 