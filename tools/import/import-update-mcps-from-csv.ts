#!/usr/bin/env node

import * as dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';
import { CsvMcpRow, readCsvFile } from '../common/csv';
import { getGithubInfo } from '../common/github';
import { analyzeGithubInfoForMcp } from '../common/ai';
import { prefetchMcpInfo, createMcp, updateMcp } from '../common/api';
import { getMcpByUuid } from '../common/db';
import { Items, McpLocalization } from '../../nextjs/types/items';

// Load environment variables
dotenv.config();

// Helper function to handle rate limit errors
async function withRateLimitRetry<T>(fn: () => Promise<T>, maxRetries = 3): Promise<T> {
  let retries = 0;
  
  while (true) {
    try {
      return await fn();
    } catch (error: any) {
      // Check if it's a rate limit error (TPM, RPM, etc.)
      const isRateLimit = 
        error?.statusCode === 429 || 
        (error?.message && error.message.includes("rate limit")) ||
        (error?.responseBody && error.responseBody.includes("TPM limit")) ||
        (error?.responseBody && error.responseBody.includes("RPM limit"));
      
      if (isRateLimit && retries < maxRetries) {
        retries++;
        console.log(`Rate limit reached. Waiting 1 minute before retry (${retries}/${maxRetries})...`);
        // Wait for 1 minute (60000 ms)
        await new Promise(resolve => setTimeout(resolve, 60000));
        console.log('Retrying...');
      } else {
        // Re-throw the error if it's not a rate limit error or we've exceeded retries
        throw error;
      }
    }
  }
}

// Define command line arguments
interface CliArgs {
  csvFile: string;
  allowPublic: boolean;
  concurrency: number;
}

// Parse command line arguments
function parseArgs(): CliArgs {
  const args = process.argv.slice(2);
  const csvFileArg = args.find(arg => !arg.startsWith('--'));
  const allowPublicArg = args.includes('--allow-public');
  
  // Parse concurrency parameter
  const concurrencyArg = args.find(arg => arg.startsWith('--concurrency='));
  const concurrency = concurrencyArg 
    ? parseInt(concurrencyArg.split('=')[1], 10) 
    : 1;
  
  if (!csvFileArg) {
    console.error('Usage: import-update-mcps-from-csv <csv-file-path> [--allow-public] [--concurrency=N]');
    process.exit(1);
  }
  
  return {
    csvFile: csvFileArg,
    allowPublic: allowPublicArg,
    concurrency: isNaN(concurrency) || concurrency < 1 ? 1 : concurrency
  };
}

// Function to write failed MCPs to a CSV file
function writeFailedItemsToCsv(failedItems: CsvMcpRow[], outputPath: string): void {
  // Create CSV header
  const header = ['website_url', 'uuid', 'failure_reason'].join(',');
  
  // Create CSV rows
  const rows = failedItems.map(mcp => {
    return `"${mcp.website_url || ''}","${mcp.uuid || ''}","${mcp.failure_reason || ''}"`;
  });
  
  // Combine header and rows
  const csvContent = [header, ...rows].join('\n');
  
  // Write to file
  fs.writeFileSync(outputPath, csvContent, 'utf8');
  
  console.log(`Failed MCPs written to ${outputPath}`);
}

async function processMcp(row: CsvMcpRow, allowPublic: boolean): Promise<{ success: boolean; row: CsvMcpRow; error?: string }> {
  const { website_url, uuid } = row;
  
  console.log(`Processing ${website_url}...`);
  
  try {
    // Step 1: Prefetch MCP information using the API with rate limit retry
    console.log('Prefetching GitHub information...');
    const prefetchResult = await withRateLimitRetry(() => prefetchMcpInfo(website_url));
    
    if (prefetchResult.error || !prefetchResult.data) {
      const errorMsg = `Failed to prefetch GitHub information: ${prefetchResult.error}`;
      console.error(errorMsg);
      return { success: false, row, error: errorMsg };
    }

    // Use prefetched data
    const prefetchedData = prefetchResult.data;
    console.log("prefetch info success, data is: ", prefetchedData)
    
    // Analyze with AI to generate tags if not already present with rate limit retry
    console.log('Analyzing GitHub info with AI...');
    const aiAnalysis = await withRateLimitRetry(() => analyzeGithubInfoForMcp(
      prefetchedData.name,
      prefetchedData.brief || '',
      prefetchedData.detail || '',
      []
    ));
    console.log("ai analysis result is: ", aiAnalysis)
    if (aiAnalysis.tags.length == 0){
      const errorMsg = `Failed to generate AI summary, stop process ${website_url}`;
      console.error(errorMsg);
      return { success: false, row, error: errorMsg };
    }
    
    // Extract metadata from prefetched data
    const extractedMetadata = prefetchedData.preprocessinfo?.metadata || {};
    
    // Prepare MCP data
    const mcpData: Partial<Items> & { localizations?: Partial<McpLocalization>[] } = {
      uuid: uuid,
      name: prefetchedData.name,
      brief: prefetchedData.brief || aiAnalysis.brief,
      item_avatar_url: prefetchedData.item_avatar_url || '',
      user_avatar_url: prefetchedData.user_avatar_url || '',
      website_url: website_url,
      author_name: prefetchedData.author_name,
      is_recommended: false,
      is_official: false,
      allow_public: allowPublic,
      tags: aiAnalysis.tags,
      metadata: extractedMetadata,
      localizations: [
        {
          language_code: 'en',
          brief: prefetchedData.brief || aiAnalysis.brief,
          detail: prefetchedData.detail || '',
          processinfo: aiAnalysis.processinfo
        }
      ]
    };

    console.log("prepare to update/add mcp: ", mcpData)
    
    // Create or update MCP with rate limit retry
    if (uuid) {
      // Check if MCP exists
      const existingMcp = await getMcpByUuid(uuid);
      
      if (existingMcp) {
        // Update existing MCP
        console.log(`Updating MCP with UUID ${uuid}...`);
        // If exist mcp, some data use old
        mcpData.name = existingMcp.name;
        mcpData.item_avatar_url = existingMcp.item_avatar_url;
        mcpData.is_recommended = existingMcp.is_recommended;
        mcpData.is_official = existingMcp.is_official;
        mcpData.allow_public = existingMcp.allow_public;
        console.log("Exist Mcp, update some val to old: ", mcpData)
        const updateResult = await withRateLimitRetry(() => updateMcp(uuid, mcpData));
        
        if (updateResult.error) {
          const errorMsg = `Failed to update MCP: ${updateResult.error}`;
          throw new Error(errorMsg);
        }
        
        console.log(`Successfully updated MCP: ${mcpData.name}`);
      } else {
        // Create new MCP with provided UUID
        console.log(`Creating new MCP with provided UUID ${uuid}...`);
        const createResult = await withRateLimitRetry(() => createMcp(mcpData));
        
        if (createResult.error) {
          const errorMsg = `Failed to create MCP: ${createResult.error}`;
          throw new Error(errorMsg);
        }
        
        console.log(`Successfully created MCP: ${mcpData.name}`);
      }
    } else {
      // Create new MCP without UUID (will be generated)
      console.log('Creating new MCP...');
      const createResult = await withRateLimitRetry(() => createMcp(mcpData));
      
      if (createResult.error) {
        const errorMsg = `Failed to create MCP: ${createResult.error}`;
        throw new Error(errorMsg);
      }
      
      console.log(`Successfully created MCP: ${mcpData.name}`);
    }
    
    return { success: true, row };
  } catch (error) {
    const errorMsg = `Error processing ${website_url}: ${error instanceof Error ? error.message : String(error)}`;
    console.error(errorMsg);
    return { success: false, row, error: errorMsg };
  }
}

// Concurrency control function with error tracking
async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  concurrency: number
): Promise<R[]> {
  const results: R[] = [];
  const inProgress: Promise<void>[] = [];
  const itemQueue = [...items];

  const startNext = async (): Promise<void> => {
    if (itemQueue.length === 0) return;
    
    const item = itemQueue.shift()!;
    const processingPromise = processor(item)
      .then(result => {
        results.push(result);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      })
      .catch(error => {
        console.error('Error in batch processing:', error);
        const index = inProgress.indexOf(processingPromise);
        if (index !== -1) {
          inProgress.splice(index, 1);
        }
        return startNext();
      });
    
    inProgress.push(processingPromise);
  };

  // Start initial batch of tasks
  const initialBatchSize = Math.min(concurrency, items.length);
  for (let i = 0; i < initialBatchSize; i++) {
    await startNext();
  }

  // Wait for all tasks to complete
  await Promise.all(inProgress);
  return results;
}

async function main() {
  // Parse command line arguments
  const args = parseArgs();
  
  console.log(`Starting MCP import from CSV: ${args.csvFile}`);
  console.log(`Allow public: ${args.allowPublic}`);
  console.log(`Concurrency: ${args.concurrency}`);
  
  try {
    // Read CSV file
    const csvRows = readCsvFile(args.csvFile);
    console.log(`Found ${csvRows.length} rows in CSV file`);
    
    // Process rows with concurrency
    const results = await processBatch(
      csvRows,
      row => processMcp(row, args.allowPublic),
      args.concurrency
    );
    
    // Extract successful and failed MCPs
    const successfulResults = results.filter(result => result.success);
    const failedResults = results.filter(result => !result.success);
    
    // Add failure reason to failed MCPs
    const failedItems = failedResults.map(result => ({
      ...result.row,
      failure_reason: result.error
    }));
    
    // Write failed MCPs to CSV if there are any
    if (failedItems.length > 0) {
      const inputFile = path.basename(args.csvFile);
      const outputFile = path.join(path.dirname(args.csvFile), `.failed_${inputFile}`);
      writeFailedItemsToCsv(failedItems, outputFile);
    }
    
    console.log(`Import completed. Successfully processed ${successfulResults.length} MCPs. Failed: ${failedResults.length}`);
  } catch (error) {
    console.error('Error during CSV import:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 