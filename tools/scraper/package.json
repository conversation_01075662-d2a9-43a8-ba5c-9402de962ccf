{"name": "jike-scraper", "version": "1.0.0", "description": "Jike scraper service for extracting and storing media content", "main": "dist/jike-scraper.js", "scripts": {"build": "tsc", "start": "node dist/jike-scraper.js", "dev": "ts-node jike-scraper.ts", "lint": "eslint . --ext .ts", "test": "jest"}, "keywords": ["jike", "scraper", "media", "content", "extraction"], "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.7", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "dotenv": "^16.4.5", "express": "^4.18.3", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}