import axios from 'axios';
import * as cheerio from 'cheerio';
import { createObjectCsvWriter } from 'csv-writer';
import * as path from 'path';
import * as fs from 'fs';
import { Items } from '../../nextjs/types/items';

interface McpRepoInfo {
  name: string;
  website_url: string;
  is_uploaded: boolean;
}

const awesomeLists: string[] = [
  "https://github.com/punkpeye/awesome-mcp-servers/blob/main/README.md",
  "https://github.com/wong2/awesome-mcp-servers/blob/main/README.md",
  "https://github.com/appcypher/awesome-mcp-servers/blob/main/README.md",
  "https://github.com/r-huijts/awesome-mcp-servers/blob/main/README.md",
  "https://github.com/RafalWilinski/awesome-mcp-servers/blob/main/README.md"
];

const otherJsonLists: string[] = [
  "https://glama.ai/mcp/servers.json"
];

const dataDir = path.join(__dirname); // Relative to the script location
const outputFile = path.join(dataDir, '.awesome_mcp.csv');

async function extractGithubLinks(url: string): Promise<string[]> {
  try {
    const response = await axios.get(url);
    const $ = cheerio.load(response.data);
    const links: string[] = [];
    $('a[href]').each((_, element) => {
      const href = $(element).attr('href');
      if (href && href.includes("github.com") && (href.includes("mcp") || href.includes("server"))) { // Broaden search slightly
        // Handle relative GitHub links
        if (href.startsWith('/')) {
            links.push(`https://github.com${href}`);
        } else if (href.startsWith('http')) {
            links.push(href);
        }
      }
    });
    // Filter for valid GitHub repo URLs specifically
    return links.filter(link => /^https:\/\/github\.com\/[^/]+\/[^/]+(\/)?$/.test(link.split('#')[0].split('?')[0]));
  } catch (error) {
    console.error(`Error fetching URL: ${url} - ${error instanceof Error ? error.message : error}`);
    return [];
  }
}

function parseGithubLink(link: string): McpRepoInfo | null {
  const match = link.match(/https:\/\/github\.com\/([^/]+)\/([^/]+)/);
  if (match) {
    const repoName = match[2].replace(/(\/|\.git)$/, ''); // Clean repo name
    const githubUrl = `https://github.com/${match[1]}/${repoName}`; // Standardize URL
    return {
      name: repoName,
      website_url: githubUrl,
      is_uploaded: false
    };
  }
  return null;
}

async function mcpGetPageContent(url: string): Promise<string | null> {
  const headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  };
  try {
    const response = await axios.get(url, { headers });
    return response.data;
  } catch (error) {
    console.error(`Error fetching page ${url}: ${error instanceof Error ? error.message : error}`);
    return null;
  }
}

function mcpGithublinkFromHtml(htmlContent: string): string[] {
  const pattern = /https?:\/\/(?:www\.)?github\.com\/[a-zA-Z0-9_.-]+\/[a-zA-Z0-9_.-]+/g;
  const rawMatches = htmlContent.match(pattern) || [];
  const decodedMatches = rawMatches.map(match => decodeURIComponent(match));

  // Use Set for efficient deduplication while preserving order approximately
  const seen = new Set<string>();
  const uniqueLinks: string[] = [];
  for (const link of decodedMatches) {
      const cleanedLink = link.replace(/(\/|\.git)$/, ''); // Clean trailing slash or .git
      if (!seen.has(cleanedLink)) {
          seen.add(cleanedLink);
          uniqueLinks.push(cleanedLink);
      }
  }
  return uniqueLinks;
}

async function mcpFetchHomePage(): Promise<string[]> {
  const baseUrl = 'https://mcp.so/';
  const htmlContent = await mcpGetPageContent(baseUrl);
  let parsedData: string[] = [];

  if (htmlContent) {
    parsedData = mcpGithublinkFromHtml(htmlContent);
  }
  return parsedData;
}

async function main() {
  let allData: McpRepoInfo[] = [];

  // Fetch from Awesome Lists
  console.log("Fetching from Awesome lists...");
  for (const listUrl of awesomeLists) {
    console.log(`Fetching awesome list: ${listUrl}`);
    const githubLinks = await extractGithubLinks(listUrl);
    console.log(`Found ${githubLinks.length} github links`);
    for (const link of githubLinks) {
      const parsedLink = parseGithubLink(link);
      if (parsedLink) {
        console.log(`Found ${parsedLink.name} in ${parsedLink.website_url}`);
        allData.push(parsedLink);
      }
      else {
        console.log(`No parsed link found for ${link}`);
      }
    }
  }
  console.log(`Awesome lists done. Current count: ${allData.length}`);

  // Fetch from Other JSON Lists
  console.log("\nFetching from other JSON lists...");
  for (const listUrl of otherJsonLists) {
    console.log(`Fetching other json: ${listUrl}`);
    try {
      const response = await axios.get(listUrl);
      const jsonData = response.data;
      if (jsonData && Array.isArray(jsonData.servers)) {
        for (const item of jsonData.servers) {
          if (item.githubUrl) {
            const parsedLink = parseGithubLink(item.githubUrl);
             if (parsedLink) {
                allData.push(parsedLink);
             }
          }
        }
      } else {
         console.warn(`Unexpected JSON structure from ${listUrl}`);
      }
    } catch (error) {
      console.error(`Error fetching or parsing JSON URL: ${listUrl} - ${error instanceof Error ? error.message : error}`);
      continue;
    }
  }
  console.log(`Other JSON lists done. Current count: ${allData.length}`);

  // Fetch from mcp.so Home Page
  console.log("\nFetching mcp.so home page...");
  const mcpsoList = await mcpFetchHomePage();
  for (const item of mcpsoList) {
     const parsedLink = parseGithubLink(item);
     if (parsedLink) {
        allData.push(parsedLink);
     }
  }
  console.log(`Fetching mcp.so home page done. Current count: ${allData.length}`);


  // Deduplicate data based on website_url
  const uniqueDataMap = new Map<string, McpRepoInfo>();
  allData.forEach(item => {
    if (!uniqueDataMap.has(item.website_url)) {
      uniqueDataMap.set(item.website_url, item);
    }
  });
  const uniqueData = Array.from(uniqueDataMap.values());

  console.log(`\nTotal unique MCPs found: ${uniqueData.length}`);

  // compare with existing mcps from backup/data/mcps_least.json
  const existingItems = JSON.parse(fs.readFileSync(path.join(__dirname, '../backup/data/mcps_least.json'), 'utf8'));
  const existingItemsSet = new Set(existingItems.map((mcp: Items) => mcp.website_url));
  const newItems = uniqueData.filter((mcp: McpRepoInfo) => !existingItemsSet.has(mcp.website_url));
  console.log(`\nTotal new MCPs found: ${newItems.length}`);

  // Ensure data directory exists
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log(`Created directory: ${dataDir}`);
  }

  // Write data to CSV
  const csvWriter = createObjectCsvWriter({
    path: outputFile,
    header: [
      { id: 'name', title: 'name' },
      { id: 'website_url', title: 'website_url' }
    ]
  });

  try {
    await csvWriter.writeRecords(uniqueData);
    console.log(`Data successfully saved to ${outputFile}`);
  } catch (error) {
     console.error(`Error writing CSV file: ${error instanceof Error ? error.message : error}`);
  }
}

main().catch(error => {
  console.error("An error occurred during script execution:", error);
  process.exit(1);
});
