import express from 'express';
import axios from 'axios';
import cheerio from 'cheerio';
import cron from 'node-cron';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Database configuration
const supabaseUrl = process.env.SUPABASE_URL || 'https://towzzcheowkqpfrbkipc.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvd3p6Y2hlb3drcXBmcmJraXBjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTE0MzMxMTAsImV4cCI6MjAyNzAwOTExMH0.iOagRMsTfxKEYsm_BA5noPg8qrac5oRXiB8brXrLdVo';
const supabaseTable = process.env.SUPABASE_TABLE || 'media_stats';
const platform = 'jike';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Interface definitions
interface JikeUser {
  username: string;
  screenName: string;
  avatarImage: string;
  profileUrl: string;
}

interface JikePicture {
  picUrl: string;
  format: string;
}

interface JikePost {
  id: string;
  content: string;
  pictures: JikePicture[];
  user: JikeUser;
  createdAt: string;
  url: string;
  likeCount: number;
}

interface MediaStats {
  followed_count: number;
  user_name: string;
  screen_name: string;
  liked: number;
  url: string;
  platform: string;
  created_at?: string;
}

// Create Express app
const app = express();
app.use(express.json());

// API to get media stats
app.get('/api/media-stats', async (req, res) => {
  try {
    const { platform, id } = req.query;

    if (!platform || !id) {
      return res.status(400).json({ error: 'Missing required query parameters: platform and id' });
    }

    console.log(`[API] get request to: ${platform}, ${id}`);

    const { data, error } = await supabase
      .from(supabaseTable)
      .select('*')
      .eq('user_name', id)
      .eq('platform', platform)
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error querying database:', error);
      return res.status(500).json({ error: 'Database query error' });
    }

    if (data && data.length > 0) {
      res.status(200).json(data[0]);
    } else {
      res.status(404).json({ error: 'No records found matching the given criteria' });
    }
  } catch (error) {
    console.error('Error fetching data from Supabase:', error);
    res.status(500).json({ error: 'Internal server error while fetching data from Supabase.' });
  }
});

// API to extract Jike post content
app.post('/api/jike/extract', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'Missing required parameter: url' });
    }

    if (!url.includes('okjike.com')) {
      return res.status(400).json({ error: 'Invalid Jike URL' });
    }

    const postId = extractJikeId(url);
    if (!postId) {
      return res.status(400).json({ error: 'Could not extract post ID from URL' });
    }

    const post = await getJikePost(postId);
    if (!post) {
      return res.status(404).json({ error: 'Failed to fetch Jike post data' });
    }

    // Format response to match the structure expected by the frontend
    const response = {
      success: true,
      data: {
        type: 'jike',
        id: postId,
        authorName: post.user.screenName,
        authorAvatar: post.user.avatarImage,
        content: post.content,
        imageUrls: post.pictures.map(pic => pic.picUrl),
        videoUrls: []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error extracting Jike content:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to extract Jike content'
    });
  }
});

// Extract Jike ID from URL
function extractJikeId(url: string): string | null {
  try {
    // Handle Jike URL format
    // https://m.okjike.com/originalPosts/POST_ID
    const regex = /okjike\.com\/(?:originalPosts|repost)\/([a-zA-Z0-9]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  } catch (error) {
    console.error('Error extracting Jike ID:', error);
    return null;
  }
}

// Fetch and parse Jike post data
async function getJikePost(id: string): Promise<JikePost | undefined> {
  try {
    const response = await axios.get(`https://m.okjike.com/originalPosts/${id}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
      }
    });

    if (!response.data) {
      throw new Error('Failed to fetch Jike post');
    }

    const html = response.data;
    const $ = cheerio.load(html);
    
    // Extract next.js data
    const dataStr = $('#__NEXT_DATA__').text().trim();
    let jsonData;
    
    try {
      jsonData = JSON.parse(dataStr);
    } catch (e) {
      console.error('Failed to parse JSON data:', e);
      // Fall back to DOM parsing if JSON parsing fails
      return parseDomForPost($, id);
    }
    
    // Try to extract from next.js data first
    if (jsonData && jsonData.props && jsonData.props.pageProps && jsonData.props.pageProps.post) {
      const post = jsonData.props.pageProps.post;
      
      return {
        id,
        content: post.content || '',
        pictures: post.pictures || [],
        user: {
          username: post.user.username,
          screenName: post.user.screenName,
          avatarImage: post.user.avatarImage?.thumbnailUrl || post.user.avatarImage?.picUrl || '',
          profileUrl: `https://m.okjike.com/users/${post.user.username}`
        },
        createdAt: post.createdAt,
        url: `https://m.okjike.com/originalPosts/${id}`,
        likeCount: post.likeCount || 0
      };
    }
    
    // Fall back to DOM parsing
    return parseDomForPost($, id);
  } catch (error) {
    console.error('Error fetching Jike post:', error);
    return undefined;
  }
}

// Parse post data from DOM when JSON data is not available
function parseDomForPost($: cheerio.CheerioAPI, id: string): JikePost | undefined {
  try {
    // Find elements using selectors
    const findBySelector = (selector: string): cheerio.Cheerio => {
      return $(selector);
    };
    
    // Extract user info
    const userInfo = findBySelector('.user-info');
    const avatarImg = findBySelector('.avatar img');
    const username = userInfo.find('.username').text().trim();
    const timestamp = userInfo.find('.time').text().trim();
    
    // Extract post content
    const contentElement = findBySelector('.content');
    const content = contentElement.text().trim();
    
    // Extract images
    const images = findBySelector('.images img');
    const pictures: JikePicture[] = [];
    
    images.each((_, img) => {
      const imgUrl = $(img).attr('src');
      if (imgUrl) {
        pictures.push({
          picUrl: imgUrl,
          format: 'jpeg' // Default format
        });
      }
    });
    
    // Extract likes count
    const likeCount = findBySelector('.like-count').text().trim();
    
    // Get user profile link
    const userProfileLink = userInfo.find('a').attr('href') || '';
    const userProfileId = userProfileLink.split('/').pop() || '';
    
    // Get avatar image URL
    const avatarUrl = avatarImg.attr('src') || '';
    
    return {
      id,
      content,
      pictures,
      user: {
        username: userProfileId,
        screenName: username,
        avatarImage: avatarUrl,
        profileUrl: `https://m.okjike.com/users/${userProfileId}`
      },
      createdAt: timestamp,
      url: `https://m.okjike.com/originalPosts/${id}`,
      likeCount: parseInt(likeCount) || 0
    };
  } catch (error) {
    console.error('Error parsing DOM for Jike post:', error);
    return undefined;
  }
}

// Function to fetch and store Jike user data
async function fetchAndStoreUserStats(url: string) {
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0'
  };

  try {
    const response = await axios.get(url, { headers });

    if (response.status !== 200) {
      throw new Error(`Failed to fetch data with status code ${response.status}`);
    }

    const $ = cheerio.load(response.data);
    const dataStr = $("#__NEXT_DATA__").text().trim();
    const data = JSON.parse(dataStr);

    // Extract user data
    const result: MediaStats = {
      followed_count: data.props.pageProps.user.statsCount.followedCount || 0,
      user_name: data.props.pageProps.user.username || '',
      screen_name: data.props.pageProps.user.screenName || '',
      liked: data.props.pageProps.user.statsCount.liked || 0,
      url: url,
      platform: platform
    };

    console.log(`Fetched Jike user data:`, result);

    // Store data in Supabase
    const { data: insertData, error } = await supabase
      .from(supabaseTable)
      .insert(result);

    if (error) {
      console.error('Error storing data in Supabase:', error);
      return;
    }

    console.log("Data fetched and stored successfully.");
  } catch (error) {
    console.error("Error fetching and storing Jike user data:", error);
  }
}

// Load and process users list
async function loadAndProcessUsers() {
  try {
    // Check if a users file exists
    const usersFilePath = path.resolve(__dirname, 'jike-users.json');
    
    if (fs.existsSync(usersFilePath)) {
      const fileContent = fs.readFileSync(usersFilePath, 'utf8');
      const users = JSON.parse(fileContent);
      
      // Process each user
      for (const user of users) {
        const userUrl = user.url || `http://m.okjike.com/users/${user.id}`;
        console.log(`Processing user: ${user.id}`);
        await fetchAndStoreUserStats(userUrl);
        
        // Add a small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } else {
      // Default user if no file is provided
      const defaultUserUrl = process.env.DEFAULT_JIKE_USER_URL || 'http://m.okjike.com/users/4E822130-4B7B-4DB9-A4CA-B326397ADB32';
      await fetchAndStoreUserStats(defaultUserUrl);
    }
    
    console.log("User processing complete");
  } catch (error) {
    console.error("Error processing users:", error);
  }
}

// Schedule data fetching
function scheduleDataFetching() {
  // Schedule to run every hour
  const cronSchedule = process.env.CRON_SCHEDULE || '0 * * * *';
  cron.schedule(cronSchedule, loadAndProcessUsers);
  console.log(`Scheduled data fetching with cron: ${cronSchedule}`);
  
  // Run immediately on startup
  loadAndProcessUsers();
}

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Jike Scraper service running on port ${PORT}`);
  scheduleDataFetching();
});

// Export for testing purposes
export {
  extractJikeId,
  getJikePost
}; 