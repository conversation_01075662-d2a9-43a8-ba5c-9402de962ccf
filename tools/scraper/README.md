# Jike Scraper Service

A TypeScript-based service for scraping Jike social media content and storing statistics in a Supabase database.

## Features

- Extract Jike post content (text, images) from URLs
- Track Jike user statistics (followers, likes)
- Store data in Supabase
- Scheduled data collection via cron jobs
- Docker support for easy deployment

## API Endpoints

- `POST /api/jike/extract` - Extract content from a Jike post URL
- `GET /api/media-stats?platform=jike&id=user_id` - Get the latest stats for a specific user

## Installation

### Prerequisites

- Node.js 16+
- npm or yarn
- [Optional] Docker and Docker Compose for containerized deployment

### Local Development

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example`
4. Run the development server:
   ```
   npm run dev
   ```

### Docker Deployment

1. Create a `.env` file based on `.env.example`
2. [Optional] Update `jike-users.json` with the users you want to track
3. Build and start the Docker container:
   ```
   docker-compose up -d
   ```

## Environment Variables

- `PORT` - Server port (default: 3001)
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_KEY` - Supabase API key
- `SUPABASE_TABLE` - Table name for storing media stats
- `DEFAULT_JIKE_USER_URL` - Default Jike user URL to monitor
- `CRON_SCHEDULE` - Cron schedule for data collection (default: every hour)

## Usage in MistralOCR

This service is used by MistralOCR to provide Jike content extraction functionality, replacing the previous in-app implementation in `media.ts` that wasn't compatible with the Edge Runtime.

### Example API Call

```typescript
// Extract content from a Jike post
const response = await fetch('http://localhost:3001/api/jike/extract', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    url: 'https://m.okjike.com/originalPosts/POST_ID'
  })
});

const data = await response.json();
console.log(data);
```

## License

MIT 