version: '3.8'

services:
  jike-scraper:
    build: .
    container_name: jike-scraper
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_TABLE=${SUPABASE_TABLE}
      - DEFAULT_JIKE_USER_URL=${DEFAULT_JIKE_USER_URL}
      - CRON_SCHEDULE=${CRON_SCHEDULE}
    volumes:
      - ./jike-users.json:/app/jike-users.json 