#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';

// Get the backup directory path
const backupDir = path.join(__dirname, '/data');

function getLatestFileInDir(dir: string, prefix: string): string | null {
  if (!fs.existsSync(dir)) {
    console.error(`Directory ${dir} does not exist.`);
    return null;
  }
  
  // Get all files in the directory
  const files = fs.readdirSync(dir)
    .filter(file => file.startsWith(prefix))
    .sort()
    .reverse(); // Sort in descending order to get the latest file first
  
  if (files.length === 0) {
    console.error(`No files with prefix ${prefix} found in ${dir}.`);
    return null;
  }
  
  return files[0];
}

function copyLatestBackup(prefix: string) {
  const latestFile = getLatestFileInDir(backupDir, prefix);
  
  if (!latestFile) {
    return false;
  }
  
  // Create the reference filename with -least suffix
  const extension = path.extname(latestFile);
  const referenceFileName = `${prefix}least${extension}`;
  
  // Source and destination paths
  const sourcePath = path.join(backupDir, latestFile);
  const destPath = path.join(backupDir, referenceFileName);
  
  try {
    // Copy the file
    fs.copyFileSync(sourcePath, destPath);
    console.log(`Successfully copied ${latestFile} to ${referenceFileName}`);
    return true;
  } catch (error) {
    console.error(`Error copying ${latestFile}:`, error);
    return false;
  }
}

function main() {
  console.log('Creating reference files for comparison...');
  
  // Copy the latest mcps backup file
  const mcpsSuccess = copyLatestBackup('mcps_');
  
  // Copy the latest item_localizations backup file
  const localizationsSuccess = copyLatestBackup('item_localizations_');
  
  // Copy the latest submissions backup file
  const submissionsSuccess = copyLatestBackup('submissions_');
  
  if (mcpsSuccess && localizationsSuccess && submissionsSuccess) {
    console.log('All reference files created successfully!');
  } else {
    console.error('Error creating some reference files.');
    process.exit(1);
  }
}

// Run the main function
main(); 