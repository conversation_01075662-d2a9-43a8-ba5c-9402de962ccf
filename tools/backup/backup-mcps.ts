#!/usr/bin/env node

import * as dotenv from 'dotenv';
import { getAllItems, getAllMcpLocalizations, exportSubmissions, saveBackup } from '../common/db';

// Load environment variables
dotenv.config();

async function main() {
  console.log('Starting MCPs database backup...');
  
  try {
    // Backup MCPs
    console.log('Exporting mcps.mcps table...');
    const mcps = await getAllItems();
    const mcpsBackupFile = saveBackup(mcps, 'mcps');
    console.log(`Successfully backed up ${mcps.length} MCPs to ${mcpsBackupFile}`);
    
    // Backup MCP localizations
    console.log('Exporting mcps.item_localizations table...');
    const localizations = await getAllMcpLocalizations();
    const localizationsBackupFile = saveBackup(localizations, 'item_localizations');
    console.log(`Successfully backed up ${localizations.length} MCP localizations to ${localizationsBackupFile}`);
    
    // Backup MCP submissions
    console.log('Exporting mcps.submissions table...');
    const submissions = await exportSubmissions();
    const submissionsBackupFile = saveBackup(submissions, 'submissions');
    console.log(`Successfully backed up ${submissions.length} MCP submissions to ${submissionsBackupFile}`);
    
    console.log('Database backup completed successfully!');
  } catch (error) {
    console.error('Error during database backup:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 