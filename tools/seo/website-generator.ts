#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';
import { openAIChat } from '../common/ai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * 1. Generate TDK based on given keywords
 * 2. Generate hero content for the website
 * 3. Generate FAQ content for the website
 * 4. Generate blog article titles, descriptions, and content
 */

// Define interfaces
interface TDK {
  title: string;
  description: string;
  keywords: string;
}

interface Hero {
  title: string;
  description: string;
}

interface FAQ {
  question: string;
  answer: string;
}

interface BlogArticle {
  title: string;
  description: string;
  fileName?: string;
}

interface BlogContent {
  articles: BlogArticle[];
}

/**
 * Extract JSON content from AI response text
 * @param text AI response text that may contain <PERSON><PERSON><PERSON> in markdown code blocks
 * @returns Extracted content or null if not found
 */
async function extractJsonContent(text: string): Promise<string | null> {
  const reg = /```json\n([\s\S]*?)\n```/s;
  const formatted = reg.exec(text);
  return formatted ? formatted[1] : null;
}

/**
 * Parse JSON safely with fallback
 * @param text Text to parse as JSO<PERSON>
 * @returns Parsed JSON object or null if parsing fails
 */
function safeJsonParse(text: string): any | null {
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error('Failed to parse JSON:', e);
    return null;
  }
}

/**
 * Generate website TDK (Title, Description, Keywords)
 * @param keyword Main keyword for the website
 * @param description Description of the website
 * @param outputPath Path to save the output
 */
async function generateTDK(
  keyword: string, 
  description: string, 
  outputPath: string
): Promise<TDK | null> {
  const prompt = `
    I want you to act as an SEO expert. As an SEO expert, you have extensive knowledge and experience in helping websites improve their ranking in search engine results, attracting more traffic and users.
    You are familiar with the algorithms and rules of various search engines, and can use various strategies and techniques to optimize website content, structure, and links to improve their visibility in search results.
    I am developing a website with the keyword: ${keyword}, and the main function of the website is: ${description}. Based on this information, please provide suitable TDK (in English).
    Please respond in JSON format without any irrelevant information for easier script parsing. Response format example: {"title":"","description":"","keywords":""}
  `;
  
  let tdkResponse = await openAIChat(prompt);
  let tdkContent = await extractJsonContent(tdkResponse);
  
  // If no JSON content was extracted, try parsing the whole response
  if (!tdkContent) {
    tdkContent = tdkResponse;
  }
  
  const tdk = safeJsonParse(tdkContent || '');
  
  if (tdk) {
    // Save to file
    fs.writeFileSync(path.join(outputPath, '.tdk.json'), JSON.stringify(tdk, null, 2));
    console.log('TDK generated:', tdk);
    return tdk;
  }
  
  console.error('Failed to generate TDK');
  return null;
}

/**
 * Generate hero section content
 * @param keyword Main keyword for the website
 * @param description Description of the website
 * @param outputPath Path to save the output
 */
async function generateHero(
  keyword: string, 
  description: string, 
  outputPath: string
): Promise<Hero | null> {
  const prompt = `
    I want you to act as an SEO expert. As an SEO expert, you have extensive knowledge and experience in helping websites improve their ranking in search engine results, attracting more traffic and users.
    You are familiar with the algorithms and rules of various search engines, and can use various strategies and techniques to optimize website content, structure, and links to improve their visibility in search results.
    I am developing a website with the keyword: ${keyword}, and the main function of the website is: ${description}. Based on this information, please provide a suitable hero section headline and description for the website (in English).
    Please respond in JSON format without any irrelevant information for easier script parsing. Response format example: {"title":"","description":""}
  `;
  
  let heroResponse = await openAIChat(prompt);
  let heroContent = await extractJsonContent(heroResponse);
  
  // If no JSON content was extracted, try parsing the whole response
  if (!heroContent) {
    heroContent = heroResponse;
  }
  
  const hero = safeJsonParse(heroContent || '');
  
  if (hero) {
    // Save to file
    fs.writeFileSync(path.join(outputPath, '.hero.json'), JSON.stringify(hero, null, 2));
    console.log('Hero content generated:', hero);
    return hero;
  }
  
  console.error('Failed to generate hero content');
  return null;
}

/**
 * Generate FAQ content
 * @param keyword Main keyword for the website
 * @param description Description of the website
 * @param outputPath Path to save the output
 */
async function generateFAQ(
  keyword: string, 
  description: string, 
  outputPath: string
): Promise<FAQ[] | null> {
  const prompt = `
    I want you to act as an SEO expert. As an SEO expert, you have extensive knowledge and experience in helping websites improve their ranking in search engine results, attracting more traffic and users.
    You are familiar with the algorithms and rules of various search engines, and can use various strategies and techniques to optimize website content, structure, and links to improve their visibility in search results.
    I am developing a website with the keyword: ${keyword}, and the main function of the website is: ${description}. Based on this information, please provide suitable FAQs (in English).
    Please respond in JSON format without any irrelevant information for easier script parsing. Response format example: [{"question":"","answer":""},{"question":"","answer":""}]
  `;
  
  let faqResponse = await openAIChat(prompt);
  let faqContent = await extractJsonContent(faqResponse);
  
  // If no JSON content was extracted, try parsing the whole response
  if (!faqContent) {
    faqContent = faqResponse;
  }
  
  const faq = safeJsonParse(faqContent || '');
  
  if (faq) {
    // Save to file
    fs.writeFileSync(path.join(outputPath, '.faq.json'), JSON.stringify(faq, null, 2));
    console.log('FAQ content generated:', faq);
    return faq;
  }
  
  console.error('Failed to generate FAQ content');
  return null;
}

/**
 * Generate blog article ideas
 * @param keyword Main keyword for the website
 * @param description Description of the website
 * @param outputPath Path to save the output
 */
async function generateBlogs(
  keyword: string, 
  description: string, 
  outputPath: string
): Promise<BlogContent | null> {
  const prompt = `
    I want you to act as an SEO expert. As an SEO expert, you have extensive knowledge and experience in helping websites improve their ranking in search engine results, attracting more traffic and users.
    You are familiar with the algorithms and rules of various search engines, and can use various strategies and techniques to optimize website content, structure, and links to improve their visibility in search results.
    I am developing a website with the keyword: ${keyword}, and the main function of the website is: ${description}.
    Please help me create blog post titles and summary descriptions based on these materials (in English).
    Requirements:
      1. Increase the keyword density of ${keyword}
      2. Excellent SEO
      3. Write titles as questions from a user's perspective
      4. At least 3 articles
    Please respond in JSON format. Response format example: {"articles":[{"title":"","description":""},{"title":"","description":""}]}
  `;
  
  let blogsResponse = await openAIChat(prompt);
  
  try {
    // Try to parse the response directly
    const parsedBlogs = safeJsonParse(blogsResponse);
    if (parsedBlogs) {
      fs.writeFileSync(path.join(outputPath, 'blogs.json'), JSON.stringify(parsedBlogs, null, 2));
      console.log('Blog ideas generated:', parsedBlogs);
      return parsedBlogs;
    }
  } catch (e) {
    // If direct parsing fails, try extracting JSON content
    const blogsContent = await extractJsonContent(blogsResponse);
    if (blogsContent) {
      const blogs = safeJsonParse(blogsContent);
      if (blogs) {
        fs.writeFileSync(path.join(outputPath, 'blogs.json'), JSON.stringify(blogs, null, 2));
        console.log('Blog ideas generated:', blogs);
        return blogs;
      }
    }
  }
  
  console.error('Failed to generate blog ideas');
  return null;
}

/**
 * Generate full blog content for each article
 * @param keyword Main keyword for the website
 * @param description Description of the website
 * @param outputPath Path to save the output
 */
async function generateBlogContent(
  keyword: string, 
  description: string, 
  outputPath: string
): Promise<void> {
  // Read blogs.json file
  const blogsPath = path.join(outputPath, 'blogs.json');
  if (!fs.existsSync(blogsPath)) {
    console.error('blogs.json file not found. Run generateBlogs first.');
    return;
  }
  
  const blogs: BlogContent = safeJsonParse(fs.readFileSync(blogsPath, 'utf-8'));
  if (!blogs || !blogs.articles) {
    console.error('Invalid blog data in blogs.json');
    return;
  }
  
  // Create blog directory if it doesn't exist
  const blogDir = path.join(outputPath, 'blogs');
  if (!fs.existsSync(blogDir)) {
    fs.mkdirSync(blogDir, { recursive: true });
  }
  
  // Generate content for each blog
  for (const blog of blogs.articles) {
    const prompt = `
      I want you to act as an SEO expert. As an SEO expert, you have extensive knowledge and experience in helping websites improve their ranking in search engine results, attracting more traffic and users.
      You are familiar with the algorithms and rules of various search engines, and can use various strategies and techniques to optimize website content, structure, and links to improve their visibility in search results.
      I am developing a website with the keyword: ${keyword}, and the main function of the website is: ${description}. The blog title is: ${blog.title}, and the blog description is: ${blog.description}.
      Please help me create the full blog content based on these materials (in English).
      Requirements:
        1. Increase the keyword density of ${keyword}
        2. Excellent SEO
        3. Return in markdown format and follow the format below
      Response format example:
        ---
        title: 'How to Make a Picture Have a Transparent Background Using the Best Online Tools'
        slug: how-to-make-a-picture-have-a-transparent-background
        description: 'Learn how to easily create transparent backgrounds for your pictures using top online tools like Adobe Express and Photoroom. Enhance your images for professional and personal use with simple steps.'
        createdAt: '${new Date().toISOString().split('T')[0]} ${new Date().toTimeString().split(' ')[0]}'
        fileName: How-to-Make-a-Picture-Have-a-Transparent-Background.mdx
        image: https://example.com/image.webp
        ---
        
        Blog content here...
    `;
    
    const blogContent = await openAIChat(prompt);
    console.log(`Generated content for blog: ${blog.title}`);
    
    // Extract markdown content
    const markdownReg = /```markdown\n([\s\S]*)\n```/g;
    const match = markdownReg.exec(blogContent);
    
    // Generate filename from title
    const titleWords = blog.title.match(/[a-zA-Z]+/g);
    const fileName = titleWords ? `${titleWords.join('-')}.mdx` : `blog-${Date.now()}.mdx`;
    blog.fileName = fileName;
    
    // Save content to file
    if (match) {
      const extractedContent = match[1];
      fs.writeFileSync(path.join(blogDir, fileName), extractedContent);
    } else {
      fs.writeFileSync(path.join(blogDir, fileName), blogContent);
    }
  }
  
  // Update blogs.json with filenames
  fs.writeFileSync(path.join(outputPath, 'blogs.json'), JSON.stringify(blogs, null, 2));
}

/**
 * Generate color scheme based on primary color
 * @param mainColor Primary color in hex or RGB format
 */
async function modifyPrimaryColor(mainColor: string): Promise<string> {
  const prompt = `Main color is: ${mainColor}

Please modify the following color content:
 primary: {
          DEFAULT: '#ec008c',
          50: 'rgb(255, 230, 204)',
          100: 'rgb(255, 204, 153)',
          200: 'rgb(255, 178, 102)',
          300: 'rgb(255, 153, 51)',
          400: 'rgb(255, 128, 25)',
          500: '#FF6600',
          600: 'rgb(204, 82, 0)',
          700: 'rgb(153, 61, 0)',
          800: 'rgb(102, 41, 0)',
          900: 'rgb(76, 30, 0)',
          950: 'rgb(51, 20, 0)'
        }`;

  const result = await openAIChat(prompt);
  console.log('Modified color scheme:', result);
  return result;
}

/**
 * Main function to generate website content
 * @param keyword Main keyword for the website
 * @param description Description of the website
 * @param outputPath Path to save the output
 */
async function generateWebsite(
  keyword: string,
  description: string,
  outputPath: string = process.cwd(),
  options: {
    generateBlogContent?: boolean,
    primaryColor?: string
  } = {}
): Promise<void> {
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
  }
  
  console.log(`Generating website content for keyword: ${keyword}`);
  console.log(`Description: ${description}`);
  console.log(`Output path: ${outputPath}`);
  
  await generateTDK(keyword, description, outputPath);
  await generateHero(keyword, description, outputPath);
  await generateFAQ(keyword, description, outputPath);
  await generateBlogs(keyword, description, outputPath);
  
  if (options.generateBlogContent) {
    await generateBlogContent(keyword, description, outputPath);
  }
  
  if (options.primaryColor) {
    await modifyPrimaryColor(options.primaryColor);
  }
  
  console.log('Website content generation complete!');
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  // Check if we have the required arguments
  if (args.length < 2) {
    console.error('Usage: website-generator <keyword> <description> [outputPath] [--blogs] [--color=#hexcode]');
    process.exit(1);
  }
  
  const keyword = args[0];
  const description = args[1];
  let outputPath = args[2] || path.join(process.cwd(), 'result');
  
  const options = {
    generateBlogContent: args.includes('--blogs'),
    primaryColor: undefined as string | undefined
  };
  
  // Parse color option
  const colorArg = args.find(arg => arg.startsWith('--color='));
  if (colorArg) {
    options.primaryColor = colorArg.split('=')[1];
  }
  
  generateWebsite(keyword, description, outputPath, options)
    .then(() => console.log('Website generation complete'))
    .catch(err => {
      console.error('Error generating website content:', err);
      process.exit(1);
    });
}

// Export functions for use in other modules
export {
  generateWebsite,
  generateTDK,
  generateHero,
  generateFAQ,
  generateBlogs,
  generateBlogContent,
  modifyPrimaryColor
}; 