#!/usr/bin/env node

import * as fs from 'fs/promises';
import * as path from 'path';
import { translate } from '../common/ai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define paths
const projectRoot = path.join(__dirname, '../../');
const localeConfig = require(path.join(projectRoot, 'framework/locale/localeConfig'));
const directoryPath = path.join(projectRoot, 'translations');

// Define interfaces
interface LocaleMessage {
  message: string;
  translation?: string;
}

interface LocaleMessages {
  [key: string]: LocaleMessage;
}

/**
 * Enhance word density in translations for SEO optimization
 * @param language Target language code
 * @param word Word to enhance density for
 */
async function enhanceWordDensity(language: string, word: string): Promise<void> {
  console.log(`Processing language: ${language}, ${localeConfig[language]}, Target word: ${word}`);

  const filePath = path.join(directoryPath, `${language}/messages.json`);
  const data = await fs.readFile(filePath, 'utf8');
  const json: LocaleMessages = JSON.parse(data);
  const keys = Object.keys(json);

  // Find translations that need enhancement
  const needEnhanceKeys: Record<string, string> = {};
  keys.forEach(function (key) {
    if (json[key]['translation']) {
      needEnhanceKeys[key] = json[key]['translation'] as string;
    }
  });

  if (Object.keys(needEnhanceKeys).length === 0) {
    console.log("No translations need enhancement in the current language file");
    return;
  }

  console.log(`Current language: ${language}, Number of keys to enhance: ${Object.keys(needEnhanceKeys).length}`);

  // Create prompt for translation enhancement
  const prompt = `
    - You are an AI expert in enhancing word density in content, with rich language expression abilities and creative thinking.
    - Please increase the frequency of the word "${word}" in the following content, raising its word density to above 3%, making the content richer and more vivid, but keeping the original meaning unchanged.
    - If the original text does not contain "${word}", please appropriately integrate it into the content, but don't force it unnaturally.
    - Output format is JSON, with keys unchanged, directly output the JSON content, without \`\`\`json\`\`\` tags.
    - Ensure JSON format accuracy, making sure keys and content appear in pairs.
    - Target language: ${localeConfig[language]}
    - Do not provide any explanations, directly output the JSON content
    - Input JSON data:
        ${JSON.stringify(needEnhanceKeys, null, 2)}
  `;

  // Parse JSON safely
  const safeJSONParse = (str: string): Record<string, string> | null => {
    try {
      return JSON.parse(str);
    } catch (e) {
      console.error("[safeJSONParse] JSON parsing failed, attempting to fix");
      // Remove leading ```json
      str = str.replace(/^\s*```json/, "");
      // Remove trailing ```
      str = str.replace(/\s*```$/, "");
      str = str.replace(/[\u0000-\u001F]+/g, "");
      str = str.replace(/([\[{])\s*,/g, "$1");
      str = str.replace(/,\s*([\]}])/g, "$1");
      console.log("[safeJSONParse] Result after fix attempts:", str);
      try {
        return JSON.parse(str);
      } catch (e) {
        console.error("[safeJSONParse] Still unable to parse JSON after fixes", e);
        return null;
      }
    }
  };

  // Get enhanced translations
  let msg = await translate(prompt);
  const enhancedTranslations = safeJSONParse(msg);
  console.log("[enhanceWordDensity] After processing:", enhancedTranslations);

  if (!enhancedTranslations) {
    console.error("Failed to parse enhanced translations");
    return;
  }

  // Update translations with enhanced content
  keys.forEach(function (key) {
    if (enhancedTranslations[key]) {
      json[key]['translation'] = enhancedTranslations[key];
    }
  });
 
  // Write updated translations back to file
  const jsonStr = JSON.stringify(json, null, 2);
  await fs.writeFile(filePath, jsonStr, 'utf8');
  console.log(`Processing completed for ${language} language file`);
}

/**
 * Main function to enhance word density
 * @param language Target language code
 * @param word Word to enhance density for 
 */
async function main(language?: string, word?: string): Promise<void> {
  try {
    if (!language || !word) {
      // Try to get from command line arguments
      const args = process.argv.slice(2);
      if (args.length >= 2) {
        language = args[0];
        word = args[1];
      } else {
        console.error('Please specify the language to process, target word, and word density');
        console.error('Usage: enhance-word-density <language_code> <target_word>');
        return;
      }
    }
    
    await enhanceWordDensity(language, word);
  } catch (err) {
    console.error('Error:', err);
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main().catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
}

// Export functions for use in other modules
export {
  enhanceWordDensity,
  main as enhanceWordDensityMain
}; 