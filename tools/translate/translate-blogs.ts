#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';
import { translate } from '../common/ai';
import matter from 'gray-matter';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define paths
const projectRoot = path.join(__dirname, '../../');
const localeConfig = require(path.join(projectRoot, 'framework/locale/localeConfig'));
const directoryPath = path.join(projectRoot, 'blogs');
const originLanguage = 'en';

// Define interfaces
interface BlogFrontMatter {
  title: string;
  slug: string;
  description: string;
  createdAt: string;
  fileName?: string;
  originalSlug?: string;
  image?: string;
  [key: string]: any;
}

/**
 * Translate a blog file to the target language
 * @param content Content of the blog to translate
 * @param filePath Path to save the translated file
 * @param language Target language
 * @param frontMatter Original frontmatter content
 * @param fileName Original file name
 */
async function translateFile(
  content: string,
  filePath: string,
  language: string,
  frontMatter: BlogFrontMatter,
  fileName: string
): Promise<void> {
  const prompt = `
    - You are an AI expert in data processing and multilingual translation, with the ability to efficiently process markdown documents and flexibly meet various language requirements.
    - The translation should consider professional terminology and formal style, suitable for official documents and website blog writing. Do not provide any explanations.
    - Consider using local idioms rather than simple word-for-word translation, understanding the original meaning and finding appropriate local expressions. This is very important. You can appropriately rewrite some content.
    - Translate to target language: ${language}
    - Maintain markdown format without any modifications
    - Review your translated content for consistency with the original meaning, check for omissions, errors, or areas needing improvement, and pay attention to details during translation.
    - The title, slug, and description fields should be formatted as YAML, so please output the translated content in YAML format to ensure it can be correctly parsed by YAML tools using content wrapped in two "---".
    - The slug field in the input is used to generate the article URL, so keep the content under 100 characters per line and ensure it's suitable for a URL address with no special characters. Hyphens (-) and underscores (_) are allowed to connect multiple words.
    - The description field should remain on a single line without line breaks or multiple line content.
    - Input content:
      ---
      title: ${frontMatter.title}
      slug: ${frontMatter.slug}
      description: ${frontMatter.description}
      ---
      \n\n
      ${content}
  `;

  let msg = await translate(prompt);
  
  try {
    // Extract content from markdown code blocks if present
    const reg = /```markdown\n([\s\S]*)\n```/g;
    const match = reg.exec(msg);
    console.log('match', match);
    
    let extractContent: string;
    let data: any;
    let content2: string;
    
    if (match) {
      extractContent = match[1];
      const parsed = matter(extractContent);
      data = parsed.data;
      content2 = parsed.content;
    } else {
      const parsed = matter(msg);
      data = parsed.data;
      content2 = parsed.content;
    }

    // Add original metadata
    data.originalSlug = frontMatter.slug;
    data.createdAt = frontMatter.createdAt;
    data.fileName = fileName;
    data.image = frontMatter.image;
    
    // Create new markdown content
    const frontMatterString = matter.stringify('', data);
    const newFileContent = `${frontMatterString}\n${content2}`;
    
    // Create file path and save file
    console.log('newFileContent', newFileContent);
    const newFilePath = path.join(filePath, `${frontMatter.slug}.mdx`);
    console.log('newFilePath', newFilePath);
    fs.writeFileSync(newFilePath, newFileContent, 'utf8');
  } catch (e) {
    console.log('Failed to write file', e);
    console.log('Translated content:', msg);
  }
}

/**
 * Main function to translate all blogs
 */
async function translateBlogs(): Promise<void> {
  // Get all files from the original language directory
  const enFiles = fs.readdirSync(path.join(directoryPath, originLanguage));
  
  for (const file of enFiles) {
    /*
      1. Loop through the language list, locate the final file directory based on the language list. Check if a file with the same name as 'file' exists in the specified language directory.
      2. If the file exists, check if the modification date of the file content is consistent with the 'file' date. If inconsistent, delete the file and retranslate.
      3. If the file does not exist, translate the file.
    */
    for (const locale of Object.keys(localeConfig)) {
      const language = localeConfig[locale];
      if (locale === originLanguage) {
        continue;
      }
      
      // Get the modification date of the corresponding original file
      const fileContent = fs.readFileSync(path.join(directoryPath, originLanguage, file), 'utf-8');
      const parsed = matter(fileContent);
      const frontMatter = parsed.data;
      const content = parsed.content;
      
      let isExist = false;
      
      // Check if target language directory exists, create if not
      const localFileDir = path.join(directoryPath, locale);
      if (!fs.existsSync(localFileDir)) {
        fs.mkdirSync(localFileDir);
      }
      
      // Loop through files in the current language directory
      const localFiles = fs.readdirSync(localFileDir);

      for (const localFile of localFiles) {
        const filePath = path.join(directoryPath, locale, localFile);
        const markdownFile = fs.readFileSync(filePath, 'utf-8');
        const parsedLocal = matter(markdownFile);
        const frontMatter2 = parsedLocal.data;
        
        if (frontMatter2.fileName === file) {
          // Get the modification date of the file
          const fileModifiedDate = frontMatter.createdAt;
          const originalFileModifiedDate = frontMatter2.createdAt;
          
          if (new Date(fileModifiedDate).getTime() !== new Date(originalFileModifiedDate).getTime()) {
            // Delete the file and retranslate
            fs.unlinkSync(filePath);
            console.log(`${file} has been updated, needs to be retranslated to: ${language}`);
          } else {
            isExist = true;
            console.log(`${file} has not been updated, no need to translate to: ${language}`);
          }
          break;
        }
      }

      // Check if the file exists
      if (!isExist) {
        console.log(`${file} does not exist in ${locale}, needs to be translated to a new language: ${language}`);
        await translateFile(content, localFileDir, language, frontMatter as BlogFrontMatter, file);
      }
    }
  }
}

// Run the main function when this script is executed directly
if (require.main === module) {
  translateBlogs().then(() => {
    console.log('Translation completed');
  }).catch(err => {
    console.error('Translation failed:', err);
  });
}

// Export functions for use in other modules
export {
  translateBlogs,
  translateFile
}; 