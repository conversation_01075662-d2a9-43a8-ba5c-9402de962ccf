#!/usr/bin/env node

import * as fs from 'fs/promises';
import * as path from 'path';
import { translate } from '../common/ai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define paths
const projectRoot = path.join(__dirname, '../../nextjs/');
const localeConfig = {
  "zh": "Chinese",
  "ja": "Japanese",
  "ko": "Korean",
  "fr": "French",
  "de": "German",
  "es": "Spanish",
  "it": "Italian",
  "pt": "Portuguese",
  "ru": "Russian",
  "ar": "Arabic",
  "tr": "Turkish",
  "vi": "Vietnamese",
  "th": "Thai",
  "id": "Indonesian",
  "ms": "Malay",
  "he": "Hebrew",
  "ur": "Urdu",
  "bn": "Bengali",
  "hi": "Hindi",
  "pa": "Punjabi",
  "gu": "Gujarati",
  "ta": "Tamil",
  "te": "Telugu",
  "kn": "Kannada",
  "ml": "Malayalam",
  "or": "Odia",
  "si": "Sinhala",
  "am": "Amharic",
  "ig": "Igbo",
  "yo": "Yoruba",
  "ha": "Hausa",
  "zu": "Zulu",
  "af": "Afrikaans",
  "sq": "Albanian",
  "hy": "Armenian",
};
const directoryPath = path.join(projectRoot, 'i18n/messages');
const originLanguage = 'en';

// Define types for locale data (can be nested)
type LocaleValue = string | LocaleDataObject;
interface LocaleDataObject {
  [key: string]: LocaleValue;
}

// Function to recursively synchronize and collect keys needing translation
function syncRecursivelyAndCollectNeedsTranslation(
  enObj: LocaleDataObject,
  targetObj: LocaleDataObject | undefined,
  currentPathParts: string[],
  needTranslateMap: Record<string, string>,
  builtTargetObj: LocaleDataObject
): void {
  for (const key in enObj) {
    if (Object.prototype.hasOwnProperty.call(enObj, key)) {
      const enValue = enObj[key];
      const currentTargetValue = targetObj ? targetObj[key] : undefined;
      const newPathParts = [...currentPathParts, key];

      if (typeof enValue === 'string') {
        // Leaf node (string to be translated)
        const flatKey = newPathParts.join('.');
        let translationToKeep = '';
        if (typeof currentTargetValue === 'string' && currentTargetValue.trim() !== '') {
          translationToKeep = currentTargetValue;
        }
        
        builtTargetObj[key] = translationToKeep;

        if (translationToKeep === '') {
          if (Object.keys(needTranslateMap).length < 50) { // Respect existing limit
            needTranslateMap[flatKey] = enValue;
          }
        }
      } else if (typeof enValue === 'object' && enValue !== null && !Array.isArray(enValue)) {
        // Nested object
        builtTargetObj[key] = {};
        syncRecursivelyAndCollectNeedsTranslation(
          enValue as LocaleDataObject,
          typeof currentTargetValue === 'object' && currentTargetValue !== null && !Array.isArray(currentTargetValue)
            ? (currentTargetValue as LocaleDataObject)
            : {},
          newPathParts,
          needTranslateMap,
          builtTargetObj[key] as LocaleDataObject
        );
      } else {
        // Non-string, non-object value (e.g., array, number, boolean). Copy from English as is.
        // Or decide on a different strategy (e.g., skip, warn). For i18n, this is less common for translatable content.
        console.warn(`[syncRecursively] Key "${newPathParts.join('.')}" has a non-string/non-object value in en.json. Copying as is: ${JSON.stringify(enValue)}`);
        builtTargetObj[key] = enValue; // Direct copy
      }
    }
  }

  // Optionally, identify and log keys present in targetObj but not in enObj (they will be removed)
  if (targetObj) {
    for (const key in targetObj) {
      if (Object.prototype.hasOwnProperty.call(targetObj, key) && !Object.prototype.hasOwnProperty.call(enObj, key)) {
        const removedKeyPath = [...currentPathParts, key].join('.');
        console.log(`  - Removing extra key from target: ${removedKeyPath}`);
      }
    }
  }
}

// Function to recursively update the target object with new translations
function updateTranslationsRecursively(
  obj: LocaleDataObject,
  pathParts: string[],
  translation: string
): void {
  const key = pathParts[0];
  if (pathParts.length === 1) {
    if (Object.prototype.hasOwnProperty.call(obj, key) || typeof obj[key] === 'string') { // Allow updating if key exists, even if it was an empty string
         obj[key] = translation;
    } else {
        console.warn(`[updateTranslationsRecursively] Path part "${key}" not found or not a string in target structure for path "${pathParts.join('.')}". Translation might be skipped.`);
    }
  } else {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      updateTranslationsRecursively(obj[key] as LocaleDataObject, pathParts.slice(1), translation);
    } else {
      console.warn(`[updateTranslationsRecursively] Path part "${key}" is not an object in target structure for path "${pathParts.join('.')}". Cannot set nested translation.`);
    }
  }
}


/**
 * Process and translate a single language file
 * @param file The language file name
 */
async function processLanguage(file: string): Promise<void> {
  const language = file.split('.')[0];
  if (language === originLanguage) {
    return;
  }
  
  console.log(`[processLanguage] Processing language: ${language}, ${localeConfig[language as keyof typeof localeConfig]}`);

  // Load origin language (English) messages
  const originFilePath = path.join(directoryPath, `${originLanguage}.json`);
  let enMessages: LocaleDataObject;
  try {
    const enData = await fs.readFile(originFilePath, 'utf8');
    enMessages = JSON.parse(enData) as LocaleDataObject;
  } catch (error) {
    console.error(`[processLanguage] CRITICAL: Error reading origin language file ${originFilePath}. Cannot proceed with language ${language}.`, error);
    return;
  }

  // Read the target language file
  const targetFilePath = path.join(directoryPath, `${language}.json`);
  let targetMessages: LocaleDataObject = {}; // Default to empty if file not found or error
  try {
    const data = await fs.readFile(targetFilePath, 'utf8');
    targetMessages = JSON.parse(data) as LocaleDataObject;
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      console.log(`[processLanguage] Target file ${targetFilePath} not found. A new file will be created for language ${language}.`);
      // targetMessages remains {}
    } else {
      console.error(`[processLanguage] Error reading target language file ${targetFilePath}. Skipping language ${language}.`, error);
      return;
    }
  }
  const needTranslateMap: Record<string, string> = {};
  const finalUpdatedTargetMessages: LocaleDataObject = {};

  // Synchronize structure and collect keys needing translation
  syncRecursivelyAndCollectNeedsTranslation(
    enMessages,
    targetMessages,
    [],
    needTranslateMap,
    finalUpdatedTargetMessages
  );

  // Check if any translations are needed or if the structure changed
  const originalTargetJsonString = JSON.stringify(targetMessages, Object.keys(targetMessages).sort(), 2);
  const newTargetJsonStringBasedOnEn = JSON.stringify(finalUpdatedTargetMessages, Object.keys(finalUpdatedTargetMessages).sort(), 2);

  if (Object.keys(needTranslateMap).length === 0) {
    if (originalTargetJsonString !== newTargetJsonStringBasedOnEn) {
      console.log(`[processLanguage] Updating ${targetFilePath} due to key restructuring or removal of extra keys, even though no new translations are needed.`);
      await fs.writeFile(targetFilePath, JSON.stringify(finalUpdatedTargetMessages, null, 2), 'utf8');
    } else {
      console.log(`[processLanguage] No new translations needed and no structural changes for language ${language} based on ${originLanguage}.json.`);
    }
    return;
  }

  console.log(`[processLanguage] Language: ${language}, Keys to translate: ${Object.keys(needTranslateMap).length}`);
  console.log('[processLanguage] Keys to translate (flat map):', needTranslateMap);

  // Create translation prompt
  const prompt = `
    - You are an AI expert in data processing and multilingual translation, with the ability to efficiently process JSON data and flexibly meet various language requirements.
    - The translation should consider professional terminology and formal style, suitable for official documents and communication.
    - Requirements:
    - 1. Target translation language: ${localeConfig[language as keyof typeof localeConfig]}
    - 2. Translation output should be in JSON format, with keys unchanged, directly output JSON content that can be parsed with json.load;
    - 3. Do not provide any explanations;
    - 4. Consider using local idioms rather than simple word-for-word translation, understanding the original meaning and finding appropriate local expressions
    - Input JSON data:
        ${JSON.stringify(needTranslateMap, null, 2)}
  `;

  let msg = await translate(prompt);
  
  // Parse the JSON response
  const safeJSONParse = (str: string): Record<string, string> | null => {
    try {
      return JSON.parse(str);
    } catch (e) {
      console.error("[processLanguage] JSON parsing failed, attempting to fix");
      // Remove leading ```json
      str = str.replace(/^\s*```json/, "");
      // Remove trailing newline
      str = str.replace(/\s*\n$/, "");
      // Remove trailing ```
      str = str.replace(/\s*```$/, "");
      str = str.replace(/[\u0000-\u001F]+/g, "");
      str = str.replace(/([\[{])\s*,/g, "$1");
      str = str.replace(/,\s*([\]}])/g, "$1");
      console.log("[processLanguage] Result after attempted fix:", str);
      try {
        return JSON.parse(str);
      } catch (e) {
        console.error("[processLanguage] Still unable to parse JSON after fix", e);
        return null;
      }
    }
  };

  const parsedResponse = safeJSONParse(msg);

  if (parsedResponse === null) {
    console.error("[processLanguage] Unable to parse returned JSON data");
  } else {
    console.log("[processLanguage] Parsed JSON data:", parsedResponse);
    // Update translations in the nested finalUpdatedTargetMessages object
    for (const flatKey in parsedResponse) {
      if (Object.prototype.hasOwnProperty.call(parsedResponse, flatKey)) {
        const translatedText = parsedResponse[flatKey];
        const pathParts = flatKey.split('.');
        try {
            updateTranslationsRecursively(finalUpdatedTargetMessages, pathParts, translatedText);
        } catch (e: any) {
            console.error(`[processLanguage] Error updating key "${flatKey}" with translation "${translatedText}": ${e.message}`);
        }
      }
    }
    // Write updated translations back to file
    const jsonStr = JSON.stringify(finalUpdatedTargetMessages, null, 2);
    await fs.writeFile(targetFilePath, jsonStr, 'utf8');
  }
}

/**
 * Process multiple languages in parallel with concurrency limit
 * @param languages Array of language files to process
 * @param concurrency Maximum number of concurrent processes
 */
async function processLanguagesInQueue(languages: string[], concurrency = 3): Promise<string[]> {
  const queue = [...languages];
  const inProgress = new Set<string>();
  const results: string[] = [];

  async function processNext(): Promise<void> {
    if (queue.length === 0) return;
    const language = queue.shift()!;
    inProgress.add(language);

    try {
      await processLanguage(language);
      results.push(`${language} processing completed`);
    } catch (error: any) {
      results.push(`${language} processing failed: ${error.message}`);
    } finally {
      inProgress.delete(language);
      if (queue.length > 0) {
        await processNext();
      }
    }
  }

  const workers = Array(Math.min(concurrency, languages.length))
    .fill(null)
    .map(() => processNext());

  await Promise.all(workers);
  return results;
}

/**
 * Main function to run the translation process
 */
async function main(): Promise<void> {
  try {
    const files = await fs.readdir(directoryPath);
    const languagesToProcess = files.filter(file => file !== originLanguage);
    const results = await processLanguagesInQueue(languagesToProcess);
    console.log("[processLanguage] Processing results:", results.join('\n'));
  } catch (err) {
    console.error('Error:', err);
  }
}

// Run the main function when this script is executed directly
if (require.main === module) {
  main();
}

// Export functions for use in other modules
export {
  processLanguage,
  processLanguagesInQueue
}; 