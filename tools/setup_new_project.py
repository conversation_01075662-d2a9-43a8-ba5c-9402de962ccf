#!/usr/bin/env python3

import os
import subprocess
import shutil
import re
import requests
import sys
from dotenv import load_dotenv

# --- Configuration ---
NEXTJS_DIR = "../nextjs"  # Relative path to the nextjs directory from tools/
ENV_EXAMPLE_PATH = os.path.join(NEXTJS_DIR, ".env.example")
ENV_PATH = os.path.join(NEXTJS_DIR, ".env")
INSTALL_SQL_PATH = os.path.join(NEXTJS_DIR, "data", "install.sql")
UPSTREAM_REPO = "**************:hekmon8/my-ship-any.git"
UPSTREAM_REMOTE_NAME = "upstream"

# Directories and extensions to skip during recursive replace
SKIP_DIRS = ['.git', 'node_modules', '.next', 'public/uploads', 'cache', 'dist', 'build', '.vercel']
# Define text file extensions to process
TEXT_EXTENSIONS = ['.js', '.ts', '.tsx', '.jsx', '.json', '.md', '.mdx', '.env',
                   '.example', '.local', '.sql', '.css', '.scss', '.html', '.toml',
                   '.yaml', '.yml', '.sh', '.py', '.txt', '.mjs', '.cjs', 'LICENSE', '.production']
# Files to skip during recursive replace
SKIP_FILES = ['.DS_Store', '.env.example', '.env.local.example', 'wrangler.toml.example']

# --- Helper Functions ---

def run_command(command, cwd=None, check=True):
    """Runs a shell command."""
    print(f"Running command: {' '.join(command)}")
    try:
        result = subprocess.run(command, cwd=cwd, check=check, capture_output=True, text=True, encoding='utf-8')
        print(result.stdout)
        if result.stderr:
            print(f"Stderr: {result.stderr}", file=sys.stderr)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {' '.join(command)}", file=sys.stderr)
        print(f"Stderr: {e.stderr}", file=sys.stderr)
        if check:
            raise
        return e
    except FileNotFoundError:
        print(f"Error: Command not found: {command[0]}", file=sys.stderr)
        if check:
            raise
        return None
    except Exception as e:
        print(f"An unexpected error occurred running command: {e}", file=sys.stderr)
        if check:
            raise
        return None


def replace_in_file(filepath, pattern, replacement, is_regex=True, ignore_case=False):
    """Replaces occurrences of pattern with replacement in a file."""
    try:
        # Try reading with UTF-8, fallback or ignore errors
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                content = file.read()
        except UnicodeDecodeError:
            print(f"Warning: Could not decode {filepath} as UTF-8. Trying latin-1.", file=sys.stderr)
            try:
                with open(filepath, 'r', encoding='latin-1') as file:
                    content = file.read()
            except Exception as e:
                print(f"Error: Could not read file {filepath} even with latin-1: {e}", file=sys.stderr)
                return # Skip file if unreadable

        flags = re.MULTILINE
        if ignore_case:
            flags |= re.IGNORECASE

        if is_regex:
            # Use the provided pattern directly if it's already regex
            compiled_pattern = re.compile(pattern, flags)
            new_content = compiled_pattern.sub(replacement, content)
        else:
            # Escape the pattern if it's literal text
            compiled_pattern = re.compile(re.escape(pattern), flags)
            new_content = compiled_pattern.sub(replacement, content)


        if new_content != content:
            with open(filepath, 'w', encoding='utf-8') as file: # Write back as UTF-8
                file.write(new_content)
            print(f"Updated: {filepath}")
        # else:
            # print(f"No changes needed in: {filepath} for pattern: {pattern}") # Too verbose
    except FileNotFoundError:
        print(f"Error: File not found during replace: {filepath}", file=sys.stderr)
    except Exception as e:
        print(f"Error updating file {filepath}: {e}", file=sys.stderr)


def recursive_replace(directory, old_text, new_text):
    """Recursively replaces old_text with new_text in text files within a directory."""
    print(f"\n--- Recursively replacing '{old_text}' with '{new_text}' in {directory} ---")
    count = 0
    # Compile the regex pattern for case-insensitive replacement
    # Escape old_text in case it contains regex special characters
    pattern = re.compile(re.escape(old_text), re.IGNORECASE)

    for root, dirs, files in os.walk(directory, topdown=True):
        # Modify dirs in-place to skip specified directories
        dirs[:] = [d for d in dirs if d not in SKIP_DIRS]

        for filename in files:
            if filename in SKIP_FILES:
                continue
            # Check if the file has a text-based extension
            if any(filename.lower().endswith(ext) for ext in TEXT_EXTENSIONS):
                filepath = os.path.join(root, filename)
                # Use replace_in_file with case-insensitivity and escaped pattern
                replace_in_file(filepath, pattern.pattern, new_text, is_regex=True, ignore_case=True)
                count += 1

    print(f"Recursive replacement finished. Checked {count} text files.")


# --- Core Functions ---

def setup_git():
    """Sets up git upstream and merges."""
    print("\n--- Setting up Git ---")
    result = run_command(["git", "remote"], check=False)
    if result and UPSTREAM_REMOTE_NAME not in result.stdout.splitlines():
        run_command(["git", "remote", "add", UPSTREAM_REMOTE_NAME, UPSTREAM_REPO])
    else:
        print(f"Git remote '{UPSTREAM_REMOTE_NAME}' already exists or failed to check.")

    run_command(["git", "fetch", UPSTREAM_REMOTE_NAME])
    run_command(["git", "merge", f"{UPSTREAM_REMOTE_NAME}/master", "--allow-unrelated-histories", "-m", "Merge upstream/master"], check=False)
    print("Git setup complete.")


def copy_env_file():
    """Copies .env.example to .env."""
    print("\n--- Setting up Environment File ---")
    if not os.path.exists(ENV_EXAMPLE_PATH):
         print(f"Error: {ENV_EXAMPLE_PATH} not found.", file=sys.stderr)
         return False
    try:
        shutil.copy2(ENV_EXAMPLE_PATH, ENV_PATH)
        print(f"Copied {ENV_EXAMPLE_PATH} to {ENV_PATH}")
        return True
    except Exception as e:
        print(f"Error copying .env file: {e}", file=sys.stderr)
        return False


def get_user_input():
    """Gets necessary user inputs."""
    print("\n--- Gathering Information ---")
    website_domain = input("Enter the website domain (e.g., yourdomain.com, without http/https): ").strip()
    full_website_url = "https://" + website_domain # Assume https
    schema_name = input("Enter the Supabase schema name: ").strip()

    if not website_domain or not full_website_url or not schema_name:
        print("Error: Website domain, full URL, and schema name cannot be empty.", file=sys.stderr)
        sys.exit(1)

    return website_domain, full_website_url, schema_name


def replace_placeholders(website_domain, full_website_url, schema_name):
    """Replaces placeholders including recursive replacement."""
    print("\n--- Replacing Placeholders ---")

    # 1. Specific replacements in .env
    print("Performing specific replacements in .env...")
    replace_in_file(ENV_PATH, r'^NEXT_PUBLIC_WEB_URL=.*', f'NEXT_PUBLIC_WEB_URL="{full_website_url}"', is_regex=True)
    replace_in_file(ENV_PATH, r'^NEXT_PUBLIC_PAY_SUCCESS_URL=.*', f'NEXT_PUBLIC_PAY_SUCCESS_URL="{full_website_url}/my-orders"', is_regex=True)
    replace_in_file(ENV_PATH, r'^NEXT_PUBLIC_PAY_FAIL_URL=.*', f'NEXT_PUBLIC_PAY_FAIL_URL="{full_website_url}/#pricing"', is_regex=True)
    replace_in_file(ENV_PATH, r'^NEXT_PUBLIC_PAY_CANCEL_URL=.*', f'NEXT_PUBLIC_PAY_CANCEL_URL="{full_website_url}/#pricing"', is_regex=True)
    replace_in_file(ENV_PATH, r'^SUPABASE_SCHEMA=.*', f'SUPABASE_SCHEMA="{schema_name}"', is_regex=True)

    # 2. Recursive replacement for the domain name (case-insensitive)
    recursive_replace(NEXTJS_DIR, "mistralocr.com", website_domain)
    recursive_replace(NEXTJS_DIR, "mistralocr", website_domain.split(".")[0])
    recursive_replace(NEXTJS_DIR, "MistralOCR", website_domain.split(".")[0].upper())
    recursive_replace(NEXTJS_DIR, "Mistralocr", website_domain.split(".")[0].title())

    # 3. Specific replacement for schema in install.sql
    print("Replacing schema prefix in install.sql...")
    replace_in_file(INSTALL_SQL_PATH, r'schema\.', f'{schema_name}.', is_regex=True)

    print("Placeholder replacement process complete.")


def _get_umami_session_token(umami_api_url, username, password):
    """Logs into Umami API to get a session token."""
    login_endpoint = f"{umami_api_url.rstrip('/')}/api/auth/login"
    headers = {"Content-Type": "application/json"}
    payload = {"username": username, "password": password}
    print(f"Attempting Umami login to {login_endpoint}...")
    try:
        response = requests.post(login_endpoint, headers=headers, json=payload, timeout=10)
        response.raise_for_status()
        data = response.json()
        token = data.get("token")
        if not token:
            print("Error: 'token' not found in Umami login response.", file=sys.stderr)
            return None
        print("Umami login successful, token obtained.")
        return token
    except requests.exceptions.RequestException as e:
        print(f"Error during Umami login request to {login_endpoint}: {e}", file=sys.stderr)
        if hasattr(e, 'response') and e.response is not None:
            try:
                print(f"Umami Login Response Status: {e.response.status_code}", file=sys.stderr)
                print(f"Umami Login Response Body: {e.response.text}", file=sys.stderr)
            except Exception:
                 print("Could not parse Umami login error response.", file=sys.stderr)
        return None
    except Exception as e:
        print(f"An unexpected error occurred during Umami login: {e}", file=sys.stderr)
        return None


def get_or_create_umami_website(umami_url, umami_username, umami_password, website_name, website_domain):
    """Gets an existing Umami website by domain or creates a new one."""
    print("\n--- Integrating with Umami ---")
    if not umami_url or not umami_username or not umami_password:
        print("Warning: UMAMI_URL, UMAMI_USERNAME, or UMAMI_PASSWORD not set. Skipping Umami integration.", file=sys.stderr)
        return None, None

    # Get session token first
    session_token = _get_umami_session_token(umami_url, umami_username, umami_password)
    if not session_token:
        print("Failed to obtain Umami session token. Cannot proceed with Umami integration.", file=sys.stderr)
        return None, None

    api_endpoint = f"{umami_url.rstrip('/')}/api/websites"
    headers = {"Authorization": f"Bearer {session_token}"}
    script_url = f"{umami_url.rstrip('/')}/script.js" # Standard script name

    # 1. Try to find existing website
    print(f"Checking for existing Umami website with domain: {website_domain}...")
    try:
        response = requests.get(api_endpoint, headers=headers, timeout=10)
        response.raise_for_status()
        websites = response.json()

        # Check if the response is a list (expected format)
        if isinstance(websites['data'], list):
             for site in websites['data']:
                 # Umami API might return domain or name, check both if necessary
                 if site.get('domain') == website_domain:
                     website_id = site.get('id')
                     print(f"Found existing Umami website. ID: {website_id}")
                     return website_id, script_url
        else:
             # Handle potential pagination or different response structure if needed
             print(f"Warning: Unexpected response format when listing Umami websites: {websites}", file=sys.stderr)


    except requests.exceptions.RequestException as e:
        print(f"Error listing Umami websites at {api_endpoint}: {e}", file=sys.stderr)
        # Decide if we should proceed to create or fail here
        print("Proceeding to attempt creation despite listing error.")
    except Exception as e:
        print(f"An unexpected error occurred listing Umami websites: {e}", file=sys.stderr)
        print("Proceeding to attempt creation despite listing error.")


    # 2. If not found, create a new one
    print(f"No existing website found for domain '{website_domain}'. Attempting to create...")
    create_headers = {
        "Authorization": f"Bearer {session_token}",
        "Content-Type": "application/json",
    }
    create_payload = {
        "name": website_name,
        "domain": website_domain,
    }
    try:
        response = requests.post(api_endpoint, headers=create_headers, json=create_payload, timeout=15)
        response.raise_for_status()

        data = response.json()
        website_id = data.get("id")
        if not website_id:
             print(f"Error: Could not find 'id' in Umami API response after creating website: {data}", file=sys.stderr)
             return None, None

        print(f"Successfully created Umami website. Website ID: {website_id}")
        return website_id, script_url

    except requests.exceptions.RequestException as e:
        print(f"Error creating Umami website at {api_endpoint}: {e}", file=sys.stderr)
        if hasattr(e, 'response') and e.response is not None:
            try:
                print(f"Umami Create Website Response Status: {e.response.status_code}", file=sys.stderr)
                print(f"Umami Create Website Response Body: {e.response.text}", file=sys.stderr)
            except Exception:
                 print("Could not parse Umami create website error response.", file=sys.stderr)
        return None, None
    except Exception as e:
        print(f"An unexpected error occurred during Umami website creation: {e}", file=sys.stderr)
        return None, None


def update_umami_env(website_id, script_url):
    """Updates nextjs/.env with Umami details."""
    if not website_id or not script_url:
        print("Skipping Umami configuration in .env as details are missing.")
        return

    print("Updating nextjs/.env with Umami details...")
    replace_in_file(ENV_PATH, r'^UMAMI_ENABLED=.*', 'UMAMI_ENABLED="true"', is_regex=True)
    replace_in_file(ENV_PATH, r'^NEXT_PUBLIC_UMAMI_WEBSITE_ID=.*', f'NEXT_PUBLIC_UMAMI_WEBSITE_ID="{website_id}"', is_regex=True)
    replace_in_file(ENV_PATH, r'^NEXT_PUBLIC_UMAMI_SCRIPT_URL=.*', f'NEXT_PUBLIC_UMAMI_SCRIPT_URL="{script_url}"', is_regex=True)
    print("Umami configuration updated in nextjs/.env.")


# --- Main Execution ---

if __name__ == "__main__":
    print("Starting New Project Setup Script...")

    # Load environment variables from .env file in the tools directory
    load_dotenv()
    umami_url = os.getenv("UMAMI_URL")
    umami_username = os.getenv("UMAMI_USERNAME")
    umami_password = os.getenv("UMAMI_PASSWORD")

    # Change to the script's directory to ensure relative paths work
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    # 1. Setup Git
    # setup_git() # Commented out as per previous state

    # 2. Copy .env file
    if not copy_env_file():
        sys.exit(1)

    # 3. Get User Input
    website_domain, full_website_url, schema_name = get_user_input()
    website_name = website_domain # Use domain as default name for Umami

    # 4. Replace Placeholders (includes recursive replace)
    replace_placeholders(website_domain, full_website_url, schema_name)

    # 5. Umami Integration (Get or Create)
    umami_website_id, umami_script_url = get_or_create_umami_website(
        umami_url, umami_username, umami_password, website_name, website_domain
    )

    # 6. Update .env with Umami details
    update_umami_env(umami_website_id, umami_script_url)

    print("\nSetup script finished successfully!")
    print(f"Remember to run 'pnpm install' or 'npm install' in the '{NEXTJS_DIR}' directory if needed.")
    print("Please review the changes made across the 'nextjs' directory.")
    if not umami_website_id:
        print("Manual Umami configuration might be required in nextjs/.env.")

    print("\nIMPORTANT SEO STEP:")
    print("===================")
    print("After deploying your site, remember to add your sitemaps to Google Search Console.")
    print("The sitemap URLs will typically be (replace '[locale]' with actual locales like 'en', 'zh', etc.):")
    print(f"  {full_website_url}/api/sitemap/[locale]")
    print("For example:")
    print(f"  {full_website_url}/api/sitemap/en")
    print(f"  {full_website_url}/api/sitemap/zh")
    print("The sitemap generation logic is located at: nextjs/app/api/sitemap/[locale]/route.ts")
    print("Ensure this path is correctly configured and accessible on your live site.")

    print("\nUmami Configuration Note:")
    print("=========================")
    print("This script attempted to find or create the Umami website using credentials from 'tools/.env'.")
    print("Ensure UMAMI_URL, UMAMI_USERNAME, and UMAMI_PASSWORD are set correctly in 'tools/.env' for this feature.")