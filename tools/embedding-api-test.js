// API test script for embedding-search API
// Run with: node embedding-api-test.js

// Set default timeout (10 seconds)
const DEFAULT_TIMEOUT = 100000;

async function testEmbeddingSearch() {
  // Try different possible ports
  const ports = [3000];
  let lastError = null;
  
  console.log('=== MCP Embedding Search API Test ===');
  console.log(`Testing on ports: ${ports.join(', ')}`);
  console.log('Timeout set to:', DEFAULT_TIMEOUT, 'ms');
  console.log('======================================\n');

  for (const port of ports) {
    try {
      console.log(`\n[Port ${port}] Trying connection...`);
      
      // Use the properly formatted API URL with the current port
      const baseUrl = `http://localhost:${port}`;
      const url = `${baseUrl}/api/mcps/embedding-search`;
      
      console.log(`[Port ${port}] Sending request to: ${url}`);
      
      // Set timeout for fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), DEFAULT_TIMEOUT);
      
      // Prepare request body
      const requestBody = {
        text: 'Google',
        language: 'en',
        threshold: 0.75,
        limit: 10
      };
      
      console.log(`[Port ${port}] Request body:`, JSON.stringify(requestBody, null, 2));
      
      // Send request to the API
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      console.log(`[Port ${port}] Response status:`, response.status);
      
      // Check HTTP status
      if (response.status >= 400) {
        console.error(`[Port ${port}] Error: HTTP ${response.status}`);
        const errorText = await response.text();
        console.error(`[Port ${port}] Error response:`, errorText.substring(0, 500) + (errorText.length > 500 ? '...' : ''));
        lastError = { error: `HTTP ${response.status}`, raw: errorText.substring(0, 500) };
        continue;
      }
      
      // Log headers
      console.log(`[Port ${port}] Response headers:`, Object.fromEntries(response.headers.entries()));

      // Check if response is JSON
      const contentType = response.headers.get('content-type') || '';
      if (!contentType.includes('application/json')) {
        console.error(`[Port ${port}] Error: API did not return JSON. Content-Type:`, contentType);
        const textResponse = await response.text();
        console.error(`[Port ${port}] Raw response:`, textResponse.substring(0, 500) + (textResponse.length > 500 ? '...' : ''));
        lastError = { error: 'API did not return JSON', contentType, raw: textResponse.substring(0, 500) };
        continue;
      }
      
      // Parse JSON response
      const result = await response.json();
      console.log(`[Port ${port}] Response body:`, JSON.stringify(result, null, 2));
      
      // Check for API-level errors
      if (result.error) {
        console.error(`[Port ${port}] API returned error:`, result.error);
        lastError = { error: result.error, details: result.details || result.message };
        continue;
      }
      
      console.log(`\n[Port ${port}] SUCCESS: API request completed successfully`);
      return { status: response.status, body: result };
    } catch (error) {
      // Handle AbortError differently
      if (error.name === 'AbortError') {
        console.error(`[Port ${port}] Error: Request timed out after ${DEFAULT_TIMEOUT}ms`);
        lastError = { error: 'Request timeout', message: `Connection timed out after ${DEFAULT_TIMEOUT}ms` };
      } else {
        console.error(`[Port ${port}] Error:`, error.message || error.toString());
        lastError = { error: error.message || error.toString() };
      }
      // Continue trying other ports
    }
  }

  // If we've tried all ports and none worked, return the last error
  console.error('\nFAILED: Could not connect to any port');
  console.error('Last error:', JSON.stringify(lastError, null, 2));
  return lastError;
}

// Run the test
testEmbeddingSearch().then(result => {
  if (result.error || (result.status && result.status >= 400)) {
    console.log('\nTest finished with errors');
    process.exit(1);
  } else {
    console.log('\nTest completed successfully');
    process.exit(0);
  }
}).catch(error => {
  console.error('\nUnhandled error during test:', error);
  process.exit(1);
}); 