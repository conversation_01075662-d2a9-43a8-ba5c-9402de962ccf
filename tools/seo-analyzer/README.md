# 网站 SEO 分析器 (Website SEO Analyzer)

一个专业的网站 SEO 分析工具，支持全面的技术标准合规性检查和现代 AI 爬虫控制。

## 🚀 核心功能

### 基础 SEO 分析
- **页面结构检查**: 标题标签、元描述、H1-H6 标题层级
- **内容质量评估**: 字数统计、关键词密度、可读性分析
- **技术性能监控**: 页面加载时间、响应大小、状态码检查
- **链接结构分析**: 内链外链检查、断链检测

### 高级合规性验证 ⭐
- **📋 JSON-LD 结构化数据**: Schema.org 类型验证、属性完整性检查
- **🗺️ sitemap.xml 标准合规**: XML格式、URL数量限制(50K)、日期格式验证
- **🤖 robots.txt 语法检查**: 指令验证、拼写检查、最佳实践建议
- **🧠 llms.txt AI 爬虫控制**: 支持新兴的 AI/LLM 爬虫管理标准

### 多格式报告输出
- **JSON 格式**: 适合程序化处理和 API 集成
- **CSV 格式**: 便于数据分析和 Excel 导入
- **HTML 格式**: 专业的可视化报告，包含图表和样式

## 📦 安装和设置

### 前置要求
- Node.js 18+ 
- TypeScript (如果使用 CLI)
- 网络连接 (用于抓取和验证外部资源)

### 快速开始

1. **安装依赖**:
   ```bash
   cd tools/seo-analyzer
   npm install
   ```

2. **基础使用** (从 `tools/` 目录):
   ```bash
   # 完整分析 - 包含所有检查项目
   npm run seo:analyze https://example.com

   # 仅检查合规性 - 专注于技术标准
   npm run seo:analyze-compliance https://example.com
   
   # MyBacklinks 示例 - 预配置的示例分析
   npm run seo:analyze-mybacklinks
   ```

## 🛠 详细使用方法

### 命令行工具 (CLI)

```bash
# 使用 TypeScript CLI (完整功能)
npx ts-node src/cli/cli.ts analyze <URL> [options]

# 或者使用编译后的版本
npm run build
node dist/cli/cli.js analyze <URL> [options]
```

#### CLI 参数选项

| 参数 | 描述 | 默认值 | 示例 |
|------|------|--------|------|
| `--max-urls <number>` | 最大分析URL数量 | 10 | `--max-urls 50` |
| `--output <format>` | 输出格式 | `json` | `--output html` |
| `--save-to <path>` | 保存报告到文件 | - | `--save-to report.html` |
| `--include-external` | 包含外部链接检查 | false | `--include-external` |
| `--performance-budget <ms>` | 性能预算(毫秒) | 3000 | `--performance-budget 2000` |

#### 使用示例

```bash
# 基础分析
npx ts-node src/cli/cli.ts analyze https://mybacklinks.app

# 生成详细的HTML报告
npx ts-node src/cli/cli.ts analyze https://example.com \
  --output html \
  --save-to seo-report.html \
  --max-urls 25

# 高性能分析，包含外部链接
npx ts-node src/cli/cli.ts analyze https://example.com \
  --include-external \
  --performance-budget 2000 \
  --max-urls 100
```

### 简化脚本 (推荐)

为了方便使用，我们提供了几个开箱即用的测试脚本:

```bash
# 🎯 完整分析 - 包含所有检查项目
node test-full-analysis.js https://your-website.com

# 🔍 合规性检查 - 专注于技术标准
node test-compliance.js https://your-website.com

# 📊 MyBacklinks 示例 - 预配置的示例分析
node test-mybacklinks.js
```

### 程序化 API 使用

```typescript
import { MainSeoAnalyzer } from './src/main-analyzer';
import { AnalyzerConfig } from './src/types';

const config: AnalyzerConfig = {
  baseUrl: 'https://example.com',
  maxUrls: 10,
  timeout: 10000,
  outputFormats: ['json', 'html'],
  // ... 其他配置
};

const analyzer = new MainSeoAnalyzer(config);
const report = await analyzer.analyze();

console.log(`发现 ${report.issues.length} 个问题`);
```

## 📊 输出格式详解

### JSON 格式
```json
{
  "summary": {
    "totalIssues": 8,
    "errors": 2,
    "warnings": 3,
    "info": 3,
    "analysisTime": 10245
  },
  "issues": [
    {
      "type": "error",
      "category": "structure",
      "url": "https://example.com",
      "message": "sitemap 使用了无效或缺失的命名空间",
      "recommendation": "使用正确的 sitemap 命名空间"
    }
  ],
  "urls": [...],
  "timestamp": "2024-01-15T10:30:45.123Z"
}
```

### HTML 报告功能
- 🎨 **专业样式**: 清晰的问题分类和优先级标识
- 📈 **可视化图表**: 问题分布统计和趋势分析
- 🔗 **交互式链接**: 点击直达具体问题URL
- 📱 **响应式设计**: 支持移动端和桌面端查看
- 🌍 **中文本地化**: 完全的中文界面和术语

### CSV 格式 (便于数据分析)
```csv
Type,Category,URL,Message,Recommendation,Severity
error,structure,https://example.com,sitemap 使用了无效命名空间,使用正确的命名空间,high
warning,structure,https://example.com,标题标签过长,控制在60字符以内,medium
```

## 🔍 检查项目详解

### 基础 SEO 检查
- **标题优化**: 长度检查 (≤60字符)、唯一性验证
- **元描述**: 存在性检查 (150-160字符最佳)
- **标题结构**: H1唯一性、层级合理性 (H1→H2→H3...)
- **图像优化**: Alt属性、文件大小、格式检查
- **链接质量**: 内链结构、外链有效性

### JSON-LD 结构化数据合规性 ⭐
```javascript
// 检查项目包括:
{
  "@context": "https://schema.org",    // ✅ 必需属性
  "@type": "Organization",            // ✅ 类型验证
  "name": "公司名称",                 // ✅ Schema.org 字段验证
  "url": "https://example.com"        // ✅ URL 格式验证
}
```

### sitemap.xml 标准合规性 ⭐
- **XML 格式验证**: 声明、编码、语法检查
- **命名空间检查**: 标准 sitemaps.org schema 验证
- **URL 限制**: 检查是否超过 50,000 个 URL 限制
- **日期格式**: ISO 8601 格式验证 (`YYYY-MM-DD` 或完整时间戳)
- **优先级验证**: priority 值范围 (0.0-1.0) 检查

### robots.txt 语法检查 ⭐
```
User-agent: *              # ✅ 指令语法检查
Disallow: /admin           # ✅ 路径格式验证  
Allow: /public             # ✅ 拼写错误检测
Sitemap: https://...       # ✅ URL 格式验证
Crawl-delay: 1             # ✅ 数值范围检查
```

### llms.txt AI 爬虫控制 ⭐
这是一个新兴的标准，用于控制 AI/LLM 爬虫的行为:
```
# 示例 llms.txt 内容检查
User-agent: *
Disallow: /private-data    # AI 训练数据排除
Allow: /public-content     # 允许 AI 访问的内容  
AI-crawl-delay: 2          # AI 特定的延迟设置
```

## ⚡ 性能优化和最佳实践

### 分析性能
- **并发处理**: 支持同时分析多个URL (默认并发数: 3)
- **智能缓存**: 避免重复请求相同资源
- **超时控制**: 防止长时间等待无响应的URL
- **内存管理**: 大规模分析时的内存优化

### 推荐分析策略

1. **小型网站 (< 50 页面)**:
   ```bash
   npm run seo:analyze https://small-site.com
   ```

2. **中型网站 (50-500 页面)**:
   ```bash
   npx ts-node src/cli/cli.ts analyze https://medium-site.com \
     --max-urls 100 \
     --output html
   ```

3. **大型网站 (> 500 页面)**:
   ```bash
   # 分阶段分析，先检查主要页面
   npx ts-node src/cli/cli.ts analyze https://large-site.com \
     --max-urls 50 \
     --include-external \
     --save-to phase1-report.html
   ```

## 🎯 实际案例分析

### MyBacklinks.app 分析结果示例
```
🎯 核心发现汇总:
📋 结构化数据 (JSON-LD): ✅ 已实现
🤖 robots.txt 合规性: ✅ 正常  
🗺️ sitemap.xml 格式: ⚠️  需要修复
🧠 AI 爬虫控制 (llms.txt): ✅ 已部署
⚡ 页面性能: ✅ 良好

💡 优先改进建议:
  1. 立即修复 sitemap.xml 命名空间问题
  2. 优化页面标题长度 (当前71字符 → 建议60字符)
  3. 添加 H1 标签提升页面结构
```

### 常见问题和解决方案

| 问题类型 | 常见原因 | 解决建议 |
|----------|----------|----------|
| 标题过长 | SEO 意识不足 | 控制在 50-60 字符，包含核心关键词 |
| 缺少 JSON-LD | 技术实现遗漏 | 添加 Schema.org 结构化数据 |
| sitemap 错误 | 生成工具配置问题 | 检查 XML 格式和命名空间 |
| robots.txt 语法错误 | 手动编辑错误 | 使用标准指令，检查拼写 |
| 性能问题 | 图片未优化 | 压缩图片，启用 CDN，优化代码 |

## 🔧 高级配置

### 配置文件 (可选)
创建 `seo-analyzer.config.json`:
```json
{
  "maxUrls": 50,
  "timeout": 15000,
  "outputFormats": ["json", "html"],
  "performanceBudget": 3000,
  "includeExternal": true,
  "customRules": {
    "titleMaxLength": 60,
    "descriptionMaxLength": 160,
    "enableJsRender": false
  }
}
```

### 自定义检查规则
```typescript
// 扩展检查规则
import { SeoAnalyzer } from './src/services/seo-analyzer';

const customAnalyzer = new SeoAnalyzer({
  customRules: [
    {
      name: 'custom-title-check',
      check: (content: string) => {
        // 自定义标题检查逻辑
        return issues;
      }
    }
  ]
});
```

## 🤝 贡献指南

1. **问题报告**: 发现 bug 请提交 detailed issue
2. **功能建议**: 欢迎提出新的检查规则或优化建议
3. **代码贡献**: Fork → 开发 → 测试 → Pull Request

### 开发环境设置
```bash
# 克隆项目
git clone [repository-url]
cd tools/seo-analyzer

# 安装依赖
npm install

# 运行测试
npm test

# 构建项目  
npm run build
```

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

## 🆘 技术支持

如果遇到问题或需要帮助：

1. **查看示例**: 先运行 `npm run seo:analyze-mybacklinks` 查看示例输出
2. **检查网络**: 确保能够访问目标网站
3. **查看日志**: 运行时添加 `--verbose` 参数获取详细日志
4. **提交 Issue**: 在项目仓库中创建 issue，包含错误信息和复现步骤

**快速故障排除**:
```bash
# 从 tools/ 目录检查工具是否正常工作
npm run seo:analyze-mybacklinks

# 如果上述命令成功，说明工具安装正确
# 可以尝试分析其他网站:
npm run seo:analyze https://your-target-website.com
```

---

**🎉 现在就开始分析你的网站，发现 SEO 优化机会！**