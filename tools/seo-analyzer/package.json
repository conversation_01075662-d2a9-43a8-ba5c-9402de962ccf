{"name": "website-seo-analyzer", "version": "1.0.0", "description": "Automated website SEO analysis tool that discovers, tests, and analyzes URLs for SEO issues", "main": "seo-analyzer.js", "bin": {"seo-analyzer": "seo-analyzer.js"}, "scripts": {"start": "ts-node seo-analyzer-cli.ts", "analyze": "ts-node seo-analyzer-cli.ts", "build": "tsc", "dev": "ts-node seo-analyzer-cli.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist", "analyze:mybacklinks": "node seo-analyzer.js https://mybacklinks.app", "analyze:local": "node seo-analyzer.js http://localhost:3000"}, "keywords": ["seo", "website-analysis", "sitemap", "robots.txt", "url-testing", "performance", "accessibility"], "author": "LinkTrackPro", "license": "MIT", "dependencies": {"axios": "^1.6.0", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "commander": "^11.1.0", "csv-writer": "^1.6.0", "dotenv": "^17.2.0", "fast-xml-parser": "^4.3.0", "fs-extra": "^11.1.1", "joi": "^17.11.0", "jsdom": "^23.0.0", "ora": "^5.4.1", "puppeteer": "^21.5.0", "robots-parser": "^3.0.1", "url-parse": "^1.5.10", "yaml": "^2.3.4"}, "devDependencies": {"@types/fs-extra": "^11.0.0", "@types/jest": "^29.5.0", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.0", "@types/node": "^20.8.0", "@types/url-parse": "^1.4.8", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0"}}