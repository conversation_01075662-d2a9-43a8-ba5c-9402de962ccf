{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true}, "include": ["src/**/*", "*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}