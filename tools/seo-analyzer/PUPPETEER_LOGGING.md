# Puppeteer 日志功能增强

## 📋 概述

为了解决 "puppeteer看不见log" 的问题，我们对 PageSpeed Checker 中的 Puppeteer 实现进行了增强，添加了全面的日志监听和调试信息。

## 🔧 实现的改进

### 1. 浏览器控制台日志监听

```javascript
// 监听控制台日志
page.on('console', (msg) => {
  const type = msg.type();
  const text = msg.text();
  console.log(`[Browser ${type.toUpperCase()}]:`, text);
});
```

**作用**: 捕获浏览器内部的所有 console.log、console.warn、console.error 等输出。

### 2. 页面错误监听

```javascript
// 监听页面错误
page.on('pageerror', (error) => {
  console.error('[Browser Error]:', error.message);
});
```

**作用**: 捕获页面中的 JavaScript 错误和异常。

### 3. 请求失败监听

```javascript
// 监听请求失败
page.on('requestfailed', (request) => {
  console.warn('[Request Failed]:', request.url(), request.failure()?.errorText);
});
```

**作用**: 监控网络请求失败，帮助诊断连接问题。

### 4. PageSpeed 分析过程日志

```javascript
console.log('[PageSpeed] Navigating to Desktop URL:', desktopUrl);
console.log('[PageSpeed] Waiting for Desktop analysis to complete...');
console.log('[PageSpeed] Extracting Desktop scores...');
console.log('[PageSpeed] Desktop scores:', desktopScores);
```

**作用**: 提供 PageSpeed 检查过程的详细进度信息。

## 📊 日志输出示例

当 Puppeteer 正常工作时，你会看到类似以下的日志输出：

```
[PageSpeed] Navigating to Desktop URL: https://pagespeed.web.dev/analysis?url=https%3A//example.com&form_factor=desktop
[Browser LOG]: Page loaded successfully
[PageSpeed] Waiting for performance gauge to appear...
[PageSpeed] Performance gauge found, waiting for content to load...
[Browser WARN]: Some non-critical resource failed to load
[PageSpeed] Analysis wait completed
[PageSpeed] Executing page evaluation to extract scores...
[PageSpeed] Raw scores extracted: { performance: 85, accessibility: 92, 'best-practices': 88, seo: 95 }
[PageSpeed] Desktop scores: { performance: 85, accessibility: 92, 'best-practices': 88, seo: 95 }
```

## 🚀 Puppeteer 配置优化

### 更新的启动参数

```javascript
browser = await puppeteer.launch({
  headless: 'new',  // 使用新的 headless 模式，避免弃用警告
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-web-security',
    '--disable-features=VizDisplayCompositor',
    '--disable-extensions',
    '--no-first-run',
    '--no-default-browser-check'
  ]
});
```

**改进**:
- 使用 `headless: 'new'` 替代 `headless: true` 避免弃用警告
- 添加更多稳定性参数
- 优化在不同环境下的兼容性

## 🔍 调试信息类别

| 日志前缀 | 说明 | 示例 |
|---------|------|------|
| `[Browser LOG]` | 浏览器控制台日志 | `[Browser LOG]: Hello from console` |
| `[Browser WARN]` | 浏览器警告 | `[Browser WARN]: Resource load warning` |
| `[Browser ERROR]` | 浏览器错误 | `[Browser ERROR]: JavaScript error occurred` |
| `[Browser Error]` | 页面错误 | `[Browser Error]: ReferenceError: variable is not defined` |
| `[Request Failed]` | 请求失败 | `[Request Failed]: https://example.com/script.js net::ERR_FAILED` |
| `[PageSpeed]` | PageSpeed 分析过程 | `[PageSpeed] Waiting for analysis to complete...` |

## 🛠️ 故障排除

### 常见问题

1. **Puppeteer 无法启动**
   ```
   Error: Failed to launch the browser process!
   ```
   
   **解决方案**:
   - 检查 Chrome/Chromium 是否安装
   - 重新安装 Puppeteer: `npm install puppeteer`
   - 尝试使用 API 模式

2. **网络连接问题**
   ```
   Error: socket hang up
   ```
   
   **解决方案**:
   - 检查网络连接
   - 增加超时时间
   - 使用代理设置

3. **权限问题**
   ```
   Error: Permission denied
   ```
   
   **解决方案**:
   - 添加 `--no-sandbox` 参数
   - 检查文件权限
   - 在 Docker 中运行时注意权限设置

### 环境变量配置

如果 Puppeteer 模式不可用，可以使用 API 模式：

```bash
# 在 .env 文件中设置
PAGESPEED_API_KEY=your_api_key_here
```

## 📈 性能影响

日志监听对性能的影响：
- **最小化**: 日志监听器使用异步事件，不会阻塞主流程
- **可控制**: 可以通过环境变量控制日志级别
- **调试友好**: 在开发和调试阶段提供详细信息

## 🎯 使用建议

1. **开发阶段**: 启用所有日志以便调试
2. **生产环境**: 可以通过环境变量控制日志级别
3. **CI/CD**: 在自动化测试中保留错误日志
4. **监控**: 使用日志来监控 PageSpeed 检查的成功率

现在，当你运行 SEO 分析器时，将能够清楚地看到 Puppeteer 的执行过程和任何可能出现的问题！ 