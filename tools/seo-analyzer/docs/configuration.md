# Configuration Management

The SEO Analyzer provides a flexible configuration system that supports multiple configuration sources and formats.

## Configuration Sources

Configuration is loaded and merged in the following priority order (later sources override earlier ones):

1. **Default values** - Built-in sensible defaults
2. **Configuration file** - JSON or YAML configuration file
3. **Command-line arguments** - CLI flags and options

## Configuration File Formats

### JSON Configuration

```json
{
  "baseUrl": "https://example.com",
  "sitemapUrl": "https://example.com/sitemap.xml",
  "maxConcurrency": 10,
  "requestTimeout": 30000,
  "slowPageThreshold": 3000,
  "maxCrawlDepth": 3,
  "userAgent": "SEO-Analyzer/1.0",
  "customHeaders": {
    "Accept-Language": "en-US,en;q=0.9"
  },
  "excludePatterns": [
    "/admin/*",
    "/private/*",
    "*.pdf"
  ],
  "outputFormats": ["json", "html"],
  "outputDir": "./seo-reports",
  "enableJsRendering": false,
  "jsRenderWaitTime": 5000
}
```

### YAML Configuration

```yaml
# SEO Analyzer Configuration
baseUrl: https://example.com
sitemapUrl: https://example.com/sitemap.xml
maxConcurrency: 10
requestTimeout: 30000
slowPageThreshold: 3000
maxCrawlDepth: 3
userAgent: SEO-Analyzer/1.0

customHeaders:
  Accept-Language: en-US,en;q=0.9
  Accept-Encoding: gzip, deflate, br

excludePatterns:
  - /admin/*
  - /private/*
  - "*.pdf"

outputFormats:
  - json
  - html

outputDir: ./seo-reports
enableJsRendering: false
jsRenderWaitTime: 5000
```

## Configuration File Discovery

The system automatically searches for configuration files in the following order:

1. `seo-analyzer.config.json`
2. `seo-analyzer.config.yaml`
3. `seo-analyzer.config.yml`
4. `.seo-analyzer.json`
5. `.seo-analyzer.yaml`
6. `.seo-analyzer.yml`

## Configuration Options

### Required Options

- **baseUrl** (string): Base URL of the website to analyze

### Optional Options

- **sitemapUrl** (string): Direct sitemap URL
- **maxConcurrency** (number, 1-100): Maximum number of concurrent requests (default: 10)
- **requestTimeout** (number, 1000-300000): Request timeout in milliseconds (default: 30000)
- **slowPageThreshold** (number, 100-60000): Threshold for slow page detection in milliseconds (default: 3000)
- **maxCrawlDepth** (number, 1-10): Maximum crawl depth for web crawling (default: 3)
- **userAgent** (string): User agent string for requests (default: "SEO-Analyzer/1.0")
- **customHeaders** (object): Custom headers to include in requests
- **excludePatterns** (array): URL patterns to exclude from analysis
- **outputFormats** (array): Output formats - json, csv, html (default: ["json", "html"])
- **outputDir** (string): Output directory for generated reports (default: "./seo-reports")
- **enableJsRendering** (boolean): Enable JavaScript rendering for SPAs (default: false)
- **jsRenderWaitTime** (number, 1000-60000): Wait time for JS rendering in milliseconds (default: 5000)

## Command Line Interface

### Basic Usage

```bash
seo-analyzer analyze https://example.com
```

### With Configuration File

```bash
seo-analyzer analyze https://example.com --config ./my-config.json
```

### With CLI Options

```bash
seo-analyzer analyze https://example.com \
  --max-concurrency 5 \
  --request-timeout 20000 \
  --sitemap https://example.com/sitemap.xml \
  --user-agent "Custom Agent" \
  --output-formats json,html \
  --exclude-patterns "/admin/*,*.pdf" \
  --enable-js-rendering \
  --verbose
```

### Generate Sample Configuration

```bash
# Generate JSON configuration
seo-analyzer init --format json --output my-config.json

# Generate YAML configuration
seo-analyzer init --format yaml --output my-config.yaml
```

### Validate Configuration

```bash
seo-analyzer validate-config ./my-config.json
```

## CLI Options Reference

| Option | Description | Type | Default |
|--------|-------------|------|---------|
| `-c, --config <path>` | Path to configuration file | string | - |
| `-s, --sitemap <url>` | Direct sitemap URL | string | - |
| `--max-concurrency <number>` | Maximum concurrent requests | number | 10 |
| `--request-timeout <number>` | Request timeout (ms) | number | 30000 |
| `--slow-page-threshold <number>` | Slow page threshold (ms) | number | 3000 |
| `--max-crawl-depth <number>` | Maximum crawl depth | number | 3 |
| `--user-agent <string>` | User agent string | string | SEO-Analyzer/1.0 |
| `--custom-headers <json>` | Custom headers as JSON | string | {} |
| `--exclude-patterns <patterns>` | Exclude patterns (comma-separated) | string | [] |
| `--output-formats <formats>` | Output formats (comma-separated) | string | json,html |
| `--output-dir <path>` | Output directory | string | ./seo-reports |
| `--enable-js-rendering` | Enable JavaScript rendering | boolean | false |
| `--js-render-wait-time <number>` | JS render wait time (ms) | number | 5000 |
| `--verbose` | Enable verbose logging | boolean | false |
| `--quiet` | Suppress non-error output | boolean | false |

## Configuration Validation

The system validates all configuration values and provides helpful error messages:

- **URL validation**: Ensures baseUrl and sitemapUrl are valid HTTP/HTTPS URLs
- **Range validation**: Validates numeric values are within acceptable ranges
- **Format validation**: Ensures output formats are supported (json, csv, html)
- **Pattern validation**: Validates exclude patterns are valid regular expressions
- **Directory validation**: Ensures output directory is writable

## Error Handling

Configuration errors are reported with specific details:

```bash
Configuration validation failed: "baseUrl" must be a valid uri, "maxConcurrency" must be less than or equal to 100
```

## Examples

### Basic Website Analysis

```bash
seo-analyzer analyze https://example.com
```

### E-commerce Site with Custom Settings

```json
{
  "baseUrl": "https://shop.example.com",
  "maxConcurrency": 5,
  "slowPageThreshold": 2000,
  "excludePatterns": [
    "/admin/*",
    "/checkout/*",
    "/account/*",
    "*.pdf",
    "*.zip"
  ],
  "customHeaders": {
    "Accept-Language": "en-US,en;q=0.9",
    "User-Agent": "SEO-Bot/1.0"
  },
  "outputFormats": ["json", "csv", "html"],
  "outputDir": "./reports/ecommerce"
}
```

### Single Page Application (SPA)

```yaml
baseUrl: https://spa.example.com
enableJsRendering: true
jsRenderWaitTime: 10000
maxCrawlDepth: 2
excludePatterns:
  - /api/*
  - /assets/*
outputFormats:
  - json
  - html
```

## Best Practices

1. **Use configuration files** for complex setups to maintain consistency
2. **Start with defaults** and only override what you need
3. **Test configuration** using the validate-config command
4. **Use appropriate concurrency** - higher isn't always better
5. **Set realistic timeouts** based on your website's performance
6. **Use exclude patterns** to avoid analyzing unnecessary pages
7. **Enable JS rendering** only when needed (it's slower)