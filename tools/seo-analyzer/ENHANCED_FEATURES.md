# SEO Analyzer Enhanced Features

本文档说明了 SEO Analyzer 新增的高级功能和配置。

## 🆕 新增功能

### 1. Sitemap.xml 文件类型检查
- **功能**: 检查 sitemap.xml 中是否包含文件类型 URL（如图片、文档等）
- **规范**: sitemap.xml 应该只包含网页 URL，不应包含 `.png`、`.pdf`、`robots.txt`、`llms.txt` 等文件
- **检查项目**:
  - 检测常见文件扩展名
  - 检测特殊文件名（如 `og.png`、`favicon.ico`）
  - 提供详细的违规 URL 统计

### 2. AI 驱动的 llms.txt 检查
- **功能**: 使用 DeepSeek R1 AI 模型检查 llms.txt 格式规范
- **AI 模型**: `deepseek-r1-distill-qwen-32b`
- **检查标准**: 
  - Markdown 格式验证
  - H1 标题格式：`# [网站名称] > [网站简介]`
  - H2 类别标题格式
  - 链接列表格式：`[链接文本](URL): 描述`
- **配置**: 需要设置 `AI_API_KEY` 环境变量

### 3. 增强的标题标签检查
- **功能**: 全面的 H1-H6 标签规范检查
- **检查项目**:
  - H1 标签唯一性和长度（推荐 < 70 字符）
  - H2-H6 标签长度优化（H2 < 60，H3 < 50，H4-H6 < 40）
  - 标题层级结构完整性
  - 空标题检测
  - 重复标题内容检测
  - 标题分布平衡性分析

### 4. 图片 Alt 描述全面检查
- **功能**: 详细的图片可访问性和 SEO 分析
- **检查项目**:
  - 缺失 alt 属性检测
  - 空 alt 属性（装饰性图片）验证
  - Alt 文本长度检查（推荐 5-125 字符）
  - 低质量 alt 文本模式检测
  - 冗余前缀检测（"image of", "picture of"）
  - 装饰性图片错误分类检测
- **统计报告**: 提供图片数量、alt 覆盖率等统计

### 5. Google PageSpeed Insights 集成
- **功能**: 自动化的 PageSpeed Insights 性能检查
- **检查方式**:
  - **API 模式**: 使用官方 API（需要 `PAGESPEED_API_KEY`）
  - **Puppeteer 模式**: 浏览器自动化抓取（无需 API Key）
- **检查范围**:
  - Desktop 和 Mobile 双端检查
  - Performance、Accessibility、Best Practices、SEO 四大类评分
  - Core Web Vitals 指标（LCP、FID、CLS）
  - 详细的性能建议

### 6. Keywords 长度限制检查
- **功能**: Meta keywords 标签长度规范检查
- **限制**: 不超过 100 字符
- **额外检查**:
  - 重复关键词检测
  - 空 keywords 标签检测
  - 关键词分布分析

### 7. 关键词密度智能分析
- **功能**: 内容关键词密度分析和过度优化检测
- **分析指标**:
  - 单词频率统计
  - 关键词密度计算（百分比）
  - 高密度关键词标记（> 3%）
  - 过度优化警告（> 5%）
- **反垃圾检测**:
  - 关键词堆砌模式识别
  - 重复短语检测
  - 内容质量评估

## 🔧 环境变量配置

创建 `.env` 文件或设置以下环境变量：

```bash
# DeepSeek AI API Key (用于 llms.txt 检查)
AI_API_KEY=your_deepseek_api_key_here

# Google PageSpeed Insights API Key (可选，不设置则使用 Puppeteer)
PAGESPEED_API_KEY=your_pagespeed_api_key_here
```

### 获取 API Keys

1. **DeepSeek API Key**:
   - 访问 [DeepSeek 官网](https://platform.deepseek.com/)
   - 注册账号并获取 API Key
   - 支持 OpenAI 兼容接口

2. **PageSpeed Insights API Key**:
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 启用 PageSpeed Insights API
   - 创建 API Key

## 📊 使用示例

```bash
# 1. 复制环境变量模板
cp env.example .env

# 2. 编辑 .env 文件，添加你的 API Keys
# AI_API_KEY=your_deepseek_key_here
# PAGESPEED_API_KEY=your_pagespeed_key_here

# 3. 基本使用（使用 Puppeteer 模式，无需 API key）
npm run analyze -- analyze https://example.com

# 4. 使用 AI 检查模式（需要 AI_API_KEY）
npm run analyze -- analyze https://example.com --verbose

# 5. 直接通过 ts-node 运行
ts-node seo-analyzer-cli.ts analyze https://example.com
```

## 🏗️ 架构改进

### 重构的模块化设计
- **`ComplianceChecker`**: 重构为协调器模式，委托给专门的检查器
- **`JsonLdChecker`**: 专门的 JSON-LD 结构化数据检查器
- **`SitemapChecker`**: 专门的 sitemap.xml 检查器
- **`RobotsChecker`**: 专门的 robots.txt 检查器
- **`LlmsChecker`**: 使用 AI SDK 的 llms.txt 检查器

### 新增服务模块
- `PageSpeedChecker`: PageSpeed Insights 检查服务（支持环境变量配置）
- `Enhanced Keyword Analysis`: 增强的关键词分析
- `AI-powered Validation`: 使用 ai-sdk 的 AI 驱动验证

### 改进的现有模块
- `SeoAnalyzer`: 扩展的标题和图片分析
- `SeoIssue Interface`: 新增 "llms" 类别
- `CLI`: 支持环境变量自动加载

## 🎯 性能与可靠性

### 错误处理
- AI API 调用失败时的优雅降级
- PageSpeed 检查超时处理
- 网络连接异常恢复

### 性能优化
- 并行检查执行
- 智能超时设置
- 内存使用优化

### 扩展性
- 支持自定义 AI 模型配置
- 可插拔的检查模块设计
- 灵活的报告格式输出

## 📋 检查报告示例

新的检查会在报告中显示为：

```json
{
  "type": "warning",
  "category": "llms",
  "url": "https://example.com/llms.txt",
  "message": "AI validation found 2 issues with llms.txt format",
  "recommendation": "Fix llms.txt format according to AI validation report",
  "details": {
    "aiValidationReport": "详细的 AI 检查报告...",
    "problemCount": 2
  }
}
```

## 🚀 下一步计划

- 集成更多 AI 模型支持
- 添加视觉回归测试
- 支持多语言内容分析
- 增加实时监控功能 