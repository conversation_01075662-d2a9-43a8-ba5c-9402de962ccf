# SEO Analyzer Configuration File
# This is an example configuration file showing all available options

# Base URL of the website to analyze (required)
baseUrl: https://example.com

# Optional direct sitemap URL
sitemapUrl: https://example.com/sitemap.xml

# Maximum number of concurrent requests (1-100)
maxConcurrency: 10

# Request timeout in milliseconds (1000-300000)
requestTimeout: 30000

# Threshold for slow page detection in milliseconds (100-60000)
slowPageThreshold: 3000

# Maximum crawl depth for web crawling (1-10)
maxCrawlDepth: 3

# User agent string for requests
userAgent: SEO-Analyzer/1.0

# Custom headers to include in requests
customHeaders:
  Accept-Language: en-US,en;q=0.9
  Accept-Encoding: gzip, deflate, br

# URL patterns to exclude from analysis
excludePatterns:
  - /admin/*
  - /private/*
  - "*.pdf"
  - "*.doc"
  - "*.docx"
  - /api/internal/*

# Output formats for reports (json, csv, html)
outputFormats:
  - json
  - html

# Output directory for generated reports
outputDir: ./seo-reports

# Enable JavaScript rendering for SPAs
enableJsRendering: false

# Wait time for JS rendering in milliseconds (1000-60000)
jsRenderWaitTime: 5000