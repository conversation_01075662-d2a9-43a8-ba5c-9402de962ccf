import { ReportGenerator, AnalysisReport, AnalysisSummary } from '../interfaces/report';
import { SeoIssue } from '../interfaces/seo-analyzer';
import { UrlTestResult } from '../interfaces/url-testing';
import { AnalyzerConfig } from '../interfaces/config';
import { promises as fs } from 'fs';
import { join } from 'path';

/**
 * Implementation of report generator service
 */
export class ReportGeneratorImpl implements ReportGenerator {
  private config: AnalyzerConfig;

  constructor(config: AnalyzerConfig) {
    this.config = config;
  }

  /**
   * Generate and save reports in configured formats
   */
  async generateReport(report: AnalysisReport): Promise<void> {
    // Ensure output directory exists
    try {
      await fs.mkdir(this.config.outputDir, { recursive: true });
    } catch (error) {
      console.warn('Could not create output directory:', error);
    }

    const baseFileName = this.generateFileName();
    const promises: Promise<void>[] = [];

    // Generate reports in configured formats
    for (const format of this.config.outputFormats) {
      switch (format) {
        case 'json':
          promises.push(this.saveJsonReport(report, baseFileName));
          break;
        case 'csv':
          promises.push(this.saveCsvReport(report, baseFileName));
          break;
        case 'html':
          promises.push(this.saveHtmlReport(report, baseFileName));
          break;
      }
    }

    await Promise.all(promises);
    console.log(`Reports generated in ${this.config.outputDir}:`);
    console.log(`- ${baseFileName}.json (${this.config.outputFormats.includes('json') ? 'Generated' : 'Skipped'})`);
    console.log(`- ${baseFileName}.csv (${this.config.outputFormats.includes('csv') ? 'Generated' : 'Skipped'})`);
    console.log(`- ${baseFileName}.html (${this.config.outputFormats.includes('html') ? 'Generated' : 'Skipped'})`);
  }

  /**
   * Export report as JSON
   */
  exportJson(report: AnalysisReport): string {
    return JSON.stringify(report, null, 2);
  }

  /**
   * Export report as CSV
   */
  exportCsv(report: AnalysisReport): string {
    const headers = [
      'URL',
      'Issue Type',
      'Category',
      'Message',
      'Recommendation',
      'Status Code',
      'Load Time (ms)',
      'Response Size (bytes)'
    ];

    const rows = [headers.join(',')];

    // Add URL results with issues
    report.issues.forEach(issue => {
      const urlResult = report.urlResults.find(r => r.url === issue.url);
      const row = [
        this.escapeCsvValue(issue.url),
        this.escapeCsvValue(issue.type),
        this.escapeCsvValue(issue.category),
        this.escapeCsvValue(issue.message),
        this.escapeCsvValue(issue.recommendation),
        urlResult?.statusCode?.toString() || '',
        urlResult?.loadTime?.toString() || '',
        urlResult?.responseSize?.toString() || ''
      ];
      rows.push(row.join(','));
    });

    // Add URLs without issues
    report.urlResults
      .filter(result => !report.issues.some(issue => issue.url === result.url))
      .forEach(result => {
        const row = [
          this.escapeCsvValue(result.url),
          'info',
          'performance',
          'No issues found',
          'Continue monitoring',
          result.statusCode.toString(),
          result.loadTime.toString(),
          result.responseSize.toString()
        ];
        rows.push(row.join(','));
      });

    return rows.join('\n');
  }

  /**
   * Export report as HTML
   */
  exportHtml(report: AnalysisReport): string {
    const summary = report.summary;
    const issues = report.issues;
    const urlResults = report.urlResults;

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Analysis Report - ${this.config.baseUrl}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header .url {
            font-size: 18px;
            color: #6c757d;
            word-break: break-all;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .metric.error {
            border-left-color: #dc3545;
        }
        .metric.warning {
            border-left-color: #ffc107;
        }
        .metric.success {
            border-left-color: #28a745;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            display: block;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .issue {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            padding: 20px;
            border-left: 4px solid #6c757d;
        }
        .issue.error {
            border-left-color: #dc3545;
            background-color: #fff5f5;
        }
        .issue.warning {
            border-left-color: #ffc107;
            background-color: #fffbf0;
        }
        .issue.info {
            border-left-color: #17a2b8;
            background-color: #f0fbfc;
        }
        .issue-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        .issue-type {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
            margin-right: 10px;
        }
        .issue-type.error {
            background: #dc3545;
            color: white;
        }
        .issue-type.warning {
            background: #ffc107;
            color: #212529;
        }
        .issue-type.info {
            background: #17a2b8;
            color: white;
        }
        .issue-category {
            background: #6c757d;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            text-transform: capitalize;
        }
        .issue-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 8px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
            font-size: 0.9em;
        }
        .issue-message {
            font-weight: 500;
            margin-bottom: 8px;
        }
        .issue-recommendation {
            color: #6c757d;
            font-style: italic;
        }
        .url-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        .url-item {
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .url-item:last-child {
            border-bottom: none;
        }
        .url-item.error {
            background-color: #fff5f5;
        }
        .url-item.slow {
            background-color: #fffbf0;
        }
        .url-item .url {
            font-family: monospace;
            font-size: 0.9em;
            word-break: break-all;
            flex: 1;
            margin-right: 15px;
        }
        .url-item .metrics {
            display: flex;
            gap: 15px;
            font-size: 0.8em;
            color: #6c757d;
        }
        .status-code {
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-2xx {
            background: #d4edda;
            color: #155724;
        }
        .status-3xx {
            background: #fff3cd;
            color: #856404;
        }
        .status-4xx {
            background: #f8d7da;
            color: #721c24;
        }
        .status-5xx {
            background: #d1ecf1;
            color: #0c5460;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SEO Analysis Report</h1>
            <div class="url">${this.escapeHtml(this.config.baseUrl)}</div>
            <div style="margin-top: 10px; font-size: 14px; color: #6c757d;">
                Generated on ${report.generatedAt.toLocaleString()}
            </div>
        </div>

        <div class="section">
            <h2>Summary</h2>
            <div class="summary">
                <div class="metric">
                    <span class="metric-value">${summary.totalUrls}</span>
                    <div class="metric-label">Total URLs</div>
                </div>
                <div class="metric ${summary.errorUrls > 0 ? 'error' : 'success'}">
                    <span class="metric-value">${summary.errorUrls}</span>
                    <div class="metric-label">Error URLs</div>
                </div>
                <div class="metric ${summary.warningUrls > 0 ? 'warning' : 'success'}">
                    <span class="metric-value">${summary.warningUrls}</span>
                    <div class="metric-label">Warning URLs</div>
                </div>
                <div class="metric ${summary.slowUrls > 0 ? 'warning' : 'success'}">
                    <span class="metric-value">${summary.slowUrls}</span>
                    <div class="metric-label">Slow URLs</div>
                </div>
                <div class="metric">
                    <span class="metric-value">${Math.round(summary.avgLoadTime)}ms</span>
                    <div class="metric-label">Avg Load Time</div>
                </div>
                <div class="metric">
                    <span class="metric-value">${Math.round(summary.analysisDuration / 1000)}s</span>
                    <div class="metric-label">Analysis Duration</div>
                </div>
            </div>
        </div>

        ${issues.length > 0 ? this.generateIssuesSection(issues) : ''}
        
        <div class="section">
            <h2>URL Analysis Results (${urlResults.length})</h2>
            <div class="url-list">
                ${urlResults.map(result => this.generateUrlItem(result, issues)).join('')}
            </div>
        </div>

        <div class="footer">
            <p>Report generated by SEO Analyzer | Analysis completed in ${Math.round(summary.analysisDuration / 1000)} seconds</p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate summary statistics
   */
  generateSummary(issues: SeoIssue[], urlResults: UrlTestResult[], startTime: Date, endTime: Date): AnalysisSummary {
    const totalUrls = urlResults.length;
    const errorUrls = new Set();
    const warningUrls = new Set();
    const issuesByCategory: Record<string, number> = {};

    // Count issues by type and category
    issues.forEach(issue => {
      if (issue.type === 'error') {
        errorUrls.add(issue.url);
      } else if (issue.type === 'warning') {
        warningUrls.add(issue.url);
      }

      if (!issuesByCategory[issue.category]) {
        issuesByCategory[issue.category] = 0;
      }
      issuesByCategory[issue.category]++;
    });

    // Calculate performance metrics
    const loadTimes = urlResults
      .filter(result => result.loadTime > 0)
      .map(result => result.loadTime);
    
    const avgLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length 
      : 0;

    const slowUrls = urlResults.filter(result => 
      result.loadTime > this.config.slowPageThreshold
    ).length;

    const notFoundUrls = urlResults.filter(result => 
      result.statusCode === 404
    ).length;

    const robotsViolations = issues.filter(issue => 
      issue.category === 'robots' && issue.type !== 'info'
    ).length;

    return {
      totalUrls,
      errorUrls: errorUrls.size,
      warningUrls: warningUrls.size,
      avgLoadTime,
      slowUrls,
      notFoundUrls,
      robotsViolations,
      issuesByCategory,
      analysisDuration: endTime.getTime() - startTime.getTime()
    };
  }

  /**
   * Generate filename based on current timestamp
   */
  private generateFileName(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const domain = new URL(this.config.baseUrl).hostname.replace(/[^\w-]/g, '');
    return `seo-analysis-${domain}-${timestamp}`;
  }

  /**
   * Save JSON report to file
   */
  private async saveJsonReport(report: AnalysisReport, baseFileName: string): Promise<void> {
    const filePath = join(this.config.outputDir, `${baseFileName}.json`);
    const content = this.exportJson(report);
    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * Save CSV report to file
   */
  private async saveCsvReport(report: AnalysisReport, baseFileName: string): Promise<void> {
    const filePath = join(this.config.outputDir, `${baseFileName}.csv`);
    const content = this.exportCsv(report);
    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * Save HTML report to file
   */
  private async saveHtmlReport(report: AnalysisReport, baseFileName: string): Promise<void> {
    const filePath = join(this.config.outputDir, `${baseFileName}.html`);
    const content = this.exportHtml(report);
    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * Escape CSV values
   */
  private escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  /**
   * Escape HTML content
   */
  private escapeHtml(text: string): string {
    const map: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    };
    return text.replace(/[&<>"']/g, char => map[char]);
  }

  /**
   * Generate issues section HTML
   */
  private generateIssuesSection(issues: SeoIssue[]): string {
    const errorIssues = issues.filter(i => i.type === 'error');
    const warningIssues = issues.filter(i => i.type === 'warning');
    const infoIssues = issues.filter(i => i.type === 'info');

    let html = '<div class="section"><h2>Issues Found</h2>';

    if (errorIssues.length > 0) {
      html += '<h3 style="color: #dc3545;">Errors</h3>';
      html += errorIssues.map(issue => this.generateIssueHtml(issue)).join('');
    }

    if (warningIssues.length > 0) {
      html += '<h3 style="color: #ffc107;">Warnings</h3>';
      html += warningIssues.map(issue => this.generateIssueHtml(issue)).join('');
    }

    if (infoIssues.length > 0) {
      html += '<h3 style="color: #17a2b8;">Information</h3>';
      html += infoIssues.map(issue => this.generateIssueHtml(issue)).join('');
    }

    html += '</div>';
    return html;
  }

  /**
   * Generate single issue HTML
   */
  private generateIssueHtml(issue: SeoIssue): string {
    return `
    <div class="issue ${issue.type}">
        <div class="issue-header">
            <span class="issue-type ${issue.type}">${issue.type}</span>
            <span class="issue-category">${issue.category}</span>
        </div>
        <div class="issue-url">${this.escapeHtml(issue.url)}</div>
        <div class="issue-message">${this.escapeHtml(issue.message)}</div>
        <div class="issue-recommendation">${this.escapeHtml(issue.recommendation)}</div>
    </div>`;
  }

  /**
   * Generate URL item HTML
   */
  private generateUrlItem(result: UrlTestResult, issues: SeoIssue[]): string {
    const hasError = result.statusCode >= 400 || result.error;
    const isSlow = result.loadTime > this.config.slowPageThreshold;
    const issueCount = issues.filter(i => i.url === result.url).length;

    const statusClass = result.statusCode >= 200 && result.statusCode < 300 ? 'status-2xx' :
                       result.statusCode >= 300 && result.statusCode < 400 ? 'status-3xx' :
                       result.statusCode >= 400 && result.statusCode < 500 ? 'status-4xx' :
                       'status-5xx';

    return `
    <div class="url-item ${hasError ? 'error' : isSlow ? 'slow' : ''}">
        <div class="url">${this.escapeHtml(result.url)}</div>
        <div class="metrics">
            <span class="status-code ${statusClass}">${result.statusCode}</span>
            <span>${result.loadTime}ms</span>
            <span>${Math.round(result.responseSize / 1024)}KB</span>
            ${issueCount > 0 ? `<span style="color: #dc3545;">${issueCount} issues</span>` : ''}
        </div>
    </div>`;
  }
}