import { SeoIssue } from '../interfaces/seo-analyzer';
import { AnalyzerConfig } from '../interfaces/config';
import { UrlTestResult } from '../interfaces/url-testing';
import { JsonLdChecker } from './checkers/json-ld-checker';
import { SitemapChecker } from './checkers/sitemap-checker';
import { RobotsChecker } from './checkers/robots-checker';
import { LlmsChecker } from './checkers/llms-checker';

/**
 * 高级合规性检查器 - 协调各种技术标准文件的合规性检查
 * 包括 JSON-LD、sitemap.xml、robots.txt、llms.txt 等标准的深度验证
 */
export class ComplianceChecker {
  private config: AnalyzerConfig;
  private jsonLdChecker: JsonLdChecker;
  private sitemapChecker: SitemapChecker;
  private robotsChecker: RobotsChecker;
  private llmsChecker: LlmsChecker;

  constructor(config: AnalyzerConfig) {
    this.config = config;
    this.jsonLdChecker = new JsonLdChecker();
    this.sitemapChecker = new SitemapChecker();
    this.robotsChecker = new RobotsChecker();
    this.llmsChecker = new LlmsChecker(config);
  }

  /**
   * 检查 JSON-LD 结构化数据的合规性
   */
  validateJsonLdCompliance(url: string, content: string): SeoIssue[] {
    return this.jsonLdChecker.validateJsonLdCompliance(url, content);
  }

  /**
   * 检查 sitemap.xml 的合规性
   */
  async validateSitemapCompliance(sitemapUrl: string, sitemapContent: string): Promise<SeoIssue[]> {
    return this.sitemapChecker.validateSitemapCompliance(sitemapUrl, sitemapContent);
  }

  /**
   * 检查 robots.txt 的合规性
   */
  validateRobotsCompliance(robotsUrl: string, robotsContent: string): SeoIssue[] {
    return this.robotsChecker.validateRobotsCompliance(robotsUrl, robotsContent);
  }

  /**
   * 检查 llms.txt 的合规性
   */
  async validateLlmsTxtCompliance(baseUrl: string): Promise<SeoIssue[]> {
    return this.llmsChecker.validateLlmsTxtCompliance(baseUrl);
  }

  /**
   * 生成合规性检查报告
   */
  async generateComplianceReport(baseUrl: string, urlResults: UrlTestResult[]): Promise<SeoIssue[]> {
    const allIssues: SeoIssue[] = [];

    // 1. 检查主页的 JSON-LD
    const mainPageResult = urlResults.find(result => result.url === baseUrl || result.url === `${baseUrl}/`);
    if (mainPageResult?.content) {
      const jsonLdIssues = this.validateJsonLdCompliance(mainPageResult.url, mainPageResult.content);
      allIssues.push(...jsonLdIssues);
    }

    // 2. 检查 sitemap.xml
    const sitemapResult = urlResults.find(result => result.url.includes('sitemap.xml'));
    if (sitemapResult?.content && sitemapResult.statusCode === 200) {
      const sitemapIssues = await this.validateSitemapCompliance(sitemapResult.url, sitemapResult.content);
      allIssues.push(...sitemapIssues);
    }

    // 3. 检查 robots.txt
    const robotsResult = urlResults.find(result => result.url.includes('robots.txt'));
    if (robotsResult?.content && robotsResult.statusCode === 200) {
      const robotsIssues = this.validateRobotsCompliance(robotsResult.url, robotsResult.content);
      allIssues.push(...robotsIssues);
    }

    // 4. 检查 llms.txt
    const llmsIssues = await this.validateLlmsTxtCompliance(baseUrl);
    allIssues.push(...llmsIssues);

    return allIssues;
  }
} 