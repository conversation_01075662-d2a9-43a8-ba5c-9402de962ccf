import puppeteer from 'puppeteer';
import { SeoIssue } from '../interfaces/seo-analyzer';
import axios from 'axios';

/**
 * Google PageSpeed Insights 检查器
 * 使用 Google PageSpeed Insights API 和 puppeteer 进行性能分析
 */
export class PageSpeedChecker {
  private apiKey?: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.PAGESPEED_API_KEY;
  }

  /**
   * 检查页面的 PageSpeed Insights 分数
   */
  async checkPageSpeed(url: string): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];

    try {
      // 方法1: 使用 Google PageSpeed Insights API (如果有 API Key)
      if (this.apiKey) {
        const apiIssues = await this.checkWithAPI(url);
        issues.push(...apiIssues);
      } else {
        // 方法2: 使用 puppeteer 抓取 PageSpeed Insights 网页
        const puppeteerIssues = await this.checkWithPuppeteer(url);
        issues.push(...puppeteerIssues);
      }
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'performance',
        url,
        message: `PageSpeed Insights check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Unable to retrieve PageSpeed Insights data. Check connectivity and try again.'
      });
    }

    return issues;
  }

  /**
   * 使用 Google PageSpeed Insights API 检查
   */
  private async checkWithAPI(url: string): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];

    try {
      // Desktop 检查
      const desktopResponse = await axios.get('https://www.googleapis.com/pagespeedonline/v5/runPagespeed', {
        params: {
          url,
          key: this.apiKey,
          strategy: 'desktop',
          category: ['PERFORMANCE', 'ACCESSIBILITY', 'BEST_PRACTICES', 'SEO']
        },
        timeout: 60000 // 60 seconds timeout
      });

      // Mobile 检查
      const mobileResponse = await axios.get('https://www.googleapis.com/pagespeedonline/v5/runPagespeed', {
        params: {
          url,
          key: this.apiKey,
          strategy: 'mobile',
          category: ['PERFORMANCE', 'ACCESSIBILITY', 'BEST_PRACTICES', 'SEO']
        },
        timeout: 60000 // 60 seconds timeout
      });

      // 分析 Desktop 结果
      issues.push(...this.analyzePageSpeedResults(url, desktopResponse.data, 'desktop'));
      
      // 分析 Mobile 结果
      issues.push(...this.analyzePageSpeedResults(url, mobileResponse.data, 'mobile'));

    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 400) {
        issues.push({
          type: 'warning',
          category: 'performance',
          url,
          message: 'PageSpeed Insights API could not analyze this URL',
          recommendation: 'Ensure the URL is publicly accessible and valid'
        });
      } else {
        throw error;
      }
    }

    return issues;
  }

  /**
   * 使用 puppeteer 抓取 PageSpeed Insights 网页
   */
  private async checkWithPuppeteer(url: string): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];
    let browser;

    try {
      browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-extensions',
          '--no-first-run',
          '--no-default-browser-check'
        ]
      });

      const page = await browser.newPage();
      
      // 监听控制台日志
      page.on('console', (msg) => {
        const type = msg.type();
        const text = msg.text();
        console.log(`[Browser ${type.toUpperCase()}]:`, text);
      });

      // 监听页面错误
      page.on('pageerror', (error) => {
        console.error('[Browser Error]:', error.message);
      });

      // 监听请求失败
      page.on('requestfailed', (request) => {
        console.warn('[Request Failed]:', request.url(), request.failure()?.errorText);
      });
      
      // 设置用户代理
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // 检查 Desktop 版本
      const desktopUrl = `https://pagespeed.web.dev/analysis?url=${encodeURIComponent(url)}&form_factor=desktop`;
      console.log('[PageSpeed] Navigating to Desktop URL:', desktopUrl);
      await page.goto(desktopUrl, { waitUntil: 'networkidle2', timeout: 60000 });
      
      // 等待分析完成
      console.log('[PageSpeed] Waiting for Desktop analysis to complete...');
      await this.waitForAnalysis(page);
      
      // 提取 Desktop 分数
      console.log('[PageSpeed] Extracting Desktop scores...');
      const desktopScores = await this.extractScores(page);
      console.log('[PageSpeed] Desktop scores:', desktopScores);
      issues.push(...this.createIssuesFromScores(url, desktopScores, 'desktop'));
      
      // 检查 Mobile 版本
      const mobileUrl = `https://pagespeed.web.dev/analysis?url=${encodeURIComponent(url)}&form_factor=mobile`;
      console.log('[PageSpeed] Navigating to Mobile URL:', mobileUrl);
      await page.goto(mobileUrl, { waitUntil: 'networkidle2', timeout: 60000 });
      
      // 等待分析完成
      console.log('[PageSpeed] Waiting for Mobile analysis to complete...');
      await this.waitForAnalysis(page);
      
      // 提取 Mobile 分数
      console.log('[PageSpeed] Extracting Mobile scores...');
      const mobileScores = await this.extractScores(page);
      console.log('[PageSpeed] Mobile scores:', mobileScores);
      issues.push(...this.createIssuesFromScores(url, mobileScores, 'mobile'));

    } finally {
      if (browser) {
        await browser.close();
      }
    }

    return issues;
  }

  /**
   * 等待 PageSpeed Insights 分析完成
   */
  private async waitForAnalysis(page: any): Promise<void> {
    try {
      console.log('[PageSpeed] Waiting for performance gauge to appear...');
      // 等待分数元素出现，最多等待 120 秒
      await page.waitForSelector('[data-i18n="lh-gauge__label--performance"], .lh-gauge--performance', { 
        timeout: 120000 
      });
      
      console.log('[PageSpeed] Performance gauge found, waiting for content to load...');
      // 额外等待确保所有内容加载完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('[PageSpeed] Analysis wait completed');
    } catch (error) {
      console.warn('[PageSpeed] Analysis may not have completed fully:', error.message);
    }
  }

  /**
   * 从页面提取分数
   */
  private async extractScores(page: any): Promise<{[key: string]: number}> {
    try {
      console.log('[PageSpeed] Executing page evaluation to extract scores...');
      const scores = await page.evaluate(() => {
        const result: {[key: string]: number} = {};
        
        // 尝试多种选择器来获取分数
        const selectors = [
          '.lh-gauge--performance .lh-gauge__percentage',
          '.lh-gauge--accessibility .lh-gauge__percentage',
          '.lh-gauge--best-practices .lh-gauge__percentage',
          '.lh-gauge--seo .lh-gauge__percentage'
        ];
        
        const categories = ['performance', 'accessibility', 'best-practices', 'seo'];
        
        selectors.forEach((selector, index) => {
          const element = document.querySelector(selector);
          if (element) {
            const scoreText = element.textContent?.trim();
            if (scoreText) {
              const score = parseInt(scoreText.replace('%', ''));
              if (!isNaN(score)) {
                result[categories[index]] = score;
              }
            }
          }
        });
        
        return result;
      });
      
      console.log('[PageSpeed] Raw scores extracted:', scores);
      return scores;
    } catch (error) {
      console.warn('[PageSpeed] Failed to extract scores from page:', error.message);
      return {};
    }
  }

  /**
   * 根据分数创建问题
   */
  private createIssuesFromScores(url: string, scores: {[key: string]: number}, device: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    Object.entries(scores).forEach(([category, score]) => {
      let issueType: 'error' | 'warning' | 'info' = 'info';
      let message = '';
      let recommendation = '';
      
      if (score < 50) {
        issueType = 'error';
        message = `${device} ${category} score is poor (${score}/100)`;
        recommendation = `Improve ${category} optimization. Score below 50 indicates significant issues.`;
      } else if (score < 90) {
        issueType = 'warning';
        message = `${device} ${category} score needs improvement (${score}/100)`;
        recommendation = `Optimize ${category} elements. Aim for a score of 90+ for better performance.`;
      } else {
        issueType = 'info';
        message = `${device} ${category} score is good (${score}/100)`;
        recommendation = `Good ${category} performance. Maintain current optimization practices.`;
      }
      
      issues.push({
        type: issueType,
        category: 'performance',
        url,
        message,
        recommendation,
        details: {
          device,
          category,
          score,
          threshold: score < 50 ? 'poor' : score < 90 ? 'needs-improvement' : 'good'
        }
      });
    });
    
    return issues;
  }

  /**
   * 分析 PageSpeed Insights API 结果
   */
  private analyzePageSpeedResults(url: string, data: any, device: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      const categories = data.lighthouseResult?.categories || {};
      
      Object.entries(categories).forEach(([categoryName, categoryData]: [string, any]) => {
        const score = Math.round((categoryData.score || 0) * 100);
        let issueType: 'error' | 'warning' | 'info' = 'info';
        let message = '';
        let recommendation = '';
        
        if (score < 50) {
          issueType = 'error';
          message = `${device} ${categoryName} score is poor (${score}/100)`;
          recommendation = `Critical ${categoryName} issues need immediate attention.`;
        } else if (score < 90) {
          issueType = 'warning';
          message = `${device} ${categoryName} score needs improvement (${score}/100)`;
          recommendation = `Optimize ${categoryName} to reach 90+ score.`;
        } else {
          issueType = 'info';
          message = `${device} ${categoryName} score is excellent (${score}/100)`;
          recommendation = `Maintain current ${categoryName} optimization.`;
        }
        
        issues.push({
          type: issueType,
          category: 'performance',
          url,
          message,
          recommendation,
          details: {
            device,
            category: categoryName,
            score,
            auditRefs: categoryData.auditRefs?.slice(0, 5) // Top 5 audits
          }
        });
      });
      
      // 添加核心 Web Vitals 信息
      const loadingExperience = data.loadingExperience;
      if (loadingExperience?.metrics) {
        const vitals = this.extractCoreWebVitals(loadingExperience.metrics);
        vitals.forEach(vital => {
          issues.push({
            type: vital.category as 'error' | 'warning' | 'info',
            category: 'performance',
            url,
            message: `${device} Core Web Vitals - ${vital.name}: ${vital.status}`,
            recommendation: vital.recommendation,
            details: {
              device,
              metric: vital.name,
              value: vital.value,
              status: vital.status
            }
          });
        });
      }
      
    } catch (error) {
      issues.push({
        type: 'warning',
        category: 'performance',
        url,
        message: `Failed to parse ${device} PageSpeed Insights results`,
        recommendation: 'Results may be incomplete due to parsing error'
      });
    }
    
    return issues;
  }

  /**
   * 提取核心 Web Vitals
   */
  private extractCoreWebVitals(metrics: any): Array<{
    name: string;
    value: string;
    status: string;
    category: string;
    recommendation: string;
  }> {
    const vitals = [];
    
    // Largest Contentful Paint (LCP)
    if (metrics.LARGEST_CONTENTFUL_PAINT_MS) {
      const lcp = metrics.LARGEST_CONTENTFUL_PAINT_MS;
      const value = lcp.percentile;
      const status = value <= 2500 ? 'Good' : value <= 4000 ? 'Needs Improvement' : 'Poor';
      const category = status === 'Good' ? 'info' : status === 'Needs Improvement' ? 'warning' : 'error';
      
      vitals.push({
        name: 'Largest Contentful Paint',
        value: `${value}ms`,
        status,
        category,
        recommendation: status === 'Good' ? 'LCP is within good range' : 'Optimize loading performance to improve LCP'
      });
    }
    
    // First Input Delay (FID)
    if (metrics.FIRST_INPUT_DELAY_MS) {
      const fid = metrics.FIRST_INPUT_DELAY_MS;
      const value = fid.percentile;
      const status = value <= 100 ? 'Good' : value <= 300 ? 'Needs Improvement' : 'Poor';
      const category = status === 'Good' ? 'info' : status === 'Needs Improvement' ? 'warning' : 'error';
      
      vitals.push({
        name: 'First Input Delay',
        value: `${value}ms`,
        status,
        category,
        recommendation: status === 'Good' ? 'FID is within good range' : 'Optimize JavaScript execution to improve FID'
      });
    }
    
    // Cumulative Layout Shift (CLS)
    if (metrics.CUMULATIVE_LAYOUT_SHIFT_SCORE) {
      const cls = metrics.CUMULATIVE_LAYOUT_SHIFT_SCORE;
      const value = cls.percentile;
      const status = value <= 0.1 ? 'Good' : value <= 0.25 ? 'Needs Improvement' : 'Poor';
      const category = status === 'Good' ? 'info' : status === 'Needs Improvement' ? 'warning' : 'error';
      
      vitals.push({
        name: 'Cumulative Layout Shift',
        value: value.toFixed(3),
        status,
        category,
        recommendation: status === 'Good' ? 'CLS is within good range' : 'Reduce layout shifts to improve CLS'
      });
    }
    
    return vitals;
  }
} 