import { SeoIssue } from '../interfaces/seo-analyzer';
import { UrlTestResult } from '../interfaces/url-testing';
import { Logger } from './logger';

/**
 * Security Checker
 * 专门处理网站安全相关的SEO问题
 */
export class SecurityChecker {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * 检测安全问题
   */
  detectSecurityIssues(urlResults: UrlTestResult[], baseUrl: string): SeoIssue[] {
    this.logger.debug('SECURITY', 'Checking security issues');
    
    const issues: SeoIssue[] = [];

    urlResults.forEach(result => {
      if (!result.content || result.statusCode !== 200) return;

      // 检查HTTPS使用
      const httpsIssues = this.checkHttpsUsage(result);
      issues.push(...httpsIssues);

      // 检查混合内容
      const mixedContentIssues = this.checkMixedContent(result);
      issues.push(...mixedContentIssues);

      // 检查不安全的外部链接
      const unsafeLinksIssues = this.checkUnsafeExternalLinks(result);
      issues.push(...unsafeLinksIssues);

      // 检查CSP头
      const cspIssues = this.checkContentSecurityPolicy(result);
      issues.push(...cspIssues);

      // 检查安全头
      const securityHeadersIssues = this.checkSecurityHeaders(result);
      issues.push(...securityHeadersIssues);
    });

    return issues;
  }

  /**
   * 检查HTTPS使用
   */
  private checkHttpsUsage(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];

    if (result.url.startsWith('http://')) {
      issues.push({
        type: 'error',
        category: 'accessibility',
        url: result.url,
        message: 'Page not served over HTTPS',
        recommendation: 'Migrate to HTTPS for better security and SEO rankings',
        details: { 
          protocol: 'http',
          issue: 'no-https'
        }
      });
    }

    return issues;
  }

  /**
   * 检查混合内容
   */
  private checkMixedContent(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];

    if (result.url.startsWith('https://')) {
      const httpResources = this.findHttpResources(result.content!);
      
      if (httpResources.length > 0) {
        issues.push({
          type: 'error',
          category: 'accessibility',
          url: result.url,
          message: `Mixed content detected: ${httpResources.length} HTTP resources on HTTPS page`,
          recommendation: 'Update all resources to use HTTPS URLs to prevent security warnings',
          details: { 
            httpResources: httpResources.slice(0, 10), // 限制显示前10个
            totalCount: httpResources.length,
            issue: 'mixed-content'
          }
        });
      }
    }

    return issues;
  }

  /**
   * 检查不安全的外部链接
   */
  private checkUnsafeExternalLinks(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const unsafeLinks = this.getUnsafeExternalLinks(result.content!);

    if (unsafeLinks.length > 0) {
      issues.push({
        type: 'warning',
        category: 'accessibility',
        url: result.url,
        message: `Unsafe external links detected: ${unsafeLinks.length} links missing security attributes`,
        recommendation: 'Add rel="noopener noreferrer" to external links that open in new windows',
        details: { 
          unsafeLinks: unsafeLinks.slice(0, 5),
          totalCount: unsafeLinks.length,
          issue: 'unsafe-external-links'
        }
      });
    }

    return issues;
  }

  /**
   * 检查Content Security Policy
   */
  private checkContentSecurityPolicy(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];

    // 检查响应头中的CSP
    const cspHeader = result.headers && (
      result.headers['content-security-policy'] || 
      result.headers['x-content-security-policy']
    );

    // 检查meta标签中的CSP
    const cspMeta = result.content!.match(/<meta[^>]*http-equiv=["']content-security-policy["'][^>]*>/i);

    if (!cspHeader && !cspMeta) {
      issues.push({
        type: 'info',
        category: 'accessibility',
        url: result.url,
        message: 'No Content Security Policy found',
        recommendation: 'Consider implementing CSP to prevent XSS and other injection attacks',
        details: { 
          issue: 'no-csp',
          hasMetaCSP: !!cspMeta,
          hasHeaderCSP: !!cspHeader
        }
      });
    } else if (cspHeader) {
      // 验证CSP配置
      const cspIssues = this.validateCSPDirectives(cspHeader, result.url);
      issues.push(...cspIssues);
    }

    return issues;
  }

  /**
   * 检查安全响应头
   */
  private checkSecurityHeaders(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const headers = result.headers || {};

    // 检查关键安全头
    const securityHeaders = {
      'x-frame-options': 'X-Frame-Options header missing - page vulnerable to clickjacking',
      'x-content-type-options': 'X-Content-Type-Options header missing - MIME type sniffing possible',
      'x-xss-protection': 'X-XSS-Protection header missing - XSS protection not enabled',
      'strict-transport-security': 'HSTS header missing - not enforcing HTTPS',
      'referrer-policy': 'Referrer-Policy header missing - referrer information may leak'
    };

    Object.entries(securityHeaders).forEach(([header, message]) => {
      if (!headers[header] && !headers[header.toLowerCase()]) {
        issues.push({
          type: 'info',
          category: 'accessibility',
          url: result.url,
          message,
          recommendation: `Add ${header} header to improve security`,
          details: { 
            missingHeader: header,
            issue: 'missing-security-header'
          }
        });
      }
    });

    // 检查HSTS配置
    const hstsHeader = headers['strict-transport-security'];
    if (hstsHeader && result.url.startsWith('https://')) {
      const hstsIssues = this.validateHSTSHeader(hstsHeader, result.url);
      issues.push(...hstsIssues);
    }

    return issues;
  }

  /**
   * 查找HTTP资源
   */
  private findHttpResources(content: string): string[] {
    const httpResources: string[] = [];
    
    // 查找各种HTTP资源
    const patterns = [
      /src=["']http:\/\/[^"']+["']/gi,
      /href=["']http:\/\/[^"']+["']/gi,
      /url\(["']?http:\/\/[^"')]+["']?\)/gi,
      /action=["']http:\/\/[^"']+["']/gi
    ];

    patterns.forEach(pattern => {
      const matches = content.match(pattern) || [];
      matches.forEach(match => {
        const urlMatch = match.match(/http:\/\/[^"'\s)]+/);
        if (urlMatch && !urlMatch[0].includes('localhost')) {
          httpResources.push(urlMatch[0]);
        }
      });
    });

    return [...new Set(httpResources)]; // 去重
  }

  /**
   * 获取不安全的外部链接
   */
  private getUnsafeExternalLinks(content: string): string[] {
    const unsafeLinks: string[] = [];
    
    // 查找所有外部链接
    const linkMatches = content.match(/<a[^>]*href=["']https?:\/\/[^"']+["'][^>]*>/gi) || [];
    
    linkMatches.forEach(link => {
      // 检查是否有target="_blank"但缺少安全属性
      const hasTargetBlank = /target=["']_blank["']/i.test(link);
      const hasNoopener = /rel=["'][^"']*noopener[^"']*["']/i.test(link);
      const hasNoreferrer = /rel=["'][^"']*noreferrer[^"']*["']/i.test(link);
      
      if (hasTargetBlank && (!hasNoopener || !hasNoreferrer)) {
        const hrefMatch = link.match(/href=["']([^"']+)["']/i);
        if (hrefMatch) {
          unsafeLinks.push(hrefMatch[1]);
        }
      }
    });

    return unsafeLinks;
  }

  /**
   * 验证CSP指令
   */
  private validateCSPDirectives(csp: string, url: string): SeoIssue[] {
    const issues: SeoIssue[] = [];

    // 检查危险的CSP配置
    if (csp.includes("'unsafe-inline'")) {
      issues.push({
        type: 'warning',
        category: 'accessibility',
        url,
        message: "CSP contains 'unsafe-inline' directive",
        recommendation: "Remove 'unsafe-inline' and use nonces or hashes for better security",
        details: { 
          cspValue: csp,
          issue: 'csp-unsafe-inline'
        }
      });
    }

    if (csp.includes("'unsafe-eval'")) {
      issues.push({
        type: 'warning',
        category: 'accessibility',
        url,
        message: "CSP contains 'unsafe-eval' directive",
        recommendation: "Remove 'unsafe-eval' to prevent code injection attacks",
        details: { 
          cspValue: csp,
          issue: 'csp-unsafe-eval'
        }
      });
    }

    if (csp.includes('*') && !csp.includes('data:') && !csp.includes('blob:')) {
      issues.push({
        type: 'info',
        category: 'accessibility',
        url,
        message: "CSP uses wildcard (*) directive",
        recommendation: "Consider using more specific sources instead of wildcards",
        details: { 
          cspValue: csp,
          issue: 'csp-wildcard'
        }
      });
    }

    return issues;
  }

  /**
   * 验证HSTS头配置
   */
  private validateHSTSHeader(hsts: string, url: string): SeoIssue[] {
    const issues: SeoIssue[] = [];

    // 提取max-age值
    const maxAgeMatch = hsts.match(/max-age=(\d+)/i);
    if (maxAgeMatch) {
      const maxAge = parseInt(maxAgeMatch[1]);
      const oneYear = 31536000; // 365 * 24 * 60 * 60

      if (maxAge < oneYear) {
        issues.push({
          type: 'info',
          category: 'accessibility',
          url,
          message: `HSTS max-age is less than one year (${maxAge} seconds)`,
          recommendation: 'Consider setting HSTS max-age to at least one year (31536000 seconds)',
          details: { 
            currentMaxAge: maxAge,
            recommendedMaxAge: oneYear,
            issue: 'hsts-short-duration'
          }
        });
      }
    }

    // 检查includeSubDomains
    if (!hsts.includes('includeSubDomains')) {
      issues.push({
        type: 'info',
        category: 'accessibility',
        url,
        message: 'HSTS header missing includeSubDomains directive',
        recommendation: 'Add includeSubDomains to apply HSTS to all subdomains',
        details: { 
          hstsValue: hsts,
          issue: 'hsts-no-subdomains'
        }
      });
    }

    return issues;
  }

  /**
   * 检测可疑的外部资源
   */
  detectSuspiciousExternalResources(urlResults: UrlTestResult[]): SeoIssue[] {
    this.logger.debug('SECURITY', 'Checking for suspicious external resources');
    
    const issues: SeoIssue[] = [];
    const suspiciousDomains = new Set<string>();

    urlResults.forEach(result => {
      if (!result.content || result.statusCode !== 200) return;

      const externalResources = this.extractExternalResources(result.content);
      
      externalResources.forEach(resource => {
        try {
          const domain = new URL(resource).hostname;
          
          // 检查可疑的域名模式
          if (this.isSuspiciousDomain(domain)) {
            suspiciousDomains.add(domain);
            
            issues.push({
              type: 'warning',
              category: 'accessibility',
              url: result.url,
              message: `Potentially suspicious external resource: ${domain}`,
              recommendation: 'Verify this external resource is legitimate and necessary',
              details: { 
                resource,
                domain,
                issue: 'suspicious-external-resource'
              }
            });
          }
        } catch {
          // 忽略无效URL
        }
      });
    });

    return issues;
  }

  /**
   * 提取外部资源
   */
  private extractExternalResources(content: string): string[] {
    const resources: string[] = [];
    
    // 提取各种外部资源
    const patterns = [
      /src=["']https?:\/\/[^"']+["']/gi,
      /href=["']https?:\/\/[^"']+["']/gi,
      /url\(["']?https?:\/\/[^"')]+["']?\)/gi
    ];

    patterns.forEach(pattern => {
      const matches = content.match(pattern) || [];
      matches.forEach(match => {
        const urlMatch = match.match(/https?:\/\/[^"'\s)]+/);
        if (urlMatch) {
          resources.push(urlMatch[0]);
        }
      });
    });

    return [...new Set(resources)];
  }

  /**
   * 检查是否为可疑域名
   */
  private isSuspiciousDomain(domain: string): boolean {
    // 常见的恶意或可疑域名模式
    const suspiciousPatterns = [
      /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/, // IP地址
      /[a-z]{20,}\./, // 过长的随机字符串
      /\.tk$|\.ml$|\.ga$|\.cf$/, // 可疑的免费顶级域名
      /bit\.ly|tinyurl|goo\.gl/, // 短链接服务（可能不安全）
      /[0-9]+[a-z]+[0-9]+/, // 数字字母混合的可疑模式
    ];

    return suspiciousPatterns.some(pattern => pattern.test(domain));
  }
} 