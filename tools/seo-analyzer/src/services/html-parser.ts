import { J<PERSON><PERSON> } from 'jsdom';
import { URL } from 'url';

/**
 * Interface for extracted link information
 */
export interface ExtractedLink {
  /** The resolved absolute URL */
  url: string;
  /** The original href attribute value */
  originalHref: string;
  /** Link text content */
  text: string;
  /** Link type (anchor, canonical, etc.) */
  type: 'anchor' | 'canonical' | 'alternate' | 'stylesheet' | 'script';
  /** Additional attributes */
  attributes: Record<string, string>;
}

/**
 * HTML parser service for extracting links and metadata
 */
export class HtmlParser {
  /**
   * Extract all links from HTML content
   */
  extractLinks(html: string, baseUrl: string): ExtractedLink[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const links: ExtractedLink[] = [];
      const baseUrlObj = new URL(baseUrl);

      // Extract anchor links
      const anchorLinks = document.querySelectorAll('a[href]');
      anchorLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href) {
          const extractedLink = this.processLink(href, baseUrlObj, 'anchor', {
            text: link.textContent?.trim() || '',
            title: link.getAttribute('title') || '',
            rel: link.getAttribute('rel') || ''
          });
          if (extractedLink) {
            links.push(extractedLink);
          }
        }
      });

      // Extract canonical links
      const canonicalLinks = document.querySelectorAll('link[rel="canonical"][href]');
      canonicalLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href) {
          const extractedLink = this.processLink(href, baseUrlObj, 'canonical', {
            rel: 'canonical'
          });
          if (extractedLink) {
            links.push(extractedLink);
          }
        }
      });

      // Extract alternate links (hreflang)
      const alternateLinks = document.querySelectorAll('link[rel="alternate"][href]');
      alternateLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href) {
          const extractedLink = this.processLink(href, baseUrlObj, 'alternate', {
            rel: 'alternate',
            hreflang: link.getAttribute('hreflang') || '',
            type: link.getAttribute('type') || ''
          });
          if (extractedLink) {
            links.push(extractedLink);
          }
        }
      });

      return links;
    } catch (error) {
      console.warn(`Warning: Failed to parse HTML for ${baseUrl}:`, error instanceof Error ? error.message : 'Unknown error');
      return [];
    }
  }

  /**
   * Extract only internal links for crawling
   */
  extractInternalLinks(html: string, baseUrl: string): string[] {
    const allLinks = this.extractLinks(html, baseUrl);
    const baseUrlObj = new URL(baseUrl);
    
    return allLinks
      .filter(link => {
        try {
          const linkUrl = new URL(link.url);
          // Only include links from the same domain
          return linkUrl.hostname === baseUrlObj.hostname && 
                 link.type === 'anchor' && 
                 this.isValidCrawlableUrl(link.url);
        } catch {
          return false;
        }
      })
      .map(link => link.url);
  }

  /**
   * Check if JavaScript rendering might be needed
   */
  detectJavaScriptContent(html: string): boolean {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;

      // Check for common SPA indicators
      const indicators = [
        // React
        document.querySelector('[data-reactroot]'),
        document.querySelector('#root'),
        document.querySelector('#react-root'),
        
        // Vue
        document.querySelector('[data-v-]'),
        document.querySelector('#app'),
        
        // Angular
        document.querySelector('[ng-app]'),
        document.querySelector('[ng-version]'),
        document.querySelector('app-root'),
        
        // General indicators
        document.querySelector('script[src*="bundle"]'),
        document.querySelector('script[src*="app"]'),
        document.querySelector('script[src*="main"]'),
      ];

      // Check if there are script tags but very little content
      const scripts = document.querySelectorAll('script');
      const bodyText = document.body?.textContent?.trim() || '';
      const hasScripts = scripts.length > 2;
      const hasLittleContent = bodyText.length < 500;

      return indicators.some(indicator => indicator !== null) || 
             (hasScripts && hasLittleContent);
    } catch {
      return false;
    }
  }

  /**
   * Process and resolve a link
   */
  private processLink(
    href: string, 
    baseUrl: URL, 
    type: ExtractedLink['type'], 
    attributes: Record<string, string>
  ): ExtractedLink | null {
    try {
      // Skip invalid or unwanted links
      if (!href || 
          href.startsWith('#') || 
          href.startsWith('javascript:') || 
          href.startsWith('mailto:') || 
          href.startsWith('tel:') ||
          href.startsWith('data:')) {
        return null;
      }

      // Resolve relative URLs
      const resolvedUrl = new URL(href, baseUrl.toString());
      
      return {
        url: resolvedUrl.toString(),
        originalHref: href,
        text: attributes.text || '',
        type,
        attributes
      };
    } catch (error) {
      console.warn(`Warning: Failed to process link "${href}":`, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Check if URL is valid for crawling
   */
  private isValidCrawlableUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // Skip non-HTTP(S) protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // Skip common file extensions that aren't HTML
      const pathname = urlObj.pathname.toLowerCase();
      const skipExtensions = [
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
        '.zip', '.rar', '.tar', '.gz',
        '.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp',
        '.mp3', '.mp4', '.avi', '.mov', '.wmv',
        '.css', '.js', '.json', '.xml', '.txt'
      ];

      return !skipExtensions.some(ext => pathname.endsWith(ext));
    } catch {
      return false;
    }
  }
}