import axios, { AxiosResponse } from 'axios';
import { URL } from 'url';
import { HtmlParser, ExtractedLink } from './html-parser';
import { DiscoveredUrl } from '../interfaces/url-discovery';
import { RobotsParserImpl } from './robots-parser';
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

/**
 * Configuration for web crawler
 */
export interface CrawlerConfig {
  /** Maximum crawl depth */
  maxDepth: number;
  /** Maximum number of concurrent requests */
  maxConcurrency: number;
  /** Request timeout in milliseconds */
  requestTimeout: number;
  /** Delay between requests in milliseconds */
  crawlDelay: number;
  /** User agent string */
  userAgent: string;
  /** Custom headers */
  customHeaders: Record<string, string>;
  /** URL patterns to exclude */
  excludePatterns: string[];
  /** Enable JavaScript rendering */
  enableJsRendering: boolean;
  /** Wait time for JS rendering */
  jsRenderWaitTime: number;
}

/**
 * Interface for crawl queue item
 */
interface CrawlQueueItem {
  url: string;
  depth: number;
  parentUrl?: string;
}

/**
 * Interface for crawl result
 */
interface CrawlResult {
  url: string;
  depth: number;
  statusCode: number;
  contentType: string;
  links: string[];
  error?: string;
  needsJsRendering?: boolean;
}

/**
 * Web crawler service for discovering URLs through crawling
 */
export class WebCrawler {
  private htmlParser: HtmlParser;
  private robotsParser: RobotsParserImpl;
  private visitedUrls: Set<string>;
  private crawlQueue: CrawlQueueItem[];
  private activeCrawls: number;
  private results: Map<string, CrawlResult>;

  constructor(private config: CrawlerConfig) {
    this.htmlParser = new HtmlParser();
    this.robotsParser = new RobotsParserImpl();
    this.visitedUrls = new Set();
    this.crawlQueue = [];
    this.activeCrawls = 0;
    this.results = new Map();
  }

  /**
   * Crawl website starting from base URL
   */
  async crawl(baseUrl: string): Promise<DiscoveredUrl[]> {
    try {
      // Initialize robots.txt parser
      await this.robotsParser.fetchAndParseRobots(baseUrl);

      // Reset state
      this.visitedUrls.clear();
      this.crawlQueue = [];
      this.activeCrawls = 0;
      this.results.clear();

      // Add base URL to queue
      this.crawlQueue.push({ url: baseUrl, depth: 0 });

      // Process queue with breadth-first approach
      await this.processQueue();

      // Convert results to DiscoveredUrl format
      return this.convertResultsToDiscoveredUrls();
    } catch (error) {
      throw new Error(`Crawling failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process crawl queue with breadth-first approach
   */
  private async processQueue(): Promise<void> {
    while (this.crawlQueue.length > 0 || this.activeCrawls > 0) {
      // Start new crawls up to concurrency limit
      while (this.crawlQueue.length > 0 && 
             this.activeCrawls < this.config.maxConcurrency) {
        const item = this.crawlQueue.shift()!;
        
        // Skip if already visited or exceeds depth limit
        if (this.visitedUrls.has(item.url) || item.depth > this.config.maxDepth) {
          continue;
        }

        // Skip if excluded by patterns
        if (this.isExcludedByPatterns(item.url)) {
          continue;
        }

        // Skip if disallowed by robots.txt
        if (!this.robotsParser.isUrlAllowed(item.url, this.config.userAgent)) {
          continue;
        }

        this.visitedUrls.add(item.url);
        this.activeCrawls++;

        // Start crawling this URL
        this.crawlUrl(item).catch(error => {
          console.warn(`Warning: Failed to crawl ${item.url}:`, error instanceof Error ? error.message : 'Unknown error');
        }).finally(() => {
          this.activeCrawls--;
        });

        // Add delay between requests for politeness
        if (this.config.crawlDelay > 0) {
          await this.delay(this.config.crawlDelay);
        }
      }

      // Wait a bit before checking queue again
      await this.delay(100);
    }
  }

  /**
   * Crawl a single URL
   */
  private async crawlUrl(item: CrawlQueueItem): Promise<void> {
    try {
      const response = await this.fetchUrl(item.url);
      const contentType = response.headers['content-type'] || '';
      
      const result: CrawlResult = {
        url: item.url,
        depth: item.depth,
        statusCode: response.status,
        contentType,
        links: []
      };

      // Only process HTML content
      if (contentType.includes('text/html')) {
        const html = response.data;
        
        // Check if JavaScript rendering might be needed
        const needsJsRendering = this.htmlParser.detectJavaScriptContent(html);
        result.needsJsRendering = needsJsRendering;

        // Extract links from HTML
        const internalLinks = this.htmlParser.extractInternalLinks(html, item.url);
        result.links = internalLinks;

        // Add new links to queue for next depth level
        if (item.depth < this.config.maxDepth) {
          for (const link of internalLinks) {
            if (!this.visitedUrls.has(link)) {
              this.crawlQueue.push({
                url: link,
                depth: item.depth + 1,
                parentUrl: item.url
              });
            }
          }
        }

        // If JS rendering is enabled and needed, try to render
        if (this.config.enableJsRendering && needsJsRendering) {
          try {
            const renderedLinks = await this.renderWithJavaScript(item.url);
            // Merge with existing links, avoiding duplicates
            const allLinks = [...new Set([...result.links, ...renderedLinks])];
            result.links = allLinks;

            // Add newly discovered links to queue
            if (item.depth < this.config.maxDepth) {
              for (const link of renderedLinks) {
                if (!this.visitedUrls.has(link) && !result.links.includes(link)) {
                  this.crawlQueue.push({
                    url: link,
                    depth: item.depth + 1,
                    parentUrl: item.url
                  });
                }
              }
            }
          } catch (jsError) {
            console.warn(`Warning: JavaScript rendering failed for ${item.url}:`, jsError instanceof Error ? jsError.message : 'Unknown error');
          }
        }
      }

      this.results.set(item.url, result);
    } catch (error) {
      const result: CrawlResult = {
        url: item.url,
        depth: item.depth,
        statusCode: 0,
        contentType: '',
        links: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      this.results.set(item.url, result);
    }
  }

  /**
   * Fetch URL with proper error handling
   */
  private async fetchUrl(url: string): Promise<AxiosResponse> {
    const headers = {
      'User-Agent': this.config.userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      ...this.config.customHeaders
    };

    return await axios.get(url, {
      timeout: this.config.requestTimeout,
      maxRedirects: 5,
      headers,
      validateStatus: (status) => status < 500, // Accept 4xx errors for analysis
    });
  }

  /**
   * Render page with JavaScript using Puppeteer
   */
  private async renderWithJavaScript(url: string): Promise<string[]> {
    let browser: Browser | null = null;
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      const page: Page = await browser.newPage();
      await page.setUserAgent(this.config.userAgent);
      
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: this.config.requestTimeout,
      });

      await page.waitForTimeout(this.config.jsRenderWaitTime);

      const html = await page.content();
      return this.htmlParser.extractInternalLinks(html, url);
    } catch (error) {
      console.warn(`Warning: Puppeteer rendering failed for ${url}:`, error instanceof Error ? error.message : 'Unknown error');
      return [];
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Check if URL is excluded by patterns
   */
  private isExcludedByPatterns(url: string): boolean {
    return this.config.excludePatterns.some(pattern => {
      try {
        const regex = new RegExp(pattern);
        return regex.test(url);
      } catch {
        // If pattern is not a valid regex, treat as simple string match
        return url.includes(pattern);
      }
    });
  }

  /**
   * Convert crawl results to DiscoveredUrl format
   */
  private convertResultsToDiscoveredUrls(): DiscoveredUrl[] {
    const discoveredUrls: DiscoveredUrl[] = [];

    for (const [url, result] of this.results) {
      discoveredUrls.push({
        url,
        source: 'crawl',
        depth: result.depth
      });
    }

    return discoveredUrls;
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get crawl statistics
   */
  getCrawlStats() {
    const totalUrls = this.results.size;
    const successfulUrls = Array.from(this.results.values()).filter(r => r.statusCode >= 200 && r.statusCode < 400).length;
    const errorUrls = Array.from(this.results.values()).filter(r => r.error || r.statusCode >= 400).length;
    const jsRenderingNeeded = Array.from(this.results.values()).filter(r => r.needsJsRendering).length;

    return {
      totalUrls,
      successfulUrls,
      errorUrls,
      jsRenderingNeeded,
      visitedUrls: this.visitedUrls.size
    };
  }
}

// Example usage (for testing purposes)
async function main() {
  const config: CrawlerConfig = {
    maxDepth: 2,
    maxConcurrency: 5,
    requestTimeout: 10000,
    crawlDelay: 200,
    userAgent: 'SEOBot/1.0',
    customHeaders: {},
    excludePatterns: ['/login', '/register'],
    enableJsRendering: false,
    jsRenderWaitTime: 3000
  };

  const crawler = new WebCrawler(config);
  try {
    const discoveredUrls = await crawler.crawl('https://www.example.com');
    console.log('Discovered URLs:', discoveredUrls);
    console.log('Crawl Stats:', crawler.getCrawlStats());
  } catch (error) {
    console.error('Crawling failed:', error);
  }
}

// Uncomment to run example
// main();