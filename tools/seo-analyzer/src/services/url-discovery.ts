import axios from 'axios';
import { XMLParser } from 'fast-xml-parser';
import { URL } from 'url';
import { UrlDiscoveryService, DiscoveredUrl } from '../interfaces/url-discovery';
import { RobotsParserImpl } from './robots-parser';

/**
 * Implementation of URL discovery service with sitemap support
 */
export class UrlDiscoveryServiceImpl implements UrlDiscoveryService {
  private xmlParser: XMLParser;
  private robotsParser: RobotsParserImpl;

  constructor() {
    // Configure XML parser for sitemap parsing
    this.xmlParser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_',
      textNodeName: '#text',
      removeNSPrefix: false,
      parseTagValue: true,
      trimValues: true,
      isArray: (name, jpath, isLeafNode, isAttribute) => {
        // Always treat these as arrays for consistent processing
        return ['sitemap', 'url'].includes(name);
      }
    });

    this.robotsParser = new RobotsParserImpl();
  }

  /**
   * Discover URLs from a sitemap
   */
  async discoverFromSitemap(sitemapUrl: string): Promise<DiscoveredUrl[]> {
    try {
      console.log(`🔍 Fetching sitemap: ${sitemapUrl}`);
      const response = await this.fetchSitemap(sitemapUrl);
      
      // Log response details for debugging
      console.log(`📄 Sitemap response: ${response.status} ${response.statusText}`);
      console.log(`📊 Content length: ${response.data?.length || 0} characters`);
      console.log(`🔤 Content type: ${response.headers['content-type'] || 'unknown'}`);
      
      // Check if response is empty or not XML
      if (!response.data || typeof response.data !== 'string') {
        throw new Error(`Empty or invalid sitemap content`);
      }

      // Check if content looks like XML
      const trimmedContent = response.data.trim();
      if (!trimmedContent.startsWith('<?xml') && !trimmedContent.startsWith('<')) {
        throw new Error(`Content does not appear to be XML. Content preview: ${trimmedContent.substring(0, 200)}...`);
      }

      // Quick check for HTML content (common issue)
      if (trimmedContent.toLowerCase().includes('<!doctype html') || 
          trimmedContent.toLowerCase().includes('<html')) {
        throw new Error(`Sitemap URL returns HTML page instead of XML sitemap. This may indicate incorrect server configuration.`);
      }

      console.log(`🔧 Parsing XML content...`);
      const parsedXml = this.xmlParser.parse(response.data);
      
      // Log parsed XML structure for debugging
      console.log(`📋 Parsed XML root keys:`, Object.keys(parsedXml));
      
      // Check if this is a sitemap index or regular sitemap
      if (parsedXml.sitemapindex) {
        console.log(`📑 Processing sitemap index...`);
        return await this.processSitemapIndex(parsedXml.sitemapindex, sitemapUrl);
      } else if (parsedXml.urlset) {
        console.log(`📄 Processing URL set...`);
        return this.processUrlSet(parsedXml.urlset, sitemapUrl);
      } else {
        // Try to find alternative root elements
        const rootKeys = Object.keys(parsedXml);
        console.log(`❓ Unknown sitemap format. Available root elements:`, rootKeys);
        
        // Try to handle different namespaced formats
        const sitemapIndexKey = rootKeys.find(key => key.includes('sitemapindex'));
        const urlsetKey = rootKeys.find(key => key.includes('urlset'));
        
        if (sitemapIndexKey && parsedXml[sitemapIndexKey]) {
          console.log(`📑 Found namespaced sitemap index: ${sitemapIndexKey}`);
          return await this.processSitemapIndex(parsedXml[sitemapIndexKey], sitemapUrl);
        } else if (urlsetKey && parsedXml[urlsetKey]) {
          console.log(`📄 Found namespaced URL set: ${urlsetKey}`);
          return this.processUrlSet(parsedXml[urlsetKey], sitemapUrl);
        }
        
        // If still no valid format found, show more details
        const xmlPreview = response.data.substring(0, 500).replace(/\s+/g, ' ');
        throw new Error(`Invalid sitemap format. Expected 'sitemapindex' or 'urlset' root element. Found: ${rootKeys.join(', ')}. XML preview: ${xmlPreview}...`);
      }
    } catch (error) {
      console.error(`❌ Sitemap parsing error for ${sitemapUrl}:`, error);
      
      if (error instanceof Error) {
        // Don't double-wrap the error message
        if (error.message.includes('Failed to parse sitemap')) {
          throw error;
        }
        throw new Error(`Failed to parse sitemap ${sitemapUrl}: ${error.message}`);
      }
      throw new Error(`Failed to parse sitemap ${sitemapUrl}: Unknown error`);
    }
  }

  /**
   * Discover URLs by crawling the website
   */
  async discoverByCrawling(baseUrl: string, maxDepth: number): Promise<DiscoveredUrl[]> {
    const { WebCrawler } = await import('./web-crawler');
    
    const crawlerConfig = {
      maxDepth,
      maxConcurrency: 5, // Conservative default for crawling
      requestTimeout: 30000,
      crawlDelay: 1000, // 1 second delay between requests for politeness
      userAgent: 'SEO-Analyzer/1.0',
      customHeaders: {},
      excludePatterns: [
        // Common patterns to exclude
        '\\.pdf$', '\\.doc$', '\\.docx$', '\\.xls$', '\\.xlsx$',
        '\\.jpg$', '\\.jpeg$', '\\.png$', '\\.gif$', '\\.svg$',
        '\\.css$', '\\.js$', '\\.json$', '\\.xml$',
        '/admin/', '/wp-admin/', '/wp-content/',
        '\\?.*download', '\\?.*print'
      ],
      enableJsRendering: false, // Disabled by default
      jsRenderWaitTime: 5000
    };

    const crawler = new WebCrawler(crawlerConfig);
    return await crawler.crawl(baseUrl);
  }

  /**
   * Find sitemap URLs from robots.txt and common locations
   */
  async findSitemaps(baseUrl: string): Promise<string[]> {
    const sitemapUrls: string[] = [];
    const baseUrlObj = new URL(baseUrl);
    const baseOrigin = `${baseUrlObj.protocol}//${baseUrlObj.host}`;

    try {
      // First, try to get sitemaps from robots.txt
      const robotsRules = await this.robotsParser.fetchAndParseRobots(baseUrl);
      const robotsSitemaps = this.robotsParser.getSitemapsFromRobots();
      sitemapUrls.push(...robotsSitemaps);

      // Try common sitemap locations
      const commonSitemapPaths = [
        '/sitemap.xml',
        '/sitemap_index.xml',
        '/sitemaps.xml',
        '/sitemap/sitemap.xml',
        '/wp-sitemap.xml', // WordPress
        '/sitemap-index.xml'
      ];

      for (const path of commonSitemapPaths) {
        const sitemapUrl = `${baseOrigin}${path}`;
        
        // Skip if already found in robots.txt
        if (sitemapUrls.includes(sitemapUrl)) {
          continue;
        }

        try {
          const response = await axios.head(sitemapUrl, {
            timeout: 10000,
            headers: {
              'User-Agent': 'SEO-Analyzer/1.0'
            }
          });

          if (response.status === 200) {
            const contentType = response.headers['content-type'] || '';
            if (contentType.includes('xml') || contentType.includes('text')) {
              sitemapUrls.push(sitemapUrl);
            }
          }
        } catch {
          // Ignore errors for common location checks
        }
      }
    } catch (error) {
      console.warn(`Warning: Failed to discover sitemaps for ${baseUrl}:`, error instanceof Error ? error.message : 'Unknown error');
    }

    // Remove duplicates and return
    return [...new Set(sitemapUrls)];
  }

  /**
   * Parse sitemap index files and return child sitemap URLs
   */
  async parseSitemapIndex(sitemapIndexUrl: string): Promise<string[]> {
    try {
      const response = await this.fetchSitemap(sitemapIndexUrl);
      const parsedXml = this.xmlParser.parse(response.data);

      if (!parsedXml.sitemapindex) {
        throw new Error('Not a valid sitemap index file');
      }

      return this.extractSitemapUrls(parsedXml.sitemapindex);
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to parse sitemap index ${sitemapIndexUrl}: ${error.message}`);
      }
      throw new Error(`Failed to parse sitemap index ${sitemapIndexUrl}: Unknown error`);
    }
  }

  /**
   * Fetch sitemap with proper error handling and streaming support
   */
  private async fetchSitemap(sitemapUrl: string) {
    try {
      const response = await axios.get(sitemapUrl, {
        timeout: 30000, // 30 second timeout for large sitemaps
        maxContentLength: 50 * 1024 * 1024, // 50MB max size
        headers: {
          'User-Agent': 'SEO-Analyzer/1.0',
          'Accept': 'application/xml, text/xml, */*',
          'Accept-Encoding': 'gzip, deflate'
        },
        responseType: 'text'
      });

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error('Request timeout - sitemap too large or server too slow');
        } else if (error.response) {
          throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
        } else if (error.request) {
          throw new Error('Network error - unable to reach sitemap URL');
        }
      }
      throw error;
    }
  }

  /**
   * Process sitemap index and recursively fetch all child sitemaps
   */
  private async processSitemapIndex(sitemapIndex: any, parentUrl: string): Promise<DiscoveredUrl[]> {
    const childSitemapUrls = this.extractSitemapUrls(sitemapIndex);
    const allUrls: DiscoveredUrl[] = [];

    // Process each child sitemap
    for (const childSitemapUrl of childSitemapUrls) {
      try {
        const childUrls = await this.discoverFromSitemap(childSitemapUrl);
        allUrls.push(...childUrls);
      } catch (error) {
        console.warn(`Warning: Failed to process child sitemap ${childSitemapUrl}:`, error instanceof Error ? error.message : 'Unknown error');
      }
    }

    return allUrls;
  }

  /**
   * Extract sitemap URLs from sitemap index
   */
  private extractSitemapUrls(sitemapIndex: any): string[] {
    const sitemapUrls: string[] = [];

    if (sitemapIndex.sitemap) {
      const sitemaps = Array.isArray(sitemapIndex.sitemap) ? sitemapIndex.sitemap : [sitemapIndex.sitemap];
      
      for (const sitemap of sitemaps) {
        if (sitemap.loc) {
          const loc = Array.isArray(sitemap.loc) ? sitemap.loc[0] : sitemap.loc;
          if (typeof loc === 'string') {
            sitemapUrls.push(loc);
          } else if (loc && loc['#text']) {
            sitemapUrls.push(loc['#text']);
          }
        }
      }
    }

    return sitemapUrls;
  }

  /**
   * Process URL set from regular sitemap
   */
  private processUrlSet(urlset: any, sitemapUrl: string): DiscoveredUrl[] {
    const discoveredUrls: DiscoveredUrl[] = [];

    if (urlset.url) {
      const urls = Array.isArray(urlset.url) ? urlset.url : [urlset.url];

      for (const urlEntry of urls) {
        try {
          const discoveredUrl = this.parseUrlEntry(urlEntry, sitemapUrl);
          if (discoveredUrl) {
            discoveredUrls.push(discoveredUrl);
          }
        } catch (error) {
          console.warn(`Warning: Failed to parse URL entry in sitemap ${sitemapUrl}:`, error instanceof Error ? error.message : 'Unknown error');
        }
      }
    }

    return discoveredUrls;
  }

  /**
   * Parse individual URL entry from sitemap
   */
  private parseUrlEntry(urlEntry: any, sitemapUrl: string): DiscoveredUrl | null {
    // Extract URL
    let url: string | null = null;
    if (urlEntry.loc) {
      if (typeof urlEntry.loc === 'string') {
        url = urlEntry.loc;
      } else if (Array.isArray(urlEntry.loc) && urlEntry.loc.length > 0) {
        url = urlEntry.loc[0];
      } else if (urlEntry.loc['#text']) {
        url = urlEntry.loc['#text'];
      }
    }

    if (!url || typeof url !== 'string') {
      console.warn(`Warning: Invalid URL entry in sitemap ${sitemapUrl}`);
      return null;
    }

    // Validate URL
    try {
      new URL(url);
    } catch {
      console.warn(`Warning: Invalid URL format in sitemap ${sitemapUrl}: ${url}`);
      return null;
    }

    // Parse optional fields
    const discoveredUrl: DiscoveredUrl = {
      url,
      source: 'sitemap',
      depth: 0
    };

    // Parse lastmod
    if (urlEntry.lastmod) {
      const lastmod = this.extractTextValue(urlEntry.lastmod);
      if (lastmod) {
        const date = new Date(lastmod);
        if (!isNaN(date.getTime())) {
          discoveredUrl.lastModified = date;
        }
      }
    }

    // Parse changefreq
    if (urlEntry.changefreq) {
      const changefreq = this.extractTextValue(urlEntry.changefreq);
      if (changefreq && this.isValidChangeFreq(changefreq)) {
        discoveredUrl.changeFreq = changefreq as DiscoveredUrl['changeFreq'];
      }
    }

    // Parse priority
    if (urlEntry.priority) {
      const priority = this.extractTextValue(urlEntry.priority);
      if (priority) {
        const priorityNum = parseFloat(priority);
        if (!isNaN(priorityNum) && priorityNum >= 0 && priorityNum <= 1) {
          discoveredUrl.priority = priorityNum;
        }
      }
    }

    return discoveredUrl;
  }

  /**
   * Extract text value from XML element
   */
  private extractTextValue(element: any): string | null {
    if (typeof element === 'string') {
      return element;
    } else if (Array.isArray(element) && element.length > 0) {
      return typeof element[0] === 'string' ? element[0] : element[0]['#text'] || null;
    } else if (element && element['#text']) {
      return element['#text'];
    }
    return null;
  }

  /**
   * Validate change frequency value
   */
  private isValidChangeFreq(changefreq: string): boolean {
    const validValues = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
    return validValues.includes(changefreq.toLowerCase());
  }
}