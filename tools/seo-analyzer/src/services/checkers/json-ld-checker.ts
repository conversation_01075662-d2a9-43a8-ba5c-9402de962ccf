import { SeoIssue } from '../../interfaces/seo-analyzer';

/**
 * JSON-LD 结构化数据检查器
 */
export class JsonLdChecker {
  /**
   * 检查 JSON-LD 结构化数据的合规性
   */
  validateJsonLdCompliance(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // 使用正则表达式提取 JSON-LD 脚本，避免依赖 DOM 解析器
      const jsonLdMatches = content.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis);
      
      if (!jsonLdMatches || jsonLdMatches.length === 0) {
        issues.push({
          type: 'info',
          category: 'structure',
          url,
          message: 'No JSON-LD structured data found',
          recommendation: 'Consider adding JSON-LD structured data to help search engines understand your content better'
        });
        return issues;
      }

      jsonLdMatches.forEach((match, index) => {
        try {
          // 提取 JSON 内容
          const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\/script>/i, '').trim();
          
          if (!jsonContent) {
            issues.push({
              type: 'warning',
              category: 'structure',
              url,
              message: `Empty JSON-LD script ${index + 1}`,
              recommendation: 'Remove empty JSON-LD scripts or add proper structured data'
            });
            return;
          }

          const jsonData = JSON.parse(jsonContent);
          
          // 验证 JSON-LD 基本结构
          this.validateJsonLdStructure(jsonData, url, issues, index + 1);
          
          // 验证 schema.org 类型
          this.validateSchemaOrgTypes(jsonData, url, issues, index + 1);
          
        } catch (parseError) {
          issues.push({
            type: 'error',
            category: 'structure',
            url,
            message: `Invalid JSON-LD syntax in script ${index + 1}: ${parseError instanceof Error ? parseError.message : 'Parse error'}`,
            recommendation: 'Fix JSON syntax errors in structured data. Use JSON validator to check syntax.',
            details: { scriptIndex: index + 1, error: parseError instanceof Error ? parseError.message : 'Unknown error' }
          });
        }
      });

      // 检查重复的结构化数据
      if (jsonLdMatches.length > 3) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url,
          message: `Many JSON-LD scripts found (${jsonLdMatches.length})`,
          recommendation: 'Consider consolidating structured data into fewer, more comprehensive scripts'
        });
      }

    } catch (error) {
      issues.push({
        type: 'error',
        category: 'structure',
        url,
        message: `JSON-LD validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Check page structure and ensure valid HTML with proper JSON-LD scripts'
      });
    }

    return issues;
  }

  /**
   * 验证 JSON-LD 基本结构
   */
  private validateJsonLdStructure(jsonData: any, url: string, issues: SeoIssue[], scriptIndex: number): void {
    // 检查 @context
    if (!jsonData['@context']) {
      issues.push({
        type: 'error',
        category: 'structure',
        url,
        message: `JSON-LD script ${scriptIndex} missing @context`,
        recommendation: 'Add @context property, typically "https://schema.org"',
        details: { scriptIndex }
      });
    } else if (typeof jsonData['@context'] === 'string' && !jsonData['@context'].includes('schema.org')) {
      issues.push({
        type: 'warning',
        category: 'structure',
        url,
        message: `JSON-LD script ${scriptIndex} uses non-standard @context`,
        recommendation: 'Consider using "https://schema.org" as the primary context',
        details: { scriptIndex, context: jsonData['@context'] }
      });
    }

    // 检查 @type
    if (!jsonData['@type']) {
      issues.push({
        type: 'error',
        category: 'structure',
        url,
        message: `JSON-LD script ${scriptIndex} missing @type`,
        recommendation: 'Add @type property to specify the schema type (e.g., "Organization", "Article", "Product")',
        details: { scriptIndex }
      });
    }

    // 检查数组格式的 JSON-LD
    if (Array.isArray(jsonData)) {
      jsonData.forEach((item, itemIndex) => {
        this.validateJsonLdStructure(item, url, issues, scriptIndex);
      });
    }
  }

  /**
   * 验证 Schema.org 类型的常见属性
   */
  private validateSchemaOrgTypes(jsonData: any, url: string, issues: SeoIssue[], scriptIndex: number): void {
    if (Array.isArray(jsonData)) {
      jsonData.forEach(item => this.validateSchemaOrgTypes(item, url, issues, scriptIndex));
      return;
    }

    const type = jsonData['@type'];
    if (!type) return;

    // 根据类型检查必需属性
    const typeValidations: Record<string, string[]> = {
      'Organization': ['name', 'url'],
      'Person': ['name'],
      'Article': ['headline', 'author', 'datePublished'],
      'BlogPosting': ['headline', 'author', 'datePublished'],
      'NewsArticle': ['headline', 'author', 'datePublished'],
      'Product': ['name', 'description'],
      'Offer': ['price', 'priceCurrency'],
      'LocalBusiness': ['name', 'address'],
      'Restaurant': ['name', 'address', 'telephone'],
      'Event': ['name', 'startDate'],
      'Recipe': ['name', 'recipeIngredient', 'recipeInstructions'],
      'FAQ': ['mainEntity'],
      'HowTo': ['name', 'step'],
      'JobPosting': ['title', 'description', 'hiringOrganization']
    };

    const requiredProps = typeValidations[type];
    if (requiredProps) {
      requiredProps.forEach(prop => {
        if (!jsonData[prop]) {
          issues.push({
            type: 'warning',
            category: 'structure',
            url,
            message: `JSON-LD ${type} missing recommended property "${prop}"`,
            recommendation: `Add "${prop}" property to improve structured data completeness`,
            details: { scriptIndex, type, missingProperty: prop }
          });
        }
      });
    }

    // 检查图片 URL 的有效性
    if (jsonData.image) {
      const images = Array.isArray(jsonData.image) ? jsonData.image : [jsonData.image];
      images.forEach((image: any, imageIndex: number) => {
        const imageUrl = typeof image === 'string' ? image : image.url;
        if (imageUrl && !this.isValidUrl(imageUrl)) {
          issues.push({
            type: 'warning',
            category: 'structure',
            url,
            message: `Invalid image URL in JSON-LD ${type}`,
            recommendation: 'Ensure image URLs are absolute and properly formatted',
            details: { scriptIndex, imageIndex, imageUrl }
          });
        }
      });
    }
  }

  /**
   * 验证 URL 格式
   */
  private isValidUrl(urlString: string): boolean {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  }
} 