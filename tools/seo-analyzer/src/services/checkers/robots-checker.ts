import { SeoIssue } from '../../interfaces/seo-analyzer';

/**
 * Robots.txt 检查器
 */
export class RobotsChecker {
  /**
   * 检查 robots.txt 的合规性
   */
  validateRobotsCompliance(robotsUrl: string, robotsContent: string): SeoIssue[] {
    const issues: SeoIssue[] = [];

    try {
      const lines = robotsContent.split('\n').map(line => line.trim());
      let currentUserAgent: string | null = null;
      const userAgents = new Set<string>();
      const sitemaps = new Set<string>();
      
      lines.forEach((line, lineNumber) => {
        if (!line || line.startsWith('#')) return; // 跳过空行和注释

        const [directive, ...valueParts] = line.split(':');
        const value = valueParts.join(':').trim();

        if (!directive || !value) {
          issues.push({
            type: 'warning',
            category: 'robots',
            url: robotsUrl,
            message: `Invalid syntax at line ${lineNumber + 1}: "${line}"`,
            recommendation: 'Use format "directive: value" with proper spacing',
            details: { line: lineNumber + 1, content: line }
          });
          return;
        }

        const directiveLower = directive.toLowerCase().trim();
        const validationResult = this.validateDirective(directiveLower, value, robotsUrl, lineNumber, currentUserAgent);
        
        if (validationResult.issues) {
          issues.push(...validationResult.issues);
        }
        
        if (validationResult.userAgent) {
          currentUserAgent = validationResult.userAgent;
          userAgents.add(validationResult.userAgent);
        }
        
        if (validationResult.sitemap) {
          sitemaps.add(validationResult.sitemap);
        }
      });

      // 添加全局检查
      const globalIssues = this.performGlobalValidation(userAgents, sitemaps, robotsUrl);
      issues.push(...globalIssues);

    } catch (error) {
      issues.push({
        type: 'error',
        category: 'robots',
        url: robotsUrl,
        message: `Robots.txt validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Check robots.txt format and fix any syntax errors'
      });
    }

    return issues;
  }

  /**
   * 验证单个指令
   */
  private validateDirective(
    directive: string, 
    value: string, 
    robotsUrl: string, 
    lineNumber: number, 
    currentUserAgent: string | null
  ): {
    issues?: SeoIssue[];
    userAgent?: string;
    sitemap?: string;
  } {
    const issues: SeoIssue[] = [];
    let userAgent: string | undefined;
    let sitemap: string | undefined;

    switch (directive) {
      case 'user-agent':
        userAgent = value;
        // 检查重复 user-agent 的逻辑在外层处理
        break;

      case 'disallow':
      case 'allow':
        if (!currentUserAgent) {
          issues.push({
            type: 'error',
            category: 'robots',
            url: robotsUrl,
            message: `${directive} directive without user-agent at line ${lineNumber + 1}`,
            recommendation: 'Add user-agent directive before allow/disallow rules',
            details: { line: lineNumber + 1, directive }
          });
        }
        
        // 检查路径格式
        if (value && !value.startsWith('/') && value !== '*') {
          issues.push({
            type: 'warning',
            category: 'robots',
            url: robotsUrl,
            message: `${directive} path should start with "/" at line ${lineNumber + 1}`,
            recommendation: 'Use absolute paths starting with "/" for allow/disallow rules',
            details: { line: lineNumber + 1, path: value }
          });
        }
        break;

      case 'sitemap':
        if (!this.isValidUrl(value)) {
          issues.push({
            type: 'error',
            category: 'robots',
            url: robotsUrl,
            message: `Invalid sitemap URL at line ${lineNumber + 1}: "${value}"`,
            recommendation: 'Use absolute URLs for sitemap directives',
            details: { line: lineNumber + 1, sitemapUrl: value }
          });
        } else {
          sitemap = value;
        }
        break;

      case 'crawl-delay':
        const delay = parseInt(value);
        if (isNaN(delay) || delay < 0) {
          issues.push({
            type: 'warning',
            category: 'robots',
            url: robotsUrl,
            message: `Invalid crawl-delay value at line ${lineNumber + 1}: "${value}"`,
            recommendation: 'Use positive integer values for crawl-delay (seconds)',
            details: { line: lineNumber + 1, value }
          });
        } else if (delay > 86400) { // 24 hours
          issues.push({
            type: 'warning',
            category: 'robots',
            url: robotsUrl,
            message: `Very high crawl-delay (${delay}s) at line ${lineNumber + 1}`,
            recommendation: 'Consider if such a high delay is necessary',
            details: { line: lineNumber + 1, delay }
          });
        }
        break;

      default:
        const unknownDirectiveIssues = this.handleUnknownDirective(directive, robotsUrl, lineNumber);
        issues.push(...unknownDirectiveIssues);
    }

    return { issues: issues.length > 0 ? issues : undefined, userAgent, sitemap };
  }

  /**
   * 处理未知指令
   */
  private handleUnknownDirective(directive: string, robotsUrl: string, lineNumber: number): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    // 检查常见的拼写错误
    const suggestions: Record<string, string> = {
      'useragent': 'user-agent',
      'user_agent': 'user-agent',
      'disalow': 'disallow',
      'dissallow': 'disallow',
      'crawler-delay': 'crawl-delay',
      'crawldelay': 'crawl-delay'
    };
    
    if (suggestions[directive.toLowerCase()]) {
      issues.push({
        type: 'warning',
        category: 'robots',
        url: robotsUrl,
        message: `Unknown directive "${directive}" at line ${lineNumber + 1}, did you mean "${suggestions[directive.toLowerCase()]}"?`,
        recommendation: `Use "${suggestions[directive.toLowerCase()]}" instead of "${directive}"`,
        details: { line: lineNumber + 1, directive, suggestion: suggestions[directive.toLowerCase()] }
      });
    } else {
      issues.push({
        type: 'info',
        category: 'robots',
        url: robotsUrl,
        message: `Unknown directive "${directive}" at line ${lineNumber + 1}`,
        recommendation: 'Verify directive spelling and check robots.txt specification',
        details: { line: lineNumber + 1, directive }
      });
    }

    return issues;
  }

  /**
   * 执行全局验证
   */
  private performGlobalValidation(
    userAgents: Set<string>, 
    sitemaps: Set<string>, 
    robotsUrl: string
  ): SeoIssue[] {
    const issues: SeoIssue[] = [];

    // 检查是否有通配符用户代理
    if (!userAgents.has('*')) {
      issues.push({
        type: 'info',
        category: 'robots',
        url: robotsUrl,
        message: 'No wildcard (*) user-agent found',
        recommendation: 'Consider adding "User-agent: *" rules for general crawlers'
      });
    }

    // 检查是否有 sitemap 声明
    if (sitemaps.size === 0) {
      issues.push({
        type: 'info',
        category: 'robots',
        url: robotsUrl,
        message: 'No sitemap URLs declared in robots.txt',
        recommendation: 'Consider adding sitemap URLs to help crawlers discover your content'
      });
    }

    return issues;
  }

  /**
   * 验证 URL 格式
   */
  private isValidUrl(urlString: string): boolean {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  }
} 