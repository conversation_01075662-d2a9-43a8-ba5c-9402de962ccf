import { SeoIssue } from '../../interfaces/seo-analyzer';

/**
 * Sitemap.xml 检查器
 */
export class SitemapChecker {
  /**
   * 检查 sitemap.xml 的合规性
   */
  async validateSitemapCompliance(sitemapUrl: string, sitemapContent: string): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];

    try {
      // 基本 XML 格式验证
      if (!sitemapContent.includes('<?xml')) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url: sitemapUrl,
          message: 'Sitemap missing XML declaration',
          recommendation: 'Add XML declaration: <?xml version="1.0" encoding="UTF-8"?>'
        });
      }

      // 检查 sitemap 命名空间
      if (!sitemapContent.includes('http://www.sitemaps.org/schemas/sitemap')) {
        issues.push({
          type: 'error',
          category: 'structure',
          url: sitemapUrl,
          message: 'Invalid or missing sitemap namespace',
          recommendation: 'Use proper sitemap namespace: http://www.sitemaps.org/schemas/sitemap/0.9'
        });
      }

      // 检查 URL 条目数量 (sitemap 限制为 50,000 个 URL)
      const urlMatches = sitemapContent.match(/<url>/g);
      if (urlMatches && urlMatches.length > 50000) {
        issues.push({
          type: 'error',
          category: 'structure',
          url: sitemapUrl,
          message: `Sitemap contains ${urlMatches.length} URLs (exceeds 50,000 limit)`,
          recommendation: 'Split large sitemaps into multiple files and use a sitemap index'
        });
      }

      // 检查必需的 loc 元素和内容类型验证
      const locMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
      if (!locMatches || locMatches.length === 0) {
        issues.push({
          type: 'error',
          category: 'structure',
          url: sitemapUrl,
          message: 'Sitemap contains no URL locations',
          recommendation: 'Add <loc> elements with valid URLs'
        });
      } else {
        const urlValidationResults = this.validateSitemapUrls(locMatches, sitemapUrl);
        issues.push(...urlValidationResults);
      }

      // 检查日期格式
      const dateValidationResults = this.validateDateFormats(sitemapContent, sitemapUrl);
      issues.push(...dateValidationResults);

      // 检查优先级值
      const priorityValidationResults = this.validatePriorityValues(sitemapContent, sitemapUrl);
      issues.push(...priorityValidationResults);

      // 检查变更频率
      const changefreqValidationResults = this.validateChangeFreqValues(sitemapContent, sitemapUrl);
      issues.push(...changefreqValidationResults);

    } catch (error) {
      issues.push({
        type: 'error',
        category: 'structure',
        url: sitemapUrl,
        message: `Sitemap validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Check sitemap XML structure and fix syntax errors'
      });
    }

    return issues;
  }

  /**
   * 验证 sitemap 中的 URL
   */
  private validateSitemapUrls(locMatches: string[], sitemapUrl: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    let invalidUrls = 0;
    let fileUrls = 0;
    
    const fileExtensions = [
      '.png', '.jpg', '.jpeg', '.gif', '.svg', '.pdf', '.txt', '.xml', 
      '.css', '.js', '.ico', '.zip', '.rar', '.doc', '.docx', '.xls', 
      '.xlsx', '.mp4', '.mp3', '.avi', '.mov'
    ];
    
    locMatches.forEach(locMatch => {
      const urlText = locMatch.replace(/<\/?loc>/g, '');
      
      if (!this.isValidUrl(urlText)) {
        invalidUrls++;
      } else {
        // 检查是否为文件类型 URL
        const urlPath = new URL(urlText).pathname.toLowerCase();
        const hasFileExtension = fileExtensions.some(ext => urlPath.endsWith(ext));
        
        // 特殊检查常见文件名
        const isCommonFile = urlPath.match(/\/(robots\.txt|sitemap\.xml|favicon\.ico|og\.png|logo\.(png|jpg|jpeg|svg)|apple-touch-icon.*\.png)$/i);
        
        if (hasFileExtension || isCommonFile) {
          fileUrls++;
        }
      }
    });

    if (invalidUrls > 0) {
      issues.push({
        type: 'warning',
        category: 'structure',
        url: sitemapUrl,
        message: `${invalidUrls} invalid URLs found in sitemap`,
        recommendation: 'Ensure all URLs are absolute and properly formatted'
      });
    }

    if (fileUrls > 0) {
      issues.push({
        type: 'error',
        category: 'structure',
        url: sitemapUrl,
        message: `${fileUrls} file URLs found in sitemap (should only contain web pages)`,
        recommendation: 'Remove file URLs like images, documents, robots.txt, etc. Sitemaps should only contain web page URLs'
      });
    }

    return issues;
  }

  /**
   * 验证日期格式
   */
  private validateDateFormats(sitemapContent: string, sitemapUrl: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const lastmodMatches = sitemapContent.match(/<lastmod>(.*?)<\/lastmod>/g);
    
    if (lastmodMatches) {
      let invalidDates = 0;
      lastmodMatches.forEach(dateMatch => {
        const dateText = dateMatch.replace(/<\/?lastmod>/g, '');
        if (!/^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}([+-]\d{2}:\d{2}|Z))?$/.test(dateText)) {
          invalidDates++;
        }
      });

      if (invalidDates > 0) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url: sitemapUrl,
          message: `${invalidDates} invalid date formats in sitemap`,
          recommendation: 'Use ISO 8601 date format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS+00:00)'
        });
      }
    }

    return issues;
  }

  /**
   * 验证优先级值
   */
  private validatePriorityValues(sitemapContent: string, sitemapUrl: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const priorityMatches = sitemapContent.match(/<priority>(.*?)<\/priority>/g);
    
    if (priorityMatches) {
      let invalidPriorities = 0;
      priorityMatches.forEach(priorityMatch => {
        const priorityText = priorityMatch.replace(/<\/?priority>/g, '');
        const priority = parseFloat(priorityText);
        if (isNaN(priority) || priority < 0 || priority > 1) {
          invalidPriorities++;
        }
      });

      if (invalidPriorities > 0) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url: sitemapUrl,
          message: `${invalidPriorities} invalid priority values in sitemap`,
          recommendation: 'Priority values must be between 0.0 and 1.0'
        });
      }
    }

    return issues;
  }

  /**
   * 验证变更频率值
   */
  private validateChangeFreqValues(sitemapContent: string, sitemapUrl: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const changefreqMatches = sitemapContent.match(/<changefreq>(.*?)<\/changefreq>/g);
    
    if (changefreqMatches) {
      const validFreqs = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
      let invalidFreqs = 0;
      
      changefreqMatches.forEach(freqMatch => {
        const freqText = freqMatch.replace(/<\/?changefreq>/g, '');
        if (!validFreqs.includes(freqText)) {
          invalidFreqs++;
        }
      });

      if (invalidFreqs > 0) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url: sitemapUrl,
          message: `${invalidFreqs} invalid changefreq values in sitemap`,
          recommendation: 'Use valid changefreq values: always, hourly, daily, weekly, monthly, yearly, never'
        });
      }
    }

    return issues;
  }

  /**
   * 验证 URL 格式
   */
  private isValidUrl(urlString: string): boolean {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  }
} 