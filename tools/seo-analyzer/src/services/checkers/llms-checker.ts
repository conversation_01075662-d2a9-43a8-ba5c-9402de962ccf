import { SeoIssue } from '../../interfaces/seo-analyzer';
import { AnalyzerConfig } from '../../interfaces/config';
import { createOpenAI } from '@ai-sdk/openai';
import { generateText } from 'ai';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * LLMS.txt 检查器 - 使用 AI 进行格式验证
 */
export class LlmsChecker {
  private config: AnalyzerConfig;

  constructor(config: AnalyzerConfig) {
    this.config = config;
  }

  /**
   * 检查 llms.txt 的合规性 (AI 爬虫指令文件)
   */
  async validateLlmsTxtCompliance(baseUrl: string): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];
    const llmsUrl = `${baseUrl}/llms.txt`;

    try {
      // 尝试获取 llms.txt 文件
      const response = await fetch(llmsUrl, {
        method: 'HEAD',
        headers: { 'User-Agent': this.config.userAgent }
      });

      if (response.status === 404) {
        issues.push({
          type: 'info',
          category: 'robots',
          url: llmsUrl,
          message: 'llms.txt file not found',
          recommendation: 'Consider adding llms.txt to control AI/LLM crawling behavior. This is a new standard for AI crawler control.'
        });
        return issues;
      }

      if (response.status !== 200) {
        issues.push({
          type: 'warning',
          category: 'robots',
          url: llmsUrl,
          message: `llms.txt returns HTTP ${response.status}`,
          recommendation: 'Ensure llms.txt is accessible and returns 200 OK status'
        });
        return issues;
      }

      // 获取文件内容进行验证
      const fullResponse = await fetch(llmsUrl, {
        headers: { 'User-Agent': this.config.userAgent }
      });
      
      const content = await fullResponse.text();
      
      // 使用 AI 验证 llms.txt 内容格式
      const llmsIssues = await this.validateLlmsTxtContentWithAI(llmsUrl, content);
      issues.push(...llmsIssues);

      // 成功找到 llms.txt
      issues.push({
        type: 'info',
        category: 'robots',
        url: llmsUrl,
        message: 'llms.txt file found and accessible',
        recommendation: 'Good! Having llms.txt helps control AI crawler behavior'
      });

    } catch (error) {
      issues.push({
        type: 'info',
        category: 'robots',
        url: llmsUrl,
        message: 'Could not check llms.txt availability',
        recommendation: 'llms.txt is a new standard for AI crawler control. Consider adding one.',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    }

    return issues;
  }

  /**
   * 使用 AI SDK 验证 llms.txt 内容格式
   */
  private async validateLlmsTxtContentWithAI(llmsUrl: string, content: string): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];
    
    try {
      // 读取 llms_check.txt prompt
      const promptPath = path.join(__dirname, '../../prompts/llms_check.txt');
      const promptContent = await fs.readFile(promptPath, 'utf-8');
      
      // 配置 AI 模型
      const model = this.createAIModel();
      
      // 使用 AI 检查 llms.txt
      const result = await generateText({
        model,
        prompt: `${promptContent}\n\n以下是需要检查的 llms.txt 内容：\n\n${content}`,
        maxTokens: 2000,
        temperature: 0.1,
      });

      // 解析 AI 返回的结果
      const aiResponse = result.text;
      
      // 解析 AI 检查结果
      const parsedIssues = this.parseAIResponse(aiResponse, llmsUrl);
      issues.push(...parsedIssues);
      
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'llms',
        url: llmsUrl,
        message: `AI validation of llms.txt failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Check AI service configuration and try again',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    }

    return issues;
  }

  /**
   * 创建 AI 模型实例
   */
  private createAIModel() {
    const apiKey = process.env.AI_API_KEY;
    const baseURL = process.env.AI_API_BASE_URL || 'https://api.deepseek.com/v1';
    const modelName = process.env.AI_MODEL_NAME || 'deepseek-r1-distill-qwen-32b';

    if (!apiKey) {
      throw new Error('AI_API_KEY environment variable is required');
    }

    // 创建 OpenAI 兼容的提供者
    const provider = createOpenAI({
      apiKey,
      baseURL,
    });

    return provider(modelName);
  }

  /**
   * 解析 AI 响应结果
   */
  private parseAIResponse(aiResponse: string, llmsUrl: string): SeoIssue[] {
    const issues: SeoIssue[] = [];

    // 检查是否存在问题
    if (aiResponse.includes('文件存在') && aiResponse.includes('处问题')) {
      // 提取问题数量
      const problemMatch = aiResponse.match(/文件存在\s*(\d+)\s*处问题/);
      const problemCount = problemMatch ? parseInt(problemMatch[1]) : 1;
      
      issues.push({
        type: 'warning',
        category: 'llms',
        url: llmsUrl,
        message: `AI validation found ${problemCount} issues with llms.txt format`,
        recommendation: 'Fix llms.txt format according to AI validation report',
        details: { 
          aiValidationReport: aiResponse,
          problemCount 
        }
      });
    } else if (aiResponse.includes('❌')) {
      // 检查是否有不符合的规则
      const failedRules = aiResponse.match(/❌ 不符合/g);
      if (failedRules && failedRules.length > 0) {
        issues.push({
          type: 'warning',
          category: 'llms',
          url: llmsUrl,
          message: `llms.txt format validation failed (${failedRules.length} rules violated)`,
          recommendation: 'Fix llms.txt format according to validation rules',
          details: { 
            aiValidationReport: aiResponse,
            failedRuleCount: failedRules.length 
          }
        });
      }
    }
    
    // 如果没有问题，记录成功
    if (issues.length === 0 && aiResponse.includes('文件整体符合规范')) {
      issues.push({
        type: 'info',
        category: 'llms',
        url: llmsUrl,
        message: 'llms.txt format validation passed',
        recommendation: 'llms.txt format is compliant with standards',
        details: { aiValidationReport: aiResponse }
      });
    }

    return issues;
  }
} 