import { SeoIssue } from '../interfaces/seo-analyzer';
import { UrlTestResult } from '../interfaces/url-testing';
import { Logger } from './logger';

/**
 * International SEO Checker
 * 专门处理国际化和多语言SEO问题
 */
export class InternationalSeoChecker {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * 检测国际化SEO问题
   */
  detectInternationalIssues(urlResults: UrlTestResult[]): SeoIssue[] {
    this.logger.debug('INTERNATIONAL_SEO', 'Checking international SEO issues');
    
    const issues: SeoIssue[] = [];

    urlResults.forEach(result => {
      if (!result.content || result.statusCode !== 200) return;

      // 检查hreflang标签
      const hreflangIssues = this.validateHreflangTags(result);
      issues.push(...hreflangIssues);

      // 检查lang属性
      const langIssues = this.validateLangAttribute(result);
      issues.push(...langIssues);

      // 检测语言切换器
      const languageSwitcherIssues = this.checkLanguageSwitcher(result);
      issues.push(...languageSwitcherIssues);
    });

    return issues;
  }

  /**
   * 验证 hreflang 标签
   */
  private validateHreflangTags(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const hreflangTags = this.extractHreflangTags(result.content!);

    if (hreflangTags.length > 0) {
      // 验证hreflang语法
      hreflangTags.forEach(tag => {
        if (!this.isValidHreflang(tag.hreflang)) {
          issues.push({
            type: 'error',
            category: 'i18n',
            url: result.url,
            message: `Invalid hreflang value: ${tag.hreflang}`,
            recommendation: 'Use valid language-country codes (e.g., en-US, fr-FR)',
            details: { 
              invalidHreflang: tag.hreflang,
              href: tag.href 
            }
          });
        }

        // 检查hreflang URL是否可访问
        if (!this.isValidUrl(tag.href)) {
          issues.push({
            type: 'error',
            category: 'i18n',
            url: result.url,
            message: `Invalid hreflang URL: ${tag.href}`,
            recommendation: 'Ensure hreflang URLs are valid and accessible',
            details: { 
              hreflang: tag.hreflang,
              invalidUrl: tag.href 
            }
          });
        }
      });

      // 检查自引用hreflang
      const selfReferencing = hreflangTags.some(tag => 
        this.normalizeUrl(tag.href) === this.normalizeUrl(result.url)
      );
      
      if (!selfReferencing) {
        issues.push({
          type: 'warning',
          category: 'i18n',
          url: result.url,
          message: 'Missing self-referencing hreflang tag',
          recommendation: 'Include hreflang tag pointing to the current page',
          details: { 
            currentUrl: result.url,
            hreflangTags: hreflangTags.length
          }
        });
      }

      // 检查x-default hreflang
      const hasXDefault = hreflangTags.some(tag => tag.hreflang === 'x-default');
      if (hreflangTags.length > 2 && !hasXDefault) {
        issues.push({
          type: 'info',
          category: 'i18n',
          url: result.url,
          message: 'Consider adding x-default hreflang for fallback',
          recommendation: 'Add hreflang="x-default" to specify default language version',
          details: { 
            hreflangCount: hreflangTags.length
          }
        });
      }

      // 检查重复的hreflang值
      const hreflangValues = hreflangTags.map(tag => tag.hreflang);
      const duplicates = hreflangValues.filter((val, index) => hreflangValues.indexOf(val) !== index);
      
      if (duplicates.length > 0) {
        issues.push({
          type: 'error',
          category: 'i18n',
          url: result.url,
          message: `Duplicate hreflang values found: ${[...new Set(duplicates)].join(', ')}`,
          recommendation: 'Remove duplicate hreflang declarations',
          details: { 
            duplicateValues: [...new Set(duplicates)]
          }
        });
      }
    }

    return issues;
  }

  /**
   * 验证 lang 属性
   */
  private validateLangAttribute(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const htmlLang = result.content!.match(/<html[^>]*lang=["']([^"']+)["']/i);
    
    if (!htmlLang) {
      issues.push({
        type: 'warning',
        category: 'i18n',
        url: result.url,
        message: 'Missing lang attribute on html element',
        recommendation: 'Add lang attribute to help search engines understand page language',
        details: { issue: 'no-lang-attribute' }
      });
    } else {
      const langValue = htmlLang[1];
      
      // 验证lang属性格式
      if (!this.isValidLanguageCode(langValue)) {
        issues.push({
          type: 'warning',
          category: 'i18n',
          url: result.url,
          message: `Invalid lang attribute format: ${langValue}`,
          recommendation: 'Use valid language codes (e.g., en, zh-CN, fr-FR)',
          details: { 
            currentLang: langValue,
            issue: 'invalid-lang-format'
          }
        });
      }

      // 检查lang与hreflang一致性
      const hreflangTags = this.extractHreflangTags(result.content!);
      const selfHreflang = hreflangTags.find(tag => 
        this.normalizeUrl(tag.href) === this.normalizeUrl(result.url)
      );
      
      if (selfHreflang && selfHreflang.hreflang !== langValue && selfHreflang.hreflang !== 'x-default') {
        issues.push({
          type: 'warning',
          category: 'i18n',
          url: result.url,
          message: `Lang attribute (${langValue}) doesn't match self-referencing hreflang (${selfHreflang.hreflang})`,
          recommendation: 'Ensure lang attribute matches the hreflang value for current page',
          details: { 
            langAttribute: langValue,
            hreflangValue: selfHreflang.hreflang
          }
        });
      }
    }

    return issues;
  }

  /**
   * 检查语言切换器
   */
  private checkLanguageSwitcher(result: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];
    const content = result.content!;

    // 检测语言切换器标识
    const hasLanguageSwitcher = this.hasLanguageSwitcherIndicators(content);
    const hreflangTags = this.extractHreflangTags(content);

    if (hreflangTags.length > 1 && !hasLanguageSwitcher) {
      issues.push({
        type: 'info',
        category: 'i18n',
        url: result.url,
        message: 'Multiple language versions detected but no visible language switcher',
        recommendation: 'Consider adding a user-friendly language switcher for better UX',
        details: { 
          hreflangCount: hreflangTags.length,
          issue: 'no-language-switcher'
        }
      });
    }

    return issues;
  }

  /**
   * 提取 hreflang 标签
   */
  private extractHreflangTags(content: string): Array<{hreflang: string, href: string}> {
    const matches = content.match(/<link[^>]*rel=["']alternate["'][^>]*hreflang=["']([^"']+)["'][^>]*href=["']([^"']+)["']/gi) || [];
    return matches.map(match => {
      const hreflangMatch = match.match(/hreflang=["']([^"']+)["']/i);
      const hrefMatch = match.match(/href=["']([^"']+)["']/i);
      return {
        hreflang: hreflangMatch ? hreflangMatch[1] : '',
        href: hrefMatch ? hrefMatch[1] : ''
      };
    });
  }

  /**
   * 验证 hreflang 格式
   */
  private isValidHreflang(hreflang: string): boolean {
    // 支持的格式：
    // - 语言代码：en, zh, fr
    // - 语言-地区代码：en-US, zh-CN, fr-FR
    // - x-default
    return /^([a-z]{2}(-[A-Z]{2})?|x-default)$/i.test(hreflang);
  }

  /**
   * 验证语言代码格式
   */
  private isValidLanguageCode(langCode: string): boolean {
    // 支持 ISO 639-1 语言代码和可选的 ISO 3166-1 alpha-2 国家代码
    return /^[a-z]{2}(-[A-Z]{2})?$/i.test(langCode);
  }

  /**
   * 检测语言切换器标识
   */
  private hasLanguageSwitcherIndicators(content: string): boolean {
    const indicators = [
      /language.?select/i,
      /lang.?switch/i,
      /choose.?language/i,
      /select.?language/i,
      /language.?menu/i,
      /language.?dropdown/i,
      /country.?select/i,
      /region.?select/i
    ];

    return indicators.some(pattern => pattern.test(content));
  }

  /**
   * 规范化 URL
   */
  private normalizeUrl(url: string): string {
    try {
      const parsed = new URL(url);
      return `${parsed.protocol}//${parsed.host}${parsed.pathname}`.replace(/\/$/, '');
    } catch {
      return url;
    }
  }

  /**
   * 验证 URL 格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 检测多语言站点模式
   */
  detectMultilingualPatterns(urlResults: UrlTestResult[]): SeoIssue[] {
    this.logger.debug('INTERNATIONAL_SEO', 'Analyzing multilingual site patterns');
    
    const issues: SeoIssue[] = [];
    const patterns = this.analyzeUrlPatterns(urlResults);

    if (patterns.hasSubdomainPattern && patterns.hasPathPattern) {
      issues.push({
        type: 'warning',
        category: 'i18n',
        url: 'N/A',
        message: 'Mixed language URL patterns detected (subdomain + path)',
        recommendation: 'Use consistent URL structure for all language versions',
        details: {
          subdomainLanguages: patterns.subdomainLanguages,
          pathLanguages: patterns.pathLanguages,
          issue: 'mixed-url-patterns'
        }
      });
    }

    if (patterns.languages.length > 1 && !patterns.hasHreflang) {
      issues.push({
        type: 'error',
        category: 'i18n',
        url: 'N/A',
        message: 'Multilingual site detected but missing hreflang implementation',
        recommendation: 'Implement hreflang tags to indicate language relationships',
        details: {
          detectedLanguages: patterns.languages,
          issue: 'missing-hreflang'
        }
      });
    }

    return issues;
  }

  /**
   * 分析URL语言模式
   */
  private analyzeUrlPatterns(urlResults: UrlTestResult[]): {
    hasSubdomainPattern: boolean;
    hasPathPattern: boolean;
    hasHreflang: boolean;
    languages: string[];
    subdomainLanguages: string[];
    pathLanguages: string[];
  } {
    const subdomainLanguages = new Set<string>();
    const pathLanguages = new Set<string>();
    let hasHreflang = false;

    urlResults.forEach(result => {
      try {
        const url = new URL(result.url);
        
        // 检测子域名语言模式 (en.example.com, fr.example.com)
        const subdomainMatch = url.hostname.match(/^([a-z]{2})\.(.+)$/);
        if (subdomainMatch) {
          subdomainLanguages.add(subdomainMatch[1]);
        }

        // 检测路径语言模式 (/en/, /fr/, /zh-cn/)
        const pathMatch = url.pathname.match(/^\/([a-z]{2}(?:-[a-z]{2})?)\//i);
        if (pathMatch) {
          pathLanguages.add(pathMatch[1].toLowerCase());
        }

        // 检测hreflang标签
        if (result.content && result.content.includes('hreflang=')) {
          hasHreflang = true;
        }
      } catch {
        // 忽略无效URL
      }
    });

    const allLanguages = [...subdomainLanguages, ...pathLanguages];

    return {
      hasSubdomainPattern: subdomainLanguages.size > 1,
      hasPathPattern: pathLanguages.size > 1,
      hasHreflang,
      languages: [...new Set(allLanguages)],
      subdomainLanguages: [...subdomainLanguages],
      pathLanguages: [...pathLanguages]
    };
  }
} 