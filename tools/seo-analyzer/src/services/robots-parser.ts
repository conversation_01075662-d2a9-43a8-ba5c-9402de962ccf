import axios from 'axios';
import * as robotsParser from 'robots-parser';
import { URL } from 'url';
import { RobotsParser, RobotsRule } from '../interfaces/robots';

/**
 * Implementation of robots.txt parser and validator
 */
export class RobotsParserImpl implements RobotsParser {
  private robotsRules: RobotsRule[] = [];
  private sitemapUrls: string[] = [];
  private robotsContent?: string;

  /**
   * Parse robots.txt content into structured rules
   */
  parseRobots(robotsContent: string): RobotsRule[] {
    const rules: RobotsRule[] = [];
    const sitemaps: string[] = [];
    
    // Split content into lines and normalize
    const lines = robotsContent
      .split(/\r?\n/)
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));

    let currentRule: Partial<RobotsRule> | null = null;

    for (const line of lines) {
      const [directive, ...valueParts] = line.split(':');
      const value = valueParts.join(':').trim();
      
      if (!directive || !value) continue;

      const normalizedDirective = directive.toLowerCase().trim();

      switch (normalizedDirective) {
        case 'user-agent':
          // Save previous rule if exists
          if (currentRule && currentRule.userAgent) {
            rules.push(this.finalizeRule(currentRule));
          }
          // Start new rule
          currentRule = {
            userAgent: value,
            disallowed: [],
            allowed: [],
            sitemaps: []
          };
          break;

        case 'disallow':
          if (currentRule) {
            if (value) {
              currentRule.disallowed = currentRule.disallowed || [];
              currentRule.disallowed.push(value);
            }
          }
          break;

        case 'allow':
          if (currentRule) {
            if (value) {
              currentRule.allowed = currentRule.allowed || [];
              currentRule.allowed.push(value);
            }
          }
          break;

        case 'crawl-delay':
          if (currentRule) {
            const delay = parseInt(value, 10);
            if (!isNaN(delay) && delay >= 0) {
              currentRule.crawlDelay = delay;
            }
          }
          break;

        case 'sitemap':
          if (this.isValidUrl(value)) {
            sitemaps.push(value);
          }
          break;
      }
    }

    // Add final rule
    if (currentRule && currentRule.userAgent) {
      rules.push(this.finalizeRule(currentRule));
    }

    // Add sitemaps to all rules
    rules.forEach(rule => {
      rule.sitemaps = [...sitemaps];
    });

    this.robotsRules = rules;
    this.sitemapUrls = sitemaps;

    return rules;
  }

  /**
   * Check if a URL is allowed for a specific user agent
   */
  isUrlAllowed(url: string, userAgent: string = '*'): boolean {
    if (!this.robotsRules.length) {
      return true; // No robots.txt means everything is allowed
    }

    // Find applicable rules for the user agent
    const applicableRules = this.findApplicableRules(userAgent);
    
    if (!applicableRules.length) {
      return true; // No specific rules found, allow by default
    }

    // Parse URL to get pathname
    let pathname: string;
    try {
      const parsedUrl = new URL(url);
      pathname = parsedUrl.pathname;
    } catch {
      // If URL parsing fails, assume it's a path
      pathname = url.startsWith('/') ? url : '/' + url;
    }

    // Check each applicable rule
    for (const rule of applicableRules) {
      // Check explicit allows first (they take precedence)
      for (const allowPattern of rule.allowed) {
        if (this.matchesPattern(pathname, allowPattern)) {
          return true;
        }
      }

      // Check disallows
      for (const disallowPattern of rule.disallowed) {
        if (this.matchesPattern(pathname, disallowPattern)) {
          return false;
        }
      }
    }

    return true; // Default to allow if no matching rules
  }

  /**
   * Get sitemap URLs from robots.txt
   */
  getSitemapsFromRobots(): string[] {
    return [...this.sitemapUrls];
  }

  /**
   * Fetch and parse robots.txt from a domain
   */
  async fetchAndParseRobots(baseUrl: string): Promise<RobotsRule[]> {
    try {
      const robotsUrl = this.buildRobotsUrl(baseUrl);
      
      const response = await axios.get(robotsUrl, {
        timeout: 10000,
        headers: {
          'User-Agent': 'SEO-Analyzer/1.0'
        },
        validateStatus: (status) => status < 500 // Accept 404 as valid response
      });

      if (response.status === 404) {
        // No robots.txt found, return empty rules
        this.robotsContent = '';
        return [];
      }

      // Store raw content for GSC analysis
      this.robotsContent = response.data;
      return this.parseRobots(response.data);
    } catch (error) {
      // Network error or other issues - assume no restrictions
      console.warn(`Failed to fetch robots.txt from ${baseUrl}:`, error instanceof Error ? error.message : 'Unknown error');
      return [];
    }
  }

  /**
   * Validate robots.txt syntax and return list of issues
   */
  validateRobotsSyntax(robotsContent: string): string[] {
    const issues: string[] = [];
    const lines = robotsContent.split(/\r?\n/);
    
    let hasUserAgent = false;
    let currentUserAgent: string | null = null;
    let lineNumber = 0;

    for (const line of lines) {
      lineNumber++;
      const trimmedLine = line.trim();
      
      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue;
      }

      // Check if line contains colon
      if (!trimmedLine.includes(':')) {
        issues.push(`Line ${lineNumber}: Invalid syntax - missing colon separator`);
        continue;
      }

      const [directive, ...valueParts] = trimmedLine.split(':');
      const value = valueParts.join(':').trim();
      const normalizedDirective = directive.toLowerCase().trim();

      // Validate directive
      const validDirectives = ['user-agent', 'disallow', 'allow', 'crawl-delay', 'sitemap'];
      if (!validDirectives.includes(normalizedDirective)) {
        issues.push(`Line ${lineNumber}: Unknown directive '${directive}'`);
        continue;
      }

      // Check for user-agent
      if (normalizedDirective === 'user-agent') {
        hasUserAgent = true;
        currentUserAgent = value;
        if (!value) {
          issues.push(`Line ${lineNumber}: User-agent directive missing value`);
        }
      } else if (normalizedDirective === 'sitemap') {
        // Validate sitemap URL
        if (!this.isValidUrl(value)) {
          issues.push(`Line ${lineNumber}: Invalid sitemap URL '${value}'`);
        }
      } else if (normalizedDirective === 'crawl-delay') {
        // Validate crawl-delay value
        const delay = parseInt(value, 10);
        if (isNaN(delay) || delay < 0) {
          issues.push(`Line ${lineNumber}: Invalid crawl-delay value '${value}' - must be a non-negative integer`);
        }
      } else {
        // For disallow/allow, check if we have a current user-agent
        if (!currentUserAgent) {
          issues.push(`Line ${lineNumber}: ${directive} directive found without preceding User-agent`);
        }
      }
    }

    if (!hasUserAgent && robotsContent.trim()) {
      issues.push('No User-agent directive found in robots.txt');
    }

    return issues;
  }

  /**
   * Build robots.txt URL from base URL
   */
  private buildRobotsUrl(baseUrl: string): string {
    try {
      const url = new URL(baseUrl);
      return `${url.protocol}//${url.host}/robots.txt`;
    } catch {
      // Fallback for invalid URLs
      const cleanUrl = baseUrl.replace(/\/+$/, '');
      return `${cleanUrl}/robots.txt`;
    }
  }

  /**
   * Finalize a rule by ensuring all required fields are present
   */
  private finalizeRule(rule: Partial<RobotsRule>): RobotsRule {
    return {
      userAgent: rule.userAgent || '*',
      disallowed: rule.disallowed || [],
      allowed: rule.allowed || [],
      crawlDelay: rule.crawlDelay,
      sitemaps: rule.sitemaps || []
    };
  }

  /**
   * Find rules applicable to a user agent
   */
  private findApplicableRules(userAgent: string): RobotsRule[] {
    const rules: RobotsRule[] = [];
    
    // First, look for exact matches
    const exactMatch = this.robotsRules.find(rule => 
      rule.userAgent.toLowerCase() === userAgent.toLowerCase()
    );
    if (exactMatch) {
      rules.push(exactMatch);
    }

    // Then, look for wildcard matches
    const wildcardMatch = this.robotsRules.find(rule => rule.userAgent === '*');
    if (wildcardMatch && !exactMatch) {
      rules.push(wildcardMatch);
    }

    return rules;
  }

  /**
   * Check if a path matches a robots.txt pattern
   */
  private matchesPattern(path: string, pattern: string): boolean {
    // Handle empty pattern
    if (!pattern) {
      return false;
    }

    // Exact match
    if (pattern === path) {
      return true;
    }

    // Pattern ending with * matches prefix
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1);
      return path.startsWith(prefix);
    }

    // Pattern ending with $ matches exact path
    if (pattern.endsWith('$')) {
      const exactPattern = pattern.slice(0, -1);
      return path === exactPattern;
    }

    // Default prefix matching
    return path.startsWith(pattern);
  }

  /**
   * Validate if a string is a valid URL
   */
  private isValidUrl(urlString: string): boolean {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get the raw robots.txt content (for GSC analysis)
   */
  getRobotsContent(): string | undefined {
    return this.robotsContent;
  }

  /**
   * Get discovered sitemap URLs
   */
  getSitemapUrls(): string[] {
    return [...this.sitemapUrls];
  }

  /**
   * Get parsed robots rules
   */
  getRobotsRules(): RobotsRule[] {
    return [...this.robotsRules];
  }
}