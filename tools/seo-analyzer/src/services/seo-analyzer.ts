import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SeoIssue } from '../interfaces/seo-analyzer';
import { UrlTestResult } from '../interfaces/url-testing';
import { RobotsParser } from '../interfaces/robots';
import { AnalyzerConfig } from '../interfaces/config';
import { HtmlParser } from './html-parser';

/**
 * Implementation of SEO analyzer service
 */
export class SeoAnalyzerImpl implements SeoAnalyzer {
  private robotsParser: RobotsParser;
  private config: AnalyzerConfig;
  private htmlParser: HtmlParser;

  constructor(robotsParser: RobotsParser, config: AnalyzerConfig) {
    this.robotsParser = robotsParser;
    this.config = config;
    this.htmlParser = new HtmlParser();
  }

  /**
   * Comprehensive SEO analysis of a URL
   */
  async analyzeUrl(url: string, content: string, testResult: UrlTestResult): Promise<SeoIssue[]> {
    const issues: SeoIssue[] = [];
    
    try {
      // Performance analysis
      issues.push(...this.analyzePerformance(url, testResult));
      
      // Page structure analysis
      issues.push(...this.analyzePageStructure(url, content));
      
      // Meta tags analysis
      issues.push(...this.analyzeMetaTags(url, content));
      
      // Keywords analysis
      issues.push(...this.analyzeKeywords(url, content));
      
      // Hreflang validation
      issues.push(...this.validateHreflang(url, content));
      
      // Canonical URL checking
      issues.push(...this.checkCanonicals(url, content));
      
      // Structured data validation
      issues.push(...this.validateStructuredData(url, content));
      
      // Robots.txt compliance
      issues.push(...this.checkRobotsCompliance(url));
      
      // Image alt attribute analysis
      issues.push(...this.analyzeImageAltAttributes(url, content));
      
      // Accessibility checks
      issues.push(...this.checkAccessibility(url, content));
      
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'structure',
        url,
        message: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Check if the page content is valid HTML and accessible'
      });
    }
    
    return issues;
  }

  /**
   * Analyze performance metrics
   */
  private analyzePerformance(url: string, testResult: UrlTestResult): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    // Check load time
    if (testResult.loadTime > this.config.slowPageThreshold) {
      issues.push({
        type: 'warning',
        category: 'performance',
        url,
        message: `Page load time is ${testResult.loadTime}ms, exceeding threshold of ${this.config.slowPageThreshold}ms`,
        recommendation: 'Optimize images, minify resources, enable compression, or use a CDN',
        details: { loadTime: testResult.loadTime, threshold: this.config.slowPageThreshold }
      });
    }
    
    // Check HTTP status codes
    if (testResult.statusCode >= 400) {
      const severity = testResult.statusCode >= 500 ? 'error' : 'warning';
      issues.push({
        type: severity,
        category: 'accessibility',
        url,
        message: `Page returns HTTP ${testResult.statusCode} status`,
        recommendation: testResult.statusCode === 404 ? 'Fix broken link or implement proper redirect' : 'Fix server error or implement error handling',
        details: { statusCode: testResult.statusCode }
      });
    }
    
    // Check response size
    const sizeMB = testResult.responseSize / (1024 * 1024);
    if (sizeMB > 2) {
      issues.push({
        type: 'warning',
        category: 'performance',
        url,
        message: `Page size is ${sizeMB.toFixed(2)}MB, which is quite large`,
        recommendation: 'Optimize images, reduce content size, or implement lazy loading',
        details: { sizeBytes: testResult.responseSize, sizeMB }
      });
    }
    
    return issues;
  }

  /**
   * Analyze page structure and HTML elements
   */
  analyzePageStructure(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      
      // Check for title tag
      const titles = parsed.querySelectorAll('title');
      if (titles.length === 0) {
        issues.push({
          type: 'error',
          category: 'structure',
          url,
          message: 'Missing title tag',
          recommendation: 'Add a unique, descriptive title tag to every page'
        });
      } else if (titles.length > 1) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url,
          message: 'Multiple title tags found',
          recommendation: 'Use only one title tag per page'
        });
      } else {
        const title = titles[0].textContent?.trim();
        if (!title) {
          issues.push({
            type: 'warning',
            category: 'structure',
            url,
            message: 'Empty title tag',
            recommendation: 'Add descriptive content to the title tag'
          });
        } else if (title.length > 60) {
          issues.push({
            type: 'warning',
            category: 'structure',
            url,
            message: `Title tag is ${title.length} characters (exceeds 60)`,
            recommendation: 'Keep title tags under 60 characters for optimal display in search results',
            details: { title, length: title.length }
          });
        }
      }
      
      // Check for meta description
      const metaDescription = parsed.querySelector('meta[name="description"]');
      if (!metaDescription) {
        issues.push({
          type: 'warning',
          category: 'meta',
          url,
          message: 'Missing meta description',
          recommendation: 'Add a unique meta description for each page (150-160 characters)'
        });
      } else {
        const content = metaDescription.getAttribute('content')?.trim();
        if (!content) {
          issues.push({
            type: 'warning',
            category: 'meta',
            url,
            message: 'Empty meta description',
            recommendation: 'Add descriptive content to the meta description'
          });
        } else if (content.length > 160) {
          issues.push({
            type: 'info',
            category: 'meta',
            url,
            message: `Meta description is ${content.length} characters (exceeds 160)`,
            recommendation: 'Keep meta descriptions under 160 characters for optimal display',
            details: { description: content, length: content.length }
          });
        }
      }
      
      // Enhanced heading structure analysis
      const headings = parsed.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const h1s = parsed.querySelectorAll('h1');
      const h2s = parsed.querySelectorAll('h2');
      const h3s = parsed.querySelectorAll('h3');
      const h4s = parsed.querySelectorAll('h4');
      const h5s = parsed.querySelectorAll('h5');
      const h6s = parsed.querySelectorAll('h6');
      
      // H1 tag validation
      if (h1s.length === 0) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url,
          message: 'Missing H1 tag',
          recommendation: 'Add one H1 tag per page for better SEO structure'
        });
      } else if (h1s.length > 1) {
        issues.push({
          type: 'warning',
          category: 'structure',
          url,
          message: 'Multiple H1 tags found',
          recommendation: 'Use only one H1 tag per page and use H2-H6 for subheadings'
        });
      } else {
        // Check H1 content
        const h1Content = h1s[0].textContent?.trim();
        if (!h1Content) {
          issues.push({
            type: 'warning',
            category: 'structure',
            url,
            message: 'Empty H1 tag',
            recommendation: 'H1 tag should contain descriptive text about the page content'
          });
        } else if (h1Content.length > 70) {
          issues.push({
            type: 'info',
            category: 'structure',
            url,
            message: `H1 tag is ${h1Content.length} characters (recommended: under 70)`,
            recommendation: 'Keep H1 tags concise and under 70 characters for better readability',
            details: { h1Content, length: h1Content.length }
          });
        }
      }
      
      // Check for empty headings
      headings.forEach((heading, index) => {
        const content = heading.textContent?.trim();
        if (!content) {
          issues.push({
            type: 'warning',
            category: 'structure',
            url,
            message: `Empty ${heading.tagName} tag found`,
            recommendation: `Add descriptive content to the ${heading.tagName} tag`,
            details: { headingIndex: index, tagName: heading.tagName }
          });
        }
      });
      
      // Check heading length recommendations
      headings.forEach((heading, index) => {
        const content = heading.textContent?.trim();
        const tagName = heading.tagName;
        let maxLength = 70; // Default
        
        // Set recommended lengths for different heading levels
        switch (tagName) {
          case 'H1': maxLength = 70; break;
          case 'H2': maxLength = 60; break;
          case 'H3': maxLength = 50; break;
          case 'H4': 
          case 'H5': 
          case 'H6': maxLength = 40; break;
        }
        
        if (content && content.length > maxLength) {
          issues.push({
            type: 'info',
            category: 'structure',
            url,
            message: `${tagName} tag is ${content.length} characters (recommended: under ${maxLength})`,
            recommendation: `Keep ${tagName} tags concise for better readability`,
            details: { 
              tagName, 
              content, 
              length: content.length, 
              recommendedLength: maxLength,
              headingIndex: index 
            }
          });
        }
      });
      
      // Check for proper heading hierarchy
      if (headings.length > 0) {
        const headingLevels: number[] = [];
        const headingContents: string[] = [];
        
        headings.forEach(heading => {
          const level = parseInt(heading.tagName[1]);
          const content = heading.textContent?.trim() || '';
          headingLevels.push(level);
          headingContents.push(content);
        });
        
        // Check for hierarchy skips
        for (let i = 1; i < headingLevels.length; i++) {
          if (headingLevels[i] - headingLevels[i - 1] > 1) {
            issues.push({
              type: 'warning',
              category: 'structure',
              url,
              message: `Heading hierarchy skip: H${headingLevels[i - 1]} → H${headingLevels[i]}`,
              recommendation: 'Use heading tags in proper hierarchical order (H1 → H2 → H3, etc.)',
              details: { 
                previousLevel: headingLevels[i - 1],
                currentLevel: headingLevels[i],
                previousContent: headingContents[i - 1],
                currentContent: headingContents[i]
              }
            });
          }
        }
        
        // Check for duplicate heading content
        const contentMap = new Map<string, number[]>();
        headingContents.forEach((content, index) => {
          if (content) {
            if (!contentMap.has(content)) {
              contentMap.set(content, []);
            }
            contentMap.get(content)!.push(index);
          }
        });
        
        contentMap.forEach((indices, content) => {
          if (indices.length > 1) {
            issues.push({
              type: 'info',
              category: 'structure',
              url,
              message: `Duplicate heading content: "${content}" (found ${indices.length} times)`,
              recommendation: 'Use unique, descriptive content for each heading to improve SEO and user experience',
              details: { 
                duplicateContent: content,
                occurrences: indices.length,
                headingIndices: indices
              }
            });
          }
        });
      }
      
      // Check heading distribution and balance
      if (h1s.length === 1 && h2s.length === 0 && headings.length > 1) {
        issues.push({
          type: 'info',
          category: 'structure',
          url,
          message: 'Page has H1 but no H2 tags, yet has other headings',
          recommendation: 'Consider using H2 tags for main sections before using H3+ tags'
        });
      }
      
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'structure',
        url,
        message: `Failed to parse HTML structure: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Ensure the page returns valid HTML content'
      });
    }
    
    return issues;
  }

  /**
   * Analyze image alt attributes for accessibility and SEO
   */
  private analyzeImageAltAttributes(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      
      // Find all image tags
      const images = parsed.querySelectorAll('img');
      const totalImages = images.length;
      let imagesWithoutAlt = 0;
      let imagesWithEmptyAlt = 0;
      let imagesWithShortAlt = 0;
      let imagesWithExcessivelyLongAlt = 0;
      let decorativeImages = 0;
      
      if (totalImages === 0) {
        // No images found - this might be informational
        issues.push({
          type: 'info',
          category: 'accessibility',
          url,
          message: 'No images found on this page',
          recommendation: 'Consider adding relevant images to improve user engagement and SEO'
        });
        return issues;
      }
      
      images.forEach((img, index) => {
        const alt = img.getAttribute('alt');
        const src = img.getAttribute('src') || '';
        const title = img.getAttribute('title') || '';
        
        // Check if alt attribute exists
        if (alt === null) {
          imagesWithoutAlt++;
          issues.push({
            type: 'warning',
            category: 'accessibility',
            url,
            message: `Image missing alt attribute`,
            recommendation: 'Add descriptive alt text for accessibility. Use alt="" for decorative images.',
            details: { 
              imageIndex: index,
              src: src.substring(0, 100),
              hasTitle: !!title
            }
          });
        } else if (alt.trim() === '') {
          // Empty alt is valid for decorative images
          decorativeImages++;
        } else {
          // Check alt text quality
          const altText = alt.trim();
          
          if (altText.length < 5) {
            imagesWithShortAlt++;
            issues.push({
              type: 'info',
              category: 'accessibility',
              url,
              message: `Image has very short alt text: "${altText}"`,
              recommendation: 'Consider providing more descriptive alt text (5+ characters) unless this is decorative',
              details: { 
                imageIndex: index,
                altText,
                altLength: altText.length,
                src: src.substring(0, 100)
              }
            });
          } else if (altText.length > 125) {
            imagesWithExcessivelyLongAlt++;
            issues.push({
              type: 'info',
              category: 'accessibility',
              url,
              message: `Image has very long alt text (${altText.length} characters)`,
              recommendation: 'Alt text should be concise (under 125 characters). Consider using caption or surrounding text for detailed descriptions.',
              details: { 
                imageIndex: index,
                altText: altText.substring(0, 100) + '...',
                altLength: altText.length,
                src: src.substring(0, 100)
              }
            });
          }
          
          // Check for poor alt text patterns
          const lowQualityPatterns = [
            /^(image|img|picture|pic|photo)$/i,
            /^(image|img|picture|pic|photo)\s*\d*$/i,
            /^(untitled|unknown|default)$/i,
            /\.(jpg|jpeg|png|gif|svg|webp)$/i // Alt text ending with file extension
          ];
          
          const poorQualityMatch = lowQualityPatterns.some(pattern => pattern.test(altText));
          if (poorQualityMatch) {
            issues.push({
              type: 'warning',
              category: 'accessibility',
              url,
              message: `Image has generic or poor quality alt text: "${altText}"`,
              recommendation: 'Use descriptive alt text that explains what the image shows or its purpose',
              details: { 
                imageIndex: index,
                altText,
                src: src.substring(0, 100)
              }
            });
          }
          
          // Check for redundant text patterns
          if (altText.toLowerCase().startsWith('image of') || 
              altText.toLowerCase().startsWith('picture of') ||
              altText.toLowerCase().startsWith('photo of')) {
            issues.push({
              type: 'info',
              category: 'accessibility',
              url,
              message: `Alt text contains redundant prefix: "${altText.substring(0, 20)}..."`,
              recommendation: 'Avoid starting alt text with "image of", "picture of", etc. Screen readers already announce it as an image.',
              details: { 
                imageIndex: index,
                altText: altText.substring(0, 50),
                src: src.substring(0, 100)
              }
            });
          }
        }
        
        // Check if image is likely decorative but has alt text
        if (alt && alt.trim().length > 0) {
          const srcLower = src.toLowerCase();
          if (srcLower.includes('decoration') || 
              srcLower.includes('border') || 
              srcLower.includes('spacer') ||
              srcLower.includes('divider')) {
            issues.push({
              type: 'info',
              category: 'accessibility',
              url,
              message: 'Potentially decorative image has alt text',
              recommendation: 'If this image is purely decorative, consider using alt="" instead',
              details: { 
                imageIndex: index,
                altText: alt.substring(0, 50),
                src: src.substring(0, 100)
              }
            });
          }
        }
      });
      
      // Summary statistics
      if (imagesWithoutAlt > 0) {
        issues.push({
          type: 'error',
          category: 'accessibility',
          url,
          message: `${imagesWithoutAlt} out of ${totalImages} images missing alt attributes`,
          recommendation: 'All images should have alt attributes for accessibility compliance',
          details: { 
            totalImages,
            imagesWithoutAlt,
            percentageMissing: Math.round((imagesWithoutAlt / totalImages) * 100)
          }
        });
      }
      
      // Positive feedback when all images have alt attributes
      if (imagesWithoutAlt === 0 && totalImages > 0) {
        issues.push({
          type: 'info',
          category: 'accessibility',
          url,
          message: `All ${totalImages} images have alt attributes`,
          recommendation: 'Good accessibility practice! Ensure alt text is descriptive and meaningful.',
          details: { 
            totalImages,
            decorativeImages,
            imagesWithContent: totalImages - decorativeImages
          }
        });
      }
      
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'accessibility',
        url,
        message: `Failed to analyze image alt attributes: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Ensure the page returns valid HTML content'
      });
    }
    
    return issues;
  }

  /**
   * Analyze meta tags
   */
  private analyzeMetaTags(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      
      // Check for viewport meta tag
      const viewport = parsed.querySelector('meta[name="viewport"]');
      if (!viewport) {
        issues.push({
          type: 'warning',
          category: 'meta',
          url,
          message: 'Missing viewport meta tag',
          recommendation: 'Add viewport meta tag for mobile responsiveness: <meta name="viewport" content="width=device-width, initial-scale=1">'
        });
      }
      
      // Check for robots meta tag conflicts
      const robotsMeta = parsed.querySelector('meta[name="robots"]');
      if (robotsMeta) {
        const robotsContent = robotsMeta.getAttribute('content')?.toLowerCase();
        if (robotsContent?.includes('noindex')) {
          issues.push({
            type: 'info',
            category: 'robots',
            url,
            message: 'Page has noindex directive',
            recommendation: 'Ensure this page should not be indexed by search engines',
            details: { robotsContent }
          });
        }
      }
      
      // Check for Open Graph tags
      const ogTitle = parsed.querySelector('meta[property="og:title"]');
      const ogDescription = parsed.querySelector('meta[property="og:description"]');
      const ogImage = parsed.querySelector('meta[property="og:image"]');
      
      if (!ogTitle && !ogDescription && !ogImage) {
        issues.push({
          type: 'info',
          category: 'meta',
          url,
          message: 'Missing Open Graph tags',
          recommendation: 'Add Open Graph tags for better social media sharing'
        });
      }
      
    } catch (error) {
      // Meta tag analysis is not critical, log but continue
      console.warn(`Meta tag analysis failed for ${url}:`, error);
    }
    
    return issues;
  }

  /**
   * Check robots.txt compliance for a URL
   */
  checkRobotsCompliance(url: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      const userAgent = this.config.userAgent || '*';
      const isAllowed = this.robotsParser.isUrlAllowed(url, userAgent);
      
      if (!isAllowed) {
        issues.push({
          type: 'warning',
          category: 'robots',
          url,
          message: 'URL is disallowed by robots.txt',
          recommendation: 'If this page should be indexed, update robots.txt or remove the disallow directive',
          details: { userAgent }
        });
      }
      
    } catch (error) {
      issues.push({
        type: 'info',
        category: 'robots',
        url,
        message: 'Could not check robots.txt compliance',
        recommendation: 'Ensure robots.txt is accessible and properly formatted',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    }
    
    return issues;
  }

  /**
   * Analyze keywords and keyword density
   */
  private analyzeKeywords(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      
      // Check meta keywords tag
      const keywordsTag = parsed.querySelector('meta[name="keywords"]');
      if (keywordsTag) {
        const keywordsContent = keywordsTag.getAttribute('content')?.trim();
        if (keywordsContent) {
          // Check keywords length (should not exceed 100 characters)
          if (keywordsContent.length > 100) {
            issues.push({
              type: 'warning',
              category: 'meta',
              url,
              message: `Meta keywords is ${keywordsContent.length} characters (exceeds 100 character limit)`,
              recommendation: 'Keep meta keywords under 100 characters. Consider focusing on the most important keywords.',
              details: { 
                keywords: keywordsContent,
                length: keywordsContent.length 
              }
            });
          }
          
          // Analyze keyword density
          const keywordList = keywordsContent.split(/[,;]/).map(k => k.trim().toLowerCase()).filter(k => k.length > 0);
          const duplicates = this.findDuplicateKeywords(keywordList);
          
          if (duplicates.length > 0) {
            issues.push({
              type: 'info',
              category: 'meta',
              url,
              message: `Duplicate keywords found in meta keywords: ${duplicates.join(', ')}`,
              recommendation: 'Remove duplicate keywords to improve keyword focus',
              details: { duplicateKeywords: duplicates }
            });
          }
        } else {
          issues.push({
            type: 'info',
            category: 'meta',
            url,
            message: 'Empty meta keywords tag found',
            recommendation: 'Either add relevant keywords or remove the empty meta keywords tag'
          });
        }
      }
      
      // Analyze content keyword density
      const keywordDensityIssues = this.analyzeContentKeywordDensity(url, content, parsed);
      issues.push(...keywordDensityIssues);
      
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'meta',
        url,
        message: `Failed to analyze keywords: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Ensure the page returns valid HTML content'
      });
    }
    
    return issues;
  }

  /**
   * Find duplicate keywords in a list
   */
  private findDuplicateKeywords(keywords: string[]): string[] {
    const seen = new Set<string>();
    const duplicates = new Set<string>();
    
    keywords.forEach(keyword => {
      if (seen.has(keyword)) {
        duplicates.add(keyword);
      } else {
        seen.add(keyword);
      }
    });
    
    return Array.from(duplicates);
  }

  /**
   * Analyze content keyword density
   */
  private analyzeContentKeywordDensity(url: string, content: string, parsed: any): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Extract text content from the page
      const textContent = this.extractTextContent(parsed);
      if (!textContent || textContent.length < 100) {
        issues.push({
          type: 'warning',
          category: 'content',
          url,
          message: 'Page has insufficient text content for keyword analysis',
          recommendation: 'Add more meaningful text content to improve SEO'
        });
        return issues;
      }
      
      // Calculate word count and frequency
      const words = textContent.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2); // Only consider words longer than 2 characters
      
      const totalWords = words.length;
      const wordFrequency = new Map<string, number>();
      
      // Count word frequencies
      words.forEach(word => {
        wordFrequency.set(word, (wordFrequency.get(word) || 0) + 1);
      });
      
      // Calculate density and find high-density keywords
      const keywordDensity = new Map<string, number>();
      const highDensityKeywords: Array<{word: string, count: number, density: number}> = [];
      
      wordFrequency.forEach((count, word) => {
        const density = (count / totalWords) * 100;
        keywordDensity.set(word, density);
        
        // Flag keywords with density > 3% as potentially over-optimized
        if (density > 3 && count > 5) {
          highDensityKeywords.push({ word, count, density });
        }
      });
      
      // Sort by density
      highDensityKeywords.sort((a, b) => b.density - a.density);
      
      // Report high-density keywords
      if (highDensityKeywords.length > 0) {
        const topKeywords = highDensityKeywords.slice(0, 5); // Top 5
        
        topKeywords.forEach(keyword => {
          let issueType: 'error' | 'warning' | 'info' = 'info';
          let message = '';
          let recommendation = '';
          
          if (keyword.density > 5) {
            issueType = 'warning';
            message = `Keyword "${keyword.word}" has very high density (${keyword.density.toFixed(2)}%, ${keyword.count} occurrences)`;
            recommendation = 'Consider reducing keyword frequency to avoid over-optimization penalties';
          } else if (keyword.density > 3) {
            issueType = 'info';
            message = `Keyword "${keyword.word}" has elevated density (${keyword.density.toFixed(2)}%, ${keyword.count} occurrences)`;
            recommendation = 'Monitor keyword density to maintain natural content flow';
          }
          
          if (message) {
            issues.push({
              type: issueType,
              category: 'content',
              url,
              message,
              recommendation,
              details: {
                keyword: keyword.word,
                count: keyword.count,
                density: keyword.density,
                totalWords
              }
            });
          }
        });
      }
      
      // Check for keyword stuffing patterns
      const keywordStuffingIssues = this.detectKeywordStuffing(url, textContent, wordFrequency, totalWords);
      issues.push(...keywordStuffingIssues);
      
      // Provide overall content quality insights
      if (highDensityKeywords.length === 0) {
        issues.push({
          type: 'info',
          category: 'content',
          url,
          message: `Content analysis: ${totalWords} words, balanced keyword distribution`,
          recommendation: 'Good keyword distribution. Continue creating natural, valuable content.',
          details: { 
            totalWords,
            uniqueWords: wordFrequency.size,
            averageWordsPerSentence: Math.round(totalWords / Math.max(1, textContent.split(/[.!?]+/).length))
          }
        });
      }
      
    } catch (error) {
      issues.push({
        type: 'error',
        category: 'content',
        url,
        message: `Failed to analyze keyword density: ${error instanceof Error ? error.message : 'Unknown error'}`,
        recommendation: 'Ensure the page returns valid HTML content'
      });
    }
    
    return issues;
  }

  /**
   * Extract clean text content from parsed HTML
   */
  private extractTextContent(parsed: any): string {
    // Remove script and style elements
    const scripts = parsed.querySelectorAll('script, style, nav, footer, header');
    scripts.forEach((element: any) => element.remove());
    
    // Get text from main content areas
    const contentSelectors = ['main', 'article', '.content', '#content', '.post', '.entry'];
    let textContent = '';
    
    for (const selector of contentSelectors) {
      const element = parsed.querySelector(selector);
      if (element) {
        textContent = element.textContent || '';
        break;
      }
    }
    
    // Fallback to body if no specific content area found
    if (!textContent) {
      textContent = parsed.body?.textContent || '';
    }
    
    // Clean up the text
    return textContent
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, ' ')
      .trim();
  }

  /**
   * Detect keyword stuffing patterns
   */
  private detectKeywordStuffing(url: string, textContent: string, wordFrequency: Map<string, number>, totalWords: number): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Check for repetitive phrases
      const sentences = textContent.split(/[.!?]+/).filter(s => s.trim().length > 10);
      const phraseMap = new Map<string, number>();
      
      sentences.forEach(sentence => {
        const words = sentence.trim().toLowerCase().split(/\s+/);
        
        // Check for 2-4 word phrases
        for (let len = 2; len <= Math.min(4, words.length - 1); len++) {
          for (let i = 0; i <= words.length - len; i++) {
            const phrase = words.slice(i, i + len).join(' ');
            if (phrase.length > 5) { // Only meaningful phrases
              phraseMap.set(phrase, (phraseMap.get(phrase) || 0) + 1);
            }
          }
        }
      });
      
      // Find overly repeated phrases
      const stuffedPhrases: Array<{phrase: string, count: number}> = [];
      phraseMap.forEach((count, phrase) => {
        if (count > 3 && phrase.split(' ').length >= 2) {
          stuffedPhrases.push({ phrase, count });
        }
      });
      
      if (stuffedPhrases.length > 0) {
        stuffedPhrases.sort((a, b) => b.count - a.count);
        const topStuffed = stuffedPhrases.slice(0, 3);
        
        issues.push({
          type: 'warning',
          category: 'content',
          url,
          message: `Potential keyword stuffing detected: repeated phrases found`,
          recommendation: 'Vary your language and avoid excessive repetition of phrases',
          details: {
            stuffedPhrases: topStuffed,
            totalPhrases: stuffedPhrases.length
          }
        });
      }
      
    } catch (error) {
      // Don't fail the entire analysis if keyword stuffing detection fails
      console.warn('Keyword stuffing detection failed:', error);
    }
    
    return issues;
  }

  /**
   * Validate hreflang tags
   */
  validateHreflang(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      const hreflangs = parsed.querySelectorAll('link[rel="alternate"][hreflang]');
      
      if (hreflangs.length > 0) {
        const hreflangUrls: Record<string, string> = {};
        const currentPageHreflang = new URL(url).pathname;
        
        hreflangs.forEach(link => {
          const hreflang = link.getAttribute('hreflang');
          const href = link.getAttribute('href');
          
          if (hreflang && href) {
            if (hreflangUrls[hreflang]) {
              issues.push({
                type: 'error',
                category: 'i18n',
                url,
                message: `Duplicate hreflang "${hreflang}" found`,
                recommendation: 'Each hreflang value should appear only once per page',
                details: { hreflang, href, duplicate: hreflangUrls[hreflang] }
              });
            }
            hreflangUrls[hreflang] = href;
            
            // Validate hreflang format
            if (!/^[a-z]{2}(-[A-Z]{2})?$|^x-default$/.test(hreflang)) {
              issues.push({
                type: 'warning',
                category: 'i18n',
                url,
                message: `Invalid hreflang format: "${hreflang}"`,
                recommendation: 'Use proper language codes (e.g., "en", "en-US") or "x-default"',
                details: { hreflang, href }
              });
            }
          }
        });
        
        // Check for self-referencing hreflang
        const selfRef = Object.values(hreflangUrls).some(hrefUrl => {
          try {
            return new URL(hrefUrl, url).href === url;
          } catch {
            return false;
          }
        });
        
        if (!selfRef) {
          issues.push({
            type: 'warning',
            category: 'i18n',
            url,
            message: 'Missing self-referencing hreflang',
            recommendation: 'Include a hreflang link pointing to the current page'
          });
        }
      }
      
    } catch (error) {
      console.warn(`Hreflang validation failed for ${url}:`, error);
    }
    
    return issues;
  }

  /**
   * Check canonical URL implementation
   */
  checkCanonicals(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      const canonicals = parsed.querySelectorAll('link[rel="canonical"]');
      
      if (canonicals.length === 0) {
        issues.push({
          type: 'info',
          category: 'structure',
          url,
          message: 'Missing canonical tag',
          recommendation: 'Add canonical tag to prevent duplicate content issues'
        });
      } else if (canonicals.length > 1) {
        issues.push({
          type: 'error',
          category: 'structure',
          url,
          message: 'Multiple canonical tags found',
          recommendation: 'Use only one canonical tag per page',
          details: { canonicalCount: canonicals.length }
        });
      } else {
        const canonical = canonicals[0];
        const href = canonical.getAttribute('href');
        
        if (!href) {
          issues.push({
            type: 'error',
            category: 'structure',
            url,
            message: 'Canonical tag missing href attribute',
            recommendation: 'Canonical tag must include a valid href attribute'
          });
        } else {
          try {
            const canonicalUrl = new URL(href, url).href;
            if (canonicalUrl !== url) {
              issues.push({
                type: 'info',
                category: 'structure',
                url,
                message: 'Page canonicalizes to different URL',
                recommendation: 'Verify this is intentional to avoid duplicate content',
                details: { canonical: canonicalUrl, current: url }
              });
            }
          } catch {
            issues.push({
              type: 'error',
              category: 'structure',
              url,
              message: 'Invalid canonical URL format',
              recommendation: 'Ensure canonical href contains a valid URL',
              details: { href }
            });
          }
        }
      }
      
    } catch (error) {
      console.warn(`Canonical validation failed for ${url}:`, error);
    }
    
    return issues;
  }

  /**
   * Check for multilingual URL patterns
   */
  checkMultilingualPatterns(urls: string[]): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    // Extract language patterns from URLs
    const langPatterns: Record<string, string[]> = {};
    const pathStructures: Record<string, string[]> = {};
    
    urls.forEach(url => {
      try {
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/').filter(Boolean);
        
        // Check for language codes in path
        const possibleLangCode = pathParts[0];
        if (possibleLangCode && /^[a-z]{2}(-[a-z]{2})?$/i.test(possibleLangCode)) {
          if (!langPatterns[possibleLangCode]) {
            langPatterns[possibleLangCode] = [];
          }
          langPatterns[possibleLangCode].push(url);
          
          // Check structure consistency
          const structure = pathParts.slice(1).join('/');
          if (!pathStructures[structure]) {
            pathStructures[structure] = [];
          }
          pathStructures[structure].push(possibleLangCode);
        }
        
        // Check for language subdomains
        const subdomain = urlObj.hostname.split('.')[0];
        if (/^[a-z]{2}(-[a-z]{2})?$/i.test(subdomain)) {
          if (!langPatterns[subdomain]) {
            langPatterns[subdomain] = [];
          }
          langPatterns[subdomain].push(url);
        }
        
      } catch {
        // Skip invalid URLs
      }
    });
    
    // Check for inconsistent multilingual patterns
    const languages = Object.keys(langPatterns);
    if (languages.length > 1) {
      // Check if all content structures are available in all languages
      Object.entries(pathStructures).forEach(([structure, langs]) => {
        const missingLangs = languages.filter(lang => !langs.includes(lang));
        if (missingLangs.length > 0) {
          issues.push({
            type: 'warning',
            category: 'i18n',
            url: `Multiple URLs with structure: ${structure}`,
            message: `Content structure missing in languages: ${missingLangs.join(', ')}`,
            recommendation: 'Ensure all content is available in all supported languages',
            details: { structure, availableLanguages: langs, missingLanguages: missingLangs }
          });
        }
      });
    }
    
    return issues;
  }

  /**
   * Validate structured data (JSON-LD, microdata, etc.)
   */
  validateStructuredData(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      
      // Check for JSON-LD structured data
      const jsonLdScripts = parsed.querySelectorAll('script[type="application/ld+json"]');
      
      jsonLdScripts.forEach((script, index) => {
        try {
          const jsonContent = script.textContent?.trim();
          if (jsonContent) {
            JSON.parse(jsonContent);
            // Valid JSON-LD found
            issues.push({
              type: 'info',
              category: 'structure',
              url,
              message: `Valid JSON-LD structured data found (${index + 1}/${jsonLdScripts.length})`,
              recommendation: 'Good! Structured data helps search engines understand your content'
            });
          }
        } catch {
          issues.push({
            type: 'error',
            category: 'structure',
            url,
            message: `Invalid JSON-LD syntax in script ${index + 1}`,
            recommendation: 'Fix JSON syntax in structured data script'
          });
        }
      });
      
      // Check for microdata
      const itemScopes = parsed.querySelectorAll('[itemscope]');
      if (itemScopes.length > 0) {
        issues.push({
          type: 'info',
          category: 'structure',
          url,
          message: `Microdata structured data found (${itemScopes.length} items)`,
          recommendation: 'Consider using JSON-LD for easier maintenance'
        });
      }
      
    } catch (error) {
      console.warn(`Structured data validation failed for ${url}:`, error);
    }
    
    return issues;
  }

  /**
   * Check basic accessibility issues
   */
  private checkAccessibility(url: string, content: string): SeoIssue[] {
    const issues: SeoIssue[] = [];
    
    try {
      // Parse HTML using JSDOM
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(content);
      const parsed = dom.window.document;
      
      // Check for images without alt tags
      const images = parsed.querySelectorAll('img');
      let imagesWithoutAlt = 0;
      
      images.forEach(img => {
        const alt = img.getAttribute('alt');
        if (alt === null) {
          imagesWithoutAlt++;
        }
      });
      
      if (imagesWithoutAlt > 0) {
        issues.push({
          type: 'warning',
          category: 'accessibility',
          url,
          message: `${imagesWithoutAlt} images missing alt attributes`,
          recommendation: 'Add descriptive alt attributes to all images for accessibility',
          details: { totalImages: images.length, missingAlt: imagesWithoutAlt }
        });
      }
      
      // Check for form labels
      const inputs = parsed.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], textarea, select');
      let inputsWithoutLabels = 0;
      
      inputs.forEach(input => {
        const id = input.getAttribute('id');
        const ariaLabel = input.getAttribute('aria-label');
        const ariaLabelledBy = input.getAttribute('aria-labelledby');
        
        if (id) {
          const label = parsed.querySelector(`label[for="${id}"]`);
          if (!label && !ariaLabel && !ariaLabelledBy) {
            inputsWithoutLabels++;
          }
        } else if (!ariaLabel && !ariaLabelledBy) {
          inputsWithoutLabels++;
        }
      });
      
      if (inputsWithoutLabels > 0) {
        issues.push({
          type: 'warning',
          category: 'accessibility',
          url,
          message: `${inputsWithoutLabels} form inputs missing labels`,
          recommendation: 'Associate labels with form inputs for accessibility',
          details: { totalInputs: inputs.length, missingLabels: inputsWithoutLabels }
        });
      }
      
    } catch (error) {
      console.warn(`Accessibility check failed for ${url}:`, error);
    }
    
    return issues;
  }
}