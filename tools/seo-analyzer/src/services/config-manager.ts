import * as fs from 'fs-extra';
import * as path from 'path';
import * as yaml from 'yaml';
import Joi from 'joi';
import { AnalyzerConfig, DEFAULT_CONFIG } from '../interfaces/config';

/**
 * Configuration validation schema using Joi
 */
const configSchema = Joi.object({
  baseUrl: Joi.string().uri().required(),
  sitemapUrl: Joi.string().uri().optional(),
  maxConcurrency: Joi.number().integer().min(1).max(100).default(DEFAULT_CONFIG.maxConcurrency),
  requestTimeout: Joi.number().integer().min(1000).max(300000).default(DEFAULT_CONFIG.requestTimeout),
  slowPageThreshold: Joi.number().integer().min(100).max(60000).default(DEFAULT_CONFIG.slowPageThreshold),
  maxCrawlDepth: Joi.number().integer().min(1).max(10).default(DEFAULT_CONFIG.maxCrawlDepth),
  userAgent: Joi.string().min(1).max(200).default(DEFAULT_CONFIG.userAgent),
  customHeaders: Joi.object().pattern(Joi.string(), Joi.string()).default(DEFAULT_CONFIG.customHeaders),
  excludePatterns: Joi.array().items(Joi.string()).default(DEFAULT_CONFIG.excludePatterns),
  outputFormats: Joi.array().items(Joi.string().valid('json', 'csv', 'html')).min(1).default(DEFAULT_CONFIG.outputFormats),
  outputDir: Joi.string().min(1).default(DEFAULT_CONFIG.outputDir),
  enableJsRendering: Joi.boolean().default(DEFAULT_CONFIG.enableJsRendering),
  jsRenderWaitTime: Joi.number().integer().min(1000).max(60000).default(DEFAULT_CONFIG.jsRenderWaitTime),
});

/**
 * Configuration file formats supported
 */
export type ConfigFileFormat = 'json' | 'yaml' | 'yml';

/**
 * Configuration manager for handling configuration loading, validation, and merging
 */
export class ConfigManager {
  /**
   * Load configuration from a file
   * @param filePath Path to the configuration file
   * @returns Parsed configuration object
   */
  static async loadFromFile(filePath: string): Promise<Partial<AnalyzerConfig>> {
    if (!await fs.pathExists(filePath)) {
      throw new Error(`Configuration file not found: ${filePath}`);
    }

    const fileContent = await fs.readFile(filePath, 'utf-8');
    const fileExtension = path.extname(filePath).toLowerCase().slice(1) as ConfigFileFormat;

    try {
      switch (fileExtension) {
        case 'json':
          return JSON.parse(fileContent);
        case 'yaml':
        case 'yml':
          return yaml.parse(fileContent);
        default:
          throw new Error(`Unsupported configuration file format: ${fileExtension}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse configuration file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save configuration to a file
   * @param config Configuration object to save
   * @param filePath Path where to save the configuration
   * @param format Format to save in (json, yaml, yml)
   */
  static async saveToFile(config: AnalyzerConfig, filePath: string, format: ConfigFileFormat = 'json'): Promise<void> {
    let content: string;

    try {
      switch (format) {
        case 'json':
          content = JSON.stringify(config, null, 2);
          break;
        case 'yaml':
        case 'yml':
          content = yaml.stringify(config);
          break;
        default:
          throw new Error(`Unsupported configuration file format: ${format}`);
      }

      await fs.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, content, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to save configuration file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate configuration object against schema
   * @param config Configuration object to validate
   * @returns Validated and normalized configuration
   */
  static validateConfig(config: Partial<AnalyzerConfig>): AnalyzerConfig {
    const { error, value } = configSchema.validate(config, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessages = error.details.map(detail => detail.message).join(', ');
      throw new Error(`Configuration validation failed: ${errorMessages}`);
    }

    return value as AnalyzerConfig;
  }

  /**
   * Merge multiple configuration objects with priority (later objects override earlier ones)
   * @param configs Array of configuration objects to merge
   * @returns Merged configuration object
   */
  static mergeConfigs(...configs: Partial<AnalyzerConfig>[]): Partial<AnalyzerConfig> {
    const merged: Partial<AnalyzerConfig> = {};

    for (const config of configs) {
      // Merge all properties except special ones
      const { customHeaders, excludePatterns, outputFormats, ...otherProps } = config;
      Object.assign(merged, otherProps);
      
      // Special handling for objects - merge instead of replace
      if (customHeaders) {
        merged.customHeaders = { ...merged.customHeaders, ...customHeaders };
      }
      
      // Special handling for arrays - concatenate instead of replace
      if (excludePatterns) {
        merged.excludePatterns = [...(merged.excludePatterns || []), ...excludePatterns];
      }
      
      // Output formats should replace, not concatenate
      if (outputFormats) {
        merged.outputFormats = [...outputFormats];
      }
    }

    return merged;
  }

  /**
   * Create a complete configuration by merging defaults, file config, and overrides
   * @param fileConfig Configuration loaded from file (optional)
   * @param overrides Configuration overrides (e.g., from CLI args)
   * @returns Complete validated configuration
   */
  static createConfig(
    fileConfig: Partial<AnalyzerConfig> = {},
    overrides: Partial<AnalyzerConfig> = {}
  ): AnalyzerConfig {
    const merged = this.mergeConfigs(DEFAULT_CONFIG, fileConfig, overrides);
    return this.validateConfig(merged);
  }

  /**
   * Find configuration file in common locations
   * @param baseDir Base directory to search from
   * @returns Path to found configuration file or null
   */
  static async findConfigFile(baseDir: string = process.cwd()): Promise<string | null> {
    const configFileNames = [
      'seo-analyzer.config.json',
      'seo-analyzer.config.yaml',
      'seo-analyzer.config.yml',
      '.seo-analyzer.json',
      '.seo-analyzer.yaml',
      '.seo-analyzer.yml',
    ];

    for (const fileName of configFileNames) {
      const filePath = path.join(baseDir, fileName);
      if (await fs.pathExists(filePath)) {
        return filePath;
      }
    }

    return null;
  }

  /**
   * Generate a sample configuration file
   * @returns Sample configuration object with comments
   */
  static generateSampleConfig(): AnalyzerConfig {
    return {
      baseUrl: 'https://example.com',
      sitemapUrl: 'https://example.com/sitemap.xml',
      maxConcurrency: 10,
      requestTimeout: 30000,
      slowPageThreshold: 3000,
      maxCrawlDepth: 3,
      userAgent: 'SEO-Analyzer/1.0',
      customHeaders: {
        'Accept-Language': 'en-US,en;q=0.9',
      },
      excludePatterns: [
        '/admin/*',
        '/private/*',
        '*.pdf',
      ],
      outputFormats: ['json', 'html'],
      outputDir: './seo-reports',
      enableJsRendering: false,
      jsRenderWaitTime: 5000,
    };
  }

  /**
   * Validate URL patterns in exclude patterns
   * @param patterns Array of patterns to validate
   * @returns Array of validation errors (empty if all valid)
   */
  static validateExcludePatterns(patterns: string[]): string[] {
    const errors: string[] = [];

    for (const pattern of patterns) {
      try {
        // Test if pattern can be used as a regex
        new RegExp(pattern.replace(/\*/g, '.*'));
      } catch (error) {
        errors.push(`Invalid exclude pattern "${pattern}": ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return errors;
  }

  /**
   * Validate output directory is writable
   * @param outputDir Directory path to validate
   * @returns Promise that resolves if directory is valid/writable
   */
  static async validateOutputDir(outputDir: string): Promise<void> {
    try {
      await fs.ensureDir(outputDir);
      
      // Test write permissions by creating a temporary file
      const testFile = path.join(outputDir, '.write-test');
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);
    } catch (error) {
      throw new Error(`Output directory "${outputDir}" is not writable: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}