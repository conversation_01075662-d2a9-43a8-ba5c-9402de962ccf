import { promises as fs } from 'fs';
import { join } from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  url?: string;
  statusCode?: number;
  loadTime?: number;
  error?: string;
  details?: Record<string, any>;
}

export interface UrlTestLogEntry {
  url: string;
  startTime: string;
  endTime: string;
  duration: number;
  statusCode: number;
  finalUrl?: string;
  redirectChain: string[];
  error?: string;
  responseHeaders?: Record<string, string>;
  requestHeaders?: Record<string, string>;
  size?: number;
  userAgent?: string;
}

/**
 * Enhanced logging service with detailed URL tracking
 */
export class Logger {
  private logs: LogEntry[] = [];
  private urlTestLogs: UrlTestLogEntry[] = [];
  private logLevel: LogLevel;
  private outputDir: string;
  private enableConsole: boolean;

  constructor(logLevel: LogLevel = LogLevel.INFO, outputDir: string = './seo-reports', enableConsole: boolean = true) {
    this.logLevel = logLevel;
    this.outputDir = outputDir;
    this.enableConsole = enableConsole;
  }

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  private getLogLevelString(level: LogLevel): string {
    const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG', 'TRACE'];
    return levels[level] || 'UNKNOWN';
  }

  private getLogEmoji(level: LogLevel): string {
    const emojis = ['🔴', '🟡', 'ℹ️', '🔍', '🔬'];
    return emojis[level] || '📝';
  }

  /**
   * Log a message with specified level
   */
  log(level: LogLevel, category: string, message: string, details?: {
    url?: string;
    statusCode?: number;
    loadTime?: number;
    error?: string;
    details?: Record<string, any>;
  }): void {
    if (!this.shouldLog(level)) return;

    const logEntry: LogEntry = {
      timestamp: this.formatTimestamp(),
      level,
      category,
      message,
      ...details
    };

    this.logs.push(logEntry);

    if (this.enableConsole) {
      const emoji = this.getLogEmoji(level);
      const levelStr = this.getLogLevelString(level);
      const timeStr = new Date().toLocaleTimeString();
      
      let logMessage = `${emoji} [${timeStr}] [${levelStr}] [${category}] ${message}`;
      
      if (details?.url) {
        logMessage += `\n   🌐 URL: ${details.url}`;
      }
      
      if (details?.statusCode) {
        logMessage += `\n   📊 Status: ${details.statusCode}`;
      }
      
      if (details?.loadTime) {
        logMessage += `\n   ⏱️  Load Time: ${details.loadTime}ms`;
      }
      
      if (details?.error) {
        logMessage += `\n   ❌ Error: ${details.error}`;
      }

      console.log(logMessage);
    }
  }

  /**
   * Log URL test details
   */
  logUrlTest(entry: UrlTestLogEntry): void {
    this.urlTestLogs.push(entry);

    if (this.enableConsole && this.shouldLog(LogLevel.DEBUG)) {
      const status = entry.statusCode >= 200 && entry.statusCode < 300 ? '✅' : 
                    entry.statusCode >= 300 && entry.statusCode < 400 ? '🔄' : 
                    entry.statusCode >= 400 && entry.statusCode < 500 ? '❌' : '🔥';
      
      console.log(`${status} [${new Date(entry.startTime).toLocaleTimeString()}] ${entry.url} (${entry.statusCode}) - ${entry.duration}ms`);
      
      if (entry.redirectChain.length > 1) {
        console.log(`   🔄 Redirects: ${entry.redirectChain.join(' → ')}`);
      }
      
      if (entry.error) {
        console.log(`   ❌ Error: ${entry.error}`);
      }
    }
  }

  // Convenience methods
  error(category: string, message: string, details?: any): void {
    this.log(LogLevel.ERROR, category, message, details);
  }

  warn(category: string, message: string, details?: any): void {
    this.log(LogLevel.WARN, category, message, details);
  }

  info(category: string, message: string, details?: any): void {
    this.log(LogLevel.INFO, category, message, details);
  }

  debug(category: string, message: string, details?: any): void {
    this.log(LogLevel.DEBUG, category, message, details);
  }

  trace(category: string, message: string, details?: any): void {
    this.log(LogLevel.TRACE, category, message, details);
  }

  /**
   * Progress tracking for batch operations
   */
  logProgress(current: number, total: number, category: string, message?: string): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar = this.createProgressBar(percentage);
    const msg = message || `Processing ${current}/${total}`;
    
    if (this.enableConsole) {
      console.log(`📊 [${category}] ${progressBar} ${percentage}% - ${msg}`);
    }
  }

  private createProgressBar(percentage: number, width: number = 20): string {
    const filled = Math.round((percentage / 100) * width);
    const empty = width - filled;
    return '█'.repeat(filled) + '░'.repeat(empty);
  }

  /**
   * Performance summary
   */
  logPerformanceSummary(stats: {
    totalUrls: number;
    successCount: number;
    errorCount: number;
    avgLoadTime: number;
    slowPages: number;
    fastPages: number;
  }): void {
    this.info('PERFORMANCE', 'Analysis Performance Summary', stats);
    
    if (this.enableConsole) {
      console.log('\n📊 Performance Summary:');
      console.log(`   🔍 Total URLs tested: ${stats.totalUrls}`);
      console.log(`   ✅ Successful: ${stats.successCount}`);
      console.log(`   ❌ Errors: ${stats.errorCount}`);
      console.log(`   ⚡ Average load time: ${Math.round(stats.avgLoadTime)}ms`);
      console.log(`   🐌 Slow pages (>3s): ${stats.slowPages}`);
      console.log(`   🚀 Fast pages (<1s): ${stats.fastPages}`);
    }
  }

  /**
   * Save logs to files
   */
  async saveLogs(baseUrl: string): Promise<void> {
    await fs.mkdir(this.outputDir, { recursive: true });
    
    const timestamp = new Date().toISOString().split('T')[0];
    const domain = new URL(baseUrl).hostname;
    
    // Save main logs
    const logsPath = join(this.outputDir, `seo-analysis-logs-${domain}-${timestamp}.json`);
    await fs.writeFile(logsPath, JSON.stringify({
      meta: {
        baseUrl,
        timestamp: new Date().toISOString(),
        totalEntries: this.logs.length
      },
      logs: this.logs
    }, null, 2));

    // Save URL test logs
    const urlLogsPath = join(this.outputDir, `url-test-logs-${domain}-${timestamp}.json`);
    await fs.writeFile(urlLogsPath, JSON.stringify({
      meta: {
        baseUrl,
        timestamp: new Date().toISOString(),
        totalTests: this.urlTestLogs.length
      },
      urlTests: this.urlTestLogs
    }, null, 2));

    // Save detailed CSV for URL tests
    const csvPath = join(this.outputDir, `url-test-details-${domain}-${timestamp}.csv`);
    const csvContent = this.generateUrlTestCsv();
    await fs.writeFile(csvPath, csvContent);

    console.log(`📄 Logs saved:`);
    console.log(`   📋 Main logs: ${logsPath}`);
    console.log(`   🔗 URL tests: ${urlLogsPath}`);
    console.log(`   📊 CSV details: ${csvPath}`);
  }

  private generateUrlTestCsv(): string {
    const headers = [
      'URL',
      'Start Time',
      'End Time',
      'Duration (ms)',
      'Status Code',
      'Final URL',
      'Redirect Count',
      'Error',
      'Size (bytes)',
      'User Agent'
    ];

    const rows = this.urlTestLogs.map(entry => [
      `"${entry.url}"`,
      `"${entry.startTime}"`,
      `"${entry.endTime}"`,
      entry.duration.toString(),
      entry.statusCode.toString(),
      `"${entry.finalUrl || entry.url}"`,
      (entry.redirectChain.length - 1).toString(),
      `"${entry.error || ''}"`,
      (entry.size || 0).toString(),
      `"${entry.userAgent || ''}"`
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  /**
   * Get statistics about logged data
   */
  getStats(): {
    totalLogs: number;
    logsByLevel: Record<string, number>;
    urlTestStats: {
      totalTests: number;
      successfulTests: number;
      failedTests: number;
      avgDuration: number;
    };
  } {
    const logsByLevel = this.logs.reduce((acc, log) => {
      const level = this.getLogLevelString(log.level);
      acc[level] = (acc[level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const successfulTests = this.urlTestLogs.filter(test => test.statusCode >= 200 && test.statusCode < 400);
    const avgDuration = this.urlTestLogs.length > 0 ? 
      this.urlTestLogs.reduce((sum, test) => sum + test.duration, 0) / this.urlTestLogs.length : 0;

    return {
      totalLogs: this.logs.length,
      logsByLevel,
      urlTestStats: {
        totalTests: this.urlTestLogs.length,
        successfulTests: successfulTests.length,
        failedTests: this.urlTestLogs.length - successfulTests.length,
        avgDuration: Math.round(avgDuration)
      }
    };
  }

  /**
   * Clear all logs
   */
  clear(): void {
    this.logs = [];
    this.urlTestLogs = [];
  }
} 