import { UrlTestingService, UrlTestResult } from '../interfaces/url-testing';
import { AnalyzerConfig } from '../interfaces/config';
import { SeoIssue } from '../interfaces/seo-analyzer';
import { performance } from 'perf_hooks';
import { performance as perfHooks } from 'perf_hooks';
import { Logger, LogLevel, UrlTestLogEntry } from './logger';

/**
 * Implementation of URL testing service with performance monitoring
 */
export class UrlTestingServiceImpl implements UrlTestingService {
  private config: AnalyzerConfig;
  private userAgent: string;
  private timeout: number;
  private logger: Logger;

  constructor(config: AnalyzerConfig, logger?: Logger) {
    this.config = config;
    this.userAgent = config.userAgent;
    this.timeout = config.requestTimeout;
    this.logger = logger || new Logger(LogLevel.INFO, config.outputDir);
  }

  /**
   * Test a single URL with performance monitoring
   */
  async testUrl(url: string): Promise<UrlTestResult> {
    const startTime = perfHooks.now();
    const startTimeISO = new Date().toISOString();
    let redirectChain: string[] = [url];
    
    this.logger.debug('URL_TEST', `Starting URL test: ${url}`);
    
    try {
      // Validate URL format
      new URL(url);
      
      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);
      
      // Prepare request headers
      const headers: Record<string, string> = {
        'User-Agent': this.userAgent,
        ...this.config.customHeaders
      };

      // Make HTTP request with fetch
      const response = await fetch(url, {
        method: 'GET',
        headers,
        signal: controller.signal,
        redirect: 'manual' // Handle redirects manually to track chain
      });

      clearTimeout(timeoutId);
      
      // Handle redirects manually to build redirect chain
      let finalResponse = response;
      let currentUrl = url;
      
      while (finalResponse.status >= 300 && finalResponse.status < 400) {
        const location = finalResponse.headers.get('location');
        if (!location) break;
        
        // Build absolute URL from relative location
        const nextUrl = new URL(location, currentUrl).toString();
        redirectChain.push(nextUrl);
        currentUrl = nextUrl;
        
        // Follow redirect
        const redirectController = new AbortController();
        const redirectTimeoutId = setTimeout(() => redirectController.abort(), this.timeout);
        
        finalResponse = await fetch(nextUrl, {
          method: 'GET',
          headers,
          signal: redirectController.signal,
          redirect: 'manual'
        });
        
        clearTimeout(redirectTimeoutId);
        
        // Prevent infinite redirect loops
        if (redirectChain.length > 10) {
          throw new Error('Too many redirects');
        }
      }

      // Get response content
      const content = await finalResponse.text();
      const endTime = perfHooks.now();
      const endTimeISO = new Date().toISOString();
      
      // Calculate metrics
      const loadTime = Math.round(endTime - startTime);
      const responseSize = new Blob([content]).size;
      
      // Extract response headers
      const responseHeaders: Record<string, string> = {};
      finalResponse.headers.forEach((value, key) => {
        responseHeaders[key.toLowerCase()] = value;
      });

      // Log detailed URL test results
      const urlTestLog: UrlTestLogEntry = {
        url,
        startTime: startTimeISO,
        endTime: endTimeISO,
        duration: loadTime,
        statusCode: finalResponse.status,
        finalUrl: currentUrl !== url ? currentUrl : undefined,
        redirectChain,
        responseHeaders,
        requestHeaders: { 'User-Agent': this.userAgent, ...this.config.customHeaders },
        size: responseSize,
        userAgent: this.userAgent
      };

      this.logger.logUrlTest(urlTestLog);

      // Log performance info
      if (loadTime > this.config.slowPageThreshold) {
        this.logger.warn('PERFORMANCE', `Slow page detected: ${url}`, {
          url,
          statusCode: finalResponse.status,
          loadTime,
          details: { threshold: this.config.slowPageThreshold }
        });
      }

      // Log status code issues
      if (finalResponse.status >= 400) {
        this.logger.error('HTTP_ERROR', `HTTP error ${finalResponse.status}: ${url}`, {
          url,
          statusCode: finalResponse.status,
          loadTime
        });
      } else if (finalResponse.status >= 300) {
        this.logger.info('REDIRECT', `Redirect detected: ${url}`, {
          url,
          statusCode: finalResponse.status,
          loadTime,
          details: { redirectCount: redirectChain.length - 1 }
        });
      }

      return {
        url,
        statusCode: finalResponse.status,
        loadTime,
        responseSize,
        headers: responseHeaders,
        redirectChain: redirectChain.length > 1 ? redirectChain : undefined,
        content,
        jsRendered: false
      };

    } catch (error: any) {
      const endTime = perfHooks.now();
      const endTimeISO = new Date().toISOString();
      const loadTime = Math.round(endTime - startTime);
      
      let errorMessage = 'Unknown error';
      if (error.name === 'AbortError') {
        errorMessage = `Request timeout after ${this.timeout}ms`;
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'DNS resolution failed';
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused';
      } else if (error.code === 'ECONNRESET') {
        errorMessage = 'Connection reset';
      } else {
        errorMessage = error.message || error.toString();
      }

      // Log detailed error information
      const urlTestLog: UrlTestLogEntry = {
        url,
        startTime: startTimeISO,
        endTime: endTimeISO,
        duration: loadTime,
        statusCode: 0,
        redirectChain,
        error: errorMessage,
        requestHeaders: { 'User-Agent': this.userAgent, ...this.config.customHeaders },
        size: 0,
        userAgent: this.userAgent
      };

      this.logger.logUrlTest(urlTestLog);

      // Log the error with appropriate level
      if (error.name === 'AbortError') {
        this.logger.warn('TIMEOUT', `Request timeout: ${url}`, {
          url,
          loadTime,
          error: errorMessage,
          details: { timeout: this.timeout }
        });
      } else {
        this.logger.error('REQUEST_ERROR', `Request failed: ${url}`, {
          url,
          loadTime,
          error: errorMessage,
          details: { errorCode: error.code, errorName: error.name }
        });
      }

      return {
        url,
        statusCode: 0,
        loadTime,
        responseSize: 0,
        headers: {},
        error: errorMessage
      };
    }
  }

  /**
   * Test multiple URLs concurrently with rate limiting
   */
  async testUrls(urls: string[]): Promise<UrlTestResult[]> {
    const results: UrlTestResult[] = [];
    const concurrency = this.config.maxConcurrency;
    
    this.logger.info('BATCH_TEST', `Starting batch URL testing for ${urls.length} URLs with concurrency ${concurrency}`);
    
    // Process URLs in batches to control concurrency
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      const batchNumber = Math.floor(i / concurrency) + 1;
      const totalBatches = Math.ceil(urls.length / concurrency);
      
      this.logger.debug('BATCH_PROGRESS', `Processing batch ${batchNumber}/${totalBatches} (${batch.length} URLs)`);
      this.logger.logProgress(i + batch.length, urls.length, 'URL_TESTING', `Batch ${batchNumber}/${totalBatches}`);
      
      const batchPromises = batch.map(url => this.testUrl(url));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        // Handle batch errors - some requests might still succeed
        const settledResults = await Promise.allSettled(batchPromises);
        settledResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            // Create error result for failed test
            results.push({
              url: batch[index],
              statusCode: 0,
              loadTime: 0,
              responseSize: 0,
              headers: {},
              error: `Batch processing failed: ${result.reason}`
            });
          }
        });
      }
      
      // Add delay between batches to be respectful
      if (i + concurrency < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Log batch testing summary
    const successCount = results.filter(r => r.statusCode >= 200 && r.statusCode < 400).length;
    const errorCount = results.filter(r => r.statusCode >= 400 || r.error).length;
    const avgLoadTime = results.length > 0 ? 
      results.reduce((sum, r) => sum + r.loadTime, 0) / results.length : 0;
    const slowPages = results.filter(r => r.loadTime > this.config.slowPageThreshold).length;
    const fastPages = results.filter(r => r.loadTime < 1000).length;

    this.logger.logPerformanceSummary({
      totalUrls: results.length,
      successCount,
      errorCount,
      avgLoadTime,
      slowPages,
      fastPages
    });

    return results;
  }

  /**
   * Test URL with JavaScript rendering using puppeteer
   * Note: This requires puppeteer to be installed as an optional dependency
   */
  async testUrlWithJs(url: string): Promise<UrlTestResult> {
    // First try basic URL test
    const basicResult = await this.testUrl(url);
    
    // If JS rendering is not enabled, return basic result
    if (!this.config.enableJsRendering) {
      return { ...basicResult, jsRendered: false };
    }

    try {
      // Dynamic import puppeteer to make it optional
      const puppeteer = await import('puppeteer').catch(() => null);
      
      if (!puppeteer) {
        console.warn('Puppeteer not available, falling back to basic testing');
        return { ...basicResult, jsRendered: false };
      }

      const startTime = perfHooks.now();
      
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-dev-shm-usage']
      });
      
      const page = await browser.newPage();
      
      // Set user agent and custom headers
      await page.setUserAgent(this.userAgent);
      await page.setExtraHTTPHeaders(this.config.customHeaders);
      
      // Set timeout
      page.setDefaultTimeout(this.timeout);
      
      // Navigate to page
      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: this.timeout
      });
      
      // Wait for additional JS rendering
      if (this.config.jsRenderWaitTime) {
        await page.waitForTimeout(this.config.jsRenderWaitTime);
      }
      
      // Get rendered content
      const content = await page.content();
      const endTime = perfHooks.now();
      
      await browser.close();
      
      if (!response) {
        throw new Error('No response received');
      }
      
      // Calculate metrics
      const loadTime = Math.round(endTime - startTime);
      const responseSize = new Blob([content]).size;
      
      // Get response headers
      const responseHeaders: Record<string, string> = {};
      const headers = response.headers();
      Object.keys(headers).forEach(key => {
        responseHeaders[key.toLowerCase()] = headers[key];
      });
      
      return {
        url,
        statusCode: response.status(),
        loadTime,
        responseSize,
        headers: responseHeaders,
        content,
        jsRendered: true
      };
      
    } catch (error: any) {
      console.warn(`JS rendering failed for ${url}:`, error.message);
      return { ...basicResult, jsRendered: false };
    }
  }

  /**
   * Check if a URL is slow based on configured threshold
   */
  isUrlSlow(result: UrlTestResult): boolean {
    return result.loadTime > this.config.slowPageThreshold;
  }

  /**
   * Check if a URL has errors
   */
  hasError(result: UrlTestResult): boolean {
    return result.statusCode >= 400 || !!result.error;
  }

  /**
   * Get error category for a status code
   */
  getErrorCategory(statusCode: number): string {
    if (statusCode >= 400 && statusCode < 500) {
      return 'Client Error';
    } else if (statusCode >= 500) {
      return 'Server Error';
    } else if (statusCode >= 300 && statusCode < 400) {
      return 'Redirect';
    } else if (statusCode === 0) {
      return 'Network Error';
    }
    return 'Success';
  }

  /**
   * Detect URL-related SEO issues
   */
  detectUrlIssues(urlResults: UrlTestResult[]): SeoIssue[] {
    this.logger.debug('URL_ISSUES', 'Detecting URL-related SEO issues');
    
    const allIssues: SeoIssue[] = [];

    // 1. Coverage Issues (4xx, 5xx errors)
    const coverageIssues = this.detectCoverageIssues(urlResults);
    allIssues.push(...coverageIssues);

    // 2. Redirect Issues
    const redirectIssues = this.detectRedirectIssues(urlResults);
    allIssues.push(...redirectIssues);

    // 3. Performance Issues
    const performanceIssues = this.detectPerformanceIssues(urlResults);
    allIssues.push(...performanceIssues);

    return allIssues;
  }

  /**
   * Detect coverage issues (HTTP errors)
   */
  private detectCoverageIssues(urlResults: UrlTestResult[]): SeoIssue[] {
    const issues: SeoIssue[] = [];

    urlResults.forEach(result => {
      // 4xx errors
      if (result.statusCode >= 400 && result.statusCode < 500) {
        issues.push({
          type: 'error',
          category: 'performance',
          url: result.url,
          message: `Page returns ${result.statusCode} error`,
          recommendation: this.getHttpErrorRecommendation(result.statusCode),
          details: {
            statusCode: result.statusCode,
            loadTime: result.loadTime,
            error: result.error
          }
        });
      }

      // 5xx errors
      if (result.statusCode >= 500) {
        issues.push({
          type: 'error',
          category: 'performance',
          url: result.url,
          message: `Server error: ${result.statusCode}`,
          recommendation: 'Fix server issues immediately. This prevents indexing.',
          details: {
            statusCode: result.statusCode,
            error: result.error
          }
        });
      }

      // Extremely slow responses (>10s)
      if (result.loadTime > 10000) {
        issues.push({
          type: 'warning',
          category: 'performance',
          url: result.url,
          message: `Extremely slow response time: ${result.loadTime}ms`,
          recommendation: 'Pages taking >10s to load may timeout during crawling',
          details: { 
            loadTime: result.loadTime,
            threshold: 10000
          }
        });
      }
    });

    return issues;
  }

  /**
   * Detect redirect issues
   */
  private detectRedirectIssues(urlResults: UrlTestResult[]): SeoIssue[] {
    const issues: SeoIssue[] = [];

    urlResults.forEach(result => {
      if (result.redirectChain && result.redirectChain.length > 1) {
        // Redirect chain too long
        if (result.redirectChain.length > 5) {
          issues.push({
            type: 'error',
            category: 'performance',
            url: result.url,
            message: `Redirect chain too long (${result.redirectChain.length - 1} redirects)`,
            recommendation: 'Reduce redirect chains to maximum 3 hops',
            details: { 
              redirectCount: result.redirectChain.length - 1,
              redirectChain: result.redirectChain 
            }
          });
        } else if (result.redirectChain.length > 3) {
          issues.push({
            type: 'warning',
            category: 'performance',
            url: result.url,
            message: `Multiple redirects detected (${result.redirectChain.length - 1} redirects)`,
            recommendation: 'Consider reducing redirect chains for better performance',
            details: { 
              redirectCount: result.redirectChain.length - 1,
              redirectChain: result.redirectChain 
            }
          });
        }

        // Check for redirect loops
        if (this.hasRedirectLoop(result.redirectChain)) {
          issues.push({
            type: 'error',
            category: 'performance',
            url: result.url,
            message: 'Redirect loop detected',
            recommendation: 'Fix redirect loop immediately to prevent crawl errors',
            details: { redirectChain: result.redirectChain }
          });
        }
      }

      // Check HTTP to HTTPS redirects
      if (result.url.startsWith('http://') && result.redirectChain) {
        const hasHttpsRedirect = result.redirectChain.some(url => url.startsWith('https://'));
        if (!hasHttpsRedirect) {
          issues.push({
            type: 'warning',
            category: 'accessibility',
            url: result.url,
            message: 'HTTP URL not redirecting to HTTPS',
            recommendation: 'Set up HTTP to HTTPS redirects for all pages',
            details: { protocol: 'http' }
          });
        }
      }
    });

    return issues;
  }

  /**
   * Detect performance issues
   */
  private detectPerformanceIssues(urlResults: UrlTestResult[]): SeoIssue[] {
    const issues: SeoIssue[] = [];

    urlResults.forEach(result => {
      if (result.statusCode !== 200) return;

      // Slow pages
      if (result.loadTime > this.config.slowPageThreshold) {
        issues.push({
          type: 'warning',
          category: 'performance',
          url: result.url,
          message: `Slow loading page: ${result.loadTime}ms`,
          recommendation: 'Optimize page loading speed for better Core Web Vitals',
          details: { 
            loadTime: result.loadTime,
            threshold: this.config.slowPageThreshold,
            metric: 'loading-speed'
          }
        });
      }

      // Large response size
      if (result.responseSize && result.responseSize > 5 * 1024 * 1024) { // >5MB
        issues.push({
          type: 'warning',
          category: 'performance',
          url: result.url,
          message: `Large page size: ${Math.round(result.responseSize / 1024 / 1024)}MB`,
          recommendation: 'Optimize page size by compressing images and minifying resources',
          details: { 
            responseSize: result.responseSize,
            sizeMB: Math.round(result.responseSize / 1024 / 1024),
            threshold: 5 * 1024 * 1024
          }
        });
      }
    });

    return issues;
  }

  /**
   * Check for redirect loops
   */
  private hasRedirectLoop(redirectChain: string[]): boolean {
    const seen = new Set<string>();
    for (const url of redirectChain) {
      if (seen.has(url)) return true;
      seen.add(url);
    }
    return false;
  }

  /**
   * Get HTTP error recommendation
   */
  private getHttpErrorRecommendation(statusCode: number): string {
    const recommendations: Record<number, string> = {
      404: 'Create missing content, set up redirect, or remove broken links',
      403: 'Check server permissions and access controls',
      401: 'Verify authentication requirements for public content',
      410: 'Update internal links or remove references to deleted content',
      429: 'Review rate limiting and server capacity',
      500: 'Fix server errors immediately - this blocks indexing',
      502: 'Check server configuration and upstream services',
      503: 'Ensure service availability and reduce maintenance time',
      504: 'Optimize server response time and gateway configuration'
    };
    return recommendations[statusCode] || 'Investigate and fix HTTP error';
  }
}