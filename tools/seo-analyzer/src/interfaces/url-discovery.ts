/**
 * Represents a discovered URL with metadata
 */
export interface DiscoveredUrl {
  /** The URL that was discovered */
  url: string;
  
  /** Source of URL discovery */
  source: 'sitemap' | 'crawl' | 'manual';
  
  /** Crawl depth (0 for root, 1+ for discovered links) */
  depth: number;
  
  /** Last modified date from sitemap */
  lastModified?: Date;
  
  /** Change frequency from sitemap */
  changeFreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  
  /** Priority from sitemap (0.0 to 1.0) */
  priority?: number;
}

/**
 * Service interface for URL discovery
 */
export interface UrlDiscoveryService {
  /**
   * Discover URLs from a sitemap
   */
  discoverFromSitemap(sitemapUrl: string): Promise<DiscoveredUrl[]>;
  
  /**
   * Discover URLs by crawling the website
   */
  discoverByCrawling(baseUrl: string, maxDepth: number): Promise<DiscoveredUrl[]>;
  
  /**
   * Find sitemap URLs from robots.txt and common locations
   */
  findSitemaps(baseUrl: string): Promise<string[]>;
  
  /**
   * Parse sitemap index files
   */
  parseSitemapIndex(sitemapIndexUrl: string): Promise<string[]>;
}