/**
 * Represents a robots.txt rule for a specific user agent
 */
export interface RobotsRule {
  /** User agent this rule applies to */
  userAgent: string;
  
  /** List of disallowed URL patterns */
  disallowed: string[];
  
  /** List of explicitly allowed URL patterns */
  allowed: string[];
  
  /** Crawl delay in seconds */
  crawlDelay?: number;
  
  /** Sitemap URLs found in robots.txt */
  sitemaps: string[];
}

/**
 * Service interface for robots.txt parsing and validation
 */
export interface RobotsParser {
  /**
   * Parse robots.txt content into rules
   */
  parseRobots(robotsContent: string): RobotsRule[];
  
  /**
   * Check if a URL is allowed for a specific user agent
   */
  isUrlAllowed(url: string, userAgent: string): boolean;
  
  /**
   * Get sitemap URLs from robots.txt
   */
  getSitemapsFromRobots(): string[];
  
  /**
   * Fetch and parse robots.txt from a domain
   */
  fetchAndParseRobots(baseUrl: string): Promise<RobotsRule[]>;
  
  /**
   * Validate robots.txt syntax
   */
  validateRobotsSyntax(robotsContent: string): string[];
}