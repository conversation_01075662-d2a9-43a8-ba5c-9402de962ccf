import { SeoIssue } from './seo-analyzer';
import { UrlTestResult } from './url-testing';
import { DiscoveredUrl } from './url-discovery';

/**
 * Summary statistics for the analysis
 */
export interface AnalysisSummary {
  /** Total number of URLs analyzed */
  totalUrls: number;
  
  /** Number of URLs with errors */
  errorUrls: number;
  
  /** Number of URLs with warnings */
  warningUrls: number;
  
  /** Average load time across all URLs */
  avgLoadTime: number;
  
  /** Number of slow-loading URLs */
  slowUrls: number;
  
  /** Number of 404 errors found */
  notFoundUrls: number;
  
  /** Number of robots.txt violations */
  robotsViolations: number;
  
  /** Number of SEO issues by category */
  issuesByCategory: Record<string, number>;
  
  /** Analysis duration in milliseconds */
  analysisDuration: number;
}

/**
 * Complete analysis report
 */
export interface AnalysisReport {
  /** Summary statistics */
  summary: AnalysisSummary;
  
  /** All SEO issues found */
  issues: SeoIssue[];
  
  /** URL test results */
  urlResults: UrlTestResult[];
  
  /** Discovered URLs with metadata */
  discoveredUrls: DiscoveredUrl[];
  
  /** Configuration used for analysis */
  config: import('./config').AnalyzerConfig;
  
  /** Timestamp when report was generated */
  generatedAt: Date;
  
  /** Analysis start time */
  startedAt: Date;
  
  /** Analysis end time */
  completedAt: Date;
}

/**
 * Service interface for report generation
 */
export interface ReportGenerator {
  /**
   * Generate and save reports in configured formats
   */
  generateReport(report: AnalysisReport): Promise<void>;
  
  /**
   * Export report as JSON
   */
  exportJson(report: AnalysisReport): string;
  
  /**
   * Export report as CSV
   */
  exportCsv(report: AnalysisReport): string;
  
  /**
   * Export report as HTML
   */
  exportHtml(report: AnalysisReport): string;
  
  /**
   * Generate summary statistics
   */
  generateSummary(issues: SeoIssue[], urlResults: UrlTestResult[], startTime: Date, endTime: Date): AnalysisSummary;
}