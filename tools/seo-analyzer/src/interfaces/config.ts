/**
 * Configuration interface for the SEO analyzer
 */
export interface AnalyzerConfig {
  /** Base URL of the website to analyze */
  baseUrl: string;
  
  /** Optional direct sitemap URL */
  sitemapUrl?: string;
  
  /** Maximum number of concurrent requests */
  maxConcurrency: number;
  
  /** Request timeout in milliseconds */
  requestTimeout: number;
  
  /** Threshold for slow page detection in milliseconds */
  slowPageThreshold: number;
  
  /** Maximum crawl depth for web crawling */
  maxCrawlDepth: number;
  
  /** User agent string for requests */
  userAgent: string;
  
  /** Custom headers to include in requests */
  customHeaders: Record<string, string>;
  
  /** URL patterns to exclude from analysis */
  excludePatterns: string[];
  
  /** Output formats for reports */
  outputFormats: ('json' | 'csv' | 'html')[];
  
  /** Output directory for generated reports */
  outputDir: string;
  
  /** Enable JavaScript rendering for SPAs */
  enableJsRendering?: boolean;
  
  /** Wait time for JS rendering in milliseconds */
  jsRenderWaitTime?: number;
}

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG: Partial<AnalyzerConfig> = {
  maxConcurrency: 10,
  requestTimeout: 30000,
  slowPageThreshold: 3000,
  maxCrawlDepth: 3,
  userAgent: 'SEO-Analyzer/1.0',
  customHeaders: {},
  excludePatterns: [],
  outputFormats: ['json', 'html'],
  outputDir: './seo-reports',
  enableJsRendering: false,
  jsRenderWaitTime: 5000,
};