/**
 * Represents an SEO issue found during analysis
 */
export interface SeoIssue {
  /** Issue severity level */
  type: 'error' | 'warning' | 'info';
  
  /** Category of the issue */
  category: 'accessibility' | 'performance' | 'robots' | 'i18n' | 'structure' | 'meta' | 'content' | 'llms';
  
  /** URL where the issue was found */
  url: string;
  
  /** Human-readable issue description */
  message: string;
  
  /** Actionable recommendation to fix the issue */
  recommendation: string;
  
  /** Additional context or details */
  details?: Record<string, any>;
}

/**
 * Service interface for SEO analysis
 */
export interface SeoAnalyzer {
  /**
   * Analyze a URL for SEO issues
   */
  analyzeUrl(url: string, content: string, testResult: import('./url-testing').UrlTestResult): Promise<SeoIssue[]>;
  
  /**
   * Check robots.txt compliance for a URL
   */
  checkRobotsCompliance(url: string): SeoIssue[];
  
  /**
   * Validate hreflang tags
   */
  validateHreflang(url: string, content: string): SeoIssue[];
  
  /**
   * Check canonical URL implementation
   */
  checkCanonicals(url: string, content: string): SeoIssue[];
  
  /**
   * Analyze page structure and meta tags
   */
  analyzePageStructure(url: string, content: string): SeoIssue[];
  
  /**
   * Check for multilingual URL patterns
   */
  checkMultilingualPatterns(urls: string[]): SeoIssue[];
  
  /**
   * Validate structured data
   */
  validateStructuredData(url: string, content: string): SeoIssue[];
}