/**
 * Result of testing a single URL
 */
export interface UrlTestResult {
  /** The URL that was tested */
  url: string;
  
  /** HTTP status code */
  statusCode: number;
  
  /** Load time in milliseconds */
  loadTime: number;
  
  /** Response size in bytes */
  responseSize: number;
  
  /** Response headers */
  headers: Record<string, string>;
  
  /** Error message if request failed */
  error?: string;
  
  /** Redirect chain if redirects occurred */
  redirectChain?: string[];
  
  /** Response body content (for analysis) */
  content?: string;
  
  /** Whether the page was rendered with JavaScript */
  jsRendered?: boolean;
}

/**
 * Service interface for URL testing
 */
export interface UrlTestingService {
  /**
   * Test a single URL
   */
  testUrl(url: string): Promise<UrlTestResult>;
  
  /**
   * Test multiple URLs concurrently
   */
  testUrls(urls: string[]): Promise<UrlTestResult[]>;
  
  /**
   * Test URL with JavaScript rendering
   */
  testUrlWithJs(url: string): Promise<UrlTestResult>;
}