import { AnalyzerConfig } from './interfaces/config';
import { AnalysisReport } from './interfaces/report';
import { RobotsParserImpl } from './services/robots-parser';
import { UrlDiscoveryServiceImpl } from './services/url-discovery';
import { UrlTestingServiceImpl } from './services/url-testing';
import { SeoAnalyzerImpl } from './services/seo-analyzer';
import { ReportGeneratorImpl } from './services/report-generator';
import { ComplianceChecker } from './services/compliance-checker';
import { Logger, LogLevel } from './services/logger';
import { InternationalSeoChecker } from './services/international-seo';
import { SecurityChecker } from './services/security-checker';
import { PageSpeedChecker } from './services/pagespeed-checker';

/**
 * Main SEO analyzer orchestrator
 */
export class MainSeoAnalyzer {
  private config: AnalyzerConfig;
  private logger: Logger;
  private robotsParser: RobotsParserImpl;
  private urlDiscovery: UrlDiscoveryServiceImpl;
  private urlTesting: UrlTestingServiceImpl;
  private seoAnalyzer: SeoAnalyzerImpl;
  private reportGenerator: ReportGeneratorImpl;
  private complianceChecker: ComplianceChecker;
  private internationalSeoChecker: InternationalSeoChecker;
  private securityChecker: SecurityChecker;
  private pageSpeedChecker: PageSpeedChecker;

  constructor(config: AnalyzerConfig, verbose: boolean = false) {
    this.config = config;
    
    // Initialize logger with appropriate level
    const logLevel = verbose ? LogLevel.DEBUG : LogLevel.INFO;
    this.logger = new Logger(logLevel, config.outputDir, true);
    
    // Initialize services with logger
    this.robotsParser = new RobotsParserImpl();
    this.urlDiscovery = new UrlDiscoveryServiceImpl();
    this.urlTesting = new UrlTestingServiceImpl(config, this.logger);
    this.seoAnalyzer = new SeoAnalyzerImpl(this.robotsParser, config);
    this.reportGenerator = new ReportGeneratorImpl(config);
    this.complianceChecker = new ComplianceChecker(config);
    this.internationalSeoChecker = new InternationalSeoChecker(this.logger);
    this.securityChecker = new SecurityChecker(this.logger);
    this.pageSpeedChecker = new PageSpeedChecker();
  }

  /**
   * Run complete SEO analysis
   */
  async analyze(): Promise<AnalysisReport> {
    const startTime = new Date();
    this.logger.info('ANALYSIS', `🚀 Starting comprehensive SEO analysis for ${this.config.baseUrl}`);

    // Validate configuration first
    this.validateConfig();

    try {
      // Step 1: Load robots.txt
      console.log('📋 Loading robots.txt...');
      await this.robotsParser.fetchAndParseRobots(this.config.baseUrl);
      console.log('✅ Robots.txt loaded successfully');

      // Step 2: Discover URLs
      console.log('🔍 Discovering URLs...');
      let discoveredUrls;
      
      if (this.config.sitemapUrl) {
        console.log(`📑 Using provided sitemap: ${this.config.sitemapUrl}`);
        discoveredUrls = await this.urlDiscovery.discoverFromSitemap(this.config.sitemapUrl);
      } else {
        // Try to discover sitemaps first
        const sitemaps = await this.urlDiscovery.findSitemaps(this.config.baseUrl);
        
        if (sitemaps.length > 0) {
          console.log(`📑 Found ${sitemaps.length} sitemap(s), processing...`);
          const allUrls = [];
          let validSitemapCount = 0;
          
          for (const sitemap of sitemaps) {
            try {
              const urls = await this.urlDiscovery.discoverFromSitemap(sitemap);
              allUrls.push(...urls);
              validSitemapCount++;
              console.log(`✅ Successfully processed sitemap: ${sitemap} (${urls.length} URLs)`);
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Unknown error';
              console.warn(`⚠️  Failed to process sitemap ${sitemap}: ${errorMessage}`);
              this.logger.warn('SITEMAP_ERROR', `Sitemap processing failed: ${sitemap}`, {
                sitemap,
                error: errorMessage
              });
            }
          }
          
          if (allUrls.length > 0) {
            discoveredUrls = allUrls;
            console.log(`🎯 Total URLs discovered from ${validSitemapCount}/${sitemaps.length} valid sitemaps: ${allUrls.length}`);
          } else {
            console.log('⚠️  No valid sitemaps found or all sitemaps failed, falling back to web crawling...');
            discoveredUrls = await this.urlDiscovery.discoverByCrawling(
              this.config.baseUrl,
              this.config.maxCrawlDepth
            );
          }
        } else {
          console.log('🕷️  No sitemaps found, starting web crawling...');
          discoveredUrls = await this.urlDiscovery.discoverByCrawling(
            this.config.baseUrl,
            this.config.maxCrawlDepth
          );
        }
      }

      console.log(`✅ Discovered ${discoveredUrls.length} URLs`);

      if (discoveredUrls.length === 0) {
        throw new Error('No URLs discovered for analysis');
      }

      // Step 3: Test URLs
      console.log('🧪 Testing URLs for performance and accessibility...');
      const urls = discoveredUrls.map(u => u.url);
      const urlResults = await this.urlTesting.testUrls(urls);
      console.log(`✅ Tested ${urlResults.length} URLs`);

      // Step 4: SEO analysis
      console.log('📊 Performing SEO analysis...');
      const allIssues = [];
      
      for (let i = 0; i < urlResults.length; i++) {
        const result = urlResults[i];
        
        // Show progress for every 10% of URLs processed
        if (i > 0 && i % Math.max(1, Math.floor(urlResults.length / 10)) === 0) {
          const progress = Math.round((i / urlResults.length) * 100);
          console.log(`  📈 Progress: ${progress}% (${i}/${urlResults.length} URLs analyzed)`);
        }

        if (result && result.content && result.statusCode >= 200 && result.statusCode < 300) {
          const issues = await this.seoAnalyzer.analyzeUrl(result.url, result.content, result);
          allIssues.push(...issues);
        }
      }

      // Check multilingual patterns across all URLs
      console.log('🌍 Checking multilingual patterns...');
      const multilingualIssues = this.seoAnalyzer.checkMultilingualPatterns(urls);
      allIssues.push(...multilingualIssues);

      // Step 5: URL-related Issues Detection
      this.logger.info('URL_DETECTION', '🔍 Running URL-related issue detection...');
      const urlIssues = this.urlTesting.detectUrlIssues(urlResults);
      allIssues.push(...urlIssues);

      // Step 6: International SEO Issues Detection
      this.logger.info('I18N_DETECTION', '🌍 Running international SEO detection...');
      const i18nIssues = this.internationalSeoChecker.detectInternationalIssues(urlResults);
      allIssues.push(...i18nIssues);

      // Step 7: Security Issues Detection
      this.logger.info('SECURITY_DETECTION', '🔒 Running security issue detection...');
      const securityIssues = this.securityChecker.detectSecurityIssues(urlResults, this.config.baseUrl);
      allIssues.push(...securityIssues);

      // Step 8: PageSpeed Insights Performance Check
      this.logger.info('PAGESPEED', '🚀 Running PageSpeed Insights analysis...');
      try {
        const pageSpeedIssues = await this.pageSpeedChecker.checkPageSpeed(this.config.baseUrl);
        allIssues.push(...pageSpeedIssues);
        this.logger.info('PAGESPEED', `✅ PageSpeed analysis complete (${pageSpeedIssues.length} insights)`);
      } catch (error) {
        this.logger.warn('PAGESPEED', `⚠️  PageSpeed analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Step 9: Advanced compliance checks
      this.logger.info('COMPLIANCE', '📋 Running advanced compliance checks...');
      this.logger.debug('COMPLIANCE', '  📋 Validating JSON-LD structured data...');
      this.logger.debug('COMPLIANCE', '  🗺️  Checking sitemap.xml compliance...');
      this.logger.debug('COMPLIANCE', '  🤖 Verifying robots.txt format...');
      this.logger.debug('COMPLIANCE', '  🧠 Checking llms.txt (AI crawler directives)...');
      
      const complianceIssues = await this.complianceChecker.generateComplianceReport(this.config.baseUrl, urlResults);
      allIssues.push(...complianceIssues);

      this.logger.info('ANALYSIS', `✅ SEO analysis complete. Found ${allIssues.length} total issues`);

      // Issue breakdown by category
      const issuesByType = allIssues.reduce((acc, issue) => {
        acc[issue.type] = (acc[issue.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      this.logger.info('SUMMARY', 'Issue breakdown by severity:', {
        details: issuesByType
      });

      // Step 10: Generate report
      const endTime = new Date();
      const summary = this.reportGenerator.generateSummary(allIssues, urlResults, startTime, endTime);

      const report: AnalysisReport = {
        summary,
        issues: allIssues,
        urlResults,
        discoveredUrls,
        config: this.config,
        generatedAt: new Date(),
        startedAt: startTime,
        completedAt: endTime
      };

      // Step 11: Save reports
      this.logger.info('REPORTS', '📄 Generating reports...');
      await this.reportGenerator.generateReport(report);

      // Step 12: Save detailed logs
      this.logger.info('LOGS', '💾 Saving detailed analysis logs...');
      await this.logger.saveLogs(this.config.baseUrl);

      // Step 13: Display summary
      this.displaySummary(report);

      return report;

    } catch (error) {
      console.error('❌ Analysis failed:', error instanceof Error ? error.message : error);
      throw error;
    }
  }

  /**
   * Display analysis summary to console
   */
  private displaySummary(report: AnalysisReport): void {
    const { summary } = report;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 ANALYSIS SUMMARY');
    console.log('='.repeat(60));
    console.log(`🌐 Website: ${this.config.baseUrl}`);
    console.log(`⏱️  Duration: ${Math.round(summary.analysisDuration / 1000)}s`);
    console.log(`📈 URLs Analyzed: ${summary.totalUrls}`);
    console.log('');
    
    // Status breakdown
    console.log('📊 Status Breakdown:');
    console.log(`  ✅ Success: ${summary.totalUrls - summary.errorUrls - summary.warningUrls} URLs`);
    if (summary.warningUrls > 0) {
      console.log(`  ⚠️  Warnings: ${summary.warningUrls} URLs`);
    }
    if (summary.errorUrls > 0) {
      console.log(`  ❌ Errors: ${summary.errorUrls} URLs`);
    }
    console.log('');

    // Performance metrics
    console.log('⚡ Performance Metrics:');
    console.log(`  📊 Average Load Time: ${Math.round(summary.avgLoadTime)}ms`);
    if (summary.slowUrls > 0) {
      console.log(`  🐌 Slow URLs: ${summary.slowUrls} (>${this.config.slowPageThreshold}ms)`);
    }
    if (summary.notFoundUrls > 0) {
      console.log(`  🔗 Broken Links: ${summary.notFoundUrls}`);
    }
    console.log('');

    // Issues by category
    if (Object.keys(summary.issuesByCategory).length > 0) {
      console.log('🏷️  Issues by Category:');
      Object.entries(summary.issuesByCategory)
        .sort(([,a], [,b]) => b - a) // Sort by count descending
        .forEach(([category, count]) => {
          const icon = this.getCategoryIcon(category);
          console.log(`  ${icon} ${category}: ${count} issues`);
        });
      console.log('');
    }

    // Recommendations
    if (summary.errorUrls > 0 || summary.warningUrls > 0) {
      console.log('💡 Recommendations:');
      if (summary.errorUrls > 0) {
        console.log('  🔴 Fix critical errors first (404s, server errors, missing title tags)');
      }
      if (summary.warningUrls > 0) {
        console.log('  🟡 Address warnings to improve SEO performance');
      }
      if (summary.slowUrls > 0) {
        console.log('  ⚡ Optimize slow-loading pages for better user experience');
      }
      console.log('  📄 Check the detailed HTML report for specific recommendations');
      console.log('');
    }

    console.log('📁 Reports saved to:', this.config.outputDir);
    console.log('='.repeat(60));
  }

  /**
   * Get emoji icon for issue category
   */
  private getCategoryIcon(category: string): string {
    const icons: Record<string, string> = {
      'accessibility': '♿',
      'performance': '⚡',
      'robots': '🤖',
      'i18n': '🌍',
      'structure': '🏗️',
      'meta': '📋',
      'content': '📝'
    };
    return icons[category] || '📊';
  }

  /**
   * Validate configuration before analysis
   */
  private validateConfig(): void {
    if (!this.config.baseUrl) {
      throw new Error('Base URL is required');
    }

    try {
      new URL(this.config.baseUrl);
    } catch {
      throw new Error('Invalid base URL format');
    }

    if (this.config.maxConcurrency <= 0) {
      throw new Error('Max concurrency must be greater than 0');
    }

    if (this.config.requestTimeout <= 0) {
      throw new Error('Request timeout must be greater than 0');
    }
  }

  /**
   * Set up graceful shutdown
   */
  setupGracefulShutdown(): void {
    const gracefulShutdown = (signal: string) => {
      console.log(`\n⚠️  Received ${signal}. Gracefully shutting down...`);
      console.log('📊 Analysis interrupted. Partial results may be available in output directory.');
      process.exit(0);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}