import { Command } from 'commander';
import { AnalyzerConfig } from '../interfaces/config';

/**
 * CLI argument parser for the SEO analyzer
 */
export class CliParser {
  private program: Command;

  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  /**
   * Setup CLI commands and options
   */
  private setupCommands(): void {
    this.program
      .name('seo-analyzer')
      .description('Automated website SEO analysis tool')
      .version('1.0.0');

    // Main analyze command
    this.program
      .command('analyze')
      .description('Analyze a website for SEO issues')
      .argument('<url>', 'Base URL of the website to analyze')
      .option('-c, --config <path>', 'Path to configuration file')
      .option('-s, --sitemap <url>', 'Direct sitemap URL')
      .option('--max-concurrency <number>', 'Maximum number of concurrent requests', (value) => this.parseInteger(value))
      .option('--request-timeout <number>', 'Request timeout in milliseconds', (value) => this.parseInteger(value))
      .option('--slow-page-threshold <number>', 'Threshold for slow page detection in milliseconds', (value) => this.parseInteger(value))
      .option('--max-crawl-depth <number>', 'Maximum crawl depth for web crawling', (value) => this.parseInteger(value))
      .option('--user-agent <string>', 'User agent string for requests')
      .option('--custom-headers <json>', 'Custom headers as JSON string', (value) => this.parseJson(value))
      .option('--exclude-patterns <patterns>', 'URL patterns to exclude (comma-separated)', (value) => this.parseArray(value))
      .option('--output-formats <formats>', 'Output formats (comma-separated: json,csv,html)', (value) => this.parseOutputFormats(value))
      .option('--output-dir <path>', 'Output directory for generated reports')
      .option('--enable-js-rendering', 'Enable JavaScript rendering for SPAs')
      .option('--js-render-wait-time <number>', 'Wait time for JS rendering in milliseconds', (value) => this.parseInteger(value))
      .option('--verbose', 'Enable verbose logging')
      .option('--quiet', 'Suppress non-error output')
      .action(this.handleAnalyzeCommand.bind(this));

    // Generate config command
    this.program
      .command('init')
      .description('Generate a sample configuration file')
      .option('-f, --format <format>', 'Configuration file format (json, yaml, yml)', 'json')
      .option('-o, --output <path>', 'Output path for configuration file', 'seo-analyzer.config.json')
      .action(this.handleInitCommand.bind(this));

    // Validate config command
    this.program
      .command('validate-config')
      .description('Validate a configuration file')
      .argument('<path>', 'Path to configuration file to validate')
      .action(this.handleValidateConfigCommand.bind(this));
  }

  /**
   * Parse CLI arguments and return configuration overrides
   * @param args Command line arguments
   * @returns Configuration overrides and command info
   */
  async parse(args: string[]): Promise<{
    command: string;
    baseUrl?: string;
    configPath?: string;
    configOverrides: Partial<AnalyzerConfig>;
    options: {
      verbose?: boolean;
      quiet?: boolean;
      format?: string;
      output?: string;
    };
  }> {
    // Parse the arguments
    const parsed = this.program.parse(args, { from: 'node' });
    
    // Get the command that was executed
    const commandName = parsed.args[0] || 'analyze';
    
    // Get the options from the parsed command
    let opts: any = {};
    let baseUrl: string | undefined;
    
    // Find the command that was executed and get its options
    const commands = this.program.commands;
    for (const cmd of commands) {
      if (cmd.name() === commandName) {
        opts = cmd.opts();
        baseUrl = cmd.args[0];
        break;
      }
    }
    
    // If no command was found, use the program's options
    if (Object.keys(opts).length === 0) {
      opts = this.program.opts();
    }
    
    const result: {
      command: string;
      baseUrl?: string;
      configPath?: string;
      configOverrides: Partial<AnalyzerConfig>;
      options: {
        verbose?: boolean;
        quiet?: boolean;
        format?: string;
        output?: string;
      };
    } = {
      command: commandName,
      configOverrides: this.extractConfigOverrides(opts),
      options: {},
    };

    if (baseUrl) {
      result.baseUrl = baseUrl;
    }
    if (opts.config) {
      result.configPath = opts.config;
    }
    if (opts.verbose) {
      result.options.verbose = opts.verbose;
    }
    if (opts.quiet) {
      result.options.quiet = opts.quiet;
    }
    if (opts.format) {
      result.options.format = opts.format;
    }
    if (opts.output) {
      result.options.output = opts.output;
    }

    return result;
  }

  /**
   * Handle the analyze command
   */
  private async handleAnalyzeCommand(_url: string, _options: any): Promise<void> {
    // This will be handled by the main CLI controller
    // The actual implementation will be in the main CLI file
  }

  /**
   * Handle the init command
   */
  private async handleInitCommand(_options: any): Promise<void> {
    // This will be handled by the main CLI controller
  }

  /**
   * Handle the validate-config command
   */
  private async handleValidateConfigCommand(_configPath: string): Promise<void> {
    // This will be handled by the main CLI controller
  }

  /**
   * Extract configuration overrides from CLI options
   */
  private extractConfigOverrides(options: any): Partial<AnalyzerConfig> {
    const overrides: Partial<AnalyzerConfig> = {};

    if (options.sitemap) overrides.sitemapUrl = options.sitemap;
    if (options.maxConcurrency) overrides.maxConcurrency = options.maxConcurrency;
    if (options.requestTimeout) overrides.requestTimeout = options.requestTimeout;
    if (options.slowPageThreshold) overrides.slowPageThreshold = options.slowPageThreshold;
    if (options.maxCrawlDepth) overrides.maxCrawlDepth = options.maxCrawlDepth;
    if (options.userAgent) overrides.userAgent = options.userAgent;
    if (options.customHeaders) overrides.customHeaders = options.customHeaders;
    if (options.excludePatterns) overrides.excludePatterns = options.excludePatterns;
    if (options.outputFormats) overrides.outputFormats = options.outputFormats;
    if (options.outputDir) overrides.outputDir = options.outputDir;
    if (options.enableJsRendering) overrides.enableJsRendering = options.enableJsRendering;
    if (options.jsRenderWaitTime) overrides.jsRenderWaitTime = options.jsRenderWaitTime;

    return overrides;
  }

  /**
   * Parse integer values with validation
   */
  private parseInteger(value: string): number {
    const parsed = parseInt(value, 10);
    if (isNaN(parsed) || value.includes('.')) {
      throw new Error(`Invalid integer value: ${value}`);
    }
    return parsed;
  }

  /**
   * Parse JSON string values
   */
  private parseJson(value: string): any {
    try {
      return JSON.parse(value);
    } catch (error) {
      throw new Error(`Invalid JSON value: ${value}`);
    }
  }

  /**
   * Parse comma-separated array values
   */
  private parseArray(value: string): string[] {
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
  }

  /**
   * Parse and validate output formats
   */
  private parseOutputFormats(value: string): ('json' | 'csv' | 'html')[] {
    const formats = this.parseArray(value);
    const validFormats = ['json', 'csv', 'html'];
    
    for (const format of formats) {
      if (!validFormats.includes(format)) {
        throw new Error(`Invalid output format: ${format}. Valid formats are: ${validFormats.join(', ')}`);
      }
    }
    
    return formats as ('json' | 'csv' | 'html')[];
  }

  /**
   * Display help information
   */
  showHelp(): void {
    this.program.help();
  }

  /**
   * Display version information
   */
  showVersion(): void {
    console.log(this.program.version());
  }
}

/**
 * Validation helpers for CLI arguments
 */
export class CliValidator {
  /**
   * Validate URL format
   */
  static validateUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Validate file path exists
   */
  static async validateFilePath(filePath: string): Promise<boolean> {
    const fs = await import('fs-extra');
    return fs.pathExists(filePath);
  }

  /**
   * Validate directory is writable
   */
  static async validateWritableDirectory(dirPath: string): Promise<boolean> {
    try {
      const fs = await import('fs-extra');
      const path = await import('path');
      
      await fs.ensureDir(dirPath);
      
      // Test write permissions
      const testFile = path.join(dirPath, '.write-test');
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate configuration file format
   */
  static validateConfigFormat(format: string): boolean {
    return ['json', 'yaml', 'yml'].includes(format.toLowerCase());
  }
}