#!/usr/bin/env node

import { Command } from 'commander';
import { AnalyzerConfig, DEFAULT_CONFIG } from '../interfaces/config';
import { ConfigManager } from '../services/config-manager';
import { MainSeoAnalyzer } from '../main-analyzer';
import { promises as fs } from 'fs';

/**
 * CLI interface for the SEO analyzer
 */
export class SeoAnalyzerCli {
  private program: Command;

  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  private setupCommands(): void {
    this.program
      .name('seo-analyzer')
      .description('Automated website SEO analysis tool')
      .version('1.0.0');

    this.program
      .command('analyze')
      .description('Analyze a website for SEO issues')
      .requiredOption('-u, --url <url>', 'Base URL of the website to analyze')
      .option('-s, --sitemap <url>', 'Direct sitemap URL')
      .option('-c, --config <path>', 'Configuration file path')
      .option('-o, --output <dir>', 'Output directory for reports', DEFAULT_CONFIG.outputDir)
      .option('--max-concurrency <number>', 'Maximum concurrent requests', String(DEFAULT_CONFIG.maxConcurrency))
      .option('--timeout <number>', 'Request timeout in milliseconds', String(DEFAULT_CONFIG.requestTimeout))
      .option('--slow-threshold <number>', 'Slow page threshold in milliseconds', String(DEFAULT_CONFIG.slowPageThreshold))
      .option('--max-depth <number>', 'Maximum crawl depth', String(DEFAULT_CONFIG.maxCrawlDepth))
      .option('--js-rendering', 'Enable JavaScript rendering for SPAs')
      .option('--js-wait <number>', 'Wait time for JS rendering in milliseconds', '5000')
      .option('--formats <formats>', 'Output formats (json,csv,html)', 'json,html')
      .option('--exclude <patterns>', 'URL patterns to exclude (comma-separated)')
      .option('--user-agent <agent>', 'Custom user agent string')
      .option('--verbose', 'Enable verbose output')
      .action(this.handleAnalyze.bind(this));

    this.program
      .command('config')
      .description('Generate a sample configuration file')
      .option('-o, --output <path>', 'Output path for config file', 'seo-analyzer.config.json')
      .option('--format <format>', 'Config file format (json|yaml)', 'json')
      .action(this.handleGenerateConfig.bind(this));

    this.program
      .command('validate-config')
      .description('Validate a configuration file')
      .requiredOption('-c, --config <path>', 'Configuration file path')
      .action(this.handleValidateConfig.bind(this));
  }

  private async handleAnalyze(options: any): Promise<void> {
    try {
      // Load configuration
      let config: AnalyzerConfig;
      
      if (options.config) {
        console.log(`📄 Loading configuration from ${options.config}...`);
        const fileConfig = await ConfigManager.loadFromFile(options.config);
        config = { ...DEFAULT_CONFIG, ...fileConfig } as AnalyzerConfig;
        console.log('✅ Configuration loaded successfully');
      } else {
        config = { ...DEFAULT_CONFIG } as AnalyzerConfig;
      }

      // Override config with CLI options
      config = this.applyCliOptions(config, options);

      // Validate configuration
      this.validateConfig(config);

      // Create and run analyzer
      const analyzer = new MainSeoAnalyzer(config, options.verbose);
      analyzer.setupGracefulShutdown();
      
      const startTime = Date.now();
      await analyzer.analyze();
      
      const duration = Math.round((Date.now() - startTime) / 1000);
      console.log(`\n🎉 Analysis completed successfully in ${duration}s!`);
      
    } catch (error) {
      console.error('\n❌ Analysis failed:');
      if (error instanceof Error) {
        console.error(error.message);
        if (options.verbose) {
          console.error('\nStack trace:');
          console.error(error.stack);
        }
      } else {
        console.error(error);
      }
      process.exit(1);
    }
  }

  private async handleGenerateConfig(options: any): Promise<void> {
    try {
      const config: Partial<AnalyzerConfig> = {
        ...DEFAULT_CONFIG,
        baseUrl: 'https://example.com',
        // Add some example excludePatterns and customHeaders
        excludePatterns: [
          '/admin/*',
          '/api/*',
          '*.pdf',
          '*.jpg',
          '*.png'
        ],
        customHeaders: {
          'Accept-Language': 'en-US,en;q=0.9'
        }
      };

      const filePath = options.output;
      
      if (options.format === 'yaml') {
        // If YAML format is requested, we'll need to install js-yaml
        try {
          const yaml = await import('js-yaml');
          const yamlContent = yaml.dump(config, { indent: 2 });
          await fs.writeFile(filePath.replace(/\.json$/, '.yaml'), yamlContent, 'utf8');
          console.log(`✅ YAML configuration file generated: ${filePath.replace(/\.json$/, '.yaml')}`);
        } catch (yamlError) {
          console.warn('⚠️  js-yaml not available, generating JSON instead');
          await fs.writeFile(filePath, JSON.stringify(config, null, 2), 'utf8');
          console.log(`✅ JSON configuration file generated: ${filePath}`);
        }
      } else {
        await fs.writeFile(filePath, JSON.stringify(config, null, 2), 'utf8');
        console.log(`✅ JSON configuration file generated: ${filePath}`);
      }
      
      console.log('\n📝 Edit the configuration file to customize your analysis settings.');
      console.log('💡 Run with: seo-analyzer analyze -c <config-file>');
      
    } catch (error) {
      console.error('❌ Failed to generate config:', error);
      process.exit(1);
    }
  }

  private async handleValidateConfig(options: any): Promise<void> {
    try {
      console.log(`🔍 Validating configuration: ${options.config}`);
      
      const fileConfig = await ConfigManager.loadFromFile(options.config);
      const config = { ...DEFAULT_CONFIG, ...fileConfig } as AnalyzerConfig;
      this.validateConfig(config);
      
      console.log('✅ Configuration is valid!');
      console.log('\n📊 Configuration Summary:');
      console.log(`  🌐 Base URL: ${config.baseUrl}`);
      console.log(`  ⚙️  Max Concurrency: ${config.maxConcurrency}`);
      console.log(`  ⏱️  Request Timeout: ${config.requestTimeout}ms`);
      console.log(`  📏 Slow Page Threshold: ${config.slowPageThreshold}ms`);
      console.log(`  🕷️  Max Crawl Depth: ${config.maxCrawlDepth}`);
      console.log(`  📄 Output Formats: ${config.outputFormats?.join(', ') || 'not specified'}`);
      console.log(`  📁 Output Directory: ${config.outputDir}`);
      
    } catch (error) {
      console.error('❌ Configuration validation failed:');
      console.error(error instanceof Error ? error.message : error);
      process.exit(1);
    }
  }

  private applyCliOptions(config: AnalyzerConfig, options: any): AnalyzerConfig {
    return {
      ...config,
      baseUrl: options.url,
      sitemapUrl: options.sitemap,
      outputDir: options.output || config.outputDir,
      maxConcurrency: options.maxConcurrency ? parseInt(options.maxConcurrency) : config.maxConcurrency,
      requestTimeout: options.timeout ? parseInt(options.timeout) : config.requestTimeout,
      slowPageThreshold: options.slowThreshold ? parseInt(options.slowThreshold) : config.slowPageThreshold,
      maxCrawlDepth: options.maxDepth ? parseInt(options.maxDepth) : config.maxCrawlDepth,
      enableJsRendering: options.jsRendering || config.enableJsRendering,
      jsRenderWaitTime: options.jsWait ? parseInt(options.jsWait) : (config.jsRenderWaitTime || 5000),
      outputFormats: options.formats ? options.formats.split(',').map((f: string) => f.trim()) : config.outputFormats,
      excludePatterns: options.exclude ? options.exclude.split(',').map((p: string) => p.trim()) : config.excludePatterns,
      userAgent: options.userAgent || config.userAgent,
      customHeaders: config.customHeaders || {}
    };
  }

  private validateConfig(config: AnalyzerConfig): void {
    if (!config.baseUrl) {
      throw new Error('Base URL is required');
    }

    try {
      new URL(config.baseUrl);
    } catch {
      throw new Error('Invalid base URL format');
    }

    if (config.maxConcurrency <= 0 || config.maxConcurrency > 50) {
      throw new Error('Max concurrency must be between 1 and 50');
    }

    if (config.requestTimeout <= 0 || config.requestTimeout > 300000) {
      throw new Error('Request timeout must be between 1ms and 5 minutes');
    }

    if (config.slowPageThreshold <= 0) {
      throw new Error('Slow page threshold must be greater than 0');
    }

    if (config.maxCrawlDepth < 1 || config.maxCrawlDepth > 10) {
      throw new Error('Max crawl depth must be between 1 and 10');
    }

    const validFormats = ['json', 'csv', 'html'];
    const invalidFormats = config.outputFormats.filter(f => !validFormats.includes(f));
    if (invalidFormats.length > 0) {
      throw new Error(`Invalid output formats: ${invalidFormats.join(', ')}. Valid formats: ${validFormats.join(', ')}`);
    }
  }

  public run(): void {
    this.program.parse();
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new SeoAnalyzerCli();
  cli.run();
}