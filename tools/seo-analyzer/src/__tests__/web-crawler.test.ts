import { WebCrawler } from '../services/web-crawler';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock robots parser
jest.mock('../services/robots-parser', () => ({
  RobotsParserImpl: jest.fn().mockImplementation(() => ({
    fetchAndParseRobots: jest.fn().mockResolvedValue([]),
    isUrlAllowed: jest.fn().mockReturnValue(true),
    getSitemapsFromRobots: jest.fn().mockReturnValue([])
  }))
}));

describe('WebCrawler', () => {
  let crawler: WebCrawler;
  const mockConfig = {
    maxDepth: 2,
    maxConcurrency: 2,
    requestTimeout: 5000,
    crawlDelay: 100,
    userAgent: 'Test-Crawler/1.0',
    customHeaders: {},
    excludePatterns: [],
    enableJsRendering: false,
    jsRenderWaitTime: 1000
  };

  beforeEach(() => {
    crawler = new WebCrawler(mockConfig);
    jest.clearAllMocks();
  });

  describe('crawl', () => {
    it('should crawl a simple website with internal links', async () => {
      // Mock responses
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
                <a href="/page2">Page 2</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <h1>Page 1</h1>
                <a href="/page3">Page 3</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <h1>Page 2</h1>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <h1>Page 3</h1>
              </body>
            </html>
          `
        });

      const results = await crawler.crawl('https://example.com');

      expect(results).toHaveLength(4);
      expect(results.map(r => r.url)).toContain('https://example.com');
      expect(results.map(r => r.url)).toContain('https://example.com/page1');
      expect(results.map(r => r.url)).toContain('https://example.com/page2');
      expect(results.map(r => r.url)).toContain('https://example.com/page3');

      // Check depths
      const baseUrl = results.find(r => r.url === 'https://example.com');
      const page1 = results.find(r => r.url === 'https://example.com/page1');
      const page3 = results.find(r => r.url === 'https://example.com/page3');

      expect(baseUrl?.depth).toBe(0);
      expect(page1?.depth).toBe(1);
      expect(page3?.depth).toBe(2);
    });

    it('should respect max depth limit', async () => {
      const shallowCrawler = new WebCrawler({ ...mockConfig, maxDepth: 1 });

      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <h1>Page 1</h1>
                <a href="/page2">Page 2</a>
              </body>
            </html>
          `
        });

      const results = await shallowCrawler.crawl('https://example.com');

      expect(results).toHaveLength(2);
      expect(results.map(r => r.url)).toContain('https://example.com');
      expect(results.map(r => r.url)).toContain('https://example.com/page1');
      expect(results.map(r => r.url)).not.toContain('https://example.com/page2');
    });

    it('should handle HTTP errors gracefully', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
                <a href="/page2">Page 2</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 404,
          headers: { 'content-type': 'text/html' },
          data: 'Not Found'
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <h1>Page 2</h1>
              </body>
            </html>
          `
        });

      const results = await crawler.crawl('https://example.com');

      expect(results).toHaveLength(3);
      
      // Should still include the 404 page in results
      const page1 = results.find(r => r.url === 'https://example.com/page1');
      expect(page1).toBeDefined();
    });

    it('should exclude URLs by patterns', async () => {
      const excludingCrawler = new WebCrawler({
        ...mockConfig,
        excludePatterns: ['/admin/', '\\.pdf$']
      });

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        headers: { 'content-type': 'text/html' },
        data: `
          <html>
            <body>
              <a href="/page1">Page 1</a>
              <a href="/admin/dashboard">Admin</a>
              <a href="/document.pdf">PDF</a>
            </body>
          </html>
        `
      });

      const results = await excludingCrawler.crawl('https://example.com');

      expect(results).toHaveLength(1); // Only base URL
      expect(results[0].url).toBe('https://example.com');
    });

    it('should only process HTML content', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
                <a href="/api/data">API</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <h1>Page 1</h1>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'application/json' },
          data: '{"data": "value"}'
        });

      const results = await crawler.crawl('https://example.com');

      expect(results).toHaveLength(3);
      
      // API endpoint should be included but not processed for links
      const apiResult = results.find(r => r.url === 'https://example.com/api/data');
      expect(apiResult).toBeDefined();
    });

    it('should detect JavaScript content', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        headers: { 'content-type': 'text/html' },
        data: `
          <html>
            <body>
              <div id="root"></div>
              <script src="/bundle.js"></script>
            </body>
          </html>
        `
      });

      const results = await crawler.crawl('https://example.com');
      const stats = crawler.getCrawlStats();

      expect(results).toHaveLength(1);
      expect(stats.jsRenderingNeeded).toBe(1);
    });

    it('should handle network errors', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
              </body>
            </html>
          `
        })
        .mockRejectedValueOnce(new Error('Network error'));

      const results = await crawler.crawl('https://example.com');

      expect(results).toHaveLength(2);
      
      // Should include failed URL with error
      const failedPage = results.find(r => r.url === 'https://example.com/page1');
      expect(failedPage).toBeDefined();
    });
  });

  describe('getCrawlStats', () => {
    it('should return correct statistics', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
                <a href="/page2">Page 2</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: '<html><body><h1>Page 1</h1></body></html>'
        })
        .mockResolvedValueOnce({
          status: 404,
          headers: { 'content-type': 'text/html' },
          data: 'Not Found'
        });

      await crawler.crawl('https://example.com');
      const stats = crawler.getCrawlStats();

      expect(stats.totalUrls).toBe(3);
      expect(stats.successfulUrls).toBe(2);
      expect(stats.errorUrls).toBe(1);
      expect(stats.visitedUrls).toBe(3);
    });
  });
});