import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { ConfigManager } from '../../services/config-manager';
import { CliParser } from '../../cli/cli-parser';

describe('Configuration Integration Tests', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'seo-analyzer-integration-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  it('should create complete configuration from file and CLI overrides', async () => {
    // Create a configuration file
    const configFile = {
      baseUrl: 'https://example.com',
      maxConcurrency: 5,
      outputFormats: ['json'] as ('json' | 'csv' | 'html')[]
    };
    const configPath = path.join(tempDir, 'config.json');
    await fs.writeFile(configPath, JSON.stringify(configFile));

    // Load configuration from file
    const fileConfig = await ConfigManager.loadFromFile(configPath);

    // Create CLI overrides
    const cliOverrides = {
      maxConcurrency: 10,
      requestTimeout: 25000,
      enableJsRendering: true
    };

    // Create complete configuration
    const finalConfig = ConfigManager.createConfig(fileConfig, cliOverrides);

    // Verify the configuration is properly merged
    expect(finalConfig.baseUrl).toBe('https://example.com'); // From file
    expect(finalConfig.maxConcurrency).toBe(10); // CLI override
    expect(finalConfig.requestTimeout).toBe(25000); // CLI override
    expect(finalConfig.enableJsRendering).toBe(true); // CLI override
    expect(finalConfig.outputFormats).toEqual(['json']); // From file
    expect(finalConfig.userAgent).toBeDefined(); // From defaults
  });

  it('should validate configuration and provide helpful error messages', () => {
    const invalidConfig = {
      baseUrl: 'not-a-url',
      maxConcurrency: -1,
      outputFormats: ['invalid-format'] as any
    };

    expect(() => ConfigManager.validateConfig(invalidConfig))
      .toThrow('Configuration validation failed');
  });

  it('should find configuration files in common locations', async () => {
    // Create config file in common location
    const configPath = path.join(tempDir, 'seo-analyzer.config.json');
    await fs.writeFile(configPath, '{"baseUrl": "https://example.com"}');

    const foundPath = await ConfigManager.findConfigFile(tempDir);
    expect(foundPath).toBe(configPath);
  });

  it('should save and load configuration in different formats', async () => {
    const config = ConfigManager.generateSampleConfig();

    // Save as JSON
    const jsonPath = path.join(tempDir, 'config.json');
    await ConfigManager.saveToFile(config, jsonPath, 'json');

    // Save as YAML
    const yamlPath = path.join(tempDir, 'config.yaml');
    await ConfigManager.saveToFile(config, yamlPath, 'yaml');

    // Load both and verify they're equivalent
    const loadedJson = await ConfigManager.loadFromFile(jsonPath);
    const loadedYaml = await ConfigManager.loadFromFile(yamlPath);

    expect(loadedJson.baseUrl).toBe(loadedYaml.baseUrl);
    expect(loadedJson.maxConcurrency).toBe(loadedYaml.maxConcurrency);
    expect(loadedJson.outputFormats).toEqual(loadedYaml.outputFormats);
  });

  it('should handle CLI argument parsing for configuration', async () => {
    const parser = new CliParser();
    
    // Test basic parsing
    const args = ['node', 'cli.js', 'analyze', 'https://example.com'];
    const result = await parser.parse(args);

    expect(result.command).toBe('analyze');
    expect(result.baseUrl).toBe('https://example.com');
    expect(result.configOverrides).toEqual({});
  });

  it('should validate exclude patterns correctly', () => {
    const validPatterns = ['/admin/*', '*.pdf', '/api/v*/private'];
    const invalidPatterns = ['/admin/*', '[invalid-regex', '*.pdf'];

    const validErrors = ConfigManager.validateExcludePatterns(validPatterns);
    const invalidErrors = ConfigManager.validateExcludePatterns(invalidPatterns);

    expect(validErrors).toHaveLength(0);
    expect(invalidErrors.length).toBeGreaterThan(0);
  });

  it('should validate output directory permissions', async () => {
    // Test with writable directory
    await expect(ConfigManager.validateOutputDir(tempDir))
      .resolves.not.toThrow();

    // Test with new directory creation
    const newDir = path.join(tempDir, 'new-output');
    await expect(ConfigManager.validateOutputDir(newDir))
      .resolves.not.toThrow();
    
    expect(await fs.pathExists(newDir)).toBe(true);
  });
});