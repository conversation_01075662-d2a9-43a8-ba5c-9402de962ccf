import { UrlDiscoveryServiceImpl } from '../../services/url-discovery';
import axios from 'axios';

// Mock axios for controlled testing
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock robots parser
jest.mock('../../services/robots-parser', () => ({
  RobotsParserImpl: jest.fn().mockImplementation(() => ({
    fetchAndParseRobots: jest.fn().mockResolvedValue([]),
    isUrlAllowed: jest.fn().mockReturnValue(true),
    getSitemapsFromRobots: jest.fn().mockReturnValue([])
  }))
}));

describe('UrlDiscoveryService - Web Crawling Integration', () => {
  let service: UrlDiscoveryServiceImpl;

  beforeEach(() => {
    service = new UrlDiscoveryServiceImpl();
    jest.clearAllMocks();
  });

  describe('discoverByCrawling', () => {
    it('should discover URLs through web crawling', async () => {
      // Mock the crawling responses
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>Home Page</title>
              </head>
              <body>
                <nav>
                  <a href="/about">About Us</a>
                  <a href="/services">Services</a>
                  <a href="/contact">Contact</a>
                </nav>
                <main>
                  <h1>Welcome to our website</h1>
                  <p>This is the home page.</p>
                </main>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>About Us</title>
              </head>
              <body>
                <nav>
                  <a href="/">Home</a>
                  <a href="/services">Services</a>
                  <a href="/team">Our Team</a>
                </nav>
                <main>
                  <h1>About Us</h1>
                  <p>Learn more about our company.</p>
                </main>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>Services</title>
              </head>
              <body>
                <nav>
                  <a href="/">Home</a>
                  <a href="/about">About</a>
                </nav>
                <main>
                  <h1>Our Services</h1>
                  <ul>
                    <li><a href="/services/web-design">Web Design</a></li>
                    <li><a href="/services/seo">SEO Services</a></li>
                  </ul>
                </main>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>Contact</title>
              </head>
              <body>
                <h1>Contact Us</h1>
                <p>Get in touch with us.</p>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>Our Team</title>
              </head>
              <body>
                <h1>Meet Our Team</h1>
                <p>Our talented team members.</p>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>Web Design Services</title>
              </head>
              <body>
                <h1>Web Design</h1>
                <p>Professional web design services.</p>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <head>
                <title>SEO Services</title>
              </head>
              <body>
                <h1>SEO Services</h1>
                <p>Improve your search rankings.</p>
              </body>
            </html>
          `
        });

      const results = await service.discoverByCrawling('https://example.com', 2);

      expect(results).toHaveLength(7);
      
      // Check that all URLs are discovered
      const urls = results.map(r => r.url);
      expect(urls).toContain('https://example.com');
      expect(urls).toContain('https://example.com/about');
      expect(urls).toContain('https://example.com/services');
      expect(urls).toContain('https://example.com/contact');
      expect(urls).toContain('https://example.com/team');
      expect(urls).toContain('https://example.com/services/web-design');
      expect(urls).toContain('https://example.com/services/seo');

      // Check that all results have correct source
      results.forEach(result => {
        expect(result.source).toBe('crawl');
      });

      // Check depth levels
      const homeResult = results.find(r => r.url === 'https://example.com');
      const aboutResult = results.find(r => r.url === 'https://example.com/about');
      const webDesignResult = results.find(r => r.url === 'https://example.com/services/web-design');

      expect(homeResult?.depth).toBe(0);
      expect(aboutResult?.depth).toBe(1);
      expect(webDesignResult?.depth).toBe(2);
    });

    it('should handle crawling errors gracefully', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page1">Page 1</a>
                <a href="/page2">Page 2</a>
              </body>
            </html>
          `
        })
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValueOnce({
          status: 404,
          headers: { 'content-type': 'text/html' },
          data: 'Page not found'
        });

      const results = await service.discoverByCrawling('https://example.com', 1);

      expect(results).toHaveLength(3);
      
      // Should include all URLs even if some failed
      const urls = results.map(r => r.url);
      expect(urls).toContain('https://example.com');
      expect(urls).toContain('https://example.com/page1');
      expect(urls).toContain('https://example.com/page2');
    });

    it('should respect depth limits', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/level1">Level 1</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/level2">Level 2</a>
              </body>
            </html>
          `
        });

      // Crawl with max depth of 1
      const results = await service.discoverByCrawling('https://example.com', 1);

      expect(results).toHaveLength(2);
      
      const urls = results.map(r => r.url);
      expect(urls).toContain('https://example.com');
      expect(urls).toContain('https://example.com/level1');
      expect(urls).not.toContain('https://example.com/level2');
    });

    it('should exclude external links during crawling', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        headers: { 'content-type': 'text/html' },
        data: `
          <html>
            <body>
              <a href="/internal">Internal Page</a>
              <a href="https://external.com/page">External Page</a>
              <a href="https://example.com/internal2">Internal Page 2</a>
            </body>
          </html>
        `
      });

      const results = await service.discoverByCrawling('https://example.com', 1);

      expect(results).toHaveLength(1); // Only base URL since internal pages aren't mocked
      
      // Should not attempt to crawl external URLs
      expect(mockedAxios.get).toHaveBeenCalledTimes(1);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://example.com',
        expect.any(Object)
      );
    });

    it('should handle JavaScript-heavy sites', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        headers: { 'content-type': 'text/html' },
        data: `
          <html>
            <body>
              <div id="root"></div>
              <script src="/bundle.js"></script>
              <noscript>
                <a href="/fallback">Fallback Page</a>
              </noscript>
            </body>
          </html>
        `
      });

      const results = await service.discoverByCrawling('https://example.com', 1);

      expect(results).toHaveLength(1);
      expect(results[0].url).toBe('https://example.com');
      expect(results[0].source).toBe('crawl');
    });

    it('should filter out non-HTML resources', async () => {
      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: `
            <html>
              <body>
                <a href="/page.html">HTML Page</a>
                <a href="/document.pdf">PDF Document</a>
                <a href="/image.jpg">Image</a>
                <a href="/style.css">CSS File</a>
                <a href="/about">About Page</a>
              </body>
            </html>
          `
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: '<html><body><h1>HTML Page</h1></body></html>'
        })
        .mockResolvedValueOnce({
          status: 200,
          headers: { 'content-type': 'text/html' },
          data: '<html><body><h1>About Page</h1></body></html>'
        });

      const results = await service.discoverByCrawling('https://example.com', 1);

      expect(results).toHaveLength(3);
      
      const urls = results.map(r => r.url);
      expect(urls).toContain('https://example.com');
      expect(urls).toContain('https://example.com/page.html');
      expect(urls).toContain('https://example.com/about');
      
      // Should not include non-HTML resources
      expect(urls).not.toContain('https://example.com/document.pdf');
      expect(urls).not.toContain('https://example.com/image.jpg');
      expect(urls).not.toContain('https://example.com/style.css');
    });
  });
});