import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { ConfigManager } from '../services/config-manager';
import { AnalyzerConfig, DEFAULT_CONFIG } from '../interfaces/config';

describe('ConfigManager', () => {
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'seo-analyzer-test-'));
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('loadFromFile', () => {
    it('should load JSON configuration file', async () => {
      const config = {
        baseUrl: 'https://example.com',
        maxConcurrency: 5,
        outputFormats: ['json']
      };
      const configPath = path.join(tempDir, 'config.json');
      await fs.writeFile(configPath, JSON.stringify(config));

      const result = await ConfigManager.loadFromFile(configPath);
      expect(result).toEqual(config);
    });

    it('should load YAML configuration file', async () => {
      const yamlContent = `
baseUrl: https://example.com
maxConcurrency: 5
outputFormats:
  - json
`;
      const configPath = path.join(tempDir, 'config.yaml');
      await fs.writeFile(configPath, yamlContent);

      const result = await ConfigManager.loadFromFile(configPath);
      expect(result.baseUrl).toBe('https://example.com');
      expect(result.maxConcurrency).toBe(5);
      expect(result.outputFormats).toEqual(['json']);
    });

    it('should throw error for non-existent file', async () => {
      const configPath = path.join(tempDir, 'nonexistent.json');
      
      await expect(ConfigManager.loadFromFile(configPath))
        .rejects.toThrow('Configuration file not found');
    });

    it('should throw error for invalid JSON', async () => {
      const configPath = path.join(tempDir, 'invalid.json');
      await fs.writeFile(configPath, '{ invalid json }');

      await expect(ConfigManager.loadFromFile(configPath))
        .rejects.toThrow('Failed to parse configuration file');
    });

    it('should throw error for unsupported file format', async () => {
      const configPath = path.join(tempDir, 'config.txt');
      await fs.writeFile(configPath, 'some content');

      await expect(ConfigManager.loadFromFile(configPath))
        .rejects.toThrow('Unsupported configuration file format');
    });
  });

  describe('saveToFile', () => {
    it('should save configuration as JSON', async () => {
      const config: AnalyzerConfig = {
        baseUrl: 'https://example.com',
        maxConcurrency: 5,
        requestTimeout: 30000,
        slowPageThreshold: 3000,
        maxCrawlDepth: 3,
        userAgent: 'Test Agent',
        customHeaders: {},
        excludePatterns: [],
        outputFormats: ['json'],
        outputDir: './reports'
      };
      const configPath = path.join(tempDir, 'saved-config.json');

      await ConfigManager.saveToFile(config, configPath, 'json');

      const savedContent = await fs.readFile(configPath, 'utf-8');
      const parsedConfig = JSON.parse(savedContent);
      expect(parsedConfig.baseUrl).toBe('https://example.com');
      expect(parsedConfig.maxConcurrency).toBe(5);
    });

    it('should save configuration as YAML', async () => {
      const config: AnalyzerConfig = {
        baseUrl: 'https://example.com',
        maxConcurrency: 5,
        requestTimeout: 30000,
        slowPageThreshold: 3000,
        maxCrawlDepth: 3,
        userAgent: 'Test Agent',
        customHeaders: {},
        excludePatterns: [],
        outputFormats: ['json'],
        outputDir: './reports'
      };
      const configPath = path.join(tempDir, 'saved-config.yaml');

      await ConfigManager.saveToFile(config, configPath, 'yaml');

      const savedContent = await fs.readFile(configPath, 'utf-8');
      expect(savedContent).toContain('baseUrl: https://example.com');
      expect(savedContent).toContain('maxConcurrency: 5');
    });

    it('should create directory if it does not exist', async () => {
      const config: AnalyzerConfig = {
        baseUrl: 'https://example.com',
        maxConcurrency: 5,
        requestTimeout: 30000,
        slowPageThreshold: 3000,
        maxCrawlDepth: 3,
        userAgent: 'Test Agent',
        customHeaders: {},
        excludePatterns: [],
        outputFormats: ['json'],
        outputDir: './reports'
      };
      const nestedDir = path.join(tempDir, 'nested', 'dir');
      const configPath = path.join(nestedDir, 'config.json');

      await ConfigManager.saveToFile(config, configPath, 'json');

      expect(await fs.pathExists(configPath)).toBe(true);
    });
  });

  describe('validateConfig', () => {
    it('should validate valid configuration', () => {
      const config = {
        baseUrl: 'https://example.com',
        maxConcurrency: 5,
        outputFormats: ['json'] as ('json' | 'csv' | 'html')[]
      };

      const result = ConfigManager.validateConfig(config);
      expect(result.baseUrl).toBe('https://example.com');
      expect(result.maxConcurrency).toBe(5);
      expect(result.outputFormats).toEqual(['json']);
    });

    it('should apply default values for missing fields', () => {
      const config = {
        baseUrl: 'https://example.com'
      };

      const result = ConfigManager.validateConfig(config);
      expect(result.maxConcurrency).toBe(DEFAULT_CONFIG.maxConcurrency);
      expect(result.requestTimeout).toBe(DEFAULT_CONFIG.requestTimeout);
      expect(result.userAgent).toBe(DEFAULT_CONFIG.userAgent);
    });

    it('should throw error for invalid baseUrl', () => {
      const config = {
        baseUrl: 'not-a-url'
      };

      expect(() => ConfigManager.validateConfig(config))
        .toThrow('Configuration validation failed');
    });

    it('should throw error for invalid maxConcurrency', () => {
      const config = {
        baseUrl: 'https://example.com',
        maxConcurrency: -1
      };

      expect(() => ConfigManager.validateConfig(config))
        .toThrow('Configuration validation failed');
    });

    it('should throw error for invalid outputFormats', () => {
      const config = {
        baseUrl: 'https://example.com',
        outputFormats: ['invalid-format'] as any
      };

      expect(() => ConfigManager.validateConfig(config))
        .toThrow('Configuration validation failed');
    });

    it('should validate timeout ranges', () => {
      const config = {
        baseUrl: 'https://example.com',
        requestTimeout: 500 // Too low
      };

      expect(() => ConfigManager.validateConfig(config))
        .toThrow('Configuration validation failed');
    });

    it('should validate crawl depth limits', () => {
      const config = {
        baseUrl: 'https://example.com',
        maxCrawlDepth: 15 // Too high
      };

      expect(() => ConfigManager.validateConfig(config))
        .toThrow('Configuration validation failed');
    });
  });

  describe('mergeConfigs', () => {
    it('should merge multiple configurations', () => {
      const config1 = {
        baseUrl: 'https://example.com',
        maxConcurrency: 5
      };
      const config2 = {
        maxConcurrency: 10,
        requestTimeout: 20000
      };
      const config3 = {
        outputFormats: ['json', 'html'] as ('json' | 'csv' | 'html')[]
      };

      const result = ConfigManager.mergeConfigs(config1, config2, config3);
      expect(result.baseUrl).toBe('https://example.com');
      expect(result.maxConcurrency).toBe(10); // Later config overrides
      expect(result.requestTimeout).toBe(20000);
      expect(result.outputFormats).toEqual(['json', 'html']);
    });

    it('should merge custom headers correctly', () => {
      const config1 = {
        customHeaders: { 'User-Agent': 'Test1', 'Accept': 'application/json' }
      };
      const config2 = {
        customHeaders: { 'User-Agent': 'Test2', 'Authorization': 'Bearer token' }
      };

      const result = ConfigManager.mergeConfigs(config1, config2);
      expect(result.customHeaders).toEqual({
        'User-Agent': 'Test2',
        'Accept': 'application/json',
        'Authorization': 'Bearer token'
      });
    });

    it('should merge exclude patterns correctly', () => {
      const config1 = {
        excludePatterns: ['/admin/*', '/private/*']
      };
      const config2 = {
        excludePatterns: ['*.pdf', '*.doc']
      };

      const result = ConfigManager.mergeConfigs(config1, config2);
      expect(result.excludePatterns).toEqual(['/admin/*', '/private/*', '*.pdf', '*.doc']);
    });
  });

  describe('createConfig', () => {
    it('should create complete configuration with defaults', () => {
      const fileConfig = {
        baseUrl: 'https://example.com'
      };
      const overrides = {
        maxConcurrency: 15
      };

      const result = ConfigManager.createConfig(fileConfig, overrides);
      expect(result.baseUrl).toBe('https://example.com');
      expect(result.maxConcurrency).toBe(15);
      expect(result.requestTimeout).toBe(DEFAULT_CONFIG.requestTimeout);
    });

    it('should validate the final merged configuration', () => {
      const fileConfig = {
        baseUrl: 'https://example.com'
      };
      const overrides = {
        maxConcurrency: -1 // Invalid
      };

      expect(() => ConfigManager.createConfig(fileConfig, overrides))
        .toThrow('Configuration validation failed');
    });
  });

  describe('findConfigFile', () => {
    it('should find configuration file in directory', async () => {
      const configPath = path.join(tempDir, 'seo-analyzer.config.json');
      await fs.writeFile(configPath, '{}');

      const result = await ConfigManager.findConfigFile(tempDir);
      expect(result).toBe(configPath);
    });

    it('should return null if no config file found', async () => {
      const result = await ConfigManager.findConfigFile(tempDir);
      expect(result).toBeNull();
    });

    it('should prioritize config files in correct order', async () => {
      // Create multiple config files
      await fs.writeFile(path.join(tempDir, '.seo-analyzer.json'), '{}');
      await fs.writeFile(path.join(tempDir, 'seo-analyzer.config.json'), '{}');

      const result = await ConfigManager.findConfigFile(tempDir);
      expect(result).toBe(path.join(tempDir, 'seo-analyzer.config.json'));
    });
  });

  describe('generateSampleConfig', () => {
    it('should generate valid sample configuration', () => {
      const sample = ConfigManager.generateSampleConfig();
      
      expect(sample.baseUrl).toBe('https://example.com');
      expect(sample.maxConcurrency).toBeDefined();
      expect(sample.outputFormats).toBeDefined();
      expect(Array.isArray(sample.excludePatterns)).toBe(true);
      
      // Should be valid according to schema
      expect(() => ConfigManager.validateConfig(sample)).not.toThrow();
    });
  });

  describe('validateExcludePatterns', () => {
    it('should validate correct patterns', () => {
      const patterns = ['/admin/*', '*.pdf', '/api/v*/private'];
      const errors = ConfigManager.validateExcludePatterns(patterns);
      expect(errors).toHaveLength(0);
    });

    it('should detect invalid regex patterns', () => {
      const patterns = ['/admin/*', '[invalid-regex', '*.pdf'];
      const errors = ConfigManager.validateExcludePatterns(patterns);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0]).toContain('Invalid exclude pattern');
    });
  });

  describe('validateOutputDir', () => {
    it('should validate writable directory', async () => {
      await expect(ConfigManager.validateOutputDir(tempDir))
        .resolves.not.toThrow();
    });

    it('should create directory if it does not exist', async () => {
      const newDir = path.join(tempDir, 'new-output-dir');
      
      await expect(ConfigManager.validateOutputDir(newDir))
        .resolves.not.toThrow();
      
      expect(await fs.pathExists(newDir)).toBe(true);
    });

    it('should throw error for non-writable directory', async () => {
      // Create a read-only directory (this test might be platform-specific)
      const readOnlyDir = path.join(tempDir, 'readonly');
      await fs.ensureDir(readOnlyDir);
      
      // Try to make it read-only (may not work on all systems)
      try {
        await fs.chmod(readOnlyDir, 0o444);
        
        await expect(ConfigManager.validateOutputDir(readOnlyDir))
          .rejects.toThrow('not writable');
      } catch (error) {
        // Skip this test if we can't create read-only directory
        console.warn('Skipping read-only directory test:', error);
      }
    });
  });
});