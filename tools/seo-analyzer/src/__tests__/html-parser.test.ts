import { HtmlParser } from '../services/html-parser';

describe('HtmlParser', () => {
  let parser: HtmlParser;

  beforeEach(() => {
    parser = new HtmlParser();
  });

  describe('extractLinks', () => {
    it('should extract anchor links correctly', () => {
      const html = `
        <html>
          <body>
            <a href="/page1">Page 1</a>
            <a href="https://example.com/page2" title="Page 2">Page 2</a>
            <a href="../relative/page3">Page 3</a>
          </body>
        </html>
      `;
      
      const links = parser.extractLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(3);
      expect(links[0]).toMatchObject({
        url: 'https://example.com/page1',
        originalHref: '/page1',
        text: 'Page 1',
        type: 'anchor'
      });
      expect(links[1]).toMatchObject({
        url: 'https://example.com/page2',
        originalHref: 'https://example.com/page2',
        text: 'Page 2',
        type: 'anchor'
      });
      expect(links[2]).toMatchObject({
        url: 'https://example.com/relative/page3',
        originalHref: '../relative/page3',
        text: 'Page 3',
        type: 'anchor'
      });
    });

    it('should extract canonical links', () => {
      const html = `
        <html>
          <head>
            <link rel="canonical" href="https://example.com/canonical-page">
          </head>
        </html>
      `;
      
      const links = parser.extractLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(1);
      expect(links[0]).toMatchObject({
        url: 'https://example.com/canonical-page',
        type: 'canonical',
        attributes: { rel: 'canonical' }
      });
    });

    it('should extract alternate links with hreflang', () => {
      const html = `
        <html>
          <head>
            <link rel="alternate" hreflang="es" href="https://example.com/es/page">
            <link rel="alternate" hreflang="fr" href="https://example.com/fr/page">
          </head>
        </html>
      `;
      
      const links = parser.extractLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(2);
      expect(links[0]).toMatchObject({
        url: 'https://example.com/es/page',
        type: 'alternate',
        attributes: { rel: 'alternate', hreflang: 'es' }
      });
      expect(links[1]).toMatchObject({
        url: 'https://example.com/fr/page',
        type: 'alternate',
        attributes: { rel: 'alternate', hreflang: 'fr' }
      });
    });

    it('should skip invalid links', () => {
      const html = `
        <html>
          <body>
            <a href="#fragment">Fragment</a>
            <a href="javascript:void(0)">JavaScript</a>
            <a href="mailto:<EMAIL>">Email</a>
            <a href="tel:+1234567890">Phone</a>
            <a href="data:text/plain,hello">Data URL</a>
            <a href="">Empty</a>
            <a>No href</a>
          </body>
        </html>
      `;
      
      const links = parser.extractLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(0);
    });

    it('should handle malformed HTML gracefully', () => {
      const html = '<html><body><a href="/test">Test</a><unclosed><tag>';
      
      const links = parser.extractLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(1);
      expect(links[0].url).toBe('https://example.com/test');
    });
  });

  describe('extractInternalLinks', () => {
    it('should only return internal links from same domain', () => {
      const html = `
        <html>
          <body>
            <a href="/internal1">Internal 1</a>
            <a href="https://example.com/internal2">Internal 2</a>
            <a href="https://other.com/external">External</a>
            <a href="https://subdomain.example.com/sub">Subdomain</a>
          </body>
        </html>
      `;
      
      const links = parser.extractInternalLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(2);
      expect(links).toContain('https://example.com/internal1');
      expect(links).toContain('https://example.com/internal2');
      expect(links).not.toContain('https://other.com/external');
      expect(links).not.toContain('https://subdomain.example.com/sub');
    });

    it('should exclude non-crawlable file types', () => {
      const html = `
        <html>
          <body>
            <a href="/page.html">HTML Page</a>
            <a href="/document.pdf">PDF Document</a>
            <a href="/image.jpg">Image</a>
            <a href="/style.css">CSS File</a>
            <a href="/script.js">JavaScript File</a>
            <a href="/data.json">JSON File</a>
          </body>
        </html>
      `;
      
      const links = parser.extractInternalLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(1);
      expect(links[0]).toBe('https://example.com/page.html');
    });

    it('should include pages without extensions', () => {
      const html = `
        <html>
          <body>
            <a href="/about">About</a>
            <a href="/contact/">Contact</a>
            <a href="/blog/post-1">Blog Post</a>
          </body>
        </html>
      `;
      
      const links = parser.extractInternalLinks(html, 'https://example.com/current');
      
      expect(links).toHaveLength(3);
      expect(links).toContain('https://example.com/about');
      expect(links).toContain('https://example.com/contact/');
      expect(links).toContain('https://example.com/blog/post-1');
    });
  });

  describe('detectJavaScriptContent', () => {
    it('should detect React applications', () => {
      const html = `
        <html>
          <body>
            <div id="root" data-reactroot=""></div>
            <script src="/static/js/main.bundle.js"></script>
          </body>
        </html>
      `;
      
      const needsJs = parser.detectJavaScriptContent(html);
      expect(needsJs).toBe(true);
    });

    it('should detect Vue applications', () => {
      const html = `
        <html>
          <body>
            <div id="app" data-v-123456=""></div>
            <script src="/js/app.js"></script>
          </body>
        </html>
      `;
      
      const needsJs = parser.detectJavaScriptContent(html);
      expect(needsJs).toBe(true);
    });

    it('should detect Angular applications', () => {
      const html = `
        <html>
          <body>
            <app-root ng-version="15.0.0"></app-root>
            <script src="/main.js"></script>
          </body>
        </html>
      `;
      
      const needsJs = parser.detectJavaScriptContent(html);
      expect(needsJs).toBe(true);
    });

    it('should detect apps with little content but many scripts', () => {
      const html = `
        <html>
          <body>
            <div>Loading...</div>
            <script src="/bundle1.js"></script>
            <script src="/bundle2.js"></script>
            <script src="/bundle3.js"></script>
          </body>
        </html>
      `;
      
      const needsJs = parser.detectJavaScriptContent(html);
      expect(needsJs).toBe(true);
    });

    it('should not detect JS for regular HTML pages', () => {
      const html = `
        <html>
          <body>
            <h1>Welcome to our website</h1>
            <p>This is a regular HTML page with lots of content that doesn't require JavaScript to render properly.</p>
            <nav>
              <a href="/about">About</a>
              <a href="/contact">Contact</a>
            </nav>
            <script>
              // Simple analytics script
              console.log('Page loaded');
            </script>
          </body>
        </html>
      `;
      
      const needsJs = parser.detectJavaScriptContent(html);
      expect(needsJs).toBe(false);
    });

    it('should handle malformed HTML gracefully', () => {
      const html = '<html><body><div id="root"><unclosed><tag>';
      
      const needsJs = parser.detectJavaScriptContent(html);
      expect(needsJs).toBe(true); // Should detect the root div
    });
  });
});