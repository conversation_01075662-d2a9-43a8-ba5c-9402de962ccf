{"name": "web-AiMCP-tools", "version": "1.0.0", "description": "Command-line tools for web-AiMCP MCP platform", "private": true, "scripts": {"backup": "ts-node backup/backup-mcps.ts && ts-node backup/copy-reference.ts", "submit-via-csv": "ts-node import/import-submit-mcps-from-csv.ts", "update-via-csv": "ts-node import/import-update-mcps-from-csv.ts", "process-submissions": "ts-node process/process-submissions.ts --all --translate", "extract-tools": "ts-node process/extract-mcp-tools.ts", "extract-all-tools": "ts-node process/extract-mcp-tools.ts --all", "update-mcps": "ts-node process/update-mcps.ts", "update-translations": "ts-node process/update-mcps.ts --translations-only --concurrency=5", "update-github-meta": "ts-node process/update-mcps.ts --github-only --concurrency=3", "fetch-mcp-tools": "ts-node process/extract-mcp-tools.ts --all --concurrency=5", "translate:locale": "ts-node translate/translate-locale.ts --locale", "translate:blogs": "ts-node translate/translate-blogs.ts --blogs", "seo:enhance-word": "ts-node seo/enhance-word-density.ts", "seo:generate-website": "ts-node seo/website-generator.ts", "approve-mcps": "ts-node approve/approve-mcps.ts", "fetch-mcps": "ts-node scraper/fetch_awesome_mcps.ts", "update-embeddings": "ts-node process/update-embeddings.ts", "auto-process": "bash process-mcps.sh", "find-abnormal-mcps": "ts-node scripts/find-abnormal-mcps.ts", "i18n:check": "python3 scripts/manage_i18n.py check ../nextjs ../nextjs/i18n/messages", "i18n:sync": "python3 scripts/manage_i18n.py sync ../nextjs ../nextjs/i18n/messages --update-reference", "i18n:translate": "python3 scripts/manage_i18n.py translate ../nextjs ../nextjs/i18n/messages", "i18n:full": "python3 scripts/manage_i18n.py full ../nextjs ../nextjs/i18n/messages", "components:check": "python3 scripts/component_check.py", "components:cleanup": "python3 scripts/cleanup_unused_components.py ../nextjs --dry-run", "components:delete": "python3 scripts/cleanup_unused_components.py ../nextjs --delete", "mcp:test": "ts-node mcp-test.ts", "convert-icon": "bash scripts/svg-to-favicon.sh", "start-new": "python3 setup_new_project.py", "seo:analyze": "ts-node seo-analyzer/seo-analyzer-cli.ts analyze", "seo:test": "ts-node seo-analyzer/seo-analyzer-cli.ts analyze --url=https://mybacklinks.app --verbose", "seo:test-local": "ts-node seo-analyzer/seo-analyzer-cli.ts analyze --url=http://localhost:3000 --verbose"}, "dependencies": {"@ai-sdk/openai": "^1.3.9", "@octokit/rest": "^20.0.2", "@supabase/supabase-js": "^2.47.0", "@types/cheerio": "^0.22.35", "@types/eventsource": "^3.0.0", "ai": "^4.0.0", "axios": "^1.6.2", "cheerio": "^1.0.0", "csv-parse": "^5.5.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "eventsource": "^3.0.6", "gray-matter": "^4.0.3", "hono": "^4.7.8", "node-fetch": "^3.3.2", "react-tweet": "^3.2.1", "sharp": "^0.34.2", "ts-node": "^10.9.1", "uuid": "^11.0.0"}, "devDependencies": {"@iarna/toml": "^2.2.5", "@types/axios": "^0.14.0", "@types/node": "^20.8.0", "@types/uuid": "^9.0.6", "typescript": "^5.2.2"}}