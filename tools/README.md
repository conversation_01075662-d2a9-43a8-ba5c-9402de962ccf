# Tools

## Backup Tools
```bash
# Create a backup of all MCPs, localizations, and submissions
# Create reference files for comparison (used by other flows to check duplicates)
pnpm run backup
```

## Import

### Import to update via csv
```bash
# Import MCPs from a CSV file
pnpm run update-csv path/to/file.csv
# Import and mark as publicly visible when processed
pnpm run update-csv path/to/file.csv --allow-public
# Specify the number of concurrent requests (default: 1)
# 根据现有的 website_url 获取github信息、交给AI解析、生成MCP并提交到数据库
# 1）没有翻译为多语言
# 2）如果原本存在uuid，则为更新，否则为新增
pnpm run update-csv path/to/file.csv --concurrency=5
```

### Import to submit via csv
```bash
# Import MCPs from a CSV file
pnpm run submit-csv path/to/file.csv
# Specify the number of concurrent requests (default: 1)
# 根据现有的 website_url 获取github信息，提交到submit表，等待人工审核
pnpm run submit-csv path/to/file.csv --concurrency=5
```

## Process

### Process Submissions
```bash
# Process all pending submissions
pnpm run process-submissions --all
# Process and translate content to supported languages
# 1）根据现有的 website_url 获取github信息、交给AI解析、生成MCP并提交到数据库
# 2）翻译为多语言
pnpm run process-submissions --all --translate
```

### Update MCPs
```bash
# Update MCPs from a CSV file
pnpm run update-mcps path/to/file.csv
# 只更新翻译信息
pnpm run update-translations
pnpm run update-mcps path/to/file.csv --translations-only
# 只更新github meta信息（forks、stars等）
pnpm run update-github-meta
pnpm run update-mcps path/to/file.csv --github-only
# Specify the number of concurrent requests (default: 1)
# 1）根据数据库中已有的uuid更新MCP
# 2）包括提取github meta信息、多语言翻译、更新数据库等
pnpm run update-mcps path/to/file.csv --concurrency=5
```

## Translate
```bash
# Lingui i18n多语言翻译，未使用
pnpm run translate:locale
# 翻译博客文章，未使用
pnpm run translate:blogs
```

## SEO
```bash
# 生成网站内容
pnpm run seo:generate-website
# 增强单词密度
pnpm run seo:enhance-word
```

## MCP Submission Workflow

```bash
### Flow 001: Backup
pnpm run backup

### Flow 002: Import MCPs from CSV
# Import MCPs from a CSV file
pnpm run import-csv path/to/file.csv --max-concurrency 5

### Flow 003: Process Submissions
# Process all pending submissions
npm run process-submissions --all

### Flow 004: Approve MCPs
# Approve all processed MCPs
npm run approve-mcps --all
```

### Automated Workflow (Flows 001-004)

For convenience, you can run all four steps automatically using the provided shell script:

```bash
# Run the automated workflow with a CSV file
pnpm run auto-process path/to/file.csv

# Include translation of content
pnpm run auto-process path/to/file.csv --translate

# Make MCPs publicly visible
pnpm run auto-process path/to/file.csv --allow-public

# Both options
pnpm run auto-process path/to/file.csv --translate --allow-public
```

This script will:
1. Backup the database and create reference files
2. Import MCPs from the provided CSV file
3. Process all pending submissions
4. Approve all processed MCPs