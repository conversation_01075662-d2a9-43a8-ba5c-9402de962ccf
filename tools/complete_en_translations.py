#!/usr/bin/env python3
"""
Complete all missing English translations in en.json
"""

import json
from pathlib import Path
from collections import OrderedDict

# Read the current en.json
en_path = Path("/Users/<USER>/Code/LinkTrackPro/nextjs/i18n/messages/en.json")

with open(en_path, 'r', encoding='utf-8') as f:
    data = json.load(f, object_pairs_hook=OrderedDict)

# Complete all missing translations
def fill_missing_translations(data):
    """Fill all empty string values with appropriate English translations"""
    
    # User section
    data["user"]["sign_in"] = "Sign In"
    data["user"]["sign_out"] = "Sign Out" 
    data["user"]["my_orders"] = "My Orders"
    data["user"]["no_auth_or_email"] = "No authentication or email found"
    
    # Task and file management
    data["task_succeeded"] = "Task completed successfully"
    data["file"] = "File"
    
    # Status section
    data["status"]["online"] = "Online"
    data["status"]["view_pending"] = "View Pending"
    data["status"]["created"] = "Created"
    data["status"]["no_pending"] = "No pending items"
    data["status"]["error"] = "Error"
    data["status"]["sent"] = "Sent"
    data["status"]["title"] = "Status"
    data["status"]["failed"] = "Failed"
    data["status"]["active"] = "Active"
    data["status"]["description"] = "Description"
    data["status"]["inactive"] = "Inactive"
    data["status"]["pending"] = "Pending"
    data["status"]["offline"] = "Offline"
    
    # Link type section
    data["link_type"] = OrderedDict({
        "paid": "Paid",
        "free": "Free"
    })
    
    # General UI elements
    data["user_id"] = "User ID"
    data["historyLimit"] = "History Limit"
    data["withStats"] = "With Statistics"
    data["action"] = "Action"
    
    # Table section
    data["table"]["created_at"] = "Created At"
    data["table"]["id"] = "ID"
    data["table"]["actions"] = "Actions"
    data["table"]["name"] = "Name"
    data["table"]["updated_at"] = "Updated At"
    data["table"]["event_type"] = "Event Type"
    data["table"]["status"] = "Status"
    data["table"]["sent_at"] = "Sent At"
    data["table"]["tools_count"] = "Tools Count"
    data["table"]["type"] = "Type"
    data["table"]["recipient"] = "Recipient"
    data["table"]["subject"] = "Subject"
    data["table"]["item_uuid"] = "Item UUID"
    
    # Results and UI states
    data["no_image_result"] = "No image result"
    data["no_logs"] = "No logs available"
    data["no_templates"] = "No templates available"
    data["category"] = "Category"
    data["result_image"] = "Result Image"
    data["cancel_button"] = "Cancel"
    data["completed_at"] = "Completed At"
    data["error_reason"] = "Error Reason"
    data["my_tasks"] = "My Tasks"
    data["T"] = "T"
    data["update_error"] = "Update Error"
    data["saved_success"] = "Saved Successfully"
    data["create_error"] = "Create Error"
    
    # Details section
    data["details"] = OrderedDict({
        "requirements": "Requirements",
        "responseTime": "Response Time",
        "priceRange": "Price Range",
        "submissionMethod": "Submission Method",
        "successRate": "Success Rate"
    })
    
    # More UI elements
    data["selected_items"] = "Selected Items"
    data["no_json_result"] = "No JSON result"
    data["searchTerm"] = "Search Term"
    data["task_status"] = "Task Status"
    data["type"] = "Type"
    
    # Empty section
    data["empty"]["no_tools"] = "No tools available"
    data["empty"]["no_parameters"] = "No parameters available"
    data["empty"]["no_tools_to_translate"] = "No tools to translate"
    
    # Navigation and pagination
    data["prev_page"] = "Previous Page"
    data["error_loading"] = "Error Loading"
    data["provider"] = "Provider"
    data["content"] = "Content"
    data["search"] = "Search"
    data["task_processing"] = "Task Processing"
    data["projectId"] = "Project ID"
    data["create_success"] = "Created Successfully"
    data["div"] = "Division"
    data["my_tasks_description"] = "My Tasks Description"
    data["print_result"] = "Print Result"
    data["slug"] = "Slug"
    data["a"] = "Link"
    data["brief"] = "Brief"
    data["test"] = "Test"
    data["sort"] = "Sort"
    data["timeRange"] = "Time Range"
    data["query"] = "Query"
    data["author_avatar_url"] = "Author Avatar URL"
    data["saved"] = "Saved"
    data["tab_json"] = "JSON Tab"
    data["saved_error"] = "Save Error"
    data["url"] = "URL"
    data["update_success"] = "Updated Successfully"
    data["tip"] = "Tip"
    data["host"] = "Host"
    data["task_view"] = "Task View"
    data["processinfo"] = "Process Information"
    data["isPaid"] = "Is Paid"
    data["q"] = "Question"
    data["is_recommended"] = "Is Recommended"
    data["checked_at"] = "Checked At"
    data["page"] = "Page"
    data["keywords"] = "Keywords"
    data["video_urls"] = "Video URLs"
    data["uuid"] = "UUID"
    data["tab_image"] = "Image Tab"
    data["sessionId"] = "Session ID"
    data["no_tasks"] = "No tasks available"
    data["goHome"] = "Go Home"
    data["task_date"] = "Task Date"
    data["current"] = "Current"
    data["image_urls"] = "Image URLs"
    data["create_button"] = "Create"
    data["tab_text"] = "Text Tab"
    data["processing_time"] = "Processing Time"
    data["task_credit_cost"] = "Task Credit Cost"
    data["cover_url"] = "Cover URL"
    data["save"] = "Save"
    data["Authorization"] = "Authorization"
    data["signin_type"] = "Sign In Type"
    data["invite_code"] = "Invite Code"
    data["lang"] = "Language"
    data["view_task_result"] = "View Task Result"
    data["refresh_button"] = "Refresh"
    data["back_to_submissions"] = "Back to Submissions"
    data["results_title"] = "Results"
    data["saving"] = "Saving..."
    data["USD"] = "USD"
    data["offset"] = "Offset"
    data["website_url"] = "Website URL"
    data["task_product"] = "Task Product"
    data["tagName"] = "Tag Name"
    data["task_pending"] = "Task Pending"
    data["stdio"] = "Standard I/O"
    data["sse"] = "Server-Sent Events"
    data["includeHistory"] = "Include History"
    data["is_official"] = "Is Official"
    data["task_result"] = "Task Result"
    data["title"] = "Title"
    data["limit"] = "Limit"
    data["process_button"] = "Process"
    data["next_page"] = "Next Page"
    data["author_name"] = "Author Name"
    data["locale"] = "Locale"
    data["update_button"] = "Update"
    data["description"] = "Description"
    data["field"] = "Field"
    data["api_key"] = "API Key"
    data["ai_summary"] = "AI Summary"
    data["domain"] = "Domain"
    data["task_failed_status"] = "Task Failed"
    data["no_text_result"] = "No text result"
    
    # Invitation section
    data["invitation"]["invites_count"] = "Invites Count"
    data["invitation"]["earn_credits"] = "Earn Credits"
    data["invitation"]["share_title"] = "Share Invitation"
    data["invitation"]["credits_per_invite"] = "Credits per Invite"
    data["invitation"]["copy"] = "Copy"
    data["invitation"]["share_text"] = "Share this invitation link with your friends!"
    data["invitation"]["copy_success"] = "Copied to clipboard!"
    data["invitation"]["how_it_works"] = "How It Works"
    data["invitation"]["your_code"] = "Your Code"
    data["invitation"]["copying"] = "Copying..."
    data["invitation"]["share_success"] = "Shared successfully!"
    data["invitation"]["share_description"] = "Invite friends and earn credits"
    data["invitation"]["share_code"] = "Share Code"
    data["invitation"]["title"] = "Invitation System"
    data["invitation"]["share_invite_link"] = "Share Invite Link"
    data["invitation"]["copy_failed"] = "Copy failed"
    data["invitation"]["share"] = "Share"
    data["invitation"]["share_failed"] = "Share failed"
    
    # Editor section
    data["editor"]["result_image"] = "Result Image"
    data["editor"]["copied_json"] = "JSON copied to clipboard!"
    data["editor"]["results_title"] = "Results"
    
    # Links section
    data["links"]["traffic"] = "Traffic"
    data["links"]["title"] = "Links"
    
    # SubmitPage section
    data["SubmitPage"]["heading"] = "Submit Your Resource"
    data["SubmitPage"]["home"] = "Home"
    data["SubmitPage"]["submit"] = "Submit"
    data["SubmitPage"]["title"] = "Submit Page"
    
    # SearchPage section
    data["SearchPage"]["search"] = "Search"
    data["SearchPage"]["title"] = "Search"
    
    # Integrations
    data["integrations"] = "Integrations"
    
    return data

# Apply the translations
data = fill_missing_translations(data)

# Save the updated file
with open(en_path, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
    f.write('\n')

print("✅ Successfully completed all missing English translations in en.json")
print("📊 Added translations for:")
print("   • Status indicators and UI states")
print("   • Table headers and data fields")
print("   • Navigation and pagination elements")
print("   • Task management and processing")
print("   • User interface components")
print("   • Form elements and actions")
print("   • Error messages and notifications")
print("   • Editor and content management")
print("   • Invitation system")
print("   • Links and traffic data")
print("   • Search and submit pages")
print("   • General system terminology")