# 域名和项目自动关联功能

本文档说明了域名管理和项目管理之间的自动关联功能实现。

## 功能概述

1. **项目创建时自动添加域名**: 当创建新项目时，系统会自动提取项目域名的顶级域名，并添加到域名管理中
2. **域名添加时自动发现项目**: 当添加域名时，系统会主动搜索用户的相关项目并自动建立关联
3. **域名列表自动更新项目关联**: 获取域名列表时自动检查和更新项目关联关系
4. **域名数据自动关联项目显示**: 域名管理界面会自动显示与该域名关联的所有项目信息

## 实现的功能

### 1. 项目创建自动添加域名 (`/api/projects/route.ts`)

**触发时机**: 用户创建新项目时

**实现逻辑**:
```typescript
// 提取顶级域名
const topLevelDomain = extractTopLevelDomain(domain);

// 验证域名格式 (只允许顶级域名，例如: thistools.app)
if (topLevelDomain && validateDomainFormat(topLevelDomain)) {
  // 检查域名是否已存在
  const existingDomain = existingDomains.find(d => d.domain === topLevelDomain);
  
  if (existingDomain) {
    // 域名已存在，直接关联项目
    await DomainModel.associateDomainWithProject(userUuid, existingDomain.id, project.id, true);
  } else {
    // 创建新域名记录
    const domainData = await DomainModel.createOrUpdateDomain(userUuid, {
      domain: topLevelDomain,
      notes: `Auto-created from project: ${body.name || domain}`
    });
    
    // 关联项目
    await DomainModel.associateDomainWithProject(userUuid, domainData.id, project.id, true);
    
    // 后台获取WHOIS数据
    // ...
  }
}
```

### 2. 域名添加时自动发现项目 (**新增功能**)

**文件位置**: 
- API: `nextjs/app/api/domain/management/route.ts`
- 模型: `nextjs/models/domain.ts` - `DomainModel.discoverAndAssociateRelatedProjects()`

**触发时机**: 用户手动添加域名时

**实现逻辑**:
```typescript
// 自动发现和关联相关项目
const relatedProjectsResult = await DomainModel.discoverAndAssociateRelatedProjects(
  user.uuid, 
  normalizedDomain, 
  newDomain.id
);

// 实现步骤：
1. 获取用户的所有项目
2. 遍历每个项目，提取其顶级域名
3. 比较项目的顶级域名与添加的域名
4. 如果匹配且未关联，自动建立关联关系
5. 返回关联结果统计
```

**示例场景**:
```
用户添加域名: thistools.app
系统自动发现并关联:
- ✅ stripmd.thistools.app (项目名: StripMD)
- ✅ tools.thistools.app (项目名: Tools Dashboard)
- ✅ api.thistools.app (项目名: API Gateway)

结果: 自动关联了 3 个相关项目
```

### 3. 域名列表自动更新项目关联 (**新增功能**)

**文件位置**: `nextjs/models/domain.ts` - `DomainModel.getUserDomains()`

**触发时机**: 用户访问域名管理页面或刷新域名列表时

**实现逻辑**:
```typescript
// 获取域名列表时自动更新项目关联
static async getUserDomains(userId: string, filters?: DomainFilters, autoAssociate: boolean = true): Promise<DomainInfo[]> {
  // 带节流的自动关联更新
  if (autoAssociate) {
    await this.updateAllDomainProjectAssociationsThrottled(userId);
  }
  
  // 获取更新后的域名列表
  // ...
}
```

**节流机制**:
- 每个用户的关联更新操作有5分钟的节流限制
- 避免频繁的数据库查询和关联操作
- 在内存中缓存上次更新时间

**实现步骤**:
1. 检查用户上次关联更新时间
2. 如果超过节流时间，执行关联更新
3. 遍历用户的所有域名和项目
4. 自动发现新的匹配关系并建立关联
5. 返回包含最新关联信息的域名列表

**应用场景**:
```
用户场景: 用户创建了新项目，然后查看域名管理页面
系统行为:
1. ✅ 检测到需要更新关联（超过5分钟）
2. ✅ 扫描所有域名和项目的匹配关系
3. ✅ 自动关联新发现的项目
4. ✅ 显示更新后的域名列表和项目计数
5. ✅ 记录更新时间，5分钟内不再重复检查
```

### 4. 智能域名匹配算法

**实现位置**: `nextjs/models/domain.ts` - `extractTopLevelDomainFromHostname()`

**匹配逻辑**:
- 标准化处理: 移除 www 前缀，统一小写
- 顶级域名提取: 从 `sub.domain.com` 提取 `domain.com`
- 容错处理: 支持带协议和不带协议的URL
- 避免重复: 检查已有关联避免重复创建

### 5. 用户界面增强

**域名管理界面** (`nextjs/components/blocks/link-dashboard/domain-management.tsx`):

**新增功能**:
- 智能提示: 添加域名后显示自动关联的项目数量和名称
- 详细反馈: 用户可以看到具体关联了哪些项目
- 实时更新: 关联完成后自动刷新域名列表

**提示消息示例**:
```
✅ Domain added successfully and automatically linked to 3 project(s): StripMD, Tools Dashboard, API Gateway
```

## 数据库关联更新

### 自动关联流程

```sql
-- 1. 创建域名记录
INSERT INTO domain_management (domain, user_id, ...) VALUES (?, ?, ...);

-- 2. 查找相关项目
SELECT id, name, domain FROM projects WHERE user_id = ?;

-- 3. 匹配和关联
FOR EACH project WHERE extract_tld(project.domain) = added_domain:
  INSERT INTO domain_project_associations (domain_id, project_id, user_id, is_primary)
  VALUES (?, ?, ?, false);
```

### 关联状态标识

- `is_primary: true` - 从项目创建时自动关联的主域名
- `is_primary: false` - 从域名添加时自动发现的关联

## 技术实现亮点

### 1. 智能域名解析
```typescript
// 处理各种URL格式
const formats = [
  'https://stripmd.thistools.app',
  'stripmd.thistools.app',
  'www.stripmd.thistools.app'
];
// 统一提取为: thistools.app
```

### 2. 防重复关联
```typescript
// 检查现有关联避免重复
const { data: existingAssociation } = await supabase
  .from('domain_project_associations')
  .select('id')
  .eq('domain_id', domainId)
  .eq('project_id', project.id)
  .single();
```

### 3. 错误隔离
- 项目创建时域名失败不影响项目创建
- 域名关联失败不影响域名添加
- 单个项目关联失败不影响其他项目关联

### 4. 异步优化
- WHOIS 数据异步获取，不阻塞用户操作
- 批量项目关联使用事务保证一致性

## 使用示例

### 场景1: 添加已有项目的域名

```
用户操作: 手动添加域名 "thistools.app"
系统自动行为:
1. ✅ 创建域名记录
2. ✅ 扫描用户的所有项目
3. ✅ 发现匹配项目:
   - stripmd.thistools.app → StripMD 项目
   - api.thistools.app → API Gateway 项目
4. ✅ 自动建立关联关系
5. ✅ 显示关联结果: "自动关联了 2 个项目"
6. ✅ 后台获取 WHOIS 信息
```

### 场景2: 创建项目后再添加域名

```
用户操作1: 创建项目 "blog.example.com"
系统行为: 自动创建域名 "example.com" 并关联

用户操作2: 手动添加域名 "example.com" (已存在)
系统行为: 
1. 检测到域名已存在
2. 扫描发现更多相关项目
3. 自动关联新发现的项目
4. 更新项目关联计数
```

## 监控和日志

### 关键日志记录
```
Auto-associated 3 projects with domain thistools.app
Associated project {projectId} with existing domain {domain}
Error discovering related projects: {error}
```

### 数据一致性检查
- 定期验证关联关系的有效性
- 清理无效的项目引用
- 统计域名关联项目数量的准确性

## 配置要求

```env
# WHOIS服务配置 (用于后台获取域名信息)
BACKEND_WORKER_URL=https://backend.mybacklinks.app
CRON_WORKER_AUTH_TOKEN=your_auth_token
```

## 性能优化

### 数据库优化
- 在 `domain_project_associations` 表上建立复合索引
- 使用数据库视图 `domain_management_with_projects` 预聚合关联数据
- 项目查询使用用户ID索引提高性能

### 前端优化
- 异步处理避免界面阻塞
- 智能缓存减少重复请求
- 分页加载大量域名数据

## 未来扩展计划

1. **批量域名导入时的项目批量关联**
2. **项目域名变更时的自动重新关联**
3. **域名过期提醒包含关联项目信息**
4. **关联项目的统计数据聚合显示**
5. **跨用户的域名项目关联分析（管理员功能）**

## 总结

新的域名和项目自动关联功能实现了：

✅ **双向自动关联**: 项目创建→域名添加 + 域名添加→项目发现  
✅ **智能匹配算法**: 准确提取和比较顶级域名  
✅ **防重复机制**: 避免重复关联和数据冗余  
✅ **用户友好界面**: 清晰的操作反馈和结果展示  
✅ **错误隔离**: 保证核心功能的稳定性  
✅ **性能优化**: 异步处理和数据库优化  

这个功能大大提升了用户体验，让域名和项目管理更加智能和自动化。 