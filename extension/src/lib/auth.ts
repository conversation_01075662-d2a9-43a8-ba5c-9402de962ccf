import { Storage } from "@plasmohq/storage";

const storage = new Storage();

export interface AuthState {
  apiKey: string | null;
  isAuthenticated: boolean;
  isGuestMode: boolean;
  user: {
    uuid: string;
    email: string;
    nickname: string;
  } | null;
}

export class ExtensionAuth {
  private static instance: ExtensionAuth;
  private baseUrl = "https://mybacklinks.app";
  
  static getInstance(): ExtensionAuth {
    if (!ExtensionAuth.instance) {
      ExtensionAuth.instance = new ExtensionAuth();
    }
    return ExtensionAuth.instance;
  }

  async getApiKey(): Promise<string | null> {
    return await storage.get("apiKey");
  }

  async setApiKey(apiKey: string): Promise<void> {
    await storage.set("apiKey", apiKey);
  }

  async removeApiKey(): Promise<void> {
    await storage.remove("apiKey");
    await storage.remove("user");
  }

  async validateApiKey(apiKey: string): Promise<AuthState> {
    try {
      const response = await fetch(`${this.baseUrl}/api/extension/auth`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({ action: "validate" })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Store user data
        await storage.set("user", data.user);
        await this.setApiKey(apiKey);
        
        return {
          apiKey,
          isAuthenticated: true,
          isGuestMode: false,
          user: data.user
        };
      } else {
        throw new Error(data.message || "Invalid API key");
      }
    } catch (error) {
      console.error("API key validation failed:", error);
      return {
        apiKey: null,
        isAuthenticated: false,
        isGuestMode: false,
        user: null
      };
    }
  }

  async getCurrentAuthState(): Promise<AuthState> {
    const apiKey = await this.getApiKey();
    const user = await storage.get("user");
    const guestMode = await storage.get("guestMode");
    
    if (guestMode) {
      return {
        apiKey: null,
        isAuthenticated: false,
        isGuestMode: true,
        user: null
      };
    }
    
    if (!apiKey || !user) {
      return {
        apiKey: null,
        isAuthenticated: false,
        isGuestMode: false,
        user: null
      };
    }

    return {
      apiKey,
      isAuthenticated: true,
      isGuestMode: false,
      user: user as {
        uuid: string;
        email: string;
        nickname: string;
      }
    };
  }

  async makeAuthenticatedRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const apiKey = await this.getApiKey();
    
    if (!apiKey) {
      throw new Error("No API key found. Please authenticate first.");
    }

    const headers = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`,
      ...options.headers
    };

    return fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers
    });
  }

  async enableGuestMode(): Promise<AuthState> {
    await storage.set("guestMode", true);
    await this.removeApiKey();
    
    return {
      apiKey: null,
      isAuthenticated: false,
      isGuestMode: true,
      user: null
    };
  }

  async logout(): Promise<void> {
    await this.removeApiKey();
    await storage.remove("guestMode");
  }
}

export const auth = ExtensionAuth.getInstance();