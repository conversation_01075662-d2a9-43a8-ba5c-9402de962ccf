import { Storage } from "@plasmohq/storage";

const storage = new Storage();

export interface ProjectData {
  id: string;
  name: string;
  domain: string;
  description: string;
  user_id: string;
  created_at: string;
}

export interface LinkData {
  id: string;
  url: string;
  title: string;
  link_type: "free" | "paid";
  price?: number;
  source?: string;
  project_id?: string;
  user_id: string;
  created_at: string;
}

export interface SubmissionData {
  id: string;
  project_id: string;
  link_id: string;
  target_url: string;
  submission_data: any;
  status: "pending" | "submitted" | "failed";
  created_at: string;
}

export interface ExtensionSettings {
  autoFillEnabled: boolean;
  aiGenerationEnabled: boolean;
  defaultProject: string | null;
  theme: "light" | "dark";
  notifications: boolean;
}

export interface GuestLinkData {
  id: string;
  url: string;
  title: string;
  description?: string;
  created_at: string;
}

export class ExtensionStorage {
  private static instance: ExtensionStorage;
  
  static getInstance(): ExtensionStorage {
    if (!ExtensionStorage.instance) {
      ExtensionStorage.instance = new ExtensionStorage();
    }
    return ExtensionStorage.instance;
  }

  // Settings management
  async getSettings(): Promise<ExtensionSettings> {
    const defaultSettings: ExtensionSettings = {
      autoFillEnabled: true,
      aiGenerationEnabled: true,
      defaultProject: null,
      theme: "light",
      notifications: true
    };

    const settings = await storage.get("settings");
    return Object.assign({}, defaultSettings, settings || {});
  }

  async updateSettings(settings: Partial<ExtensionSettings>): Promise<void> {
    const currentSettings = await this.getSettings();
    const newSettings = Object.assign({}, currentSettings, settings);
    await storage.set("settings", newSettings);
  }

  // Project data cache
  async cacheProjects(projects: ProjectData[]): Promise<void> {
    await storage.set("projects", projects);
    await storage.set("projectsLastFetch", Date.now());
  }

  async getCachedProjects(): Promise<ProjectData[] | null> {
    const lastFetch = await storage.get("projectsLastFetch");
    const cacheTimeout = 10 * 60 * 1000; // 10 minutes
    
    if (!lastFetch || (Date.now() - Number(lastFetch)) > cacheTimeout) {
      return null;
    }
    
    return await storage.get("projects");
  }

  // Link data cache
  async cacheLinks(links: LinkData[]): Promise<void> {
    await storage.set("links", links);
    await storage.set("linksLastFetch", Date.now());
  }

  async getCachedLinks(): Promise<LinkData[] | null> {
    const lastFetch = await storage.get("linksLastFetch");
    const cacheTimeout = 10 * 60 * 1000; // 10 minutes
    
    if (!lastFetch || (Date.now() - Number(lastFetch)) > cacheTimeout) {
      return null;
    }
    
    return await storage.get("links");
  }

  // Submission history
  async addSubmission(submission: SubmissionData): Promise<void> {
    const submissions = await this.getSubmissions();
    submissions.unshift(submission);
    
    // Keep only last 100 submissions
    if (submissions.length > 100) {
      submissions.splice(100);
    }
    
    await storage.set("submissions", submissions);
  }

  async getSubmissions(): Promise<SubmissionData[]> {
    return (await storage.get("submissions")) || [];
  }

  async updateSubmissionStatus(id: string, status: SubmissionData["status"]): Promise<void> {
    const submissions = await this.getSubmissions();
    const submission = submissions.find(s => s.id === id);
    
    if (submission) {
      submission.status = status;
      await storage.set("submissions", submissions);
    }
  }

  // Form data cache for current page
  async cacheFormData(url: string, formData: any): Promise<void> {
    const cache = await storage.get("formCache") || {};
    cache[url] = {
      data: formData,
      timestamp: Date.now()
    };
    await storage.set("formCache", cache);
  }

  async getCachedFormData(url: string): Promise<any | null> {
    const cache = await storage.get("formCache") || {};
    const cached = cache[url];
    
    if (!cached) return null;
    
    // Cache expires after 1 hour
    const cacheTimeout = 60 * 60 * 1000;
    if ((Date.now() - Number(cached.timestamp)) > cacheTimeout) {
      delete cache[url];
      await storage.set("formCache", cache);
      return null;
    }
    
    return cached.data;
  }

  // Guest mode link management
  async addGuestLink(linkData: Omit<GuestLinkData, "id" | "created_at">): Promise<GuestLinkData> {
    const guestLinks = await this.getGuestLinks();
    const newLink: GuestLinkData = {
      id: crypto.randomUUID(),
      ...linkData,
      created_at: new Date().toISOString()
    };
    
    guestLinks.unshift(newLink);
    
    // Keep only last 50 links
    if (guestLinks.length > 50) {
      guestLinks.splice(50);
    }
    
    await storage.set("guestLinks", guestLinks);
    return newLink;
  }

  async getGuestLinks(): Promise<GuestLinkData[]> {
    return (await storage.get("guestLinks")) || [];
  }

  async removeGuestLink(id: string): Promise<void> {
    const guestLinks = await this.getGuestLinks();
    const updatedLinks = guestLinks.filter(link => link.id !== id);
    await storage.set("guestLinks", updatedLinks);
  }

  // Clear all data
  async clearAll(): Promise<void> {
    await storage.clear();
  }
}

export const extensionStorage = ExtensionStorage.getInstance();