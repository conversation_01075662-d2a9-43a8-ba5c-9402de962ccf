import { auth } from "./auth";
import { ProjectData, LinkData, SubmissionData, extensionStorage } from "./storage";

export class ExtensionAPI {
  private static instance: ExtensionAPI;
  
  static getInstance(): ExtensionAPI {
    if (!ExtensionAPI.instance) {
      ExtensionAPI.instance = new ExtensionAPI();
    }
    return ExtensionAPI.instance;
  }

  async getProjects(): Promise<ProjectData[]> {
    try {
      // Try to get from cache first
      const cached = await extensionStorage.getCachedProjects();
      if (cached) {
        return cached;
      }

      // Fetch from API
      const response = await auth.makeAuthenticatedRequest("/api/extension/projects");
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        await extensionStorage.cacheProjects(data.projects);
        return data.projects;
      } else {
        throw new Error(data.message || "Failed to fetch projects");
      }
    } catch (error) {
      console.error("Failed to fetch projects:", error);
      throw error;
    }
  }

  async getLinks(projectId?: string): Promise<LinkData[]> {
    try {
      // Try to get from cache first
      const cached = await extensionStorage.getCachedLinks();
      if (cached) {
        return projectId ? cached.filter(link => link.project_id === projectId) : cached;
      }

      // Fetch from API
      const url = projectId ? `/api/extension/links?project_id=${projectId}` : "/api/extension/links";
      const response = await auth.makeAuthenticatedRequest(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        await extensionStorage.cacheLinks(data.links);
        return data.links;
      } else {
        throw new Error(data.message || "Failed to fetch links");
      }
    } catch (error) {
      console.error("Failed to fetch links:", error);
      throw error;
    }
  }

  async addLink(linkData: Omit<LinkData, "id" | "user_id" | "created_at">): Promise<LinkData> {
    try {
      const response = await auth.makeAuthenticatedRequest("/api/extension/links", {
        method: "POST",
        body: JSON.stringify(linkData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Invalidate cache
        await extensionStorage.cacheLinks([]);
        return data.link;
      } else {
        throw new Error(data.message || "Failed to add link");
      }
    } catch (error) {
      console.error("Failed to add link:", error);
      throw error;
    }
  }

  async submitLink(submissionData: {
    project_id: string;
    link_id: string;
    target_url: string;
    form_data: any;
  }): Promise<SubmissionData> {
    try {
      const response = await auth.makeAuthenticatedRequest("/api/extension/submit", {
        method: "POST",
        body: JSON.stringify(submissionData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Add to local submission history
        await extensionStorage.addSubmission(data.submission);
        return data.submission;
      } else {
        throw new Error(data.message || "Failed to submit link");
      }
    } catch (error) {
      console.error("Failed to submit link:", error);
      throw error;
    }
  }

  async analyzeForm(formHtml: string, targetUrl: string): Promise<{
    fields: Array<{
      name: string;
      type: string;
      label: string;
      required: boolean;
      suggestions: string[];
    }>;
    formType: string;
    confidence: number;
  }> {
    try {
      const response = await auth.makeAuthenticatedRequest("/api/extension/analyze-form", {
        method: "POST",
        body: JSON.stringify({
          form_html: formHtml,
          target_url: targetUrl
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        return data.analysis;
      } else {
        throw new Error(data.message || "Failed to analyze form");
      }
    } catch (error) {
      console.error("Failed to analyze form:", error);
      throw error;
    }
  }

  async generateContent(projectId: string, formType: string, fields: string[]): Promise<{
    [fieldName: string]: string;
  }> {
    try {
      const response = await auth.makeAuthenticatedRequest("/api/extension/generate-content", {
        method: "POST",
        body: JSON.stringify({
          project_id: projectId,
          form_type: formType,
          fields: fields
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        return data.content;
      } else {
        throw new Error(data.message || "Failed to generate content");
      }
    } catch (error) {
      console.error("Failed to generate content:", error);
      throw error;
    }
  }

  async updateSubmissionStatus(submissionId: string, status: SubmissionData["status"]): Promise<void> {
    try {
      const response = await auth.makeAuthenticatedRequest(`/api/extension/submit/${submissionId}`, {
        method: "PATCH",
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Update local storage
        await extensionStorage.updateSubmissionStatus(submissionId, status);
      } else {
        throw new Error(data.message || "Failed to update submission status");
      }
    } catch (error) {
      console.error("Failed to update submission status:", error);
      throw error;
    }
  }
}

export const extensionAPI = ExtensionAPI.getInstance();