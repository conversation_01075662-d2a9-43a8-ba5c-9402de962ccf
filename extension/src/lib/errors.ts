// Comprehensive error handling for LinkTrackPro extension\n\nexport interface ExtensionError {\n  code: string;\n  message: string;\n  details?: any;\n  userMessage: string;\n  retryable: boolean;\n  timestamp: string;\n}\n\nexport class ExtensionErrorHandler {\n  private static instance: ExtensionErrorHandler;\n  \n  static getInstance(): ExtensionErrorHandler {\n    if (!ExtensionErrorHandler.instance) {\n      ExtensionErrorHandler.instance = new ExtensionErrorHandler();\n    }\n    return ExtensionErrorHandler.instance;\n  }\n\n  // Error codes and their user-friendly messages\n  private errorCodes = {\n    // Authentication errors\n    AUTH_INVALID_API_KEY: \"Invalid API key. Please check your credentials.\",\n    AUTH_EXPIRED_TOKEN: \"Your session has expired. Please authenticate again.\",\n    AUTH_MISSING_CREDENTIALS: \"Please provide your API key to continue.\",\n    \n    // Network errors\n    NETWORK_CONNECTION_FAILED: \"Unable to connect to LinkTrackPro. Please check your internet connection.\",\n    NETWORK_TIMEOUT: \"Request timed out. Please try again.\",\n    NETWORK_SERVER_ERROR: \"Server temporarily unavailable. Please try again later.\",\n    \n    // Form detection errors\n    FORM_NOT_DETECTED: \"No submission form found on this page. Please navigate to a submission page.\",\n    FORM_ANALYSIS_FAILED: \"Unable to analyze the form. Manual submission may be required.\",\n    FORM_FILL_FAILED: \"Failed to fill form fields. Please review and submit manually.\",\n    \n    // Project/Link errors\n    PROJECT_NOT_FOUND: \"Selected project not found. Please refresh and try again.\",\n    LINK_NOT_FOUND: \"Selected link not found. Please choose a different link.\",\n    PROJECT_ACCESS_DENIED: \"You don't have access to this project.\",\n    \n    // Submission errors\n    SUBMISSION_FAILED: \"Failed to submit link. Please try again or submit manually.\",\n    SUBMISSION_DUPLICATE: \"This link has already been submitted to this target.\",\n    SUBMISSION_INVALID_DATA: \"Invalid submission data. Please check your inputs.\",\n    \n    // AI service errors\n    AI_SERVICE_UNAVAILABLE: \"AI services are temporarily unavailable. Using fallback content generation.\",\n    AI_CONTENT_GENERATION_FAILED: \"AI content generation failed. Using template content instead.\",\n    \n    // Storage errors\n    STORAGE_QUOTA_EXCEEDED: \"Extension storage is full. Please clear some data in settings.\",\n    STORAGE_ACCESS_DENIED: \"Unable to access extension storage. Please restart the extension.\",\n    \n    // General errors\n    UNKNOWN_ERROR: \"An unexpected error occurred. Please try again.\",\n    VALIDATION_ERROR: \"Invalid input data. Please check your entries.\",\n    PERMISSION_DENIED: \"Permission denied. Please check your extension permissions.\"\n  };\n\n  createError(\n    code: keyof typeof this.errorCodes,\n    details?: any,\n    originalError?: Error\n  ): ExtensionError {\n    return {\n      code,\n      message: originalError?.message || code,\n      details: details || originalError,\n      userMessage: this.errorCodes[code],\n      retryable: this.isRetryableError(code),\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  private isRetryableError(code: string): boolean {\n    const retryableCodes = [\n      'NETWORK_CONNECTION_FAILED',\n      'NETWORK_TIMEOUT',\n      'NETWORK_SERVER_ERROR',\n      'AI_SERVICE_UNAVAILABLE',\n      'AI_CONTENT_GENERATION_FAILED',\n      'FORM_ANALYSIS_FAILED',\n      'SUBMISSION_FAILED'\n    ];\n    return retryableCodes.includes(code);\n  }\n\n  handleError(error: ExtensionError | Error | any): ExtensionError {\n    let processedError: ExtensionError;\n\n    if (this.isExtensionError(error)) {\n      processedError = error;\n    } else if (error instanceof Error) {\n      processedError = this.classifyError(error);\n    } else {\n      processedError = this.createError('UNKNOWN_ERROR', error);\n    }\n\n    // Log error for debugging\n    console.error('[LinkTrackPro Extension Error]:', {\n      code: processedError.code,\n      message: processedError.message,\n      details: processedError.details,\n      timestamp: processedError.timestamp\n    });\n\n    return processedError;\n  }\n\n  private isExtensionError(error: any): error is ExtensionError {\n    return error && typeof error.code === 'string' && typeof error.userMessage === 'string';\n  }\n\n  private classifyError(error: Error): ExtensionError {\n    const message = error.message.toLowerCase();\n\n    // Network errors\n    if (message.includes('fetch') || message.includes('network') || message.includes('connection')) {\n      if (message.includes('timeout')) {\n        return this.createError('NETWORK_TIMEOUT', null, error);\n      }\n      return this.createError('NETWORK_CONNECTION_FAILED', null, error);\n    }\n\n    // Authentication errors\n    if (message.includes('unauthorized') || message.includes('401')) {\n      return this.createError('AUTH_INVALID_API_KEY', null, error);\n    }\n\n    if (message.includes('forbidden') || message.includes('403')) {\n      return this.createError('PROJECT_ACCESS_DENIED', null, error);\n    }\n\n    // Server errors\n    if (message.includes('500') || message.includes('502') || message.includes('503')) {\n      return this.createError('NETWORK_SERVER_ERROR', null, error);\n    }\n\n    // Form errors\n    if (message.includes('form') && message.includes('not found')) {\n      return this.createError('FORM_NOT_DETECTED', null, error);\n    }\n\n    // Storage errors\n    if (message.includes('quota') || message.includes('storage')) {\n      return this.createError('STORAGE_QUOTA_EXCEEDED', null, error);\n    }\n\n    // Default to unknown error\n    return this.createError('UNKNOWN_ERROR', null, error);\n  }\n\n  async showUserNotification(error: ExtensionError): Promise<void> {\n    try {\n      // Send message to background script to show notification\n      chrome.runtime.sendMessage({\n        action: 'showNotification',\n        data: {\n          title: 'LinkTrackPro Error',\n          message: error.userMessage,\n          type: 'error'\n        }\n      });\n    } catch (notificationError) {\n      console.error('Failed to show error notification:', notificationError);\n    }\n  }\n\n  async logErrorToAnalytics(error: ExtensionError): Promise<void> {\n    try {\n      // Don't log sensitive information\n      const sanitizedError = {\n        code: error.code,\n        timestamp: error.timestamp,\n        retryable: error.retryable,\n        // Don't include details or messages that might contain sensitive data\n        userAgent: navigator.userAgent,\n        url: window.location.hostname // Only hostname, not full URL\n      };\n\n      // Store in extension usage stats if available\n      chrome.runtime.sendMessage({\n        action: 'logError',\n        data: sanitizedError\n      });\n    } catch (logError) {\n      console.error('Failed to log error to analytics:', logError);\n    }\n  }\n}\n\n// Error boundary for React components\nexport class ErrorBoundary {\n  static handleComponentError(error: Error, errorInfo: any): ExtensionError {\n    const errorHandler = ExtensionErrorHandler.getInstance();\n    const processedError = errorHandler.handleError(error);\n    \n    // Log additional React error info\n    console.error('[React Error Boundary]:', {\n      error: processedError,\n      errorInfo,\n      componentStack: errorInfo.componentStack\n    });\n\n    return processedError;\n  }\n}\n\n// Retry mechanism for failed operations\nexport class RetryManager {\n  private static maxRetries = 3;\n  private static baseDelay = 1000; // 1 second\n\n  static async withRetry<T>(\n    operation: () => Promise<T>,\n    errorHandler: ExtensionErrorHandler,\n    maxRetries: number = RetryManager.maxRetries\n  ): Promise<T> {\n    let lastError: ExtensionError;\n    \n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = errorHandler.handleError(error);\n        \n        // Don't retry if error is not retryable or on final attempt\n        if (!lastError.retryable || attempt === maxRetries) {\n          throw lastError;\n        }\n        \n        // Exponential backoff\n        const delay = RetryManager.baseDelay * Math.pow(2, attempt - 1);\n        await new Promise(resolve => setTimeout(resolve, delay));\n        \n        console.log(`[RetryManager] Retrying operation (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);\n      }\n    }\n    \n    throw lastError!;\n  }\n}\n\n// Utility functions\nexport function isRetryableError(error: ExtensionError): boolean {\n  return error.retryable;\n}\n\nexport function getErrorMessage(error: ExtensionError | Error | any): string {\n  if (error && typeof error.userMessage === 'string') {\n    return error.userMessage;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n}\n\nexport function logError(error: ExtensionError | Error | any, context?: string): void {\n  const errorHandler = ExtensionErrorHandler.getInstance();\n  const processedError = errorHandler.handleError(error);\n  \n  if (context) {\n    console.error(`[${context}]`, processedError);\n  }\n  \n  // Log to analytics\n  errorHandler.logErrorToAnalytics(processedError);\n}\n\n// Export singleton instance\nexport const errorHandler = ExtensionErrorHandler.getInstance();"