import { ExtensionAuth } from '~lib/auth';\nimport { Storage } from '@plasmohq/storage';\n\n// Mock dependencies\njest.mock('@plasmohq/storage');\njest.mock('global', () => ({\n  ...global,\n  fetch: jest.fn()\n}));\n\nconst mockStorage = Storage as jest.MockedClass<typeof Storage>;\nconst mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;\n\ndescribe('ExtensionAuth', () => {\n  let auth: ExtensionAuth;\n  let mockStorageInstance: jest.Mocked<Storage>;\n\n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n    \n    // Setup storage mock\n    mockStorageInstance = {\n      get: jest.fn(),\n      set: jest.fn(),\n      remove: jest.fn()\n    } as any;\n    \n    mockStorage.mockImplementation(() => mockStorageInstance);\n    \n    auth = ExtensionAuth.getInstance();\n  });\n\n  describe('getApiKey', () => {\n    it('should return stored API key', async () => {\n      const testApiKey = 'test-api-key-123';\n      mockStorageInstance.get.mockResolvedValue(testApiKey);\n\n      const result = await auth.getApiKey();\n\n      expect(result).toBe(testApiKey);\n      expect(mockStorageInstance.get).toHaveBeenCalledWith('apiKey');\n    });\n\n    it('should return null when no API key stored', async () => {\n      mockStorageInstance.get.mockResolvedValue(null);\n\n      const result = await auth.getApiKey();\n\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('setApiKey', () => {\n    it('should store API key', async () => {\n      const testApiKey = 'test-api-key-123';\n      mockStorageInstance.set.mockResolvedValue(undefined);\n\n      await auth.setApiKey(testApiKey);\n\n      expect(mockStorageInstance.set).toHaveBeenCalledWith('apiKey', testApiKey);\n    });\n  });\n\n  describe('validateApiKey', () => {\n    const testApiKey = 'test-api-key-123';\n    const mockUser = {\n      uuid: 'user-123',\n      email: '<EMAIL>',\n      nickname: 'Test User'\n    };\n\n    it('should validate correct API key', async () => {\n      // Mock successful API response\n      const mockResponse = {\n        ok: true,\n        json: jest.fn().mockResolvedValue({\n          success: true,\n          user: mockUser\n        })\n      };\n      mockFetch.mockResolvedValue(mockResponse as any);\n      mockStorageInstance.set.mockResolvedValue(undefined);\n\n      const result = await auth.validateApiKey(testApiKey);\n\n      expect(result.isAuthenticated).toBe(true);\n      expect(result.user).toEqual(mockUser);\n      expect(result.apiKey).toBe(testApiKey);\n      expect(mockStorageInstance.set).toHaveBeenCalledWith('user', mockUser);\n    });\n\n    it('should reject invalid API key', async () => {\n      // Mock failed API response\n      const mockResponse = {\n        ok: false,\n        status: 401,\n        statusText: 'Unauthorized'\n      };\n      mockFetch.mockResolvedValue(mockResponse as any);\n\n      const result = await auth.validateApiKey(testApiKey);\n\n      expect(result.isAuthenticated).toBe(false);\n      expect(result.user).toBeNull();\n      expect(result.apiKey).toBeNull();\n    });\n\n    it('should handle network errors', async () => {\n      mockFetch.mockRejectedValue(new Error('Network error'));\n\n      const result = await auth.validateApiKey(testApiKey);\n\n      expect(result.isAuthenticated).toBe(false);\n      expect(result.user).toBeNull();\n    });\n\n    it('should handle invalid JSON response', async () => {\n      const mockResponse = {\n        ok: true,\n        json: jest.fn().mockRejectedValue(new Error('Invalid JSON'))\n      };\n      mockFetch.mockResolvedValue(mockResponse as any);\n\n      const result = await auth.validateApiKey(testApiKey);\n\n      expect(result.isAuthenticated).toBe(false);\n    });\n  });\n\n  describe('getCurrentAuthState', () => {\n    it('should return authenticated state when user and API key exist', async () => {\n      const testApiKey = 'test-api-key-123';\n      const mockUser = { uuid: 'user-123', email: '<EMAIL>' };\n      \n      mockStorageInstance.get.mockImplementation((key) => {\n        if (key === 'apiKey') return Promise.resolve(testApiKey);\n        if (key === 'user') return Promise.resolve(mockUser);\n        return Promise.resolve(null);\n      });\n\n      const result = await auth.getCurrentAuthState();\n\n      expect(result.isAuthenticated).toBe(true);\n      expect(result.apiKey).toBe(testApiKey);\n      expect(result.user).toEqual(mockUser);\n    });\n\n    it('should return unauthenticated state when API key missing', async () => {\n      mockStorageInstance.get.mockImplementation((key) => {\n        if (key === 'apiKey') return Promise.resolve(null);\n        if (key === 'user') return Promise.resolve({ uuid: 'user-123' });\n        return Promise.resolve(null);\n      });\n\n      const result = await auth.getCurrentAuthState();\n\n      expect(result.isAuthenticated).toBe(false);\n      expect(result.apiKey).toBeNull();\n      expect(result.user).toBeNull();\n    });\n  });\n\n  describe('makeAuthenticatedRequest', () => {\n    const testApiKey = 'test-api-key-123';\n    const testEndpoint = '/api/test';\n\n    beforeEach(() => {\n      mockStorageInstance.get.mockResolvedValue(testApiKey);\n    });\n\n    it('should make request with authorization header', async () => {\n      const mockResponse = { ok: true, json: jest.fn() };\n      mockFetch.mockResolvedValue(mockResponse as any);\n\n      await auth.makeAuthenticatedRequest(testEndpoint);\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://mybacklinks.app/api/test',\n        expect.objectContaining({\n          headers: expect.objectContaining({\n            'Authorization': `Bearer ${testApiKey}`,\n            'Content-Type': 'application/json'\n          })\n        })\n      );\n    });\n\n    it('should merge custom headers', async () => {\n      const mockResponse = { ok: true };\n      mockFetch.mockResolvedValue(mockResponse as any);\n      \n      const customHeaders = { 'X-Custom': 'value' };\n\n      await auth.makeAuthenticatedRequest(testEndpoint, {\n        headers: customHeaders\n      });\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://mybacklinks.app/api/test',\n        expect.objectContaining({\n          headers: expect.objectContaining({\n            'Authorization': `Bearer ${testApiKey}`,\n            'Content-Type': 'application/json',\n            'X-Custom': 'value'\n          })\n        })\n      );\n    });\n\n    it('should throw error when no API key stored', async () => {\n      mockStorageInstance.get.mockResolvedValue(null);\n\n      await expect(auth.makeAuthenticatedRequest(testEndpoint))\n        .rejects\n        .toThrow('No API key found. Please authenticate first.');\n    });\n  });\n\n  describe('logout', () => {\n    it('should remove API key and user data', async () => {\n      mockStorageInstance.remove.mockResolvedValue(undefined);\n\n      await auth.logout();\n\n      expect(mockStorageInstance.remove).toHaveBeenCalledWith('apiKey');\n      expect(mockStorageInstance.remove).toHaveBeenCalledWith('user');\n    });\n  });\n\n  describe('singleton pattern', () => {\n    it('should return same instance', () => {\n      const instance1 = ExtensionAuth.getInstance();\n      const instance2 = ExtensionAuth.getInstance();\n\n      expect(instance1).toBe(instance2);\n    });\n  });\n});"