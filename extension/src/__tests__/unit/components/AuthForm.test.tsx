import React from 'react';\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { AuthForm } from '~components/AuthForm';\nimport { auth } from '~lib/auth';\n\n// Mock the auth module\njest.mock('~lib/auth', () => ({\n  auth: {\n    validateApiKey: jest.fn()\n  }\n}));\n\nconst mockAuth = auth as jest.Mocked<typeof auth>;\n\ndescribe('AuthForm', () => {\n  const mockOnAuthSuccess = jest.fn();\n  \n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  it('should render authentication form', () => {\n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    expect(screen.getByRole('heading', { name: /authenticate/i })).toBeInTheDocument();\n    expect(screen.getByPlaceholderText(/enter your api key/i)).toBeInTheDocument();\n    expect(screen.getByRole('button', { name: /authenticate/i })).toBeInTheDocument();\n  });\n\n  it('should show error for empty API key', async () => {\n    const user = userEvent.setup();\n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    await user.click(submitButton);\n    \n    expect(screen.getByText(/please enter your api key/i)).toBeInTheDocument();\n  });\n\n  it('should handle successful authentication', async () => {\n    const user = userEvent.setup();\n    const mockAuthState = {\n      apiKey: 'test-key',\n      isAuthenticated: true,\n      user: { uuid: 'user-123', email: '<EMAIL>', nickname: 'Test User' }\n    };\n    \n    mockAuth.validateApiKey.mockResolvedValue(mockAuthState);\n    \n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    \n    await user.type(apiKeyInput, 'test-api-key');\n    await user.click(submitButton);\n    \n    await waitFor(() => {\n      expect(mockAuth.validateApiKey).toHaveBeenCalledWith('test-api-key');\n      expect(mockOnAuthSuccess).toHaveBeenCalledWith(mockAuthState);\n    });\n  });\n\n  it('should handle authentication failure', async () => {\n    const user = userEvent.setup();\n    const mockAuthState = {\n      apiKey: null,\n      isAuthenticated: false,\n      user: null\n    };\n    \n    mockAuth.validateApiKey.mockResolvedValue(mockAuthState);\n    \n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    \n    await user.type(apiKeyInput, 'invalid-key');\n    await user.click(submitButton);\n    \n    await waitFor(() => {\n      expect(screen.getByText(/invalid api key/i)).toBeInTheDocument();\n      expect(mockOnAuthSuccess).not.toHaveBeenCalled();\n    });\n  });\n\n  it('should handle network errors', async () => {\n    const user = userEvent.setup();\n    mockAuth.validateApiKey.mockRejectedValue(new Error('Network error'));\n    \n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    \n    await user.type(apiKeyInput, 'test-key');\n    await user.click(submitButton);\n    \n    await waitFor(() => {\n      expect(screen.getByText(/authentication failed/i)).toBeInTheDocument();\n    });\n  });\n\n  it('should show loading state during authentication', async () => {\n    const user = userEvent.setup();\n    let resolveAuth: (value: any) => void;\n    const authPromise = new Promise(resolve => {\n      resolveAuth = resolve;\n    });\n    \n    mockAuth.validateApiKey.mockReturnValue(authPromise as any);\n    \n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    \n    await user.type(apiKeyInput, 'test-key');\n    await user.click(submitButton);\n    \n    // Check loading state\n    expect(screen.getByText(/authenticating/i)).toBeInTheDocument();\n    expect(submitButton).toBeDisabled();\n    \n    // Resolve the promise\n    resolveAuth!({\n      apiKey: 'test-key',\n      isAuthenticated: true,\n      user: { uuid: 'user-123', email: '<EMAIL>' }\n    });\n    \n    await waitFor(() => {\n      expect(screen.queryByText(/authenticating/i)).not.toBeInTheDocument();\n    });\n  });\n\n  it('should disable submit button when API key is empty', () => {\n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    expect(submitButton).toBeDisabled();\n  });\n\n  it('should enable submit button when API key is provided', async () => {\n    const user = userEvent.setup();\n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    \n    expect(submitButton).toBeDisabled();\n    \n    await user.type(apiKeyInput, 'test-key');\n    \n    expect(submitButton).toBeEnabled();\n  });\n\n  it('should trim whitespace from API key', async () => {\n    const user = userEvent.setup();\n    const mockAuthState = {\n      apiKey: 'test-key',\n      isAuthenticated: true,\n      user: { uuid: 'user-123', email: '<EMAIL>' }\n    };\n    \n    mockAuth.validateApiKey.mockResolvedValue(mockAuthState);\n    \n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    const submitButton = screen.getByRole('button', { name: /authenticate/i });\n    \n    await user.type(apiKeyInput, '  test-key  ');\n    await user.click(submitButton);\n    \n    await waitFor(() => {\n      expect(mockAuth.validateApiKey).toHaveBeenCalledWith('test-key');\n    });\n  });\n\n  it('should render link to settings page', () => {\n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const settingsLink = screen.getByRole('link', { name: /linktrackpro settings/i });\n    expect(settingsLink).toHaveAttribute('href', 'https://mybacklinks.app/settings');\n    expect(settingsLink).toHaveAttribute('target', '_blank');\n  });\n\n  it('should handle form submission via Enter key', async () => {\n    const user = userEvent.setup();\n    const mockAuthState = {\n      apiKey: 'test-key',\n      isAuthenticated: true,\n      user: { uuid: 'user-123', email: '<EMAIL>' }\n    };\n    \n    mockAuth.validateApiKey.mockResolvedValue(mockAuthState);\n    \n    render(<AuthForm onAuthSuccess={mockOnAuthSuccess} />);\n    \n    const apiKeyInput = screen.getByPlaceholderText(/enter your api key/i);\n    \n    await user.type(apiKeyInput, 'test-key');\n    await user.keyboard('{Enter}');\n    \n    await waitFor(() => {\n      expect(mockAuth.validateApiKey).toHaveBeenCalledWith('test-key');\n    });\n  });\n});"