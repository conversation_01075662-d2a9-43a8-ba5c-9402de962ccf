<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LinkTrackPro Extension Settings</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f6fa;
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px;
      text-align: center;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .header p {
      opacity: 0.9;
      font-size: 16px;
    }

    .content {
      padding: 32px;
    }

    .section {
      margin-bottom: 40px;
      padding-bottom: 32px;
      border-bottom: 1px solid #e1e5e9;
    }

    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .section h2 {
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .user-info {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .user-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: #667eea;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      font-weight: 600;
    }

    .user-details h3 {
      color: #2d3748;
      font-size: 18px;
      margin-bottom: 4px;
    }

    .user-details p {
      color: #718096;
      font-size: 14px;
    }

    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      margin-top: 8px;
    }

    .status-badge.authenticated {
      background: #d1fae5;
      color: #065f46;
    }

    .status-badge.guest {
      background: #fed7d7;
      color: #742a2a;
    }

    .card-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .card {
      background: #f8f9fa;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 20px;
    }

    .card h3 {
      color: #2d3748;
      font-size: 16px;
      margin-bottom: 12px;
    }

    .card p {
      color: #718096;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 16px;
    }

    .button {
      background: #667eea;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      width: 100%;
    }

    .button:hover {
      background: #5a67d8;
      transform: translateY(-1px);
    }

    .button.secondary {
      background: #e2e8f0;
      color: #4a5568;
    }

    .button.secondary:hover {
      background: #cbd5e0;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      font-weight: 500;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .form-group input,
    .form-group textarea {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s;
    }

    .form-group input:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .stat-item {
      text-align: center;
      padding: 16px;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .list-item {
      padding: 12px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      margin-bottom: 8px;
      background: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .list-item h4 {
      color: #2d3748;
      font-size: 14px;
      margin-bottom: 2px;
    }

    .list-item p {
      color: #718096;
      font-size: 12px;
    }

    .import-export {
      display: flex;
      gap: 12px;
      margin-top: 16px;
    }

    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      z-index: 1000;
      animation: slideIn 0.3s ease;
    }

    .notification.success { background: #10b981; }
    .notification.error { background: #ef4444; }
    .notification.info { background: #3b82f6; }

    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    .empty-state {
      text-align: center;
      color: #718096;
      font-style: italic;
      padding: 40px 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>LinkTrackPro Settings</h1>
      <p>Manage your extension preferences and data</p>
    </div>

    <div class="content">
      <!-- User Information -->
      <div class="section">
        <h2>👤 User Information</h2>
        <div class="user-info">
          <div class="user-avatar" id="userAvatar">G</div>
          <div class="user-details">
            <h3 id="userName">Guest User</h3>
            <p id="userEmail">Not authenticated</p>
            <div class="status-badge guest" id="userStatus">Guest Mode</div>
          </div>
        </div>
      </div>

      <!-- Projects -->
      <div class="section">
        <h2>📁 Projects</h2>
        <p>Manage your link tracking projects</p>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value" id="projectCount">0</div>
            <div class="stat-label">Projects</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="totalProjectLinks">0</div>
            <div class="stat-label">Total Links</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="indexedProjectLinks">0</div>
            <div class="stat-label">Indexed</div>
          </div>
        </div>

        <div id="projectsList" class="card-grid">
          <!-- Projects will be loaded here -->
        </div>

        <div class="card" style="margin-top: 20px;">
          <h3>Add New Project</h3>
          <div class="form-group">
            <label>Project Name</label>
            <input type="text" id="newProjectName" placeholder="My Website">
          </div>
          <div class="form-group">
            <label>Domain</label>
            <input type="text" id="newProjectDomain" placeholder="example.com">
          </div>
          <div class="form-group">
            <label>Description (Optional)</label>
            <textarea id="newProjectDescription" rows="3" placeholder="Project description..."></textarea>
          </div>
          <button class="button" onclick="addProject()">Add Project</button>
        </div>
      </div>

      <!-- Link Resources -->
      <div class="section">
        <h2>🔗 Link Resources</h2>
        <p>Manage your link submission resources</p>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value" id="resourceCount">0</div>
            <div class="stat-label">Resources</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="freeResourceCount">0</div>
            <div class="stat-label">Free</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="paidResourceCount">0</div>
            <div class="stat-label">Paid</div>
          </div>
        </div>

        <div class="import-export">
          <button class="button secondary" onclick="exportResources()">📤 Export</button>
          <button class="button secondary" onclick="importResources()">📥 Import</button>
          <input type="file" id="importFile" accept=".json" style="display: none;" onchange="handleFileImport(event)">
        </div>

        <div id="resourcesList" style="margin-top: 20px;">
          <!-- Resources will be loaded here -->
        </div>

        <div class="card" style="margin-top: 20px;">
          <h3>Add New Resource</h3>
          <div class="form-group">
            <label>Website URL</label>
            <input type="url" id="newResourceUrl" placeholder="https://example.com/submit">
          </div>
          <div class="form-group">
            <label>Title</label>
            <input type="text" id="newResourceTitle" placeholder="Resource title">
          </div>
          <div class="form-group">
            <label>Type</label>
            <select id="newResourceType" style="width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px;">
              <option value="free">Free</option>
              <option value="paid">Paid</option>
            </select>
          </div>
          <div class="form-group">
            <label>Notes (Optional)</label>
            <textarea id="newResourceNotes" rows="2" placeholder="Additional notes..."></textarea>
          </div>
          <button class="button" onclick="addResource()">Add Resource</button>
        </div>
      </div>
    </div>
  </div>

  <script src="settings.js"></script>
</body>
</html>