// Settings page functionality for LinkTrackPro extension
class LinkTrackProSettings {
  constructor() {
    this.authState = {
      isAuthenticated: false,
      isGuestMode: true,
      user: null,
      apiKey: null
    };
    this.projects = [];
    this.linkResources = [];
    
    this.init();
  }

  async init() {
    await this.loadAuthState();
    await this.loadUserData();
    this.updateUI();
    this.parseUrlParams();
  }

  parseUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const domain = urlParams.get('domain');
    const url = urlParams.get('url');
    
    if (domain && url) {
      // Pre-fill new resource form with current page info
      document.getElementById('newResourceUrl').value = url;
      document.getElementById('newResourceTitle').value = domain;
    }
  }

  async loadAuthState() {
    try {
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState']);
      
      if (result.apiKey && result.user) {
        this.authState = {
          isAuthenticated: true,
          isGuestMode: false,
          user: result.user,
          apiKey: result.apiKey
        };
      } else {
        this.authState = {
          isAuthenticated: false,
          isGuestMode: true,
          user: null,
          apiKey: null
        };
      }
    } catch (error) {
      console.error('Failed to load auth state:', error);
    }
  }

  async loadUserData() {
    if (this.authState.isAuthenticated) {
      await this.loadProjectsFromAPI();
      await this.loadResourcesFromAPI();
    } else {
      await this.loadGuestData();
    }
  }

  async loadProjectsFromAPI() {
    try {
      const response = await fetch('https://mybacklinks.app/api/projects', {
        headers: {
          'Authorization': `Bearer ${this.authState.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        this.projects = await response.json();
      }
    } catch (error) {
      console.error('Failed to load projects from API:', error);
      await this.loadGuestData();
    }
  }

  async loadResourcesFromAPI() {
    try {
      const response = await fetch('https://mybacklinks.app/api/link-resources', {
        headers: {
          'Authorization': `Bearer ${this.authState.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        this.linkResources = await response.json();
      }
    } catch (error) {
      console.error('Failed to load resources from API:', error);
      this.linkResources = [];
    }
  }

  async loadGuestData() {
    try {
      const result = await chrome.storage.local.get(['guestProjects', 'guestLinkResources']);
      this.projects = result.guestProjects || [];
      this.linkResources = result.guestLinkResources || [];
    } catch (error) {
      console.error('Failed to load guest data:', error);
      this.projects = [];
      this.linkResources = [];
    }
  }

  updateUI() {
    this.updateUserInfo();
    this.updateProjectsSection();
    this.updateResourcesSection();
  }

  updateUserInfo() {
    const userAvatar = document.getElementById('userAvatar');
    const userName = document.getElementById('userName');
    const userEmail = document.getElementById('userEmail');
    const userStatus = document.getElementById('userStatus');

    if (this.authState.isAuthenticated) {
      userAvatar.textContent = this.authState.user?.name?.charAt(0).toUpperCase() || 'U';
      userAvatar.style.background = '#10b981';
      userName.textContent = this.authState.user?.name || 'Authenticated User';
      userEmail.textContent = this.authState.user?.email || 'No email provided';
      userStatus.textContent = 'Authenticated';
      userStatus.className = 'status-badge authenticated';
    } else {
      userAvatar.textContent = 'G';
      userAvatar.style.background = '#6b7280';
      userName.textContent = 'Guest User';
      userEmail.textContent = 'Not authenticated - using local storage';
      userStatus.textContent = 'Guest Mode';
      userStatus.className = 'status-badge guest';
    }
  }

  updateProjectsSection() {
    // Update stats
    document.getElementById('projectCount').textContent = this.projects.length;
    
    const totalLinks = this.projects.reduce((sum, p) => sum + (p.total_links || 0), 0);
    const indexedLinks = this.projects.reduce((sum, p) => sum + (p.indexed_links || 0), 0);
    
    document.getElementById('totalProjectLinks').textContent = totalLinks;
    document.getElementById('indexedProjectLinks').textContent = indexedLinks;

    // Update projects list
    const projectsList = document.getElementById('projectsList');
    projectsList.innerHTML = '';

    if (this.projects.length === 0) {
      projectsList.innerHTML = '<div class="empty-state">No projects yet. Add your first project below!</div>';
      return;
    }

    this.projects.forEach(project => {
      const projectCard = document.createElement('div');
      projectCard.className = 'card';
      projectCard.innerHTML = `
        <h3>${project.name}</h3>
        <p><strong>Domain:</strong> ${project.domain}</p>
        <p>${project.description || 'No description provided'}</p>
        <div class="stats-grid" style="margin: 12px 0;">
          <div class="stat-item">
            <div class="stat-value">${project.total_links || 0}</div>
            <div class="stat-label">Total</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${project.indexed_links || 0}</div>
            <div class="stat-label">Indexed</div>
          </div>
        </div>
        <button class="button secondary" onclick="deleteProject('${project.id}')">Delete Project</button>
      `;
      projectsList.appendChild(projectCard);
    });
  }

  updateResourcesSection() {
    // Update stats
    const totalResources = this.linkResources.length;
    const freeResources = this.linkResources.filter(r => r.link_type === 'free').length;
    const paidResources = this.linkResources.filter(r => r.link_type === 'paid').length;

    document.getElementById('resourceCount').textContent = totalResources;
    document.getElementById('freeResourceCount').textContent = freeResources;
    document.getElementById('paidResourceCount').textContent = paidResources;

    // Update resources list
    const resourcesList = document.getElementById('resourcesList');
    resourcesList.innerHTML = '';

    if (this.linkResources.length === 0) {
      resourcesList.innerHTML = '<div class="empty-state">No link resources yet. Add your first resource below!</div>';
      return;
    }

    this.linkResources.forEach(resource => {
      const resourceItem = document.createElement('div');
      resourceItem.className = 'list-item';
      
      const domain = this.extractDomain(resource.url);
      
      resourceItem.innerHTML = `
        <div>
          <h4>${resource.title}</h4>
          <p>${domain} • ${resource.link_type === 'paid' ? '💰 Paid' : '🆓 Free'}</p>
          ${resource.notes ? `<p style="font-style: italic; color: #9ca3af;">${resource.notes}</p>` : ''}
        </div>
        <button class="button secondary" onclick="deleteResource('${resource.id || resource.url}')" style="width: auto; padding: 6px 12px;">Delete</button>
      `;
      resourcesList.appendChild(resourceItem);
    });
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname.replace(/^www\./, '');
    } catch (e) {
      return url;
    }
  }

  async addProject() {
    const name = document.getElementById('newProjectName').value.trim();
    const domain = document.getElementById('newProjectDomain').value.trim();
    const description = document.getElementById('newProjectDescription').value.trim();

    if (!name || !domain) {
      this.showNotification('Please fill in project name and domain', 'error');
      return;
    }

    const projectData = {
      id: Date.now().toString(),
      name: name,
      domain: domain.replace(/^https?:\/\//, '').replace(/^www\./, ''),
      description: description || '',
      total_links: 0,
      indexed_links: 0,
      created_at: new Date().toISOString()
    };

    try {
      if (this.authState.isAuthenticated) {
        // Submit to API
        const response = await fetch('https://mybacklinks.app/api/projects', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.authState.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(projectData)
        });

        if (response.ok) {
          const newProject = await response.json();
          this.projects.push(newProject);
        } else {
          throw new Error('Failed to create project');
        }
      } else {
        // Add to guest storage
        this.projects.push(projectData);
        await chrome.storage.local.set({ guestProjects: this.projects });
      }

      // Clear form
      document.getElementById('newProjectName').value = '';
      document.getElementById('newProjectDomain').value = '';
      document.getElementById('newProjectDescription').value = '';

      this.updateProjectsSection();
      this.showNotification('Project added successfully!', 'success');
    } catch (error) {
      console.error('Failed to add project:', error);
      this.showNotification('Failed to add project: ' + error.message, 'error');
    }
  }

  async deleteProject(projectId) {
    if (!confirm('Are you sure you want to delete this project?')) {
      return;
    }

    try {
      if (this.authState.isAuthenticated) {
        const response = await fetch(`https://mybacklinks.app/api/projects/${projectId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.authState.apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to delete project');
        }
      }

      // Remove from local array
      this.projects = this.projects.filter(p => p.id !== projectId);
      
      if (!this.authState.isAuthenticated) {
        await chrome.storage.local.set({ guestProjects: this.projects });
      }

      this.updateProjectsSection();
      this.showNotification('Project deleted successfully!', 'success');
    } catch (error) {
      console.error('Failed to delete project:', error);
      this.showNotification('Failed to delete project: ' + error.message, 'error');
    }
  }

  async addResource() {
    const url = document.getElementById('newResourceUrl').value.trim();
    const title = document.getElementById('newResourceTitle').value.trim();
    const type = document.getElementById('newResourceType').value;
    const notes = document.getElementById('newResourceNotes').value.trim();

    if (!url || !title) {
      this.showNotification('Please fill in URL and title', 'error');
      return;
    }

    const resourceData = {
      id: Date.now().toString(),
      url: url,
      title: title,
      link_type: type,
      source: 'extension_settings',
      notes: notes || '',
      created_at: new Date().toISOString()
    };

    try {
      if (this.authState.isAuthenticated) {
        const response = await fetch('https://mybacklinks.app/api/link-resources', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.authState.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(resourceData)
        });

        if (response.ok) {
          const newResource = await response.json();
          this.linkResources.push(newResource);
        } else {
          throw new Error('Failed to create resource');
        }
      } else {
        this.linkResources.push(resourceData);
        await chrome.storage.local.set({ guestLinkResources: this.linkResources });
      }

      // Clear form
      document.getElementById('newResourceUrl').value = '';
      document.getElementById('newResourceTitle').value = '';
      document.getElementById('newResourceType').value = 'free';
      document.getElementById('newResourceNotes').value = '';

      this.updateResourcesSection();
      this.showNotification('Resource added successfully!', 'success');
    } catch (error) {
      console.error('Failed to add resource:', error);
      this.showNotification('Failed to add resource: ' + error.message, 'error');
    }
  }

  async deleteResource(resourceId) {
    if (!confirm('Are you sure you want to delete this resource?')) {
      return;
    }

    try {
      if (this.authState.isAuthenticated && resourceId !== this.linkResources.find(r => r.url === resourceId)?.url) {
        const response = await fetch(`https://mybacklinks.app/api/link-resources/${resourceId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.authState.apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to delete resource');
        }
      }

      this.linkResources = this.linkResources.filter(r => r.id !== resourceId && r.url !== resourceId);
      
      if (!this.authState.isAuthenticated) {
        await chrome.storage.local.set({ guestLinkResources: this.linkResources });
      }

      this.updateResourcesSection();
      this.showNotification('Resource deleted successfully!', 'success');
    } catch (error) {
      console.error('Failed to delete resource:', error);
      this.showNotification('Failed to delete resource: ' + error.message, 'error');
    }
  }

  exportResources() {
    const data = {
      projects: this.projects,
      linkResources: this.linkResources,
      exportDate: new Date().toISOString(),
      userType: this.authState.isAuthenticated ? 'authenticated' : 'guest'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `linktrackpro-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.showNotification('Data exported successfully!', 'success');
  }

  importResources() {
    document.getElementById('importFile').click();
  }

  async handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      if (!data.projects && !data.linkResources) {
        throw new Error('Invalid file format');
      }

      if (data.projects) {
        this.projects = [...this.projects, ...data.projects];
      }

      if (data.linkResources) {
        this.linkResources = [...this.linkResources, ...data.linkResources];
      }

      if (!this.authState.isAuthenticated) {
        await chrome.storage.local.set({
          guestProjects: this.projects,
          guestLinkResources: this.linkResources
        });
      }

      this.updateUI();
      this.showNotification('Data imported successfully!', 'success');
    } catch (error) {
      console.error('Import failed:', error);
      this.showNotification('Import failed: Invalid file format', 'error');
    }

    // Reset file input
    event.target.value = '';
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }
}

// Global functions for HTML onclick handlers
let settingsManager;

function addProject() {
  settingsManager.addProject();
}

function deleteProject(projectId) {
  settingsManager.deleteProject(projectId);
}

function addResource() {
  settingsManager.addResource();
}

function deleteResource(resourceId) {
  settingsManager.deleteResource(resourceId);
}

function exportResources() {
  settingsManager.exportResources();
}

function importResources() {
  settingsManager.importResources();
}

function handleFileImport(event) {
  settingsManager.handleFileImport(event);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  settingsManager = new LinkTrackProSettings();
});