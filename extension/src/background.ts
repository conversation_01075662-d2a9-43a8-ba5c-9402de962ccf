// Background service worker for LinkTrackPro extension

import { Storage } from "@plasmohq/storage";

const storage = new Storage();

// Extension installation and updates
chrome.runtime.onInstalled.addListener((details) => {
  console.log('LinkTrackPro extension installed/updated:', details.reason);
  
  if (details.reason === 'install') {
    // Set default settings on first install
    storage.set('settings', {
      autoFillEnabled: true,
      aiGenerationEnabled: true,
      defaultProject: null,
      theme: 'light',
      notifications: true
    });
    
    // Open welcome page
    chrome.tabs.create({
      url: 'https://mybacklinks.app/extension-welcome'
    });
  }
  
  // Create context menu items
  chrome.contextMenus.create({
    id: 'linktrackpro-submit',
    title: 'Submit with LinkTrackPro',
    contexts: ['page', 'link']
  });
  
  chrome.contextMenus.create({
    id: 'linktrackpro-add-link',
    title: 'Add this page as link resource',
    contexts: ['page']
  });
});

// Handle extension icon clicks - Open native sidebar
chrome.action.onClicked.addListener(async (tab) => {
  if (tab.id) {
    try {
      // Open the sidebar using Chrome's native sidePanel API
      await chrome.sidePanel.open({ tabId: tab.id });
    } catch (error) {
      console.error('Failed to open sidebar:', error);
      // Fallback to old content script method if sidePanel API fails
      await toggleSidebar(tab.id);
    }
  }
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'linktrackpro-submit' && tab?.id) {
    // Toggle sidebar with submit mode
    toggleSidebar(tab.id, 'submit');
  } else if (info.menuItemId === 'linktrackpro-add-link' && tab) {
    // Add current page as link resource
    addCurrentPageAsLink(tab);
  }
});

// Badge management
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  await updateBadgeForTab(activeInfo.tabId);
});

chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    await updateBadgeForTab(tabId);
  }
});

// Toggle sidebar in content script
async function toggleSidebar(tabId: number, mode?: string) {
  try {
    // First check if tab is accessible and get tab info
    const tab = await chrome.tabs.get(tabId);
    
    // Skip chrome:// and other restricted URLs
    if (!tab.url || 
        tab.url.startsWith('chrome://') || 
        tab.url.startsWith('chrome-extension://') ||
        tab.url.startsWith('moz-extension://') ||
        tab.url.startsWith('edge://')) {
      console.log('Cannot inject script into restricted page:', tab.url);
      return;
    }

    // Try to send message to existing content script first
    try {
      await chrome.tabs.sendMessage(tabId, {
        action: 'toggleSidebar',
        mode: mode || null
      });
      return;
    } catch (messageError) {
      // Content script not available, fall back to script injection
      console.log('Content script not responding, injecting script...');
    }

    // Inject script to toggle sidebar
    await chrome.scripting.executeScript({
      target: { tabId },
      func: (toggleMode) => {
        // Send message to content script
        window.postMessage({
          type: 'LINKTRACKPRO_TOGGLE_SIDEBAR',
          mode: toggleMode || null
        }, '*');
      },
      args: [mode || null]
    });
  } catch (error) {
    console.error('Failed to toggle sidebar:', error);
    
    // Show user-friendly notification if needed
    if (error.message.includes('Cannot access')) {
      console.log('Extension cannot run on this type of page');
    }
  }
}

// Message handling from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'getAuthState') {
    getAuthState().then(sendResponse);
    return true;
  } else if (message.action === 'submitLink') {
    handleLinkSubmission(message.data).then(sendResponse);
    return true;
  } else if (message.action === 'showNotification') {
    showNotification(message.data);
  } else if (message.action === 'toggleSidebar' && sender.tab?.id) {
    toggleSidebar(sender.tab.id, message.mode);
  }
});

// Notification management
async function showNotification(data: {
  title: string;
  message: string;
  type: 'success' | 'error' | 'info';
}) {
  const settings = await storage.get('settings');
  
  if (!settings?.notifications) {
    return;
  }
  
  const iconMap = {
    success: 'assets/icon.png',
    error: 'assets/icon.png',
    info: 'assets/icon.png'
  };
  
  chrome.notifications.create({
    type: 'basic',
    iconUrl: iconMap[data.type],
    title: data.title,
    message: data.message
  });
}

// Badge update for form detection
async function updateBadgeForTab(tabId: number) {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        const forms = document.querySelectorAll('form');
        const hasSubmissionForm = Array.from(forms).some(form => {
          const text = form.textContent?.toLowerCase() || '';
          return text.includes('submit') || text.includes('add') || text.includes('link');
        });
        return hasSubmissionForm;
      }
    });
    
    if (results[0]?.result) {
      chrome.action.setBadgeText({ tabId, text: '●' });
      chrome.action.setBadgeBackgroundColor({ tabId, color: '#22c55e' });
      chrome.action.setTitle({ tabId, title: 'Form detected - Click to submit with LinkTrackPro' });
    } else {
      chrome.action.setBadgeText({ tabId, text: '' });
      chrome.action.setTitle({ tabId, title: 'LinkTrackPro - Automate backlink submissions' });
    }
  } catch (error) {
    // Tab might not support script injection (e.g., chrome:// pages)
    chrome.action.setBadgeText({ tabId, text: '' });
  }
}

// Authentication state management
async function getAuthState() {
  try {
    const apiKey = await storage.get('apiKey');
    const user = await storage.get('user');
    
    return {
      isAuthenticated: !!(apiKey && user),
      user
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null
    };
  }
}

// Link submission handling
async function handleLinkSubmission(data: {
  projectId: string;
  linkId: string;
  targetUrl: string;
  formData: any;
}) {
  try {
    const apiKey = await storage.get('apiKey');
    
    if (!apiKey) {
      throw new Error('Not authenticated');
    }
    
    const response = await fetch('https://mybacklinks.app/api/extension/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        project_id: data.projectId,
        link_id: data.linkId,
        target_url: data.targetUrl,
        form_data: data.formData
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      // Show success notification
      showNotification({
        title: 'Submission Successful',
        message: 'Link submitted successfully!',
        type: 'success'
      });
      
      // Store submission in local history
      const submissions = await storage.get('submissions') || [];
      submissions.unshift({
        id: result.submission.id,
        project_id: data.projectId,
        link_id: data.linkId,
        target_url: data.targetUrl,
        status: 'submitted',
        created_at: new Date().toISOString()
      });
      
      // Keep only last 50 submissions
      if (submissions.length > 50) {
        submissions.splice(50);
      }
      
      await storage.set('submissions', submissions);
      
      return { success: true, submission: result.submission };
    } else {
      throw new Error(result.message || 'Submission failed');
    }
  } catch (error) {
    console.error('Link submission failed:', error);
    
    showNotification({
      title: 'Submission Failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      type: 'error'
    });
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Add current page as link resource
async function addCurrentPageAsLink(tab: chrome.tabs.Tab) {
  try {
    const apiKey = await storage.get('apiKey');
    
    if (!apiKey) {
      showNotification({
        title: 'Authentication Required',
        message: 'Please authenticate first',
        type: 'error'
      });
      return;
    }
    
    if (!tab.url || !tab.title) {
      throw new Error('Invalid page data');
    }
    
    const response = await fetch('https://mybacklinks.app/api/extension/links', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        url: tab.url,
        title: tab.title,
        link_type: 'free',
        source: 'extension_context_menu'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      showNotification({
        title: 'Link Added',
        message: `"${tab.title}" added to your link resources`,
        type: 'success'
      });
    } else {
      throw new Error(result.message || 'Failed to add link');
    }
  } catch (error) {
    console.error('Failed to add link:', error);
    
    showNotification({
      title: 'Failed to Add Link',
      message: error instanceof Error ? error.message : 'Unknown error',
      type: 'error'
    });
  }
}

// Cleanup on extension disable/uninstall
chrome.runtime.onSuspend.addListener(() => {
  console.log('LinkTrackPro extension suspending...');
});

console.log('LinkTrackPro background script loaded');