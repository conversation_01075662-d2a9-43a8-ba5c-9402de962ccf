import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "~components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~components/ui/card"
import { Input } from "~components/ui/input"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "~components/ui/tooltip"
import { Settings, User, AlertCircle, CheckCircle, Globe, FolderOpen, Search, Bot, Edit, Send, Plus, Info, Loader2 } from "lucide-react"

interface Project {
  id: string
  name: string
  domain: string
  description?: string
  total_links: number
  indexed_links: number
}

interface LinkResource {
  id: string
  url: string
  title: string
  link_type: 'free' | 'paid'
  notes?: string
}

interface AuthState {
  isAuthenticated: boolean
  isGuestMode: boolean
  user: any
  apiKey: string | null
}

interface FormDetection {
  hasForm: boolean
  forms: any[]
  confidence: number
  fieldCount: number
}

function SidePanel() {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isGuestMode: true,
    user: null,
    apiKey: null
  })
  const [currentTab, setCurrentTab] = useState<any>(null)
  const [currentDomain, setCurrentDomain] = useState<string>('')
  const [projects, setProjects] = useState<Project[]>([])
  const [linkResources, setLinkResources] = useState<LinkResource[]>([])
  const [selectedProject, setSelectedProject] = useState<string>('')
  const [formDetection, setFormDetection] = useState<FormDetection>({
    hasForm: false,
    forms: [],
    confidence: 0,
    fieldCount: 0
  })
  const [loading, setLoading] = useState(false)
  const [showApiKeyInput, setShowApiKeyInput] = useState(false)
  const [apiKeyInput, setApiKeyInput] = useState('')
  const [domainInResources, setDomainInResources] = useState(false)

  useEffect(() => {
    initializeData()
  }, [])

  useEffect(() => {
    // 当认证状态改变时，重新加载数据
    loadUserData()
    checkDomainInResources()
  }, [authState.isAuthenticated])

  const initializeData = async () => {
    await updateCurrentTab()
    await loadAuthState()
    await loadUserData()
    await checkDomainInResources()
  }

  const updateCurrentTab = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      setCurrentTab(tab)
      
      if (tab?.url) {
        const url = new URL(tab.url)
        const domain = url.hostname.replace(/^www\./, '')
        setCurrentDomain(domain)
      }
    } catch (error) {
      console.error('Failed to get current tab:', error)
    }
  }

  const loadAuthState = async () => {
    try {
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState'])
      
      if (result.apiKey && result.user) {
        setAuthState({
          isAuthenticated: true,
          isGuestMode: false,
          user: result.user,
          apiKey: result.apiKey
        })
      }
    } catch (error) {
      console.error('Failed to load auth state:', error)
    }
  }

  const loadUserData = async () => {
    // 直接从存储中检查认证状态，而不是依赖可能尚未更新的React状态
    try {
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState'])
      
      if (result.apiKey && result.user) {
        // 用户已认证，加载认证用户数据
        await Promise.all([loadProjects(), loadLinkResources()])
      } else {
        // 用户未认证，加载访客数据
        await loadGuestData()
      }
    } catch (error) {
      console.error('Failed to check auth state:', error)
      // 出错时回退到访客模式
      await loadGuestData()
    }
  }

  const loadProjects = async () => {
    try {
      // 直接从存储中获取认证信息
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState'])
      
      if (result.apiKey && result.user) {
        // 用户已认证，从API获取项目数据
        const response = await fetch('https://mybacklinks.app/api/projects', {
          headers: {
            'Authorization': `Bearer ${result.apiKey}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          setProjects(data)
        }
      } else {
        // 用户未认证，加载访客项目
        const guestResult = await chrome.storage.local.get(['guestProjects'])
        setProjects(guestResult.guestProjects || [
          { 
            id: 'guest-project', 
            name: 'Guest Project', 
            domain: currentDomain || 'example.com', 
            total_links: 0, 
            indexed_links: 0 
          }
        ])
      }
    } catch (error) {
      console.error('Failed to load projects:', error)
      setProjects([
        { 
          id: 'guest-project', 
          name: 'Guest Project', 
          domain: currentDomain || 'example.com', 
          total_links: 0, 
          indexed_links: 0 
        }
      ])
    }
  }

  const loadLinkResources = async () => {
    try {
      // 直接从存储中获取认证信息
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState'])
      
      if (result.apiKey && result.user) {
        // 用户已认证，从API获取链接资源
        const response = await fetch('https://mybacklinks.app/api/link-resources', {
          headers: {
            'Authorization': `Bearer ${result.apiKey}`,
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          setLinkResources(data)
        }
      } else {
        // 用户未认证，加载访客链接资源
        const guestResult = await chrome.storage.local.get(['guestLinkResources'])
        setLinkResources(guestResult.guestLinkResources || [])
      }
    } catch (error) {
      console.error('Failed to load link resources:', error)
      setLinkResources([])
    }
  }

  const loadGuestData = async () => {
    try {
      const result = await chrome.storage.local.get(['guestProjects', 'guestLinkResources'])
      setProjects(result.guestProjects || [
        { 
          id: 'guest-project', 
          name: 'Guest Project', 
          domain: currentDomain || 'example.com', 
          total_links: 0, 
          indexed_links: 0 
        }
      ])
      setLinkResources(result.guestLinkResources || [])
    } catch (error) {
      console.error('Failed to load guest data:', error)
    }
  }

  const checkDomainInResources = async () => {
    if (!currentDomain) return
    
    const exists = linkResources.some(resource => {
      try {
        const resourceDomain = new URL(resource.url).hostname.replace(/^www\./, '')
        return resourceDomain === currentDomain
      } catch (e) {
        return false
      }
    })
    
    setDomainInResources(exists)
  }

  const handleAuth = async () => {
    if (authState.isAuthenticated) {
      await logout()
    } else {
      setShowApiKeyInput(!showApiKeyInput)
    }
  }

  const authenticateWithApiKey = async () => {
    if (!apiKeyInput.trim()) return

    setLoading(true)
    
    try {
      const response = await fetch('https://mybacklinks.app/api/auth/validate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKeyInput}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const userData = await response.json()
        
        await chrome.storage.local.set({
          apiKey: apiKeyInput,
          user: userData.user,
          authState: { isAuthenticated: true, isGuestMode: false }
        })
        
        setAuthState({
          isAuthenticated: true,
          isGuestMode: false,
          user: userData.user,
          apiKey: apiKeyInput
        })
        
        await loadUserData()
        setShowApiKeyInput(false)
        setApiKeyInput('')
        showNotification('Successfully authenticated!', 'success')
      } else {
        throw new Error('Invalid API key')
      }
    } catch (error) {
      console.error('Authentication error:', error)
      showNotification('Authentication failed', 'error')
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    await chrome.storage.local.remove(['apiKey', 'user', 'authState'])
    
    setAuthState({
      isAuthenticated: false,
      isGuestMode: true,
      user: null,
      apiKey: null
    })
    
    await loadGuestData()
    showNotification('Logged out successfully', 'info')
  }

  const detectForms = async () => {
    if (!currentTab?.id) {
      showNotification('No active tab available', 'error')
      return
    }

    setLoading(true)
    
    try {
      const response = await chrome.tabs.sendMessage(currentTab.id, {
        action: 'detectForm'
      })
      
      if (response && response.success) {
        setFormDetection({
          hasForm: response.hasForm,
          confidence: response.confidence,
          fieldCount: response.fieldCount,
          forms: response.forms || []
        })
        
        if (response.hasForm) {
          showNotification(`Found ${response.fieldCount} form fields`, 'success')
        } else {
          showNotification('No suitable forms detected', 'warning')
        }
      } else {
        throw new Error('Form detection failed')
      }
    } catch (error) {
      console.error('Form detection error:', error)
      showNotification('Failed to detect forms', 'error')
    } finally {
      setLoading(false)
    }
  }

  const autoFillForm = async () => {
    if (!currentTab?.id || !formDetection.hasForm) return

    setLoading(true)
    
    try {
      let formData
      
      // 直接从存储中获取认证信息
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState'])
      
      if (result.apiKey && result.user && selectedProject) {
        // 用户已认证且选择了项目，使用项目信息
        const project = projects.find(p => p.id === selectedProject)
        formData = {
          url: project?.domain ? `https://${project.domain}` : currentTab.url,
          title: project?.name || currentTab.title,
          description: project?.description || `Backlink submission for ${project?.name}`,
          email: result.user?.email || '',
          name: result.user?.name || ''
        }
      } else {
        // 用户未认证或未选择项目，使用手动输入
        const url = prompt('Enter target URL:', currentTab.url)
        if (!url) {
          setLoading(false)
          return
        }
        
        formData = {
          url: url,
          title: currentTab.title || 'Manual Submission',
          description: 'Backlink submission via LinkTrackPro',
          email: '',
          name: ''
        }
      }

      const response = await chrome.tabs.sendMessage(currentTab.id, {
        action: 'fillForm',
        data: formData
      })
      
      if (response && response.success) {
        showNotification(`Filled ${response.filledFields?.length || 0} fields`, 'success')
      } else {
        throw new Error(response?.error || 'Form filling failed')
      }
    } catch (error) {
      console.error('Auto-fill error:', error)
      showNotification('Auto-fill failed', 'error')
    } finally {
      setLoading(false)
    }
  }

  const addCurrentDomainToResources = async () => {
    if (!currentDomain || !currentTab) return
    
    setLoading(true)
    
    try {
      const resourceData = {
        url: currentTab.url,
        title: currentTab.title || currentDomain,
        link_type: 'free' as const,
        source: 'extension_manual_add',
        notes: `Added from extension on ${new Date().toLocaleDateString()}`
      }
      
      // 直接从存储中获取认证信息
      const result = await chrome.storage.local.get(['apiKey', 'user', 'authState'])
      
      if (result.apiKey && result.user) {
        // 用户已认证，发送到后端API
        const response = await fetch('https://mybacklinks.app/api/link-resources', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${result.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(resourceData)
        })
        
        if (response.ok) {
          await loadLinkResources()
          showNotification('Domain added to resources!', 'success')
        } else {
          throw new Error('Failed to add to backend')
        }
      } else {
        // 用户未认证，保存到本地存储
        const newResource = { id: Date.now().toString(), ...resourceData }
        const updatedResources = [...linkResources, newResource]
        setLinkResources(updatedResources)
        
        await chrome.storage.local.set({
          guestLinkResources: updatedResources
        })
        
        showNotification('Domain added to local resources!', 'success')
      }
      
      await checkDomainInResources()
    } catch (error) {
      console.error('Failed to add domain:', error)
      showNotification('Failed to add domain to resources', 'error')
    } finally {
      setLoading(false)
    }
  }

  const openSettings = () => {
    const settingsUrl = chrome.runtime.getURL('settings.html') + 
      `?domain=${encodeURIComponent(currentDomain || '')}&` +
      `url=${encodeURIComponent(currentTab?.url || '')}&` +
      `authenticated=${authState.isAuthenticated}`
    
    chrome.tabs.create({ url: settingsUrl })
  }

  const [notification, setNotification] = useState<{message: string, type: 'success' | 'error' | 'warning' | 'info'} | null>(null)

  const showNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
    setNotification({ message, type })
    setTimeout(() => setNotification(null), 3000)
  }

  const getSelectedProject = () => {
    return projects.find(p => p.id === selectedProject)
  }

  return (
    <div className="flex flex-col h-full bg-white text-sm">
      {/* Authentication Header */}
      <div className="bg-gray-50 border-b p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-semibold text-white ${
              authState.isAuthenticated ? 'bg-green-500' : 'bg-gray-500'
            }`}>
              {authState.isAuthenticated ? authState.user?.name?.charAt(0)?.toUpperCase() || 'U' : 'G'}
            </div>
            <div>
              <div className="font-medium text-gray-900">
                {authState.isAuthenticated ? authState.user?.name || 'Authenticated User' : 'Guest User'}
              </div>
              <div className="text-xs text-gray-500">
                {authState.isAuthenticated ? 'Connected to LinkTrackPro' : 'Not authenticated'}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" onClick={openSettings}>
              <Settings className="h-4 w-4" />
            </Button>
            <Button 
              variant={authState.isAuthenticated ? "ghost" : "default"} 
              size="sm" 
              onClick={handleAuth}
            >
              {authState.isAuthenticated ? 'Logout' : 'Login'}
            </Button>
          </div>
        </div>
        
        {showApiKeyInput && (
          <div className="flex gap-2">
            <Input
              type="password"
              placeholder="Enter API key..."
              value={apiKeyInput}
              onChange={(e) => setApiKeyInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && authenticateWithApiKey()}
              className="text-xs"
            />
            <Button size="sm" onClick={authenticateWithApiKey} disabled={loading}>
              Connect
            </Button>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Current Page Info */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Current Page
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-xs text-gray-600 break-all">
              {currentTab?.url || 'Loading...'}
            </div>
            <div className="font-medium">{currentDomain || '-'}</div>
            
            {/* Form Detection */}
            <div className={`p-3 rounded border ${
              formDetection.hasForm 
                ? 'bg-green-50 border-green-200' 
                : formDetection.confidence === -1 
                  ? 'bg-red-50 border-red-200'
                  : 'bg-blue-50 border-blue-200'
            }`}>
              <div className="flex items-center gap-2 text-xs">
                <div className={`w-2 h-2 rounded-full ${
                  formDetection.hasForm 
                    ? 'bg-green-500' 
                    : formDetection.confidence === -1 
                      ? 'bg-red-500'
                      : 'bg-blue-500'
                }`} />
                <span className="font-medium">
                  {formDetection.hasForm 
                    ? `✓ ${formDetection.forms?.length || 1} form(s) detected`
                    : formDetection.confidence === -1 
                      ? '✗ Error detecting forms'
                      : '◐ Ready to detect forms'
                  }
                </span>
              </div>
              {formDetection.hasForm && (
                <div className="text-xs text-gray-600 mt-1">
                  {formDetection.fieldCount} fields found • {Math.round(formDetection.confidence * 100)}% confidence
                </div>
              )}
              {!formDetection.hasForm && formDetection.confidence !== -1 && (
                <div className="text-xs text-blue-700 mt-1 flex items-center gap-1">
                  <Info className="h-3 w-3" />
                  <span>Click "Detect Forms" to scan for submission forms</span>
                </div>
              )}
            </div>
            
            <Button 
              className="w-full" 
              onClick={detectForms} 
              disabled={loading}
              size="sm"
              variant={formDetection.hasForm ? "outline" : "default"}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              {formDetection.hasForm ? 'Re-scan Forms' : 'Detect Forms'}
            </Button>
          </CardContent>
        </Card>

        {/* Project Selection */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <FolderOpen className="h-4 w-4" />
              Project
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <select 
              value={selectedProject} 
              onChange={(e) => setSelectedProject(e.target.value)}
              className="w-full p-2 border rounded text-sm"
            >
              <option value="">Select a project...</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.name} ({project.domain})
                </option>
              ))}
            </select>
            
            {selectedProject && getSelectedProject() && (
              <div className="text-xs text-gray-600">
                {getSelectedProject()?.description || `Tracking links for ${getSelectedProject()?.domain}`}
              </div>
            )}
            
            {!authState.isAuthenticated && (
              <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded border border-blue-200">
                <div className="flex items-center gap-1 font-medium">
                  <Info className="h-3 w-3" />
                  <span>Guest Mode</span>
                </div>
                <div className="mt-1">Projects are stored locally. Login to sync with your account.</div>
              </div>
            )}
            
            {selectedProject && getSelectedProject() && (
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div className="text-center p-2 bg-gray-50 rounded">
                  <div className="font-semibold">{getSelectedProject()?.total_links || 0}</div>
                  <div className="text-xs text-gray-600">Total Links</div>
                </div>
                <div className="text-center p-2 bg-gray-50 rounded">
                  <div className="font-semibold">{getSelectedProject()?.indexed_links || 0}</div>
                  <div className="text-xs text-gray-600">Indexed</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Auto Fill Actions */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Auto Fill
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {!formDetection.hasForm && (
              <div className="text-xs text-amber-700 bg-amber-50 p-2 rounded border border-amber-200 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                <span>Detect forms first to enable auto-fill features</span>
              </div>
            )}
            
            <div className="space-y-2">
              <Button 
                className="w-full" 
                onClick={autoFillForm} 
                disabled={!formDetection.hasForm || loading}
                size="sm"
                variant={formDetection.hasForm ? "default" : "outline"}
                title={!formDetection.hasForm ? "Detect forms first" : "Fill form with project data"}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Bot className="h-4 w-4 mr-2" />
                )}
                Auto Fill Form
              </Button>
              <Button 
                className="w-full" 
                disabled={!formDetection.hasForm || loading}
                size="sm"
                variant="outline"
                title={!formDetection.hasForm ? "Detect forms first" : "Manually edit form fields"}
              >
                <Edit className="h-4 w-4 mr-2" />
                Custom Fill
              </Button>
              <Button 
                className="w-full" 
                disabled={!formDetection.hasForm || loading}
                size="sm"
                variant="outline"
                title={!formDetection.hasForm ? "Detect forms first" : "Submit the form"}
              >
                <Send className="h-4 w-4 mr-2" />
                Submit
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Link Resource Check */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Link Resource
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className={`flex items-center justify-between p-3 rounded border ${
              domainInResources 
                ? 'bg-green-50 border-green-200 text-green-700' 
                : 'bg-orange-50 border-orange-200 text-orange-700'
            }`}>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  domainInResources ? 'bg-green-500' : 'bg-orange-500'
                }`} />
                <span className="text-xs font-medium">
                  {domainInResources ? 'Domain tracked' : 'Domain not tracked'}
                </span>
              </div>
            </div>
            
            {!domainInResources && (
              <Button 
                className="w-full" 
                onClick={addCurrentDomainToResources} 
                disabled={loading}
                size="sm"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add to Resources
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Notification Toast */}
      {notification && (
        <div className={`fixed bottom-4 right-4 left-4 p-3 rounded border shadow-lg z-50 ${
          notification.type === 'success' ? 'bg-green-50 border-green-200 text-green-700' :
          notification.type === 'error' ? 'bg-red-50 border-red-200 text-red-700' :
          notification.type === 'warning' ? 'bg-amber-50 border-amber-200 text-amber-700' :
          'bg-blue-50 border-blue-200 text-blue-700'
        }`}>
          <div className="flex items-center gap-2 text-sm">
            {notification.type === 'success' && <CheckCircle className="h-4 w-4" />}
            {notification.type === 'error' && <AlertCircle className="h-4 w-4" />}
            {notification.type === 'warning' && <AlertCircle className="h-4 w-4" />}
            {notification.type === 'info' && <Info className="h-4 w-4" />}
            <span>{notification.message}</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default SidePanel 