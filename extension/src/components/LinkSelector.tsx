import { useState } from "react";
import { LinkData } from "~lib/storage";
import { extensionAPI } from "~lib/api";
import { Button } from "~components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~components/ui/card";
import { Input } from "~components/ui/input";
import { ChevronDown, Link, Plus, DollarSign } from "lucide-react";

interface LinkSelectorProps {
  links: LinkData[];
  selectedLink: string | null;
  onLinkChange: (linkId: string) => void;
}

export function LinkSelector({ links, selectedLink, onLinkChange }: LinkSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newLinkUrl, setNewLinkUrl] = useState("");
  const [newLinkTitle, setNewLinkTitle] = useState("");
  const [newLinkType, setNewLinkType] = useState<"free" | "paid">("free");
  const [newLinkPrice, setNewLinkPrice] = useState("");
  const [loading, setLoading] = useState(false);

  const selectedLinkData = links.find(l => l.id === selectedLink);

  const handleLinkSelect = (linkId: string) => {
    onLinkChange(linkId);
    setIsOpen(false);
  };

  const handleAddLink = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newLinkUrl.trim() || !newLinkTitle.trim()) {
      return;
    }

    setLoading(true);
    try {
      const linkData = {
        url: newLinkUrl.trim(),
        title: newLinkTitle.trim(),
        link_type: newLinkType,
        price: newLinkType === "paid" ? parseFloat(newLinkPrice) || 0 : undefined,
        source: "extension"
      };

      const newLink = await extensionAPI.addLink(linkData);
      
      // Reset form
      setNewLinkUrl("");
      setNewLinkTitle("");
      setNewLinkType("free");
      setNewLinkPrice("");
      setShowAddForm(false);
      
      // Select the new link
      onLinkChange(newLink.id);
      
    } catch (error) {
      console.error("Failed to add link:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <Link className="h-4 w-4" />
          Select Link Resource
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="relative">
            <Button
              variant="outline"
              className="w-full justify-between"
              onClick={() => setIsOpen(!isOpen)}
            >
              <span className="truncate">
                {selectedLinkData ? (
                  <div className="text-left">
                    <div className="font-medium">{selectedLinkData.title}</div>
                    <div className="text-xs text-gray-500 flex items-center gap-2">
                      <span>{selectedLinkData.url}</span>
                      {selectedLinkData.link_type === "paid" && (
                        <span className="flex items-center gap-1 text-green-600">
                          <DollarSign className="h-3 w-3" />
                          {selectedLinkData.price || 0}
                        </span>
                      )}
                    </div>
                  </div>
                ) : (
                  "Choose a link..."
                )}
              </span>
              <ChevronDown className={`h-4 w-4 transition-transform ${
                isOpen ? "rotate-180" : ""
              }`} />
            </Button>
            
            {isOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
                {links.length === 0 ? (
                  <div className="p-3 text-center text-gray-500 text-sm">
                    No links found for this project
                  </div>
                ) : (
                  links.map((link) => (
                    <button
                      key={link.id}
                      className={`w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                        selectedLink === link.id ? "bg-blue-50 text-blue-600" : ""
                      }`}
                      onClick={() => handleLinkSelect(link.id)}
                    >
                      <div className="font-medium text-sm">{link.title}</div>
                      <div className="text-xs text-gray-500 flex items-center gap-2">
                        <span className="truncate">{link.url}</span>
                        {link.link_type === "paid" && (
                          <span className="flex items-center gap-1 text-green-600">
                            <DollarSign className="h-3 w-3" />
                            {link.price || 0}
                          </span>
                        )}
                      </div>
                    </button>
                  ))
                )}
              </div>
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => setShowAddForm(!showAddForm)}
          >
            <Plus className="h-3 w-3 mr-1" />
            Add New Link
          </Button>
          
          {showAddForm && (
            <div className="border border-gray-200 rounded-md p-3 space-y-3">
              <form onSubmit={handleAddLink} className="space-y-3">
                <Input
                  placeholder="Link URL"
                  value={newLinkUrl}
                  onChange={(e) => setNewLinkUrl(e.target.value)}
                  disabled={loading}
                  required
                />
                <Input
                  placeholder="Link Title"
                  value={newLinkTitle}
                  onChange={(e) => setNewLinkTitle(e.target.value)}
                  disabled={loading}
                  required
                />
                
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={newLinkType === "free" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setNewLinkType("free")}
                    disabled={loading}
                  >
                    Free
                  </Button>
                  <Button
                    type="button"
                    variant={newLinkType === "paid" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setNewLinkType("paid")}
                    disabled={loading}
                  >
                    Paid
                  </Button>
                </div>
                
                {newLinkType === "paid" && (
                  <Input
                    type="number"
                    placeholder="Price ($)"
                    value={newLinkPrice}
                    onChange={(e) => setNewLinkPrice(e.target.value)}
                    disabled={loading}
                    min="0"
                    step="0.01"
                  />
                )}
                
                <div className="flex gap-2">
                  <Button type="submit" size="sm" disabled={loading}>
                    {loading ? "Adding..." : "Add Link"}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setShowAddForm(false)}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}