import React, { Component, ReactNode } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w } from 'lucide-react';\nimport { Button } from '~components/ui/button';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '~components/ui/card';\nimport { ErrorBoundary as ErrorBou<PERSON>ryHandler, ExtensionError } from '~lib/errors';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error: ExtensionError | null;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false, error: null };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    const processedError = ErrorBoundaryHandler.handleComponentError(error, {});\n    return {\n      hasError: true,\n      error: processedError\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    const processedError = ErrorBoundaryHandler.handleComponentError(error, errorInfo);\n    this.setState({ error: processedError });\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: null });\n  };\n\n  handleReload = () => {\n    chrome.runtime.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div className=\"p-4 h-full flex items-center justify-center\">\n          <Card className=\"w-full max-w-md\">\n            <CardHeader className=\"text-center\">\n              <div className=\"flex justify-center mb-2\">\n                <AlertTriangle className=\"h-8 w-8 text-red-500\" />\n              </div>\n              <CardTitle className=\"text-red-600\">Something went wrong</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"text-sm text-gray-600 text-center\">\n                {this.state.error?.userMessage || 'An unexpected error occurred in the extension.'}\n              </div>\n              \n              {this.state.error?.retryable && (\n                <div className=\"text-xs text-gray-500 text-center\">\n                  This error can be retried. Try refreshing or reloading the extension.\n                </div>\n              )}\n              \n              <div className=\"flex gap-2\">\n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={this.handleRetry}\n                  className=\"flex-1\"\n                >\n                  <RefreshCw className=\"h-3 w-3 mr-1\" />\n                  Try Again\n                </Button>\n                \n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={this.handleReload}\n                  className=\"flex-1\"\n                >\n                  Reload Extension\n                </Button>\n              </div>\n              \n              <div className=\"text-xs text-gray-400 text-center\">\n                Error Code: {this.state.error?.code}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Simple error display component\nexport function ErrorDisplay({ \n  error, \n  onRetry, \n  onDismiss \n}: { \n  error: ExtensionError | Error | string;\n  onRetry?: () => void;\n  onDismiss?: () => void;\n}) {\n  const getErrorMessage = () => {\n    if (typeof error === 'string') return error;\n    if ('userMessage' in error) return error.userMessage;\n    if (error instanceof Error) return error.message;\n    return 'An error occurred';\n  };\n\n  const isRetryable = () => {\n    return error && typeof error === 'object' && 'retryable' in error && error.retryable;\n  };\n\n  return (\n    <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n      <div className=\"flex items-start gap-2\">\n        <AlertTriangle className=\"h-4 w-4 text-red-500 mt-0.5 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <div className=\"text-sm text-red-800\">{getErrorMessage()}</div>\n          \n          {(onRetry || onDismiss) && (\n            <div className=\"mt-2 flex gap-2\">\n              {onRetry && isRetryable() && (\n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={onRetry}\n                  className=\"h-6 px-2 text-xs\"\n                >\n                  Retry\n                </Button>\n              )}\n              \n              {onDismiss && (\n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={onDismiss}\n                  className=\"h-6 px-2 text-xs\"\n                >\n                  Dismiss\n                </Button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Loading state with error handling\nexport function LoadingWithError({ \n  loading, \n  error, \n  onRetry, \n  children \n}: {\n  loading: boolean;\n  error: ExtensionError | Error | string | null;\n  onRetry?: () => void;\n  children: ReactNode;\n}) {\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-4\">\n        <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"></div>\n        <span className=\"ml-2 text-sm text-gray-600\">Loading...</span>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <ErrorDisplay \n        error={error} \n        onRetry={onRetry}\n      />\n    );\n  }\n\n  return <>{children}</>;\n}"