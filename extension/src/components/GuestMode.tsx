import { useState, useEffect } from "react";
import { extensionStorage, type GuestLinkData } from "~lib/storage";
import { But<PERSON> } from "~components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~components/ui/card";
import { Input } from "~components/ui/input";
import { Send, Plus, Trash2, ExternalLink, AlertCircle, CheckCircle } from "lucide-react";

interface GuestModeProps {
  guestLinks: GuestLinkData[];
  onLinksUpdated: () => void;
}

export function GuestMode({ guestLinks, onLinksUpdated }: GuestModeProps) {
  const [newLinkUrl, setNewLinkUrl] = useState("");
  const [newLinkTitle, setNewLinkTitle] = useState("");
  const [newLinkDescription, setNewLinkDescription] = useState("");
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);
  const [currentUrl, setCurrentUrl] = useState<string>("");
  const [formDetected, setFormDetected] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    // Get current tab URL
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.url) {
        setCurrentUrl(tabs[0].url);
        checkForForm(tabs[0].id!);
      }
    });
  }, []);

  const checkForForm = async (tabId: number) => {
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: detectFormOnPage
      });
      
      if (results[0]?.result) {
        setFormDetected(true);
      }
    } catch (error) {
      console.error("Failed to check for form:", error);
    }
  };

  const handleAddLink = async () => {
    if (!newLinkUrl.trim() || !newLinkTitle.trim()) {
      return;
    }

    try {
      await extensionStorage.addGuestLink({
        url: newLinkUrl.trim(),
        title: newLinkTitle.trim(),
        description: newLinkDescription.trim() || undefined
      });

      setNewLinkUrl("");
      setNewLinkTitle("");
      setNewLinkDescription("");
      setShowAddForm(false);
      onLinksUpdated();
    } catch (error) {
      console.error("Failed to add link:", error);
    }
  };

  const handleRemoveLink = async (linkId: string) => {
    try {
      await extensionStorage.removeGuestLink(linkId);
      if (selectedLinkId === linkId) {
        setSelectedLinkId(null);
      }
      onLinksUpdated();
    } catch (error) {
      console.error("Failed to remove link:", error);
    }
  };

  const handleSubmit = async () => {
    if (!formDetected || !selectedLinkId) {
      setError("No form detected or no link selected");
      return;
    }

    const selectedLink = guestLinks.find(link => link.id === selectedLinkId);
    if (!selectedLink) {
      setError("Selected link not found");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTab = tabs[0];
      
      if (!currentTab?.id) {
        throw new Error("No active tab found");
      }

      // Execute content script to fill form
      const results = await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        func: fillFormWithGuestData,
        args: [selectedLink]
      });

      const result = results[0]?.result;
      
      if (result?.success) {
        setSuccess(true);
        
        // Auto-close popup after successful submission
        setTimeout(() => {
          window.close();
        }, 2000);
      } else {
        throw new Error(result?.error || "Form filling failed");
      }
    } catch (error) {
      console.error("Submission failed:", error);
      setError(error instanceof Error ? error.message : "Submission failed");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="p-3 space-y-4">
      {/* Current page info */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Current Page</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs text-gray-600 mb-2">
            <div className="truncate">{currentUrl || "Loading..."}</div>
          </div>
          
          <div className={`flex items-center gap-2 text-xs ${
            formDetected ? "text-green-600" : "text-orange-600"
          }`}>
            {formDetected ? (
              <>
                <CheckCircle className="h-3 w-3" />
                Form detected on page
              </>
            ) : (
              <>
                <AlertCircle className="h-3 w-3" />
                No form detected
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Link management */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center justify-between">
            Your Links ({guestLinks.length})
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowAddForm(!showAddForm)}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {showAddForm && (
            <div className="space-y-2 p-2 border rounded-md bg-gray-50">
              <Input
                placeholder="Link URL"
                value={newLinkUrl}
                onChange={(e) => setNewLinkUrl(e.target.value)}
                className="text-xs"
              />
              <Input
                placeholder="Link Title"
                value={newLinkTitle}
                onChange={(e) => setNewLinkTitle(e.target.value)}
                className="text-xs"
              />
              <Input
                placeholder="Description (optional)"
                value={newLinkDescription}
                onChange={(e) => setNewLinkDescription(e.target.value)}
                className="text-xs"
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleAddLink}
                  disabled={!newLinkUrl.trim() || !newLinkTitle.trim()}
                  className="text-xs"
                >
                  Add Link
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                  className="text-xs"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {guestLinks.length === 0 ? (
            <div className="text-xs text-gray-500 text-center py-4">
              No links added yet. Click + to add your first link.
            </div>
          ) : (
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {guestLinks.map((link) => (
                <div
                  key={link.id}
                  className={`flex items-center gap-2 p-2 border rounded-md cursor-pointer hover:bg-gray-50 ${
                    selectedLinkId === link.id ? "bg-blue-50 border-blue-300" : ""
                  }`}
                  onClick={() => setSelectedLinkId(link.id)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="text-xs font-medium truncate">{link.title}</div>
                    <div className="text-xs text-gray-500 truncate">{link.url}</div>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(link.url, '_blank');
                    }}
                    className="h-6 w-6 p-0"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveLink(link.id);
                    }}
                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Submission */}
      {selectedLinkId && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Send className="h-4 w-4" />
              Submit Link
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="bg-gray-50 rounded-md p-2 text-xs">
              <div className="font-medium mb-1">Selected Link:</div>
              <div>{guestLinks.find(l => l.id === selectedLinkId)?.title}</div>
              <div className="text-gray-500">{guestLinks.find(l => l.id === selectedLinkId)?.url}</div>
            </div>

            {error && (
              <div className="flex items-center gap-2 text-red-600 text-xs">
                <AlertCircle className="h-3 w-3" />
                <span>{error}</span>
              </div>
            )}
            
            {success && (
              <div className="flex items-center gap-2 text-green-600 text-xs">
                <CheckCircle className="h-3 w-3" />
                <span>Form filled successfully!</span>
              </div>
            )}

            <Button
              onClick={handleSubmit}
              className="w-full"
              disabled={!formDetected || !selectedLinkId || submitting || success}
              size="sm"
            >
              {submitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                  Filling Form...
                </div>
              ) : success ? (
                "Form Filled!"
              ) : (
                "Fill Form"
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Function to be injected into page to detect forms
function detectFormOnPage(): boolean {
  const forms = document.querySelectorAll('form');
  const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="url"], textarea');
  
  const hasSubmissionKeywords = document.body.textContent?.toLowerCase().includes('submit') ||
    document.body.textContent?.toLowerCase().includes('add') ||
    document.body.textContent?.toLowerCase().includes('link');
  
  return forms.length > 0 && inputs.length > 0 && hasSubmissionKeywords;
}

// Function to be injected into page to fill form with guest data
function fillFormWithGuestData(linkData: GuestLinkData): { success: boolean; error?: string } {
  try {
    const forms = document.querySelectorAll('form');
    
    if (forms.length === 0) {
      return { success: false, error: "No forms found on page" };
    }
    
    const targetForm = Array.from(forms).find(form => {
      const formText = form.textContent?.toLowerCase() || '';
      return formText.includes('submit') || formText.includes('add') || formText.includes('link');
    }) || forms[0];
    
    const inputs = targetForm.querySelectorAll('input, textarea, select');
    
    inputs.forEach((input: any) => {
      const name = input.name || input.id || input.placeholder || '';
      const type = input.type || input.tagName.toLowerCase();
      
      if (name.toLowerCase().includes('url') || name.toLowerCase().includes('link')) {
        input.value = linkData.url;
      } else if (name.toLowerCase().includes('title') || name.toLowerCase().includes('name')) {
        input.value = linkData.title;
      } else if (name.toLowerCase().includes('description') || type === 'textarea') {
        input.value = linkData.description || `${linkData.title} - A valuable resource`;
      }
      
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
    });
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}