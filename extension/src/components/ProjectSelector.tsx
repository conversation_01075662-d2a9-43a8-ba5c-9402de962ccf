import { useState } from "react";
import { ProjectData } from "~lib/storage";
import { But<PERSON> } from "~components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~components/ui/card";
import { ChevronDown, Folder, Plus } from "lucide-react";

interface ProjectSelectorProps {
  projects: ProjectData[];
  selectedProject: string | null;
  onProjectChange: (projectId: string) => void;
}

export function ProjectSelector({ projects, selectedProject, onProjectChange }: ProjectSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const selectedProjectData = projects.find(p => p.id === selectedProject);

  const handleProjectSelect = (projectId: string) => {
    onProjectChange(projectId);
    setIsOpen(false);
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <Folder className="h-4 w-4" />
          Select Project
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <Button
            variant="outline"
            className="w-full justify-between"
            onClick={() => setIsOpen(!isOpen)}
          >
            <span className="truncate">
              {selectedProjectData ? (
                <div className="text-left">
                  <div className="font-medium">{selectedProjectData.name}</div>
                  <div className="text-xs text-gray-500">{selectedProjectData.domain}</div>
                </div>
              ) : (
                "Choose a project..."
              )}
            </span>
            <ChevronDown className={`h-4 w-4 transition-transform ${
              isOpen ? "rotate-180" : ""
            }`} />
          </Button>
          
          {isOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
              {projects.length === 0 ? (
                <div className="p-3 text-center text-gray-500 text-sm">
                  No projects found
                  <div className="mt-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => {
                        chrome.tabs.create({ url: "https://mybacklinks.app/projects" });
                        setIsOpen(false);
                      }}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Create Project
                    </Button>
                  </div>
                </div>
              ) : (
                projects.map((project) => (
                  <button
                    key={project.id}
                    className={`w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                      selectedProject === project.id ? "bg-blue-50 text-blue-600" : ""
                    }`}
                    onClick={() => handleProjectSelect(project.id)}
                  >
                    <div className="font-medium text-sm">{project.name}</div>
                    <div className="text-xs text-gray-500">{project.domain}</div>
                    {project.description && (
                      <div className="text-xs text-gray-400 truncate mt-1">
                        {project.description}
                      </div>
                    )}
                  </button>
                ))
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}