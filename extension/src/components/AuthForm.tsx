import { useState } from "react";
import { auth, type AuthState } from "~lib/auth";
import { Button } from "~components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~components/ui/card";
import { Input } from "~components/ui/input";
import { AlertCircle, Key } from "lucide-react";

interface AuthFormProps {
  onAuthSuccess: (authState: AuthState) => void;
}

export function AuthForm({ onAuthSuccess }: AuthFormProps) {
  const [apiKey, setApiKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      setError("Please enter your API key");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const authState = await auth.validateApiKey(apiKey.trim());
      
      if (authState.isAuthenticated) {
        onAuthSuccess(authState);
      } else {
        setError("Invalid API key. Please check and try again.");
      }
    } catch (error) {
      console.error("Authentication failed:", error);
      setError("Authentication failed. Please check your API key and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 h-full flex items-center justify-center">
      <Card className="w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-2">
            <Key className="h-8 w-8 text-blue-500" />
          </div>
          <CardTitle>Authenticate</CardTitle>
          <p className="text-sm text-gray-600">
            Enter your LinkTrackPro API key to get started
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                type="password"
                placeholder="Enter your API key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                disabled={loading}
                className="w-full"
              />
            </div>
            
            {error && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}
            
            <Button 
              type="submit" 
              className="w-full" 
              disabled={loading || !apiKey.trim()}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Authenticating...
                </div>
              ) : (
                "Authenticate"
              )}
            </Button>
          </form>
          
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Get your API key from your{" "}
              <a 
                href="https://mybacklinks.app/settings" 
                target="_blank" 
                className="text-blue-500 hover:underline"
              >
                LinkTrackPro settings
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}