import { useState, useEffect } from "react";
import { ProjectData, LinkData } from "~lib/storage";
import { extensionAPI } from "~lib/api";
import { But<PERSON> } from "~components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~components/ui/card";
import { Checkbox } from "~components/ui/checkbox";
import { Send, Bot, AlertCircle, CheckCircle } from "lucide-react";

interface SubmissionPanelProps {
  project: ProjectData;
  link: LinkData;
  onSubmissionComplete: () => void;
}

export function SubmissionPanel({ project, link, onSubmissionComplete }: SubmissionPanelProps) {
  const [currentUrl, setCurrentUrl] = useState<string>("");
  const [formDetected, setFormDetected] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get current tab URL
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.url) {
        setCurrentUrl(tabs[0].url);
        // Check if form is detected on current page
        checkForForm(tabs[0].id!);
      }
    });
  }, []);

  const checkForForm = async (tabId: number) => {
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: detectFormOnPage
      });
      
      if (results[0]?.result) {
        setFormDetected(true);
      }
    } catch (error) {
      console.error("Failed to check for form:", error);
    }
  };

  const handleSubmit = async () => {
    if (!formDetected) {
      setError("No form detected on current page");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      // Get current tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTab = tabs[0];
      
      if (!currentTab?.id) {
        throw new Error("No active tab found");
      }

      // Execute content script to analyze and fill form
      const results = await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        func: analyzeAndFillForm,
        args: [{
          project,
          link,
          aiEnabled,
          apiEndpoint: "https://mybacklinks.app"
        }]
      });

      const result = results[0]?.result;
      
      if (result?.success) {
        // Track submission in backend
        await extensionAPI.submitLink({
          project_id: project.id,
          link_id: link.id,
          target_url: currentUrl,
          form_data: result.formData
        });
        
        setSuccess(true);
        onSubmissionComplete();
        
        // Auto-close popup after successful submission
        setTimeout(() => {
          window.close();
        }, 2000);
      } else {
        throw new Error(result?.error || "Form submission failed");
      }
    } catch (error) {
      console.error("Submission failed:", error);
      setError(error instanceof Error ? error.message : "Submission failed");
    } finally {
      setSubmitting(false);
    }
  };

  const openCurrentPageInNewTab = () => {
    chrome.tabs.create({ url: currentUrl });
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <Send className="h-4 w-4" />
          Submit Link
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Current page info */}
        <div className="text-xs text-gray-600">
          <div className="font-medium">Current page:</div>
          <div className="truncate">{currentUrl || "Loading..."}</div>
        </div>
        
        {/* Form detection status */}
        <div className={`flex items-center gap-2 text-xs ${
          formDetected ? "text-green-600" : "text-orange-600"
        }`}>
          {formDetected ? (
            <>
              <CheckCircle className="h-3 w-3" />
              Form detected on page
            </>
          ) : (
            <>
              <AlertCircle className="h-3 w-3" />
              No form detected
            </>
          )}
        </div>
        
        {/* AI generation toggle */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="ai-enabled"
            checked={aiEnabled}
            onCheckedChange={(checked) => setAiEnabled(checked as boolean)}
          />
          <label 
            htmlFor="ai-enabled" 
            className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-1"
          >
            <Bot className="h-3 w-3" />
            Use AI for content generation
          </label>
        </div>
        
        {/* Submission info */}
        <div className="bg-gray-50 rounded-md p-2 text-xs">
          <div className="font-medium mb-1">Submission Details:</div>
          <div>Project: {project.name}</div>
          <div>Link: {link.title}</div>
          <div>Target: {link.url}</div>
        </div>
        
        {/* Error message */}
        {error && (
          <div className="flex items-center gap-2 text-red-600 text-xs">
            <AlertCircle className="h-3 w-3" />
            <span>{error}</span>
          </div>
        )}
        
        {/* Success message */}
        {success && (
          <div className="flex items-center gap-2 text-green-600 text-xs">
            <CheckCircle className="h-3 w-3" />
            <span>Submission completed successfully!</span>
          </div>
        )}
        
        {/* Action buttons */}
        <div className="space-y-2">
          <Button
            onClick={handleSubmit}
            className="w-full"
            disabled={!formDetected || submitting || success}
            size="sm"
          >
            {submitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                Submitting...
              </div>
            ) : success ? (
              "Submitted!"
            ) : (
              "Submit Link"
            )}
          </Button>
          
          {!formDetected && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={openCurrentPageInNewTab}
            >
              Open page in new tab
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Function to be injected into page to detect forms
function detectFormOnPage(): boolean {
  const forms = document.querySelectorAll('form');
  const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="url"], textarea');
  
  // Look for common submission form patterns
  const hasSubmissionKeywords = document.body.textContent?.toLowerCase().includes('submit') ||
    document.body.textContent?.toLowerCase().includes('add') ||
    document.body.textContent?.toLowerCase().includes('link');
  
  return forms.length > 0 && inputs.length > 0 && hasSubmissionKeywords;
}

// Function to be injected into page to analyze and fill form
function analyzeAndFillForm(config: {
  project: ProjectData;
  link: LinkData;
  aiEnabled: boolean;
  apiEndpoint: string;
}): { success: boolean; formData?: any; error?: string } {
  try {
    const forms = document.querySelectorAll('form');
    
    if (forms.length === 0) {
      return { success: false, error: "No forms found on page" };
    }
    
    // Find the most likely submission form
    const targetForm = Array.from(forms).find(form => {
      const formText = form.textContent?.toLowerCase() || '';
      return formText.includes('submit') || formText.includes('add') || formText.includes('link');
    }) || forms[0];
    
    // Find relevant input fields
    const inputs = targetForm.querySelectorAll('input, textarea, select');
    const formData: { [key: string]: string } = {};
    
    inputs.forEach((input: any) => {
      const name = input.name || input.id || input.placeholder || '';
      const type = input.type || input.tagName.toLowerCase();
      
      // Auto-fill based on field patterns
      if (name.toLowerCase().includes('url') || name.toLowerCase().includes('link')) {
        input.value = config.link.url;
        formData[name] = config.link.url;
      } else if (name.toLowerCase().includes('title') || name.toLowerCase().includes('name')) {
        input.value = config.link.title;
        formData[name] = config.link.title;
      } else if (name.toLowerCase().includes('description') || type === 'textarea') {
        const description = config.project.description || `${config.link.title} - A valuable resource from ${config.project.domain}`;
        input.value = description;
        formData[name] = description;
      }
      
      // Trigger change events
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
    });
    
    return { success: true, formData };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}