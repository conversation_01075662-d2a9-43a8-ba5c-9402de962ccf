import { useState, useEffect } from "react";
import { extensionStorage, type ExtensionSettings } from "~lib/storage";
import { Button } from "~components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~components/ui/card";
import { Checkbox } from "~components/ui/checkbox";
import { Settings as SettingsIcon, Trash2, ExternalLink } from "lucide-react";

export function Settings() {
  const [settings, setSettings] = useState<ExtensionSettings>({
    autoFillEnabled: true,
    aiGenerationEnabled: true,
    defaultProject: null,
    theme: "light",
    notifications: true
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const currentSettings = await extensionStorage.getSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error("Failed to load settings:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async <K extends keyof ExtensionSettings>(
    key: K,
    value: ExtensionSettings[K]
  ) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await extensionStorage.updateSettings({ [key]: value });
    } catch (error) {
      console.error("Failed to update setting:", error);
    }
  };

  const clearAllData = async () => {
    if (confirm("Are you sure you want to clear all extension data? This will log you out and remove all cached data.")) {
      try {
        await extensionStorage.clearAll();
        // Reload the extension
        chrome.runtime.reload();
      } catch (error) {
        console.error("Failed to clear data:", error);
      }
    }
  };

  const openDashboard = () => {
    chrome.tabs.create({ url: "https://mybacklinks.app" });
  };

  const openApiSettings = () => {
    chrome.tabs.create({ url: "https://mybacklinks.app/settings" });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <SettingsIcon className="h-4 w-4" />
            Extension Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Auto-fill setting */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium">Auto-fill forms</div>
              <div className="text-xs text-gray-500">Automatically fill detected form fields</div>
            </div>
            <Checkbox
              checked={settings.autoFillEnabled}
              onCheckedChange={(checked) => updateSetting("autoFillEnabled", checked as boolean)}
            />
          </div>
          
          {/* AI generation setting */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium">AI content generation</div>
              <div className="text-xs text-gray-500">Use AI to generate submission content</div>
            </div>
            <Checkbox
              checked={settings.aiGenerationEnabled}
              onCheckedChange={(checked) => updateSetting("aiGenerationEnabled", checked as boolean)}
            />
          </div>
          
          {/* Notifications setting */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium">Notifications</div>
              <div className="text-xs text-gray-500">Show submission status notifications</div>
            </div>
            <Checkbox
              checked={settings.notifications}
              onCheckedChange={(checked) => updateSetting("notifications", checked as boolean)}
            />
          </div>
          
          {/* Theme setting */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium">Theme</div>
              <div className="text-xs text-gray-500">Extension appearance</div>
            </div>
            <div className="flex gap-1">
              <Button
                variant={settings.theme === "light" ? "default" : "outline"}
                size="sm"
                onClick={() => updateSetting("theme", "light")}
              >
                Light
              </Button>
              <Button
                variant={settings.theme === "dark" ? "default" : "outline"}
                size="sm"
                onClick={() => updateSetting("theme", "dark")}
              >
                Dark
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start"
            onClick={openDashboard}
          >
            <ExternalLink className="h-3 w-3 mr-2" />
            Open Dashboard
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start"
            onClick={openApiSettings}
          >
            <ExternalLink className="h-3 w-3 mr-2" />
            Manage API Keys
          </Button>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-red-600">Danger Zone</CardTitle>
        </CardHeader>
        <CardContent>
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start text-red-600 border-red-200 hover:bg-red-50"
            onClick={clearAllData}
          >
            <Trash2 className="h-3 w-3 mr-2" />
            Clear All Data
          </Button>
          <div className="text-xs text-gray-500 mt-1">
            This will log you out and remove all cached data
          </div>
        </CardContent>
      </Card>
    </div>
  );
}