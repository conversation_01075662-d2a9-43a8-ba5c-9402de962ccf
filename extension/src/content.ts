// Content script for form detection and auto-filling

interface FormAnalysis {
  forms: HTMLFormElement[];
  fields: {
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
    name: string;
    type: string;
    label: string;
    required: boolean;
    placeholder?: string;
  }[];
  confidence: number;
}

interface SubmissionData {
  project: {
    id: string;
    name: string;
    domain: string;
    description: string;
  };
  link: {
    id: string;
    url: string;
    title: string;
    link_type: "free" | "paid";
    price?: number;
  };
  aiEnabled: boolean;
  apiEndpoint: string;
}

class FormDetector {
  private static instance: FormDetector;
  
  static getInstance(): FormDetector {
    if (!FormDetector.instance) {
      FormDetector.instance = new FormDetector();
    }
    return FormDetector.instance;
  }

  detectForms(): FormAnalysis {
    const forms = Array.from(document.querySelectorAll('form'));
    const allFields: FormAnalysis['fields'] = [];
    
    forms.forEach(form => {
      const inputs = form.querySelectorAll('input, textarea, select');
      
      inputs.forEach((element: any) => {
        if (this.isRelevantField(element)) {
          allFields.push({
            element,
            name: element.name || element.id || element.placeholder || '',
            type: element.type || element.tagName.toLowerCase(),
            label: this.getFieldLabel(element),
            required: element.required || element.hasAttribute('required'),
            placeholder: element.placeholder
          });
        }
      });
    });
    
    const confidence = this.calculateConfidence(forms, allFields);
    
    return {
      forms,
      fields: allFields,
      confidence
    };
  }
  
  private isRelevantField(element: HTMLElement): boolean {
    const tagName = element.tagName.toLowerCase();
    const type = (element as HTMLInputElement).type?.toLowerCase();
    
    // Include text inputs, textareas, and select elements
    if (tagName === 'textarea' || tagName === 'select') {
      return true;
    }
    
    if (tagName === 'input') {
      const relevantTypes = ['text', 'email', 'url', 'tel', 'search', ''];
      return relevantTypes.includes(type || '');
    }
    
    return false;
  }
  
  private getFieldLabel(element: HTMLElement): string {
    // Try to find associated label
    const id = element.id;
    if (id) {
      const label = document.querySelector(`label[for="${id}"]`);
      if (label) {
        return label.textContent?.trim() || '';
      }
    }
    
    // Try parent label
    const parentLabel = element.closest('label');
    if (parentLabel) {
      return parentLabel.textContent?.trim() || '';
    }
    
    // Try nearby text
    const prev = element.previousElementSibling;
    if (prev && prev.tagName.toLowerCase() === 'label') {
      return prev.textContent?.trim() || '';
    }
    
    // Fallback to placeholder or name
    return (element as HTMLInputElement).placeholder || 
           element.getAttribute('name') || 
           element.id || 
           '';
  }
  
  private calculateConfidence(forms: HTMLFormElement[], fields: FormAnalysis['fields']): number {
    if (forms.length === 0 || fields.length === 0) {
      return 0;
    }
    
    let score = 0;
    const pageText = document.body.textContent?.toLowerCase() || '';
    
    // Check for submission-related keywords
    const keywords = ['submit', 'add', 'create', 'post', 'link', 'url', 'website'];
    keywords.forEach(keyword => {
      if (pageText.includes(keyword)) {
        score += 10;
      }
    });
    
    // Check field types and names
    fields.forEach(field => {
      const fieldInfo = (field.name + ' ' + field.label + ' ' + field.placeholder).toLowerCase();
      
      if (fieldInfo.includes('url') || fieldInfo.includes('link') || fieldInfo.includes('website')) {
        score += 20;
      }
      if (fieldInfo.includes('title') || fieldInfo.includes('name')) {
        score += 15;
      }
      if (fieldInfo.includes('description') || field.type === 'textarea') {
        score += 10;
      }
      if (fieldInfo.includes('email')) {
        score += 5;
      }
    });
    
    // Normalize score to 0-100
    return Math.min(100, score);
  }
}

class FormFiller {
  private static instance: FormFiller;
  
  static getInstance(): FormFiller {
    if (!FormFiller.instance) {
      FormFiller.instance = new FormFiller();
    }
    return FormFiller.instance;
  }

  async fillForm(submissionData: SubmissionData): Promise<{ success: boolean; filledFields: string[]; error?: string }> {
    try {
      const analysis = FormDetector.getInstance().detectForms();
      
      if (analysis.confidence < 30) {
        return {
          success: false,
          filledFields: [],
          error: "Low confidence in form detection"
        };
      }
      
      const filledFields: string[] = [];
      
      // Generate content if AI is enabled
      let generatedContent: { [key: string]: string } = {};
      if (submissionData.aiEnabled) {
        generatedContent = await this.generateAIContent(submissionData, analysis);
      }
      
      // Fill each field
      for (const field of analysis.fields) {
        const fieldInfo = (field.name + ' ' + field.label + ' ' + field.placeholder).toLowerCase();
        let value = '';
        
        // Determine what to fill based on field characteristics
        if (fieldInfo.includes('url') || fieldInfo.includes('link') || fieldInfo.includes('website')) {
          value = submissionData.link.url;
        } else if (fieldInfo.includes('title') || fieldInfo.includes('name')) {
          value = submissionData.link.title;
        } else if (fieldInfo.includes('description') || field.type === 'textarea') {
          value = generatedContent[field.name] || 
                  submissionData.project.description || 
                  `${submissionData.link.title} - A valuable resource from ${submissionData.project.domain}`;
        } else if (fieldInfo.includes('email')) {
          // Don't auto-fill email for security reasons
          continue;
        } else {
          // Try AI-generated content for other fields
          value = generatedContent[field.name] || '';
        }
        
        if (value) {
          this.setFieldValue(field.element, value);
          filledFields.push(field.name || field.label);
        }
      }
      
      return {
        success: true,
        filledFields
      };
    } catch (error) {
      return {
        success: false,
        filledFields: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  private async generateAIContent(submissionData: SubmissionData, analysis: FormAnalysis): Promise<{ [key: string]: string }> {
    try {
      // This would typically call the backend API for AI generation
      // For now, return basic generated content
      const content: { [key: string]: string } = {};
      
      analysis.fields.forEach(field => {
        const fieldInfo = (field.name + ' ' + field.label + ' ' + field.placeholder).toLowerCase();
        
        if (fieldInfo.includes('description') || field.type === 'textarea') {
          content[field.name] = `${submissionData.link.title} is an excellent resource that provides valuable information related to ${submissionData.project.domain}. This high-quality link offers great value to users interested in this topic.`;
        } else if (fieldInfo.includes('category') || fieldInfo.includes('tag')) {
          content[field.name] = this.extractCategoryFromDomain(submissionData.project.domain);
        } else if (fieldInfo.includes('comment') || fieldInfo.includes('note')) {
          content[field.name] = `Found this valuable resource at ${submissionData.link.url}. Thought it would be a great addition to your collection.`;
        }
      });
      
      return content;
    } catch (error) {
      console.error('AI content generation failed:', error);
      return {};
    }
  }
  
  private extractCategoryFromDomain(domain: string): string {
    // Simple category extraction based on domain keywords
    const categories = {
      'tech': ['tech', 'software', 'programming', 'code', 'developer'],
      'business': ['business', 'finance', 'marketing', 'startup'],
      'design': ['design', 'ui', 'ux', 'creative'],
      'education': ['education', 'learn', 'course', 'tutorial'],
      'news': ['news', 'blog', 'media', 'journalism']
    };
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => domain.toLowerCase().includes(keyword))) {
        return category;
      }
    }
    
    return 'general';
  }
  
  private setFieldValue(element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement, value: string): void {
    // Set the value
    element.value = value;
    
    // Trigger events to ensure the change is detected by the website
    const events = ['input', 'change', 'blur'];
    events.forEach(eventType => {
      element.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
    });
    
    // For React/Vue apps, trigger additional events
    element.dispatchEvent(new Event('keyup', { bubbles: true }));
    element.dispatchEvent(new Event('keydown', { bubbles: true }));
  }
}

// Message handling
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'detectForm') {
    const detector = FormDetector.getInstance();
    const analysis = detector.detectForms();
    sendResponse({
      success: true,
      hasForm: analysis.forms.length > 0,
      confidence: analysis.confidence,
      fieldCount: analysis.fields.length
    });
  } else if (message.action === 'fillForm') {
    const filler = FormFiller.getInstance();
    filler.fillForm(message.data).then(result => {
      sendResponse(result);
    }).catch(error => {
      sendResponse({
        success: false,
        filledFields: [],
        error: error.message
      });
    });
    return true; // Keep message channel open for async response
  } else if (message.action === 'toggleSidebar') {
    const sidebar = SidebarManager.getInstance();
    sidebar.toggle(message.mode);
    sendResponse({ success: true });
  }
});

// Export for use in popup
if (typeof window !== 'undefined') {
  (window as any).LinkTrackProFormDetector = FormDetector.getInstance();
  (window as any).LinkTrackProFormFiller = FormFiller.getInstance();
}

// Sidebar management
class SidebarManager {
  private static instance: SidebarManager;
  private sidebarContainer: HTMLDivElement | null = null;
  private isVisible = false;
  
  static getInstance(): SidebarManager {
    if (!SidebarManager.instance) {
      SidebarManager.instance = new SidebarManager();
    }
    return SidebarManager.instance;
  }

  toggle(mode?: string): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show(mode);
    }
  }

  show(mode?: string): void {
    if (this.sidebarContainer) {
      this.sidebarContainer.style.display = 'block';
      this.isVisible = true;
      return;
    }

    this.createSidebar(mode);
  }

  hide(): void {
    if (this.sidebarContainer) {
      this.sidebarContainer.style.display = 'none';
      this.isVisible = false;
    }
  }

  destroy(): void {
    if (this.sidebarContainer) {
      this.sidebarContainer.remove();
      this.sidebarContainer = null;
      this.isVisible = false;
    }
  }

  private createSidebar(mode?: string): void {
    // Create sidebar container
    this.sidebarContainer = document.createElement('div');
    this.sidebarContainer.id = 'linktrackpro-sidebar';
    
    // Styles for the sidebar
    const styles = `
      position: fixed;
      top: 0;
      right: 0;
      width: 400px;
      height: 100vh;
      background: white;
      border-left: 1px solid #e5e7eb;
      z-index: 2147483647;
      box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      transform: translateX(0);
      transition: transform 0.3s ease-in-out;
      overflow: hidden;
    `;
    
    this.sidebarContainer.style.cssText = styles;
    
    // Create shadow root for better isolation
    const shadowRoot = this.sidebarContainer.attachShadow({ mode: 'closed' });
    
    // Create the React app container inside shadow root
    const reactContainer = document.createElement('div');
    reactContainer.style.cssText = `
      width: 100%;
      height: 100%;
      background: white;
      color: #1f2937;
    `;
    
    // Add CSS reset and Tailwind-like styles
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
      * {
        box-sizing: border-box;
      }
      
      .space-y-4 > * + * { margin-top: 1rem; }
      .space-y-3 > * + * { margin-top: 0.75rem; }
      .space-y-2 > * + * { margin-top: 0.5rem; }
      .space-y-1 > * + * { margin-top: 0.25rem; }
      
      .flex { display: flex; }
      .flex-col { flex-direction: column; }
      .flex-1 { flex: 1; }
      .items-center { align-items: center; }
      .justify-between { justify-content: space-between; }
      .gap-2 { gap: 0.5rem; }
      .gap-4 { gap: 1rem; }
      
      .p-3 { padding: 0.75rem; }
      .p-4 { padding: 1rem; }
      .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
      .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
      .mt-2 { margin-top: 0.5rem; }
      .mt-4 { margin-top: 1rem; }
      .mb-2 { margin-bottom: 0.5rem; }
      .mb-4 { margin-bottom: 1rem; }
      
      .text-lg { font-size: 1.125rem; }
      .text-sm { font-size: 0.875rem; }
      .text-xs { font-size: 0.75rem; }
      .font-semibold { font-weight: 600; }
      .font-medium { font-weight: 500; }
      
      .text-gray-600 { color: #6b7280; }
      .text-gray-500 { color: #9ca3af; }
      .text-blue-500 { color: #3b82f6; }
      
      .border { border: 1px solid #e5e7eb; }
      .border-b { border-bottom: 1px solid #e5e7eb; }
      .rounded { border-radius: 0.375rem; }
      .rounded-md { border-radius: 0.375rem; }
      
      .bg-background { background-color: #ffffff; }
      .bg-muted { background-color: #f9fafb; }
      .bg-blue-50 { background-color: #eff6ff; }
      
      .overflow-y-auto { overflow-y: auto; }
      .truncate { 
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .animate-spin {
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
      
      button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s;
        background: #f3f4f6;
        color: #374151;
      }
      
      button:hover {
        background: #e5e7eb;
      }
      
      button.primary {
        background: #3b82f6;
        color: white;
      }
      
      button.primary:hover {
        background: #2563eb;
      }
      
      button.ghost {
        background: transparent;
        border: none;
        padding: 0.25rem;
      }
      
      button.ghost:hover {
        background: #f3f4f6;
      }
      
      input, textarea, select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        background: white;
      }
      
      input:focus, textarea:focus, select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    `;
    
    shadowRoot.appendChild(styleSheet);
    shadowRoot.appendChild(reactContainer);
    
    // Create a simple sidebar interface without React for now
    reactContainer.innerHTML = `
      <div style="height: 100%; display: flex; flex-direction: column;">
        <div style="padding: 1rem; border-bottom: 1px solid #e5e7eb; background: white;">
          <div style="display: flex; align-items: center; justify-content: between;">
            <h1 style="font-size: 1.125rem; font-weight: 600; margin: 0;">LinkTrackPro</h1>
            <button id="close-sidebar" style="background: transparent; border: none; padding: 0.25rem; cursor: pointer; margin-left: auto;">
              ✕
            </button>
          </div>
          <div style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">
            Extension Sidebar
          </div>
        </div>
        
        <div style="flex: 1; padding: 1rem; overflow-y: auto;">
          <div style="margin-bottom: 1rem;">
            <h3 style="font-weight: 500; margin-bottom: 0.5rem;">Current Page</h3>
            <div style="font-size: 0.875rem; color: #6b7280; word-break: break-all;">
              ${window.location.href}
            </div>
          </div>
          
          <div style="margin-bottom: 1rem;">
            <h3 style="font-weight: 500; margin-bottom: 0.5rem;">Form Detection</h3>
            <div id="form-status" style="font-size: 0.875rem;">
              Checking for forms...
            </div>
          </div>
          
          <div style="margin-bottom: 1rem;">
            <button id="detect-forms" class="primary" style="width: 100%;">
              Detect Forms
            </button>
          </div>
          
          <div>
            <h3 style="font-weight: 500; margin-bottom: 0.5rem;">Quick Actions</h3>
            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
              <button id="add-current-page">Add Current Page as Link</button>
              <button id="open-main-app">Open LinkTrackPro App</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Add event listeners
    const closeBtn = shadowRoot.getElementById('close-sidebar');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }
    
    const detectFormsBtn = shadowRoot.getElementById('detect-forms');
    if (detectFormsBtn) {
      detectFormsBtn.addEventListener('click', () => this.detectForms(shadowRoot));
    }
    
    const addPageBtn = shadowRoot.getElementById('add-current-page');
    if (addPageBtn) {
      addPageBtn.addEventListener('click', () => this.addCurrentPage());
    }
    
    const openAppBtn = shadowRoot.getElementById('open-main-app');
    if (openAppBtn) {
      openAppBtn.addEventListener('click', () => {
        window.open('https://mybacklinks.app', '_blank');
      });
    }
    
    // Add to DOM
    document.body.appendChild(this.sidebarContainer);
    this.isVisible = true;
    
    // Animate in
    requestAnimationFrame(() => {
      if (this.sidebarContainer) {
        this.sidebarContainer.style.transform = 'translateX(0)';
      }
    });
    
    // Initial form detection
    this.detectForms(shadowRoot);
  }
  
  private detectForms(shadowRoot: ShadowRoot): void {
    const formStatus = shadowRoot.getElementById('form-status');
    if (!formStatus) return;
    
    const detector = FormDetector.getInstance();
    const analysis = detector.detectForms();
    
    if (analysis.forms.length > 0) {
      formStatus.innerHTML = `
        <div style="color: #059669;">
          ✓ ${analysis.forms.length} form(s) detected
        </div>
        <div style="font-size: 0.75rem; margin-top: 0.25rem;">
          ${analysis.fields.length} fields found, ${analysis.confidence}% confidence
        </div>
      `;
    } else {
      formStatus.innerHTML = `
        <div style="color: #dc2626;">
          ✗ No forms detected
        </div>
      `;
    }
  }
  
  private addCurrentPage(): void {
    // Send message to background script
    chrome.runtime.sendMessage({
      action: 'addCurrentPage',
      data: {
        url: window.location.href,
        title: document.title
      }
    });
  }
}

// Listen for messages from background script
window.addEventListener('message', (event) => {
  if (event.source !== window) return;
  
  if (event.data.type === 'LINKTRACKPRO_TOGGLE_SIDEBAR') {
    const sidebar = SidebarManager.getInstance();
    sidebar.toggle(event.data.mode);
  }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  const sidebar = SidebarManager.getInstance();
  sidebar.destroy();
});

console.log('LinkTrackPro content script loaded');