# Service Worker 修复说明

## 🐛 问题诊断

**症状：** 扩展显示 "Service Worker (无效)"

**根本原因：**
1. Background script 中有重复的 `chrome.runtime.onInstalled.addListener`
2. 缺少必要的权限：`contextMenus` 和 `notifications`
3. 代码结构导致的执行时错误

## ✅ 修复措施

### 1. 修复重复监听器问题

**问题代码：**
```typescript
// 第一个监听器
chrome.runtime.onInstalled.addListener((details) => {
  // 设置逻辑
});

// 第二个监听器 - 这里会冲突
chrome.runtime.onInstalled.addListener(() => {
  // 菜单创建逻辑
});
```

**修复后：**
```typescript
chrome.runtime.onInstalled.addListener((details) => {
  // 合并所有初始化逻辑
  
  if (details.reason === 'install') {
    // 设置默认配置
  }
  
  // 创建上下文菜单
  chrome.contextMenus.create({...});
});
```

### 2. 添加必要权限

**更新 package.json：**
```json
{
  "manifest": {
    "permissions": [
      "storage",
      "activeTab", 
      "scripting",
      "tabs",
      "contextMenus",    // ← 新增
      "notifications"    // ← 新增
    ]
  }
}
```

**更新 manifest.json：**
```json
{
  "permissions": [
    "storage",
    "activeTab",
    "scripting", 
    "tabs",
    "contextMenus",
    "notifications"
  ]
}
```

### 3. 验证修复结果

**构建后的 manifest.json 应包含：**
- ✅ 正确的权限列表
- ✅ Content script 配置
- ✅ Background service worker 配置
- ❌ 没有 default_popup

## 🔧 重新安装步骤

### 1. 移除旧版本扩展
1. 前往 `chrome://extensions/`
2. 找到 LinkTrackPro 扩展
3. 点击"移除"按钮

### 2. 加载新版本
1. 确保开发者模式已启用
2. 点击"加载已解压的扩展程序"
3. 选择文件夹：`/Users/<USER>/Code/LinkTrackPro/extension/build/chrome-mv3-prod`

### 3. 验证修复
安装后检查：
- ✅ 扩展状态应该显示正常（不再有"无效"标识）
- ✅ "检查视图: Service Worker" 应该可以点击
- ✅ 没有错误消息

## 🧪 功能测试

### 测试 1: Service Worker 状态
1. 加载扩展后，查看扩展详情
2. **预期：** 不再显示"Service Worker (无效)"
3. **预期：** 可以点击"Service Worker"查看日志

### 测试 2: 扩展图标点击
1. 在任意网页上点击扩展图标
2. **预期：** 右侧出现 sidebar
3. **预期：** 没有控制台错误

### 测试 3: 上下文菜单
1. 在网页上右键
2. **预期：** 看到 LinkTrackPro 相关菜单项
3. **预期：** 点击菜单项可以触发相应功能

### 测试 4: Sidebar 功能
1. 打开 sidebar
2. **预期：** 显示当前页面信息
3. **预期：** 表单检测工作正常
4. **预期：** 快速操作按钮可以点击

## 🚨 如果仍有问题

### 检查 Service Worker 日志
1. 在扩展详情页面，点击"Service Worker"
2. 查看控制台是否有错误信息
3. 应该看到：`"LinkTrackPro background script loaded"`

### 检查权限
确认扩展详情页面显示所有必要权限：
- 读取和更改您在所有网站上的数据
- 存储空间
- 查看您的数据和浏览记录

### 重启浏览器
如果问题仍然存在：
1. 完全关闭 Chrome
2. 重新启动 Chrome
3. 重新加载扩展

## 📝 技术细节

### Background Script 架构
```typescript
// 单一初始化监听器
chrome.runtime.onInstalled.addListener(...)

// 图标点击处理
chrome.action.onClicked.addListener(...)

// 上下文菜单处理  
chrome.contextMenus.onClicked.addListener(...)

// 消息处理
chrome.runtime.onMessage.addListener(...)
```

### Permissions 说明
- `contextMenus`: 创建右键菜单所需
- `notifications`: 显示通知所需
- `scripting`: 注入 sidebar 脚本所需
- `activeTab`: 访问当前标签页所需

---

**修复完成后，Service Worker 应该正常工作，扩展可以正常使用 Sidebar 功能！** 🎉